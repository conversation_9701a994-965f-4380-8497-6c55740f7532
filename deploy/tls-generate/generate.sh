#!/bin/sh

echo "signing cert for kcs-pilot"


echo "generate ca key and ca cert"
openssl genrsa -out ca.key 2048
openssl req -x509 -new -nodes -key ca.key -subj "/CN=*.kcs-system" -days 10000 -out ca.crt

echo "generate tls key"
openssl genrsa -out tls.key 2048

echo "make tls csr"
cat << EOF >csr.conf
[ req ]
default_bits = 2048
prompt = no
default_md = sha256
req_extensions = req_ext
distinguished_name = dn

[ dn ]
C = ch
ST = zj
L = hz
O = kcs
CN = *.kcs-system

[ req_ext ]
subjectAltName = @alt_names

[ alt_names ]
DNS.1 = *.kcs-system
DNS.2 = *.kcs-system.svc
DNS.3 = *.kcs-system.svc.cluster.local
DNS.4 = *.ke.com
DNS.5 = *.ke.cloud
IP.1 = 127.0.0.1

[ v3_ext ]
authorityKeyIdentifier=keyid,issuer:always
basicConstraints=CA:FALSE
keyUsage=keyEncipherment,dataEncipherment
extendedKeyUsage=serverAuth,clientAuth
subjectAltName=@alt_names
EOF
openssl req -new -key tls.key -out tls.csr -config csr.conf

echo "generate tls cert"
openssl x509 -req -in tls.csr -CA ca.crt -CAkey ca.key -CAcreateserial -out tls.crt -days 10000 -extensions v3_ext -extfile csr.conf
