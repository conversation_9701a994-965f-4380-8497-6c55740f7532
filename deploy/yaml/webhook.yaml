apiVersion: v1
kind: Service
metadata:
  name: kcs-agent-webhook
  namespace: kcs-system
spec:
  ports:
    - name: port-9443
      port: 9443
      protocol: TCP
      targetPort: 9443
  selector:
    app.oam.dev/component: kcs-agent
  type: ClusterIP
---
apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: kcs-agent-cert
  namespace: kcs-system
spec:
  dnsNames:
    - kcs-agent-webhook.{{ .Release.Namespace }}.svc
    - kcs-agent-webhook.{{ .Release.Namespace }}.svc.cluster.local
  issuerRef:
    kind: Issuer
    name: kcs-ctrlmgr-controller-selfsigned-issuer
  secretName: kcs-webhook-server-cert
---
apiVersion: cert-manager.io/v1
kind: Issuer
metadata:
  name: kcs-agent-selfsigned-issuer
  namespace: kcs-system
spec:
  selfSigned: { }
---
apiVersion: admissionregistration.k8s.io/v1
kind: MutatingWebhookConfiguration
metadata:
  annotations:
    cert-manager.io/inject-ca-from: kcs-system/kcs-agent-cert
  name: kcs-agent-webhook-configuration
webhooks:
  - admissionReviewVersions:
      - v1
      - v1beta1
    clientConfig:
      service:
        name: kcs-agent-webhook
        namespace: kcs-system
        path: /admission-sglang-distribution-worker
        port: 443
    failurePolicy: Fail
    name: admission-network.kcs.io
    namespaceSelector:
      matchExpressions:
        - key: federation.kcs.io/workspace
          operator: Exists
    rules:
      - apiGroups:
          - ""
        apiVersions:
          - "v1"
        operations:
          - CREATE
        resources:
          - pods
    sideEffects: None
---
