package main

import (
	"context"
	"fmt"
	"git.lianjia.com/cloudnative/kic/kic-platform/aistudio/cmd/openvsx-proxy/app"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/slog"
	"os"
	"os/signal"
	"syscall"
)

var shutdownSignals = []os.Signal{os.Interrupt, syscall.SIGTERM}

var onlyOneSignalHandler = make(chan struct{})

func main() {
	ctx := SetupSignalHandler()
	err := slog.Init(ctx)
	if err != nil {
		fmt.Printf("【系统错误】%s, exit -1\n", err.Error())
		os.Exit(-1)
	}
	cmd := app.NewCommand(ctx)
	err = cmd.Execute()
	if err != nil {
		fmt.Printf("【系统错误】%s, exit -1\n", err.Error())
		os.Exit(-1)
	}
	fmt.Printf("【系统正常退出】\n")
}

func SetupSignalHandler() context.Context {
	close(onlyOneSignalHandler) // panics when called twice
	ctx, cancel := context.WithCancel(context.Background())
	c := make(chan os.Signal, 2)
	signal.Notify(c, shutdownSignals...)
	go func() {
		<-c
		cancel()
		<-c
		os.Exit(1) // second signal. Exit directly.
	}()

	return ctx
}
