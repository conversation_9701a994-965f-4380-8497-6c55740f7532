package app

import (
	"context"
	"fmt"
	"git.lianjia.com/cloudnative/kic/kic-platform/aistudio/cmd/openvsx-proxy/app/options"
	openvsx_proxy "git.lianjia.com/cloudnative/kic/kic-platform/aistudio/pkg/components/openvsx-proxy"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/property"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/sharedcli/profileflag"
	"github.com/spf13/cobra"
)

func NewCommand(ctx context.Context) *cobra.Command {
	opts := options.NewOptions()
	cmd := &cobra.Command{
		Use:  "智能云-机器学习平台-VSCode插件代理服务",
		Long: "智能云-机器学习平台-VSCode插件代理服务，用于代理OpenVSX插件下载请求，提供插件下载服务。",
		RunE: func(cmd *cobra.Command, args []string) error {
			if err := opts.Validate(); err != nil {
				return fmt.Errorf("args validate failed: %w", err)
			}
			if opts.ProfileOptions.EnableProfile {
				profileflag.ListenAndServe(opts.ProfileOptions)
			}
			err := Run(ctx, opts)
			if err != nil {
				return fmt.Errorf("run failed: %w", err)
			}
			return nil
		},
	}
	opts.AddFlags(cmd.Flags())
	return cmd
}

func Run(ctx context.Context, opts *options.Options) error {
	properties, err := property.NewEnvironmentPropertyFromConfigFile(opts.ConfigFile)
	if err != nil {
		return fmt.Errorf("parser config file failed:%s", err.Error())
	}
	cfg, err := openvsx_proxy.NewConfig(properties)
	if err != nil {
		return err
	}
	openVSXProxy := openvsx_proxy.OpenVSXProxy{Config: cfg}
	return openVSXProxy.Start(ctx)
}
