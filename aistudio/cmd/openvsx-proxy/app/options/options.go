package options

import (
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/sharedcli/profileflag"
	"github.com/spf13/pflag"
)

type Options struct {
	ConfigFile     string
	ProfileOptions profileflag.Options
}

func NewOptions() *Options {
	o := &Options{}
	return o
}

func (o *Options) Validate() error {
	//todo
	return nil
}

func (o *Options) AddFlags(flags *pflag.FlagSet) {
	flags.StringVar(&o.ConfigFile, "config-file", "/tmp/config.yaml", "配置文件路径")
	flags.BoolVar(&o.ProfileOptions.EnableProfile, "profile-enabled", false, "开启Profile")
	flags.StringVar(&o.ProfileOptions.ProfilingBindAddress, "profile-address", "0.0.0.0:6060", "Profile的启动地址")
}
