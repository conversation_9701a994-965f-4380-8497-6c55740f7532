application:
  name: kic-aistudio
  env: prod
  http:
    addr: 0.0.0.0:8000
    timeout: 30s
  grpc:
    addr: 0.0.0.0:9000
    timeout: 30s
  jwt:
    signedKey: 676od5XcHV5kotLfcFnECBTgbG2ZLxg4
  trace:
    enabled: true
    otel:
      endpoint: kic.tempo.kcs.ke.com
  static:
    enabled: true
  logLevel: info
  domain: aistudio.kcs.ke.com
  modules:
    - tcr
    - uc
    - velaux
    - cmdb
    - keycloak
    - cubefs
    - dragonfly
    - csg
    - gitea
    - notice
    - ldap
    - s3
    - ldap
    - distributedcache

authentication:
  enabled: false
  impersonation: wangtianqing003

gateway:
  default:
    httpRoute:
      hostTemplate: "{{.ServiceName}}.aistudio-{{.Workspace}}.kcs.ke.com"

database:
  type: mongodb
  url: ************************************************
  dbname: kic-aistudio

prometheus:
  nodes:
    url: http://queries.kcs.ke.com


pyroscope:
  enabled: true
  server:
    address: http://kcs.pyroscope.kcs.ke.com

nightingale:
  host: http://n9e.kcs.ke.com
  user: kic
  token: a2ljMTIzNDU2

kubernetes:
  proxy:
    #支持gateway和direct两种模式
    model: gateway
  multicluster:
    clusterName: demo-cluster
    hubEndpoint: https://rancher.ke.com
    hubToken: "****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
    clientQps: 100
    clientBurst: 200
    user: kic-bootstrap
    impersonation: false

keycloak:
  url: http://keycloak.kcs.ke.com
  realm: kic
  clientId: aistudio
  clientSecret: ZYQK0k1YUU00Nqbbj3XFCP6WmUTTMymK


rbac:
  config:
    path: "/cmd/server/conf/rbac.yaml"

workflow:
  config:
    path: "/cmd/server/conf/workflow.yaml"

uc:
  enabled: true
  url: http://uc.lianjia.com
  appId: "147"
  appKey: "c9c577392c9f573d8ad298594e28bf2c"

cmdb:
  url: http://kcs-nodeman.kcs-yz.ke.com
  x-api-key: b34c3b93-09b4-4229-bef8-fd612e4b32dd

velaux:
  url: http://vela.kcs.ke.com
  user: kcs_admin
  password: ZU#Yj8nJC9
  project:
    label: dev

dragonfly:
  url: http://dragonfly.kcs.ke.com/
  token: MGY4YWQyZjgtYjZmNC00NWUxLTk3MDUtMzNjODYyZDFhZmYz

region:
  - region_bj
  - region_sh

tensorboard:
  region_bj:
    zone: wq
    enabled: true
    engine: cubefs

discovery:
  polaris:
    namespace: kic
    heartbeat: true
    grpc:
      addresses:
        - mars-01.test.eureka.ke.com:28639
        - mars-02.test.eureka.ke.com:28639
        - mars-03.test.eureka.ke.com:28639

cubefs:
  dashboardUser: admin
  dashboardPassword: kcs123456
  dashboardURL: http://cubefs-dashboard.kcs.ke.com


tcr:
  regions: [ "region_bj" ]
  region_bj:
    secretId: AKID9BzfDeT9dt3ziJitpX6RYktFTghU1Ukr
    secretKey: AGZlrljwytcGoFal3NWo18Of3LXfgnrp
    registryId: tcr-m446wuql
    region: ap-beijing
  region_sh:
    secretId: AKID9BzfDeT9dt3ziJitpX6RYktFTghU1Ukr
    secretKey: AGZlrljwytcGoFal3NWo18Of3LXfgnrp
    registryId: tcr-aaoqso0v
    region: ap-shanghai

csg:
  url: http://csgserver.kcs.ke.com
  token: c7ab4948c36d6ecdf35fd4582def759ddd820f8899f5ff365ce16d7185cb2f609f3052e15681e931897259872391cbf46d78f4e75763a0a0633ef52abcdc840c
  user: kic

gitea:
  host: http://gitea.kcs.ke.com
  username: kicadmin
  password: kic123456


dns:
  servers:
    - ***********
    - ***********
    - ***********

nica:
  user: nica-admin
  password: nica123456
  endpoint: nica.kcs.ke.com

s3:
  endpoint: http://kos-proxy.kcs.ke.com
  accessKey: dceUflUZoGkNr87I
  secretKey: Le2eRSFtXfKWwmEMIDiFAH1wDJFFT1lK
  region: ap-beijing
  s3ForcePathStyle: true
  buildImageBucket: kic-builder

ldap:
  host: lj-nuc-ldap.lianjia.com
  port: 389
  baseDN: DC=ljstaff,DC=com
  baseUser: cn=warning-ldap,ou=LdapUser,dc=ljstaff,dc=com
  basePassword: 5DGuBXnK@89NTPEj
  authFilter: (&(objectCategory=Person)(sAMAccountName=%s))
  attributes:
    name: name
    id: name
    email: email
  insecureSkipVerify: false
  startTLS: false
  enabled: true
  checkConnInterval: 1m

openobserve:
  host: openobserve.kcs.ke.com
  authorization: "Basic ********************************************"
  org: cloudnative
  stream: aistudio_pod_insight

distributedcache:
  port: 8080
  url: http://cubefs-checktool.kic-system

modelhub: 
  syncModelImage: harbor.intra.ke.com/kcs/kic/aictl:v1.1.5_sync_model
  warmupModelImage: harbor.intra.ke.com/kcs/kic/aictl:v1.1.4_warmup_model
  cliDomain: http://aistudio.kcs.ke.com
  cluster: kcs-online-tz01
  publicWorkspace: kic
  warmupModelVolume: kic-model-cache
  modelHostPathPrefix: /data/models-cfs-readonly


devmachine:
  initContainerImage: harbor.intra.ke.com/kcs/aicp-common/aicp_system/notebook-init-container:v1.1.5
  initContainerImageWithHadoop: harbor.intra.ke.com/kcs/aicp-common/aicp_system/notebook-init-container:v1.1.6-hadoop

config:
  bucket: aistudio-config

temporal:
  hostport: *************:38243
  namespace: aistudio
  taskqueue: aistudio_timer_queue

multichannel_notification:
  url: http://ruler.kcs.ke.com
  defaultAppId: kic
  defaultAppToken: 4lkMhcsiZSUPuwfTjpGg1TTuF6dLKAWP9Z