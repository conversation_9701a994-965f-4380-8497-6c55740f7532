application:
  name: kic-aistudio
  env: dev
  http:
    addr: 0.0.0.0:8000
    timeout: 30s
  grpc:
    addr: 0.0.0.0:9000
    timeout: 30s
  jwt:
    signedKey: 676od5XcHV5kotLfcFnECBTgbG2ZLxg4
  trace:
    enabled: true
    otel:
      endpoint: kic.tempo.kcs.ke.com
  static:
    enabled: true
  logLevel: info
  domain: aistudio.kcs.ke.com
  modules:
    - tcr
    - uc
    - velaux
    - cmdb
    - keycloak
    - cubefs
    - dragonfly
    - csg
    - gitea
    - notice
    - ldap
    - s3
    - openobserve
    - oceanbase
    - bkticket
    - distributedcache
    - prometheus
    - nightingale
    - temporal
    - wxwork
    - multichannel_notification

authentication:
  enabled: true

database:
  type: mongodb
  url: ************************************************
  dbname: kic-aistudio

prometheus:
  nodes:
    url: http://queries.kcs.ke.com

pyroscope:
  enabled: true
  server:
    address: http://kcs-test.pyroscope.kcs.ke.com

nightingale:
  host: http://n9e.kcs.ke.com
  user: kic
  token: a2ljMTIzNDU2

kubernetes:
  multicluster:
    clusterName: demo-cluster
    #hubEndpoint: https://**************:8443  #测试环境
    hubEndpoint: https://rancher.ke.com
    #hubToken: "**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
    hubToken: "****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
    clientQps: 100
    clientBurst: 200
    user: kic-bootstrap
    impersonation: false

keycloak:
  url: http://keycloak.kcs.ke.com
  realm: aistudio-test
  clientId: aistudio-test
  clientSecret: C863xtRDdJr9uHLYbJpJPJLuYl5yPox1

rbac:
  config:
    path: "/cmd/server/conf/rbac.yaml"

workflow:
  config:
    path: "/cmd/server/conf/workflow.yaml"

uc:
  enabled: true
  url: http://uc.lianjia.com
  appId: "147"
  appKey: "c9c577392c9f573d8ad298594e28bf2c"


cmdb:
  url: http://kcs-nodeman.kcs-yz.ke.com
  x-api-key: b34c3b93-09b4-4229-bef8-fd612e4b32dd

velaux:
  url: http://vela.kcs.ke.com
  user: kcs_admin
  password: ZU#Yj8nJC9
  jwtSignKey:

dragonfly:
  url: http://test-dragonfly.kcs.ke.com/
  token: YmExMzRjMzktMTgzNy00MmNiLWFjNzktZjA4YzExNTZkZTdh

region:
  - region_bj
  - region_sh

tensorboard:
  region_bj:
    zone: wq
    enabled: true
    engine: cubefs

discovery:
  polaris:
    namespace: kic
    heartbeat: true
    grpc:
      addresses:
        - ************:8091

cubefs:
  dashboardUser: admin
  dashboardPassword: kcs123456
  dashboardURL: http://cubefs-dashboard.kcs.ke.com


tcr:
  regions: ["region_bj"]
  region_bj:
    secretId: AKID9BzfDeT9dt3ziJitpX6RYktFTghU1Ukr
    secretKey: AGZlrljwytcGoFal3NWo18Of3LXfgnrp
    registryId: tcr-m446wuql
    region: ap-beijing
  region_sh:
    secretId: AKID9BzfDeT9dt3ziJitpX6RYktFTghU1Ukr
    secretKey: AGZlrljwytcGoFal3NWo18Of3LXfgnrp
    registryId: tcr-aaoqso0v
    region: ap-shanghai

csg:
  url: http://csgserver.kcs.ke.com
  token: c7ab4948c36d6ecdf35fd4582def759ddd820f8899f5ff365ce16d7185cb2f609f3052e15681e931897259872391cbf46d78f4e75763a0a0633ef52abcdc840c

gitea:
  host: http://gitea.kcs.ke.com
  username: kicadmin
  password: kic123456
#
#csg:
#  url: http://localhost:8080
#  token: c7ab4948c36d6ecdf35fd4582def759ddd820f8899f5ff365ce16d7185cb2f609f3052e15681e931897259872391cbf46d78f4e75763a0a0633ef52abcdc840c
#
#gitea:
#  host: http://localhost:3001
#  username: root
#  password: password123

ldap:
  host: lj-nuc-ldap.lianjia.com
  port: 389
  baseDN: DC=ljstaff,DC=com
  baseUser: cn=warning-ldap,ou=LdapUser,dc=ljstaff,dc=com
  basePassword: 5DGuBXnK@89NTPEj
  authFilter: (&(objectCategory=Person)(sAMAccountName=%s))
  attributes:
    name: name
    id: name
    email: email
  insecureSkipVerify: false
  startTLS: false
  enabled: true
  checkConnInterval: 1m

dns:
  servers:
    - ***********
    - ***********
    - ***********

nica:
  user: nica-admin
  password: nica123456
  endpoint: nica-test.kcs.ke.com


s3:
  endpoint: http://kos-proxy.kcs.ke.com
  accessKey: dceUflUZoGkNr87I
  secretKey: Le2eRSFtXfKWwmEMIDiFAH1wDJFFT1lK
  region: ap-beijing
  s3ForcePathStyle: true
  buildImageBucket: kic-builder-local

#openobserve:
#  host: openobserve.kcs-test.ke.com
#  authorization: "Basic ********************************************"
openobserve:
  host: openobserve.kcs-test.ke.com
  authorization: "Basic ********************************************"
  org: default
  stream: aistudio_pod_insight_test

distributedcache:
  port: 8080
  url: http://cubefs-checktool.kic-system

modelhub:
  syncModelImage: harbor.intra.ke.com/kcs/kic/aictl:v1.1.5_sync_model_preview
  warmupModelImage: harbor.intra.ke.com/kcs/kic/aictl:v1.1.4_warmup_model_preview
  cliDomain: http://host.docker.internal:8000
  cluster: k3d-local
  publicWorkspace: local-test-0528
  warmupModelVolume: wntest
  modelHostPathPrefix: /data/models-cfs-readonly

devmachine:
  initContainerImage: harbor.intra.ke.com/kcs/aicp-common/aicp_system/notebook-init-container:v1.1.5
  initContainerImageWithHadoop: harbor.intra.ke.com/kcs/aicp-common/aicp_system/notebook-init-container:v1.1.6-hadoop
job:
  initContainerImageWithHadoop: harbor.intra.ke.com/kcs/job/job-init-container:v1.1.8-hadoop

#自测试环境
#oceanbase:
#  host: 127.0.0.1
#  port: 3308
#  user: root@aistudio_finops
#  password: root@n2F#Y#3y
#  database: aistudio_finops_test
#通用测试环境
oceanbase:
  host: m12171.mars.test.mysql.ljnode.com
  port: 12171
  user: root
  password: cJp3V542RblOXnLT
  database: aistudio_finops_test

bkticket:
  url: http://kcs-nodeman.kcs-yz.ke.com
  x-api-key: b34c3b93-09b4-4229-bef8-fd612e4b32dd
  serviceID:
    joinWorkspaceService: 92
    joinQueueService: 91


config:
  bucket: aistudio-config-local

wxwork:
  url: https://wxwork.ke.com
  appId: kic
  appToken: 4lkMhcsiZSUPuwfTjpGg1TTuF6dLKAWP9Z

temporal:
  hostport: ***********:31981
  namespace: default
  taskqueue: aistudio_timer_queue

multichannel_notification:
  url: http://ruler-preview.kcs.ke.com
  defaultAppId: kic
  defaultAppToken: 4lkMhcsiZSUPuwfTjpGg1TTuF6dLKAWP9Z
