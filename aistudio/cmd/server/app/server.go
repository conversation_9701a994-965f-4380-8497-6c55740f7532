package app

import (
	"context"
	"fmt"
	sdkhttp "net/http"
	"runtime"
	"strings"

	"git.lianjia.com/cloudnative/kic/kic-platform/aistudio/pkg/dns"
	"git.lianjia.com/cloudnative/kic/kic-platform/aistudio/pkg/server/domain/service"
	"github.com/grafana/pyroscope-go"
	"github.com/sirupsen/logrus"
	"go.opentelemetry.io/contrib/instrumentation/net/http/otelhttp"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/cluster"

	"git.lianjia.com/cloudnative/healthcheck"
	"git.lianjia.com/cloudnative/kic/kic-platform/aistudio/cmd/server/app/options"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/multicluster/clients"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/property"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/sharedcli/klogflag"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/sharedcli/profileflag"
	thirdpart "git.lianjia.com/cloudnative/kic/kic-platform/pkg/third_party"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/watcher"
	"github.com/go-kratos/kratos/v2/transport/http"
	"github.com/spf13/cobra"
	"k8s.io/client-go/rest"
	"k8s.io/klog/v2"
	controllerruntime "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/cache"
)

func NewCommand(ctx context.Context) *cobra.Command {
	opts := options.NewOptions()
	cmd := &cobra.Command{
		Use:  "智能云-机器学习平台服务",
		Long: "智能云-机器学习平台服务，上地八街最好的机器学习平台。",
		RunE: func(cmd *cobra.Command, args []string) error {
			if err := opts.Validate(); err != nil {
				return fmt.Errorf("args validate failed: %w", err)
			}
			if opts.ProfileOptions.EnableProfile {
				profileflag.ListenAndServe(opts.ProfileOptions)
			}
			err := Run(ctx, opts)
			if err != nil {
				return fmt.Errorf("run failed: %w", err)
			}
			return nil
		},
	}
	opts.AddFlags(cmd.Flags())
	klogflag.Add(cmd.Flags())
	return cmd
}

func Run(ctx context.Context, opts *options.Options) error {
	properties, err := property.NewEnvironmentPropertyFromConfigFile(opts.ConfigFile)
	property.GetEnvironmentProperty(opts.ConfigFile)
	if err != nil {
		return fmt.Errorf("parser config file failed:%s", err.Error())
	}
	runtime.SetMutexProfileFraction(5)
	runtime.SetBlockProfileRate(5)
	logger := logrus.New()
	logger.SetLevel(logrus.InfoLevel)
	if properties.GetBoolDefault("pyroscope.enabled", false) {
		profiler, err := pyroscope.Start(pyroscope.Config{
			ApplicationName: properties.MustGetString("application.name"),
			Tags: map[string]string{
				"platform": "kic",
			},
			ServerAddress: properties.GetDefault("pyroscope.server.address", "http://localhost:4040"),
			Logger:        logger,
			ProfileTypes: []pyroscope.ProfileType{
				pyroscope.ProfileCPU,
				pyroscope.ProfileAllocObjects,
				pyroscope.ProfileAllocSpace,
				pyroscope.ProfileInuseObjects,
				pyroscope.ProfileInuseSpace,
			},
		})
		if err != nil {
			return fmt.Errorf("start pyroscope failed: %v", err)
		}
		go func() {
			for {
				select {
				case <-ctx.Done():
					_ = profiler.Stop()
					return
				}
			}
		}()
	}

	err = dns.Init(properties)
	if err != nil {
		return err
	}

	leaderID := properties.GetDefault("application.name", "kic-aistudio")
	ctrlOpts := controllerruntime.Options{
		Logger:                  klog.Background(),
		Scheme:                  service.GetLocalScheme(),
		LeaderElection:          true,
		LeaderElectionID:        leaderID,
		LeaderElectionNamespace: opts.ControllerLeaderElectionNamespace,
		Port:                    opts.ControllerPort,
		MetricsBindAddress:      opts.ControllerMetricsBindAddress,
		HealthProbeBindAddress:  opts.ControllerProbeBindAddress,
		NewCache: func(config *rest.Config, opts cache.Options) (cache.Cache, error) {
			objectWatcher, err := watcher.NewClusterObjectWatcher(ctx, "local", clients.WithCacheOptions(opts), clients.WithRestConfig(config), clients.WithScheme(service.GetLocalScheme()))
			if err != nil {
				return nil, err
			}
			err = objectWatcher.Start(ctx, false, service.GetAllLocalClusterListeners()...)
			if err != nil {
				return nil, err
			}
			return objectWatcher.GetCache(), nil
		},
		NewClient: func(cache cache.Cache, config *rest.Config, options client.Options, uncachedObjects ...client.Object) (client.Client, error) {
			config.WrapTransport = func(rt sdkhttp.RoundTripper) sdkhttp.RoundTripper {
				return otelhttp.NewTransport(rt, otelhttp.WithSpanNameFormatter(func(operation string, r *sdkhttp.Request) string {
					return fmt.Sprintf("%s", r.URL.Path)
				}), otelhttp.WithFilter(func(request *sdkhttp.Request) bool {
					if strings.Contains(request.URL.Path, "coordination") {
						return false
					}
					return true
				}))
			}
			return cluster.DefaultNewClient(cache, config, options, uncachedObjects...)
		},
	}
	if properties.GetBoolDefault("application.cliProxy.enabled", false) {
		ctrlOpts.LeaderElection = false
	}
	err = thirdpart.SetupManager(ctx, properties)
	if err != nil {
		return fmt.Errorf("thirdpart init failed: %v", err)
	}
	apiServer, err := NewAPIServer(ctx, properties, ctrlOpts)
	if err != nil {
		return fmt.Errorf("init APIServer failed: %v", err)
	}
	apiServer.PrepareRun(func(httpServer *http.Server, healthCheck healthcheck.Handler) error {
		return nil
	}).Run()
	//todo 初始化服务
	return nil
}
