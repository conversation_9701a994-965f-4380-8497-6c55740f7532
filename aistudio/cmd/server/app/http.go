package app

import (
	"context"
	"fmt"
	costv1 "git.lianjia.com/cloudnative/kic/kic-platform/apis/aistudio/cost/v1"
	obscenterv1 "git.lianjia.com/cloudnative/kic/kic-platform/apis/aistudio/obscenter/v1"
	nethttp "net/http"
	stdhttp "net/http"
	"os"
	"path/filepath"
	"regexp"

	"go.opentelemetry.io/contrib/instrumentation/net/http/otelhttp"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/exporters/otlp/otlptrace/otlptracehttp"

	"git.lianjia.com/cloudnative/kic/kic-platform/aistudio/pkg/server/domain/model"
	"git.lianjia.com/cloudnative/kic/kic-platform/aistudio/pkg/server/domain/service"
	applicationv1 "git.lianjia.com/cloudnative/kic/kic-platform/apis/aistudio/application/v1"
	configv1 "git.lianjia.com/cloudnative/kic/kic-platform/apis/aistudio/config/v1"
	domainv1 "git.lianjia.com/cloudnative/kic/kic-platform/apis/aistudio/domain/v1"
	gatewayv1 "git.lianjia.com/cloudnative/kic/kic-platform/apis/aistudio/gateway/v1"
	imagehubv1 "git.lianjia.com/cloudnative/kic/kic-platform/apis/aistudio/imagehub/v1"
	modelhubv1 "git.lianjia.com/cloudnative/kic/kic-platform/apis/aistudio/modelhub/v1"
	ticketv1 "git.lianjia.com/cloudnative/kic/kic-platform/apis/aistudio/ticket/v1"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/apis/scheduling.kcs.io/v1alpha1"
	"sigs.k8s.io/controller-runtime/pkg/client"

	"git.lianjia.com/cloudnative/kic/kic-platform/apis/aistudio/bcode"
	"github.com/golang-jwt/jwt/v4"

	swaggerUI "github.com/tx7do/kratos-swagger-ui"
	controllerruntime "sigs.k8s.io/controller-runtime"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/healthz"

	"strings"
	"syscall"
	"time"

	"git.lianjia.com/cloudnative/healthcheck"
	"git.lianjia.com/cloudnative/kic/kic-platform/aistudio/pkg/constant"
	"git.lianjia.com/cloudnative/kic/kic-platform/aistudio/pkg/server/domain"
	"git.lianjia.com/cloudnative/kic/kic-platform/aistudio/pkg/server/event"
	"git.lianjia.com/cloudnative/kic/kic-platform/aistudio/pkg/server/interfaces"
	"git.lianjia.com/cloudnative/kic/kic-platform/apis"
	authenticationv1 "git.lianjia.com/cloudnative/kic/kic-platform/apis/aistudio/authentication/v1"
	cubefsv1 "git.lianjia.com/cloudnative/kic/kic-platform/apis/aistudio/cubefs/v1"
	devmachinev1 "git.lianjia.com/cloudnative/kic/kic-platform/apis/aistudio/devmachine/v1"
	jobv1 "git.lianjia.com/cloudnative/kic/kic-platform/apis/aistudio/job/v1"
	metricsv1 "git.lianjia.com/cloudnative/kic/kic-platform/apis/aistudio/metrics/v1"
	platformv1 "git.lianjia.com/cloudnative/kic/kic-platform/apis/aistudio/platform/v1"
	queuev1 "git.lianjia.com/cloudnative/kic/kic-platform/apis/aistudio/queue/v1"
	rbacv1 "git.lianjia.com/cloudnative/kic/kic-platform/apis/aistudio/rbac/v1"
	resourcegroupv1 "git.lianjia.com/cloudnative/kic/kic-platform/apis/aistudio/resourcegroup/v1"
	workspacev1 "git.lianjia.com/cloudnative/kic/kic-platform/apis/aistudio/workspace/v1"
	userv1 "git.lianjia.com/cloudnative/kic/kic-platform/apis/usercenter/v1"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/infrastructure/datastore"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/infrastructure/datastore/mongodb"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/multicluster"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/property"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/util/container"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/version"
	prom "github.com/go-kratos/kratos/contrib/metrics/prometheus/v2"
	polaris "github.com/go-kratos/kratos/contrib/registry/polaris/v2"
	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	kratoslog "github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/middleware"
	"github.com/go-kratos/kratos/v2/middleware/metrics"
	"github.com/go-kratos/kratos/v2/middleware/recovery"
	"github.com/go-kratos/kratos/v2/middleware/tracing"
	"github.com/go-kratos/kratos/v2/middleware/validate"
	"github.com/go-kratos/kratos/v2/transport"
	"github.com/go-kratos/kratos/v2/transport/http"
	"github.com/gorilla/mux"
	"github.com/polarismesh/polaris-go/api"
	"github.com/polarismesh/polaris-go/pkg/config"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/sdk/resource"
	tracesdk "go.opentelemetry.io/otel/sdk/trace"
	semconv "go.opentelemetry.io/otel/semconv/v1.4.0"
)

const (

	// bearerWord the bearer key word for authorization
	bearerWord string = "Bearer"

	// bearerFormat authorization token format
	bearerFormat string = "Bearer %s"

	// authorizationKey holds the key used to store the JWT Token in the request tokenHeader.
	authorizationKey string = "Authorization"

	// reason holds the error reason.
	reason string = "UNAUTHORIZED"
)

var (
	ErrMissingJwtToken = errors.Unauthorized(reason, "JWT token is missing")
	ErrWrongContext    = errors.Unauthorized(reason, "Wrong context for middleware")
)

var (
	_metricSeconds = prometheus.NewHistogramVec(prometheus.HistogramOpts{
		Namespace: "server",
		Subsystem: "requests",
		Name:      "duration_sec",
		Help:      "server requests duratio(sec).",
		Buckets:   []float64{0.005, 0.01, 0.025, 0.05, 0.1, 0.250, 0.5, 1},
	}, []string{"kind", "operation"})

	_metricRequests = prometheus.NewCounterVec(prometheus.CounterOpts{
		Namespace: "client",
		Subsystem: "requests",
		Name:      "code_total",
		Help:      "The total number of processed requests",
	}, []string{"kind", "operation", "code", "reason"})
)

type APIServer struct {
	beanContainer     *container.Container
	properties        property.EnvironmentProperty
	ctx               context.Context
	serverMiddlewares []middleware.Middleware
	//serverOptions     []http.ServerOption
	options        []kratos.Option
	httpServer     *http.Server
	healthCheck    healthcheck.Handler
	logger         *kratoslog.Helper
	stdLogger      log.Logger
	ds             datastore.DataStore
	serverMode     string
	client         apis.Client
	manager        ctrl.Manager
	managerOptions controllerruntime.Options
}

func NewAPIServer(ctx context.Context, properties property.EnvironmentProperty, mgrOptions controllerruntime.Options) (*APIServer, error) {
	apiServer := &APIServer{
		beanContainer: container.NewContainer(),
		properties:    properties,
		ctx:           ctx,
		healthCheck:   healthcheck.NewHandler(),
	}
	apiServer.serverMiddlewares = append(apiServer.serverMiddlewares, recovery.Recovery(
		recovery.WithHandler(func(ctx context.Context, req, err interface{}) error {
			apiServer.logger.Errorf("panic recover err: %v", err)
			return nil
		}),
	))
	apiServer.EnableLogging()
	apiServer.EnabledMetrics()
	apiServer.managerOptions = mgrOptions
	apiServer.options = append(apiServer.options, kratos.Signal(syscall.SIGHUP, syscall.SIGTERM))
	if properties.GetBoolDefault("application.trace.enabled", false) {
		apiServer.EnableTracing()
	}
	apiServer.EnableValidator()
	return apiServer, nil
}

func (a *APIServer) EnableLogging() *APIServer {
	stdLogger := a.buildLogger()
	a.options = append(a.options, kratos.Logger(stdLogger))
	a.logger = kratoslog.NewHelper(stdLogger)
	a.stdLogger = stdLogger
	log.SetLogger(stdLogger)
	a.serverMiddlewares = append(a.serverMiddlewares, Server(stdLogger))
	return a
}

func (a *APIServer) EnableValidator() *APIServer {
	a.serverMiddlewares = append(a.serverMiddlewares, validate.Validator())
	return a
}

func (a *APIServer) EnabledMetrics() *APIServer {
	a.serverMiddlewares = append(a.serverMiddlewares, metrics.Server(
		metrics.WithSeconds(prom.NewHistogram(_metricSeconds)),
		metrics.WithRequests(prom.NewCounter(_metricRequests)),
	))
	return a
}

func (a *APIServer) EnableTracing() *APIServer {
	err := a.initTracer(a.ctx)
	if err != nil {
		a.logger.Fatalf("init tracing failed: %v", err)
	}
	a.serverMiddlewares = append(a.serverMiddlewares, tracing.Server())
	return a
}

func (a *APIServer) PrepareRun(hookFunc func(httpServer *http.Server, healthCheck healthcheck.Handler) error) *APIServer {
	err := a.initHTTPServer()
	if err != nil {
		a.logger.Fatalf("启动HTTPServer失败:%v", err.Error())
	}
	err = a.buildIOCContainer()
	if err != nil {
		a.logger.Fatalf("初始化IOC容器失败:%v", err)
	}
	if err := hookFunc(a.httpServer, a.healthCheck); err != nil {
		a.logger.Fatalf("HTTPServer预启动失败:%v", err)
	}
	//启动metrics的访问地址
	a.httpServer.Handle("/metrics", promhttp.Handler())
	fmt.Println("启动metrics的访问地址")

	a.EnableHealthCheck()
	a.initRouters()
	if a.properties.GetBoolDefault(constant.ApplicationStaticEnabledPropertyKey, false) {
		a.EnableStaticFiles()
	}
	go func() {
		if err = a.manager.Start(a.ctx); err != nil {
			a.logger.Fatalf("controller manager exits unexpectedly: %v", err)
		}
	}()
	return a
}

func (a *APIServer) RegisterBean(bean interface{}) error {
	return a.beanContainer.Provides(bean)
}

func (a *APIServer) Run() {
	a.options = append(a.options,
		kratos.ID(a.properties.MustGetString(constant.ApplicationNameKey)),
		kratos.Name(a.properties.MustGetString(constant.ApplicationNameKey)),
		kratos.Version(version.Get().GitVersion),
		kratos.Logger(a.stdLogger),
		kratos.Server(a.httpServer),
		kratos.Metadata(map[string]string{}))
	polarisCfg := a.initPolarisConfiguration(a.properties)
	var opts []polaris.Option
	opts = append(opts, polaris.WithNamespace(a.properties.MustGetString("discovery.polaris.namespace")))
	opts = append(opts, polaris.WithHeartbeat(true))
	opts = append(opts, polaris.WithTTL(30))
	reg := polaris.NewRegistryWithConfig(polarisCfg, opts...)
	a.options = append(a.options, kratos.Registrar(reg))
	app := kratos.New(a.options...)
	err := app.Run()
	if err != nil {
		a.logger.Fatalf("服务启动失败:%v", err)
	}
}

func (a *APIServer) WithHTTPFilter(path string, f func(h nethttp.Handler) nethttp.Handler) *APIServer {
	a.httpServer.Route(path, f)
	return a
}

func (a *APIServer) WithHTTPHandlerFunc(path string, handlerFunc nethttp.HandlerFunc) *APIServer {
	a.httpServer.HandleFunc(path, handlerFunc)
	return a
}

func (a *APIServer) WithHTTPHandler(prefix string, handler nethttp.Handler) *APIServer {
	a.httpServer.HandlePrefix(prefix, handler)
	return a
}

func (a *APIServer) buildIOCContainer() error {
	err := a.beanContainer.Provides(a.properties)
	if err != nil {
		return fmt.Errorf("fail to provides the properties bean to the container: %v", err)
	}
	err = a.beanContainer.ProvideWithName("logger", a.logger)
	if err != nil {
		return fmt.Errorf("fail to provides the logger bean to the container: %v", err)
	}
	err = a.beanContainer.Provides(event.InitEventBridgeBean(a.ctx, a.properties)...)
	if err != nil {
		return fmt.Errorf("fail to provides the event bridge bean to the container: %v", err)
	}
	properties := a.properties
	options := &multicluster.Options{
		ClusterName:   properties.MustGetString("kubernetes.multicluster.clusterName"),
		MetaEndpoint:  properties.MustGetString("kubernetes.multicluster.hubEndpoint"),
		MetaToken:     properties.MustGetString("kubernetes.multicluster.hubToken"),
		User:          properties.MustGetString("kubernetes.multicluster.user"),
		Impersonation: properties.MustGetBool("kubernetes.multicluster.impersonation"),
		ClientQps:     properties.GetFloat64Default("kubernetes.multicluster.clientQps", 100),
		ClientBurst:   properties.GetIntDefault("kubernetes.multicluster.clientBurst", 200),
	}
	if properties.GetBoolDefault("application.trace.enabled", false) {
		options.WrapperTransport = func(tripper stdhttp.RoundTripper) stdhttp.RoundTripper {
			return otelhttp.NewTransport(tripper, otelhttp.WithSpanNameFormatter(func(operation string, r *stdhttp.Request) string {
				return r.URL.Path
			}), otelhttp.WithFilter(func(request *stdhttp.Request) bool {
				if strings.Contains(request.URL.Path, "coordination") {
					return false
				}
				return true
			}))
		}
	}
	for k, svcBean := range domain.InitServiceBean() {
		err = a.beanContainer.ProvideWithName(k, svcBean)
		if err != nil {
			return fmt.Errorf("fail to provides the service bean[%v] to the container: %v", k, err)
		}
	}
	restCfg, err := controllerruntime.GetConfig()
	if err != nil {
		return fmt.Errorf("get rest config failed: %v", err)
	}
	controllerManager, err := controllerruntime.NewManager(restCfg, a.managerOptions)
	if err != nil {
		return fmt.Errorf("create controller manager failed: %v", err)
	}
	a.manager = controllerManager
	if err := controllerManager.AddHealthzCheck("healthz", healthz.Ping); err != nil {
		return fmt.Errorf("unable to set up health check: %s", err)
	}
	if err := controllerManager.AddReadyzCheck("readyz", healthz.Ping); err != nil {
		return fmt.Errorf("unable to set up ready check: %s", err)
	}
	err = a.beanContainer.Provides(controllerManager)
	if err != nil {
		return fmt.Errorf("fail to provides the controller manager bean to the container: %v", err)
	}

	err = multicluster.InitWithManager(a.ctx, options, a.manager)
	if err != nil {
		return err
	}
	err = a.beanContainer.Provides(interfaces.InitAPIBean(a.httpServer, a.properties)...)
	if err != nil {
		return fmt.Errorf("fail to provides the api bean to the container: %v", err)
	}
	var ds datastore.DataStore
	switch a.properties.MustGetString("database.type") {
	case "mongodb":
		mongodbBean, err := mongodb.New(a.ctx, a.properties)
		if err != nil {
			return err
		}
		ds = mongodbBean
	default:
		return fmt.Errorf("unsupported database type: %s", a.properties.MustGetString("database.type"))
	}
	err = a.beanContainer.ProvideWithName("datastore", ds)
	if err != nil {
		return err
	}
	if err := a.beanContainer.Populate(); err != nil {
		return fmt.Errorf("fail to populate the bean container: %v", err)
	}
	return domain.ServicePostConstruct(a.ctx, a.properties, a.manager)
}

func (a *APIServer) initHTTPServer() error {
	var opts = []http.ServerOption{
		http.Middleware(
			a.serverMiddlewares...,
		),
	}
	network := a.properties.GetDefault("application.http.network", "tcp")
	addr := a.properties.GetDefault("application.http.addr", ":0.0.0.0:9000")
	timeout := a.properties.GetDefault("application.http.timeout", "30s")
	opts = append(opts, http.Network(network))
	opts = append(opts, http.Address(addr))
	opts = append(opts, http.ErrorEncoder(func(writer stdhttp.ResponseWriter, request *stdhttp.Request, err error) {
		//todo 此处增加错误请求的监控信息，上报到prometheus
		a.logger.Errorf("http请求发生错误: %v", err)
		http.DefaultErrorEncoder(writer, request, err)
	}))

	timeoutDuration, err := time.ParseDuration(timeout)
	opts = append(opts, http.Timeout(timeoutDuration))
	if err != nil {
		return fmt.Errorf("http timeout的格式错误：%+v", err)
	}
	srv := http.NewServer(opts...)
	a.httpServer = srv
	a.RegisterSwagger()
	a.initHTTPFilters()
	return nil
}

// RegisterSwagger 注册swagger
func (a *APIServer) RegisterSwagger() {
	swaggerUI.RegisterSwaggerUIServerWithOption(
		a.httpServer,
		swaggerUI.WithBasePath("/swagger"),
		swaggerUI.WithShowTopBar(true),
		swaggerUI.WithTitle("AIStudio2.0 API"),
		swaggerUI.WithLocalFile("openapi.yaml"),
	)
}

func (a *APIServer) EnableHealthCheck() *APIServer {
	health := healthcheck.NewHandler(healthcheck.CheckLanIP(false))
	a.httpServer.HandleFunc("/health/liveness", health.LivenessEndpoint)
	a.httpServer.HandleFunc("/health/readiness", health.ReadinessEndpoint)
	a.httpServer.HandleFunc("/maintenance/readiness", health.TrafficEndpoint)
	return a
}

func (a *APIServer) EnableStaticFiles() *APIServer {
	router := mux.NewRouter()
	router.PathPrefix("/").Handler(a)
	a.httpServer.HandlePrefix("/", router)
	return a
}

func (a *APIServer) ServeHTTP(writer nethttp.ResponseWriter, request *nethttp.Request) {
	path, err := filepath.Abs(request.URL.Path)
	if err != nil {
		a.logger.Errorf("获取文件路径失败：%v", err)
		nethttp.Error(writer, err.Error(), nethttp.StatusBadRequest)
		return
	}

	if strings.Contains(request.Header.Get("Accept"), "text/html") {
		// 删除请求中的 If-Modified-Since 头部，只对 text/html 类型的请求操作
		request.Header.Del("If-Modified-Since")

		// 设置不缓存的头部信息
		writer.Header().Set("Cache-Control", "no-cache, no-store, must-revalidate")
		writer.Header().Set("Pragma", "no-cache")
		writer.Header().Set("Expires", "0")
	} else if strings.HasPrefix(request.URL.Path, "/assets/") {
		writer.Header().Set("Cache-Control", "no-cache, no-store, must-revalidate") // 不缓存 /assets/ 目录下的文件
		writer.Header().Set("Pragma", "no-cache")
		writer.Header().Set("Expires", "0")
	} else {
		// 对于其他类型的资源，可以设置长时间缓存
		writer.Header().Set("Cache-Control", "public, max-age=31536000") // 缓存一年
	}

	path = filepath.Join("dist", path)

	_, err = os.Stat(path)
	if os.IsNotExist(err) {
		// File does not exist, serve index.html
		nethttp.ServeFile(writer, request, filepath.Join("dist", "index.html"))
		return
	} else if err != nil {
		a.logger.Errorf("获取文件信息失败：%v", err)
		nethttp.Error(writer, err.Error(), nethttp.StatusInternalServerError)
		return
	}

	nethttp.FileServer(nethttp.Dir("dist")).ServeHTTP(writer, request)
}

func (a *APIServer) initRouters() {
	//todo 补充路由
	authenticationv1.RegisterAuthenticationServiceHTTPServer(a.httpServer, domain.GetBean("authenticationService").(authenticationv1.AuthenticationServiceHTTPServer))
	resourcegroupv1.RegisterNodeServiceHTTPServer(a.httpServer, domain.GetBean("nodeService").(resourcegroupv1.NodeServiceHTTPServer))
	resourcegroupv1.RegisterResourceGroupServiceHTTPServer(a.httpServer, domain.GetBean("resourceGroupService").(resourcegroupv1.ResourceGroupServiceHTTPServer))
	platformv1.RegisterPlatformServiceHTTPServer(a.httpServer, domain.GetBean("platformService").(platformv1.PlatformServiceHTTPServer))
	workspacev1.RegisterWorkspaceServiceHTTPServer(a.httpServer, domain.GetBean("workspaceService").(workspacev1.WorkspaceServiceHTTPServer))
	userv1.RegisterUserServiceHTTPServer(a.httpServer, domain.GetBean("userService").(userv1.UserServiceHTTPServer))
	rbacv1.RegisterRbacServiceHTTPServer(a.httpServer, domain.GetBean("rbacService").(rbacv1.RbacServiceHTTPServer))
	queuev1.RegisterQueueServiceHTTPServer(a.httpServer, domain.GetBean("queueService").(queuev1.QueueServiceHTTPServer))
	jobv1.RegisterJobServiceHTTPServer(a.httpServer, domain.GetBean("jobService").(jobv1.JobServiceHTTPServer))
	metricsv1.RegisterMetricsServiceHTTPServer(a.httpServer, domain.GetBean("metricsService").(metricsv1.MetricsServiceHTTPServer))
	imagehubv1.RegisterImageHubServiceHTTPServer(a.httpServer, domain.GetBean("imageHubService").(imagehubv1.ImageHubServiceHTTPServer))
	devmachinev1.RegisterDevMachineServiceHTTPServer(a.httpServer, domain.GetBean("devMachineService").(devmachinev1.DevMachineServiceHTTPServer))
	interfaces.InitRouters()
	cubefsv1.RegisterCubeFSServiceHTTPServer(a.httpServer, domain.GetBean("cubeFSService").(cubefsv1.CubeFSServiceHTTPServer))
	modelhubv1.RegisterModelHubServiceHTTPServer(a.httpServer, domain.GetBean("modelHubService").(modelhubv1.ModelHubServiceHTTPServer))
	applicationv1.RegisterApplicationServiceHTTPServer(a.httpServer, domain.GetBean("applicationService").(applicationv1.ApplicationServiceHTTPServer))
	domainv1.RegisterDomainServiceHTTPServer(a.httpServer, domain.GetBean("domainService").(domainv1.DomainServiceHTTPServer))
	gatewayv1.RegisterDiscoveryServiceHTTPServer(a.httpServer, domain.GetBean("destinationService").(gatewayv1.DiscoveryServiceHTTPServer))
	gatewayv1.RegisterGatewayServiceHTTPServer(a.httpServer, domain.GetBean("gatewayService").(gatewayv1.GatewayServiceHTTPServer))
	costv1.RegisterCostServiceHTTPServer(a.httpServer, domain.GetBean("costService").(costv1.CostServiceHTTPServer))
	obscenterv1.RegisterObsCenterServiceHTTPServer(a.httpServer, domain.GetBean("obsCenterService").(obscenterv1.ObsCenterServiceHTTPServer))
	applicationv1.RegisterApplicationServiceHTTPServer(a.httpServer, domain.GetBean("applicationService").(applicationv1.ApplicationServiceHTTPServer))
	configv1.RegisterConfigServiceHTTPServer(a.httpServer, domain.GetBean("configService").(configv1.ConfigServiceHTTPServer))
	ticketv1.RegisterTicketServiceHTTPServer(a.httpServer, domain.GetBean("ticketService").(ticketv1.TicketServiceHTTPServer))
}

func (a *APIServer) initHTTPFilters() {
	a.httpServer.Use(authenticationv1.AuthenticationService_RefreshToken_FullMethodName, a.DefaultMiddlewares()...)
	a.httpServer.Use(authenticationv1.AuthenticationService_Logout_FullMethodName, a.DefaultMiddlewares()...)
	a.httpServer.Use("/apis.usercenter.v1*", a.DefaultMiddlewares()...)
	a.httpServer.Use("/apis.aistudio.platform.v1*", a.DefaultMiddlewares()...)
	a.httpServer.Use("/apis.workspace.v1*", a.DefaultMiddlewares()...)
	a.httpServer.Use("/apis.aistudio.resourcegroup.v1*", a.DefaultMiddlewares()...)
	a.httpServer.Use("/apis.aistudio.rbac.v1*", a.DefaultMiddlewares()...)
	a.httpServer.Use("/apis.aistudio.queue.v1*", a.DefaultMiddlewares()...)
	a.httpServer.Use("/apis.aistudio.job.v1*", a.DefaultMiddlewares()...)
	a.httpServer.Use("/apis.aistudio.config.v1*", a.DefaultMiddlewares()...)
	a.httpServer.Use("/apis.aistudio.devmachine.v1*", a.DefaultMiddlewares()...)
	a.httpServer.Use("/apis.proxy/VelaProxy", a.DefaultMiddlewares()...)
	a.httpServer.Use("/apis.kubernetes.proxy/clusters*", a.DefaultMiddlewares()...)
	a.httpServer.Use("/apis.aistudio.cubefs.v1*", a.DefaultMiddlewares()...)
	a.httpServer.Use("/apis.aistudio.modelhub.v1*", a.DefaultMiddlewares()...)
	a.httpServer.Use("/apis.aistudio.imagehub.v1*", a.DefaultMiddlewares()...)
	a.httpServer.Use("/apis.aistudio.application.v1*", a.AuthCheckMiddleware, a.ExtractWorkspaceMiddleware)
	a.httpServer.Use("/apis.aistudio.domain.v1*", a.DefaultMiddlewares()...)
	a.httpServer.Use("/apis.aistudio.obscenter.v1*", a.DefaultMiddlewares()...)
	a.httpServer.Use("/apis.external.modelhub*", a.DefaultMiddlewares()...)
	a.httpServer.Use("/apis.frontend*", a.DefaultMiddlewares()...)
	a.httpServer.Use("/apis.agent*")
	a.httpServer.Use("/apis.tickets/updateQueue", a.DefaultMiddlewares()...)
	//a.httpServer.Use("/apis.proxy/notebookProxy*", a.DefaultMiddlewares()...)
	//a.httpServer.Use("/apis.proxy/vscodeProxy*", a.DefaultMiddlewares()...)
	a.httpServer.Use("/apis.imagehub/synchronize*", a.DefaultMiddlewares()...)
	a.httpServer.Use("/apis.proxy/tensorboardProxy*", a.DefaultMiddlewares()...)
	a.httpServer.Use("/apis.observability.v1*", a.DefaultMiddlewares()...)
	a.httpServer.Use("/apis.aistudio.gateway.v1*", a.AuthCheckMiddleware, a.ExtractWorkspaceMiddleware)
	a.httpServer.Use("/gateway.apis*", a.DefaultMiddlewares()...)
	a.httpServer.Use("/apis/extension/v1*", a.DefaultMiddlewares()...)

	a.httpServer.Use("/apis.podinsight*", a.DefaultMiddlewares()...)
	a.httpServer.Use("/apis.opsone*")
	a.httpServer.Use("/apis.proxy/intelligentAssistantProxy", a.DefaultMiddlewares()...)
	a.httpServer.Use("/apis.aistudio.ticket.v1*", a.DefaultMiddlewares()...)
	a.httpServer.Use("/apis.aistudio.cost.v1*", a.DefaultMiddlewares()...)
}

func (a *APIServer) DefaultMiddlewares() []middleware.Middleware {
	return []middleware.Middleware{
		a.AuthCheckMiddleware,
	}
}

// ExtractWorkspaceMiddleware 精确匹配workspaceID
func (a *APIServer) ExtractWorkspaceMiddleware(handler middleware.Handler) middleware.Handler {
	return func(ctx context.Context, req interface{}) (interface{}, error) {
		httpCtx, ok := http.RequestFromServerContext(ctx)
		if !ok {
			return nil, bcode.ErrorServerInternalError("http.RequestFromServerContext failed")
		}
		if strings.Contains(httpCtx.URL.Path, "/apis/v1/workspace") {
			//标准的workspace请求
			rex := regexp.MustCompile(`/apis/v1/workspace?/([^/]+)`)
			parts := rex.FindStringSubmatch(httpCtx.URL.Path)
			if len(parts) > 1 {
				workspaceName := parts[1]
				if workspaceName != "" {
					workspaceBase, err := domain.GetBean("workspaceService").(*service.WorkspaceService).GetWorkspaceBase(httpCtx.Context(), &workspacev1.GetWorkspaceBaseRequest{
						Name: workspaceName,
					})
					if err != nil {
						return nil, bcode.ErrorServerInternalError("GetWorkspaceBase[%s] failed", workspaceName)
					}
					ctx = context.WithValue(ctx, constant.WorkspaceCtxKey, workspaceBase)
					var workspaceCR = v1alpha1.Workspace{}
					err = multicluster.Instance().GetLocalCluster().Direct().Get(ctx, client.ObjectKey{Name: workspaceName}, &workspaceCR)
					if err != nil {
						return nil, bcode.ErrorServerInternalError("GetWorkspaceCR[%s] failed", workspaceName)
					}
					ctx = context.WithValue(ctx, constant.WorkspaceCRCtxKey, &workspaceCR)
					return handler(ctx, req)
				}
			}
		}

		// appBase注入 app全局唯一,为了服务治理
		if strings.Contains(httpCtx.URL.Path, "/apis/v1/application/") {
			parts := strings.Split(httpCtx.URL.Path, "/")
			if len(parts) > 0 {
				appName := parts[4]
				if appName != "" {
					appBase, err := domain.GetBean("applicationService").(*service.ApplicationService).GetApplicationBase(httpCtx.Context(), &applicationv1.GetApplicationRequest{
						AppName: appName,
					})
					if err != nil {
						return nil, bcode.ErrorServerInternalError("GetAppBase[%s] failed", appName)
					}
					ctx = context.WithValue(ctx, constant.ApplicationCtxKey, appBase)
					workspaceName := appBase.WorkspaceName
					workspaceBase, err := domain.GetBean("workspaceService").(*service.WorkspaceService).GetWorkspaceBase(httpCtx.Context(), &workspacev1.GetWorkspaceBaseRequest{
						Name: workspaceName,
					})
					if err != nil {
						return nil, bcode.ErrorServerInternalError("GetWorkspaceBase[%s] failed", workspaceName)
					}
					ctx = context.WithValue(ctx, constant.WorkspaceCtxKey, workspaceBase)
					var workspaceCR = v1alpha1.Workspace{}
					err = multicluster.Instance().GetLocalCluster().Direct().Get(ctx, client.ObjectKey{Name: workspaceName}, &workspaceCR)
					if err != nil {
						return nil, bcode.ErrorServerInternalError("GetWorkspaceCR[%s] failed", workspaceName)
					}
					ctx = context.WithValue(ctx, constant.WorkspaceCRCtxKey, &workspaceCR)
					return handler(ctx, req)
				}
			}
		}

		return handler(ctx, req)
	}
}

func (a *APIServer) AuthCheckMiddleware(handler middleware.Handler) middleware.Handler {
	//注入环境信息
	env := a.properties.GetDefault("application.env", "prod")

	if !a.properties.GetBoolDefault("authentication.enabled", false) {
		return func(ctx context.Context, req interface{}) (interface{}, error) {
			ctx = context.WithValue(ctx, constant.UserCtxKey, a.properties.GetDefault("authentication.impersonation", "admin"))
			ctx = context.WithValue(ctx, constant.ApplicationEnvCtxKey, env)
			return handler(ctx, req)
		}
	}
	return func(ctx context.Context, req interface{}) (interface{}, error) {
		if header, ok := transport.FromServerContext(ctx); ok {
			auths := strings.SplitN(header.RequestHeader().Get(authorizationKey), " ", 2)
			if len(auths) != 2 || !strings.EqualFold(auths[0], bearerWord) {
				return nil, ErrMissingJwtToken
			}
			jwtToken := auths[1]

			// 解析 Token
			tokenInfo, err := a.validateToken(jwtToken)
			if err != nil {
				return nil, bcode.ErrorUnauthorized("token invalid, err: %v", err)
			}
			ctx = context.WithValue(ctx, constant.UserCtxKey, tokenInfo.Account)
			ctx = context.WithValue(ctx, constant.ApplicationEnvCtxKey, env)
			return handler(ctx, req)
		}
		return nil, ErrWrongContext
	}
}

// TokenInfo 定义 TokenInfo 结构体
type TokenInfo struct {
	Account            string
	GrantType          model.GrantType
	UserType           model.UserType
	AuthenticationType model.AuthenticationType
}

// validateToken 方法
func (a *APIServer) validateToken(jwtToken string) (*TokenInfo, error) {
	// 从配置文件中获取签名 key
	signedKey, err := a.properties.GetString("application.jwt.signedKey")
	if err != nil {
		return nil, bcode.ErrorServerInternalError("获取jwt签名key失败, err: %v", err)
	}

	// 解析 Token
	token, err := jwt.ParseWithClaims(jwtToken, &model.CustomClaims{}, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return []byte(signedKey), nil
	})

	if err != nil {
		return nil, bcode.ErrorUnauthorized("无效的token, err: %v", err)
	}

	if !token.Valid {
		return nil, bcode.ErrorUnauthorized("token已过期, 请重新登录")
	}

	// 获取用户信息
	claims, ok := token.Claims.(*model.CustomClaims)
	if !ok {
		return nil, bcode.ErrorUnauthorized("无效的token, 请重新登录")
	}
	return &TokenInfo{
		Account:            claims.Account,
		GrantType:          claims.GrantType,
		UserType:           claims.UserType,
		AuthenticationType: claims.AuthenticationType,
	}, nil
}

func (a *APIServer) getLogLevel() log.Level {
	switch a.properties.MustGetString("application.logLevel") {
	case "debug":
		return log.LevelDebug
	case "info":
		return log.LevelInfo
	case "warn":
		return log.LevelWarn
	case "error":
		return log.LevelError
	default:
		return log.LevelInfo
	}
}

var re = regexp.MustCompile(`password:"[^"]*"`)

func (a *APIServer) buildLogger() log.Logger {
	logger := log.NewStdLogger(os.Stdout)
	logger = log.NewFilter(logger, log.FilterLevel(a.getLogLevel()), log.FilterFunc(
		func(level log.Level, keyvals ...interface{}) bool {
			for i, kv := range keyvals {
				if s, ok := kv.(string); ok && strings.HasPrefix(s, "username") {
					keyvals[i] = re.ReplaceAllString(s, `password:"******"`)
				}
			}
			return false
		},
	))
	return log.With(logger,
		"timestamp", log.DefaultTimestamp,
		"caller", log.DefaultCaller,
		"application.name", a.properties.MustGetString("application.name"))
}

func (a *APIServer) initTracer(ctx context.Context) error {
	traceExporter, err := otlptracehttp.New(ctx, otlptracehttp.WithEndpoint(a.properties.MustGetString("application.trace.otel.endpoint")), otlptracehttp.WithInsecure())
	if err != nil {
		return fmt.Errorf("failed to create trace exporter: %w", err)
	}
	tp := tracesdk.NewTracerProvider(
		tracesdk.WithSampler(tracesdk.ParentBased(tracesdk.TraceIDRatioBased(1.0))),
		tracesdk.WithBatcher(traceExporter),
		tracesdk.WithResource(resource.NewSchemaless(
			semconv.ServiceNameKey.String(a.properties.MustGetString("application.name")),
			attribute.String("service.env", a.properties.MustGetString("application.env")),
		)),
	)
	otel.SetTracerProvider(tp)
	return nil
}

func (a *APIServer) initPolarisConfiguration(properties property.EnvironmentProperty) config.Configuration {
	cfg := api.NewConfiguration()
	grpcAddresses := properties.GetStringSlice("discovery.polaris.grpc.addresses")
	cfg.GetGlobal().GetServerConnector().SetAddresses(grpcAddresses)
	cfg.GetGlobal().GetServerConnector().SetConnectTimeout(properties.GetDurationDefault("discovery.polaris.grpc.connectTimeout", time.Second*10))
	cfg.GetGlobal().GetServerConnector().SetMessageTimeout(properties.GetDurationDefault("discovery.polaris.grpc.messageTimeout", time.Second*10))
	cfg.GetGlobal().GetSystem().GetDiscoverCluster().SetNamespace("Polaris")
	cfg.GetGlobal().GetSystem().GetDiscoverCluster().SetService("polaris.checker")
	cfg.GetGlobal().GetSystem().GetDiscoverCluster().SetRefreshInterval(10 * time.Minute)
	cfg.GetGlobal().GetAPI().SetTimeout(time.Second * 10)
	return cfg
}

// Server is an server logging middleware.
func Server(logger log.Logger) middleware.Middleware {
	return func(handler middleware.Handler) middleware.Handler {
		return func(ctx context.Context, req interface{}) (reply interface{}, err error) {
			var (
				code      int32
				reason    string
				kind      string
				operation string
			)
			startTime := time.Now()
			if info, ok := transport.FromServerContext(ctx); ok {
				kind = info.Kind().String()
				operation = info.Operation()
			}
			reply, err = handler(ctx, req)
			if se := errors.FromError(err); se != nil {
				code = se.Code
				reason = se.Reason
			}
			level, stack := extractError(err)
			_ = log.WithContext(ctx, logger).Log(level,
				"trace_id", tracing.TraceID(),
				"span_id", tracing.SpanID(),
				"kind", "server",
				"component", kind,
				"operation", operation,
				"args", extractArgs(req),
				"code", code,
				"reason", reason,
				"stack", stack,
				"latency", time.Since(startTime).Seconds(),
			)
			return
		}
	}
}

// extractArgs returns the string of the req
func extractArgs(req interface{}) string {
	if stringer, ok := req.(fmt.Stringer); ok {
		return stringer.String()
	}
	return fmt.Sprintf("%+v", req)
}

// extractError returns the string of the error
func extractError(err error) (log.Level, string) {
	if err != nil {
		return log.LevelError, fmt.Sprintf("%+v", err)
	}
	return log.LevelInfo, ""
}
