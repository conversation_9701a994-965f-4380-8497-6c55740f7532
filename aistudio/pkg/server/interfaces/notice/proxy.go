package notice

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"git.lianjia.com/cloudnative/kic/kic-platform/aistudio/pkg/server/domain/service"
	jobv1 "git.lianjia.com/cloudnative/kic/kic-platform/apis/aistudio/job/v1"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/property"
	thirdnotice "git.lianjia.com/cloudnative/kic/kic-platform/pkg/third_party/notice"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/third_party/uc"
	"github.com/go-kratos/kratos/v2/transport/http"
	"io"
	"k8s.io/klog/v2"
	sdkhttp "net/http"
	"strings"
	"text/template"
	"time"
)

// AlertData 夜莺告警
type AlertData struct {
	ID               int      `json:"id"`
	Tags             []string `json:"tags"`
	IsRecovered      bool     `json:"is_recovered"`
	NotifyUsersObj   []string `json:"notify_users_obj"`
	LastEvalTime     int64    `json:"last_eval_time"`
	LastSentTime     int64    `json:"last_sent_time"`
	NotifyCurNumber  int      `json:"notify_cur_number"`
	FirstTriggerTime int64    `json:"first_trigger_time"`
}

type TemplateData struct {
	TaskName    string
	TaskID      string
	Cluster     string
	Workspace   string
	Creator     string
	JobType     string
	RunningDur  string
	CreateTime  string
	Replicas    int32
	Queue       string
	TriggerTime string
	Color       string
	Status      string
	Description string
	UserId      string
}

type APIServer struct {
	httpServer *http.Server
	Properties property.EnvironmentProperty `inject:""`
	JobService *service.JobService          `inject:"jobService"`
}

func NewAPIServer(httpServer *http.Server) *APIServer {
	return &APIServer{
		httpServer: httpServer,
	}
}

func (n *APIServer) InitRouters() {
	n.httpServer.Route("/notice").POST("/send", func(c http.Context) error {
		http.SetOperation(c, "/notice.apis/send")
		key := c.Request().FormValue("key")
		if key == "" {
			return c.Result(sdkhttp.StatusBadRequest, "key is required")
		}
		body, err := io.ReadAll(c.Request().Body)
		if err != nil {
			return c.Result(sdkhttp.StatusInternalServerError, err.Error())
		}
		var alertData AlertData
		if err := json.Unmarshal(body, &alertData); err != nil {
			return c.Result(sdkhttp.StatusBadRequest, err.Error())
		}
		tags := alertData.Tags
		// 获取 job 信息
		jobRequest := &jobv1.GetJobRequest{
			WorkspaceName: getTagsValueByKey(tags, "workspace"),
			JobName:       getTagsValueByKey(tags, "created_by_name"),
			IsDeleted:     "false",
		}
		job, err := n.JobService.GetJobDetail(c, jobRequest)
		if job.AlarmShielding {
			return c.Result(sdkhttp.StatusOK, "alarm has been blocked")
		}
		if err != nil {
			return c.Result(sdkhttp.StatusInternalServerError, err.Error())
		}
		// 发送通知
		if err = sendNotice(c, key, job); err != nil {
			return c.Result(sdkhttp.StatusInternalServerError, err.Error())
		}
		return c.Result(sdkhttp.StatusOK, "success")
	})
}

func getTagsValueByKey(tags []string, key string) string {
	prefix := key + "="
	for _, tag := range tags {
		if strings.HasPrefix(tag, prefix) {
			return strings.SplitN(tag, "=", 2)[1]
		}
	}
	return ""
}

func sendNotice(ctx context.Context, key string, job *jobv1.Job) error {
	tmpl := `### 告警标题: 【Aistudio2.0平台】训练任务: {{.TaskName}} <font color={{.Color}}>训练{{.Status}}</font>
			#### 告警内容:
			> 任务 ID: <font color="comment">**{{.TaskID}}**</font>
			> 任务名称：<font color="black">**{{.TaskName}}**</font>
			> 工作空间：<font>{{.Workspace}}</font>
			> 任务队列：<font color="comment">{{.Queue}}</font>
			> 创建人: <font>{{.Creator}}</font>
			> 任务类型：<font color="blue">{{.JobType}}</font>
			> 任务创建时间：<font color="info">{{.CreateTime}}</font>
			> 任务运行时长：<font color="warning">{{.RunningDur}}</font>
			> 任务描述: <font color={{.Color}}>{{.Description}}</font>
			> 触发时间：<font color="comment">{{.TriggerTime}}</font>
            > <@{{.UserId}}>
`
	var runningDur, phase, status, color, desc string
	if job.JobStatus != nil {
		runningDur = job.JobStatus.RunningDuration
		phase = job.JobStatus.State
	}
	if phase == "Completed" {
		status = "完成"
		color = "info"
		desc = "任务已完成, 请知晓"
	} else if phase == "Failed" {
		status = "失败"
		color = "red"
		desc = "任务失败, 请及时处理"
	}
	// get user id
	data := struct{ TemplateData }{
		TemplateData{
			TaskID:      job.Name,
			TaskName:    job.DisplayName,
			Workspace:   job.WorkspaceName,
			Creator:     job.Creator,
			JobType:     job.JobType.String(),
			RunningDur:  runningDur,
			Queue:       job.QueueName,
			CreateTime:  job.CreateTime,
			TriggerTime: time.Now().Format(time.DateTime),
			Status:      status,
			Color:       color,
			Description: desc,
		},
	}
	userInfo, err := uc.Instance().GetUser(ctx, job.Creator)
	if err != nil {
		klog.Errorf("get user info failed: %v", err)
	} else {
		data.UserId = userInfo.Code
	}

	tmp, err := template.New("markdown").Parse(tmpl)
	if err != nil {
		return fmt.Errorf("Error parsing template: %v\n", err)
	}
	var content bytes.Buffer
	if err := tmp.Execute(&content, data); err != nil {
		fmt.Printf("Error executing template: %v\n", err)
		return err
	}
	request := thirdnotice.MessageMarkdownRequest{
		MsgType: thirdnotice.MsgTypeMarkdown,
		Markdown: thirdnotice.Content{
			Content: content.String(),
		},
	}
	if err := thirdnotice.Instance().SendMessages(ctx, key, request); err != nil {
		return err
	}
	return nil
}
