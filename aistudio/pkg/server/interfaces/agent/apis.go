package agent

import (
	sdkhttp "net/http"

	"git.lianjia.com/cloudnative/kic/kic-platform/aistudio/pkg/server/domain/service"
	v1 "git.lianjia.com/cloudnative/kic/kic-platform/apis/usercenter/v1"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/infrastructure/datastore"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/property"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/third_party/uc"
	"github.com/go-kratos/kratos/v2/transport/http"
)

const (
	PresetImageWorkspaceName = "aistudio"
)

type APIServer struct {
	httpServer  *http.Server
	Properties  property.EnvironmentProperty `inject:""`
	UserService *service.UserService         `inject:""`
	Store       datastore.DataStore          `inject:"datastore"`
}

func NewAPIServer(httpServer *http.Server) *APIServer {
	return &APIServer{
		httpServer: httpServer,
	}
}

func (a *APIServer) InitRouters() {
	agentRoute := a.httpServer.Route("/apis/agent")
	agentRoute.GET("/gettoken", func(c http.Context) error {
		http.SetOperation(c, "/apis.agent/gettoken")
		userID := c.Request().FormValue("userID")
		if userID == "" {
			return c.String(sdkhttp.StatusBadRequest, "userID is required")
		}
		ucClient := uc.Instance()
		userInfo, err := ucClient.GetUserByID(c, userID)
		if err != nil {
			return c.String(sdkhttp.StatusInternalServerError, err.Error())
		}
		if userInfo == nil || userInfo.Account == "" {
			return c.String(sdkhttp.StatusNotFound, "user not found")
		}
		account := userInfo.Account
		tokens, err := a.UserService.GetAccessToken(c, &v1.GetAccessTokenRequest{
			Account: account,
		})
		if err != nil {
			return c.String(sdkhttp.StatusInternalServerError, err.Error())
		}
		if tokens == nil || len(tokens.AccessTokens) == 0 {
			_, err := a.UserService.GenerateAccessToken(c, &v1.GenerateAccessTokenRequest{
				Account:    account,
				ExpireTime: **********,
			})
			if err != nil {
				return c.String(sdkhttp.StatusInternalServerError, err.Error())
			}
			tokens, err = a.UserService.GetAccessToken(c, &v1.GetAccessTokenRequest{
				Account: account,
			})
			if err != nil {
				return c.String(sdkhttp.StatusInternalServerError, err.Error())
			}
		}
		if tokens == nil || len(tokens.AccessTokens) == 0 {
			return c.String(sdkhttp.StatusInternalServerError, "get access token failed")
		}
		token := tokens.AccessTokens[0].AccessToken
		type Token struct {
			Token   string `json:"token"`
			Account string `json:"account"`
		}
		return c.JSON(sdkhttp.StatusOK, Token{Token: token, Account: account})
	})
}
