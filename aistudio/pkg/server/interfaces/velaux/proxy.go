package velaux

import (
	"context"
	"fmt"
	"git.lianjia.com/cloudnative/kic/kic-platform/aistudio/pkg/constant"
	"git.lianjia.com/cloudnative/kic/kic-platform/apis/aistudio/bcode"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/property"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/third_party/velaux"
	"github.com/go-kratos/kratos/v2/transport/http"
	"net/http/httputil"
	"net/url"
	"strings"
)

const (
	ImpersonateUserHeader = "X-AUTH-USER-IMPERSONATION"
)

type Proxy struct {
	httpServer *http.Server
	Properties property.EnvironmentProperty `inject:""`
}

func NewProxy(httpServer *http.Server) *Proxy {
	p := &Proxy{
		httpServer: httpServer,
	}
	return p
}

func (p *Proxy) InitRouters() {
	p.httpServer.Route("/velaux/proxy").HandlePrefix("/", func(c http.Context) error {
		http.SetOperation(c, "/apis.proxy/VelaProxy")
		var userKey string
		h := c.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			var globalURL *url.URL
			velauxUrl, err := p.Properties.GetString("velaux.url")
			if err != nil {
				return nil, err
			}
			uk, ok := ctx.Value(constant.UserCtxKey).(string)
			if !ok {
				return nil, bcode.ErrorUnauthorized("user key not found")
			}
			userKey = uk
			globalURL, err = url.Parse(velauxUrl)
			if err != nil {
				return nil, err
			}
			return globalURL, nil
		})
		output, err := h(c, nil)
		if err != nil {
			return bcode.ErrorServerInternalError("get velaux url failed: %v", err)
		}

		globalURL := output.(*url.URL)
		realPath := c.Request().URL.Path[strings.Index(c.Request().URL.Path, "proxy")+5:]
		if realPath == "" {
			realPath = "/"
		}
		targetURL := globalURL.JoinPath(realPath)
		proxy := httputil.NewSingleHostReverseProxy(globalURL)
		proxy.Director = func(req *http.Request) {
			req.URL.Path = realPath
			req.URL.Host = targetURL.Host
			req.URL.Scheme = targetURL.Scheme
			req.Host = targetURL.Host
			req.Header.Set(ImpersonateUserHeader, userKey)
			req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", velaux.Instance().GetAccessToken()))
			req.Header.Set("Host", targetURL.Host)
			if strings.Contains(req.URL.Path, "/api/v1") {
				req.Header.Set("Content-Type", "application/json")
			}
		}
		proxy.ServeHTTP(c.Response(), c.Request())
		return nil
	})
}
