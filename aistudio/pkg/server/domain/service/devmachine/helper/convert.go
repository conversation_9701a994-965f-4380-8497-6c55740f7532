package helper

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"git.lianjia.com/cloudnative/kic/kic-platform/aistudio/pkg/constant"
	"git.lianjia.com/cloudnative/kic/kic-platform/aistudio/pkg/server/domain/model"
	servicehelper "git.lianjia.com/cloudnative/kic/kic-platform/aistudio/pkg/server/domain/service/helper"
	devmachinev1 "git.lianjia.com/cloudnative/kic/kic-platform/apis/aistudio/devmachine/v1"
	"git.lianjia.com/cloudnative/kic/kic-platform/apis/common"
	automlkcsiov1alpha1 "git.lianjia.com/cloudnative/kic/kic-platform/pkg/apis/automl.kcs.io/v1alpha1"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/multicluster"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/util"
	"go.mongodb.org/mongo-driver/bson/primitive"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/klog/v2"
	"sigs.k8s.io/controller-runtime/pkg/client"
)

const (
	DevMachineStatePending     = "Pending"   // 资源不足时,为 pending
	DevMachineStateDeploying   = "Deploying" // 已经调度到节点上
	DevMachineStateRunning     = "Running"   // pod 运行中
	DevMachineStateTerminating = "Terminating"
	DevMachineStateTerminated  = "Terminated"
	DevMachineStateUnknown     = "Unknown"
	DefaultSSHPort             = 10022
	DevMachineStateError       = "Error"
)

const (
	VolumeNameHadoopClient = "volume-hadoop-client"
	VolumeNameKeytab       = "volume-keytab"
)

func GetClusterNamespaceName(workspaceName string) string {
	return constant.AIStudioPrefix + workspaceName
}

func ConvertToDevMachineEntity(request *devmachinev1.CreateDevMachineRequest) *model.DevMachineEntity {
	dmEntity := &model.DevMachineEntity{}
	dmEntity.ID = primitive.NewObjectID()
	if request.Name == "" {
		dmEntity.Name = util.GenerateRandomStringWithPrefix("d", 4)
	} else {
		dmEntity.Name = request.Name
	}
	dmEntity.Region = request.Region
	dmEntity.Members = request.Members
	dmEntity.Description = request.Description
	dmEntity.DisplayName = request.DisplayName
	dmEntity.WorkspaceName = request.WorkspaceName
	dmEntity.Labels = request.Labels
	dmEntity.Image = request.Image
	dmEntity.QueueName = request.QueueName
	dmEntity.EnvVars = servicehelper.EnvVarsToEnvEntity(request.EnvVars)
	dmEntity.Specification = servicehelper.SpecificationToEntity(request.Specification)
	dmEntity.NodeSpecificationName = request.NodeSpecificationName
	dmEntity.NodeIP = request.NodeIP
	dmEntity.VolumeSpecs = servicehelper.VolumeSpecToEntity(request.VolumeSpecs)
	dmEntity.TensorboardEnabled = request.TensorboardEnabled

	dmEntity.SSHConfig = &model.SSHConfig{
		SSHEnabled:  request.SshConfig.EnableSSH,
		SSHUser:     request.SshConfig.SshUser,
		SSHPassword: request.SshConfig.SshPassword,
		SSHKeys:     request.SshConfig.SshKeys,
	}
	dmEntity.IsDeleted = false
	dmEntity.HadoopEnabled = request.HadoopEnabled
	dmEntity.HadoopUsers = request.HadoopUsers
	dmEntity.HostNetworkEnabled = request.HostNetworkEnabled
	dmEntity.HostIPCEnabled = request.HostIPCEnabled
	dmEntity.PrivilegedModeEnabled = request.PrivilegedModeEnabled
	dmEntity.ExposePortConfigs = exposePortsToEntity(request.ExposePortConfigs)
	dmEntity.GpuShareEnabled = request.GpuShareEnabled
	dmEntity.ConfigSpecs = model.ConvertToConfigSpecEntity(request.ConfigSpecs)
	return dmEntity
}

func exposePortsToEntity(accessPorts []*devmachinev1.ExposePortConfig) []*model.ExposePortConfig {
	if accessPorts == nil {
		return nil
	}
	var portEntities []*model.ExposePortConfig
	for _, port := range accessPorts {
		portEntities = append(portEntities, &model.ExposePortConfig{
			TargetPort: port.TargetPort,
			NodePort:   port.NodePort,
			Protocol:   port.Protocol.String(),
		})
	}
	return portEntities
}

func ConvertToUpdateDevMachineEntity(devMachineEntity *model.DevMachineEntity, request *devmachinev1.UpdateDevMachineRequest) *model.DevMachineEntity {
	devMachineEntity.Description = request.Description
	devMachineEntity.DisplayName = request.DisplayName
	devMachineEntity.WorkspaceName = request.WorkspaceName
	devMachineEntity.Labels = request.Labels
	devMachineEntity.Image = request.Image
	devMachineEntity.QueueName = request.QueueName
	devMachineEntity.EnvVars = servicehelper.EnvVarsToEnvEntity(request.EnvVars)
	devMachineEntity.Specification = servicehelper.SpecificationToEntity(request.Specification)
	devMachineEntity.NodeSpecificationName = request.NodeSpecificationName
	devMachineEntity.NodeIP = request.NodeIP
	devMachineEntity.VolumeSpecs = servicehelper.VolumeSpecToEntity(request.VolumeSpecs)
	devMachineEntity.TensorboardEnabled = request.TensorboardEnabled

	devMachineEntity.SSHConfig = &model.SSHConfig{
		SSHEnabled:  request.SshConfig.EnableSSH,
		SSHUser:     request.SshConfig.SshUser,
		SSHPassword: request.SshConfig.SshPassword,
		SSHKeys:     request.SshConfig.SshKeys,
	}
	devMachineEntity.IsDeleted = false
	devMachineEntity.HadoopEnabled = request.HadoopEnabled
	devMachineEntity.HadoopUsers = request.HadoopUsers
	devMachineEntity.IsDeleted = false
	devMachineEntity.HostNetworkEnabled = request.HostNetworkEnabled
	devMachineEntity.HostIPCEnabled = request.HostIPCEnabled
	devMachineEntity.PrivilegedModeEnabled = request.PrivilegedModeEnabled
	devMachineEntity.ExposePortConfigs = exposePortsToEntity(request.ExposePortConfigs)
	devMachineEntity.GpuShareEnabled = request.GpuShareEnabled
	devMachineEntity.ConfigSpecs = model.ConvertToConfigSpecEntity(request.ConfigSpecs)
	return devMachineEntity
}

func CovertFromDevMachineEntity(devMachineEntity *model.DevMachineEntity) *devmachinev1.DevMachine {
	return &devmachinev1.DevMachine{
		Id:            devMachineEntity.ID.Hex(),
		Name:          devMachineEntity.Name,
		Region:        devMachineEntity.Region,
		Description:   devMachineEntity.Description,
		DisplayName:   devMachineEntity.DisplayName,
		WorkspaceName: devMachineEntity.WorkspaceName,
		Members:       devMachineEntity.Members,
		Managers:      devMachineEntity.Managers,
		Labels:        devMachineEntity.Labels,
		Image:         devMachineEntity.Image,
		QueueName:     devMachineEntity.QueueName,
		EnvVars:       servicehelper.EnvEntityToEnvVars(devMachineEntity.EnvVars),
		Specification: &common.Specification{
			CpuNum:    devMachineEntity.Specification.CpuNum,
			MemoryGiB: devMachineEntity.Specification.MemoryGiB,
			GpuNum:    devMachineEntity.Specification.GpuNum,
			GpuMem:    devMachineEntity.Specification.GpuMem,
		},
		NodeSpecificationName: devMachineEntity.NodeSpecificationName,
		NodeIP:                devMachineEntity.NodeIP,
		VolumeSpecs:           servicehelper.VolumeEntityToVolumeSpecs(devMachineEntity.VolumeSpecs),
		TensorboardEnabled:    devMachineEntity.TensorboardEnabled,
		SshConfig: &devmachinev1.SSHConfig{
			EnableSSH:   devMachineEntity.SSHConfig.SSHEnabled,
			SshUser:     devMachineEntity.SSHConfig.SSHUser,
			SshPassword: devMachineEntity.SSHConfig.SSHPassword,
			SshKeys:     devMachineEntity.SSHConfig.SSHKeys,
		},
		IsDeleted:             devMachineEntity.IsDeleted,
		CreateTime:            util.TimeFormat(devMachineEntity.CreateTime),
		UpdateTime:            util.TimeFormat(devMachineEntity.UpdateTime),
		Creator:               devMachineEntity.Creator,
		HostNetworkEnabled:    devMachineEntity.HostNetworkEnabled,
		HostIPCEnabled:        devMachineEntity.HostIPCEnabled,
		PrivilegedModeEnabled: devMachineEntity.PrivilegedModeEnabled,
		ExposePortConfigs:     exposePortEntityToConfig(devMachineEntity.ExposePortConfigs),
		GpuShareEnabled:       devMachineEntity.GpuShareEnabled,
		ConfigSpecs:           model.ConvertToConfigSpec(devMachineEntity.ConfigSpecs),
		HadoopEnabled:         devMachineEntity.HadoopEnabled,
		HadoopUsers:           devMachineEntity.HadoopUsers,
	}
}

func exposePortEntityToConfig(accessPorts []*model.ExposePortConfig) []*devmachinev1.ExposePortConfig {
	if accessPorts == nil {
		return nil
	}
	var portConfigs []*devmachinev1.ExposePortConfig
	for _, port := range accessPorts {
		portConfigs = append(portConfigs, &devmachinev1.ExposePortConfig{
			TargetPort: port.TargetPort,
			NodePort:   port.NodePort,
			Protocol:   devmachinev1.ProtocolType(devmachinev1.ProtocolType_value[port.Protocol]),
		})
	}
	return portConfigs
}

func UpdateNotebookWithDevMachine(ctx context.Context, devMachineEntity *model.DevMachineEntity, clusterName, nodeSpecificationName, nodeName, initImage string, notebook *automlkcsiov1alpha1.Notebook, updater string) *automlkcsiov1alpha1.Notebook {
	updateNotebook := notebook.DeepCopy()
	annotations := map[string]string{
		constant.DevMachineDisplayNameAnnotationKey: devMachineEntity.DisplayName,
		constant.SchedulerVolcanoQueueAnnotationKey: fmt.Sprintf("%s-%s", devMachineEntity.WorkspaceName, devMachineEntity.QueueName),
		constant.DevMachineStartAnnotationKey:       fmt.Sprintf("start by %s at %s", devMachineEntity.Creator, time.Now().Format(time.DateTime)),
	}

	labels := map[string]string{}
	if updateNotebook.ObjectMeta.Labels != nil {
		labels = updateNotebook.ObjectMeta.Labels
}
	labels[constant.UpdateByLabelKey] = updater
	labels[constant.WorkloadTypeLabelKey] = constant.WorkloadTypeDevMachine
	if devMachineEntity.GpuShareEnabled {
		labels[constant.NodeGPUSharedMemoryLabelKey] = "enabled"
	} else {
		delete(labels, constant.NodeGPUSharedMemoryLabelKey)
	}

	updateNotebook.ObjectMeta.Annotations = annotations
	spec := generateNotebookSpec(devMachineEntity, nodeSpecificationName, nodeName, initImage, clusterName)
	updateNotebook.Spec.Cluster = clusterName
	updateNotebook.Spec.Template.Spec = spec
	if devMachineEntity.SSHConfig != nil {
		updateNotebook.Spec.SSHConfigSpec = automlkcsiov1alpha1.SSHConfigSpec{
			SSHEnabled:  devMachineEntity.SSHConfig.SSHEnabled,
			SSHUser:     devMachineEntity.SSHConfig.SSHUser,
			SSHPassword: devMachineEntity.SSHConfig.SSHPassword,
			SSHKeys:     devMachineEntity.SSHConfig.SSHKeys,
			SSHPort:     DefaultSSHPort,
		}
	}
	if devMachineEntity.ExposePortConfigs != nil {
		clusterServicePorts, err := listClusterServicePorts(ctx, clusterName)
		if err != nil {
			klog.Errorf("failed to list cluster service ports: %v", err)
		}
		updateNotebook.Spec.ExposePortConfigs = portEntityToAutoML(clusterServicePorts, devMachineEntity.ExposePortConfigs)
	}
	return updateNotebook
}

// CreateNotebookWithDevMachine create notebook with dev machine entity
func CreateNotebookWithDevMachine(ctx context.Context, devMachineEntity *model.DevMachineEntity, clusterName, nodeSpecificationName, nodeName, initImage string) *automlkcsiov1alpha1.Notebook {
	replicas := int32(1)
	labels := map[string]string{
		constant.DevMachineNameLabelKey: devMachineEntity.Name,
		constant.WorkspaceNameLabelKey:  devMachineEntity.WorkspaceName,
		constant.QueueNameLabelKey:      devMachineEntity.QueueName,
		constant.RegionLabelKey:         devMachineEntity.Region,
		constant.CreateByLabelKey:       devMachineEntity.Creator,
		constant.WorkloadTypeLabelKey:   constant.WorkloadTypeDevMachine,
		constant.WorkloadNameLabelKey:   devMachineEntity.Name,
	}
	if devMachineEntity.GpuShareEnabled {
		labels[constant.NodeGPUSharedMemoryLabelKey] = "enabled"
	}
	notebook := &automlkcsiov1alpha1.Notebook{
		ObjectMeta: metav1.ObjectMeta{
			Name:      devMachineEntity.Name,
			Namespace: GetClusterNamespaceName(devMachineEntity.WorkspaceName),
			Labels:    labels,
			Annotations: map[string]string{
				constant.DevMachineDisplayNameAnnotationKey: devMachineEntity.DisplayName,
				constant.SchedulerVolcanoQueueAnnotationKey: fmt.Sprintf("%s-%s", devMachineEntity.WorkspaceName, devMachineEntity.QueueName),
				constant.DevMachineStartAnnotationKey:       fmt.Sprintf("start by %s at %s", devMachineEntity.Creator, time.Now().Format(time.DateTime)),
			},
		},
		Spec: automlkcsiov1alpha1.NotebookSpec{
			Replicas:     &replicas,
			Cluster:      clusterName,
			ExposureMode: automlkcsiov1alpha1.ExposureServiceNodePort,
			Template: automlkcsiov1alpha1.NotebookTemplateSpec{
				Spec: generateNotebookSpec(devMachineEntity, nodeSpecificationName, nodeName, initImage, clusterName),
			},
			Shutdown: false,
		},
	}
	if devMachineEntity.SSHConfig != nil {
		notebook.Spec.SSHConfigSpec = automlkcsiov1alpha1.SSHConfigSpec{
			SSHEnabled:  devMachineEntity.SSHConfig.SSHEnabled,
			SSHUser:     devMachineEntity.SSHConfig.SSHUser,
			SSHPassword: devMachineEntity.SSHConfig.SSHPassword,
			SSHKeys:     devMachineEntity.SSHConfig.SSHKeys,
			SSHPort:     DefaultSSHPort,
		}
	}
	if devMachineEntity.ExposePortConfigs != nil {
		clusterServicePorts, err := listClusterServicePorts(ctx, clusterName)
		if err != nil {
			klog.Errorf("failed to list cluster service ports: %v", err)
		}
		notebook.Spec.ExposePortConfigs = portEntityToAutoML(clusterServicePorts, devMachineEntity.ExposePortConfigs)
	}

	return notebook
}

func portEntityToAutoML(clusterServicePorts map[int32]bool, accessPorts []*model.ExposePortConfig) []automlkcsiov1alpha1.ExposePortConfig {
	if accessPorts == nil {
		return nil
	}
	var portConfigs []automlkcsiov1alpha1.ExposePortConfig
	for _, port := range accessPorts {
		exportConfig := automlkcsiov1alpha1.ExposePortConfig{
			Name:       fmt.Sprintf("%s-%s", util.GenerateAlphabeticString(4), strconv.Itoa(int(port.TargetPort))),
			TargetPort: port.TargetPort,
			NodePort:   port.NodePort,
			Protocol:   port.Protocol,
		}
		if _, ok := clusterServicePorts[port.NodePort]; ok {
			exportConfig.NodePort = 0
		}
		portConfigs = append(portConfigs, exportConfig)
	}
	return portConfigs
}

func listClusterServicePorts(ctx context.Context, clusterName string) (map[int32]bool, error) {
	k8sCluster := multicluster.Instance().GetCluster(clusterName)
	if k8sCluster == nil {
		return nil, fmt.Errorf("cluster %s not found", clusterName)
	}
	// 查看特定集群的所有service
	var serviceList = &corev1.ServiceList{}
	err := k8sCluster.Direct().List(ctx, serviceList, &client.ListOptions{})
	if err != nil {
		return nil, err
	}
	servicePortMap := make(map[int32]bool)
	for _, svc := range serviceList.Items {
		if svc.Spec.Type == corev1.ServiceTypeNodePort || svc.Spec.Type == corev1.ServiceTypeLoadBalancer {
			for _, svcPort := range svc.Spec.Ports {
				servicePortMap[svcPort.NodePort] = true
			}
		}
	}
	return servicePortMap, nil
}

func generateNotebookInitContainer(devMachineEntity *model.DevMachineEntity, initImage string) []corev1.Container {
	cmdArgs := []string{
		"cp -r /notebook-init-container-files/. /aicp-init",
	}
	volumeMounts := []corev1.VolumeMount{
		{
			Name:      "shared-memory-devshm",
			MountPath: "/dev/shm",
		},
		{
			Name:      "empty-dir-notebook-init-copy",
			MountPath: "/aicp-init",
		},
	}
	if devMachineEntity.HadoopEnabled {
		cmdArgs = append(cmdArgs, "cp -r /home/<USER>/. /client-volume")
		volumeMounts = append(volumeMounts, corev1.VolumeMount{
			Name:      VolumeNameHadoopClient,
			MountPath: "/client-volume",
		})
	}
	container := corev1.Container{
		Name:  "notebook-init-copy",
		Image: initImage,
		Command: []string{
			"/bin/sh",
			"-c",
		},
		Args: []string{
			strings.Join(cmdArgs, ";"),
		},
		Resources: corev1.ResourceRequirements{
			Limits: corev1.ResourceList{
				corev1.ResourceCPU:    resource.MustParse("100m"),
				corev1.ResourceMemory: resource.MustParse("256Mi"),
			},
			Requests: corev1.ResourceList{
				corev1.ResourceCPU:    resource.MustParse("100m"),
				corev1.ResourceMemory: resource.MustParse("256Mi"),
			},
		},
		VolumeMounts:             volumeMounts,
		TerminationMessagePath:   "/dev/termination-log",
		TerminationMessagePolicy: corev1.TerminationMessageReadFile,
		ImagePullPolicy:          corev1.PullAlways,
	}
	return []corev1.Container{container}
}

func generateNotebookSpec(devMachineEntity *model.DevMachineEntity, nodeSpecificationName, nodeName, initImage, clusterName string) corev1.PodSpec {
	container := corev1.Container{
		Name:       devMachineEntity.Name,
		Image:      devMachineEntity.Image,
		WorkingDir: "/aistudio/workspace",
		Command:    []string{"/aicp-init/script/aicp-init"},
		Env:        convertEnvVarsToCoreEnvVars(devMachineEntity),
		Resources: corev1.ResourceRequirements{
			Limits:   corev1.ResourceList{},
			Requests: corev1.ResourceList{},
		},
		TerminationMessagePath:   "/dev/termination-log",
		TerminationMessagePolicy: corev1.TerminationMessageReadFile,
		ImagePullPolicy:          corev1.PullAlways,
	}
	if devMachineEntity.Specification.CpuNum != 0 {
		container.Resources.Requests[corev1.ResourceCPU] = resource.MustParse(strconv.Itoa(int(devMachineEntity.Specification.CpuNum)))
		container.Resources.Limits[corev1.ResourceCPU] = resource.MustParse(strconv.Itoa(int(devMachineEntity.Specification.CpuNum)))
	} else {
		container.Resources.Requests[corev1.ResourceCPU] = resource.MustParse("0")
		container.Resources.Limits[corev1.ResourceCPU] = resource.MustParse("0")
	}
	if devMachineEntity.Specification.MemoryGiB != 0 {
		container.Resources.Requests[corev1.ResourceMemory] = resource.MustParse(fmt.Sprintf("%dGi", devMachineEntity.Specification.MemoryGiB))
		container.Resources.Limits[corev1.ResourceMemory] = resource.MustParse(fmt.Sprintf("%dGi", devMachineEntity.Specification.MemoryGiB))
	} else {
		container.Resources.Requests[corev1.ResourceMemory] = resource.MustParse("0")
		container.Resources.Limits[corev1.ResourceMemory] = resource.MustParse("0")
	}

	container.VolumeMounts = convertToCoreVolumeMounts(devMachineEntity)
	terminationGracePeriodSeconds := int64(30)

	securityContext := &corev1.SecurityContext{
		Capabilities: &corev1.Capabilities{
			Add: []corev1.Capability{"SYS_ADMIN", "IPC_LOCK"},
		},
	}

	if devMachineEntity.PrivilegedModeEnabled {
		securityContext.Privileged = util.BoolPtr(true)
	}

	container.SecurityContext = securityContext

	spec := corev1.PodSpec{
		Volumes:        convertToCoreVolumes(devMachineEntity),
		InitContainers: generateNotebookInitContainer(devMachineEntity, initImage),
		Containers:     []corev1.Container{container},
		Tolerations: []corev1.Toleration{
			{
				Key:      "aicp.group/worker",
				Operator: corev1.TolerationOpExists,
				Effect:   corev1.TaintEffectNoSchedule,
			},
			{
				Key:      constant.AIStudioTaintKey,
				Operator: corev1.TolerationOpEqual,
				Value:    "aistudio",
			},
		},
		RestartPolicy:                 corev1.RestartPolicyAlways,
		DNSPolicy:                     corev1.DNSClusterFirst,
		TerminationGracePeriodSeconds: &terminationGracePeriodSeconds,
	}

	// 网络配置
	if devMachineEntity.HostNetworkEnabled {
		spec.HostNetwork = true
		spec.DNSPolicy = corev1.DNSClusterFirstWithHostNet
	}

	if devMachineEntity.HostIPCEnabled {
		spec.HostIPC = true
	}

	var matchExpressions []corev1.NodeSelectorRequirement
	if nodeName != "" {
		// TODO: TKE02集群因为 1.0还在用 volcaon 没升级,用不了 nodegroup,这里先兼容一下  后面升级后去掉
		if clusterName == "kcs-online-tke02" {
			spec.NodeName = nodeName
		} else {
			matchExpressions = append(matchExpressions, corev1.NodeSelectorRequirement{
				Key:      "kubernetes.io/hostname",
				Operator: corev1.NodeSelectorOpIn,
				Values:   []string{nodeName},
			})
		}

	} else if nodeSpecificationName != "" {
		nodeSpecificationNameHash := util.Md5s(nodeSpecificationName)
		matchExpressions = append(matchExpressions, corev1.NodeSelectorRequirement{
			Key:      constant.NodeSpecificationNameHashLabelKey,
			Operator: corev1.NodeSelectorOpIn,
			Values:   []string{nodeSpecificationNameHash},
		})
	}

	if devMachineEntity.GpuShareEnabled {
		matchExpressions = append(matchExpressions, corev1.NodeSelectorRequirement{
			Key:      constant.NodeGPUSharedMemoryLabelKey,
			Operator: corev1.NodeSelectorOpExists,
		})
		matchExpressions = append(matchExpressions, corev1.NodeSelectorRequirement{
			Key:      constant.QueueNameLabelKey,
			Operator: corev1.NodeSelectorOpIn,
			Values: []string{
				devMachineEntity.QueueName,
			},
		})
		if devMachineEntity.Specification.GpuNum != 0 {
			container.Resources.Requests[constant.ResourceNvidiaGPU] = resource.MustParse(strconv.Itoa(int(devMachineEntity.Specification.GpuNum)))
			container.Resources.Limits[constant.ResourceNvidiaGPU] = resource.MustParse(strconv.Itoa(int(devMachineEntity.Specification.GpuNum)))
		} else {
			container.Resources.Requests[constant.ResourceNvidiaGPU] = resource.MustParse("1")
			container.Resources.Limits[constant.ResourceNvidiaGPU] = resource.MustParse("1")
		}
		if devMachineEntity.Specification.GpuMem != 0 {
			container.Resources.Requests[constant.ResourceNvidiaGPUMemory] = resource.MustParse(strconv.Itoa(int(devMachineEntity.Specification.GpuMem)))
			container.Resources.Limits[constant.ResourceNvidiaGPUMemory] = resource.MustParse(strconv.Itoa(int(devMachineEntity.Specification.GpuMem)))
		}

	} else {
		spec.SchedulerName = "volcano"
		//可以在选择了机型之后，只设置GPU数量，不设置CPU和内存的资源配额了
		if devMachineEntity.Specification.GpuNum != 0 {
			container.Resources.Requests[constant.ResourceNvidiaGPU] = resource.MustParse(strconv.Itoa(int(devMachineEntity.Specification.GpuNum)))
			container.Resources.Limits[constant.ResourceNvidiaGPU] = resource.MustParse(strconv.Itoa(int(devMachineEntity.Specification.GpuNum)))
		} else {
			container.Resources.Requests[constant.ResourceNvidiaGPU] = resource.MustParse("0")
			container.Resources.Limits[constant.ResourceNvidiaGPU] = resource.MustParse("0")
		}
		matchExpressions = append(matchExpressions, corev1.NodeSelectorRequirement{
			Key:      constant.NodeGPUSharedMemoryLabelKey,
			Operator: corev1.NodeSelectorOpDoesNotExist,
		})
	}

	if len(matchExpressions) > 0 {
		spec.Affinity = &corev1.Affinity{
			NodeAffinity: &corev1.NodeAffinity{
				RequiredDuringSchedulingIgnoredDuringExecution: &corev1.NodeSelector{
					NodeSelectorTerms: []corev1.NodeSelectorTerm{
						{
							MatchExpressions: matchExpressions,
						},
					},
				},
			},
		}
	}

	return spec
}

func convertEnvVarsToCoreEnvVars(devMachine *model.DevMachineEntity) []corev1.EnvVar {
	envs := servicehelper.CovertToCoreEnvFromEnvVar(devMachine.EnvVars, devMachine.TensorboardEnabled)
	envs = append(envs, corev1.EnvVar{
		Name:  "WORK_DIR",
		Value: "/aistudio/workspace",
	})
	envs = append(envs, []corev1.EnvVar{
		{
			Name:  "S6_SERVICES_READYTIME",
			Value: "5000",
		},
		{
			Name:  "S6_KILL_FINISH_MAXTIME",
			Value: "600000",
		},
		{
			Name:  "S6_CMD_WAIT_FOR_SERVICES_MAXTIME",
			Value: "600000",
		},
	}...)
	if devMachine.HadoopEnabled {
		envs = append(envs, corev1.EnvVar{
			Name:  "HADOOP_USERS",
			Value: strings.Join(devMachine.HadoopUsers, ","),
		})
	}
	if devMachine.Specification.GpuNum == 0 {
		envs = append(envs, corev1.EnvVar{
			Name:  "CUDA_VISIBLE_DEVICES",
			Value: "",
		})
	}
	return envs
}

func convertToCoreVolumes(devMachineEntity *model.DevMachineEntity) []corev1.Volume {
	volumes := servicehelper.ConvertToCoreVolume(devMachineEntity.VolumeSpecs, devMachineEntity.TensorboardEnabled)
	quantity100Gi := resource.MustParse("100Gi")
	quantity500Mi := resource.MustParse("500Mi")
	volumes = append(volumes, corev1.Volume{
		Name: "shared-memory-devshm",
		VolumeSource: corev1.VolumeSource{
			EmptyDir: &corev1.EmptyDirVolumeSource{
				Medium:    corev1.StorageMediumMemory,
				SizeLimit: &quantity100Gi,
			},
		},
	})
	volumes = append(volumes, corev1.Volume{
		Name: "empty-dir-notebook-init-copy",
		VolumeSource: corev1.VolumeSource{
			EmptyDir: &corev1.EmptyDirVolumeSource{
				SizeLimit: &quantity500Mi,
			},
		},
	})
	if devMachineEntity.HadoopEnabled {
		quantity10Gi := resource.MustParse("10Gi")
		volumes = append(volumes, corev1.Volume{
			Name: VolumeNameHadoopClient,
			VolumeSource: corev1.VolumeSource{
				EmptyDir: &corev1.EmptyDirVolumeSource{
					SizeLimit: &quantity10Gi,
				},
			},
		})
		for _, hadoopUser := range devMachineEntity.HadoopUsers {
			volumes = append(volumes, corev1.Volume{
				Name: GetHadoopVolumeName(hadoopUser),
				VolumeSource: corev1.VolumeSource{
					Secret: &corev1.SecretVolumeSource{
						SecretName:  GetHadoopSecretName(hadoopUser),
						DefaultMode: util.Int32Ptr(0600),
					},
				},
			})
		}
	}
	if len(devMachineEntity.ConfigSpecs) > 0 {
		for _, configSpec := range devMachineEntity.ConfigSpecs {
			volume, _ := model.ConvertToVolume(configSpec)
			if volume != nil {
				volumes = append(volumes, *volume)
			}
		}
	}
	return volumes
}

// GetHadoopVolumeName 获取Hadoop用户对应的volume名称
func GetHadoopVolumeName(hadoopUser string) string {
	hadoopUser = strings.ReplaceAll(hadoopUser, "_", "-")
	return fmt.Sprintf("%s-%s", VolumeNameKeytab, hadoopUser)
}

// GetHadoopSecretName 获取Hadoop用户对应的keytab文件名
func GetHadoopSecretName(hadoopUser string) string {
	return strings.ReplaceAll(hadoopUser, "_", "-")
}

// convertToCoreVolumeMounts 将devMachineEntity的VolumeSpecs转换为corev1.VolumeMount
func convertToCoreVolumeMounts(devMachineEntity *model.DevMachineEntity) []corev1.VolumeMount {
	volumeMounts := servicehelper.ConvertToCoreVolumeMounts(devMachineEntity.VolumeSpecs)
	if devMachineEntity.TensorboardEnabled {
		volumeMounts = append(volumeMounts, corev1.VolumeMount{
			Name:      "tensorboard",
			MountPath: "/opt/tensorboard/logs",
			SubPath:   devMachineEntity.Name,
		})
	}
	volumeMounts = append(volumeMounts, corev1.VolumeMount{
		Name:      "shared-memory-devshm",
		MountPath: "/dev/shm",
	})
	volumeMounts = append(volumeMounts, corev1.VolumeMount{
		Name:      "empty-dir-notebook-init-copy",
		MountPath: "/aicp-init",
	})
	if devMachineEntity.HadoopEnabled {
		volumeMounts = append(volumeMounts, corev1.VolumeMount{
			Name:      VolumeNameHadoopClient,
			MountPath: "/home/<USER>",
		})
		for _, hadoopUser := range devMachineEntity.HadoopUsers {
			volumeMounts = append(volumeMounts, corev1.VolumeMount{
				Name:      GetHadoopVolumeName(hadoopUser),
				MountPath: fmt.Sprintf("/etc/keytab-volume/%s", hadoopUser),
			})
		}
	}
	if len(devMachineEntity.ConfigSpecs) > 0 {
		for _, configSpec := range devMachineEntity.ConfigSpecs {
			_, volumeMount := model.ConvertToVolume(configSpec)
			if volumeMount != nil {
				volumeMounts = append(volumeMounts, *volumeMount)
			}
		}
	}
	return volumeMounts
}
