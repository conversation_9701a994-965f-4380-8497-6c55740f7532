package service

import (
	"context"
	"fmt"
	workspacev1 "git.lianjia.com/cloudnative/kic/kic-platform/apis/aistudio/workspace/v1"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/third_party/uc"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/third_party/wxwork"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/util"
	kratoslog "github.com/go-kratos/kratos/v2/log"
	"strings"
	"time"

	"git.lianjia.com/cloudnative/kic/kic-platform/aistudio/pkg/constant"
	"git.lianjia.com/cloudnative/kic/kic-platform/aistudio/pkg/server/domain/model"
	"git.lianjia.com/cloudnative/kic/kic-platform/aistudio/pkg/server/domain/service/ticket/helper"
	"git.lianjia.com/cloudnative/kic/kic-platform/apis/aistudio/bcode"
	v1 "git.lianjia.com/cloudnative/kic/kic-platform/apis/aistudio/ticket/v1"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/infrastructure/datastore"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/property"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/third_party/bkticket"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"google.golang.org/protobuf/types/known/emptypb"
)

var _ v1.TicketServiceHTTPServer = (*TicketService)(nil)
var _ Interface = (*TicketService)(nil)

type TicketService struct {
	WorkspaceService *WorkspaceService   `inject:"workspaceService"`
	Store            datastore.DataStore `inject:"datastore"`
	RbacService      *RbacService        `inject:"rbacService"`
	QueueService     *QueueService       `inject:"queueService"`
	UserService      *UserService        `inject:""`
	BKClient         bkticket.Client
}

func NewTicketService() *TicketService {
	return &TicketService{}
}

func (t *TicketService) Init(ctx context.Context, properties property.EnvironmentProperty) error {
	t.BKClient = bkticket.Instance()
	return nil
}

func (t *TicketService) CreateTicket(ctx context.Context, req *v1.CreateTicketRequest) (*v1.CreateTicketResponse, error) {
	var (
		err                     error
		ticketEntity            *model.TicketEntity
		joinQueueTemplateEntity *model.JoinQueueTemplate
		fields                  []bkticket.CreateTicketField // 创建蓝鲸工单请求参数
	)

	// 获取当前用户
	account, ok := ctx.Value(constant.UserCtxKey).(string)

	if !ok {
		return nil, bcode.ErrorServerInternalError("get user from context failed")
	}

	if req.Template != nil {
		if tepTemplate, ok := req.Template.(*v1.CreateTicketRequest_JoinQueueTemplate); ok {
			// 检查用户是否已经在队列中等
			if tepTemplate.JoinQueueTemplate.Role == constant.Admin {
				if util.Contains(tepTemplate.JoinQueueTemplate.Managers, account) {
					return &v1.CreateTicketResponse{
						IsQueueMember: true,
					}, nil
				}
			}
			if tepTemplate.JoinQueueTemplate.Role == constant.Member {
				if util.Contains(tepTemplate.JoinQueueTemplate.Members, account) {
					return &v1.CreateTicketResponse{
						IsQueueMember: true,
					}, nil
				}
			}

		}
	}

	// 根据请求类型添加特定字段
	if req.Template != nil {
		switch template := req.Template.(type) {
		case *v1.CreateTicketRequest_ApplyResourceTemplate:
			// 获取工作空间管理员列表
			workspace := &model.WorkspaceBaseEntity{
				Name: template.ApplyResourceTemplate.WorkspaceName,
			}
			err = t.Store.Get(ctx, workspace)
			if err != nil {
				return nil, bcode.ErrorServerInternalError("get workspace error: %v", err)
			}
			// 检查工作空间管理员权限
			isWorkspaceManager, err := t.WorkspaceService.IsWorkspaceManager(ctx, &workspacev1.IsWorkspaceManagerRequest{
				Account:       account,
				WorkspaceName: template.ApplyResourceTemplate.WorkspaceName,
			})
			if err != nil {
				return nil, bcode.ErrorServerInternalError("check workspace manager error: %v", err)
			}
			applyResourceTemplate := template.ApplyResourceTemplate
			ticketEntity = helper.ConvertApplyResourceTemplateToTicketEntity(applyResourceTemplate)
			ticketEntity.Creator = account
			applyResourceTemplateEntity := ticketEntity.TicketTemplate.ApplyResourceTemplate
			applyResourceTemplateEntity.IsAdmin = isWorkspaceManager.IsManager
			applyResourceTemplateEntity.WorkspaceAdmin = t.getNormalUsers(ctx, workspace.Managers)
			applyResourceFields := helper.ConvertTicketEntityToFields(ticketEntity)
			fields = append(fields, applyResourceFields...)

		case *v1.CreateTicketRequest_JoinWorkspaceTemplate:
			// 获取工作空间管理员列表
			workspace := &model.WorkspaceBaseEntity{
				Name: template.JoinWorkspaceTemplate.Name,
			}
			err = t.Store.Get(ctx, workspace)
			if err != nil {
				return nil, bcode.ErrorServerInternalError("get workspace error: %v", err)
			}
			joinWorkspaceTemplate := template.JoinWorkspaceTemplate
			ticketEntity = helper.ConvertJoinWorkspaceTemplateToTicketEntity(joinWorkspaceTemplate)
			ticketEntity.Creator = account
			joinWorkspaceTemplateEntity := ticketEntity.TicketTemplate.JoinWorkspaceTemplate
			joinWorkspaceTemplateEntity.Managers = t.getNormalUsers(ctx, workspace.Managers)
			userInfo, err := uc.Instance().GetUser(ctx, account)
			if err != nil {
				return nil, bcode.ErrorServerInternalError("get user info error: %v", err)
			}
			orgInfo, err := uc.Instance().GetOrg(ctx, userInfo.OrgCode)
			if err != nil {
				return nil, bcode.ErrorServerInternalError("get user info error: %v", err)
			}
			joinWorkspaceTemplateEntity.Center = orgInfo.TreeLevel4Name
			joinWorkspaceTemplateEntity.Department = userInfo.OrgName
			joinWorkspaceFields := helper.ConvertTicketEntityToFields(ticketEntity)
			fields = append(fields, joinWorkspaceFields...)

		case *v1.CreateTicketRequest_JoinQueueTemplate:
			joinQueueTemplate := template.JoinQueueTemplate
			ticketEntity = helper.ConvertJoinQueueTemplateToTicketEntity(joinQueueTemplate)
			ticketEntity.Creator = account
			joinQueueTemplateEntity = ticketEntity.TicketTemplate.JoinQueueTemplate
			if joinQueueTemplate.Role == constant.Admin {
				joinQueueTemplateEntity.Role = constant.QueueManager
				joinQueueTemplateEntity.Approver = t.getNormalUsers(ctx, joinQueueTemplate.Managers)
				joinQueueTemplateEntity.Managers = append(joinQueueTemplate.Managers, account)
				joinQueueTemplateEntity.Members = joinQueueTemplate.Members
			}
			if joinQueueTemplate.Role == constant.Member {
				joinQueueTemplateEntity.Role = constant.QueueMember
				joinQueueTemplateEntity.Approver = t.getNormalUsers(ctx, joinQueueTemplate.Managers)
				joinQueueTemplateEntity.Managers = joinQueueTemplate.Managers
				joinQueueTemplateEntity.Members = append(joinQueueTemplate.Members, account)
			}
			joinQueueFields := helper.ConvertTicketEntityToFields(ticketEntity)
			fields = append(fields, joinQueueFields...)
		}
	}

	userEmail := helper.ToEmail(account)
	serviceID, err := t.BKClient.GetServiceID(ctx, ticketEntity.Type)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("get service id faild, err is %v", err)
	}
	response, err := t.BKClient.CreateTicket(ctx, &bkticket.CreateTicketRequest{
		ServiceID: serviceID,
		Creator:   userEmail,
		Fields:    fields,
	})
	if err != nil {
		return nil, bcode.ErrorServerInternalError("BK Server is abnormal")
	}

	// 使用蓝鲸返回的工单号
	ticketEntity.Sn = response.Data.Sn
	ticketEntity.ID = primitive.NewObjectID()

	// 保存到数据库
	err = t.Store.Add(ctx, ticketEntity)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("add ticket failed, err: %v", err)
	}

	return &v1.CreateTicketResponse{
		Id:            ticketEntity.ID.Hex(),
		IsQueueMember: false,
	}, nil
}

// OperateTicket 操作工单
func (t *TicketService) RevokeTicket(ctx context.Context, req *v1.OperateTicketRequest) (*emptypb.Empty, error) {
	account, ok := ctx.Value(constant.UserCtxKey).(string)
	if !ok {
		return nil, bcode.ErrorServerInternalError("get user from context failed")
	}

	objectID, err := primitive.ObjectIDFromHex(req.Id)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("invalid ticket id")
	}

	ticketEntity := &model.TicketEntity{ID: objectID}
	err = t.Store.Get(ctx, ticketEntity)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("can't revoke, get ticket failed")
	}

	_, err = bkticket.Instance().OperateTicket(ctx, &bkticket.OperateTicketRequest{
		Sn:            ticketEntity.Sn,
		Operator:      account,
		ActionType:    req.Type,
		ActionMessage: constant.Revoke,
	})
	if err != nil {
		return nil, bcode.ErrorServerInternalError("BK Server is abnormal")
	}
	//变更状态
	ticketEntity.Status = constant.REVOKED
	ticketEntity.Processors = []string{}
	ticketEntity.CurrentStep = ""
	err = t.Store.Put(ctx, ticketEntity)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("update [%s]tickets status failed", ticketEntity.Sn)
	}

	return &emptypb.Empty{}, nil
}

func (t *TicketService) ApproveTicket(ctx context.Context, req *v1.OperateTicketRequest) (*emptypb.Empty, error) {
	return &emptypb.Empty{}, t.OpertateTicket(ctx, req.Id, "approval")
}

func (t *TicketService) RejectTicket(ctx context.Context, req *v1.OperateTicketRequest) (*emptypb.Empty, error) {
	return &emptypb.Empty{}, t.OpertateTicket(ctx, req.Id, "rejection")
}

func (t *TicketService) OpertateTicket(ctx context.Context, id string, operateType string) error {
	account, ok := ctx.Value(constant.UserCtxKey).(string)
	if !ok {
		return bcode.ErrorServerInternalError("get user from context failed")
	}
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return err
	}

	ticketEntity := &model.TicketEntity{ID: objectID}
	err = t.Store.Get(ctx, ticketEntity)
	if err != nil {
		return err
	}

	operateReq := &bkticket.OperateTicketNodeRequest{
		Sn:         ticketEntity.Sn,
		Operator:   account,
		ActionType: constant.Transition,
	}
	statusReq := &bkticket.GetTicketStatusRequest{
		Sn: ticketEntity.Sn,
	}

	statusResp, err := bkticket.Instance().GetTicketStatus(ctx, statusReq)
	if err != nil {
		return err
	}
	if len(statusResp.Data.CurrentSteps) == 0 {
		return bcode.ErrorServerInternalError("can't operate the ticket")
	}
	if len(statusResp.Data.CurrentSteps[0].Fields) == 0 {
		return bcode.ErrorServerInternalError("the ticket step isn't Approval steps")
	}
	var status, step string
	operateReq.StateID = statusResp.Data.CurrentSteps[0].Fields[0].StateID
	for i, field := range statusResp.Data.CurrentSteps[0].Fields {
		if i == 0 {
			if operateType == constant.Approval {
				operateReq.Fields = append(operateReq.Fields, bkticket.OperateTicketNodeField{Key: field.Key, Value: "true"})
				status = constant.RUNNING
				step = constant.Processing
			} else {
				operateReq.Fields = append(operateReq.Fields, bkticket.OperateTicketNodeField{Key: field.Key, Value: "false"})
				status = constant.TERMINATED
				step = ""
			}
			continue
		}
		if operateType == constant.Approval {
			operateReq.Fields = append(operateReq.Fields, bkticket.OperateTicketNodeField{Key: field.Key, Value: "approve"})
		} else {
			operateReq.Fields = append(operateReq.Fields, bkticket.OperateTicketNodeField{Key: field.Key, Value: "refuse"})
		}

	}
	_, err = bkticket.Instance().OperateTicketNode(ctx, operateReq)
	if err != nil {
		return err
	}
	//审批完成，先变更状态
	ticketEntity.Status = status
	ticketEntity.Processors = []string{}
	ticketEntity.CurrentStep = step
	ticketEntity.ProcessorsHistory = append(ticketEntity.ProcessorsHistory, account)
	err = t.Store.Put(ctx, ticketEntity)
	if err != nil {
		return bcode.ErrorServerInternalError("update [%s]tickets status failed", ticketEntity.Sn)
	}

	return nil
}

// ListTickets 获取工单列表
func (t *TicketService) ListTickets(ctx context.Context, req *v1.ListTicketsOptions) (*v1.ListTicketsResult, error) {
	var filterOptions datastore.FilterOptions
	// 获取当前用户
	account, ok := ctx.Value(constant.UserCtxKey).(string)
	if !ok {
		return nil, bcode.ErrorServerInternalError("get user from context failed")
	}
	// 判断工单管理展示类型
	if req.DisplayType == "my_submit" {
		filterOptions.In = append(filterOptions.In, datastore.InQueryOption{
			Key:    "creator",
			Values: []string{account},
		})
	}
	if req.DisplayType == "my_approval" {
		processor := helper.ToEmail(account)
		filterOptions.In = append(filterOptions.In, datastore.InQueryOption{
			Key:    "processors",
			Values: []string{processor},
		})
	}
	if req.DisplayType == "my_history" {
		filterOptions.In = append(filterOptions.In, datastore.InQueryOption{
			Key:    "processorsHistory",
			Values: []string{account},
		})
	}
	if req.DisplayType == "space_ticket" {
		// 空间管理员视角
		if req.WorkspaceName == "" {
			return nil, bcode.ErrorInvalidArgument("workspace name is required")
		}
		filterOptions.In = append(filterOptions.In, datastore.InQueryOption{
			Key:    "workspaceName",
			Values: []string{req.WorkspaceName},
		})
	}
	if req.DisplayType == "all" {
		// AIStudio管理员视角
		isPlatformAdmin, err := t.RbacService.IsPlatformAdmin(ctx, account)
		if err != nil {
			return nil, bcode.ErrorServerInternalError("check platform admin error: %v", err)
		}
		if !isPlatformAdmin {
			return nil, bcode.ErrorPermissionNotAllowed("user[%s] has no permission to list all tickets", account)
		}
	}
	// 按工单类型过滤
	if req.Type != "" {
		filterOptions.In = append(filterOptions.In, datastore.InQueryOption{
			Key:    "type",
			Values: []string{req.Type},
		})
	}
	// 按工单编号过滤
	if req.Sn != "" {
		filterOptions.In = append(filterOptions.In, datastore.InQueryOption{
			Key:    "sn",
			Values: []string{req.Sn},
		})
	}
	//按工单状态过滤
	if req.Status != "" {
		filterOptions.In = append(filterOptions.In, datastore.InQueryOption{
			Key:    "status",
			Values: []string{req.Status},
		})
	}
	//支持空间过滤
	if req.WorkspaceName != "" {
		filterOptions.In = append(filterOptions.In, datastore.InQueryOption{
			Key:    "workspaceName",
			Values: []string{req.WorkspaceName},
		})
	}

	var totalCount int64
	tickets, err := t.Store.List(ctx, &model.TicketEntity{}, &datastore.ListOptions{
		Page:          int(req.Page),
		PageSize:      int(req.PageSize),
		FilterOptions: filterOptions,
		SortBy: []datastore.SortOption{
			{
				Key:   "createTime",
				Order: datastore.SortOrderDescending,
			},
		},
	})
	if err != nil {
		return nil, bcode.ErrorServerInternalError("list [%s]tickets failed", req.Sn, err)
	}

	totalCount, err = t.Store.Count(ctx, &model.TicketEntity{}, &filterOptions)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("count [%s]tickets failed", req.Sn, err)
	}

	return &v1.ListTicketsResult{
		Total:   totalCount,
		Tickets: t.processTickets(tickets),
	}, nil
}

// ModelToTicketItemList 将工单实体列表转换为API响应模型列表
func (t *TicketService) processTickets(ticketEntities []datastore.Entity) []*v1.Ticket {
	var tickets []*v1.Ticket
	for _, ticket := range ticketEntities {
		tickets = append(tickets, helper.ConvertTicketEntityToTicket(ticket.(*model.TicketEntity)))
	}
	return tickets
}

func (t *TicketService) SyncTicket(ctx context.Context, sn string) (*emptypb.Empty, error) {
	// 从蓝鲸获取工单的最新状态
	resp, err := bkticket.Instance().GetTickets(ctx, &bkticket.GetTicketsRequest{
		Sn:       sn,
		Page:     1,
		PageSize: 1,
		Username: constant.AdminAccount,
	})
	if err != nil {
		return nil, bcode.ErrorServerInternalError("BK Server is abnormal")
	}
	// 检查返回数据是否为空
	if len(resp.Data.Items) == 0 {
		return nil, bcode.ErrorServerInternalError("can't find the information of the ticket")
	}

	tk, err := t.Store.List(ctx, &model.TicketEntity{
		Sn: sn,
	}, &datastore.ListOptions{
		Page:     1,
		PageSize: 1,
	})
	if err != nil {
		return nil, bcode.ErrorServerInternalError("list tickets failed, err is %v", err)
	}

	ticketEntity := tk[0].(*model.TicketEntity)
	// 更新工单状态
	if ticketEntity.Status == constant.FINISHED || ticketEntity.Status == constant.TERMINATED {
		// 如果检测到工单状态结束，则直接返回
		return &emptypb.Empty{}, nil
	}
	ticketEntity.Status = resp.Data.Items[0].CurrentStatus
	kratoslog.Infof("ticketEntity.Status: %s", resp.Data.Items[0].CurrentProcessors)
	// 更新工单处理人
	tmpProcessor := strings.Trim(resp.Data.Items[0].CurrentProcessors, ",")
	kratoslog.Infof("tmpProcessor: %s", tmpProcessor)

	// 分割处理人字符串并过滤空元素
	processors := strings.Split(tmpProcessor, ",")
	var filteredProcessors []string
	for _, p := range processors {
		p = strings.TrimSpace(p)
		if p != "" {
			filteredProcessors = append(filteredProcessors, p)
		}
	}
	ticketEntity.Processors = filteredProcessors
	kratoslog.Infof("ticketEntity.Processors: %v", ticketEntity.Processors)
	// 检查当前步骤是否存在
	if len(resp.Data.Items[0].CurrentSteps) > 0 {
		ticketEntity.CurrentStep = resp.Data.Items[0].CurrentSteps[0].Name
	}

	err = t.Store.Put(ctx, ticketEntity)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("put tickets failed, err is %v", err)
	}

	return &emptypb.Empty{}, nil
}

func (t *TicketService) FinishTicket(ctx context.Context, sn string) (*emptypb.Empty, error) {
	tk, err := t.Store.List(ctx, &model.TicketEntity{
		Sn: sn,
	}, &datastore.ListOptions{
		Page:     1,
		PageSize: 1,
	})
	if err != nil {
		return nil, bcode.ErrorServerInternalError("list tickets failed, err is %v", err)
	}
	ticketEntity := tk[0].(*model.TicketEntity)
	//将工单状态变为结束
	ticketEntity.Status = constant.FINISHED
	ticketEntity.Processors = []string{}
	ticketEntity.CurrentStep = ""
	err = t.Store.Put(ctx, ticketEntity)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("put tickets failed, err is %v", err)
	}
	return &emptypb.Empty{}, nil

}

func (t *TicketService) SendMessage(ctx context.Context, url, sn, status, processors string) (*emptypb.Empty, error) {
	tk, err := t.Store.List(ctx, &model.TicketEntity{
		Sn: sn,
	}, &datastore.ListOptions{
		Page:     1,
		PageSize: 1,
	})
	if err != nil {
		return nil, bcode.ErrorServerInternalError("list tickets failed, err is %v", err)
	}
	ticketEntity := tk[0].(*model.TicketEntity)
	userInfo, err := uc.Instance().GetUser(ctx, ticketEntity.Creator)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("getUserNumberFromUC failed, err is %v", err)
	}
	kratoslog.Infof("Creator :%s,userInfo: %v", ticketEntity.Creator, userInfo)
	var description, title, userNumbers string
	userNumbers = userInfo.Code
	kratoslog.Infof("userNumbers: %s", userNumbers)
	//获取消息模版

	displayType := ticketEntity.Type
	if displayType == constant.JoinQueueType {
		// 使用更安全的方式获取字段值
		displayType = displayType + "(" + ticketEntity.TicketTemplate.JoinQueueTemplate.Name + ")"
	}
	if status == constant.Submit {
		title = fmt.Sprintf(constant.SubmitMessageTitle, ticketEntity.Type, ticketEntity.WorkspaceName, displayType)
		description = fmt.Sprintf(constant.SubmitMessageDescription, userInfo.Name, userInfo.Account, util.TimeFormat(ticketEntity.CreateTime), ticketEntity.Type)
	}
	if status == constant.Finish {
		title = fmt.Sprintf(constant.FinishMessageTitle, ticketEntity.Type, ticketEntity.WorkspaceName, displayType)
		description = fmt.Sprintf(constant.FinishMessageDescription, userInfo.Name, userInfo.Account, ticketEntity.Type, util.TimeFormat(ticketEntity.CreateTime))
		displayType := ticketEntity.Type
		if displayType == constant.JoinWorkspaceType {
			description = description + constant.ReloginMessage
		}

	}
	if status == constant.Withdraw {
		title = fmt.Sprintf(constant.WithdrawMessageTitle, ticketEntity.Type, ticketEntity.WorkspaceName, displayType)
		description = fmt.Sprintf(constant.WithdrawMessageDescription, userInfo.Name, userInfo.Account, ticketEntity.Type, time.Now().Format("2006-01-02 15:04:05"))
	}
	if status == constant.Terminated {
		title = fmt.Sprintf(constant.TerminateMessageTitle, ticketEntity.Type, ticketEntity.WorkspaceName, displayType)
		description = fmt.Sprintf(constant.TerminateMessageDescription, userInfo.Name, userInfo.Account, ticketEntity.Type)
	}

	if status == constant.Process {
		title = fmt.Sprintf(constant.ProcessMessageTitle, ticketEntity.Type, ticketEntity.WorkspaceName, displayType)
		description = fmt.Sprintf(constant.ProcessMessageDescription, userInfo.Name, userInfo.Account, util.TimeFormat(ticketEntity.CreateTime), ticketEntity.Type)
		if processors == "" {
			return &emptypb.Empty{}, bcode.ErrorServerInternalError("processors is empty")
		}
		var tmpUserNumbers []string
		tmpProcessors := strings.Split(processors, ",")
		for _, processor := range tmpProcessors {
			processor = strings.Trim(processor, "@ke.com")
			userInfo, err = uc.Instance().GetUser(ctx, processor)
			if err != nil {
				continue
			}
			tmpUserNumbers = append(tmpUserNumbers, userInfo.Code)
		}
		userNumbers = strings.Join(tmpUserNumbers, "|")
	}

	req := wxwork.MessageReq{
		UserNumbers: userNumbers,
		Title:       title,
		Description: description,
		TicketUrl:   url,
	}
	kratoslog.Infof("userNumbers: %s", userNumbers)
	err = wxwork.Instance().SendCard(req)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("wxwork.Instance().SendMarkdown failed, err is %v", err)
	}
	return &emptypb.Empty{}, nil
}

func (t *TicketService) GetTicketDetail(ctx context.Context, req *v1.GetTicketDetailRequest) (*v1.DisplayTicket, error) {
	objectID, err := primitive.ObjectIDFromHex(req.Id)
	if err != nil {
		return nil, err
	}

	ticketEntity := &model.TicketEntity{ID: objectID}
	err = t.Store.Get(ctx, ticketEntity)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("get ticket entity failed, err is %v", err)
	}

	// 创建返回结果
	displayTicket := &v1.DisplayTicket{
		Creator:     ticketEntity.Creator,
		CreateTime:  util.TimeFormat(ticketEntity.CreateTime),
		TicketType:  v1.TicketType(0), // 默认值
		DisplayType: ticketEntity.Type,
	}

	// 根据工单类型填充相应的显示结构
	switch ticketEntity.Type {
	case constant.JoinQueueType:
		displayTicket.TicketType = v1.TicketType_JoinQueue
		displayTicket.JoinQueueDisplay = helper.ConvertToJoinQueueDisplay(ticketEntity)
	case constant.JoinWorkspaceType:
		displayTicket.TicketType = v1.TicketType_JoinWorkspace
		displayTicket.JoinWorkspaceDisplay = helper.ConvertToJoinWorkspaceDisplay(ticketEntity)
	case constant.ApplyResourceType:
		displayTicket.TicketType = v1.TicketType_ApplyResource
		displayTicket.ApplyResourceDisplay = helper.ConvertToApplyResourceDisplay(ticketEntity)
	}

	return displayTicket, nil
}

func (t *TicketService) getNormalUsers(ctx context.Context, users []string) []string {
	var normalUsers []string
	for _, user := range users {
		if t.UserService.IsRobotUser(user) {
			continue
		}
		normalUsers = append(normalUsers, user)
	}
	return normalUsers
}
