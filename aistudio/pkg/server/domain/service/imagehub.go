package service

import (
	"archive/tar"
	"compress/gzip"
	"context"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	tcrModels "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/tcr/v20190924"

	"git.lianjia.com/cloudnative/kic/kic-platform/aistudio/pkg/constant"
	"git.lianjia.com/cloudnative/kic/kic-platform/aistudio/pkg/server/domain/model"
	"git.lianjia.com/cloudnative/kic/kic-platform/aistudio/pkg/server/domain/repository"
	"git.lianjia.com/cloudnative/kic/kic-platform/apis/aistudio/bcode"
	imagehubv1 "git.lianjia.com/cloudnative/kic/kic-platform/apis/aistudio/imagehub/v1"
	"git.lianjia.com/cloudnative/kic/kic-platform/apis/common"
	automlkcsiov1alpha1 "git.lianjia.com/cloudnative/kic/kic-platform/pkg/apis/automl.kcs.io/v1alpha1"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/apis/scheduling.kcs.io/v1alpha1"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/infrastructure/datastore"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/multicluster"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/property"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/third_party/s3"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/third_party/tcr"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/util"
	kruiseapiv1alpha1 "git.lianjia.com/cloudnative/kic/kruise-api/apps/v1alpha1"
	kratoslog "github.com/go-kratos/kratos/v2/log"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"google.golang.org/protobuf/types/known/emptypb"
	batchv1 "k8s.io/api/batch/v1"
	corev1 "k8s.io/api/core/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/intstr"
	"k8s.io/klog/v2"
	"sigs.k8s.io/controller-runtime/pkg/client"
)

var _ Interface = (*ImageHubService)(nil)
var _ imagehubv1.ImageHubServiceHTTPServer = (*ImageHubService)(nil)

// ImageHubService 是镜像中心服务的结构体，包含日志、数据存储、环境属性等依赖注入
type ImageHubService struct {
	Logger           *kratoslog.Helper            `inject:"logger"`         // 日志记录器
	Store            datastore.DataStore          `inject:"datastore"`      // 数据存储接口
	Properties       property.EnvironmentProperty `inject:""`               // 环境属性配置
	ClusterManager   *ClusterManager              `inject:"clusterManager"` // 集群管理器
	WorkspaceService *WorkspaceService            `inject:""`               // 工作空间服务
}

// 定义常用字符串常量
const (
	AiStudioPrefix  = "aistudio-"  // AI Studio 前缀
	ReadonlySuffix  = "-readonly"  // 只读后缀
	ReadwriteSuffix = "-readwrite" // 读写后缀
	TCRPrefix       = "tcr$"       // TCR 前缀
)

// 定义区域常量
const (
	Region_bj = "region_bj" // 北京区域
	Region_sh = "region_sh" // 上海区域
)

// 定义超时相关常量
const (
	ActiveDeadlineSeconds   = 1200 // 任务最大执行时间（秒）
	TTLSecondsAfterFinished = 600  // 任务完成后保留时间（秒）
)

// 镜像构建常量
const (
	BuildTypeImage      = "image"      // 镜像构建类型
	BuildTypeDockerfile = "dockerfile" // Dockerfile构建类型
	BuildTypeDev        = "dev"        // 开发环境构建类型

	BaseImagePreset = "preset" // 预设基础镜像
	BaseImageCustom = "custom" // 自定义基础镜像
	BaseImageURL    = "url"    // URL指定基础镜像

	DockerfileSourceContent = "content" // 内容直接提供Dockerfile
	DockerfileSourceGit     = "gitlab"  // 从GitLab获取Dockerfile

	KanikoImage  = "harbor.intra.ke.com/kcs/kic/build-image/kaniko:debug_v1.23.2" // Kaniko构建工具镜像
	NerdCtlImage = "harbor.intra.ke.com/kcs/kic/build-image/nerdctl:v1.7.7-build" // Nerdctl工具镜像
	DockerImage  = "harbor.intra.ke.com/kcs/kic/build-image/docker:v27.3.1-build" // Docker工具镜像

	HarborRegcred = "harbor-regcred" // Harbor仓库凭证名称

	BuildStatusWaiting   = "Waiting"   // 等待中
	BuildStatusPending   = "Pending"   // 挂起中
	BuildStatusRunning   = "Running"   // 运行中
	BuildStatusSucceeded = "Succeeded" // 成功
	BuildStatusFailed    = "Failed"    // 失败
	BuildStatusUnknown   = "Unknown"   // 未知状态
	BuildStatusAborting  = "Aborting"  // 中止中
	BuildStatusAborted   = "Aborted"   // 已中止

	BuildImageActiveDeadlineSeconds   = 86400         // 构建任务最大执行时间（秒）
	BuildImageTTLSecondsAfterFinished = 3600          // 构建任务完成后保留时间（秒）
	BuildImageBackoffLimit            = 0             // 构建任务重试次数
	PrefixJobNameBuildImage           = "build-image" // 构建任务名称前缀
	PushRetry                         = "3"           // 镜像推送重试次数

	GitLabUser = "aistudio"         // GitLab用户名
	GitLabPwd  = "rJYcVDsZZ^u@wENj" // GitLab密码

	LocalCluster = "local" // 本地集群标识

	MaxImage = 50 // 单个镜像最多保留的有效版本数

	InternalIP      = "**********"                                          // 内部IP地址
	S3InternalHost  = "cos.ap-beijing.myqcloud.com"                         // S3内部域名
	TCRInternalHost = "tcr-m446wuql-1254236265.cos.ap-beijing.myqcloud.com" // TCR内部域名

	BuildImageResourceLimitCPU    = "32000m" // 构建任务CPU限制
	BuildImageResourceLimitMemory = "128Gi"  // 构建任务内存限制

	BuildImageResourceRequestCPU    = "1000m" // 构建任务CPU请求
	BuildImageResourceRequestMemory = "2Gi"   // 构建任务内存请求

	TCRCacheNamespace = "aistudio-kaniko-cache" // 存储镜像构建时放置缓存的命名空间
)

// NewImageHubService 创建并返回一个新的ImageHubService实例
func NewImageHubService() *ImageHubService {
	return &ImageHubService{}
}

func (i *ImageHubService) OnLeader(ctx context.Context, properties property.EnvironmentProperty) error {
	// 启动一个goroutine来定时同步镜像
	go i.TimedSynchronizeImages(ctx, properties)
	return nil
}

func (i *ImageHubService) Init(ctx context.Context, properties property.EnvironmentProperty) error {
	return nil
}

// CheckImageHubStatus 检查 ImageHub 状态, 是否已经开通
func (i *ImageHubService) CheckImageHubStatus(ctx context.Context, request *imagehubv1.CheckImageHubStatusRequest) (*imagehubv1.CheckImageHubStatusResponse, error) {
	// 1. 检查申请区域是否支持
	regions := i.Properties.GetStringSlice("tcr.regions")
	if !util.Contains(regions, request.Region) {
		return &imagehubv1.CheckImageHubStatusResponse{
			Activated: false,
			Supported: false,
		}, nil
	}
	// 2. 检查数据库是否存在记录, 不存在则检查 tcr 命名空间是否存在, tcr service account 是否存在, 触发器是否存在
	imageHubStatus, err := repository.GetImageHubStatus(ctx, i.Store, request.WorkspaceName, request.Region)
	if err != nil {
		if errors.Is(err, datastore.ErrRecordNotExist) {
			// 检查 tcr 命名空间是否存在
			tcrNamespaceExist, err := i.CheckTCRNamespaceExist(ctx, request.WorkspaceName, request.Region)
			if err != nil {
				return nil, err
			}
			if !tcrNamespaceExist {
				return &imagehubv1.CheckImageHubStatusResponse{
					Activated: false,
					Supported: true,
				}, nil
			}
			// 检查 tcr service account 是否存在
			tcrServiceAccountExist, err := i.CheckTCRServiceAccountExist(ctx, request.WorkspaceName, request.Region)
			if err != nil {
				return nil, err
			}
			if !tcrServiceAccountExist {
				return &imagehubv1.CheckImageHubStatusResponse{
					Activated: false,
					Supported: true,
				}, nil
			}
			// 检查触发器是否存在
			triggerExist, err := i.CheckTCRTriggerExist(ctx, request.WorkspaceName, request.Region)
			if err != nil {
				return nil, err
			}
			if !triggerExist {
				return &imagehubv1.CheckImageHubStatusResponse{
					Activated: false,
					Supported: true,
				}, nil
			}
			// 如果全部存在，更新数据库
			imageHubStatus = &model.ImageHubStatusEntity{
				ID:            primitive.NewObjectID(),
				WorkspaceName: request.WorkspaceName,
				Region:        request.Region,
				Activated:     true,
				Supported:     true,
			}
			err = i.Store.Add(ctx, imageHubStatus)
			if err != nil {
				return nil, bcode.ErrorServerInternalError("add image hub status failed, err: %v", err)
			}
		}
		return nil, err
	}
	// 如果数据存在并且状态为 true，Activated直接返回 true
	if imageHubStatus.Activated {
		return &imagehubv1.CheckImageHubStatusResponse{
			Activated: true,
			Supported: true,
		}, nil
	}
	// 如果数据存在并且状态为 false，检查 tcr 命名空间是否存在, tcr service account 是否存在, 触发器是否存在
	// 2. 检查 tcr 命名空间是否存在
	tcrNamespaceExist, err := i.CheckTCRNamespaceExist(ctx, request.WorkspaceName, request.Region)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("check workspace/region [%s:%s]tcr namespace exist failed, err: %v", request.WorkspaceName, request.Region, err)
	}
	if !tcrNamespaceExist {
		kratoslog.Infof("tcr namespace not exist, workspaceName: %s, region: %s", request.WorkspaceName, request.Region)
		return &imagehubv1.CheckImageHubStatusResponse{
			Activated: false,
			Supported: true,
		}, nil
	}
	// 3. 检查 tcr 服务级账号是否存在
	tcrServiceAccountExist, err := i.CheckTCRServiceAccountExist(ctx, request.WorkspaceName, request.Region)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("check workspace/region [%s:%s]tcr service account exist failed, err: %v", request.WorkspaceName, request.Region, err)
	}
	if !tcrServiceAccountExist {
		kratoslog.Infof("tcr service account not exist, workspaceName: %s, region: %s", request.WorkspaceName, request.Region)
		return &imagehubv1.CheckImageHubStatusResponse{
			Activated: false,
			Supported: true,
		}, nil
	}
	// 4. 检查 tcr 触发器是否存在
	tcrTriggerExist, err := i.CheckTCRTriggerExist(ctx, request.WorkspaceName, request.Region)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("check workspace/region [%s:%s]tcr trigger exist failed, err: %v", request.WorkspaceName, request.Region, err)
	}
	if !tcrTriggerExist {
		kratoslog.Infof("tcr trigger not exist, workspaceName: %s, region: %s", request.WorkspaceName, request.Region)
		return &imagehubv1.CheckImageHubStatusResponse{
			Activated: false,
			Supported: true,
		}, nil
	}
	// 如果全部存在，更新数据库
	imageHubStatus.Activated = true
	err = i.Store.Put(ctx, imageHubStatus)
	if err != nil {
		return nil, err
	}
	return &imagehubv1.CheckImageHubStatusResponse{
		Activated: true,
		Supported: true,
	}, nil

}

// CheckTCRNamespaceExist 检查 TCR 命名空间是否存在
func (i *ImageHubService) CheckTCRNamespaceExist(ctx context.Context, workspaceName, region string) (bool, error) {
	tcrClient := tcr.Instance()[region]
	namespaceName := getNamespaceName(workspaceName)
	namespace, err := tcrClient.GetNamespace(ctx, namespaceName)
	if err != nil {
		return false, err
	}
	if len(namespace.Response.NamespaceList) == 0 {
		return false, nil
	} else {
		return true, nil
	}
}

// CheckTCRServiceAccountExist 检查 TCR 服务级账号是否存在
func (i *ImageHubService) CheckTCRServiceAccountExist(ctx context.Context, workspaceName, region string) (bool, error) {
	namespaceName := getNamespaceName(workspaceName)

	readOnlyServiceAccountName := getReadOnlyServiceAccountName(workspaceName)

	TCREntity := &model.TcrServiceAccountEntity{
		Name:          getTCRName(readOnlyServiceAccountName),
		NamespaceName: namespaceName,
		Region:        region,
	}

	err := i.Store.Get(ctx, TCREntity)
	if err != nil {
		if errors.Is(err, datastore.ErrRecordNotExist) {
			return false, nil
		}
		return false, err
	}

	readWriteServiceAccountName := getReadWriteServiceAccountName(workspaceName)

	TCREntity = &model.TcrServiceAccountEntity{
		Name:          getTCRName(readWriteServiceAccountName),
		NamespaceName: namespaceName,
		Region:        region,
	}

	err = i.Store.Get(ctx, TCREntity)
	if err != nil {
		if errors.Is(err, datastore.ErrRecordNotExist) {
			return false, nil
		}
		return false, err
	}

	return true, nil
}

// CheckTCRTriggerExist 检查 TCR 触发器是否存在
func (i *ImageHubService) CheckTCRTriggerExist(ctx context.Context, workspaceName, region string) (bool, error) {
	tcrClient := tcr.Instance()[region]

	namespace := getNamespaceName(workspaceName)
	name := getTriggerName(workspaceName)

	triggers, err := tcrClient.GetTrigger(ctx, namespace)
	if err != nil {
		return false, bcode.ErrorTcrSystemError("get trigger failed, err: %v", err)
	}
	if len(triggers.Response.Triggers) == 0 {
		return false, nil
	} else {
		for _, trigger := range triggers.Response.Triggers {
			triggerName := *trigger.Name
			if triggerName == name {
				return true, nil
			}
		}
	}
	return false, nil
}

// OpenImageHub 开通 ImageHub
func (i *ImageHubService) OpenImageHub(ctx context.Context, request *imagehubv1.OpenImageHubRequest) (*emptypb.Empty, error) {
	regions := i.Properties.GetStringSlice("tcr.regions")
	if !util.Contains(regions, request.Region) {
		return nil, fmt.Errorf("region %s not supported", request.Region)
	}
	tcrClient := tcr.Instance()[request.Region]

	// 1. 检查 tcr 命名空间是否存在，不存在创建
	tcrNamespaceExist, err := i.CheckTCRNamespaceExist(ctx, request.WorkspaceName, request.Region)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("check workspace/region[%s:%s] tcr namespace exist failed, err: %v", request.WorkspaceName, request.Region, err)
	}
	if !tcrNamespaceExist {
		err = i.CreateTCRNamespace(ctx, tcrClient, request.WorkspaceName)
		if err != nil {
			return nil, bcode.ErrorTcrSystemError("create workspace/region[%s:%s] tcr namespace failed, err: %v", request.WorkspaceName, request.Region, err)
		}
	}
	// 2. 检查 tcr 服务级账号是否存在，不存在创建
	tcrServiceAccountExist, err := i.CheckTCRServiceAccountExist(ctx, request.WorkspaceName, request.Region)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("check workspace/region[%s:%s] tcr service account exist failed, err: %v", request.WorkspaceName, request.Region, err)
	}
	if !tcrServiceAccountExist {
		err = i.CreateTCRServiceAccount(ctx, tcrClient, request.WorkspaceName, request.Region)
		if err != nil {
			return nil, bcode.ErrorTcrSystemError("create workspace/region[%s:%s] tcr service account failed, err: %v", request.WorkspaceName, request.Region, err)
		}
	}

	// 3. 检查 tcr 触发器是否存在，不存在创建
	tcrTriggerExist, err := i.CheckTCRTriggerExist(ctx, request.WorkspaceName, request.Region)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("check workspace/region[%s:%s] tcr trigger exist failed, err: %v", request.WorkspaceName, request.Region, err)
	}
	if !tcrTriggerExist {
		err = i.CreateTrigger(ctx, tcrClient, request.WorkspaceName)
		if err != nil {
			return nil, bcode.ErrorTcrSystemError("create workspace/region[%s:%s] tcr trigger failed, err: %v", request.WorkspaceName, request.Region, err)
		}
	}

	imageHubStatus, err := repository.GetImageHubStatus(ctx, i.Store, request.WorkspaceName, request.Region)
	if err != nil {
		if errors.Is(err, datastore.ErrRecordNotExist) {
			imageHubStatus = &model.ImageHubStatusEntity{
				ID:            primitive.NewObjectID(),
				WorkspaceName: request.WorkspaceName,
				Region:        request.Region,
				Activated:     true,
				Supported:     true,
			}
			err = i.Store.Add(ctx, imageHubStatus)
			if err != nil {
				return nil, bcode.ErrorServerInternalError("add image hub status failed, err: %v", err)
			}
		}
		return nil, err
	}

	imageHubStatus.Activated = true
	err = i.Store.Put(ctx, imageHubStatus)
	if err != nil {
		return nil, err
	}
	return &emptypb.Empty{}, nil
}

// CreateTCRServiceAccount 初始化 TCR 服务账号
func (i *ImageHubService) CreateTCRServiceAccount(ctx context.Context, tcrClient *tcr.Client, workspaceName, region string) error {
	err := i.CreateTCRReadOnlyServiceAccount(ctx, tcrClient, workspaceName, region)
	if err != nil {
		return bcode.ErrorServerInternalError("create workspace/region[%s:%s] read only service account failed, err: %v", workspaceName, region, err)
	}

	err = i.CreateTCRReadWriteServiceAccount(ctx, tcrClient, workspaceName, region)
	if err != nil {
		return bcode.ErrorServerInternalError("create workspace/region[%s:%s] read write service account failed, err: %v", workspaceName, region, err)
	}
	return nil
}

// CreateTCRReadOnlyServiceAccount 创建 TCR 只读服务级账号
func (i *ImageHubService) CreateTCRReadOnlyServiceAccount(ctx context.Context, tcrClient *tcr.Client, workspaceName, region string) error {
	namespaceName := getNamespaceName(workspaceName)
	readOnlyServiceAccountName := getReadOnlyServiceAccountName(workspaceName)

	sas, err := tcrClient.GetServiceAccount(ctx, readOnlyServiceAccountName)
	if err != nil {
		return bcode.ErrorServerInternalError("workspace[%s] get read only service account failed, err: %v", workspaceName, err)
	}
	if len(sas.Response.ServiceAccounts) > 0 {
		kratoslog.Infof("workspace[%s] read only service account already exists, update read only service account", workspaceName)
		_, err = tcrClient.DeleteServiceAccount(ctx, readOnlyServiceAccountName)
		if err != nil {
			return err
		}
	}
	description := "read only service account for " + workspaceName

	namespaceNames := []string{namespaceName, TCRCacheNamespace}
	res, err := tcrClient.CreateServiceAccount(ctx, readOnlyServiceAccountName, description, constant.ReadOnlyServiceAccountType, namespaceNames)
	if err != nil {
		return bcode.ErrorServerInternalError("workspace[%s] create read only service account failed, err: %v", workspaceName, err)
	}

	TCREntity := &model.TcrServiceAccountEntity{
		Name: getTCRName(readOnlyServiceAccountName),
	}
	err = i.Store.Get(ctx, TCREntity)
	if err != nil {
		if errors.Is(err, datastore.ErrRecordNotExist) {
			timeNow := time.Now()
			description := "read only service account for " + workspaceName

			TCREntity.Name = *res.Response.Name
			TCREntity.Password = *res.Response.Password
			TCREntity.NamespaceName = namespaceName
			TCREntity.Description = description
			TCREntity.ServiceAccountType = constant.ReadOnlyServiceAccountType
			TCREntity.Region = region
			TCREntity.CreateTime = timeNow
			TCREntity.UpdateTime = timeNow

			err = i.Store.Add(ctx, TCREntity)
			if err != nil {
				return bcode.ErrorServerInternalError("workspace[%s] add read only service account failed, err: %v", workspaceName, err)
			}
		}
	}

	TCREntity.Password = *res.Response.Password
	TCREntity.UpdateTime = time.Now()
	err = i.Store.Put(ctx, TCREntity)
	if err != nil {
		return bcode.ErrorServerInternalError("workspace[%s] update read only service account failed, err: %v", workspaceName, err)
	}

	return nil
}

// CreateTCRReadWriteServiceAccount 创建 TCR 读写服务级账号
func (i *ImageHubService) CreateTCRReadWriteServiceAccount(ctx context.Context, tcrClient *tcr.Client, workspaceName, region string) error {
	namespaceName := getNamespaceName(workspaceName)
	readWriteServiceAccountName := getReadWriteServiceAccountName(workspaceName)

	sas, err := tcrClient.GetServiceAccount(ctx, readWriteServiceAccountName)
	if err != nil {
		return bcode.ErrorServerInternalError("workspace[%s] get read write service account failed, err: %v", workspaceName, err)
	}
	if len(sas.Response.ServiceAccounts) > 0 {
		kratoslog.Infof("workspace[%s] read write service account already exists, update read write service account", workspaceName)
		_, err = tcrClient.DeleteServiceAccount(ctx, readWriteServiceAccountName)
		if err != nil {
			return err
		}
	}
	description := "read write service account for " + workspaceName
	namespaceNames := []string{namespaceName, TCRCacheNamespace}
	res, err := tcrClient.CreateServiceAccount(ctx, readWriteServiceAccountName, description, constant.ReadWriteServiceAccountType, namespaceNames)
	if err != nil {
		return bcode.ErrorServerInternalError("workspace[%s] create read write service account failed, err: %v", workspaceName, err)
	}

	TCREntity := &model.TcrServiceAccountEntity{
		Name: getTCRName(readWriteServiceAccountName),
	}
	err = i.Store.Get(ctx, TCREntity)
	if err != nil {
		if errors.Is(err, datastore.ErrRecordNotExist) {
			timeNow := time.Now()
			description := "read write service account for " + workspaceName

			TCREntity.Name = *res.Response.Name
			TCREntity.Password = *res.Response.Password
			TCREntity.NamespaceName = namespaceName
			TCREntity.Description = description
			TCREntity.ServiceAccountType = constant.ReadWriteServiceAccountType
			TCREntity.Region = region
			TCREntity.CreateTime = timeNow
			TCREntity.UpdateTime = timeNow

			err = i.Store.Add(ctx, TCREntity)
			if err != nil {
				return bcode.ErrorServerInternalError("workspace[%s] add read write service account failed, err: %v", workspaceName, err)
			}
		}
	}

	TCREntity.Password = *res.Response.Password
	TCREntity.UpdateTime = time.Now()
	err = i.Store.Put(ctx, TCREntity)
	if err != nil {
		return bcode.ErrorServerInternalError("workspace[%s] update read write service account failed, err: %v", workspaceName, err)
	}

	return nil
}

// CloseImageHub 关闭镜像中心
func (i *ImageHubService) CloseImageHub(ctx context.Context, request *imagehubv1.CloseImageHubRequest) (*emptypb.Empty, error) {
	tcrClient := tcr.Instance()[request.Region]

	// 1. 检查 tcr 触发器是否存在，存在删除
	tcrTriggerExist, err := i.CheckTCRTriggerExist(ctx, request.WorkspaceName, request.Region)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("check workspace/region[%s:%s] tcr trigger exist failed, err: %v", request.WorkspaceName, request.Region, err)
	}
	if tcrTriggerExist {
		err = i.DeleteTrigger(ctx, tcrClient, request.WorkspaceName)
		if err != nil {
			return nil, bcode.ErrorServerInternalError("delete workspace/region[%s:%s] tcr trigger failed, err: %v", request.WorkspaceName, request.Region, err)
		}
	}
	// 2. 检查 tcr 服务级账号是否存在，存在删除
	tcrServiceAccountExist, err := i.CheckTCRServiceAccountExist(ctx, request.WorkspaceName, request.Region)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("check workspace/region[%s:%s] tcr service account exist failed, err: %v", request.WorkspaceName, request.Region, err)
	}
	if tcrServiceAccountExist {
		err = i.DeleteTCRServiceAccount(ctx, tcrClient, request.WorkspaceName)
		if err != nil {
			return nil, bcode.ErrorServerInternalError("delete workspace/region[%s:%s] tcr service account failed, err: %v", request.WorkspaceName, request.Region, err)
		}
	}

	// 3. 检查 tcr 命名空间是否存在，存在删除
	tcrNamespaceExist, err := i.CheckTCRNamespaceExist(ctx, request.WorkspaceName, request.Region)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("check workspace/region[%s:%s] tcr namespace exist failed, err: %v", request.WorkspaceName, request.Region, err)
	}
	if tcrNamespaceExist {
		err = i.DeleteTCRNamespace(ctx, tcrClient, request.WorkspaceName)
		if err != nil {
			return nil, bcode.ErrorServerInternalError("delete workspace/region[%s:%s] tcr namespace failed, err: %v", request.WorkspaceName, request.Region, err)
		}
	}

	// 4. 更新 image hub 状态
	imageHubStatus, err := repository.GetImageHubStatus(ctx, i.Store, request.WorkspaceName, request.Region)
	if err != nil {
		if errors.Is(err, datastore.ErrRecordNotExist) {
			return nil, nil
		}
		return nil, bcode.ErrorServerInternalError("get workspace/region[%s:%s] image hub status failed, err: %v", request.WorkspaceName, request.Region, err)
	}

	imageHubStatus.Activated = false
	err = i.Store.Put(ctx, imageHubStatus)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("update workspace/region[%s:%s] image hub status failed, err: %v", request.WorkspaceName, request.Region, err)
	}
	return &emptypb.Empty{}, nil
}

// DeleteTCRServiceAccount 删除服务级账号
func (i *ImageHubService) DeleteTCRServiceAccount(ctx context.Context, tcrClient *tcr.Client, workspaceName string) error {
	err := i.DeleteTCRReadOnlyServiceAccount(ctx, tcrClient, workspaceName)
	if err != nil {
		return bcode.ErrorServerInternalError("delete read only service account failed, err: %v", err)
	}

	err = i.DeleteTCRReadWriteServiceAccount(ctx, tcrClient, workspaceName)
	if err != nil {
		return bcode.ErrorServerInternalError("delete read write service account failed, err: %v", err)
	}
	return nil
}

// DeleteTCRReadOnlyServiceAccount 删除只读服务级账号
func (i *ImageHubService) DeleteTCRReadOnlyServiceAccount(ctx context.Context, tcrClient *tcr.Client, workspaceName string) error {
	readOnlyServiceAccountName := getReadOnlyServiceAccountName(workspaceName)

	sas, err := tcrClient.GetServiceAccount(ctx, readOnlyServiceAccountName)
	if err != nil {
		return bcode.ErrorServerInternalError("workspace[%s] get read only service account failed, err: %v", workspaceName, err)
	}
	if len(sas.Response.ServiceAccounts) > 0 {
		_, err = tcrClient.DeleteServiceAccount(ctx, readOnlyServiceAccountName)
		if err != nil {
			return err
		}
	}

	TCREntity := &model.TcrServiceAccountEntity{
		Name: getTCRName(readOnlyServiceAccountName),
	}
	err = i.Store.Get(ctx, TCREntity)
	if err != nil {
		if errors.Is(err, datastore.ErrRecordNotExist) {
			return nil
		}
		return err
	}
	err = i.Store.Delete(ctx, TCREntity)
	if err != nil {
		return err
	}
	return nil
}

// DeleteTCRReadWriteServiceAccount 删除读写服务级账号
func (i *ImageHubService) DeleteTCRReadWriteServiceAccount(ctx context.Context, tcrClient *tcr.Client, workspaceName string) error {
	readWriteServiceAccountName := getReadWriteServiceAccountName(workspaceName)

	sas, err := tcrClient.GetServiceAccount(ctx, readWriteServiceAccountName)
	if err != nil {
		return bcode.ErrorServerInternalError("workspace[%s] get read write service account failed, err: %v", workspaceName, err)
	}
	if len(sas.Response.ServiceAccounts) > 0 {
		_, err = tcrClient.DeleteServiceAccount(ctx, readWriteServiceAccountName)
		if err != nil {
			return err
		}
	}

	TCREntity := &model.TcrServiceAccountEntity{
		Name: getTCRName(readWriteServiceAccountName),
	}
	err = i.Store.Get(ctx, TCREntity)
	if err != nil {
		if errors.Is(err, datastore.ErrRecordNotExist) {
			return nil
		}
		return err
	}
	err = i.Store.Delete(ctx, TCREntity)
	if err != nil {
		return err
	}
	return nil
}

// CreateTCRNamespace 创建 TCR 的命名空间
func (i *ImageHubService) CreateTCRNamespace(ctx context.Context, tcrClient *tcr.Client, workspaceName string) error {
	namespaceName := getNamespaceName(workspaceName)
	namespace, err := tcrClient.GetNamespace(ctx, namespaceName)
	if err != nil {
		return err
	}
	if len(namespace.Response.NamespaceList) == 0 {
		_, err = tcrClient.CreateNamespace(ctx, namespaceName)
		if err != nil {
			return err
		}
	} else {
		kratoslog.Infof("namespace %s already exists", namespaceName)
		return nil
	}

	return nil
}

// DeleteTCRNamespace 删除 TCR 的命名空间
func (i *ImageHubService) DeleteTCRNamespace(ctx context.Context, tcrClient *tcr.Client, workspaceName string) error {
	namespaceName := getNamespaceName(workspaceName)
	namespace, err := tcrClient.GetNamespace(ctx, namespaceName)
	if err != nil {
		return err
	}
	if len(namespace.Response.NamespaceList) == 0 {
		kratoslog.Infof("namespace %s not exists", namespaceName)
		return nil
	}
	_, err = tcrClient.DeleteNamespace(ctx, namespaceName)
	if err != nil {
		return err
	}
	return nil
}

// CreateTrigger 创建TCR触发器
func (i *ImageHubService) CreateTrigger(ctx context.Context, tcrClient *tcr.Client, workspaceName string) error {
	namespace := getNamespaceName(workspaceName)
	name := getTriggerName(workspaceName)
	// 创建触发器
	header := map[string]string{
		"Content-Type": "application/json",
	}

	triggers, err := tcrClient.GetTrigger(ctx, namespace)
	if err != nil {
		return bcode.ErrorTcrSystemError("get trigger failed, err: %v", err)
	}
	if len(triggers.Response.Triggers) > 0 {
		for _, trigger := range triggers.Response.Triggers {
			if *trigger.Name == name {
				kratoslog.Infof("trigger already exists, trigger name: %s", name)
				continue
			}
		}
	}
	_, err = tcrClient.CreateTrigger(ctx, name, namespace, header)
	if err != nil {
		return bcode.ErrorTcrSystemError("create trigger failed, err: %v", err)
	}
	return nil
}

// DeleteTrigger 所有地区删除TCR触发器
func (i *ImageHubService) DeleteTrigger(ctx context.Context, tcrClient *tcr.Client, workspaceName string) error {
	triggerName := getTriggerName(workspaceName)
	namespace := getNamespaceName(workspaceName)

	triggers, err := tcrClient.GetTrigger(ctx, namespace)
	if err != nil {
		return bcode.ErrorTcrSystemError("get trigger failed, err: %v", err)
	}
	if len(triggers.Response.Triggers) > 0 {
		for _, trigger := range triggers.Response.Triggers {
			if *trigger.Name == triggerName {
				// 删除触发器
				err = tcrClient.DeleteTrigger(ctx, namespace, *trigger.Id)
				if err != nil {
					return bcode.ErrorTcrSystemError("delete trigger failed, err: %v", err)
				}
				return nil
			}
		}
	} else {
		kratoslog.Infof("trigger not exists, trigger name: %s", triggerName)
	}

	return nil
}

// AddImages add images 批量添加同名不同tag镜像，初始化
func (i *ImageHubService) AddImages(ctx context.Context, request *imagehubv1.AddImagesRequest) (*emptypb.Empty, error) {
	imageEntity := &model.ImageHubEntity{
		Name:        request.Name,
		Region:      request.Region,
		Domain:      request.Domain,
		Namespace:   request.Namespace,
		Description: request.Description,
		Purposes:    request.Purposes,
	}
	for _, tag := range request.Tags {
		imageEntity.ID = primitive.NewObjectID()
		imageEntity.Tag = tag
		imageEntity.FullPath = fmt.Sprintf("%s/%s/%s:%s", request.Domain, request.Namespace, request.Name, tag)
		if err := i.Store.Add(ctx, imageEntity); err != nil {
			return nil, bcode.ErrorServerInternalError("add image failed,err:%v", err)
		}
	}
	return &emptypb.Empty{}, nil
}

// DeleteImage delete image 删除镜像
func (i *ImageHubService) DeleteImage(ctx context.Context, request *imagehubv1.DeleteImageRequest) (*emptypb.Empty, error) {
	Id, err := primitive.ObjectIDFromHex(request.Id)
	if err != nil {
		return nil, err
	}

	imageEntity := &model.ImageHubEntity{
		ID: Id,
	}
	if err := i.Store.Get(ctx, imageEntity); err != nil {
		if errors.Is(err, datastore.ErrRecordNotExist) {
			return nil, nil
		}
		return nil, bcode.ErrorServerInternalError("get image failed,err:%v", err)
	}

	region := imageEntity.Region
	client := tcr.Instance()[region]

	_, err = client.DeleteImageTags(ctx, imageEntity.Namespace, imageEntity.Name, []string{imageEntity.Tag})
	if err != nil {
		return nil, bcode.ErrorServerInternalError("delete image tags failed,err:%v", err)
	}
	tags, err := client.ListImageTags(ctx, imageEntity.Namespace, imageEntity.Name, 0, 0)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("list image tags failed,err:%v", err)
	}
	if *tags.Response.TotalCount == 0 {
		_, err = client.DeleteImage(ctx, imageEntity.Namespace, imageEntity.Name)
		if err != nil {
			return nil, bcode.ErrorServerInternalError("delete image failed,err:%v", err)
		}
	}

	if err := i.Store.Delete(ctx, imageEntity); err != nil {
		return nil, bcode.ErrorServerInternalError("delete image failed,err:%v", err)
	}
	return &emptypb.Empty{}, nil
}

// UpdateImage update image 更新镜像
func (i *ImageHubService) UpdateImage(ctx context.Context, request *imagehubv1.UpdateImageRequest) (*imagehubv1.Image, error) {
	//TODO implement me
	panic("implement me")
}

// ModifyImageDescription 修改镜像描述
func (i *ImageHubService) ModifyImageDescription(ctx context.Context, request *imagehubv1.ModifyImageDescriptionRequest) (*emptypb.Empty, error) {
	Id, err := primitive.ObjectIDFromHex(request.Id)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("invalid image id,err:%v", err)
	}

	imageEntity := &model.ImageHubEntity{
		ID: Id,
	}
	if err := i.Store.Get(ctx, imageEntity); err != nil {
		if errors.Is(err, datastore.ErrRecordNotExist) {
			return nil, bcode.ErrorServerInternalError("image not exist,err:%v", err)
		}
		return nil, bcode.ErrorServerInternalError("get image failed,err:%v", err)
	}

	imageEntity.Description = request.Description
	if err := i.Store.Put(ctx, imageEntity); err != nil {
		return nil, bcode.ErrorServerInternalError("update image failed,err:%v", err)
	}
	return &emptypb.Empty{}, nil
}

// ListImages list images 列出镜像
func (i *ImageHubService) ListImages(ctx context.Context, options *imagehubv1.ListImagesOptions) (*imagehubv1.ListImagesResponse, error) {
	imageEntities, total, err := i.ListImagesByOptions(ctx, options)
	if err != nil {
		return nil, err
	}
	var images []*imagehubv1.Image
	for _, entity := range imageEntities {
		image := entity.(*model.ImageHubEntity)
		images = append(images, i.formatImage(ctx, options, image))
	}
	return &imagehubv1.ListImagesResponse{
		Images: images,
		Total:  int32(total),
	}, nil
}

func (i *ImageHubService) formatImage(ctx context.Context, options *imagehubv1.ListImagesOptions, image *model.ImageHubEntity) *imagehubv1.Image {
	imageResp := &imagehubv1.Image{
		Id:           image.ID.Hex(),
		Name:         image.Name,
		Namespace:    image.Namespace,
		Domain:       image.Domain,
		Tag:          image.Tag,
		Description:  image.Description,
		Purposes:     image.Purposes,
		Label:        image.Label,
		FullPath:     image.FullPath,
		Size:         image.Size,
		Architecture: image.Architecture,
		Region:       image.Region,
		CreateTime:   util.TimeFormat(image.CreateTime),
		UpdateTime:   util.TimeFormat(image.UpdateTime),
		FullName:     fmt.Sprintf("%s:%s", image.Name, image.Tag),
	}
	//查询最后一次配置
	var filterOptions datastore.FilterOptions
	if options.WorkspaceName != "" {
		filterOptions.In = append(filterOptions.In, datastore.InQueryOption{
			Key:    "workspaceName",
			Values: []string{options.WorkspaceName},
		})
	}
	if options.Region != "" {
		filterOptions.In = append(filterOptions.In, datastore.InQueryOption{
			Key:    "region",
			Values: []string{options.Region},
		})
	}
	if image.Name != "" {
		filterOptions.In = append(filterOptions.In, datastore.InQueryOption{
			Key:    "name",
			Values: []string{image.Name},
		})
	}
	if image.Tag != "" {
		filterOptions.In = append(filterOptions.In, datastore.InQueryOption{
			Key:    "tag",
			Values: []string{image.Tag},
		})
	}
	filterOptions.In = append(filterOptions.In, datastore.InQueryOption{
		Key:    "status",
		Values: []string{BuildStatusSucceeded},
	})
	listOpt := &datastore.ListOptions{
		FilterOptions: filterOptions,
		Page:          1,
		PageSize:      1,
		SortBy: []datastore.SortOption{
			{
				Key:   "createtime",
				Order: datastore.SortOrder(-1),
			},
		},
	}
	buildList, err := i.Store.List(ctx, &model.ImageBuildHistoryEntity{}, listOpt)
	if err != nil {
		return imageResp
	}
	if len(buildList) == 0 {
		return imageResp
	}
	build := buildList[0].(*model.ImageBuildHistoryEntity)
	buildReq := imagehubv1.BuildImageRequest{}
	err = json.Unmarshal([]byte(build.BuildRequest), &buildReq)
	if err != nil {
		return imageResp
	}
	imageResp.BuildType = buildReq.BuildType
	imageResp.BaseImage = buildReq.BaseImage
	imageResp.DockerfileSource = buildReq.DockerfileSource
	imageResp.PresetImage = buildReq.PresetImage
	imageResp.CustomImage = buildReq.CustomImage
	imageResp.ImageURL = buildReq.ImageURL
	imageResp.Run = buildReq.Run
	imageResp.Cmd = buildReq.Cmd
	imageResp.GitUrl = buildReq.GitUrl
	imageResp.Creator = buildReq.Creator
	imageResp.JobName = buildReq.JobName
	imageResp.Content = buildReq.Content
	imageResp.DevID = buildReq.DevID
	imageResp.ExcludePath = buildReq.ExcludePath
	imageResp.GitDir = buildReq.GitDir
	imageResp.GitBranch = buildReq.GitBranch
	imageResp.BuildArg = buildReq.BuildArg
	imageResp.SshKey = buildReq.SshKey
	imageResp.UseCache = buildReq.UseCache
	imageResp.UseSpeedUp = buildReq.UseSpeedUp
	return imageResp
}

// ListImagesByOptions list images by options 根据条件列出镜像
func (i *ImageHubService) ListImagesByOptions(ctx context.Context, options *imagehubv1.ListImagesOptions) ([]datastore.Entity, int64, error) {
	var filterOptions datastore.FilterOptions
	if options.Name != "" {
		filterOptions.Queries = append(filterOptions.Queries, datastore.FuzzyQueryOption{
			Key:   "name",
			Query: options.Name,
		})
	}
	if options.Region != "" {
		filterOptions.In = append(filterOptions.In, datastore.InQueryOption{
			Key:    "region",
			Values: []string{options.Region},
		})
	}
	if options.Namespace != "" {
		filterOptions.In = append(filterOptions.In, datastore.InQueryOption{
			Key:    "namespace",
			Values: []string{options.Namespace},
		})
	}

	if options.WorkspaceName != "" {
		filterOptions.In = append(filterOptions.In, datastore.InQueryOption{
			Key:    "workspaceName",
			Values: []string{options.WorkspaceName},
		})
	}
	if options.SpecificName != "" {
		filterOptions.In = append(filterOptions.In, datastore.InQueryOption{
			Key:    "name",
			Values: []string{options.SpecificName},
		})
	}

	if options.Tag != "" {
		filterOptions.Queries = append(filterOptions.Queries, datastore.FuzzyQueryOption{
			Key:   "tag",
			Query: options.Tag,
		})
	}
	if options.Creator != "" {
		filterOptions.In = append(filterOptions.In, datastore.InQueryOption{
			Key:    "pusher",
			Values: []string{options.Creator},
		})
	}

	var imageEntity = model.ImageHubEntity{}
	total, err := i.Store.Count(ctx, &imageEntity, &filterOptions)
	if err != nil {
		return nil, 0, bcode.ErrorServerInternalError("count images failed,err:%v", err)
	}
	imageEntities, err := i.Store.List(ctx, &imageEntity, &datastore.ListOptions{
		FilterOptions: filterOptions,
		Page:          int(options.Page),
		PageSize:      int(options.PageSize),
		SortBy: []datastore.SortOption{
			{Key: "name", Order: datastore.SortOrderAscending},
		},
	})
	if err != nil {
		return nil, 0, bcode.ErrorServerInternalError("list preset images failed,err:%v", err)
	}
	return imageEntities, total, nil
}

// GetServiceAccounts 获取服务账号
func (i *ImageHubService) GetServiceAccounts(ctx context.Context, request *imagehubv1.GetServiceAccountsRequest) (*imagehubv1.ServiceAccounts, error) {
	workspaceName := request.WorkspaceName
	serviceAccounts := make([]*imagehubv1.ServiceAccount, 0, 2)

	readOnlyServiceAccountName := getTCRName(getReadOnlyServiceAccountName(workspaceName))
	readOnlyTCREntity := &model.TcrServiceAccountEntity{
		Name:   readOnlyServiceAccountName,
		Region: request.Region,
	}
	err := i.Store.Get(ctx, readOnlyTCREntity)
	if err != nil {
		return nil, err
	}
	serviceAccounts = append(serviceAccounts, ModelToEntity(readOnlyTCREntity))

	readWriteServiceAccountName := getTCRName(getReadWriteServiceAccountName(workspaceName))
	readWriteTCREntity := &model.TcrServiceAccountEntity{
		Name:   readWriteServiceAccountName,
		Region: request.Region,
	}
	err = i.Store.Get(ctx, readWriteTCREntity)
	if err != nil {
		return nil, err
	}
	serviceAccounts = append(serviceAccounts, ModelToEntity(readWriteTCREntity))

	return &imagehubv1.ServiceAccounts{
		ServiceAccounts: serviceAccounts,
	}, nil
}

func ModelToEntity(model *model.TcrServiceAccountEntity) *imagehubv1.ServiceAccount {
	return &imagehubv1.ServiceAccount{
		Name:               model.Name,
		Namespace:          model.NamespaceName,
		Description:        model.Description,
		ServiceAccountType: model.ServiceAccountType,
		Region:             model.Region,
		Password:           model.Password,
		Timestamp: &common.TimestampModel{
			CreateTime: util.TimeFormat(model.CreateTime),
			UpdateTime: util.TimeFormat(model.UpdateTime),
		},
	}
}

// getTriggerName 获取触发器名称
func getTriggerName(workspaceName string) string {
	return "webhook-trigger-" + workspaceName
}

// getNamespaceName 获取命名空间名称
func getNamespaceName(workspaceName string) string {
	if workspaceName == "aistudio" {
		return workspaceName
	}
	return AiStudioPrefix + workspaceName
}

// getReadOnlyServiceAccountName 获取只读服务账号名称
func getReadOnlyServiceAccountName(workspaceName string) string {
	return AiStudioPrefix + workspaceName + ReadonlySuffix
}

// getReadWriteServiceAccountName 获取读写服务账号名称
func getReadWriteServiceAccountName(workspaceName string) string {
	return AiStudioPrefix + workspaceName + ReadwriteSuffix
}

// getTCRName 获取TCR名称
func getTCRName(name string) string {
	return TCRPrefix + name
}

// getNameRemoveNamespacePrefix 获取名称（去掉 namespace 前缀）
func getNameRemoveNamespacePrefix(name, namespace string) string {
	if strings.HasPrefix(name, namespace) {
		// 获取 namespace 后面的部分
		suffix := strings.TrimPrefix(name, namespace)
		// 如果 suffix 以 '/' 开头，去掉 '/'
		if strings.HasPrefix(suffix, "/") {
			return suffix[1:]
		}
		return suffix
	}
	return name // 如果不匹配，返回原始的 name
}

// getImageFullPath 获取image全路径
func getImageFullPath(name, tag string) string {
	return "bj-harbor01.ke.com/" + name + ":" + tag
}

// getUpdateTime 获取更新时间
func getUpdateTime(updateTime string) string {
	// 自定义时间格式，匹配原始时间字符串的格式
	layout := "2006-01-02 15:04:05.999999 -0700 MST"

	t, err := time.Parse(layout, updateTime)
	if err != nil {
		return updateTime
	}
	formattedTime := t.Format("2006-01-02 15:04:05")
	return formattedTime
}

func getTagUpdateTimeStr(updateTime string) string {
	// 解析UTC时间
	layout := "2006-01-02T15:04:05.999999Z"
	utcTime, err := time.Parse(layout, updateTime)
	if err != nil {
		return updateTime
	}
	return util.TimeFormat(utcTime)
}

// ByteSize 字节大小转换为可读的字符串
func ByteSize(bytes int64) string {
	const (
		_          = iota
		KB float64 = 1 << (10 * iota)
		MB
		GB
	)
	unit := ""
	value := float64(bytes)

	switch {
	case bytes >= int64(GB):
		unit = "GB"
		value /= GB
	case bytes >= int64(MB):
		unit = "MB"
		value /= MB
	case bytes >= int64(KB):
		unit = "KB"
		value /= KB
	default:
		unit = "B"
		return fmt.Sprintf("%.2f%s", value, unit)
	}

	return fmt.Sprintf("%.2f%s", value, unit)
}

// CreatePreheatJob 创建预热任务
func (i *ImageHubService) CreatePreheatJob(ctx context.Context, request *imagehubv1.CreatePreheatJobRequest) (*emptypb.Empty, error) {
	creator := ctx.Value(constant.UserCtxKey).(string)

	policy, err := getImagePullPolicyFromInt32ToKruise(int32(request.ImagePullPolicy))
	if err != nil {
		return nil, err
	}

	// 1. 首先添加数据库
	Id := primitive.NewObjectID()
	job := &model.PreheatJobEntity{
		ID:              Id,
		WorkspaceName:   request.WorkspaceName,
		Region:          request.Region,
		Images:          request.Images,
		Queues:          request.Queues,
		Creator:         creator,
		ImagePullPolicy: request.ImagePullPolicy.String(),
		BaseModel: model.BaseModel{
			CreateTime: time.Now(),
			UpdateTime: time.Now(),
		},
	}
	err = i.Store.Add(ctx, job)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("datastore create preheat job failed, err: %v", err)
	}

	// 2. 集群内创建预热任务
	name := Id.Hex()
	multiClusterImagePullJob := &v1alpha1.MultiClusterImagePullJob{}
	err = multicluster.Instance().GetLocalCluster().Direct().Get(ctx, client.ObjectKey{Name: name, Namespace: getNamespaceName(request.WorkspaceName)}, multiClusterImagePullJob)
	if err != nil {
		if apierrors.IsNotFound(err) {
			multiClusterImagePullJob = &v1alpha1.MultiClusterImagePullJob{
				ObjectMeta: metav1.ObjectMeta{
					Name:      name,
					Namespace: getNamespaceName(request.WorkspaceName),
					Labels: map[string]string{
						"creator": creator,
					},
				},
				Spec: v1alpha1.MultiClusterImagePullJobSpec{
					Images:   request.Images,
					Clusters: i.GetClusterNameByQueueName(ctx, request.WorkspaceName, request.Queues),
					ImagePullJobTemplate: kruiseapiv1alpha1.ImagePullJobTemplate{
						Parallelism:     &intstr.IntOrString{Type: intstr.Int, IntVal: 10},
						ImagePullPolicy: policy,
						CompletionPolicy: kruiseapiv1alpha1.CompletionPolicy{
							Type:                    "Always",
							ActiveDeadlineSeconds:   ToInt64Ptr(ActiveDeadlineSeconds),
							TTLSecondsAfterFinished: ToInt32Ptr(TTLSecondsAfterFinished),
						},
						Selector: &kruiseapiv1alpha1.ImagePullJobNodeSelector{
							LabelSelector: metav1.LabelSelector{
								MatchExpressions: []metav1.LabelSelectorRequirement{
									{
										Key:      constant.QueueNameLabelKey,
										Operator: metav1.LabelSelectorOpIn,
										Values:   request.Queues,
									},
								},
							},
						},
					},
				},
			}
			err = multicluster.Instance().GetLocalCluster().Direct().Create(ctx, multiClusterImagePullJob)
			if err != nil {
				return nil, bcode.ErrorServerInternalError("cluster create multiClusterImagePullJob failed, err: %v", err)
			}
			return &emptypb.Empty{}, nil
		}
	}
	return nil, bcode.ErrorServerInternalError("cluster multiClusterImagePullJob already exists, err: %v", err)
}

// GetClusterNameByQueueName 根据队列名获取集群名
func (i *ImageHubService) GetClusterNameByQueueName(ctx context.Context, workspaceName string, queues []string) []string {
	clusterSet := make(map[string]struct{})
	var clusterNames []string
	for _, queue := range queues {
		queueEntity, err := repository.GetQueueForWorkspace(ctx, i.Store, workspaceName, queue)
		if err != nil {
			return nil
		}
		names, err := i.ClusterManager.GetClustersByNodeIPs(ctx, queueEntity.Nodes)
		if err != nil {
			return nil
		}
		for _, name := range names {
			if _, exists := clusterSet[name]; !exists {
				clusterSet[name] = struct{}{}
				clusterNames = append(clusterNames, name)
			}
		}

	}
	return clusterNames
}

// GetPreheatJob 获取预热任务
func (i *ImageHubService) GetPreheatJob(ctx context.Context, request *imagehubv1.GetPreheatJobRequest) (*imagehubv1.PreheatJob, error) {
	Id, err := primitive.ObjectIDFromHex(request.Id)
	if err != nil {
		return nil, err
	}

	job := model.PreheatJobEntity{
		ID: Id,
	}
	err = i.Store.Get(ctx, &job)
	if err != nil {
		return nil, err
	}

	name := request.Id
	policy, ok := stringToProtoPolicy[job.ImagePullPolicy]
	if !ok {
		return nil, bcode.ErrorServerInternalError("invalid ImagePullPolicy value")
	}

	namespace := getNamespaceName(request.WorkspaceName)
	multiClusterImagePullJob := &v1alpha1.MultiClusterImagePullJob{}
	err = multicluster.Instance().GetLocalCluster().Direct().Get(ctx, client.ObjectKey{Name: name, Namespace: namespace}, multiClusterImagePullJob)
	if err != nil {
		if apierrors.IsNotFound(err) {
			return &imagehubv1.PreheatJob{
				Id:              request.Id,
				WorkspaceName:   job.WorkspaceName,
				Region:          job.Region,
				Images:          job.Images,
				Queues:          job.Queues,
				Creator:         job.Creator,
				ImagePullPolicy: policy,
				JobStatus:       "JobNotFoundInCluster",
				Rate:            0,
			}, nil
		}
		return nil, bcode.ErrorServerInternalError("get preheat job failed, err: %v", err)
	}
	rate := int32(0)
	if multiClusterImagePullJob.Status.Total == 0 {
		if multiClusterImagePullJob.Status.State == v1alpha1.JobCompleted {
			rate = 100
		} else {
			rate = 0
		}
	} else {
		rate = int32(float32(multiClusterImagePullJob.Status.Succeeded) / float32(multiClusterImagePullJob.Status.Total) * 100)
	}

	var desired, active, succeeded, failed int32
	var failedNodes []string
	var imagePullJobs []*imagehubv1.ImagePullJob
	clusterImagePullJobStatus := multiClusterImagePullJob.Status.JobStatuses
	for _, clusterJobStatus := range clusterImagePullJobStatus {
		for _, jobStatus := range clusterJobStatus.ImagePullJobStatuses {
			imageJobStatus := ""
			if jobStatus.ImagePullJobStatus.Failed > 0 {
				imageJobStatus = "Failed"
			} else if jobStatus.ImagePullJobStatus.Active > 0 {
				imageJobStatus = "Running"
			} else if !jobStatus.ImagePullJobStatus.CompletionTime.IsZero() {
				imageJobStatus = "Completed"
			} else {
				imageJobStatus = "Pending"
			}
			rate := int32(0)
			if jobStatus.ImagePullJobStatus.Desired == 0 {
				if imageJobStatus == "Completed" {
					rate = 100
				} else {
					rate = 0
				}
			} else {
				rate = int32(float64(jobStatus.ImagePullJobStatus.Succeeded) / float64(jobStatus.ImagePullJobStatus.Desired) * 100)
			}
			imagePullJobs = append(imagePullJobs, &imagehubv1.ImagePullJob{
				Id:          jobStatus.Name,
				Cluster:     clusterJobStatus.Cluster,
				Image:       jobStatus.Image,
				Status:      imageJobStatus,
				Rate:        rate,
				Desired:     jobStatus.ImagePullJobStatus.Desired,
				Succeeded:   jobStatus.ImagePullJobStatus.Succeeded,
				Failed:      jobStatus.ImagePullJobStatus.Failed,
				Active:      jobStatus.ImagePullJobStatus.Active,
				FailedNodes: jobStatus.ImagePullJobStatus.FailedNodes,
				Message:     jobStatus.ImagePullJobStatus.Message,
			})
			desired += jobStatus.ImagePullJobStatus.Desired
			active += jobStatus.ImagePullJobStatus.Active
			succeeded += jobStatus.ImagePullJobStatus.Succeeded
			failed += jobStatus.ImagePullJobStatus.Failed
			failedNodes = append(failedNodes, jobStatus.ImagePullJobStatus.FailedNodes...)
		}
	}

	return &imagehubv1.PreheatJob{
		Id:              request.Id,
		WorkspaceName:   request.WorkspaceName,
		Region:          request.Region,
		Images:          job.Images,
		Queues:          job.Queues,
		Creator:         job.Creator,
		ImagePullPolicy: policy,
		Rate:            rate,
		JobStatus:       string(multiClusterImagePullJob.Status.State),
		Timestamp: &common.TimestampModel{
			CreateTime: util.TimeFormat(job.BaseModel.CreateTime),
			UpdateTime: util.TimeFormat(job.BaseModel.UpdateTime),
		},
		ImagePullJobs: imagePullJobs,
		Desired:       desired,
		Active:        active,
		Succeeded:     succeeded,
		Failed:        failed,
		FailedNodes:   failedNodes,
	}, nil
}

// ListPreheatJobs TODO 增加分页，现前端分页
// ListPreheatJobs 列出预热任务
func (i *ImageHubService) ListPreheatJobs(ctx context.Context, options *imagehubv1.ListPreheatJobOptions) (*imagehubv1.PreheatJobs, error) {
	jobs, err := i.Store.List(ctx, &model.PreheatJobEntity{WorkspaceName: options.WorkspaceName, Region: options.Region}, &datastore.ListOptions{
		SortBy: []datastore.SortOption{
			{Key: "createTime", Order: datastore.SortOrderDescending},
		},
		Page:     int(options.PageNo),
		PageSize: int(options.PageSize),
	})
	if err != nil {
		return nil, err
	}
	var preheatJobs []*imagehubv1.PreheatJob
	for _, entity := range jobs {
		job := entity.(*model.PreheatJobEntity)
		preheatJob, err := i.GetPreheatJob(ctx, &imagehubv1.GetPreheatJobRequest{
			Id:            job.ID.Hex(),
			WorkspaceName: options.WorkspaceName,
			Region:        options.Region,
		})
		if err != nil {
			return nil, err
		}
		preheatJobs = append(preheatJobs, preheatJob)
	}

	return &imagehubv1.PreheatJobs{
		PreheatJobs: preheatJobs,
	}, nil
}

// DeletePreheatJob 删除预热任务
func (i *ImageHubService) DeletePreheatJob(ctx context.Context, request *imagehubv1.DeletePreheatJobRequest) (*emptypb.Empty, error) {
	Id, err := primitive.ObjectIDFromHex(request.Id)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("invalid id, err: %v", err)
	}

	job := model.PreheatJobEntity{
		ID: Id,
	}
	err = i.Store.Get(ctx, &job)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("get preheat job failed, err: %v", err)
	}

	namespace := getNamespaceName(job.WorkspaceName)
	err = multicluster.Instance().GetLocalCluster().Direct().Delete(ctx, &v1alpha1.MultiClusterImagePullJob{
		ObjectMeta: metav1.ObjectMeta{
			Name:      request.Id,
			Namespace: namespace,
		},
	})
	if err != nil {
		return nil, bcode.ErrorServerInternalError("cluster delete multi cluster image list pull job failed, err: %v", err)
	}

	err = i.Store.Delete(ctx, &job)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("delete preheat job failed, err: %v", err)
	}

	return &emptypb.Empty{}, nil
}

// RedeployPreheatJob 重新部署预热任务
func (i *ImageHubService) RedeployPreheatJob(ctx context.Context, request *imagehubv1.RedeployPreheatJobRequest) (*emptypb.Empty, error) {
	Id, err := primitive.ObjectIDFromHex(request.Id)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("invalid id, err: %v", err)
	}

	job := model.PreheatJobEntity{
		ID: Id,
	}
	err = i.Store.Get(ctx, &job)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("get preheat job failed, err: %v", err)
	}

	namespace := getNamespaceName(job.WorkspaceName)
	err = multicluster.Instance().GetLocalCluster().Direct().Delete(ctx, &v1alpha1.MultiClusterImagePullJob{
		ObjectMeta: metav1.ObjectMeta{
			Name:      request.Id,
			Namespace: namespace,
		},
	})
	if err != nil {
		return nil, bcode.ErrorServerInternalError("cluster delete multi cluster image list pull job failed, err: %v", err)
	}
	pollCtx, cancel := context.WithTimeout(ctx, 20*time.Second)
	defer cancel()

	timer := time.NewTimer(1 * time.Second)
	defer timer.Stop()

	name := job.ID.Hex()
	deleted := false
	for {
		select {
		case <-pollCtx.Done():
			return nil, bcode.ErrorServerInternalError("delete multi cluster image list pull job timeout")
		case <-timer.C:
			multiClusterImagePullJob := &v1alpha1.MultiClusterImagePullJob{}
			err = multicluster.Instance().GetLocalCluster().Direct().Get(ctx, client.ObjectKey{Name: name, Namespace: namespace}, multiClusterImagePullJob)
			if apierrors.IsNotFound(err) {
				deleted = true
				break
			}
			if err != nil {
				return nil, bcode.ErrorServerInternalError("get multi cluster image list pull job failed, err: %v", err)
			}
		}
		if deleted {
			break
		}
	}

	policy, err := getImagePullPolicyFromStringToKruise(job.ImagePullPolicy)
	if err != nil {
		return nil, err
	}

	multiClusterImagePullJob := &v1alpha1.MultiClusterImagePullJob{
		ObjectMeta: metav1.ObjectMeta{
			Name:      name,
			Namespace: namespace,
			Labels: map[string]string{
				"creator": job.Creator,
			},
		},
		Spec: v1alpha1.MultiClusterImagePullJobSpec{
			Images:   job.Images,
			Clusters: i.GetClusterNameByQueueName(ctx, job.WorkspaceName, job.Queues),
			ImagePullJobTemplate: kruiseapiv1alpha1.ImagePullJobTemplate{
				Parallelism:     &intstr.IntOrString{Type: intstr.Int, IntVal: 10},
				ImagePullPolicy: policy,
				CompletionPolicy: kruiseapiv1alpha1.CompletionPolicy{
					Type:                    "Always",
					ActiveDeadlineSeconds:   ToInt64Ptr(ActiveDeadlineSeconds),
					TTLSecondsAfterFinished: ToInt32Ptr(TTLSecondsAfterFinished),
				},
				Selector: &kruiseapiv1alpha1.ImagePullJobNodeSelector{
					LabelSelector: metav1.LabelSelector{
						MatchExpressions: []metav1.LabelSelectorRequirement{
							{
								Key:      constant.QueueNameLabelKey,
								Operator: metav1.LabelSelectorOpIn,
								Values:   job.Queues,
							},
						},
					},
				},
			},
		},
	}

	err = multicluster.Instance().GetLocalCluster().Direct().Create(ctx, multiClusterImagePullJob)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("cluster create multiClusterImagePullJob failed, err: %v", err)
	}
	return &emptypb.Empty{}, nil

}

// TimedSynchronizeImages 定时同步镜像
func (i *ImageHubService) TimedSynchronizeImages(ctx context.Context, properties property.EnvironmentProperty) {

	// 先进行一次同步操作
	err := i.SynchronizeImages(ctx, properties)
	if err != nil {
		klog.Errorf("initial synchronize images failed, err: %v", err)
	} else {
		klog.Info("initial synchronize images success")
	}

	timer := time.NewTimer(time.Hour * 2)
	defer timer.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-timer.C:
			err := i.SynchronizeImages(ctx, properties)
			if err != nil {
				klog.Errorf("synchronize images failed, err: %v", err)
				timer.Reset(3 * time.Minute)
				continue
			}
			klog.Info("synchronize images success")
			timer.Reset(time.Hour * 2)
		}
	}
}

func (i *ImageHubService) equal(tcrImage *imagehubv1.Image, image *model.ImageHubEntity) bool {
	if tcrImage.Size != image.Size || tcrImage.FullPath != image.FullPath || tcrImage.UpdateTime > util.TimeFormat(image.UpdateTime) {
		return false
	}
	return true
}

func getImageUK(workspaceName, region, name, tag string) string {
	return fmt.Sprintf("%s:%s:%s:%s", workspaceName, region, name, tag)
}

func getImageBaseUK(workspaceName, region, name string) string {
	return fmt.Sprintf("%s:%s:%s", workspaceName, region, name)
}

// SynchronizeImages synchronize images从tcr同步镜像到数据库
func (i *ImageHubService) SynchronizeImages(ctx context.Context, properties property.EnvironmentProperty) error {
	klog.Infof("start to synchronize images")
	regions := properties.GetStringSlice("tcr.regions")
	if len(regions) == 0 {
		return bcode.ErrorServerInternalError("no tcr regions found")
	}
	workspaces, err := i.WorkspaceService.ListEnabledWorkspaces(ctx)
	if err != nil {
		return bcode.ErrorServerInternalError("list workspaces failed, err: %v", err)
	}
	workspaces = append(workspaces, "aistudio")
	for _, region := range regions {
		dbImageMap, err := i.GetAllDbImages(ctx, region)
		if err != nil {
			return bcode.ErrorServerInternalError("list images from db failed, err: %v", err)
		}
		dbImageBaseMap, err := i.GetAllDbImageBase(ctx, region)
		if err != nil {
			return bcode.ErrorServerInternalError("list images from db failed, err: %v", err)
		}
		tcrImages, err := i.GetAllImagesFromTCR(ctx, &imagehubv1.ListImagesOptions{
			Region: region,
		})
		if err != nil {
			return bcode.ErrorServerInternalError("list images from tcr failed, err: %v", err)
		}
		for fullName, image := range tcrImages {
			workspaceName := getWorkspaceNameFromNamespace(image.Namespace)
			if !util.StringInSlice(workspaceName, workspaces) {
				continue
			}

			if _, exists := dbImageBaseMap[getImageBaseUK(workspaceName, region, image.Name)]; !exists {
				creator := getReadWriteServiceAccountName(workspaceName)
				imageBaseEntity := &model.ImageBaseEntity{
					ID:            primitive.NewObjectID(),
					WorkspaceName: workspaceName,
					Region:        region,
					Name:          image.Name,
					Description:   image.Description,
					Managers:      []string{creator},
					Members:       []string{creator},
					Creator:       creator,
				}
				err = i.Store.Add(ctx, imageBaseEntity)
				if err != nil {
					return bcode.ErrorServerInternalError("add image base to DB failed, err: %v", err)
				}
				dbImageBaseMap[getImageBaseUK(workspaceName, region, image.Name)] = imageBaseEntity
			}
			if _, exists := dbImageMap[fullName]; !exists {
				imageEntity := &model.ImageHubEntity{
					ID:            primitive.NewObjectID(),
					WorkspaceName: workspaceName,
					Region:        region,
					Namespace:     image.Namespace,
					Name:          image.Name,
					Tag:           image.Tag,
					FullPath:      image.FullPath,
					Size:          image.Size,
				}
				err = i.Store.Add(ctx, imageEntity)
				if err != nil {
					return bcode.ErrorServerInternalError("add image to DB failed, err: %v", err)
				}
				dbImageMap[fullName] = imageEntity
			} else if !i.equal(image, dbImageMap[fullName]) {
				dbImage := dbImageMap[fullName]
				dbImage.Size = image.Size
				dbImage.FullPath = image.FullPath
				err = i.Store.Put(ctx, dbImage)
				if err != nil {
					return bcode.ErrorServerInternalError("update image to DB failed, err: %v", err)
				}
				dbImageMap[fullName] = dbImage
			}
		}
	}
	klog.Infof("synchronize images successfully")
	return nil
}

func (i *ImageHubService) GetAllDbImages(ctx context.Context, region string) (map[string]*model.ImageHubEntity, error) {
	filter := datastore.FilterOptions{
		In: []datastore.InQueryOption{
			{
				Key:    "region",
				Values: []string{region},
			},
		},
	}
	images := map[string]*model.ImageHubEntity{}
	page := 1
	pageSize := 1000
	repeatImage := make([]*model.ImageHubEntity, 0)
	for {
		list, err := i.Store.List(ctx, &model.ImageHubEntity{}, &datastore.ListOptions{
			Page:          page,
			PageSize:      pageSize,
			FilterOptions: filter,
		})
		if err != nil {
			return nil, err
		}
		if len(list) == 0 {
			break
		}
		for _, entity := range list {
			image := entity.(*model.ImageHubEntity)
			if _, exists := images[getImageUK(image.WorkspaceName, image.Region, image.Name, image.Tag)]; exists {
				repeatImage = append(repeatImage, image)
				continue
			}
			images[getImageUK(image.WorkspaceName, image.Region, image.Name, image.Tag)] = image
		}
		page++
	}
	for _, entity := range repeatImage {
		err := i.Store.Delete(ctx, entity)
		if err != nil {
			return nil, err
		}
	}
	return images, nil
}

func (i *ImageHubService) GetAllDbImageBase(ctx context.Context, region string) (map[string]*model.ImageBaseEntity, error) {
	filter := datastore.FilterOptions{
		In: []datastore.InQueryOption{
			{
				Key:    "region",
				Values: []string{region},
			},
		},
	}
	images := map[string]*model.ImageBaseEntity{}
	page := 1
	pageSize := 1000
	repeatImage := make([]model.ImageBaseEntity, 0)
	for {
		list, err := i.Store.List(ctx, &model.ImageBaseEntity{}, &datastore.ListOptions{
			Page:          page,
			PageSize:      pageSize,
			FilterOptions: filter,
		})
		if err != nil {
			return nil, err
		}
		if len(list) == 0 {
			break
		}
		for _, entity := range list {
			image := entity.(*model.ImageBaseEntity)
			if _, exists := images[getImageBaseUK(image.WorkspaceName, image.Region, image.Name)]; exists {
				repeatImage = append(repeatImage, *image)
				continue
			}
			images[getImageBaseUK(image.WorkspaceName, image.Region, image.Name)] = image
		}
		page++
	}
	for _, entity := range repeatImage {
		err := i.Store.Delete(ctx, &entity)
		if err != nil {
			return nil, err
		}
	}
	return images, nil
}

func (i *ImageHubService) GetAllImagesFromTCR(ctx context.Context, options *imagehubv1.ListImagesOptions) (map[string]*imagehubv1.Image, error) {
	var client *tcr.Client
	if options.Region == Region_bj {
		client = tcr.Instance()[Region_bj]
	} else if options.Region == Region_sh {
		client = tcr.Instance()[Region_sh]
	} else {
		client = tcr.Instance()[Region_bj]
	}
	res := map[string]*imagehubv1.Image{}
	var namespace string
	if options.WorkspaceName == "" {
		namespace = ""
	} else {
		namespace = getNamespaceName(options.WorkspaceName)
	}
	images, err := client.ListImages(ctx, namespace, 0, 0)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("list custom images failed, err: %v", err)
	}
	kratoslog.Infof("list images from TCR, count: %d", *images.Response.TotalCount)

	var wg sync.WaitGroup
	var mu sync.Mutex
	ch := make(chan struct{}, 8)
	for _, image := range images.Response.RepositoryList {
		tempImage := image
		wg.Add(1)
		// 获取信号量
		ch <- struct{}{}
		go func() {
			err := func(image *tcrModels.TcrRepositoryInfo) error {
				defer func() {
					<-ch
					wg.Done()
				}()
				namespace := *image.Namespace
				imageName := getNameRemoveNamespacePrefix(*image.Name, namespace)
				imageTags, err := client.ListImageTags(ctx, namespace, imageName, 0, 0)
				if err != nil {

					return bcode.ErrorServerInternalError("list custom images failed, err: %v", err)
				}
				mu.Lock()
				for _, tag := range imageTags.Response.ImageInfoList {
					i := &imagehubv1.Image{
						Name:        getNameRemoveNamespacePrefix(*image.Name, *image.Namespace),
						Namespace:   *image.Namespace,
						Description: *image.Description,
						UpdateTime:  getTagUpdateTimeStr(*tag.UpdateTime),
						FullPath:    getImageFullPath(*image.Name, *tag.ImageVersion),
						Tag:         *tag.ImageVersion,
						Size:        ByteSize(*tag.Size),
					}
					workspaceName := getWorkspaceNameFromNamespace(i.Namespace)
					res[getImageUK(workspaceName, options.Region, i.Name, i.Tag)] = i
				}
				mu.Unlock()
				return nil
			}(tempImage)
			if err != nil {
			}
		}()
	}
	wg.Wait() // 等待所有协程完成
	return res, nil
}

// ListImageNames list image names 获取镜像名称列表
func (i *ImageHubService) ListImageNames(ctx context.Context, options *imagehubv1.ListImageNamesOptions) (*imagehubv1.ListImageNamesResponse, error) {
	imagesListOptions := &imagehubv1.ListImagesOptions{
		WorkspaceName: options.WorkspaceName,
		Region:        options.Region,
	}
	if options.Name != "" {
		imagesListOptions.Name = options.Name
	}

	imageEntities, _, err := i.ListImagesByOptions(ctx, imagesListOptions)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("list image names failed, err: %v", err)
	}
	var imageNames []*imagehubv1.ImageName
	nameSet := make(map[string]struct{})
	for _, entity := range imageEntities {
		image := entity.(*model.ImageHubEntity)
		if _, exists := nameSet[image.Name]; !exists {
			imageNames = append(imageNames, &imagehubv1.ImageName{
				Name: image.Name,
			})
			nameSet[image.Name] = struct{}{}
		}
	}
	return &imagehubv1.ListImageNamesResponse{
		ImageNames: imageNames,
	}, nil
}

// ListImageTags list image tags 获取镜像标签列表
func (i *ImageHubService) ListImageTags(ctx context.Context, options *imagehubv1.ListImageTagsOptions) (*imagehubv1.ListImageTagsResponse, error) {
	imageEntities, _, err := i.ListImagesByOptions(ctx, &imagehubv1.ListImagesOptions{
		WorkspaceName: options.WorkspaceName,
		Region:        options.Region,
		Name:          options.ImageName,
	})
	if err != nil {
		return nil, bcode.ErrorServerInternalError("list image tags failed, err: %v", err)
	}
	var imageTags []*imagehubv1.ImageTag
	for _, entity := range imageEntities {
		image := entity.(*model.ImageHubEntity)
		imageTags = append(imageTags, &imagehubv1.ImageTag{
			Tag:  image.Tag,
			Path: image.FullPath,
		})
	}

	return &imagehubv1.ListImageTagsResponse{
		ImageTags: imageTags,
	}, nil
}

// GetImageFromTCR get image 从tcr获取镜像
func (i *ImageHubService) GetImageFromTCR(ctx context.Context, request *imagehubv1.GetImageTagRequest) (*imagehubv1.Image, error) {
	var client *tcr.Client
	if request.Region == Region_bj {
		client = tcr.Instance()[Region_bj]
	} else if request.Region == Region_sh {
		client = tcr.Instance()[Region_sh]
	} else {
		client = tcr.Instance()[Region_bj]
	}
	namespace := getNamespaceName(request.WorkspaceName)
	var image *imagehubv1.Image

	imageRes, err := client.GetImage(ctx, namespace, request.ImageName)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("get image failed, err: %v", err)
	}

	res, err := client.GetImageTag(ctx, namespace, request.ImageName, request.Tag)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("get image tag failed, err: %v", err)
	}

	tag := res.Response.ImageInfoList[0]
	image = &imagehubv1.Image{
		Name:        request.ImageName,
		Namespace:   namespace,
		Description: *imageRes.Response.RepositoryList[0].Description,
		UpdateTime:  getUpdateTime(*imageRes.Response.RepositoryList[0].UpdateTime),
		FullPath:    getImageFullPath(request.ImageName, *tag.ImageVersion),
		Tag:         *tag.ImageVersion,
		Size:        ByteSize(*tag.Size),
	}
	return image, nil
}

// GetImageTag get image tag 获取镜像标签
func (i *ImageHubService) GetImageTag(ctx context.Context, request *imagehubv1.GetImageTagRequest) (*imagehubv1.Image, error) {
	id, err := primitive.ObjectIDFromHex(request.Id)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("invalid image id, err: %v", err)
	}
	image := model.ImageHubEntity{
		ID: id,
	}
	if err := i.Store.Get(ctx, &image); err != nil {
		return nil, bcode.ErrorServerInternalError("get image tag failed, err: %v", err)
	}
	return &imagehubv1.Image{
		Name:        image.Name,
		Namespace:   image.Namespace,
		Description: image.Description,
		UpdateTime:  image.UpdateTime.String(),
		FullPath:    getImageFullPath(image.Name, image.Tag),
		Tag:         image.Tag,
		Size:        image.Size,
		Region:      image.Region,
		Id:          image.ID.Hex(),
		CreateTime:  image.CreateTime.String(),
	}, nil
}

// ToInt64Ptr converts int to *int64
func ToInt64Ptr(i int) *int64 {
	v := int64(i)
	return &v
}

// ToInt32Ptr converts int to *int32
func ToInt32Ptr(i int) *int32 {
	v := int32(i)
	return &v
}

// 定义一个映射，用于将字符串转换为 proto 枚举类型
var stringToProtoPolicy = map[string]imagehubv1.ImagePullPolicy{
	"Always":       imagehubv1.ImagePullPolicy_Always,
	"IfNotPresent": imagehubv1.ImagePullPolicy_IfNotPresent,
}

// 定义一个映射，用于将 int32 转换为 Kruise 的 ImagePullPolicy
var int32ToKruisePolicy = map[int32]kruiseapiv1alpha1.ImagePullPolicy{
	0: kruiseapiv1alpha1.PullAlways,
	1: kruiseapiv1alpha1.PullIfNotPresent,
}

// 定义一个映射，用于将字符串转换为 Kruise 的 ImagePullPolicy
var stringToKruisePolicy = map[string]kruiseapiv1alpha1.ImagePullPolicy{
	"Always":       kruiseapiv1alpha1.PullAlways,
	"IfNotPresent": kruiseapiv1alpha1.PullIfNotPresent,
}

// 将 int32 转换为 Kruise 的 ImagePullPolicy
func getImagePullPolicyFromInt32ToKruise(policy int32) (kruiseapiv1alpha1.ImagePullPolicy, error) {
	if kruisePolicy, ok := int32ToKruisePolicy[policy]; ok {
		return kruisePolicy, nil
	}
	return "", errors.New("无效的 ImagePullPolicy 值")
}

// 将字符串转换为 Kruise 的 ImagePullPolicy
func getImagePullPolicyFromStringToKruise(policy string) (kruiseapiv1alpha1.ImagePullPolicy, error) {
	if kruisePolicy, ok := stringToKruisePolicy[policy]; ok {
		return kruisePolicy, nil
	}
	return "", errors.New("无效的 ImagePullPolicy 值")
}

// getWorkspaceNameFromNamespace 从 namespace 获取 workspace name
func getWorkspaceNameFromNamespace(namespace string) string {
	if strings.HasPrefix(namespace, "aistudio-") {
		return strings.TrimPrefix(namespace, "aistudio-")
	}
	return namespace
}

// BuildImage 构建镜像
func (i *ImageHubService) BuildImage(ctx context.Context, req *imagehubv1.BuildImageRequest) (*imagehubv1.BuildImageResponse, error) {
	creator := ctx.Value(constant.UserCtxKey).(string)
	req.Creator = creator

	//查询当前空间生效镜像
	var filterOptions datastore.FilterOptions

	if req.Region != "" {
		filterOptions.In = append(filterOptions.In, datastore.InQueryOption{
			Key:    "region",
			Values: []string{req.Region},
		})
	}
	if req.WorkspaceName != "" {
		filterOptions.In = append(filterOptions.In, datastore.InQueryOption{
			Key:    "workspaceName",
			Values: []string{req.WorkspaceName},
		})
	}
	if req.Name != "" {
		filterOptions.In = append(filterOptions.In, datastore.InQueryOption{
			Key:    "name",
			Values: []string{req.Name},
		})
	}
	if req.Tag != "" {
		filterOptions.NotIn = append(filterOptions.NotIn, datastore.NotInQueryOption{
			Key:    "tag",
			Values: []string{req.Tag},
		})
	}
	total, err := i.Store.Count(ctx, &model.ImageHubEntity{}, &filterOptions)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("count images failed,err:%v", err)
	}
	if total >= MaxImage {
		return &imagehubv1.BuildImageResponse{
			Status: 1,
			Msg:    "该镜像版本数量已达上限，每个镜像最多支持50个镜像版本，请删除部分镜像版本或覆盖已有镜像版本",
		}, nil
	}

	//查询一下镜像是否存在，不存在初始化一个
	_, err = i.CreateImageBase(ctx, &imagehubv1.CreateImageBaseRequest{
		WorkspaceName: req.WorkspaceName,
		Region:        req.Region,
		Name:          req.Name,
		Description:   req.Description,
	})
	if err != nil {
		return nil, bcode.ErrorServerInternalError("create image base failed,err:%v", err)
	}
	jobName := i.getImageBuildJobName(req)
	clusterName := ""
	switch req.BuildType {
	case BuildTypeDev:
		//获取开发机节点信息、容器信息
		notebook := automlkcsiov1alpha1.Notebook{}
		err := multicluster.Instance().GetLocalCluster().Direct().Get(ctx, client.ObjectKey{Name: req.DevID, Namespace: getNamespaceName(req.WorkspaceName)}, &notebook)
		if err != nil && !apierrors.IsNotFound(err) {
			return nil, bcode.ErrorServerInternalError("get dev pod failed, err: %v", err)
		}
		if apierrors.IsNotFound(err) {
			return &imagehubv1.BuildImageResponse{
				Status: 1,
				Msg:    "未找到开发机notebook，请检查开发机是否正常运行",
			}, nil
		}
		clusterName = notebook.Spec.Cluster
		podName := notebook.Status.PodName
		podInfo := corev1.Pod{}
		cluster := multicluster.Instance().GetCluster(clusterName)
		if cluster == nil {
			return nil, bcode.ErrorServerInternalError("get cluster failed")
		}
		err = cluster.Direct().Get(ctx, client.ObjectKey{Name: podName, Namespace: getNamespaceName(req.WorkspaceName)}, &podInfo)
		if err != nil && !apierrors.IsNotFound(err) {
			return nil, bcode.ErrorServerInternalError("get pod failed, err: %v", err)
		}
		if apierrors.IsNotFound(err) {
			return &imagehubv1.BuildImageResponse{
				Status: 1,
				Msg:    "未找到开发机，请检查开发机是否正常运行",
			}, nil
		}
		containerID := ""
		for _, c := range podInfo.Status.ContainerStatuses {
			if c.Name == req.DevID {
				containerID = c.ContainerID

			}
		}
		if containerID == "" {
			return nil, bcode.ErrorServerInternalError("get containerID failed")
		}
		nodeName := podInfo.Spec.NodeName
		buildJob, err := i.generateCommitJob(ctx, req, nodeName, containerID)
		if err != nil {
			return nil, bcode.ErrorServerInternalError("generate nerdctl job failed, err: %v", err)
		}
		err = cluster.Direct().Create(ctx, buildJob)
		if err != nil {
			return nil, bcode.ErrorServerInternalError("create dev commit job failed, err: %v", err)
		}
	case BuildTypeImage, BuildTypeDockerfile:
		//格式化git名称、校验git权限
		if req.BuildType == BuildTypeDockerfile && req.DockerfileSource == DockerfileSourceGit {
			req.GitUrl = formatGitUrl(req.GitUrl)
			err := checkGitPermission(req.GitUrl, req.GitBranch)
			if err != nil {
				return &imagehubv1.BuildImageResponse{
					Data:   &imagehubv1.BuildImageData{},
					Msg:    fmt.Sprintf("%v", err),
					Status: 401,
				}, nil
			}
		}
		clusterName = LocalCluster
		//创建secret
		err := i.createSecret(ctx, HarborRegcred, req.WorkspaceName, req.Region)
		if err != nil {
			return nil, bcode.ErrorServerInternalError("create secret failed, err: %v", err)
		}
		buildJob, err := i.generateKanikoJob(ctx, req)
		if err != nil {
			return nil, bcode.ErrorServerInternalError("generate kaniko job failed, err: %v", err)
		}
		err = multicluster.Instance().GetLocalCluster().Direct().Create(ctx, buildJob)
		if err != nil {
			return nil, bcode.ErrorServerInternalError("create kaniko commit job failed, err: %v", err)
		}
	}
	reqStr, _ := json.Marshal(req)
	//保存镜像构建记录
	buildHistoryEntity := &model.ImageBuildHistoryEntity{
		WorkspaceName: req.WorkspaceName,
		Region:        req.Region,
		Name:          req.Name,
		Tag:           req.Tag,
		JobName:       jobName,
		Cluster:       clusterName,
		Status:        BuildStatusWaiting,
		Reason:        getImageReason(BuildStatusWaiting, nil),
		Message:       getImageMsg(BuildStatusWaiting, nil),
		BuildRequest:  string(reqStr),
		Creator:       creator,
		Conditions: model.Conditions{
			GetImageCondition(BuildStatusWaiting, nil),
		},
	}
	err = i.Store.Add(ctx, buildHistoryEntity)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("add build history failed, err: %v", err)
	}
	resp := imagehubv1.BuildImageResponse{
		Data: &imagehubv1.BuildImageData{
			JobName: jobName,
			Tag:     req.Tag,
		},
		Msg:    "请稍后刷新页面,查看镜像构建结果",
		Status: 0,
	}
	return &resp, nil
}

func formatGitUrl(gitUrl string) string {
	gitUrl = strings.Replace(gitUrl, "*******************:", "git.lianjia.com/", -1)
	gitUrl = strings.Replace(gitUrl, "https://", "", -1)
	gitUrl = strings.Replace(gitUrl, "http://", "", -1)
	gitUrl = strings.Replace(gitUrl, "git@", "", -1)
	return gitUrl
}

func checkGitPermission(url, branch string) error {
	// 创建一个HTTP客户端
	client := &http.Client{}
	// 创建一个HTTP请求
	req, err := http.NewRequest("GET", fmt.Sprintf("https://%s/info/refs?service=git-upload-pack", url), nil)
	if err != nil {
		return fmt.Errorf("创建请求失败: %v", err)
	}
	// 添加基本认证头
	auth := fmt.Sprintf("%s:%s", GitLabUser, GitLabPwd)
	encodedAuth := base64.StdEncoding.EncodeToString([]byte(auth))
	req.Header.Add("Authorization", "Basic "+encodedAuth)

	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("无法访问仓库:%v", err)
	}
	defer resp.Body.Close()

	// 检查HTTP响应状态码
	if resp.StatusCode == http.StatusOK {
		body, err := io.ReadAll(resp.Body)
		if err != nil {
			return fmt.Errorf("读取响应失败: %v", err)
		}
		// 检查分支是否存在于响应中
		if strings.Contains(string(body), "refs/heads/"+branch) {
			return nil
		} else {
			return fmt.Errorf("分支不存在,请检查分支是否正确")
		}
	} else if resp.StatusCode == http.StatusUnauthorized {
		return fmt.Errorf("没有权限访问仓库,请检查aistudio账号是否有权限访问仓库")
	} else if resp.StatusCode == http.StatusForbidden {
		return fmt.Errorf("没有权限访问仓库,请检查aistudio账号是否有权限访问仓库")
	} else if resp.StatusCode == http.StatusNotFound {
		return fmt.Errorf("仓库不存在,请检查仓库是否正确")
	} else {
		return fmt.Errorf("无法访问仓库: HTTP %d", resp.StatusCode)
	}
}

func (i *ImageHubService) getImageBuildJobName(req *imagehubv1.BuildImageRequest) string {
	req.JobName = util.GenerateRandomStringWithPrefix(PrefixJobNameBuildImage, 4)
	return req.JobName
}

func (i *ImageHubService) getKanikoJobArgs(ctx context.Context, req *imagehubv1.BuildImageRequest) ([]string, error) {
	//构建dockerfile
	args := []string{}
	buildContext := ""
	buildDockerfile := ""
	if req.BuildType == BuildTypeImage || (req.BuildType == BuildTypeDockerfile && req.DockerfileSource == DockerfileSourceContent) {
		dockerfile := getDockerFile(req)
		if dockerfile == "" {
			return nil, bcode.ErrorServerInternalError("dockerfile is empty")
		}
		//创建文件
		dockerfileName := fmt.Sprintf("dockerfile-%s", req.JobName)
		dockerfileObj, err := createTempFile(dockerfileName, dockerfile)
		if err != nil {
			return nil, bcode.ErrorServerInternalError("create dockerfile failed, err: %v", err)
		}
		dockerfileName = filepath.Base(dockerfileObj.Name())
		defer func() {
			dockerfileObj.Close()
			os.Remove(dockerfileObj.Name()) // 程序结束时删除临时文件
		}()

		//创建压缩文件
		contextName := fmt.Sprintf("context-%s.tar.gz", req.JobName)
		contextObj, err := createTarGz(contextName, dockerfileObj)
		if err != nil {
			return nil, bcode.ErrorServerInternalError("create tar file failed, err: %v", err)
		}
		contextName = filepath.Base(contextObj.Name())
		defer func() {
			contextObj.Close()
			os.Remove(contextObj.Name()) // 程序结束时删除临时文件
		}()

		//上传文件
		bucket := s3.Instance().GetConfig().BuildImageBucket
		err = s3.Instance().PutObject(ctx, bucket, contextName, contextObj.Name())
		if err != nil {
			return nil, bcode.ErrorServerInternalError("upload tar file failed, err: %v", err)
		}
		buildContext = fmt.Sprintf("s3://%s/%s", bucket, contextName)
		buildDockerfile = dockerfileName
	} else if req.BuildType == BuildTypeDockerfile && req.DockerfileSource == DockerfileSourceGit {
		gitURL := req.GitUrl
		if req.GitBranch != "" {
			gitURL = fmt.Sprintf("%s#refs/heads/%s#", req.GitUrl, req.GitBranch)
		}
		buildContext = fmt.Sprintf("git://%s", gitURL)
		buildDockerfile = req.GitDir
	} else {
		return nil, fmt.Errorf("invalid build type: %s", req.BuildType)
	}
	args = append(args, "--dockerfile", buildDockerfile)
	args = append(args, "--context", buildContext)
	args = append(args, "--push-retry", PushRetry)
	if req.UseCache {
		args = append(args, "--cache=true")
		args = append(args, "--cache-repo=bj-harbor01.ke.com/aistudio-kaniko-cache/cache")
	}
	//实验功能：加速构建
	if req.UseSpeedUp {
		args = append(args, "--snapshot-mode=redo", "--use-new-run")
	}
	args = append(args, "--destination", fmt.Sprintf("bj-harbor01.ke.com/%s/%s:%s", getNamespaceName(req.WorkspaceName), req.Name, req.Tag))
	if req.BuildArg != "" {
		buildArgs := strings.Split(req.BuildArg, "\n")
		for _, arg := range buildArgs {
			args = append(args, "--build-arg", arg)
		}
	}
	return args, nil
}

func (i *ImageHubService) getKanikoJobConmands(ctx context.Context, req *imagehubv1.BuildImageRequest) ([]string, error) {
	//构建dockerfile
	kanikoArgs, err := i.getKanikoJobArgs(ctx, req)
	if err != nil {
		return nil, err
	}
	cmd := fmt.Sprintf("/kaniko/executor %s", strings.Join(kanikoArgs, " "))
	if req.SshKey != "" {
		cpSecretCmd := "mkdir -p /kaniko/buildcontext/.ssh && cp /secret/id_rsa /kaniko/buildcontext/.ssh/id_rsa && chmod 600 /kaniko/buildcontext/.ssh/id_rsa"
		cmd = fmt.Sprintf("%s && %s", cpSecretCmd, cmd)
	}
	commands := []string{"/bin/sh", "-c", cmd}
	return commands, nil
}

func (i *ImageHubService) getKanikoJobEnvs(req *imagehubv1.BuildImageRequest) ([]corev1.EnvVar, error) {
	envs := []corev1.EnvVar{}
	if req.BuildType == BuildTypeImage || (req.BuildType == BuildTypeDockerfile && req.DockerfileSource == DockerfileSourceContent) {
		s3Config := s3.Instance().GetConfig()
		envs = append(envs,
			[]corev1.EnvVar{
				{
					Name:  "AWS_ACCESS_KEY_ID",
					Value: s3Config.AccessKey,
				},
				{
					Name:  "AWS_SECRET_ACCESS_KEY",
					Value: s3Config.SecretKey,
				},
				{
					Name:  "AWS_REGION",
					Value: s3Config.Region,
				},
				{
					Name:  "S3_ENDPOINT",
					Value: s3Config.Endpoint,
				},
				{
					Name:  "S3_FORCE_PATH_STYLE",
					Value: fmt.Sprint(s3Config.S3ForcePathStyle),
				},
			}...)
	} else if req.BuildType == BuildTypeDockerfile && req.DockerfileSource == DockerfileSourceGit {
		envs = append(envs,
			[]corev1.EnvVar{
				{
					Name:  "GIT_USERNAME",
					Value: GitLabUser,
				},
				{
					Name:  "GIT_PASSWORD",
					Value: GitLabPwd,
				},
			}...)
	} else {
		return nil, fmt.Errorf("invalid build type: %s", req.BuildType)
	}
	envs = append(envs,
		corev1.EnvVar{
			Name:  "https_proxy",
			Value: "http://kcs-proxy.ke.com:3128",
		},
		corev1.EnvVar{
			Name:  "http_proxy",
			Value: "http://kcs-proxy.ke.com:3128",
		},
		corev1.EnvVar{
			Name:  "NO_PROXY",
			Value: "10.0.0.0/8,ke.com",
		},
	)
	return envs, nil
}

func (i *ImageHubService) generateCommitJob(ctx context.Context, req *imagehubv1.BuildImageRequest, nodeName, containerID string) (*batchv1.Job, error) {
	//获取harbor账号密码
	readWriteServiceAccountName := getTCRName(getReadWriteServiceAccountName(req.WorkspaceName))
	readWriteTCREntity := &model.TcrServiceAccountEntity{
		Name:   readWriteServiceAccountName,
		Region: req.Region,
	}
	err := i.Store.Get(ctx, readWriteTCREntity)
	if err != nil {
		return nil, err
	}
	registryPass := readWriteTCREntity.Password
	registryUser := readWriteTCREntity.Name

	hostPathSocket := corev1.HostPathSocket
	var containers []corev1.Container
	var volumes []corev1.Volume
	if strings.HasPrefix(containerID, "containerd://") {
		containerName := strings.ReplaceAll(containerID, "containerd://", "")
		containers = []corev1.Container{
			{
				Name:            "nerdctl",
				Image:           NerdCtlImage,
				ImagePullPolicy: corev1.PullAlways,
				Command: []string{
					"/bin/sh", "-c",
				},
				Args: []string{
					"./build.sh",
				},
				Env: []corev1.EnvVar{
					{
						Name:  "REGISTRY_USER",
						Value: registryUser,
					},
					{
						Name:  "REGISTRY_PASS",
						Value: registryPass,
					},
					{
						Name:  "CONTAINER_NAME",
						Value: containerName,
					},
					{
						Name:  "WORKSPACE_NAME",
						Value: req.WorkspaceName,
					},
					{
						Name:  "IMAGE_NAME",
						Value: req.Name,
					},
					{
						Name:  "IMAGE_TAG",
						Value: req.Tag,
					},
				},
				VolumeMounts: []corev1.VolumeMount{
					{
						Name:      "containerd-sock",
						MountPath: "/run/containerd/containerd.sock",
					},
				},
				Lifecycle: &corev1.Lifecycle{
					PreStop: &corev1.LifecycleHandler{
						Exec: &corev1.ExecAction{
							Command: []string{
								"/bin/sh", "-c",
								fmt.Sprintf("nerdctl unpause --namespace k8s.io %s", containerName),
							},
						},
					},
				},
			},
		}
		volumes = []corev1.Volume{
			{
				Name: "containerd-sock",
				VolumeSource: corev1.VolumeSource{
					HostPath: &corev1.HostPathVolumeSource{
						Path: "/run/containerd/containerd.sock",
						Type: &hostPathSocket,
					},
				},
			},
		}
	} else if strings.HasPrefix(containerID, "docker://") {
		containerName := strings.ReplaceAll(containerID, "docker://", "")
		containers = []corev1.Container{
			{
				Name:            "docker",
				Image:           DockerImage,
				ImagePullPolicy: corev1.PullAlways,
				Command: []string{
					"/bin/sh", "-c",
				},
				Args: []string{
					"./build.sh",
				},
				Env: []corev1.EnvVar{
					{
						Name:  "REGISTRY_USER",
						Value: registryUser,
					},
					{
						Name:  "REGISTRY_PASS",
						Value: registryPass,
					},
					{
						Name:  "CONTAINER_NAME",
						Value: containerName,
					},
					{
						Name:  "WORKSPACE_NAME",
						Value: req.WorkspaceName,
					},
					{
						Name:  "IMAGE_NAME",
						Value: req.Name,
					},
					{
						Name:  "IMAGE_TAG",
						Value: req.Tag,
					},
				},
				VolumeMounts: []corev1.VolumeMount{
					{
						Name:      "docker-sock",
						MountPath: "/var/run/docker.sock",
					},
				},
				Lifecycle: &corev1.Lifecycle{
					PreStop: &corev1.LifecycleHandler{
						Exec: &corev1.ExecAction{
							Command: []string{
								"/bin/sh", "-c", fmt.Sprintf("docker unpause %s", containerName),
							},
						},
					},
				},
			},
		}
		volumes = []corev1.Volume{
			{
				Name: "docker-sock",
				VolumeSource: corev1.VolumeSource{
					HostPath: &corev1.HostPathVolumeSource{
						Path: "/var/run/docker.sock",
						Type: &hostPathSocket,
					},
				},
			},
		}
	} else {
		return nil, fmt.Errorf("invalid container runtime version: %s", containerID)
	}

	buildJob := batchv1.Job{
		ObjectMeta: metav1.ObjectMeta{
			Name:      req.JobName,
			Namespace: getNamespaceName(req.WorkspaceName),
			Labels:    i.getBuildLabels(req),
		},
		Spec: batchv1.JobSpec{
			ActiveDeadlineSeconds:   util.Int64Ptr(BuildImageActiveDeadlineSeconds),
			TTLSecondsAfterFinished: util.Int32Ptr(BuildImageTTLSecondsAfterFinished),
			BackoffLimit:            util.Int32Ptr(BuildImageBackoffLimit),
			Template: corev1.PodTemplateSpec{
				ObjectMeta: metav1.ObjectMeta{
					Labels: i.getBuildLabels(req),
				},
				Spec: corev1.PodSpec{
					NodeName:      nodeName,
					Containers:    containers,
					RestartPolicy: corev1.RestartPolicyNever,
					Volumes:       volumes,
				},
			},
		},
	}
	return &buildJob, nil
}

func (i *ImageHubService) getBuildLabels(req *imagehubv1.BuildImageRequest) map[string]string {
	return map[string]string{
		constant.WorkspaceNameLabelKey:     req.WorkspaceName,
		constant.BuildImageJobNameLabelKey: req.JobName,
		constant.CreateByLabelKey:          req.Creator,
		constant.KICManagedLabelKey:        constant.KICManagedLabelValue,
		constant.WorkloadTypeLabelKey:      constant.BuildImageWorkloadTypeLabelValue,
	}
}

func (i *ImageHubService) generateKanikoJob(ctx context.Context, req *imagehubv1.BuildImageRequest) (*batchv1.Job, error) {
	kanikoJob := batchv1.Job{
		ObjectMeta: metav1.ObjectMeta{
			Name:      req.JobName,
			Namespace: getNamespaceName(req.WorkspaceName),
			Labels:    i.getBuildLabels(req),
		},
	}
	jobSpec, err := i.getKanikoJobSpec(ctx, req)
	if err != nil {
		return nil, err
	}

	kanikoJob.Spec = *jobSpec
	return &kanikoJob, nil
}

func (i *ImageHubService) getSecretName(req *imagehubv1.BuildImageRequest) string {
	secretName := fmt.Sprintf("aistudio-ssh-key-%s-%s-%s", req.WorkspaceName, req.Region, req.SshKey)
	secretName = strings.ReplaceAll(secretName, "_", "-")
	return secretName
}

func (i *ImageHubService) getKanikoJobSpec(ctx context.Context, req *imagehubv1.BuildImageRequest) (*batchv1.JobSpec, error) {
	jobSpec := &batchv1.JobSpec{
		ActiveDeadlineSeconds:   util.Int64Ptr(BuildImageActiveDeadlineSeconds),
		TTLSecondsAfterFinished: util.Int32Ptr(BuildImageTTLSecondsAfterFinished),
		BackoffLimit:            util.Int32Ptr(BuildImageBackoffLimit),
		Template: corev1.PodTemplateSpec{
			ObjectMeta: metav1.ObjectMeta{
				Labels: i.getBuildLabels(req),
			},
		},
	}
	podSpec, err := i.getKanikoPodSpec(ctx, req)
	if err != nil {
		return nil, err
	}
	jobSpec.Template.Spec = *podSpec
	return jobSpec, nil
}
func (i *ImageHubService) getKanikoPodSpec(ctx context.Context, req *imagehubv1.BuildImageRequest) (*corev1.PodSpec, error) {
	var hostAliases []corev1.HostAlias
	applicationEnv := ctx.Value(constant.ApplicationEnvCtxKey).(string)
	if applicationEnv == constant.ApplicationEnvProd {
		hostAliases = []corev1.HostAlias{
			{
				IP: InternalIP,
				Hostnames: []string{
					S3InternalHost,
					TCRInternalHost,
				},
			},
		}
	}
	podSpec := &corev1.PodSpec{
		HostAliases: hostAliases,
		Affinity: &corev1.Affinity{
			NodeAffinity: &corev1.NodeAffinity{
				RequiredDuringSchedulingIgnoredDuringExecution: &corev1.NodeSelector{
					NodeSelectorTerms: []corev1.NodeSelectorTerm{
						{
							MatchExpressions: []corev1.NodeSelectorRequirement{
								{
									Key:      constant.BuildImageLabelKey,
									Operator: corev1.NodeSelectorOpExists,
								},
							},
						},
					},
				},
			},
		},
		Tolerations: []corev1.Toleration{
			{
				Key:      "tenant.kcs.io/name",
				Operator: corev1.TolerationOpEqual,
				Value:    "cloudnative",
			},
		},
		RestartPolicy: corev1.RestartPolicyNever,
	}
	containers, err := i.getKanikoContainers(ctx, req)
	if err != nil {
		return nil, err
	}
	podSpec.Containers = containers
	volumes, err := i.getKanikoVolumes(ctx, req)
	if err != nil {
		return nil, err
	}
	podSpec.Volumes = volumes
	return podSpec, nil
}

func (i *ImageHubService) getKanikoContainers(ctx context.Context, req *imagehubv1.BuildImageRequest) ([]corev1.Container, error) {
	cmds, err := i.getKanikoJobConmands(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("get kaniko cmds failed, err: %v", err)
	}
	envs, err := i.getKanikoJobEnvs(req)
	if err != nil {
		return nil, fmt.Errorf("get envs failed, err: %v", err)
	}
	container := corev1.Container{
		Name:            "kaniko",
		Image:           KanikoImage,
		ImagePullPolicy: corev1.PullAlways,
		Command:         cmds,
		Env:             envs,
		VolumeMounts: []corev1.VolumeMount{
			{
				Name:      "kaniko-secret",
				MountPath: "/kaniko/.docker",
			},
		},
		Resources: corev1.ResourceRequirements{
			Limits: corev1.ResourceList{
				corev1.ResourceCPU:    resource.MustParse(BuildImageResourceLimitCPU),
				corev1.ResourceMemory: resource.MustParse(BuildImageResourceLimitMemory),
			},
			Requests: corev1.ResourceList{
				corev1.ResourceCPU:    resource.MustParse(BuildImageResourceRequestCPU),
				corev1.ResourceMemory: resource.MustParse(BuildImageResourceRequestMemory),
			},
		},
	}
	if req.SshKey != "" {
		container.VolumeMounts = append(container.VolumeMounts, corev1.VolumeMount{
			Name:      "secret-volume",
			MountPath: "/secret",
		})
	}
	return []corev1.Container{container}, nil
}

func (i *ImageHubService) getKanikoVolumes(ctx context.Context, req *imagehubv1.BuildImageRequest) ([]corev1.Volume, error) {
	volumes := []corev1.Volume{
		{
			Name: "kaniko-secret",
			VolumeSource: corev1.VolumeSource{
				Secret: &corev1.SecretVolumeSource{
					SecretName: HarborRegcred,
					Items: []corev1.KeyToPath{
						{
							Key:  ".dockerconfigjson",
							Path: "config.json",
						},
					},
				},
			},
		},
	}
	if req.SshKey != "" {
		//获取 sshkey
		sshKey, err := repository.GetSSHKey(ctx, i.Store, req.Creator, req.SshKey, req.Region)
		if err != nil {
			return nil, fmt.Errorf("get ssh key failed, err: %v", err)
		}
		private, err := util.AESDecrypt(sshKey.EncodePrivate, util.SshKeyPrivateKey)
		private += "\n" //添加文件结束符,避免secret挂载时缺少文件结束符导致ssh key校验不通过
		if err != nil {
			return nil, fmt.Errorf("decrypt private key failed, err: %v", err)
		}
		if private == "" {
			return nil, fmt.Errorf("private key is empty")
		}
		secretName := i.getSecretName(req)
		err = i.createSshKey(ctx, secretName, req.WorkspaceName, private)
		if err != nil {
			return nil, fmt.Errorf("create ssh key failed, err: %v", err)
		}
		volumes = append(volumes, corev1.Volume{
			Name: "secret-volume",
			VolumeSource: corev1.VolumeSource{
				Secret: &corev1.SecretVolumeSource{
					SecretName:  i.getSecretName(req),
					DefaultMode: util.Int32Ptr(0600),
					Items: []corev1.KeyToPath{
						{
							Key:  corev1.SSHAuthPrivateKey,
							Path: "id_rsa",
						},
					},
				},
			},
		})
	}
	return volumes, nil
}

func (i *ImageHubService) createSshKey(ctx context.Context, name, workspaceName, sshKeyPrivate string) error {
	secret := &corev1.Secret{}
	getSecretErr := multicluster.Instance().GetLocalCluster().Direct().Get(ctx, client.ObjectKey{Name: name, Namespace: getNamespaceName(workspaceName)}, secret)
	if getSecretErr != nil && !apierrors.IsNotFound(getSecretErr) {
		return getSecretErr
	}
	if getSecretErr != nil && apierrors.IsNotFound(getSecretErr) {
		secret = &corev1.Secret{
			ObjectMeta: metav1.ObjectMeta{
				Name:      name,
				Namespace: getNamespaceName(workspaceName),
			},
			Data: map[string][]byte{
				corev1.SSHAuthPrivateKey: []byte(sshKeyPrivate),
			},
			Type: corev1.SecretTypeSSHAuth,
		}
		err := multicluster.Instance().GetLocalCluster().Direct().Create(ctx, secret)
		if err != nil {
			return err
		}
	} else if string(secret.Data["id_rsa"]) != sshKeyPrivate {
		secret = &corev1.Secret{
			ObjectMeta: metav1.ObjectMeta{
				Name:      name,
				Namespace: getNamespaceName(workspaceName),
			},
			Data: map[string][]byte{
				corev1.SSHAuthPrivateKey: []byte(sshKeyPrivate),
			},
			Type: corev1.SecretTypeSSHAuth,
		}
		err := multicluster.Instance().GetLocalCluster().Direct().Update(ctx, secret)
		if err != nil {
			return err
		}
	}
	return nil
}

func (i *ImageHubService) createSecret(ctx context.Context, name string, workspaceName string, region string) error {
	secret := &corev1.Secret{}
	getSecretErr := multicluster.Instance().GetLocalCluster().Direct().Get(ctx, client.ObjectKey{Name: name, Namespace: getNamespaceName(workspaceName)}, secret)
	if getSecretErr != nil && !apierrors.IsNotFound(getSecretErr) {
		return getSecretErr
	}
	readWriteServiceAccountName := getTCRName(getReadWriteServiceAccountName(workspaceName))
	readWriteTCREntity := &model.TcrServiceAccountEntity{
		Name:   readWriteServiceAccountName,
		Region: region,
	}
	err := i.Store.Get(ctx, readWriteTCREntity)
	if err != nil {
		return err
	}
	registryPass := readWriteTCREntity.Password
	registryUser := readWriteTCREntity.Name
	auth := map[string]map[string]map[string]string{
		"auths": {
			"https://bj-harbor01.ke.com/v2/": {
				"username": registryUser,
				"password": registryPass,
				"auth":     base64.StdEncoding.EncodeToString([]byte(registryUser + ":" + registryPass)),
			},
		},
	}
	authJson, err := json.Marshal(auth)
	if err != nil {
		return err
	}
	if getSecretErr != nil && apierrors.IsNotFound(getSecretErr) {
		secret = &corev1.Secret{
			ObjectMeta: metav1.ObjectMeta{
				Name:      name,
				Namespace: getNamespaceName(workspaceName),
			},
			Data: map[string][]byte{
				".dockerconfigjson": authJson,
			},
			Type: corev1.SecretTypeDockerConfigJson,
		}
		err = multicluster.Instance().GetLocalCluster().Direct().Create(ctx, secret)
		if err != nil {
			return err
		}
	} else if string(secret.Data[".dockerconfigjson"]) != string(authJson) {
		secret = &corev1.Secret{
			ObjectMeta: metav1.ObjectMeta{
				Name:      name,
				Namespace: getNamespaceName(workspaceName),
			},
			Data: map[string][]byte{
				".dockerconfigjson": authJson,
			},
			Type: corev1.SecretTypeDockerConfigJson,
		}
		err = multicluster.Instance().GetLocalCluster().Direct().Update(ctx, secret)
		if err != nil {
			return err
		}
	}
	return nil
}

func getDockerFile(req *imagehubv1.BuildImageRequest) string {
	dockerfile := ""
	switch req.BuildType {
	case BuildTypeImage:
		switch req.BaseImage {
		case BaseImagePreset:
			dockerfile = fmt.Sprintf("FROM %s", req.PresetImage)
		case BaseImageCustom:
			dockerfile = fmt.Sprintf("FROM %s", req.CustomImage)
		case BaseImageURL:
			dockerfile = fmt.Sprintf("FROM %s", req.ImageURL)
		default:
			return ""
		}
	case BuildTypeDockerfile:
		switch req.DockerfileSource {
		case DockerfileSourceContent:
			dockerfile = req.Content
		default:
			return ""
		}
	}
	if req.Run != "" {
		runCmds := strings.Split(req.Run, "\n")
		runCmds = filterEmpty(runCmds)
		runCmd := strings.Join(runCmds, " && ")
		dockerfile += fmt.Sprintf("\nRUN %s", runCmd)
	}
	if req.Cmd != "" {
		cmds := strings.Split(req.Cmd, "\n")
		cmds = filterEmpty(cmds)
		cmd := strings.Join(cmds, " \", \" ")
		dockerfile += fmt.Sprintf("\nCMD [ \"%s\" ]", cmd)
	}

	return dockerfile
}
func filterEmpty(arr []string) []string {
	var res []string
	for _, v := range arr {
		if v != "" {
			res = append(res, v)
		}
	}
	return res
}

// createTempFile 创建一个临时文件并写入内容
func createTempFile(name, content string) (*os.File, error) {
	tempFile, err := os.CreateTemp("", name)
	if err != nil {
		return nil, fmt.Errorf("创建临时文件失败: %v", err)
	}

	if _, err := tempFile.Write([]byte(content)); err != nil {
		tempFile.Close()
		os.Remove(tempFile.Name())
		return nil, fmt.Errorf("写入临时文件失败: %v", err)
	}

	if _, err := tempFile.Seek(0, 0); err != nil {
		tempFile.Close()
		os.Remove(tempFile.Name())
		return nil, fmt.Errorf("重置文件指针失败: %v", err)
	}

	return tempFile, nil
}

// createTarGz 创建一个 .tar.gz 文件并将临时文件添加到其中
func createTarGz(tarGzFilename string, file *os.File) (*os.File, error) {
	// 创建 .tar.gz 文件
	tarGzFile, err := os.CreateTemp("", tarGzFilename)
	if err != nil {
		return nil, fmt.Errorf("创建 .tar.gz 文件失败: %v", err)
	}

	// 创建 gzip writer
	gzipWriter := gzip.NewWriter(tarGzFile)
	defer gzipWriter.Close()

	// 创建 tar writer
	tarWriter := tar.NewWriter(gzipWriter)
	defer tarWriter.Close()

	// 将每个文件添加到 tar
	fileInfo, err := file.Stat()
	if err != nil {
		tarGzFile.Close()
		os.Remove(tarGzFile.Name())
		return nil, fmt.Errorf("获取文件信息失败: %v", err)
	}

	// 创建 tar 头部
	header := &tar.Header{
		Name: fileInfo.Name(),
		Size: fileInfo.Size(),
		Mode: 0600,
	}
	if err := tarWriter.WriteHeader(header); err != nil {
		tarGzFile.Close()
		os.Remove(tarGzFile.Name())
		return nil, fmt.Errorf("写入 tar 头部失败: %v", err)
	}

	// 写入文件内容
	if _, err := io.Copy(tarWriter, file); err != nil {
		tarGzFile.Close()
		os.Remove(tarGzFile.Name())
		return nil, fmt.Errorf("写入文件内容失败: %v", err)
	}
	return tarGzFile, nil
}

// CreateImageBase 创建镜像
func (i *ImageHubService) CreateImageBase(ctx context.Context, req *imagehubv1.CreateImageBaseRequest) (*imagehubv1.CreateImageBaseResponse, error) {
	creator, ok := ctx.Value(constant.UserCtxKey).(string)
	if !ok {
		return nil, bcode.ErrorServerInternalError("get user from context failed")
	}

	//权限校验

	//重复校验
	imageBaseEntity := &model.ImageBaseEntity{
		WorkspaceName: req.WorkspaceName,
		Region:        req.Region,
		Name:          req.Name,
	}
	cnt, err := i.Store.Count(ctx, imageBaseEntity, nil)
	if err != nil {
		return nil, fmt.Errorf("count image base failed, err: %v", err)
	}

	if cnt > 0 {
		return &imagehubv1.CreateImageBaseResponse{
			Msg:    "同名镜像已存在,请更换镜像名后重试",
			Status: 1,
		}, nil
	}

	//创建镜像
	members := req.Members
	if !util.StringInSlice(creator, members) {
		members = append(members, creator)
	}
	managers := req.Managers
	if !util.StringInSlice(creator, managers) {
		managers = append(managers, creator)
	}
	image := &model.ImageBaseEntity{
		ID:            primitive.NewObjectID(),
		WorkspaceName: req.WorkspaceName,
		Region:        req.Region,
		Name:          req.Name,
		Description:   req.Description,
		Members:       members,
		Managers:      managers,
		Creator:       creator,
	}
	err = i.Store.Add(ctx, image)
	if err != nil {
		return nil, fmt.Errorf("add image base failed, err: %v", err)
	}
	return nil, nil
}

// UpdateImageBase 更新镜像基础信息
func (i *ImageHubService) UpdateImageBase(ctx context.Context, req *imagehubv1.UpdateImageBaseRequest) (*imagehubv1.UpdateImageBaseResponse, error) {
	creator, ok := ctx.Value(constant.UserCtxKey).(string)
	if !ok {
		return nil, bcode.ErrorServerInternalError("get user from context failed")
	}

	//权限校验

	ID, err := primitive.ObjectIDFromHex(req.ID)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("invalid image id, err: %v", err)
	}
	imageBaseEntity := &model.ImageBaseEntity{
		ID: ID,
	}
	if err := i.Store.Get(ctx, imageBaseEntity); err != nil {
		return nil, bcode.ErrorServerInternalError("get image base failed, err: %v", err)
	}

	//更新镜像
	members := req.Members
	if !util.StringInSlice(creator, members) {
		members = append(members, creator)
	}
	imageBaseEntity.Members = members

	managers := req.Managers
	if !util.StringInSlice(creator, managers) {
		managers = append(managers, creator)
	}
	imageBaseEntity.Managers = managers
	imageBaseEntity.Description = req.Description

	err = i.Store.Put(ctx, imageBaseEntity)
	if err != nil {
		return nil, fmt.Errorf("update image base failed, err: %v", err)
	}
	return nil, nil
}

// DeleteImageBase 删除镜像基础信息
func (i *ImageHubService) DeleteImageBase(ctx context.Context, req *imagehubv1.DeleteImageBaseRequest) (*imagehubv1.DeleteImageBaseResponse, error) {
	//权限校验

	ID, err := primitive.ObjectIDFromHex(req.ID)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("invalid image id, err: %v", err)
	}
	imageBaseEntity := &model.ImageBaseEntity{
		ID: ID,
	}
	if err := i.Store.Get(ctx, imageBaseEntity); err != nil {
		return nil, bcode.ErrorServerInternalError("get image base failed, err: %v", err)
	}
	//校验镜像是否存在生效tag
	imageHubEntity := &model.ImageHubEntity{
		WorkspaceName: imageBaseEntity.WorkspaceName,
		Region:        imageBaseEntity.Region,
		Name:          imageBaseEntity.Name,
	}
	cnt, err := i.Store.Count(ctx, imageHubEntity, nil)
	if err != nil {
		return nil, fmt.Errorf("count image hub failed, err: %v", err)
	}
	if cnt > 0 {
		return &imagehubv1.DeleteImageBaseResponse{
			Msg:    "删除失败:镜像存在生效tag,请删除tag后重试",
			Status: 1,
		}, nil
	}

	//删除镜像
	err = i.Store.Delete(ctx, imageBaseEntity)
	if err != nil {
		return nil, fmt.Errorf("delete image base failed, err: %v", err)
	}
	return nil, nil
}

// ListImageBase 展示镜像
func (i *ImageHubService) ListImageBase(ctx context.Context, req *imagehubv1.ListImageBaseRequest) (*imagehubv1.ListImageBaseResponse, error) {
	resp := &imagehubv1.ListImageBaseResponse{
		Total:  0,
		Images: []*imagehubv1.ImageBase{},
	}
	//权限校验

	//展示镜像
	var filterOptions datastore.FilterOptions
	if req.WorkspaceName != "" {
		filterOptions.In = append(filterOptions.In, datastore.InQueryOption{
			Key:    "workspaceName",
			Values: []string{req.WorkspaceName},
		})
	}
	if req.Region != "" {
		filterOptions.In = append(filterOptions.In, datastore.InQueryOption{
			Key:    "region",
			Values: []string{req.Region},
		})
	}
	if req.Name != "" {
		filterOptions.Queries = append(filterOptions.Queries, datastore.FuzzyQueryOption{
			Key:   "name",
			Query: req.Name,
		})
	}
	if req.SpecificName != "" {
		filterOptions.In = append(filterOptions.In, datastore.InQueryOption{
			Key:    "name",
			Values: []string{req.SpecificName},
		})
	}
	if req.Creator != "" {
		filterOptions.In = append(filterOptions.In, datastore.InQueryOption{
			Key:    "creator",
			Values: []string{req.Creator},
		})
	}
	cnt, err := i.Store.Count(ctx, &model.ImageBaseEntity{}, &filterOptions)
	if err != nil {
		return nil, fmt.Errorf("count image base failed, err: %v", err)
	}
	if cnt == 0 {
		return resp, nil
	}
	options := &datastore.ListOptions{
		FilterOptions: filterOptions,
		Page:          int(req.Page),
		PageSize:      int(req.PerPage),
		SortBy: []datastore.SortOption{
			{
				Key:   "createTime",
				Order: datastore.SortOrderDescending,
			},
		},
	}
	imageList, err := i.Store.List(ctx, &model.ImageBaseEntity{}, options)
	if err != nil {
		return nil, fmt.Errorf("list image base failed, err: %v", err)
	}
	for _, v := range imageList {
		image := v.(*model.ImageBaseEntity)
		resp.Images = append(resp.Images, &imagehubv1.ImageBase{
			ID:            image.ID.Hex(),
			WorkspaceName: image.WorkspaceName,
			Region:        image.Region,
			Name:          image.Name,
			Description:   image.Description,
			Members:       image.Members,
			Managers:      image.Managers,
			Creator:       image.Creator,
			CreateTime:    util.TimeFormat(image.CreateTime),
		})
	}
	resp.Total = int32(cnt)
	return resp, nil
}

// ListImageBuildHistory 展示镜像构建历史
func (i *ImageHubService) ListImageBuildHistory(ctx context.Context, req *imagehubv1.ListImageBuildHistoryRequest) (*imagehubv1.ListImageBuildHistoryResponse, error) {
	resp := &imagehubv1.ListImageBuildHistoryResponse{
		Total:               0,
		ImageBuildHistories: []*imagehubv1.ImageBuildHistory{},
	}
	//权限校验

	//展示镜像
	var filterOptions datastore.FilterOptions
	if req.WorkspaceName != "" {
		filterOptions.In = append(filterOptions.In, datastore.InQueryOption{
			Key:    "workspaceName",
			Values: []string{req.WorkspaceName},
		})
	}
	if req.Region != "" {
		filterOptions.In = append(filterOptions.In, datastore.InQueryOption{
			Key:    "region",
			Values: []string{req.Region},
		})
	}
	if req.Status != "" {
		filterOptions.In = append(filterOptions.In, datastore.InQueryOption{
			Key:    "status",
			Values: []string{req.Status},
		})
	}
	if req.Name != "" {
		filterOptions.In = append(filterOptions.In, datastore.InQueryOption{
			Key:    "name",
			Values: []string{req.Name},
		})
	}
	if req.Tag != "" {
		filterOptions.In = append(filterOptions.In, datastore.InQueryOption{
			Key:    "tag",
			Values: []string{req.Tag},
		})
	}
	if req.JobName != "" {
		filterOptions.In = append(filterOptions.In, datastore.InQueryOption{
			Key:    "jobName",
			Values: []string{req.JobName},
		})
	}
	if req.Creator != "" {
		filterOptions.Queries = append(filterOptions.Queries, datastore.FuzzyQueryOption{
			Key:   "creator",
			Query: req.Creator,
		})
	}
	cnt, err := i.Store.Count(ctx, &model.ImageBuildHistoryEntity{}, &filterOptions)
	if err != nil {
		return nil, fmt.Errorf("count image base failed, err: %v", err)
	}
	if cnt == 0 {
		return resp, nil
	}
	options := &datastore.ListOptions{
		FilterOptions: filterOptions,
		Page:          int(req.Page),
		PageSize:      int(req.PerPage),
		SortBy: []datastore.SortOption{
			{
				Key:   "createtime",
				Order: datastore.SortOrder(-1),
			},
		},
	}
	buildList, err := i.Store.List(ctx, &model.ImageBuildHistoryEntity{}, options)
	if err != nil {
		return nil, fmt.Errorf("list image base failed, err: %v", err)
	}
	for _, v := range buildList {
		image := v.(*model.ImageBuildHistoryEntity)
		buildReq := &imagehubv1.BuildImageRequest{}
		_ = json.Unmarshal([]byte(image.BuildRequest), buildReq)
		buildTime := ""
		if !image.StartTime.IsZero() && image.EndTime.IsZero() {
			buildTime = time.Since(image.StartTime).String()
		} else if !image.StartTime.IsZero() && !image.EndTime.IsZero() {
			buildTime = image.EndTime.Sub(image.StartTime).String()
		}
		startTime := ""
		logFrom := int64(0)
		if !image.StartTime.IsZero() {
			startTime = util.TimeFormat(image.StartTime)
			logFrom = image.StartTime.UnixMilli()
		}
		endTime := ""
		logTo := int64(0)
		if !image.EndTime.IsZero() {
			endTime = util.TimeFormat(image.EndTime)
			logTo = image.EndTime.UnixMilli()
		}

		fullPath := ""
		if image.Status == BuildStatusSucceeded {
			fullPath = fmt.Sprintf("bj-harbor01.ke.com/aistudio-%s/%s:%s", image.WorkspaceName, image.Name, image.Tag)
		}
		resp.ImageBuildHistories = append(resp.ImageBuildHistories, &imagehubv1.ImageBuildHistory{
			Name:             image.Name,
			Tag:              image.Tag,
			QueueName:        buildReq.QueueName,
			Description:      buildReq.Description,
			WorkspaceName:    image.WorkspaceName,
			Region:           image.Region,
			BuildType:        buildReq.BuildType,
			BaseImage:        buildReq.BaseImage,
			DockerfileSource: buildReq.DockerfileSource,
			PresetImage:      buildReq.PresetImage,
			CustomImage:      buildReq.CustomImage,
			ImageURL:         buildReq.ImageURL,
			Run:              buildReq.Run,
			Cmd:              buildReq.Cmd,
			GitUrl:           buildReq.GitUrl,
			GitBranch:        buildReq.GitBranch,
			Content:          buildReq.Content,
			DevID:            buildReq.DevID,
			ExcludePath:      buildReq.ExcludePath,
			GitDir:           buildReq.GitDir,
			Status:           image.Status,
			Reason:           image.Reason,
			Creator:          image.Creator,
			CreateTime:       util.TimeFormat(image.CreateTime),
			JobName:          image.JobName,
			PodName:          image.PodName,
			StartTime:        startTime,
			EndTime:          endTime,
			BuildTime:        buildTime,
			FullPath:         fullPath,
			Cluster:          image.Cluster,
			LogFrom:          logFrom,
			LogTo:            logTo,
			Msg:              image.Message,
			Conditions:       ConvertBuildConditionsToConditions(image.Conditions),
			BuildArg:         buildReq.BuildArg,
			SshKey:           buildReq.SshKey,
			UseCache:         buildReq.UseCache,
			UseSpeedUp:       buildReq.UseSpeedUp,
		})
	}
	resp.Total = int32(cnt)
	return resp, nil
}
func ConvertBuildConditionsToConditions(conditions model.Conditions) []*imagehubv1.ImageCondition {
	v1Conditions := make([]*imagehubv1.ImageCondition, 0)
	for _, condition := range conditions {
		v1Conditions = append(v1Conditions, &imagehubv1.ImageCondition{
			Type:               condition.Type,
			Status:             string(condition.Status),
			LastTransitionTime: util.TimeFormat(condition.LastTransitionTime.Time),
			Reason:             condition.Reason,
			Message:            condition.Message,
		})
	}
	return v1Conditions
}

// StopBuildImage 停止构建
func (i *ImageHubService) StopBuildImage(ctx context.Context, req *imagehubv1.StopBuildImageRequest) (*imagehubv1.StopBuildImageResponse, error) {
	//找到构建任务
	buildImageHistoryEntity := &model.ImageBuildHistoryEntity{
		JobName:       req.JobName,
		WorkspaceName: req.WorkspaceName,
		Region:        req.Region,
	}
	err := i.Store.Get(ctx, buildImageHistoryEntity)
	if err != nil {
		return nil, fmt.Errorf("get image build history failed, err:%v", err)
	}
	var buildJob batchv1.Job
	clusterName := buildImageHistoryEntity.Cluster
	var cluster multicluster.ClusterInterface
	if clusterName == LocalCluster {
		cluster = multicluster.Instance().GetLocalCluster()
	} else {
		cluster = multicluster.Instance().GetCluster(clusterName)
	}
	err = cluster.Direct().Get(ctx, client.ObjectKey{Name: req.JobName, Namespace: getNamespaceName(req.WorkspaceName)}, &buildJob)
	if err != nil && !apierrors.IsNotFound(err) {
		return nil, fmt.Errorf("get build job failed, err: %v", err)
	}
	if apierrors.IsNotFound(err) {
		buildImageHistoryEntity.Status = BuildStatusAborted
		buildImageHistoryEntity.Reason = getImageReason(buildImageHistoryEntity.Status, nil)
		buildImageHistoryEntity.Message = getImageMsg(buildImageHistoryEntity.Status, nil)
		buildImageHistoryEntity.UpdateTime = time.Now()
		buildImageHistoryEntity.Conditions.UpsertCondition(GetImageCondition(buildImageHistoryEntity.Status, nil))
		if !buildImageHistoryEntity.StartTime.IsZero() {
			buildImageHistoryEntity.EndTime = time.Now()
		}
	} else {
		// 设置删除选项
		gracePeriodSeconds := int64(0)
		propagationPolicy := metav1.DeletePropagationForeground
		deleteOptions := client.DeleteOptions{
			GracePeriodSeconds: &gracePeriodSeconds,
			PropagationPolicy:  &propagationPolicy,
		}
		err = cluster.Direct().Delete(ctx, &buildJob, &deleteOptions)
		if err != nil {
			return nil, fmt.Errorf("delete build job failed, err: %v", err)
		}
		buildImageHistoryEntity.Status = BuildStatusAborting
		buildImageHistoryEntity.Reason = getImageReason(buildImageHistoryEntity.Status, nil)
		buildImageHistoryEntity.Message = getImageMsg(buildImageHistoryEntity.Status, nil)
		buildImageHistoryEntity.UpdateTime = time.Now()
		buildImageHistoryEntity.Conditions.UpsertCondition(GetImageCondition(buildImageHistoryEntity.Status, nil))
		if !buildImageHistoryEntity.StartTime.IsZero() {
			buildImageHistoryEntity.EndTime = time.Now()
		}
	}

	err = i.Store.Put(ctx, buildImageHistoryEntity)
	if err != nil {
		return nil, fmt.Errorf("update image build history failed, err: %v", err)
	}
	return nil, nil
}

// CheckImageTag 查看tag是否存在
func (i *ImageHubService) CheckImageTag(ctx context.Context, req *imagehubv1.CheckImageTagRequest) (*imagehubv1.CheckImageTagResponse, error) {
	if req.Tag == "" {
		return &imagehubv1.CheckImageTagResponse{}, nil
	}
	filter := datastore.FilterOptions{}
	if req.WorkspaceName != "" {
		filter.In = append(filter.In, datastore.InQueryOption{
			Key:    "workspaceName",
			Values: []string{req.WorkspaceName},
		})
	}
	if req.Region != "" {
		filter.In = append(filter.In, datastore.InQueryOption{
			Key:    "region",
			Values: []string{req.Region},
		})
	}
	if req.Name != "" {
		filter.In = append(filter.In, datastore.InQueryOption{
			Key:    "name",
			Values: []string{req.Name},
		})
	}
	if req.Tag != "" {
		filter.In = append(filter.In, datastore.InQueryOption{
			Key:    "tag",
			Values: []string{req.Tag},
		})
	}
	cnt, err := i.Store.Count(ctx, &model.ImageHubEntity{}, &filter)
	if err != nil {
		return nil, fmt.Errorf("count image hub failed, err: %v", err)
	}
	if cnt > 0 {
		return &imagehubv1.CheckImageTagResponse{
			Code: 1,
		}, nil
	}
	return &imagehubv1.CheckImageTagResponse{}, nil
}

// IsBuildingDevImage 查看是否正在构建开发机镜像
func (i *ImageHubService) IsBuildingDevImage(ctx context.Context, workspaceName, region, devName string) (bool, error) {
	filter := datastore.FilterOptions{
		In: []datastore.InQueryOption{
			{
				Key:    "workspaceName",
				Values: []string{workspaceName},
			},
			{
				Key:    "region",
				Values: []string{region},
			},
			{
				Key:    "status",
				Values: []string{BuildStatusWaiting, BuildStatusPending, BuildStatusRunning},
			},
		},
		Queries: []datastore.FuzzyQueryOption{
			{
				Key:   "buildrequest",
				Query: devName,
			},
		},
	}
	cnt, err := i.Store.Count(ctx, &model.ImageBuildHistoryEntity{}, &filter)
	if err != nil {
		klog.Errorf("count image build history failed, err: %v", err)
		return false, fmt.Errorf("count image build history failed, err: %v", err)
	}
	if cnt > 0 {
		return true, nil
	}

	return false, nil
}
