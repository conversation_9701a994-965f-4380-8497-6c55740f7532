package service

import (
	"context"
	"encoding/base64"
	"errors"
	"fmt"
	"sort"

	"git.lianjia.com/cloudnative/kic/kic-platform/aistudio/pkg/server/domain/repository"
	"git.lianjia.com/cloudnative/kic/kic-platform/aistudio/pkg/server/domain/service/helper"
	resourcegroupv1 "git.lianjia.com/cloudnative/kic/kic-platform/apis/aistudio/resourcegroup/v1"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"k8s.io/client-go/util/retry"

	"git.lianjia.com/cloudnative/kic/kic-platform/aistudio/pkg/constant"
	"git.lianjia.com/cloudnative/kic/kic-platform/aistudio/pkg/server/domain/model"
	"git.lianjia.com/cloudnative/kic/kic-platform/aistudio/pkg/server/event"
	"git.lianjia.com/cloudnative/kic/kic-platform/apis/aistudio/bcode"
	cubefsv1 "git.lianjia.com/cloudnative/kic/kic-platform/apis/aistudio/cubefs/v1"
	rbacv1 "git.lianjia.com/cloudnative/kic/kic-platform/apis/aistudio/rbac/v1"
	v1 "git.lianjia.com/cloudnative/kic/kic-platform/apis/aistudio/workspace/v1"
	"git.lianjia.com/cloudnative/kic/kic-platform/apis/common"
	eventv1 "git.lianjia.com/cloudnative/kic/kic-platform/apis/eventhub/v1"
	userv1 "git.lianjia.com/cloudnative/kic/kic-platform/apis/usercenter/v1"
	cloudfsv1alpha1 "git.lianjia.com/cloudnative/kic/kic-platform/pkg/apis/cloudfs.kcs.io/v1alpha1"
	schedulingv1alpha1 "git.lianjia.com/cloudnative/kic/kic-platform/pkg/apis/scheduling.kcs.io/v1alpha1"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/util"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/watcher"
	kratoslog "github.com/go-kratos/kratos/v2/log"

	"strings"
	"sync"
	"time"

	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/infrastructure/datastore"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/multicluster"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/property"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/pubsub"
	"github.com/openshift/library-go/pkg/verify/store"
	"google.golang.org/protobuf/types/known/emptypb"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/klog/v2"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/manager"
)

var _ v1.WorkspaceServiceHTTPServer = (*WorkspaceService)(nil)

var _ Interface = (*WorkspaceService)(nil)

const (
	DefaultTensorboardVolumeRegion = "region-bj"
	DefaultTensorboardVolumeZone   = "wq"
)

type WorkspaceService struct {
	RbacService          *RbacService                 `inject:""`
	UserService          *UserService                 `inject:""`
	ImageHubService      *ImageHubService             `inject:""`
	ModelHubService      *ModelHubService             `inject:""`
	Store                datastore.DataStore          `inject:"datastore"`
	EventBridge          *event.Bridge                `inject:""`
	Properties           property.EnvironmentProperty `inject:""`
	ClusterManager       *ClusterManager              `inject:""`
	lock                 sync.Mutex
	Manager              manager.Manager       `inject:""`
	ResourceGroupService *ResourceGroupService `inject:""`
}

func (w *WorkspaceService) ListClusterBindings(ctx context.Context, request *v1.ListClusterBindingRequest) (*v1.ListClusterBindingsResult, error) {
	workspaceName := request.WorkspaceName
	var workspaceClusterBindingList = &schedulingv1alpha1.WorkspaceClusterBindingList{}
	err := multicluster.Instance().GetLocalCluster().Direct().List(ctx, workspaceClusterBindingList, client.MatchingLabels{
		constant.FederationWorkspaceLabelKey: workspaceName,
	})
	if err != nil {
		return nil, err
	}
	clusterBindings := make([]*v1.ClusterBinding, 0)
	for _, binding := range workspaceClusterBindingList.Items {
		clusterBinding := &v1.ClusterBinding{
			Cluster:       binding.Spec.TargetCluster,
			Namespace:     binding.Spec.TargetNamespace,
			Ready:         binding.Status.State == schedulingv1alpha1.WorkspaceClusterBindingStateSynced,
			WorkspaceName: workspaceName,
		}
		clusterImport := &schedulingv1alpha1.ClusterImport{}
		err = multicluster.Instance().GetLocalCluster().Direct().Get(ctx, client.ObjectKey{
			Name: binding.Spec.TargetCluster,
		}, clusterImport)
		if err != nil {
			klog.Errorf("failed to get cluster import %s: %v", binding.Spec.TargetCluster, err)
		} else {
			clusterBinding.ClusterRegion = clusterImport.Spec.Region
			clusterBinding.ClusterZone = clusterImport.Spec.Zone
			clusterBinding.ClusterIDC = clusterImport.Spec.IDC
		}
		clusterBindings = append(clusterBindings, clusterBinding)
	}
	return &v1.ListClusterBindingsResult{ClusterBindings: clusterBindings}, nil
}

func (w *WorkspaceService) ContainsClusterGateway(ctx context.Context, clusterName string) (bool, error) {
	gateways, err := multicluster.Instance().ListClusterGateways(ctx)
	if err != nil {
		return false, err
	}
	for _, gateway := range gateways {
		if gateway.Name == clusterName {
			return true, nil
		}
	}
	return false, nil
}

func (w *WorkspaceService) SetClusterBindings(ctx context.Context, request *v1.CreateClusterBindingsRequest) (*emptypb.Empty, error) {
	for _, c := range request.Clusters {
		ok, err := w.ContainsClusterGateway(ctx, c)
		if err != nil {
			return nil, err
		}
		if !ok {
			return nil, bcode.ErrorKubernetesClusterNotFound("kubernetes cluster[%s] not found", c)
		}
	}
	workspace, err := w.GetWorkspaceBase(ctx, &v1.GetWorkspaceBaseRequest{
		Name: request.WorkspaceName,
	})
	if err != nil {
		return nil, err
	}
	for _, c := range request.Clusters {
		clusterBinding := &model.ClusterBindingEntity{
			Workspace: workspace.Name,
			Cluster:   c,
			ID:        primitive.NewObjectID(),
		}
		exist, err := repository.WorkspaceClusterBindingExists(ctx, w.Store, workspace.Name, c)
		if err != nil {
			return nil, bcode.ErrorServerInternalError("check workspace cluster binding[%s:%s]  error", workspace.Name, c)
		}
		if !exist {
			err = w.Store.Add(ctx, clusterBinding)
			if err != nil {
				return nil, err
			}
		}
	}
	err = w.RefreshWorkspaceClusterBindings(ctx, workspace)
	if err != nil {
		return nil, err
	}
	return &emptypb.Empty{}, nil
}

// RefreshWorkspaceClusterBindings 该方法会刷新工作空间的集群绑定信息, 如果新增了集群或者删除了集群, 会自动更新工作空间的集群绑定信息
func (w *WorkspaceService) RefreshWorkspaceClusterBindings(ctx context.Context, workspaceBase *v1.WorkspaceBase) error {
	items, err := w.Store.List(ctx, &model.ClusterBindingEntity{
		Workspace: workspaceBase.Name,
	}, &datastore.ListOptions{})
	if err != nil {
		return err
	}
	var clusters []schedulingv1alpha1.ClusterRef
	for _, item := range items {
		clusterBinding := item.(*model.ClusterBindingEntity)
		listNodeResult, err := w.ResourceGroupService.ListNodes(ctx, &resourcegroupv1.ListNodeDetailOptions{
			WorkspaceName: workspaceBase.Name,
			Cluster:       clusterBinding.Cluster,
		})
		if err != nil {
			return err
		}
		var nodeNames []string
		for _, node := range listNodeResult.Nodes {
			nodeNames = append(nodeNames, node.NodeName)
		}
		clusterImport := &schedulingv1alpha1.ClusterImport{}
		err = multicluster.Instance().GetLocalCluster().Direct().Get(ctx, client.ObjectKey{Name: clusterBinding.Cluster}, clusterImport)
		if err != nil {
			return err
		}
		clusters = append(clusters, schedulingv1alpha1.ClusterRef{
			ClusterName: clusterBinding.Cluster,
			NodeNames:   nodeNames,
			Region:      clusterImport.Spec.Region,
			IDC:         clusterImport.Spec.IDC,
			Zone:        clusterImport.Spec.Zone,
		})
	}
	// 更新工作空间 Robot 集群信息
	result, err := w.UserService.ListUsers(ctx, &userv1.ListOptions{
		WorkspaceName: workspaceBase.Name,
		UserType:      RobotUserType,
		IsDefault:     "true",
	})
	if err != nil {
		return err
	}
	if result == nil || result.Total == 0 {
		return bcode.ErrorServerInternalError("robot user not found")
	}
	robotUser := result.Users[0]
	return retry.RetryOnConflict(retry.DefaultRetry, func() error {
		var workspaceCR = &schedulingv1alpha1.Workspace{}
		err = multicluster.Instance().GetLocalCluster().Direct().Get(ctx, client.ObjectKey{
			Name: workspaceBase.Name,
		}, workspaceCR)
		if err != nil {
			return err
		}
		workspaceCR.Spec.Clusters = clusters
		workspaceCR.Spec.RobotAccount = schedulingv1alpha1.WorkspaceRobotAccount{
			Account:  robotUser.Account,
			Password: base64.StdEncoding.EncodeToString([]byte(robotUser.RobotAttributes.Password)),
		}
		return multicluster.Instance().GetLocalCluster().Direct().Update(ctx, workspaceCR)
	})

}

func (w *WorkspaceService) initTensorboardVolume(ctx context.Context, workspace *model.WorkspaceBaseEntity) error {
	//创建Tensorboard存储卷
	regions := w.Properties.GetStringSlice("region")
	for _, region := range regions {
		if w.Properties.IsSet(fmt.Sprintf("tensorboard.%s.enabled", region)) && w.Properties.GetBoolDefault(fmt.Sprintf("tensorboard.%s.enabled", region), false) {
			engine, err := w.Properties.GetString(fmt.Sprintf("tensorboard.%s.engine", region))
			if err != nil {
				return err
			}
			klog.Infof("initTensorboardVolume: region=%s, engine=%s", region, engine)
			switch engine {
			case "cubefs":
				tensorboardCubeFSVolume := cloudfsv1alpha1.CubeFSVolume{
					ObjectMeta: metav1.ObjectMeta{
						Name:      "tensorboard",
						Namespace: getNamespaceName(workspace.Name),
					},
					Spec: cloudfsv1alpha1.CubeFSVolumeSpec{
						DisplayName:   fmt.Sprintf("[%s]Tensorboard存储卷", workspace.Name),
						Description:   fmt.Sprintf("[%s]Tensorboard存储卷, 用于开启了TensorBoard功能的存储训练任务训练过程中产生的可观测数据", workspace.Name),
						Region:        region,
						Zone:          w.Properties.GetDefault(fmt.Sprintf("tensorboard.%s.zone", region), DefaultTensorboardVolumeZone),
						Capacity:      50,
						Creator:       "kic-admin",
						VolumeBinding: cloudfsv1alpha1.VolumeBinding{},
						Specification: strings.ToLower(cubefsv1.VolumeSpecification_Powerful.String()),
					},
				}
				err := multicluster.Instance().GetLocalCluster().Direct().Get(ctx, client.ObjectKey{Namespace: getNamespaceName(workspace.Name), Name: "tensorboard"}, &tensorboardCubeFSVolume)
				if err != nil {
					if apierrors.IsNotFound(err) {
						err = multicluster.Instance().GetLocalCluster().Direct().Create(ctx, &tensorboardCubeFSVolume)
						if err != nil {
							klog.Errorf("create tensorboard volume failed, err: %v", err)
							return err
						}
						klog.Infof("create tensorboard volume success, volume: %v", tensorboardCubeFSVolume)
					} else {
						klog.Errorf("get tensorboard volume failed, err: %v", err)
						return err
					}
				}
				klog.Infof("tensorboard volume already exists, volume: %v", tensorboardCubeFSVolume)
			default:
				return fmt.Errorf("unsupported tensorboard engine %s", engine)
			}
		}
	}
	return nil
}

func (w *WorkspaceService) Init(ctx context.Context, properties property.EnvironmentProperty) error {
	w.OnWorkspaceCreatedEventSinkToVela(ctx)
	w.EventBridge.Subscribe(ctx, pubsub.In("workspace-creator-update-matcher", []string{constant.WorkspaceCreatedEvent.Name,
		constant.WorkspaceMetadataUpdateEvent.Name, constant.WorkspaceMemberCreatedEvent.Name, constant.WorkspaceMemberUpdatedEvent.Name,
		constant.WorkspaceMemberDeletedEvent.Name,
	}), func(eventObj *eventv1.Event) {
		if eventObj.Type == eventv1.Event_NORMAL && eventObj.Reason == constant.Success {
			if eventObj.EventObject != nil && eventObj.EventObject.Kind == constant.EventObjectKindWorkspace {
				newEvent := eventObj.DeepCopy()
				workspaceName := eventObj.EventObject.Name
				switch eventObj.Name {
				case constant.WorkspaceCreatedEvent.Name:
					klog.Infof("workspace created event received, workspace name: %s", workspaceName)
					//todo nothing
				case constant.WorkspaceMetadataUpdateEvent.Name:
					klog.Infof("workspace metadata update event received, workspace name: %s", workspaceName)
					displayName := eventObj.Attributes["displayName"]
					description := eventObj.Attributes["description"]
					// 更新vela
					if err := helper.UpdateVelaProject(ctx, workspaceName, displayName, description); err != nil {
						newEvent.Type = eventv1.Event_ERROR
						newEvent.Reason = constant.WorkspaceUpdateVelaProjectFailed
						newEvent.Message = fmt.Sprintf("update vela project failed, err: %v", err)
						w.EventBridge.Publish(newEvent)
					}
					addUsers := eventObj.Attributes["addUsers"]
					addUsersString := splitString(addUsers)
					klog.Infof("add users: %v", addUsersString)
					if len(addUsersString) > 0 {
						if err := helper.AddAdminUsersIntoVelaProject(ctx, workspaceName, addUsersString); err != nil {
							newEvent.Type = eventv1.Event_ERROR
							newEvent.Reason = constant.WorkspaceAddUserIntoVelaProjectFailed
							newEvent.Message = fmt.Sprintf("add users into vela project failed, err: %v", err)
							w.EventBridge.Publish(newEvent)
						}
					}
					del := eventObj.Attributes["delUsers"]
					delUsers := splitString(del)
					klog.Infof("delUsers: %v", delUsers)
					if len(delUsers) > 0 {
						if err := helper.RemoveUsersFromVelaProject(ctx, workspaceName, delUsers); err != nil {
							newEvent.Type = eventv1.Event_ERROR
							newEvent.Reason = constant.WorkspaceRemoveUserIntoVelaProjectFailed
							newEvent.Message = fmt.Sprintf("remove users from vela project failed, err: %v", err)
							w.EventBridge.Publish(newEvent)
						}
					}

					newEvent.Type = eventv1.Event_NORMAL
					newEvent.Reason = constant.WorkspaceUpdateVelaProjectSuccess
					newEvent.Message = fmt.Sprintf("update vela project success, workspace name: %s", workspaceName)
					w.EventBridge.Publish(newEvent)

				case constant.WorkspaceMemberCreatedEvent.Name:
					klog.Infof("workspace member created event received, workspace name: %s ", workspaceName)
					members := eventObj.Attributes["addUsers"]
					users := splitString(members)
					klog.Infof("add users into vela project, users: %v", users)
					if len(users) > 0 {
						if err := helper.AddAdminUsersIntoVelaProject(ctx, workspaceName, users); err != nil {
							newEvent.Type = eventv1.Event_ERROR
							newEvent.Reason = constant.WorkspaceAddUserIntoVelaProjectFailed
							newEvent.Message = fmt.Sprintf("add admin users into vela project failed, err: %v", err)
							w.EventBridge.Publish(newEvent)
						}
					}
					newEvent.Type = eventv1.Event_NORMAL
					newEvent.Reason = constant.WorkspaceAddUserIntoVelaProjectSuccess
					newEvent.Message = fmt.Sprintf("add users into vela project success, workspace name: %s", workspaceName)
					w.EventBridge.Publish(newEvent)

				case constant.WorkspaceMemberDeletedEvent.Name:
					klog.Info("workspace member deleted event received, workspace name: ", workspaceName)
					member := eventObj.Attributes["delUsers"]
					if member != "" {
						if err := helper.RemoveUsersFromVelaProject(ctx, workspaceName, []string{member}); err != nil {
							newEvent.Type = eventv1.Event_ERROR
							newEvent.Reason = constant.WorkspaceRemoveUserIntoVelaProjectFailed
							newEvent.Message = fmt.Sprintf("remove users from vela project failed, err: %v", err)
							w.EventBridge.Publish(newEvent)
						}
						newEvent.Type = eventv1.Event_NORMAL
						newEvent.Reason = constant.WorkspaceRemoveUserIntoVelaProjectSuccess
						newEvent.Message = fmt.Sprintf("remove users from vela project success, workspace name: %s", workspaceName)
						w.EventBridge.Publish(newEvent)
					}

				case constant.WorkspaceMemberUpdatedEvent.Name:
					klog.Infof("workspace member updated event received, workspace name: %s", workspaceName)
					// 暂时不需要更新vela
				}
				//更新模型仓库用户角色
				members := UpdateMember{
					AddManagers: splitString(eventObj.Attributes["addManagers"]),
					AddMembers:  splitString(eventObj.Attributes["addMembers"]),
					DelManagers: splitString(eventObj.Attributes["delManagers"]),
					DelMembers:  splitString(eventObj.Attributes["delMembers"]),
				}
				err := w.ModelHubService.UpdateModelHubMember(ctx, workspaceName, eventObj.Operator, &members)
				if err != nil {
					klog.Errorf("UpdateModelHubMember failed, err: %v", err)
				}
			}
		}

	})

	w.EventBridge.Subscribe(ctx, pubsub.ContainsMatcher("workspace-deleted-matcher", constant.WorkspaceDeletedEvent.Name), func(eventObj *eventv1.Event) {
		// 删除时，初始化一些操作
		if eventObj.Type == eventv1.Event_NORMAL && eventObj.Reason == constant.Success {
			if eventObj.EventObject != nil && eventObj.EventObject.Name != "" {
				//newEvent := eventObj.DeepCopy()
				//workspaceName := eventObj.ObjectName
				// TODO 删除 vela 项目
				//  删除 keycloak 用户
				// 删除 namespace /需要check

			}
		}
	})
	return nil
}

type WorkspaceLocalClusterObjectChangedListener struct {
}

func (w *WorkspaceService) OnObjectAdded(ctx context.Context, clusterName string, newObject client.Object) error {
	if workspace, ok := newObject.(*schedulingv1alpha1.Workspace); ok {
		workspaceEntity := &model.WorkspaceBaseEntity{Name: workspace.Name}
		err := w.Store.Get(ctx, workspaceEntity)
		if err != nil {
			klog.Errorf("get workspace entity failed, err: %v", err)
			return err
		}
		err = w.initTensorboardVolume(ctx, workspaceEntity)
		if err != nil {
			klog.Errorf("init tensorboard volume failed, err: %v", err)
			return err
		}
	}
	return nil
}

func (w *WorkspaceService) OnObjectUpdated(ctx context.Context, clusterName string, newObject, oldObject client.Object) error {
	return nil
}

func (w *WorkspaceService) OnObjectDeleted(ctx context.Context, clusterName string, oldObject client.Object) error {
	return nil
}

func (w *WorkspaceService) GetWatchedObjectList() []watcher.WatchObject {
	return []watcher.WatchObject{
		{
			Object: &schedulingv1alpha1.Workspace{},
		},
	}
}

func NewWorkspaceService() *WorkspaceService {
	ws := &WorkspaceService{}
	RegisterLocalClusterListener("workspace-service", ws)
	return ws
}

// CreateOrUpdateWorkspaceRobot 创建或更新空间机器人用户，直接赋予机器人用户角色
func (w *WorkspaceService) CreateOrUpdateWorkspaceRobot(ctx context.Context, request *v1.CreateOrUpdateRobotAccountRequest) (*userv1.UserDetail, error) {
	// 创建机器人
	l := ctx.Value(constant.UserCtxKey).(string)
	if request.Account == "" {
		createUserRequest := &userv1.CreateOrUpdateUserRequest{
			UserType: RobotUserType,
			UserDetail: &userv1.UserDetail{
				RobotAttributes: &userv1.RobotAttributes{
					DisplayName:      request.DisplayName,
					Description:      request.Description,
					WeChatRobotToken: request.WeChatRobotToken,
					Workspaces:       []string{request.WorkspaceName},
					OriginWorkspace:  request.WorkspaceName,
				},
			},
			Creator: l,
		}
		if request.IsDefault {
			createUserRequest.UserDetail.RobotAttributes.IsDefault = "true"
		} else {
			createUserRequest.UserDetail.RobotAttributes.IsDefault = "false"
		}
		user, err := w.UserService.CreateOrUpdateUser(ctx, createUserRequest)
		if err != nil {
			return nil, bcode.ErrorServerInternalError("create robot user failed")
		}

		// 赋予机器人空间角色
		_, err = w.AddWorkspaceUsers(ctx, &v1.AddWorkspaceUsersRequest{
			WorkspaceName: request.WorkspaceName,
			Account:       []string{user.Account},
			Role:          request.Role,
			UserType:      RobotUserType,
		})
		if err != nil {
			return nil, bcode.ErrorServerInternalError("add robot user[%s] to workspace[%s] failed", user.Account, request.WorkspaceName)
		}
		return user, nil
	} else {
		robotUser := &model.RobotUserEntity{
			Account: request.Account,
		}
		if err := w.Store.Get(ctx, robotUser); err != nil {
			if errors.Is(err, datastore.ErrRecordNotExist) {
				return nil, bcode.ErrorServerInternalError("robot user[%s] not exist", request.Account)
			}
			return nil, bcode.ErrorServerInternalError("get robot user[%s] failed, err: %v", request.Account, err)
		}

		robotUser.DisplayName = request.DisplayName
		//robotUser.Password = request.Password
		robotUser.Description = request.Description
		robotUser.WeChatRobotToken = request.WeChatRobotToken
		robotUser.Updater = l
		robotUser.UpdateTime = time.Now()

		err := w.Store.Put(ctx, robotUser)
		if err != nil {
			return nil, bcode.ErrorServerInternalError("update robot user[%s] failed, err: %v", request.Account, err)
		}
		err = w.Store.Get(ctx, robotUser)
		if err != nil {
			return nil, bcode.ErrorServerInternalError("get robot user[%s] failed, err: %v", request.Account, err)
		}
		return &userv1.UserDetail{
			Account: robotUser.Account,
			RobotAttributes: &userv1.RobotAttributes{
				Account:          robotUser.Account,
				DisplayName:      robotUser.DisplayName,
				Description:      robotUser.Description,
				AccessToken:      robotUser.AccessToken,
				Password:         robotUser.Password,
				WeChatRobotToken: robotUser.WeChatRobotToken,
			},
			UserType: userv1.UserType_Robot,
			Timestamp: &common.TimestampModel{
				CreateTime: util.TimeFormat(robotUser.CreateTime),
				UpdateTime: util.TimeFormat(robotUser.UpdateTime),
			},
		}, nil
	}
}

// CheckWorkspaceIsExist 检查空间是否已经存在
// 适配 fcn 前端接口，返回 422 表示空间已存在，返回 0 表示空间不存在
func (w *WorkspaceService) CheckWorkspaceIsExist(ctx context.Context, request *v1.CheckWorkspaceIsExistRequest) (*v1.CheckWorkspaceIsExistResponse, error) {
	workspaceEntity := &model.WorkspaceBaseEntity{
		Name: request.Name,
	}
	err := w.Store.Get(ctx, workspaceEntity)
	if err != nil {
		if errors.Is(err, datastore.ErrRecordNotExist) {
			return &v1.CheckWorkspaceIsExistResponse{
				Status: int32(0),
				Errors: "当前空间不存在，可以创建",
			}, nil
		}
		return nil, bcode.ErrorServerInternalError("check workspace is exist error, err: %v ", err)
	}
	return &v1.CheckWorkspaceIsExistResponse{
		Status: int32(422),
		Errors: "当前空间已存在，请更换空间名",
	}, nil
}

// CreateWorkspace 创建空间
func (w *WorkspaceService) CreateWorkspace(ctx context.Context, request *v1.CreateWorkspaceRequest) (*v1.WorkspaceDetail, error) {
	// 检查空间是否已经存在
	isExist, err := w.CheckWorkspaceIsExist(ctx, &v1.CheckWorkspaceIsExistRequest{
		Name: request.Name,
	})
	if err != nil {
		return nil, bcode.ErrorServerInternalError("check workspace[%s] is exist error, err: %v", request.Name, err)
	}
	if isExist.Status != 0 {
		return nil, bcode.ErrorWorkspaceAlreadyExist("workspace[%s] already exist, err: %v", request.Name, err)
	}
	creator := ctx.Value(constant.UserCtxKey).(string)
	// 创建空间KeyCloak 角色、资源、策略、权限
	resourceId, err := w.RbacService.InitWorkspaceKeycloak(ctx, request.Name)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("init workspace[%s] keycloak error, err: %v", request.Name, err)
	}

	// 创建空间RBAC 权限
	err = w.RbacService.InitWorkspaceRbac(ctx, request.Name)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("init workspace[%s] rbac error, err: %v", request.Name, err)
	}

	// 创建空间元信息
	timeNow := time.Now()
	workspaceBaseEntity := &model.WorkspaceBaseEntity{
		BaseModel: model.BaseModel{
			CreateTime: timeNow,
			UpdateTime: timeNow,
		},
		Name:        request.Name,
		DisplayName: request.DisplayName,
		Description: request.Description,
		Managers:    request.Managers,
		Members:     request.Members,
		Enabled:     true,
		ResourceId:  resourceId,
	}
	err = w.Store.Add(ctx, workspaceBaseEntity)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("create workspace[%s] error, err: %v", request.Name, err)
	}

	managers := request.Managers
	if !util.Contains(managers, creator) {
		managers = append(managers, creator)
	}
	// 处理空间成员和管理员
	_, err = w.AddWorkspaceUsers(ctx, &v1.AddWorkspaceUsersRequest{
		WorkspaceName: request.Name,
		Account:       managers,
		Role:          constant.Admin,
		UserType:      constant.User,
	})
	if err != nil {
		return nil, bcode.ErrorServerInternalError("add workspace[%s] managers error, err: %v", request.Name, err)
	}

	_, err = w.AddWorkspaceUsers(ctx, &v1.AddWorkspaceUsersRequest{
		WorkspaceName: request.Name,
		Account:       request.Members,
		Role:          constant.Member,
		UserType:      constant.User,
	})
	// 创建空间默认机器人
	robotAdmin, err := w.CreateOrUpdateWorkspaceRobot(ctx, &v1.CreateOrUpdateRobotAccountRequest{
		DisplayName:   "robot-" + request.Name,
		Description:   "空间默认管理员机器人",
		WorkspaceName: request.Name,
		Role:          constant.WorkspaceAdmin,
		IsDefault:     true,
	})
	if err != nil {
		return nil, bcode.ErrorServerInternalError("create workspace[%s] robot error, err: %v", request.Name, err)
	}

	workspaceCR := schedulingv1alpha1.Workspace{}
	err = multicluster.Instance().GetLocalCluster().Direct().Get(ctx, client.ObjectKey{Name: request.Name}, &workspaceCR)
	if err != nil {
		if apierrors.IsNotFound(err) {
			workspaceCR = schedulingv1alpha1.Workspace{
				ObjectMeta: metav1.ObjectMeta{
					Name: request.Name,
				},
				Spec: schedulingv1alpha1.WorkspaceSpec{
					DisplayName: request.DisplayName,
					Description: request.Description,
					Namespace:   getNamespaceName(request.Name),
					RobotAccount: schedulingv1alpha1.WorkspaceRobotAccount{
						Account:  robotAdmin.Account,
						Password: base64.StdEncoding.EncodeToString([]byte(robotAdmin.RobotAttributes.Password)),
					},
				},
			}
			err = multicluster.Instance().GetLocalCluster().Direct().Create(ctx, &workspaceCR)
			if err != nil {
				return nil, bcode.ErrorServerInternalError("create workspace[%s] error, err: %v", request.Name, err)
			}
		} else {
			return nil, bcode.ErrorServerInternalError("get workspace[%s] error, err: %v", request.Name, err)
		}
	}
	klog.Infof("create workspace[%s] to federation success", request.Name)
	// TODO 初始化 数据集

	// 发送事件
	users := append(managers, request.Members...)
	account := ctx.Value(constant.UserCtxKey).(string)
	addManegers := append(managers, robotAdmin.Account)
	createWorkspaceEvent := &eventv1.Event{
		Source:      constant.WorkspaceEventSource,
		Name:        constant.WorkspaceCreatedEvent.Name,
		DisplayName: constant.WorkspaceCreatedEvent.DisplayName,
		Message:     fmt.Sprintf("workspace %s create success", request.Name),
		Reason:      constant.Success,
		Type:        eventv1.Event_NORMAL,
		EventObject: &eventv1.EventObject{
			Kind: constant.EventObjectKindWorkspace,
			Name: request.Name,
		},
		Attributes: map[string]string{
			"displayName": request.DisplayName,
			"addUsers":    joinStrings(users),
			"addManagers": joinStrings(addManegers),
			"addMembers":  joinStrings(request.Members),
			"description": request.Description,
		},
		Operator: account,
	}
	w.EventBridge.Publish(createWorkspaceEvent)

	// 返回空间详情
	workspaceBase := &model.WorkspaceBaseEntity{Name: request.Name}
	err = w.Store.Get(ctx, workspaceBase)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("get workspace[%s] error, err: %v", request.Name, err)
	}

	return &v1.WorkspaceDetail{
		Base: w.ModelToWorkspaceBase(workspaceBase),
	}, nil
}

// ModelToWorkspaceBase 将数据库中的WorkspaceBaseEntity转换为v1.WorkspaceBase
func (w *WorkspaceService) ModelToWorkspaceBase(workspaceBase *model.WorkspaceBaseEntity) *v1.WorkspaceBase {
	return &v1.WorkspaceBase{
		Name:        workspaceBase.Name,
		DisplayName: workspaceBase.DisplayName,
		Description: workspaceBase.Description,
		Managers:    workspaceBase.Managers,
		Members:     workspaceBase.Members,
		Enabled:     workspaceBase.Enabled,
		ResourceID:  workspaceBase.ResourceId,
		Timestamp: &common.TimestampModel{
			CreateTime: util.TimeFormat(workspaceBase.CreateTime),
			UpdateTime: util.TimeFormat(workspaceBase.UpdateTime),
		},
	}
}

// UpdateWorkspace 更新工作空间
func (w *WorkspaceService) UpdateWorkspace(ctx context.Context, request *v1.UpdateWorkspaceRequest) (*v1.WorkspaceDetail, error) {
	workspaceBase := &model.WorkspaceBaseEntity{
		Name: request.Name,
	}
	err := w.Store.Get(ctx, workspaceBase)
	if err != nil {
		if errors.Is(err, store.ErrNotFound) {
			return nil, bcode.ErrorServerInternalError("workspace[%s] not found, err: %v", request.Name, err)
		}
		return nil, bcode.ErrorServerInternalError("get workspace[%s] error, err: %v", request.Name, err)
	}
	workspaceBase.DisplayName = request.DisplayName
	workspaceBase.Description = request.Description

	managerRole := constant.WorkspaceScope + ":" + request.Name + "-" + constant.Admin
	memberRole := constant.WorkspaceScope + ":" + request.Name + "-" + constant.Member
	scope := constant.WorkspaceScope + ":" + request.Name

	// 查看 request.Managers 与 workspaceBase.Managers 的差异，添加或删除
	addManagers, delManagers := util.DiffSlice(workspaceBase.Managers, request.Managers)
	for _, manager := range addManagers {
		_, err := w.RbacService.AddKeycloakRoleToUser(ctx, AddKeycloakRoleToUserRequest{
			RoleName: managerRole,
			UserName: manager,
		})
		if err != nil {
			return nil, bcode.ErrorServerInternalError("update workspace[%s] error, err: %v", request.Name, err)
		}
		_, err = w.RbacService.CreateOrUpdateWorkspaceRoleBinding(ctx, &rbacv1.CreateOrUpdateWorkspaceRoleBindingRequest{
			Account:     manager,
			Scope:       scope,
			Description: "工作空间管理员",
			Roles:       []string{managerRole},
		})
		if err != nil {
			return nil, bcode.ErrorServerInternalError("update workspace[%s] error, err: %v", request.Name, err)
		}
	}
	for _, manager := range delManagers {
		_, err := w.RbacService.DeleteWorkspaceRoleBinding(ctx, &rbacv1.DeleteWorkspaceRoleBindingRequest{
			Account: manager,
			Scope:   scope,
		})
		if err != nil {
			return nil, bcode.ErrorServerInternalError("update workspace[%s] error, err: %v", request.Name, err)
		}
		_, err = w.RbacService.RemoveKeycloakRoleFromUser(ctx, RemoveKeycloakRoleFromUserRequest{
			RoleName: managerRole,
			UserName: manager,
		})
		if err != nil {
			return nil, bcode.ErrorServerInternalError("update workspace[%s] error, err: %v", request.Name, err)
		}
	}
	// 查看 request.Members 与 workspaceBase.Members 的差异，添加或删除
	addMembers, delMembers := util.DiffSlice(workspaceBase.Members, request.Members)
	for _, member := range addMembers {
		_, err := w.RbacService.AddKeycloakRoleToUser(ctx, AddKeycloakRoleToUserRequest{
			RoleName: memberRole,
			UserName: member,
		})
		if err != nil {
			return nil, bcode.ErrorServerInternalError("update workspace[%s] error, err: %v", request.Name, err)
		}
		_, err = w.RbacService.CreateOrUpdateWorkspaceRoleBinding(ctx, &rbacv1.CreateOrUpdateWorkspaceRoleBindingRequest{
			Account:     member,
			Scope:       scope,
			Description: "工作空间成员",
			Roles:       []string{memberRole},
		})
		if err != nil {
			return nil, bcode.ErrorServerInternalError("update workspace[%s] error, err: %v", request.Name, err)
		}
	}
	for _, member := range delMembers {
		_, err := w.RbacService.DeleteWorkspaceRoleBinding(ctx, &rbacv1.DeleteWorkspaceRoleBindingRequest{
			Account: member,
			Scope:   scope,
		})
		if err != nil {
			return nil, bcode.ErrorServerInternalError("update workspace[%s] error, err: %v", request.Name, err)
		}
		_, err = w.RbacService.RemoveKeycloakRoleFromUser(ctx, RemoveKeycloakRoleFromUserRequest{
			RoleName: memberRole,
			UserName: member,
		})
		if err != nil {
			return nil, bcode.ErrorServerInternalError("update workspace[%s] error, err: %v", request.Name, err)
		}
	}
	workspaceBase.Managers = request.Managers
	workspaceBase.Members = request.Members
	err = w.Store.Put(ctx, workspaceBase)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("update workspace[%s] error, err: %v", request.Name, err)
	}

	addUsers := append(addManagers, addMembers...)
	// 发送事件
	account := ctx.Value(constant.UserCtxKey).(string)
	delUsers := append(delManagers, delMembers...)
	updateWorkspaceEvent := &eventv1.Event{
		Source:      constant.WorkspaceEventSource,
		Name:        constant.WorkspaceMetadataUpdateEvent.Name,
		DisplayName: constant.WorkspaceMetadataUpdateEvent.DisplayName,
		Message:     fmt.Sprintf("workspace %s metadata update success", request.Name),
		Reason:      constant.Success,
		Type:        eventv1.Event_NORMAL,
		EventObject: &eventv1.EventObject{
			Kind: constant.EventObjectKindWorkspace,
			Name: request.Name,
		},
		Attributes: map[string]string{
			"description": request.Description,
			"displayName": request.DisplayName,
			"delUsers":    joinStrings(delUsers),
			"addUsers":    joinStrings(addUsers),
			"delManagers": joinStrings(delManagers),
			"delMembers":  joinStrings(delMembers),
			"addManagers": joinStrings(addManagers),
			"addMembers":  joinStrings(addMembers),
		},
		Operator: account,
	}
	w.EventBridge.Publish(updateWorkspaceEvent)

	return &v1.WorkspaceDetail{
		Base: w.ModelToWorkspaceBase(workspaceBase),
		//Settings: w.ModelToWorkspaceSettings(workspaceSettings),
	}, nil
}

// TODO 删除空间的时候检查是否有未释放的资源，如果有则不允许删除

// DisableWorkspace 删除（禁用）工作空间
func (w *WorkspaceService) DisableWorkspace(ctx context.Context, request *v1.DisableWorkspaceRequest) (*v1.WorkspaceDetail, error) {
	workspaceBase := &model.WorkspaceBaseEntity{
		Name: request.Name,
	}
	err := w.Store.Get(ctx, workspaceBase)
	if err != nil {
		if errors.Is(err, store.ErrNotFound) {
			return nil, bcode.ErrorServerInternalError("workspace[%s] not found, err: %v", request.Name, err)
		}
		return nil, bcode.ErrorServerInternalError("get workspace[%s] error, err: %v", request.Name, err)
	}

	for _, m := range workspaceBase.Managers {
		_, err = w.DeleteWorkspaceUser(ctx, &v1.DeleteWorkspaceUserRequest{
			Account: m,
			Role:    constant.Admin,
		})
	}
	for _, m := range workspaceBase.Members {
		_, err = w.DeleteWorkspaceUser(ctx, &v1.DeleteWorkspaceUserRequest{
			Account: m,
			Role:    constant.Member,
		})
	}

	// 删除空间 keycloak 相关的权限资源角色
	err = w.RbacService.ClearWorkspaceRbac(ctx, request.Name)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("clear workspace[%s] rbac error, err: %v", request.Name, err)
	}

	err = w.RbacService.ClearWorkspaceKeycloak(ctx, request.Name, workspaceBase.ResourceId)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("clear workspace[%s] keycloak error, err: %v", request.Name, err)
	}

	workspaceBase.Enabled = false
	err = w.Store.Put(ctx, workspaceBase)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("disable workspace[%s] error, err: %v", request.Name, err)
	}

	// 发送事件
	account := ctx.Value(constant.UserCtxKey).(string)

	deleteWorkspaceEvent := &eventv1.Event{
		Source:      constant.WorkspaceEventSource,
		Name:        constant.WorkspaceDeletedEvent.Name,
		DisplayName: constant.WorkspaceDeletedEvent.DisplayName,
		Message:     fmt.Sprintf("workspace %s delete success", request.Name),
		Reason:      constant.Success,
		Type:        eventv1.Event_NORMAL,
		Operator:    account,
		EventObject: &eventv1.EventObject{
			Kind: constant.EventObjectKindWorkspace,
			Name: request.Name,
		},
	}
	w.EventBridge.Publish(deleteWorkspaceEvent)

	err = w.Store.Get(ctx, workspaceBase)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("disable workspace[%s] error, err: %v", request.Name, err)
	}

	return &v1.WorkspaceDetail{
		Base: w.ModelToWorkspaceBase(workspaceBase),
	}, nil
}

// GetWorkspaceBase 获取工作空间基本信息
func (w *WorkspaceService) GetWorkspaceBase(ctx context.Context, request *v1.GetWorkspaceBaseRequest) (*v1.WorkspaceBase, error) {
	workspaceBase := &model.WorkspaceBaseEntity{
		Name: request.Name,
	}
	err := w.Store.Get(ctx, workspaceBase)
	if err != nil {
		if errors.Is(err, store.ErrNotFound) {
			return nil, bcode.ErrorServerInternalError("workspace[%s] not found, err: %v", request.Name, err)
		}
		return nil, bcode.ErrorServerInternalError("get workspace[%s] error, err: %v", request.Name, err)
	}
	if !workspaceBase.Enabled {
		return nil, bcode.ErrorServerInternalError("workspace[%s] is disabled, err: %v", request.Name, err)
	}

	return w.ModelToWorkspaceBase(workspaceBase), nil
}

// GetWorkspaceDetail 获取工作空间详细信息
func (w *WorkspaceService) GetWorkspaceDetail(ctx context.Context, request *v1.GetWorkspaceDetailRequest) (*v1.WorkspaceDetail, error) {
	workspaceBase := &model.WorkspaceBaseEntity{
		Name: request.Name,
	}
	err := w.Store.Get(ctx, workspaceBase)
	if err != nil {
		if errors.Is(err, store.ErrNotFound) {
			return nil, bcode.ErrorServerInternalError("workspace[%s] not found, err: %v", request.Name, err)
		}
		return nil, bcode.ErrorServerInternalError("get workspace[%s] error, err: %v", request.Name, err)
	}
	if !workspaceBase.Enabled {
		return nil, bcode.ErrorServerInternalError("workspace[%s] is disabled, err: %v", request.Name, err)
	}
	workspaceSettings := &model.WorkspaceSettingsEntity{
		WorkspaceName: request.Name,
	}
	err = w.Store.Get(ctx, workspaceSettings)
	if err != nil {
		if errors.Is(err, store.ErrNotFound) {
			return nil, bcode.ErrorServerInternalError("workspace[%s] setting not found, err: %v", request.Name, err)
		}
		return nil, bcode.ErrorServerInternalError("get workspace[%s] error, err: %v", request.Name, err)
	}
	return &v1.WorkspaceDetail{
		Base: w.ModelToWorkspaceBase(workspaceBase),
	}, nil
}

// GetWorkspaceResourceSummary TODO 等待其他接口
// GetWorkspaceResourceSummary 获取工作空间资源概要信息
func (w *WorkspaceService) GetWorkspaceResourceSummary(ctx context.Context, request *v1.GetWorkspaceResourceSummaryRequest) (*v1.WorkspaceResourceSummary, error) {
	//TODO implement me
	panic("implement me")
}

// ListJoinableWorkspace 获取当前用户可加入的工作空间列表
// 通过获取全量启用的workspace和用户已加入的workspace，然后做减法得到可加入的列表
func (w *WorkspaceService) ListJoinableWorkspace(ctx context.Context, options *v1.ListOptions) (*v1.ListWorkspaceResult, error) {
	if options.Account == "" {
		return nil, bcode.ErrorInvalidArgument("account is required")
	}

	// 获取所有启用的workspace（完整对象）
	allEnabledWorkspaces, err := w.getAllEnabledWorkspaces(ctx)
	if err != nil {
		return nil, err
	}

	// 获取用户已加入的workspace
	joinedWorkspaces, err := w.ListWorkspaces(ctx, options)
	if err != nil {
		return nil, err
	}

	// 创建已加入workspace的map，用于快速查找
	joinedWorkspaceMap := make(map[string]bool)
	for _, workspace := range joinedWorkspaces.Workspaces {
		joinedWorkspaceMap[workspace.Name] = true
	}

	// 过滤出可加入的workspace（启用但未加入的workspace）
	var joinableWorkspaces []*v1.WorkspaceBase
	for _, workspace := range allEnabledWorkspaces {
		// 如果用户还未加入该workspace，则可以加入
		if !joinedWorkspaceMap[workspace.Name] {
			joinableWorkspaces = append(joinableWorkspaces, workspace)
		}
	}

	return &v1.ListWorkspaceResult{
		Workspaces: joinableWorkspaces,
	}, nil
}

// getAllEnabledWorkspaces 获取所有启用的工作空间（完整对象）
func (w *WorkspaceService) getAllEnabledWorkspaces(ctx context.Context) ([]*v1.WorkspaceBase, error) {
	allWorkspaces, err := w.getAllWorkspace(ctx)
	if err != nil {
		return nil, err
	}

	var enabledWorkspaces []*v1.WorkspaceBase
	for _, workspace := range allWorkspaces {
		if workspace.Enabled {
			enabledWorkspaces = append(enabledWorkspaces, workspace)
		}
	}
	return enabledWorkspaces, nil
}

// ListWorkspaces 获取工作空间列表
func (w *WorkspaceService) ListWorkspaces(ctx context.Context, options *v1.ListOptions) (*v1.ListWorkspaceResult, error) {
	filter := &WorkspaceFilterChain{}

	if options.Account != "" {
		isPlatformAdmin, err := w.RbacService.IsPlatformAdmin(ctx, options.Account)
		if err != nil {
			return nil, bcode.ErrorServerInternalError("check platform admin error, err: %v", err)
		}
		filter.AddFilter(func(workspaceBaseEntity *model.WorkspaceBaseEntity) bool {
			if strings.EqualFold(options.Account, "admin") {
				return true
			}
			// 如果是平台管理员，直接返回true
			if isPlatformAdmin {
				return true
			}
			if len(workspaceBaseEntity.Managers) > 0 && util.Contains(workspaceBaseEntity.Managers, options.Account) {
				return true
			}
			if len(workspaceBaseEntity.Members) > 0 && util.Contains(workspaceBaseEntity.Members, options.Account) {
				return true
			}
			return false
		})
	}
	if options.Prefix != "" {
		filter.AddFilter(func(workspaceBaseEntity *model.WorkspaceBaseEntity) bool {
			return strings.HasPrefix(workspaceBaseEntity.Name, options.Prefix)
		})
	}
	filter.AddFilter(func(workspaceBaseEntity *model.WorkspaceBaseEntity) bool {
		if options.DisplayClosed {
			return true
		} else {
			return workspaceBaseEntity.Enabled
		}
	})
	var workspaceBaseEntities []datastore.Entity
	pageNo := 1
	pageSize := 100
	for {
		items, err := w.Store.List(ctx, &model.WorkspaceBaseEntity{}, &datastore.ListOptions{
			Page:     pageNo,
			PageSize: pageSize,
		})
		if err != nil {
			return nil, bcode.ErrorServerInternalError("list workspaces error, err: %v", err)
		}
		if len(items) == 0 {
			break
		}
		for _, workspace := range items {
			if filter.Filter(workspace.(*model.WorkspaceBaseEntity)) {
				workspaceBaseEntities = append(workspaceBaseEntities, workspace.(*model.WorkspaceBaseEntity))
			}
		}
		pageNo++
	}
	return &v1.ListWorkspaceResult{Workspaces: w.ModelToWorkspaceBaseList(workspaceBaseEntities)}, nil
}

// ListEnabledWorkspaces 获取所有启用的工作空间
func (w *WorkspaceService) ListEnabledWorkspaces(ctx context.Context) ([]string, error) {
	workspaces, err := w.getAllWorkspace(ctx)
	if err != nil {
		return nil, err
	}
	workspaceNames := make([]string, 0, len(workspaces))
	for _, workspace := range workspaces {
		if workspace.Enabled {
			workspaceNames = append(workspaceNames, workspace.Name)
		}
	}
	return workspaceNames, nil
}

// getAllWorkspace 获取所有工作空间
func (w *WorkspaceService) getAllWorkspace(ctx context.Context) ([]*v1.WorkspaceBase, error) {
	workspaceEntities, err := w.Store.List(ctx, &model.WorkspaceBaseEntity{}, &datastore.ListOptions{
		SortBy: []datastore.SortOption{
			{
				Key:   "name",
				Order: datastore.SortOrderAscending,
			},
		},
	})
	if err != nil {
		return nil, bcode.ErrorServerInternalError("list workspace error, err: %v", err)
	}
	return w.ModelToWorkspaceBaseList(workspaceEntities), nil
}

// ModelToWorkspaceBaseList model转换为workspaceBaseList
func (w *WorkspaceService) ModelToWorkspaceBaseList(entities []datastore.Entity) []*v1.WorkspaceBase {
	var workspaces []*v1.WorkspaceBase
	for _, entity := range entities {
		workspaces = append(workspaces, w.ModelToWorkspaceBase(entity.(*model.WorkspaceBaseEntity)))
	}
	return workspaces
}

// ListWorkspaceMembers 展示工作空间所有用户信息
func (w *WorkspaceService) ListWorkspaceMembers(ctx context.Context, request *v1.ListWorkspaceMembersRequest) (*v1.ListWorkspaceMembersResponse, error) {
	workspace := &model.WorkspaceBaseEntity{
		Name: request.WorkspaceName,
	}
	err := w.Store.Get(ctx, workspace)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("get workspace[%s] error, err: %v", request.WorkspaceName, err)
	}
	if !workspace.Enabled {
		return nil, bcode.ErrorServerInternalError("workspace[%s] is disabled", request.WorkspaceName)
	}

	var workspaceUsers []*v1.WorkspaceUser
	userRoleMap := make(map[string]string)

	for _, member := range workspace.Members {
		userRoleMap[member] = constant.WorkspaceMember
	}
	for _, manager := range workspace.Managers {
		userRoleMap[manager] = constant.WorkspaceAdmin
	}

	for account, role := range userRoleMap {
		if request.Prefix != "" {
			if !strings.Contains(account, request.Prefix) {
				continue
			}
		}
		userInfo, err := w.UserService.GetUser(ctx, &userv1.GetUserDetailRequest{
			Account: account,
		})
		if err != nil {
			return nil, bcode.ErrorServerInternalError("get user[%s] error, err: %v", account, err)
		}

		if request.UserType != "" {
			if request.UserType == constant.User && userInfo.UserType != userv1.UserType_User {
				continue
			}
			if request.UserType == constant.Robot && userInfo.UserType != userv1.UserType_Robot {
				continue
			}
		}

		if userInfo.UserType == userv1.UserType_User {
			workspaceUser := &v1.WorkspaceUser{
				Account:    account,
				UserDetail: userInfo,
				Role:       role,
			}
			workspaceUsers = append(workspaceUsers, workspaceUser)
		} else if userInfo.UserType == userv1.UserType_Robot {
			robot := userInfo.RobotAttributes
			workspaceUser := &v1.WorkspaceUser{
				Account: account,
				UserDetail: &userv1.UserDetail{
					Account: robot.Account,
					RobotAttributes: &userv1.RobotAttributes{
						Account:          robot.Account,
						DisplayName:      robot.DisplayName,
						Description:      robot.Description,
						WeChatRobotToken: robot.WeChatRobotToken,
						Workspaces:       robot.Workspaces,
						OriginWorkspace:  robot.OriginWorkspace,
						IsDefault:        robot.IsDefault,
					},
					UserType: userv1.UserType_Robot,
				},
				Role: role,
			}
			workspaceUsers = append(workspaceUsers, workspaceUser)
		}
	}
	// 在返回之前，使用 sort.Slice 对 workspaceUsers 进行排序
	sort.Slice(workspaceUsers, func(i, j int) bool {
		return workspaceUsers[i].Account < workspaceUsers[j].Account
	})

	return &v1.ListWorkspaceMembersResponse{
		WorkspaceUsers: workspaceUsers,
	}, nil
}

// AddWorkspaceUsers 添加工作空间用户
func (w *WorkspaceService) AddWorkspaceUsers(ctx context.Context, request *v1.AddWorkspaceUsersRequest) (*emptypb.Empty, error) {
	managerRole := constant.WorkspaceScope + ":" + request.WorkspaceName + "-" + constant.Admin
	memberRole := constant.WorkspaceScope + ":" + request.WorkspaceName + "-" + constant.Member
	scope := constant.WorkspaceScope + ":" + request.WorkspaceName

	// 获取工作空间信息
	workspace := &model.WorkspaceBaseEntity{
		Name: request.WorkspaceName,
	}
	err := w.Store.Get(ctx, workspace)
	if err != nil {
		if errors.Is(err, datastore.ErrRecordNotExist) {
			return nil, bcode.ErrorServerInternalError("workspace[%s] not exist, err: %v", request.WorkspaceName, err)
		}
		return nil, bcode.ErrorServerInternalError("get workspace[%s] error, err: %v", request.WorkspaceName, err)
	}

	workspaceManagerMap := make(map[string]bool)
	for _, m := range workspace.Managers {
		workspaceManagerMap[m] = true
	}
	workspaceMemberMap := make(map[string]bool)
	for _, m := range workspace.Members {
		workspaceMemberMap[m] = true
	}
	addMembers, addManagers := []string{}, []string{}
	delManagers, delMembers := []string{}, []string{}

	if request.Role == constant.WorkspaceAdmin {
		for _, user := range request.Account {
			if request.UserType == "" {
				if w.UserService.IsRobotUser(user) {
					request.UserType = constant.Robot
				} else {
					request.UserType = constant.User
				}
			}
			// 如果添加的管理员已经是成员了，需要删除之前的成员角色
			if workspaceMemberMap[user] {
				workspace.Members = remove(workspace.Members, user)
				// 普通用户需要添加 keycloak 角色
				_, err = w.RbacService.RemoveKeycloakRoleFromUser(ctx, RemoveKeycloakRoleFromUserRequest{
					RoleName: memberRole,
					UserName: user,
				})
				if err != nil {
					return nil, bcode.ErrorServerInternalError("workspace[%s] remove keycloak manager error, err: %v", request.WorkspaceName, err)
				}

				_, err = w.RbacService.DeleteWorkspaceRoleBinding(ctx, &rbacv1.DeleteWorkspaceRoleBindingRequest{
					Scope:   scope,
					Account: user,
					Roles:   []string{memberRole},
				})
				if err != nil {
					return nil, bcode.ErrorServerInternalError("workspace[%s] delete role binding error, err: %v", request.WorkspaceName, err)
				}
				delMembers = append(delMembers, user)
			}
			// 如果添加的管理员不是管理员了，需要添加
			if !workspaceManagerMap[user] {
				workspace.Managers = append(workspace.Managers, user)

				_, err = w.RbacService.AddKeycloakRoleToUser(ctx, AddKeycloakRoleToUserRequest{
					RoleName: managerRole,
					UserName: user,
				})
				if err != nil {
					return nil, bcode.ErrorServerInternalError("workspace[%s] add keycloak manager error, err: %v", request.WorkspaceName, err)
				}

				_, err = w.RbacService.CreateOrUpdateWorkspaceRoleBinding(ctx, &rbacv1.CreateOrUpdateWorkspaceRoleBindingRequest{
					Scope:   scope,
					Account: user,
					Roles:   []string{managerRole},
				})
				if err != nil {
					return nil, bcode.ErrorServerInternalError("workspace[%s] create role binding error, err: %v", request.WorkspaceName, err)
				}
				addManagers = append(addManagers, user)
			}

		}
	} else if request.Role == constant.WorkspaceMember {
		for _, user := range request.Account {
			if !workspaceMemberMap[user] && !workspaceManagerMap[user] {
				workspace.Members = append(workspace.Members, user)

				_, err = w.RbacService.AddKeycloakRoleToUser(ctx, AddKeycloakRoleToUserRequest{
					RoleName: memberRole,
					UserName: user,
				})
				if err != nil {
					return nil, bcode.ErrorServerInternalError("workspace[%s] add keycloak member error, err: %v", request.WorkspaceName, err)
				}

				_, err = w.RbacService.CreateOrUpdateWorkspaceRoleBinding(ctx, &rbacv1.CreateOrUpdateWorkspaceRoleBindingRequest{
					Account:     user,
					Scope:       scope,
					Description: "工作空间成员",
					Roles:       []string{memberRole},
				})
				if err != nil {
					return nil, bcode.ErrorServerInternalError("workspace[%s] create role binding error, err: %v", request.WorkspaceName, err)
				}
				if !workspaceMemberMap[user] {
					addMembers = append(addMembers, user)
				}
			}
		}
	}
	err = w.Store.Put(ctx, workspace)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("batch add workspace[%s] members error, err: %v", request.WorkspaceName, err)
	}

	for _, user := range request.Account {
		if request.UserType == "" {
			if w.UserService.IsRobotUser(user) {
				request.UserType = constant.Robot
			} else {
				request.UserType = constant.User
			}
		}
		if request.UserType == constant.Robot {
			robotUser := model.RobotUserEntity{
				Account: user,
			}
			err = w.Store.Get(ctx, &robotUser)
			if err != nil {
				return nil, bcode.ErrorServerInternalError("get robot user[%s] error, err: %v", user, err)
			}
			if !util.StringInSlice(request.WorkspaceName, robotUser.Workspaces) {
				robotUser.Workspaces = append(robotUser.Workspaces, request.WorkspaceName)
				err = w.Store.Put(ctx, &robotUser)
				if err != nil {
					return nil, bcode.ErrorServerInternalError("put robot user[%s] error, err: %v", user, err)
				}
			}

		}
	}

	account := ctx.Value(constant.UserCtxKey).(string)
	//发送事件
	workspaceMemberCreatedEvent := &eventv1.Event{
		Source:      constant.WorkspaceEventSource,
		Name:        constant.WorkspaceMemberCreatedEvent.Name,
		DisplayName: constant.WorkspaceMemberCreatedEvent.DisplayName,
		Message:     fmt.Sprintf("workspace %s add member %s success", request.WorkspaceName, request.Account),
		Reason:      constant.Success,
		Type:        eventv1.Event_NORMAL,
		Operator:    account,
		EventObject: &eventv1.EventObject{
			Kind: constant.EventObjectKindWorkspace,
			Name: request.WorkspaceName,
		},
		Attributes: map[string]string{
			"addUsers":    joinStrings(request.Account),
			"role":        request.Role,
			"addMembers":  joinStrings(addMembers),
			"addManagers": joinStrings(addManagers),
			"delMembers":  joinStrings(delMembers),
			"delManagers": joinStrings(delManagers),
		},
	}
	w.EventBridge.Publish(workspaceMemberCreatedEvent)

	return &emptypb.Empty{}, nil
}

// DeleteWorkspaceUser 删除工作空间用户
func (w *WorkspaceService) DeleteWorkspaceUser(ctx context.Context, request *v1.DeleteWorkspaceUserRequest) (*emptypb.Empty, error) {
	managerRole := constant.WorkspaceScope + ":" + request.WorkspaceName + "-" + constant.Admin
	memberRole := constant.WorkspaceScope + ":" + request.WorkspaceName + "-" + constant.Member
	scope := constant.WorkspaceScope + ":" + request.WorkspaceName

	addMembers, addManagers := []string{}, []string{}
	delManagers, delMembers := []string{}, []string{}

	workspace := &model.WorkspaceBaseEntity{
		Name: request.WorkspaceName,
	}
	err := w.Store.Get(ctx, workspace)
	if err != nil {
		if errors.Is(err, datastore.ErrRecordNotExist) {
			return nil, bcode.ErrorServerInternalError("workspace[%s] not exist, err: %v", request.WorkspaceName, err)
		}
		return nil, bcode.ErrorServerInternalError("get workspace[%s] error, err: %v", request.WorkspaceName, err)
	}
	if request.UserType == "" {
		if w.UserService.IsRobotUser(request.Account) {
			request.UserType = constant.Robot
		} else {
			request.UserType = constant.User
		}
	}

	if request.Role == constant.WorkspaceAdmin {
		managerIsExist := false
		for _, m := range workspace.Managers {
			if m == request.Account {
				managerIsExist = true
				break
			}
		}
		if !managerIsExist {
			return nil, bcode.ErrorServerInternalError("manager[%s] is not in workspace[%s], err: %v", request.Account, request.WorkspaceName, err)
		}

		workspace.Managers = remove(workspace.Managers, request.Account)

		_, err = w.RbacService.RemoveKeycloakRoleFromUser(ctx, RemoveKeycloakRoleFromUserRequest{
			RoleName: managerRole,
			UserName: request.Account,
		})
		if err != nil {
			return nil, bcode.ErrorServerInternalError("delete workspace[%s] manager[%s] keycloak error, err: %v", request.WorkspaceName, request.Account, err)
		}

		_, err = w.RbacService.DeleteWorkspaceRoleBinding(ctx, &rbacv1.DeleteWorkspaceRoleBindingRequest{
			Scope:   scope,
			Account: request.Account,
		})
		if err != nil {
			return nil, bcode.ErrorServerInternalError("delete workspace[%s] manager[%s] role binding error, err: %v", request.WorkspaceName, request.Account, err)
		}
		delManagers = append(delManagers, request.Account)

	} else if request.Role == constant.WorkspaceMember {

		memberIsExist := false
		for _, member := range workspace.Members {
			if member == request.Account {
				memberIsExist = true
				break
			}
		}
		if !memberIsExist {
			return nil, bcode.ErrorServerInternalError("member[%s] is not in workspace[%s], err: %v", request.Account, request.WorkspaceName, err)
		}

		workspace.Members = remove(workspace.Members, request.Account)

		_, err = w.RbacService.RemoveKeycloakRoleFromUser(ctx, RemoveKeycloakRoleFromUserRequest{
			RoleName: memberRole,
			UserName: request.Account,
		})
		if err != nil {
			return nil, bcode.ErrorServerInternalError("delete workspace[%s] member[%s] keycloak error, err: %v", request.WorkspaceName, request.Account, err)
		}

		_, err = w.RbacService.DeleteWorkspaceRoleBinding(ctx, &rbacv1.DeleteWorkspaceRoleBindingRequest{
			Scope:   scope,
			Account: request.Account,
		})
		if err != nil {
			return nil, bcode.ErrorServerInternalError("delete workspace[%s] member[%s] role binding error, err: %v", request.WorkspaceName, request.Account, err)
		}
		delMembers = append(delMembers, request.Account)
	}

	err = w.Store.Put(ctx, workspace)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("delete workspace[%s] manager[%s] error, err: %v", request.WorkspaceName, request.Account, err)
	}

	if request.UserType == constant.Robot {
		robotUser := model.RobotUserEntity{
			Account: request.Account,
		}
		err = w.Store.Get(ctx, &robotUser)
		if err != nil {
			return nil, bcode.ErrorServerInternalError("get robot user[%s] error, err: %v", request.Account, err)
		}
		if util.StringInSlice(request.WorkspaceName, robotUser.Workspaces) {
			robotUser.Workspaces = remove(robotUser.Workspaces, request.WorkspaceName)
			err = w.Store.Put(ctx, &robotUser)
			if err != nil {
				return nil, bcode.ErrorServerInternalError("put robot user[%s] error, err: %v", request.Account, err)
			}
		}
	}

	// 发送事件
	account := ctx.Value(constant.UserCtxKey).(string)
	updateWorkspaceEvent := &eventv1.Event{
		Source:      constant.WorkspaceEventSource,
		Name:        constant.WorkspaceMemberDeletedEvent.Name,
		DisplayName: constant.WorkspaceMemberDeletedEvent.DisplayName,
		Message:     fmt.Sprintf("workspace %s delete member %s success", request.WorkspaceName, request.Account),
		Reason:      constant.Success,
		Type:        eventv1.Event_NORMAL,
		Operator:    account,
		EventObject: &eventv1.EventObject{
			Kind: constant.EventObjectKindWorkspace,
			Name: request.WorkspaceName,
		},
		Attributes: map[string]string{
			"role":        request.Role,
			"delUsers":    request.Account,
			"addMembers":  joinStrings(addMembers),
			"addManagers": joinStrings(addManagers),
			"delMembers":  joinStrings(delMembers),
			"delManagers": joinStrings(delManagers),
		},
	}
	w.EventBridge.Publish(updateWorkspaceEvent)

	return &emptypb.Empty{}, nil
}

// ChangeWorkspaceUserRole 修改工作空间用户角色
func (w *WorkspaceService) ChangeWorkspaceUserRole(ctx context.Context, request *v1.ChangeWorkspaceUserRoleRequest) (*emptypb.Empty, error) {
	addMembers, addManagers, delMembers, delManagers := []string{}, []string{}, []string{}, []string{}
	memberRole := constant.WorkspaceScope + ":" + request.WorkspaceName + "-" + constant.Member
	managerRole := constant.WorkspaceScope + ":" + request.WorkspaceName + "-" + constant.Admin
	scope := constant.WorkspaceScope + ":" + request.WorkspaceName
	workspace := &model.WorkspaceBaseEntity{
		Name: request.WorkspaceName,
	}
	err := w.Store.Get(ctx, workspace)
	if err != nil {
		if errors.Is(err, datastore.ErrRecordNotExist) {
			return nil, bcode.ErrorServerInternalError("workspace[%s] not exist, err: %v", request.WorkspaceName, err)
		}
		return nil, bcode.ErrorServerInternalError("get workspace[%s] error, err: %v", request.WorkspaceName, err)
	}
	if request.Role == constant.WorkspaceAdmin {
		memberIsExist := false
		for _, m := range workspace.Members {
			if m == request.Account {
				memberIsExist = true
				break
			}
		}
		if !memberIsExist {
			return nil, bcode.ErrorServerInternalError("member[%s] is not in workspace[%s], err: %v", request.Account, request.WorkspaceName, err)
		}

		workspace.Members = remove(workspace.Members, request.Account)
		workspace.Managers = append(workspace.Managers, request.Account)

		_, err = w.RbacService.RemoveKeycloakRoleFromUser(ctx, RemoveKeycloakRoleFromUserRequest{
			RoleName: memberRole,
			UserName: request.Account,
		})
		if err != nil {
			return nil, bcode.ErrorServerInternalError("delete workspace[%s] member[%s] keycloak error, err: %v", request.WorkspaceName, request.Account, err)
		}
		_, err = w.RbacService.AddKeycloakRoleToUser(ctx, AddKeycloakRoleToUserRequest{
			RoleName: managerRole,
			UserName: request.Account,
		})
		if err != nil {
			return nil, bcode.ErrorServerInternalError("add workspace[%s] manager[%s] keycloak error, err: %v", request.WorkspaceName, request.Account, err)
		}

		_, err = w.RbacService.DeleteWorkspaceRoleBinding(ctx, &rbacv1.DeleteWorkspaceRoleBindingRequest{
			Scope:   scope,
			Account: request.Account,
		})
		if err != nil {
			return nil, bcode.ErrorServerInternalError("delete workspace[%s] member[%s] role binding error, err: %v", request.WorkspaceName, request.Account, err)
		}
		delMembers = append(delMembers, request.Account)
		_, err = w.RbacService.CreateOrUpdateWorkspaceRoleBinding(ctx, &rbacv1.CreateOrUpdateWorkspaceRoleBindingRequest{
			Scope:   scope,
			Account: request.Account,
			Roles:   []string{managerRole},
		})
		if err != nil {
			return nil, bcode.ErrorServerInternalError("add workspace[%s] manager[%s] role binding error, err: %v", request.WorkspaceName, request.Account, err)
		}
		addManagers = append(addManagers, request.Account)
	} else if request.Role == constant.WorkspaceMember {
		managerIsExist := false
		for _, m := range workspace.Managers {
			if m == request.Account {
				managerIsExist = true
				break
			}
		}
		if !managerIsExist {
			return nil, bcode.ErrorServerInternalError("manager[%s] is not in workspace[%s], err: %v", request.Account, request.WorkspaceName, err)
		}

		workspace.Managers = remove(workspace.Managers, request.Account)
		workspace.Members = append(workspace.Members, request.Account)

		_, err = w.RbacService.RemoveKeycloakRoleFromUser(ctx, RemoveKeycloakRoleFromUserRequest{
			RoleName: managerRole,
			UserName: request.Account,
		})
		if err != nil {
			return nil, bcode.ErrorServerInternalError("delete workspace[%s] manager[%s] keycloak error, err: %v", request.WorkspaceName, request.Account, err)
		}
		_, err = w.RbacService.AddKeycloakRoleToUser(ctx, AddKeycloakRoleToUserRequest{
			RoleName: memberRole,
			UserName: request.Account,
		})
		if err != nil {
			return nil, bcode.ErrorServerInternalError("Add workspace[%s] member[%s] keycloak error, err: %v", request.WorkspaceName, request.Account, err)
		}

		_, err = w.RbacService.DeleteWorkspaceRoleBinding(ctx, &rbacv1.DeleteWorkspaceRoleBindingRequest{
			Scope:   scope,
			Account: request.Account,
		})
		if err != nil {
			return nil, bcode.ErrorServerInternalError("delete workspace[%s] manager[%s] role binding error, err: %v", request.WorkspaceName, request.Account, err)
		}
		delManagers = append(delManagers, request.Account)

		_, err = w.RbacService.CreateOrUpdateWorkspaceRoleBinding(ctx, &rbacv1.CreateOrUpdateWorkspaceRoleBindingRequest{
			Account:     request.Account,
			Scope:       scope,
			Description: "工作空间成员",
			Roles:       []string{memberRole},
		})
		if err != nil {
			return nil, bcode.ErrorServerInternalError("create workspace[%s] member[%s] role binding error, err: %v", request.WorkspaceName, request.Account, err)
		}
		addMembers = append(addMembers, request.Account)
	}
	err = w.Store.Put(ctx, workspace)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("batch add workspace[%s] members error, err: %v", request.WorkspaceName, err)
	}

	// 发送事件
	account := ctx.Value(constant.UserCtxKey).(string)
	updateWorkspaceEvent := &eventv1.Event{
		Source:      constant.WorkspaceEventSource,
		Name:        constant.WorkspaceMemberUpdatedEvent.Name,
		DisplayName: constant.WorkspaceMemberUpdatedEvent.DisplayName,
		Message:     fmt.Sprintf("workspace %s change member %s role success", request.WorkspaceName, request.Account),
		Reason:      constant.Success,
		Type:        eventv1.Event_NORMAL,
		Operator:    account,
		EventObject: &eventv1.EventObject{
			Kind: constant.EventObjectKindWorkspace,
			Name: request.WorkspaceName,
		},
		Attributes: map[string]string{
			"user":        request.Account,
			"role":        request.Role,
			"addMembers":  joinStrings(addMembers),
			"addManagers": joinStrings(addManagers),
			"delMembers":  joinStrings(delMembers),
			"delManagers": joinStrings(delManagers),
		},
	}
	w.EventBridge.Publish(updateWorkspaceEvent)

	return &emptypb.Empty{}, nil
}

// IsWorkspaceManager 判断用户是否是工作空间管理员
func (w *WorkspaceService) IsWorkspaceManager(ctx context.Context, request *v1.IsWorkspaceManagerRequest) (*v1.IsWorkspaceManagerResponse, error) {
	if request.WorkspaceName == "" {
		return &v1.IsWorkspaceManagerResponse{
			IsManager: false,
		}, nil
	}
	workspace := &model.WorkspaceBaseEntity{
		Name: request.WorkspaceName,
	}
	err := w.Store.Get(ctx, workspace)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("get workspace[%s] error, err: %v", request.WorkspaceName, err)
	}
	for _, m := range workspace.Managers {
		if m == request.Account {
			return &v1.IsWorkspaceManagerResponse{
				IsManager: true,
			}, nil
		}
	}
	return &v1.IsWorkspaceManagerResponse{
		IsManager: false,
	}, nil
}

// GetClusters 获取工作空间的集群列表
func (w *WorkspaceService) GetClusters(ctx context.Context, workspaceName string) ([]string, error) {
	var workspace = &schedulingv1alpha1.Workspace{}
	err := multicluster.Instance().GetLocalCluster().Direct().Get(ctx, client.ObjectKey{Name: workspaceName}, workspace)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("list kubernetes workspace error, err: %v", err)
	}
	var clusters []string
	var workspaceClusterBindingList = &schedulingv1alpha1.WorkspaceClusterBindingList{}
	err = multicluster.Instance().GetLocalCluster().Direct().List(ctx, workspaceClusterBindingList, client.MatchingLabels{
		constant.WorkspaceNameLabelKey: workspaceName,
	})
	if err != nil {
		return nil, err
	}
	for _, binding := range workspaceClusterBindingList.Items {
		if binding.Status.State == schedulingv1alpha1.WorkspaceClusterBindingStateSynced {
			clusters = append(clusters, binding.Spec.TargetCluster)
		}
	}
	return clusters, nil
}

// GetWorkspaceByName 根据工作空间名称获取工作空间信息
func (w *WorkspaceService) GetWorkspaceByName(ctx context.Context, workspaceName string) (*model.WorkspaceBaseEntity, error) {
	workspace := &model.WorkspaceBaseEntity{
		Name: workspaceName,
	}
	err := w.Store.Get(ctx, workspace)
	if err != nil {
		if errors.Is(err, store.ErrNotFound) {
			return nil, bcode.ErrorWorkspaceNotFound("workspace[%s] not found", workspaceName)
		}
		return nil, bcode.ErrorServerInternalError("get workspace[%s] error, err: %v", workspaceName, err)
	}
	return workspace, nil
}

func getWorkspaceName(role string) string {
	ws := strings.TrimPrefix(role, constant.WorkspaceScope+":")
	if strings.HasSuffix(ws, "-member") {
		return strings.TrimSuffix(ws, "-member")
	} else if strings.HasSuffix(ws, "-admin") {
		return strings.TrimSuffix(ws, "-admin")
	}
	return ws
}

func remove(members []string, member string) []string {
	for i, m := range members {
		if m == member {
			return append(members[:i], members[i+1:]...)
		}
	}
	return members
}

func joinStrings(input []string) string {
	if len(input) > 0 {
		return strings.Join(input, ",")
	}
	return ""
}

func splitString(input string) []string {
	if input != "" {
		return strings.Split(input, ",")
	}
	return []string{}
}

// getFormattedManagers 格式化管理员列表，每个管理员的格式为 姓名(工号)，并且最多返回三个管理员。
func getFormattedManagers(ctx context.Context, managers []string, userService *UserService) []string {
	maxManagers := 3
	formattedManagers := make([]string, 0, maxManagers) // 初始化空切片，容量为maxManagers

	for _, manager := range managers {
		if len(formattedManagers) >= maxManagers {
			break // 如果已经有三个非机器人管理员，就停止循环
		}
		if strings.HasPrefix(manager, constant.Robot) {
			continue // 如果当前管理员是机器人账户，跳过
		}
		userInfo, err := userService.GetUser(ctx, &userv1.GetUserDetailRequest{
			Account: manager,
		})
		if err != nil {
			kratoslog.Errorf("get user info failed, account: %s, err: %v", manager, err)
			continue
		}
		formattedManager, err := formatManager(ctx, manager, userInfo)
		if err != nil {
			// 如果获取用户信息出错，使用原始的manager值
			formattedManagers = append(formattedManagers, manager)
		} else {
			formattedManagers = append(formattedManagers, formattedManager)
		}
	}
	return formattedManagers
}

// formatManager 根据用户账号获取用户信息，并格式化为 姓名(工号) 的形式。
// 如果获取用户信息失败或者用户信息不完整，返回错误。
func formatManager(ctx context.Context, account string, userInfo *userv1.UserDetail) (string, error) {
	if userInfo != nil && userInfo.UserAttributes != nil && userInfo.UserAttributes.CnName != "" && userInfo.UserAttributes.UserCode != "" {
		return userInfo.UserAttributes.CnName + "(" + userInfo.UserAttributes.UserCode + ")", nil
	}
	return "", fmt.Errorf("user[%s] has incomplete attributes", account)
}

// Diff 比较两个字符串数组的差异

func (w *WorkspaceService) IsManager(ctx context.Context, workspaceName, account string) (bool, error) {
	isPlatformAdmin, err := w.RbacService.IsPlatformAdmin(ctx, account)
	if err != nil {
		return false, bcode.ErrorServerInternalError("check platform admin error: %v", err)
	}
	if isPlatformAdmin {
		return true, nil
	}
	workspace, err := w.GetWorkspaceByName(ctx, workspaceName)
	if err != nil {
		return false, err
	}
	if util.StringInSlice(account, workspace.Managers) {
		return true, nil
	}
	if util.StringInSlice(account, workspace.Members) {
		return false, nil
	}
	return false, bcode.ErrorWorkspaceNotFound("workspace[%s] not found user[%s]", workspaceName, account)
}
