package service

import (
	"context"
	"fmt"
	"regexp"

	"git.lianjia.com/cloudnative/kic/kic-platform/aistudio/pkg/constant"
	"git.lianjia.com/cloudnative/kic/kic-platform/aistudio/pkg/server/domain/model"
	"git.lianjia.com/cloudnative/kic/kic-platform/apis/aistudio/bcode"
	v1 "git.lianjia.com/cloudnative/kic/kic-platform/apis/aistudio/config/v1"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/apis/scheduling.kcs.io/v1alpha1"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/infrastructure/datastore"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/multicluster"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/property"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/third_party/s3"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/util"
	kratoslog "github.com/go-kratos/kratos/v2/log"
	corev1 "k8s.io/api/core/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"sigs.k8s.io/controller-runtime/pkg/client"
)

var _ Interface = (*ConfigService)(nil)
var _ v1.ConfigServiceHTTPServer = (*ConfigService)(nil)

// ConfigService 配置服务实现
type ConfigService struct {
	RbacService *RbacService                 `inject:""`
	Store       datastore.DataStore          `inject:"datastore"`
	Properties  property.EnvironmentProperty `inject:""`
	Logger      *kratoslog.Helper            `inject:"logger"`
}

// NewConfigService 创建新的配置服务实例
func NewConfigService() *ConfigService {
	return &ConfigService{}
}

// Init 初始化
func (s *ConfigService) Init(ctx context.Context, properties property.EnvironmentProperty) error {
	return nil
}

// ConvertToConfigEntity 将请求转换为配置实体
func (s *ConfigService) ConvertToConfigEntity(ctx context.Context, req *v1.CreateOrUpdateConfigRequest) (*model.ConfigEntity, error) {
	account, ok := ctx.Value(constant.UserCtxKey).(string)
	if !ok {
		return nil, bcode.ErrorServerInternalError("从上下文中获取用户失败")
	}
	if !util.StringInSlice(account, req.Managers) {
		req.Managers = append(req.Managers, account)
	}
	for _, manager := range req.Managers {
		if !util.StringInSlice(manager, req.Members) {
			req.Members = append(req.Members, manager)
		}
	}

	// 使用正则表达式校验名称格式
	pattern := `^[a-z][a-z0-9-.]{0,48}[a-z0-9]$`
	matched, err := regexp.MatchString(pattern, req.Name)
	if err != nil {
		return nil, bcode.ErrorInvalidArgument("名称格式校验失败")
	}
	if !matched {
		return nil, bcode.ErrorInvalidArgument("名称只能包含小写字母、数字、连字符(-)和点号(.)，且不能以点号或连字符开头或结尾，最长50字符")
	}
	items := []*model.ConfigItem{}
	fileNameMap := map[string]struct{}{}
	fileNamePattern := `^[a-z][a-z0-9-._]{0,48}[a-z0-9]$`
	for _, configItem := range req.ConfigItems {
		if _, ok := fileNameMap[configItem.FileName]; ok {
			return nil, bcode.ErrorServerInternalError("配置项名称不能重复")
		}
		matched, err = regexp.MatchString(fileNamePattern, configItem.FileName)
		if err != nil {
			return nil, bcode.ErrorInvalidArgument("配置项名称格式校验失败")
		}
		if !matched {
			return nil, bcode.ErrorInvalidArgument("配置项名称只能包含小写字母、数字、连字符(-)、点号(.)、下划线(_)，且不能以点号或连字符开头或结尾，最长50字符")
		}
		if configItem.UploadType == model.UploadTypeContent && len(configItem.Content) > model.MaxContentSize {
			return nil, bcode.ErrorServerInternalError("配置项内容不能超过%d字节", model.MaxContentSize)
		}
		if configItem.UploadType == model.UploadTypeFile && configItem.FileUK == "" {
			return nil, bcode.ErrorServerInternalError("文件上传类型必须指定文件UK")
		}
		fileNameMap[configItem.FileName] = struct{}{}
		items = append(items, &model.ConfigItem{
			FileName:    configItem.FileName,
			UploadType:  configItem.UploadType,
			Content:     configItem.Content,
			ContentType: configItem.ContentType,
			FileUK:      configItem.FileUK,
		})
	}
	return &model.ConfigEntity{
		WorkspaceName: req.WorkspaceName,
		Name:          req.Name,
		ConfigType:    req.ConfigType,
		SecretType:    req.SecretType,
		Managers:      req.Managers,
		Members:       req.Members,
		Description:   req.Description,
		Creator:       account,
		ConfigItems:   items,
		CreateBy:      req.CreateBy,
	}, nil
}

// ConvertToFederationConfigMap 将请求转换为ConfigMap CR
func (s *ConfigService) ConvertToFederationConfigMap(ctx context.Context, entity *model.ConfigEntity) (*v1alpha1.FederationConfigMap, error) {
	account, ok := ctx.Value(constant.UserCtxKey).(string)
	if !ok {
		return nil, bcode.ErrorServerInternalError("从上下文中获取用户失败")
	}
	data := map[string]string{}
	binaryData := map[string][]byte{}
	for _, config := range entity.ConfigItems {
		switch config.UploadType {
		case model.UploadTypeFile:
			content, err := s3.Instance().GetObject(ctx, s.Properties.MustGetString("config.bucket"), config.FileUK)
			if err != nil {
				return nil, fmt.Errorf("获取文件失败, err: %v", err)
			}
			binaryData[config.FileName] = content
		case model.UploadTypeContent:
			data[config.FileName] = config.Content
		default:
			return nil, fmt.Errorf("无效的上传类型: %v", config.UploadType)
		}
	}
	configMapCR := v1alpha1.FederationConfigMap{
		ObjectMeta: metav1.ObjectMeta{
			Name:      entity.Name,
			Namespace: getNamespaceName(entity.WorkspaceName),
			Labels: map[string]string{
				"creator": account,
			},
			Annotations: map[string]string{
				"description": entity.Description,
			},
		},
		Spec: v1alpha1.FederationConfigMapSpec{
			WorkspaceName: entity.WorkspaceName,
			BinaryData:    binaryData,
			Data:          data,
		},
	}
	return &configMapCR, nil
}

// ConvertToFederationSecret 将请求转换为Secret CR
func (s *ConfigService) ConvertToFederationSecret(ctx context.Context, entity *model.ConfigEntity) (*v1alpha1.FederationSecret, error) {
	account, ok := ctx.Value(constant.UserCtxKey).(string)
	if !ok {
		return nil, bcode.ErrorServerInternalError("从上下文中获取用户失败")
	}
	stringData := map[string]string{}
	binaryData := map[string][]byte{}
	for _, config := range entity.ConfigItems {
		switch config.UploadType {
		case model.UploadTypeFile:
			content, err := s3.Instance().GetObject(ctx, s.Properties.MustGetString("config.bucket"), config.FileUK)
			if err != nil {
				return nil, fmt.Errorf("获取文件失败, err: %v", err)
			}
			binaryData[config.FileName] = content
		case model.UploadTypeContent:
			stringData[config.FileName] = config.Content
		default:
			return nil, fmt.Errorf("无效的上传类型: %v", config.UploadType)
		}
	}
	secretCR := v1alpha1.FederationSecret{
		ObjectMeta: metav1.ObjectMeta{
			Name:      entity.Name,
			Namespace: getNamespaceName(entity.WorkspaceName),
			Labels: map[string]string{
				"creator": account,
			},
			Annotations: map[string]string{
				"description": entity.Description,
			},
		},
		Spec: v1alpha1.FederationSecretSpec{
			WorkspaceName: entity.WorkspaceName,
			StringData:    stringData,
			Data:          binaryData,
			Type:          corev1.SecretType(entity.SecretType),
		},
	}
	return &secretCR, nil
}

// CreateConfig 实现创建配置接口
func (s *ConfigService) CreateConfig(ctx context.Context, req *v1.CreateOrUpdateConfigRequest) (*v1.CreateConfigResponse, error) {
	account, ok := ctx.Value(constant.UserCtxKey).(string)
	if !ok {
		return nil, bcode.ErrorServerInternalError("从上下文中获取用户失败")
	}
	role, err := s.RbacService.GetUserRoleInWorkspace(ctx, req.WorkspaceName, account)
	if err != nil {
		return nil, fmt.Errorf("获取用户角色失败, err: %v", err)
	}
	if role == RoleWorkspaceNone {
		return nil, bcode.ErrorPermissionNotAllowed("用户[%s]没有权限获取配置列表", account)
	}
	configEntity := &model.ConfigEntity{
		WorkspaceName: req.WorkspaceName,
		Name:          req.Name,
	}
	entitys, err := s.Store.List(ctx, configEntity, nil)
	if err != nil && !apierrors.IsNotFound(err) {
		return nil, fmt.Errorf("获取配置失败, err: %v", err)
	}
	if len(entitys) > 0 {
		return nil, bcode.ErrorServerInternalError("配置已存在")
	}
	entity, err := s.ConvertToConfigEntity(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("创建配置实体失败, err: %v", err)
	}
	err = s.Store.Add(ctx, entity)
	if err != nil {
		return nil, fmt.Errorf("保存配置实体失败, err: %v", err)
	}

	var configCR client.Object
	switch req.ConfigType {
	case model.ConfigTypeConfigMap:
		configCR, err = s.ConvertToFederationConfigMap(ctx, entity)
	default:
		configCR, err = s.ConvertToFederationSecret(ctx, entity)
	}
	if err != nil {
		return nil, fmt.Errorf("创建配置失败, err: %v", err)
	}
	err = multicluster.Instance().GetLocalCluster().Direct().Create(ctx, configCR)
	if err != nil {
		return nil, fmt.Errorf("创建配置失败, err: %v", err)
	}
	return &v1.CreateConfigResponse{}, nil
}

// ListConfigs 实现获取配置列表接口
func (s *ConfigService) ListConfigs(ctx context.Context, req *v1.ListConfigsRequest) (*v1.ListConfigsResponse, error) {
	//获取权限
	account, ok := ctx.Value(constant.UserCtxKey).(string)
	if !ok {
		return nil, bcode.ErrorServerInternalError("从上下文中获取用户失败")
	}
	role, err := s.RbacService.GetUserRoleInWorkspace(ctx, req.WorkspaceName, account)
	if err != nil {
		return nil, fmt.Errorf("获取用户角色失败, err: %v", err)
	}
	if role == RoleWorkspaceNone {
		return nil, bcode.ErrorPermissionNotAllowed("用户[%s]没有权限获取配置列表", account)
	}
	listOptions := &datastore.ListOptions{
		Page:     int(req.Page),
		PageSize: int(req.PageSize),
		SortBy: []datastore.SortOption{
			{
				Key:   "updateTime",
				Order: datastore.SortOrderDescending,
			},
		},
	}
	if req.CreateBy != "" {
		listOptions.FilterOptions.In = append(listOptions.FilterOptions.In, datastore.InQueryOption{
			Key:    "createBy",
			Values: []string{req.CreateBy},
		})
	}
	if req.OnlyPermission && role != RolePlatformAdmin {
		listOptions.FilterOptions.In = append(listOptions.FilterOptions.In, datastore.InQueryOption{
			Key:    "createBy",
			Values: []string{model.CreateByWorkspace},
		})
	}

	if req.OnlyPermission && role != RolePlatformAdmin && role != RoleWorkspaceAdmin {
		listOptions.FilterOptions.In = append(listOptions.FilterOptions.In, datastore.InQueryOption{
			Key:    "members",
			Values: []string{account},
		})
	}
	if req.WorkspaceName != "" {
		listOptions.FilterOptions.In = append(listOptions.FilterOptions.In, datastore.InQueryOption{
			Key:    "workspaceName",
			Values: []string{req.WorkspaceName},
		})
	}
	if req.Name != "" {
		listOptions.FilterOptions.Queries = append(listOptions.FilterOptions.Queries, datastore.FuzzyQueryOption{
			Key:   "name",
			Query: req.Name,
		})
	}
	if req.ConfigType != "" {
		listOptions.FilterOptions.In = append(listOptions.FilterOptions.In, datastore.InQueryOption{
			Key:    "configType",
			Values: []string{req.ConfigType},
		})
	}

	configs, err := s.Store.List(ctx, &model.ConfigEntity{}, listOptions)
	if err != nil {
		return nil, fmt.Errorf("获取配置列表失败, err: %v", err)
	}
	configList := s.formatConfig(ctx, configs, role)
	total, err := s.Store.Count(ctx, &model.ConfigEntity{}, &listOptions.FilterOptions)
	if err != nil {
		return nil, fmt.Errorf("获取配置列表失败, err: %v", err)
	}

	return &v1.ListConfigsResponse{
		Configs: configList,
		Total:   int32(total),
	}, nil
}

func (s *ConfigService) formatConfig(ctx context.Context, entitys []datastore.Entity, role string) []*v1.Config {
	//获取权限
	account, _ := ctx.Value(constant.UserCtxKey).(string)
	var configList []*v1.Config
	for _, entity := range entitys {
		config := entity.(*model.ConfigEntity)
		permissions := s.GetConfigPermissions(ctx, config, account, role)
		configList = append(configList, &v1.Config{
			Name:        config.Name,
			ConfigType:  config.ConfigType,
			SecretType:  config.SecretType,
			Managers:    config.Managers,
			Members:     config.Members,
			Description: config.Description,
			ConfigItems: func(items []*model.ConfigItem) []*v1.ConfigItem {
				var configItems []*v1.ConfigItem
				for _, item := range items {
					configItems = append(configItems, &v1.ConfigItem{
						FileName:    item.FileName,
						UploadType:  item.UploadType,
						Content:     item.Content,
						ContentType: item.ContentType,
						FileUK:      item.FileUK,
						File:        item.FileName,
					})
				}
				return configItems
			}(config.ConfigItems),
			CreateTime:  util.TimeFormat(config.BaseModel.CreateTime),
			UpdateTime:  util.TimeFormat(config.BaseModel.UpdateTime),
			Creator:     config.Creator,
			Permissions: permissions,
			CreateBy:    config.CreateBy,
		})
	}
	return configList
}

// UpdateConfig 实现更新配置接口
func (s *ConfigService) UpdateConfig(ctx context.Context, req *v1.CreateOrUpdateConfigRequest) (*v1.UpdateConfigResponse, error) {
	//获取权限
	account, ok := ctx.Value(constant.UserCtxKey).(string)
	if !ok {
		return nil, bcode.ErrorServerInternalError("从上下文中获取用户失败")
	}
	role, err := s.RbacService.GetUserRoleInWorkspace(ctx, req.WorkspaceName, account)
	if err != nil {
		return nil, fmt.Errorf("获取用户角色失败, err: %v", err)
	}
	if role == RoleWorkspaceNone {
		return nil, bcode.ErrorPermissionNotAllowed("用户[%s]没有权限获取配置列表", account)
	}
	configEntity := &model.ConfigEntity{
		WorkspaceName: req.WorkspaceName,
		Name:          req.Name,
	}
	entitys, err := s.Store.List(ctx, configEntity, nil)
	if err != nil {
		return nil, fmt.Errorf("获取配置失败, err: %v", err)
	}
	if len(entitys) == 0 {
		return nil, bcode.ErrorServerInternalError("配置不存在")
	}
	config := entitys[0].(*model.ConfigEntity)
	if s.GetConfigPermissions(ctx, config, account, role)[model.ConfigPermissionUpdate] == "false" {
		return nil, bcode.ErrorPermissionNotAllowed("用户[%s]没有权限更新配置[%s:%s]", account, req.WorkspaceName, req.Name)
	}
	entity, err := s.ConvertToConfigEntity(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("创建配置实体失败, err: %v", err)
	}

	switch req.ConfigType {
	case model.ConfigTypeConfigMap:
		newCR, err := s.ConvertToFederationConfigMap(ctx, entity)
		if err != nil {
			return nil, fmt.Errorf("创建配置CR失败, err: %v", err)
		}
		configMapCR := v1alpha1.FederationConfigMap{}
		err = multicluster.Instance().GetLocalCluster().Direct().Get(ctx, client.ObjectKey{Name: req.Name, Namespace: getNamespaceName(req.WorkspaceName)}, &configMapCR)
		if err != nil {
			return nil, fmt.Errorf("获取配置失败, err: %v", err)
		}
		configMapCR.Labels = newCR.Labels
		configMapCR.Annotations = newCR.Annotations
		configMapCR.Spec.Data = newCR.Spec.Data
		configMapCR.Spec.BinaryData = newCR.Spec.BinaryData
		err = multicluster.Instance().GetLocalCluster().Direct().Update(ctx, &configMapCR)
		if err != nil {
			return nil, fmt.Errorf("更新配置失败, err: %v", err)
		}
	case model.ConfigTypeSecret:
		newCR, err := s.ConvertToFederationSecret(ctx, entity)
		if err != nil {
			return nil, fmt.Errorf("创建配置CR失败, err: %v", err)
		}
		secretCR := v1alpha1.FederationSecret{}
		err = multicluster.Instance().GetLocalCluster().Direct().Get(ctx, client.ObjectKey{Name: req.Name, Namespace: getNamespaceName(req.WorkspaceName)}, &secretCR)
		if err != nil {
			return nil, fmt.Errorf("获取配置失败, err: %v", err)
		}
		secretCR.Labels = newCR.Labels
		secretCR.Annotations = newCR.Annotations
		secretCR.Spec.Data = newCR.Spec.Data
		secretCR.Spec.StringData = newCR.Spec.StringData
		secretCR.Spec.Type = newCR.Spec.Type
		err = multicluster.Instance().GetLocalCluster().Direct().Update(ctx, &secretCR)
		if err != nil {
			return nil, fmt.Errorf("更新配置失败, err: %v", err)
		}
	}
	config.SecretType = entity.SecretType
	config.Managers = entity.Managers
	config.Members = entity.Members
	config.Description = entity.Description
	config.ConfigItems = entity.ConfigItems
	err = s.Store.Put(ctx, config)
	if err != nil {
		return nil, fmt.Errorf("保存配置实体失败, err: %v", err)
	}
	return &v1.UpdateConfigResponse{}, nil
}

// GetConfigPermissions 获取配置权限
func (s *ConfigService) GetConfigPermissions(ctx context.Context, config *model.ConfigEntity, account string, role string) map[string]string {
	permissions := map[string]string{
		model.ConfigPermissionUpdate:   "false",
		model.ConfigPermissionDelete:   "false",
		model.ConfigPermissionDownload: "false",
	}
	if role == RolePlatformAdmin || (config.CreateBy == model.CreateByWorkspace && (role == RoleWorkspaceAdmin || util.StringInSlice(account, config.Managers))) {
		permissions[model.ConfigPermissionUpdate] = "true"
		permissions[model.ConfigPermissionDelete] = "true"
		permissions[model.ConfigPermissionDownload] = "true"
	} else if config.CreateBy == model.CreateByWorkspace && util.StringInSlice(account, config.Members) {
		permissions[model.ConfigPermissionUpdate] = "true"
		permissions[model.ConfigPermissionDownload] = "true"
	}
	return permissions
}

// DeleteConfig 实现删除配置接口
func (s *ConfigService) DeleteConfig(ctx context.Context, req *v1.DeleteConfigRequest) (*v1.DeleteConfigResponse, error) {
	//获取权限
	account, ok := ctx.Value(constant.UserCtxKey).(string)
	if !ok {
		return nil, bcode.ErrorServerInternalError("从上下文中获取用户失败")
	}
	role, err := s.RbacService.GetUserRoleInWorkspace(ctx, req.WorkspaceName, account)
	if err != nil {
		return nil, fmt.Errorf("获取用户角色失败, err: %v", err)
	}
	if role == RoleWorkspaceNone {
		return nil, bcode.ErrorPermissionNotAllowed("用户[%s]没有权限获取配置列表", account)
	}
	configEntity := &model.ConfigEntity{
		WorkspaceName: req.WorkspaceName,
		Name:          req.Name,
	}
	entitys, err := s.Store.List(ctx, configEntity, nil)
	if err != nil {
		return nil, fmt.Errorf("获取配置失败, err: %v", err)
	}
	if len(entitys) == 0 {
		return nil, bcode.ErrorServerInternalError("配置不存在")
	}
	config := entitys[0].(*model.ConfigEntity)
	if s.GetConfigPermissions(ctx, config, account, role)[model.ConfigPermissionDelete] == "false" {
		return nil, bcode.ErrorPermissionNotAllowed("用户[%s]没有权限删除配置[%s:%s]", account, req.WorkspaceName, req.Name)
	}

	switch config.ConfigType {
	case model.ConfigTypeConfigMap:
		configCR := v1alpha1.FederationConfigMap{}
		err = multicluster.Instance().GetLocalCluster().Direct().Get(ctx, client.ObjectKey{Name: req.Name, Namespace: getNamespaceName(req.WorkspaceName)}, &configCR)
		if err != nil && !apierrors.IsNotFound(err) {
			return nil, fmt.Errorf("获取配置失败, err: %v", err)
		}
		if !apierrors.IsNotFound(err) {
			err = multicluster.Instance().GetLocalCluster().Direct().Delete(ctx, &configCR)
			if err != nil {
				return nil, fmt.Errorf("删除配置失败, err: %v", err)
			}
		}
	case model.ConfigTypeSecret:
		secretCR := v1alpha1.FederationSecret{}
		err = multicluster.Instance().GetLocalCluster().Direct().Get(ctx, client.ObjectKey{Name: req.Name, Namespace: getNamespaceName(req.WorkspaceName)}, &secretCR)
		if err != nil && !apierrors.IsNotFound(err) {
			return nil, fmt.Errorf("获取配置失败, err: %v", err)
		}
		if !apierrors.IsNotFound(err) {
			err = multicluster.Instance().GetLocalCluster().Direct().Delete(ctx, &secretCR)
			if err != nil {
				return nil, fmt.Errorf("删除配置失败, err: %v", err)
			}
		}
	}
	err = s.Store.Delete(ctx, config)
	if err != nil {
		return nil, fmt.Errorf("删除配置实体失败, err: %v", err)
	}
	return &v1.DeleteConfigResponse{}, nil
}

// CheckConfigExistence 实现检查配置接口
func (s *ConfigService) CheckConfigExistence(ctx context.Context, req *v1.CheckConfigExistenceRequest) (*v1.CheckConfigExistenceResponse, error) {
	configEntity := &model.ConfigEntity{
		WorkspaceName: req.WorkspaceName,
		Name:          req.Name,
	}
	entitys, err := s.Store.List(ctx, configEntity, nil)
	if err != nil {
		return nil, fmt.Errorf("获取配置失败, err: %v", err)
	}
	if len(entitys) > 0 {
		return &v1.CheckConfigExistenceResponse{
			Status: int32(422),
			Errors: "当前工作空间存在同名配置，请更换名称后重试",
		}, nil
	}
	return &v1.CheckConfigExistenceResponse{
		Status: int32(200),
		Errors: "",
	}, nil
}
