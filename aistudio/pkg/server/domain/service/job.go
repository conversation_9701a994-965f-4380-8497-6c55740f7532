package service

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"sort"
	"strconv"
	"strings"
	"text/template"
	"time"

	"git.lianjia.com/cloudnative/kic/kic-platform/aistudio/pkg/server/domain/service/dto"
	workspacev1 "git.lianjia.com/cloudnative/kic/kic-platform/apis/aistudio/workspace/v1"
	thirdnotice "git.lianjia.com/cloudnative/kic/kic-platform/pkg/third_party/multichannel_notification"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/third_party/openobserve"
	temporalSdkClient "go.temporal.io/sdk/client"
	"go.temporal.io/sdk/temporal"
	"go.temporal.io/sdk/worker"
	"go.temporal.io/sdk/workflow"

	helper2 "git.lianjia.com/cloudnative/kic/kic-platform/aistudio/pkg/server/domain/service/helper"

	"git.lianjia.com/cloudnative/kic/kic-platform/aistudio/pkg/constant"
	"git.lianjia.com/cloudnative/kic/kic-platform/aistudio/pkg/server/domain/model"
	"git.lianjia.com/cloudnative/kic/kic-platform/aistudio/pkg/server/domain/repository"
	"git.lianjia.com/cloudnative/kic/kic-platform/aistudio/pkg/server/domain/service/job/helper"
	"git.lianjia.com/cloudnative/kic/kic-platform/aistudio/pkg/server/domain/service/job/provider"
	"git.lianjia.com/cloudnative/kic/kic-platform/aistudio/pkg/server/event"
	"git.lianjia.com/cloudnative/kic/kic-platform/apis/aistudio/bcode"
	jobv1 "git.lianjia.com/cloudnative/kic/kic-platform/apis/aistudio/job/v1"
	automlv1alpha1 "git.lianjia.com/cloudnative/kic/kic-platform/pkg/apis/automl.kcs.io/v1alpha1"
	cloudfsv1alpha1 "git.lianjia.com/cloudnative/kic/kic-platform/pkg/apis/cloudfs.kcs.io/v1alpha1"
	schedulingv1alpha1 "git.lianjia.com/cloudnative/kic/kic-platform/pkg/apis/scheduling.kcs.io/v1alpha1"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/infrastructure/datastore"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/multicluster"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/property"
	temporalClient "git.lianjia.com/cloudnative/kic/kic-platform/pkg/third_party/temporal"
	vela "git.lianjia.com/cloudnative/kic/kic-platform/pkg/third_party/velaux"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/util"
	kratoslog "github.com/go-kratos/kratos/v2/log"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"google.golang.org/protobuf/types/known/emptypb"
	corev1 "k8s.io/api/core/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/apimachinery/pkg/util/wait"
	"k8s.io/klog/v2"
	"sigs.k8s.io/controller-runtime/pkg/client"
	volcanobatchv1alpha1 "volcano.sh/apis/pkg/apis/batch/v1alpha1"
)

const (
	DefaultImage = "harbor.intra.ke.com/kcs/automl/tensorflow:2.16.1"
)

var _ Interface = (*JobService)(nil)

var _ jobv1.JobServiceHTTPServer = (*JobService)(nil)

func NewJobService() *JobService {
	return &JobService{
		jobProviders: make(map[string]provider.Provider, 4),
	}
}

const Gibibyte = 1024 * 1024 * 1024

const (
	JobTemplateState_Active   = "active"
	JobTemplateState_Inactive = "inactive"

	JobTriggerRecordStream = "job_trigger_record"
	noticeKey              = "job_trigger"
)

type JobService struct {
	Logger           *kratoslog.Helper   `inject:"logger"`
	Store            datastore.DataStore `inject:"datastore"`
	QueueService     *QueueService       `inject:""`
	PlatformService  *PlatformService    `inject:""`
	RbacService      *RbacService        `inject:""`
	WorkspaceService *WorkspaceService   `inject:""`
	ClusterManager   *ClusterManager     `inject:"clusterManager"`
	CloudFSService   *CloudFSService     `inject:"cloudFSService"`
	EventBridge      *event.Bridge       `inject:""`
	jobProviders     map[string]provider.Provider
	Properties       property.EnvironmentProperty `inject:""`

	TemporalClient *temporalClient.Client `inject:""`
	NoticeClient   *thirdnotice.Client    `inject:""`
}

func (t *JobService) Init(ctx context.Context, properties property.EnvironmentProperty) error {
	// 注册 Temporal 工作流和活动
	c := temporalClient.Instance()

	w := worker.New(c.Client, c.Cfg.TaskQueue, worker.Options{})
	w.RegisterWorkflow(t.BasicScheduledTaskWorkflow)
	w.RegisterActivity(t.BasicScheduledTaskMethod)
	go func() {
		if err := w.Run(worker.InterruptCh()); err != nil {
			t.Logger.Errorf("Worker error: %v", err)
		}
	}()
	t.TemporalClient = c

	t.NoticeClient = thirdnotice.Instance()
	if t.NoticeClient == nil {
		cfg := &thirdnotice.Config{
			URL:             properties.MustGetString("url"),
			DefaultAppId:    properties.MustGetString("defaultAppId"),
			DefaultAppToken: properties.MustGetString("defaultAppToken"),
		}
		newClient, err := thirdnotice.NewClient(ctx, cfg)
		if err != nil {
			return err
		}
		t.NoticeClient = newClient
	}
	return nil
}

// BasicScheduledTaskWorkflow 基础定时调度任务工作流
func (t *JobService) BasicScheduledTaskWorkflow(ctx workflow.Context, triggerName string, job *jobv1.Job) error {
	ctx = workflow.WithActivityOptions(ctx, workflow.ActivityOptions{
		StartToCloseTimeout: 30 * time.Second, // 活动执行总超时时间（从开始到结束），控制整个Activity执行的最长时间（包括所有重试）
		RetryPolicy: &temporal.RetryPolicy{
			InitialInterval:    1 * time.Second,  // 首次重试间隔时间
			BackoffCoefficient: 2.0,              // 重试间隔增长系数
			MaximumInterval:    10 * time.Second, // 最大重试间隔时间上限
			MaximumAttempts:    3,                // 最大尝试次数（包括首次执行）
		},
	})
	return workflow.ExecuteActivity(ctx, t.BasicScheduledTaskMethod, triggerName, job).Get(ctx, nil)
}

// BasicScheduledTaskMethod 基础定时调度任务工作方法
func (t *JobService) BasicScheduledTaskMethod(ctx context.Context, triggerName string, job *jobv1.Job) error {
	// 获取触发器信息
	triggerEntity, err := repository.GetTriggerForJobTemplate(ctx, t.Store, job.JobTemplateName, triggerName)
	if err != nil {
		if errors.Is(err, datastore.ErrRecordNotExist) {
			return bcode.ErrorServerInternalError("trigger[%s] not found", triggerName)
		}
		return bcode.ErrorServerInternalError("get trigger[%s] failed:%+v", triggerName, err)
	}

	// 提交任务
	createJobRequest := helper.ConvertJobToCreateJobRequest(job)
	userCtx := context.WithValue(ctx, constant.UserCtxKey, job.Creator)
	res, err := t.SubmitJob(userCtx, createJobRequest)
	if err != nil {
		kratoslog.Errorf("submit job failed:%+v", err)
		go t.RecordJobTriggerRecord(ctx, job, triggerName, triggerEntity.TriggerType, "failed")
		return bcode.ErrorServerInternalError("submit job failed:%+v", err)
	}
	job.Name = res.JobName

	// 更新 trigger 信息
	err = t.UpdateJobTriggerEntity(ctx, triggerName, triggerEntity)
	if err != nil {
		return bcode.ErrorServerInternalError("update trigger[%s] failed:%+v", triggerName, err)
	}
	// 通知用户
	//go t.SendJobSubmissionNotification(ctx, job, triggerName, triggerEntity)

	// 记录执行记录
	go t.RecordJobTriggerRecord(ctx, job, triggerName, triggerEntity.TriggerType, "success")

	return nil
}

// UpdateJobTriggerEntity 更新触发器信息
func (t *JobService) UpdateJobTriggerEntity(ctx context.Context, triggerName string, triggerEntity *model.TriggerEntity) error {
	handle := t.TemporalClient.Client.ScheduleClient().GetHandle(ctx, triggerName)
	desc, err := handle.Describe(ctx)
	if err != nil || desc == nil {
		return bcode.ErrorServerInternalError("get schedule failed:%+v", err)
	}

	timeNow := time.Now()
	endAt := timeNow
	if triggerEntity.TimerTriggerOptions.PeriodOfValidity != "" {
		// 解析结束时间
		timestamps := strings.Split(triggerEntity.TimerTriggerOptions.PeriodOfValidity, ",")
		if len(timestamps) == 2 {
			endTimestamp, err := strconv.ParseInt(timestamps[1], 10, 64)
			if err != nil {
				return bcode.ErrorServerInternalError("解析结束时间戳时出错")
			}
			endAt = time.Unix(endTimestamp, 0)
		}
	}

	// 1. 先判断是否已经为完成
	if triggerEntity.TimerTriggerOptions.MaxCount != 0 && desc.Schedule.State.RemainingActions == 0 {
		triggerEntity.NumActions = int32(desc.Info.NumActions)
		triggerEntity.NextActionTimes = []string{}
		triggerEntity.TriggerState = jobv1.TriggerState_TriggerState_Completed.String()
	} else if endAt.Before(timeNow) {
		// 2. 判断任务是否过期
		triggerEntity.NumActions = int32(desc.Info.NumActions)
		triggerEntity.TriggerState = jobv1.TriggerState_TriggerState_Expired.String()
		triggerEntity.NextActionTimes = []string{}
	} else if desc.Schedule.State.Paused {
		// 3. 判断任务是否暂停
		triggerEntity.NumActions = int32(desc.Info.NumActions)
		triggerEntity.NextActionTimes = []string{}
		triggerEntity.TriggerState = jobv1.TriggerState_TriggerState_Paused.String()
	} else {
		// 4. 判断任务是否正在执行
		triggerEntity.NumActions = int32(desc.Info.NumActions)
		triggerEntity.NextActionTimes = util.TimesFormat(desc.Info.NextActionTimes)
		triggerEntity.TriggerState = jobv1.TriggerState_TriggerState_Executing.String()
	}

	err = t.Store.Put(ctx, triggerEntity)
	if err != nil {
		return bcode.ErrorServerInternalError("update trigger[%s] failed:%+v", triggerName, err)
	}
	return nil
}

// SendJobSubmissionNotification 发送任务提交通知
func (t *JobService) SendJobSubmissionNotification(ctx context.Context, job *jobv1.Job, triggerName string, triggerEntity *model.TriggerEntity) {
	const notificationTemplate = `【AiStudio2.0平台】定时任务: {{.JobName}} 已提交
#### 
> 任务 ID: <font color="comment">{{.JobName}}</font>
> 任务名称：<font color="black">{{.JobDisplayName}}</font>
> 工作空间：<font>{{.WorkspaceName}}</font>
> 创建人: <font>{{.Creator}}</font>
> 触发器名称: <font>{{.TriggerName}}</font>
> 触发时间：<font color="info">{{.TriggerTime}}</font>
> 描述：<font color="info">{{.Description}}</font>`

	data := dto.JobTriggerRecord{
		JobName:         job.Name,
		JobDisplayName:  job.DisplayName,
		JobTemplateName: job.JobTemplateName,
		WorkspaceName:   job.WorkspaceName,
		TriggerName:     triggerName,
		TriggerType:     triggerEntity.TriggerType,
		TriggerTime:     util.TimeFormat(time.Now()),
		Creator:         job.Creator,
		Description:     job.Description,
	}

	var content bytes.Buffer
	tmpl, err := template.New("markdown").Parse(notificationTemplate)
	if err != nil {
		kratoslog.Errorf("Error parsing template: %v", err)
		return
	}

	if err := tmpl.Execute(&content, data); err != nil {
		kratoslog.Errorf("Error executing template: %v", err)
		return
	}

	if err := t.NoticeClient.SendWechatOfficialAccountMessage(ctx, content.String(), strings.Join(job.Members, ",")); err != nil {
		kratoslog.Errorf("Send notification error: %v", err)
	}
}

// RecordJobTriggerRecord 记录Job触发记录
func (t *JobService) RecordJobTriggerRecord(ctx context.Context, job *jobv1.Job, triggerName, triggerType, status string) {
	record := dto.JobTriggerRecord{
		JobName:         job.Name,
		JobDisplayName:  job.DisplayName,
		JobTemplateName: job.JobTemplateName,
		WorkspaceName:   job.WorkspaceName,
		TriggerTime:     util.TimeFormat(time.Now()),
		Creator:         job.Creator,
		Description:     job.Description,
		Status:          status,
	}
	if triggerName != "" {
		record.TriggerName = triggerName
	}
	if triggerType != "" {
		record.TriggerType = triggerType
	}

	kratoslog.Infof("[Job_Trigger] Writing job trigger record: %s", job.Name)
	if err := openobserve.Instance().WriteLogByJson(record, JobTriggerRecordStream); err != nil {
		kratoslog.Errorf("[Job_Trigger] Write record error: %v", err)
	}
}

// CreateJobTemplate 创建任务模板
func (t *JobService) CreateJobTemplate(ctx context.Context, template *jobv1.CreateOrUpdateJobTemplateRequest) (*emptypb.Empty, error) {
	// 获取创建者
	l, ok := ctx.Value(constant.UserCtxKey).(string)
	if !ok {
		return nil, bcode.ErrorServerInternalError("get user from context failed")
	}

	// 默认创建者为管理员
	if !util.StringInSlice(l, template.Managers) {
		template.Managers = append(template.Managers, l)
	}

	// 生成任务模板 Name，作为模板的 ID
	template.Name = util.GenerateRandomStringWithPrefix("st", 4)

	// 更新 Job 信息
	template.Job.Creator = l
	template.Job.DisplayName = template.DisplayName
	template.Job.WorkspaceName = template.WorkspaceName
	template.Job.Region = template.Region
	template.Job.Labels = template.Labels
	template.Job.JobTemplateName = template.Name
	template.Job.Description = template.Description
	template.Job.Members = append(template.Job.Members, template.Managers...)
	template.Job.Members = append(template.Job.Members, template.Members...)

	// 根据不同类型触发器进行处理
	for _, trigger := range template.Triggers {
		// 定时调度类型触发器
		if trigger.TriggerType == jobv1.TriggerType_Timer {
			if trigger.TimeTriggerOptions == nil {
				return nil, bcode.ErrorInvalidArgument("trigger options is nil")
			}
			// 生成触发器的唯一 Name
			triggerName := util.GenerateRandomStringWithPrefix("trigger", 4)
			trigger.TriggerName = triggerName
			// 如果类型不是仅保存数据，创建 Temporal Schedule
			err := t.CreateTemporalSchedule(ctx, trigger, template.Job, template.ExecutionStrategy)
			if err != nil {
				return nil, bcode.ErrorServerInternalError("create temporal schedule failed, err: %v", err)
			}
			// 更新 Message 信息
			trigger.Message = fmt.Sprintf("trigger[%s] was created by user %s at %s", triggerName, l, util.TimeFormat(time.Now()))
			//  trigger入库
			triggerEntity := helper.ConvertTriggerToTriggerModel(trigger)
			triggerEntity.JobTemplateName = template.Name
			err = t.Store.Add(ctx, triggerEntity)
			if err != nil {
				return nil, bcode.ErrorServerInternalError("create trigger[%s] failed, err: %v", triggerName, err)
			}
		}
	}

	timeNow := time.Now()
	jobTemplateEntity := helper.ConvertToJobTemplateModel(template)
	jobTemplateEntity.Creator = l
	jobTemplateEntity.CreateTime = timeNow
	jobTemplateEntity.UpdateTime = timeNow

	err := t.Store.Add(ctx, jobTemplateEntity)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("create job template failed:%+v", err)
	}
	return &emptypb.Empty{}, nil
}

// CreateTemporalSchedule 创建Temporal Schedule
func (t *JobService) CreateTemporalSchedule(ctx context.Context, trigger *jobv1.Trigger, job *jobv1.Job, strategy jobv1.ExecutionStrategy) error {
	// 获取 ScheduleId 和 workflowId 为触发器 Name
	workflowID := trigger.TriggerName
	scheduleID := trigger.TriggerName

	// 根据定时调度触发器参数获取 Temporal Schedule 参数
	spec, err := helper.ConvertTimerTriggerToScheduleSpec(trigger.TimeTriggerOptions)
	if err != nil {
		return bcode.ErrorServerInternalError("convert timer trigger to schedule spec error, err: %v", err)
	}

	// 创建 ScheduleOptions
	scheduleOptions := temporalSdkClient.ScheduleOptions{
		ID:   scheduleID,
		Spec: spec,
		Action: &temporalSdkClient.ScheduleWorkflowAction{
			ID:                       workflowID,
			Workflow:                 t.BasicScheduledTaskWorkflow,
			TaskQueue:                t.TemporalClient.Cfg.TaskQueue,
			WorkflowExecutionTimeout: 1 * time.Hour,    // 工作流执行总超时（控制整个Workflow的生命周期）
			WorkflowRunTimeout:       10 * time.Minute, // 工作流运行超时（控制Workflow的每次执行）
			RetryPolicy: &temporal.RetryPolicy{
				MaximumAttempts: 3, // 最大尝试次数（包括首次执行）
			},
			Args: []interface{}{trigger.TriggerName, job},
		},
	}

	if strategy == jobv1.ExecutionStrategy_ImmediateFirstTask {
		// 如果是立即执行第一个任务，则设置为立即执行
		scheduleOptions.TriggerImmediately = true
	} else if strategy == jobv1.ExecutionStrategy_SaveTemplateOnly {
		// 如果是仅保存模板，则设置为暂停状态
		scheduleOptions.Paused = true
	}

	// 设置最大运行次数
	if trigger.TimeTriggerOptions.MaxCount != 0 {
		scheduleOptions.RemainingActions = int(trigger.TimeTriggerOptions.MaxCount)
	}

	// 创建 Temporal Schedule
	handle, err := t.TemporalClient.Client.ScheduleClient().Create(ctx, scheduleOptions)
	if err != nil {
		return bcode.ErrorServerInternalError("create schedule failed:%+v", err)
	}
	// 获取 Temporal Schedule 信息
	desc, err := handle.Describe(ctx)
	if err != nil || desc == nil {
		return bcode.ErrorServerInternalError("get schedule failed:%+v", err)
	}

	// 获取是否为暂停状态
	if desc.Schedule.State.Paused == false {
		// 如果设置最大运行次数并且剩余运行次数为 0 代表已经运行了最大次数，更新状态为已经完成，更新下次运行时间为空
		if trigger.TimeTriggerOptions.MaxCount != 0 && desc.Schedule.State.RemainingActions == 0 {
			trigger.NumActions = int32(desc.Info.NumActions)
			trigger.NextActionTimes = []string{}
			trigger.State = jobv1.TriggerState_TriggerState_Completed
		} else {
			// 否则更新为执行中
			trigger.NumActions = int32(desc.Info.NumActions)
			trigger.NextActionTimes = util.TimesFormat(desc.Info.NextActionTimes)
			trigger.State = jobv1.TriggerState_TriggerState_Executing
		}
	} else {
		// 更新为暂停的状态
		trigger.NumActions = int32(desc.Info.NumActions)
		trigger.NextActionTimes = []string{}
		trigger.State = jobv1.TriggerState_TriggerState_Paused
	}

	return nil
}

// DeleteJobTemplate 删除任务模板
func (t *JobService) DeleteJobTemplate(ctx context.Context, request *jobv1.DeleteJobTemplateRequest) (*emptypb.Empty, error) {
	// 判断是否有删除权限
	account, ok := ctx.Value(constant.UserCtxKey).(string)
	if !ok {
		return nil, bcode.ErrorServerInternalError("get user from context failed")
	}

	jobTemplate, err := repository.GetJobTemplateForWorkspace(ctx, t.Store, request.WorkspaceName, request.JobTemplateName)
	if err != nil {
		if errors.Is(err, datastore.ErrRecordNotExist) {
			return nil, bcode.ErrorServerInternalError("job template[%s:%s] not found", request.WorkspaceName, request.JobTemplateName)
		}
		return nil, bcode.ErrorServerInternalError("get job template[%s:%s] failed:%+v", request.WorkspaceName, request.JobTemplateName, err)
	}

	// 只有平台管理员、空间管理员和模板管理员可以删除任务模板
	isPlatformAdmin, err := t.RbacService.IsPlatformAdmin(ctx, account)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("check platform admin error: %v", err)
	}
	isWorkspaceManager, err := t.WorkspaceService.IsWorkspaceManager(ctx, &workspacev1.IsWorkspaceManagerRequest{
		Account:       account,
		WorkspaceName: request.WorkspaceName,
	})
	if err != nil || isWorkspaceManager == nil {
		return nil, bcode.ErrorServerInternalError("check workspace manager error: %v", err)
	}
	if !isPlatformAdmin && !isWorkspaceManager.IsManager {
		if !util.StringInSlice(account, jobTemplate.Managers) {
			return nil, bcode.ErrorPermissionNotAllowed("permission denied")
		}
	}

	// 删除触发器
	for _, triggerName := range jobTemplate.Triggers {
		triggerEntity, err := repository.GetTriggerForJobTemplate(ctx, t.Store, request.JobTemplateName, triggerName)
		if err != nil {
			if errors.Is(err, datastore.ErrRecordNotExist) {
				return nil, bcode.ErrorServerInternalError("trigger[%s] not found", triggerName)
			}
			return nil, bcode.ErrorServerInternalError("get trigger[%s] failed:%+v", triggerName, err)
		}

		// 获取 Temporal Schedule
		handle := t.TemporalClient.Client.ScheduleClient().GetHandle(ctx, triggerName)
		_, err = handle.Describe(ctx)
		if err != nil {
			// 如果不存在，打印错误日志并且继续
			if !strings.HasPrefix(err.Error(), "workflow not found") {
				kratoslog.Errorf("get trigger[%s:%s] failed:%+v", request.JobTemplateName, triggerName, err)
				continue
			}
		}
		// 删除 Temporal Schedule
		err = handle.Delete(ctx)
		if err != nil {
			return nil, bcode.ErrorServerInternalError("delete trigger[%s:%s] failed:%+v", request.JobTemplateName, triggerName, err)
		}
		// 删除数据库
		err = t.Store.Delete(ctx, triggerEntity)
		if err != nil {
			return nil, bcode.ErrorServerInternalError("delete trigger[%s:%s] failed:%+v", request.JobTemplateName, triggerName, err)
		}
	}

	// 删除任务模板
	err = t.Store.Delete(ctx, jobTemplate)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("delete job template[%s:%s] failed:%+v", request.WorkspaceName, request.JobTemplateName, err)
	}
	return nil, nil
}

// GetJobTemplate 获取指定任务模板
func (t *JobService) GetJobTemplate(ctx context.Context, request *jobv1.GetJobTemplateRequest) (*jobv1.JobTemplate, error) {
	// 获取模板数据
	jobTemplate, err := repository.GetJobTemplateForWorkspace(ctx, t.Store, request.WorkspaceName, request.JobTemplateName)
	if err != nil {
		if errors.Is(err, datastore.ErrRecordNotExist) {
			return nil, bcode.ErrorServerInternalError("job template[%s:%s] not found", request.WorkspaceName, request.JobTemplateName)
		}
		return nil, bcode.ErrorServerInternalError("get job template[%s:%s] failed:%+v", request.WorkspaceName, request.JobTemplateName, err)
	}

	jobTemplateResult := helper.ConvertFromJobTemplateModel(jobTemplate)

	// 获取 Triggers 数据
	for _, triggerName := range jobTemplate.Triggers {
		triggerEntity, err := repository.GetTriggerForJobTemplate(ctx, t.Store, request.JobTemplateName, triggerName)
		if err != nil {
			if errors.Is(err, datastore.ErrRecordNotExist) {
				return nil, bcode.ErrorServerInternalError("trigger[%s] not found", triggerName)
			}
			return nil, bcode.ErrorServerInternalError("get trigger[%s] failed:%+v", triggerName, err)
		}
		jobTemplateResult.Triggers = append(jobTemplateResult.Triggers, helper.ConvertFromTriggerModel(triggerEntity))
	}

	return jobTemplateResult, nil
}

// UpdateJobTemplate 更新任务模板
func (t *JobService) UpdateJobTemplate(ctx context.Context, request *jobv1.CreateOrUpdateJobTemplateRequest) (*emptypb.Empty, error) {
	account, ok := ctx.Value(constant.UserCtxKey).(string)
	if !ok {
		return nil, bcode.ErrorServerInternalError("get user from context failed")
	}

	// 获取任务模板信息
	jobTemplate, err := repository.GetJobTemplateForWorkspace(ctx, t.Store, request.WorkspaceName, request.Name)
	if err != nil {
		if errors.Is(err, datastore.ErrRecordNotExist) {
			return nil, bcode.ErrorServerInternalError("job template[%s:%s] not found", request.WorkspaceName, request.Name)
		}
		return nil, bcode.ErrorServerInternalError("get job template[%s:%s] failed:%+v", request.WorkspaceName, request.Name, err)
	}

	// 判断权限
	isPlatformAdmin, err := t.RbacService.IsPlatformAdmin(ctx, account)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("check platform admin error: %v", err)
	}
	isWorkspaceManager, err := t.WorkspaceService.IsWorkspaceManager(ctx, &workspacev1.IsWorkspaceManagerRequest{
		Account:       account,
		WorkspaceName: request.WorkspaceName,
	})
	if err != nil || isWorkspaceManager == nil {
		return nil, bcode.ErrorServerInternalError("check workspace manager error: %v", err)
	}
	if !isPlatformAdmin && !isWorkspaceManager.IsManager {
		if !util.StringInSlice(account, jobTemplate.Managers) {
			return nil, bcode.ErrorPermissionNotAllowed("permission denied")
		}
	}
	// 更新模板信息
	jobTemplate.Description = request.Description
	jobTemplate.Labels = request.Labels
	jobTemplate.Members = request.Members
	jobTemplate.Managers = request.Managers

	// 更新 Job 信息
	jobTemplate.Job.Description = request.Description
	jobTemplate.Job.Labels = request.Labels
	jobTemplate.Job.Members = []string{}
	jobTemplate.Job.Members = append(jobTemplate.Job.Members, jobTemplate.Managers...)
	jobTemplate.Job.Members = append(jobTemplate.Job.Members, jobTemplate.Members...)
	jobTemplate.Job = helper.ConvertJobToJobModel(request.Job)

	// 更新 Trigger 信息
	var newTriggersName []string
	for _, trigger := range request.Triggers {
		if trigger.TriggerType == jobv1.TriggerType_Timer {
			if trigger.TimeTriggerOptions == nil {
				return nil, bcode.ErrorInvalidArgument("trigger options is nil")
			}
			// 如果 Trigger Name 不为空 代表更新 trigger
			if trigger.TriggerName != "" {
				newTriggersName = append(newTriggersName, trigger.TriggerName)
				triggerEntity, err := repository.GetTriggerForJobTemplate(ctx, t.Store, request.Name, trigger.TriggerName)
				if err != nil {
					if errors.Is(err, datastore.ErrRecordNotExist) {
						return nil, bcode.ErrorServerInternalError("trigger[%s] not found", trigger.TriggerName)
					}
					return nil, bcode.ErrorServerInternalError("get trigger[%s] failed:%+v", trigger.TriggerName, err)
				}
				// 更新触发器
				triggerEntity.UpdateTime = time.Now()
				if triggerEntity.TriggerType == jobv1.TriggerType_Timer.String() {
					triggerEntity.TimerTriggerOptions.Interval = trigger.TimeTriggerOptions.Interval
					triggerEntity.TimerTriggerOptions.Cycle = helper.ConvertToCycleModel(trigger.TimeTriggerOptions.Cycle)
					triggerEntity.TimerTriggerOptions.CronExpr = trigger.TimeTriggerOptions.CronExpr
					triggerEntity.TimerTriggerOptions.PeriodOfValidity = trigger.TimeTriggerOptions.PeriodOfValidity
					triggerEntity.TimerTriggerOptions.MaxCount = int(trigger.TimeTriggerOptions.MaxCount)
					triggerEntity.TimerTriggerOptions.SkipTime = helper.ConvertToCycleModel(trigger.TimeTriggerOptions.SkipTime)
				}

				if trigger.TriggerType == jobv1.TriggerType_Timer {
					// 更新 Temporal Schedule 如果不存在则重新创建
					err = t.UpdateTemporalSchedule(ctx, helper.ConvertFromTriggerModel(triggerEntity))
					if err != nil {
						if errors.Is(err, bcode.ErrorTemporalScheduleNotExists("temporal schedule not exist")) {
							err := t.CreateTemporalSchedule(ctx, trigger, request.Job, request.ExecutionStrategy)
							if err != nil {
								return nil, bcode.ErrorServerInternalError("temporal schedule not exist, create schedule failed:%+v", err)
							}
						} else {
							return nil, bcode.ErrorServerInternalError("update schedule failed:%+v", err)
						}
					}
					err = t.UpdateJobTriggerEntity(ctx, trigger.TriggerName, triggerEntity)
					if err != nil {
						return nil, err
					}
				}

			} else {
				// 如果为空说明需要新增 trigger 配置
				triggerName := util.GenerateRandomStringWithPrefix("trigger", 4)
				trigger.TriggerName = triggerName
				err := t.CreateTemporalSchedule(ctx, trigger, request.Job, request.ExecutionStrategy)
				if err != nil {
					return nil, bcode.ErrorServerInternalError("create temporal schedule failed, err: %v", err)
				}
				trigger.Message = fmt.Sprintf("trigger[%s] was created by user %s at %s", triggerName, account, util.TimeFormat(time.Now()))
				//  trigger入库
				triggerEntity := helper.ConvertTriggerToTriggerModel(trigger)
				triggerEntity.JobTemplateName = request.Name
				err = t.Store.Add(ctx, triggerEntity)
				if err != nil {
					return nil, bcode.ErrorServerInternalError("create trigger[%s] failed, err: %v", triggerName, err)
				}
				newTriggersName = append(newTriggersName, trigger.TriggerName)
			}
		}
	}

	// 删除 Trigger
	for _, triggerName := range jobTemplate.Triggers {
		// 如果已经删除 Trigger 需要同步删除Temporal Schedule
		if !util.StringInSlice(triggerName, newTriggersName) {
			triggerEntity, err := repository.GetTriggerForJobTemplate(ctx, t.Store, request.Name, triggerName)
			if err != nil {
				if errors.Is(err, datastore.ErrRecordNotExist) {
					return nil, bcode.ErrorServerInternalError("trigger[%s] not found", triggerName)
				}
				return nil, bcode.ErrorServerInternalError("get trigger[%s] failed:%+v", triggerName, err)
			}

			// 获取 Temporal Schedule
			handle := t.TemporalClient.Client.ScheduleClient().GetHandle(ctx, triggerName)
			_, err = handle.Describe(ctx)
			if err != nil {
				// 如果不存在，打印错误日志并且继续
				if !strings.HasPrefix(err.Error(), "workflow not found") {
					kratoslog.Errorf("get trigger[%s:%s] failed:%+v", request.Name, triggerName, err)
					continue
				}
			}
			// 删除 Temporal Schedule
			err = handle.Delete(ctx)
			if err != nil {
				return nil, bcode.ErrorServerInternalError("delete trigger[%s:%s] failed:%+v", request.Name, triggerName, err)
			}
			// 删除数据库
			err = t.Store.Delete(ctx, triggerEntity)
			if err != nil {
				return nil, bcode.ErrorServerInternalError("delete trigger[%s:%s] failed:%+v", request.Name, triggerName, err)
			}
		}
	}

	jobTemplate.UpdateTime = time.Now()
	jobTemplate.Triggers = newTriggersName
	err = t.Store.Put(ctx, jobTemplate)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("update job template failed:%+v", err)
	}
	return nil, nil
}

// ListJobTemplateViews 展示任务模板视图
func (t *JobService) ListJobTemplateViews(ctx context.Context, options *jobv1.ListJobTemplateViewsOptions) (*jobv1.ListJobTemplateViewsResponse, error) {
	var filterOptions datastore.FilterOptions

	if options.WorkspaceName != "" {
		filterOptions.In = append(filterOptions.In, datastore.InQueryOption{
			Key:    "workspaceName",
			Values: []string{options.WorkspaceName},
		})
	}
	if options.Region != "" {
		filterOptions.In = append(filterOptions.In, datastore.InQueryOption{
			Key:    "region",
			Values: []string{options.Region},
		})
	}
	if options.JobTemplateName != "" {
		filterOptions.Queries = append(filterOptions.Queries, datastore.FuzzyQueryOption{
			Key:   "name",
			Query: options.JobTemplateName,
		})
	}
	if options.Member != "" && options.Manager == "" {
		filterOptions.In = append(filterOptions.In, datastore.InQueryOption{
			Key:    "members",
			Values: []string{options.Member},
		})
	} else if options.Manager != "" && options.Member == "" {
		filterOptions.In = append(filterOptions.In, datastore.InQueryOption{
			Key:    "managers",
			Values: []string{options.Manager},
		})
	} else if options.Manager != "" && options.Member != "" {
		memberFilter := &datastore.FilterOptions{
			In: []datastore.InQueryOption{
				{Key: "members", Values: []string{options.Member}},
			},
		}
		managerFilter := &datastore.FilterOptions{
			In: []datastore.InQueryOption{
				{Key: "managers", Values: []string{options.Manager}},
			},
		}
		// 将两个条件放入 OR 分支
		orFilter := &datastore.FilterOptions{
			Or: []*datastore.FilterOptions{memberFilter, managerFilter},
		}
		filterOptions.Or = append(filterOptions.Or, orFilter)
	}

	items, err := t.Store.List(ctx, &model.JobTemplateEntity{}, &datastore.ListOptions{
		FilterOptions: filterOptions,
		SortBy: []datastore.SortOption{
			{
				Key:   "createTime",
				Order: datastore.SortOrderDescending,
			},
		},
		Page:     int(options.Page),
		PageSize: int(options.PageSize),
	})
	if err != nil {
		return nil, bcode.ErrorServerInternalError("list job template failed:%+v", err)
	}

	var result []*jobv1.JobTemplateView

	for _, item := range items {
		var active bool
		var nextNumActions []string
		jobTemplate := item.(*model.JobTemplateEntity)
		jobTemplateResult := helper.ConvertFromJobTemplateModel(jobTemplate)

		numActions, err := t.Store.Count(ctx, &model.JobEntity{}, &datastore.FilterOptions{
			In: []datastore.InQueryOption{
				{
					Key:    "jobTemplateName",
					Values: []string{jobTemplate.Name},
				},
			},
		})
		if err != nil {
			return nil, bcode.ErrorServerInternalError("count job template failed:%+v", err)
		}

		for _, triggerName := range jobTemplate.Triggers {
			triggerEntity, err := repository.GetTriggerForJobTemplate(ctx, t.Store, jobTemplate.Name, triggerName)
			if err != nil {
				if errors.Is(err, datastore.ErrRecordNotExist) {
					return nil, bcode.ErrorServerInternalError("trigger[%s] not found", triggerName)
				}
				return nil, bcode.ErrorServerInternalError("get trigger[%s] failed:%+v", triggerName, err)
			}

			trigger := helper.ConvertFromTriggerModel(triggerEntity)
			jobTemplateResult.Triggers = append(jobTemplateResult.Triggers, trigger)

			if trigger.TriggerType == jobv1.TriggerType_Timer {
				nextNumActions = append(nextNumActions, triggerEntity.NextActionTimes...)
			}
			if trigger.State == jobv1.TriggerState_TriggerState_Executing {
				active = true
			}

		}

		var jobTemplateStatus string
		if active {
			jobTemplateStatus = JobTemplateState_Active
		} else {
			jobTemplateStatus = JobTemplateState_Inactive
		}

		sort.Strings(nextNumActions)

		result = append(result, &jobv1.JobTemplateView{
			JobTemplate:       jobTemplateResult,
			JobTemplateStatus: jobTemplateStatus,
			NumActions:        int32(numActions),
			NextNumActions:    nextNumActions,
		})
	}
	return &jobv1.ListJobTemplateViewsResponse{
		Total:            int64(len(result)),
		JobTemplateViews: result,
	}, nil
}

func (t *JobService) CreateJobTemplateTrigger(ctx context.Context, request *jobv1.CreateOrUpdateJobTemplateTriggerRequest) (*emptypb.Empty, error) {
	l, ok := ctx.Value(constant.UserCtxKey).(string)
	if !ok {
		return nil, bcode.ErrorServerInternalError("get user from context failed")
	}
	// 获取模板数据
	jobTemplate, err := repository.GetJobTemplateForWorkspace(ctx, t.Store, request.WorkspaceName, request.JobTemplateName)
	if err != nil {
		if errors.Is(err, datastore.ErrRecordNotExist) {
			return nil, bcode.ErrorServerInternalError("job template[%s:%s] not found", request.WorkspaceName, request.JobTemplateName)
		}
		return nil, bcode.ErrorServerInternalError("get job template[%s:%s] failed:%+v", request.WorkspaceName, request.JobTemplateName, err)
	}
	jobTemplateResult := helper.ConvertFromJobTemplateModel(jobTemplate)

	if request.TriggerType == jobv1.TriggerType_Timer {
		if request.TimeTriggerOptions == nil {
			return nil, bcode.ErrorInvalidArgument("trigger options is nil")
		}
		// 生成触发器的唯一 Name
		triggerName := util.GenerateRandomStringWithPrefix("trigger", 4)
		request.TriggerName = triggerName

		trigger := &jobv1.Trigger{
			TriggerType:        request.TriggerType,
			TriggerName:        request.TriggerName,
			TimeTriggerOptions: request.TimeTriggerOptions,
		}
		// 创建 Temporal Schedule, 按照调度策略执行
		err := t.CreateTemporalSchedule(ctx, trigger, jobTemplateResult.Job, jobv1.ExecutionStrategy_ScheduledExecution)
		if err != nil {
			return nil, bcode.ErrorServerInternalError("create temporal schedule failed, err: %v", err)
		}
		// 更新 Message 信息
		trigger.Message = fmt.Sprintf("trigger[%s] was created by user %s at %s", triggerName, l, util.TimeFormat(time.Now()))
		//  trigger入库
		triggerEntity := helper.ConvertTriggerToTriggerModel(trigger)
		triggerEntity.JobTemplateName = request.JobTemplateName
		err = t.Store.Add(ctx, triggerEntity)
		if err != nil {
			return nil, bcode.ErrorServerInternalError("create trigger[%s] failed, err: %v", triggerName, err)
		}
	}

	// 更新 Job 模板
	jobTemplate.Triggers = append(jobTemplate.Triggers, request.TriggerName)
	jobTemplate.UpdateTime = time.Now()
	err = t.Store.Put(ctx, jobTemplate)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("update job template failed:%+v", err)
	}

	return &emptypb.Empty{}, nil
}

func (t *JobService) DeleteJobTemplateTrigger(ctx context.Context, request *jobv1.DeleteJobTemplateTriggerRequest) (*emptypb.Empty, error) {
	// 判断是否有删除权限
	account, ok := ctx.Value(constant.UserCtxKey).(string)
	if !ok {
		return nil, bcode.ErrorServerInternalError("get user from context failed")
	}

	jobTemplate, err := repository.GetJobTemplateForWorkspace(ctx, t.Store, request.WorkspaceName, request.JobTemplateName)
	if err != nil {
		if errors.Is(err, datastore.ErrRecordNotExist) {
			return nil, bcode.ErrorServerInternalError("job template[%s:%s] not found", request.WorkspaceName, request.JobTemplateName)
		}
		return nil, bcode.ErrorServerInternalError("get job template[%s:%s] failed:%+v", request.WorkspaceName, request.JobTemplateName, err)
	}

	// 只有平台管理员、空间管理员和模板管理员可以删除任务模板
	isPlatformAdmin, err := t.RbacService.IsPlatformAdmin(ctx, account)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("check platform admin error: %v", err)
	}
	isWorkspaceManager, err := t.WorkspaceService.IsWorkspaceManager(ctx, &workspacev1.IsWorkspaceManagerRequest{
		Account:       account,
		WorkspaceName: request.WorkspaceName,
	})
	if err != nil || isWorkspaceManager == nil {
		return nil, bcode.ErrorServerInternalError("check workspace manager error: %v", err)
	}
	if !isPlatformAdmin && !isWorkspaceManager.IsManager {
		if !util.StringInSlice(account, jobTemplate.Managers) {
			return nil, bcode.ErrorPermissionNotAllowed("permission denied")
		}
	}

	triggerEntity, err := repository.GetTriggerForJobTemplate(ctx, t.Store, request.JobTemplateName, request.TriggerName)
	if err != nil {
		if errors.Is(err, datastore.ErrRecordNotExist) {
			return nil, bcode.ErrorServerInternalError("trigger[%s] not found", request.TriggerName)
		}
		return nil, bcode.ErrorServerInternalError("get trigger[%s] failed:%+v", request.TriggerName, err)
	}

	// 获取 Temporal Schedule
	handle := t.TemporalClient.Client.ScheduleClient().GetHandle(ctx, request.TriggerName)
	_, err = handle.Describe(ctx)
	if err != nil {
		// 如果不存在，打印错误日志并且继续
		if !strings.HasPrefix(err.Error(), "workflow not found") {
			kratoslog.Errorf("get trigger[%s:%s] failed:%+v", request.JobTemplateName, request.TriggerName, err)
		}
	}
	// 删除 Temporal Schedule
	err = handle.Delete(ctx)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("delete trigger[%s:%s] failed:%+v", request.JobTemplateName, request.TriggerName, err)
	}
	// 删除数据库
	err = t.Store.Delete(ctx, triggerEntity)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("delete trigger[%s:%s] failed:%+v", request.JobTemplateName, request.TriggerName, err)
	}
	// 更新 Job 模板
	jobTemplate.Triggers = util.Remove(jobTemplate.Triggers, request.TriggerName)
	jobTemplate.UpdateTime = time.Now()
	err = t.Store.Put(ctx, jobTemplate)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("update job template failed:%+v", err)
	}
	return &emptypb.Empty{}, nil
}

func (t *JobService) GetJobTemplateTrigger(ctx context.Context, request *jobv1.GetJobTemplateTriggerRequest) (*jobv1.Trigger, error) {
	// 获取触发器数据
	triggerEntity, err := repository.GetTriggerForJobTemplate(ctx, t.Store, request.JobTemplateName, request.TriggerName)
	if err != nil {
		if errors.Is(err, datastore.ErrRecordNotExist) {
			return nil, bcode.ErrorServerInternalError("trigger[%s] not found", request.TriggerName)
		}
		return nil, bcode.ErrorServerInternalError("get trigger[%s] failed:%+v", request.TriggerName, err)
	}
	trigger := helper.ConvertFromTriggerModel(triggerEntity)

	return trigger, nil
}

func (t *JobService) UpdateJobTemplateTrigger(ctx context.Context, request *jobv1.CreateOrUpdateJobTemplateTriggerRequest) (*emptypb.Empty, error) {
	account, ok := ctx.Value(constant.UserCtxKey).(string)
	if !ok {
		return nil, bcode.ErrorServerInternalError("get user from context failed")
	}

	// 获取任务模板信息
	jobTemplate, err := repository.GetJobTemplateForWorkspace(ctx, t.Store, request.WorkspaceName, request.JobTemplateName)
	if err != nil {
		if errors.Is(err, datastore.ErrRecordNotExist) {
			return nil, bcode.ErrorServerInternalError("job template[%s:%s] not found", request.WorkspaceName, request.JobTemplateName)
		}
		return nil, bcode.ErrorServerInternalError("get job template[%s:%s] failed:%+v", request.WorkspaceName, request.JobTemplateName, err)
	}

	// 判断权限
	isPlatformAdmin, err := t.RbacService.IsPlatformAdmin(ctx, account)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("check platform admin error: %v", err)
	}
	isWorkspaceManager, err := t.WorkspaceService.IsWorkspaceManager(ctx, &workspacev1.IsWorkspaceManagerRequest{
		Account:       account,
		WorkspaceName: request.WorkspaceName,
	})
	if err != nil || isWorkspaceManager == nil {
		return nil, bcode.ErrorServerInternalError("check workspace manager error: %v", err)
	}
	if !isPlatformAdmin && !isWorkspaceManager.IsManager {
		if !util.StringInSlice(account, jobTemplate.Managers) {
			return nil, bcode.ErrorPermissionNotAllowed("permission denied")
		}
	}

	triggerEntity, err := repository.GetTriggerForJobTemplate(ctx, t.Store, request.JobTemplateName, request.TriggerName)
	if err != nil {
		if errors.Is(err, datastore.ErrRecordNotExist) {
			return nil, bcode.ErrorServerInternalError("trigger[%s] not found", request.TriggerName)
		}
		return nil, bcode.ErrorServerInternalError("get trigger[%s] failed:%+v", request.TriggerName, err)
	}
	// 更新触发器
	triggerEntity.UpdateTime = time.Now()
	if triggerEntity.TriggerType == jobv1.TriggerType_Timer.String() {
		triggerEntity.TimerTriggerOptions.TimerTriggerType = request.TimeTriggerOptions.Type.String()
		triggerEntity.TimerTriggerOptions.Interval = request.TimeTriggerOptions.Interval
		triggerEntity.TimerTriggerOptions.Cycle = helper.ConvertToCycleModel(request.TimeTriggerOptions.Cycle)
		triggerEntity.TimerTriggerOptions.CronExpr = request.TimeTriggerOptions.CronExpr
		triggerEntity.TimerTriggerOptions.PeriodOfValidity = request.TimeTriggerOptions.PeriodOfValidity
		triggerEntity.TimerTriggerOptions.MaxCount = int(request.TimeTriggerOptions.MaxCount)
		triggerEntity.TimerTriggerOptions.SkipTime = helper.ConvertToCycleModel(request.TimeTriggerOptions.SkipTime)
	}

	trigger := helper.ConvertFromTriggerModel(triggerEntity)

	if request.TriggerType == jobv1.TriggerType_Timer {
		err := t.UpdateTemporalSchedule(ctx, trigger)
		if err != nil {
			return nil, bcode.ErrorServerInternalError("update schedule failed:%+v", err)
		}
	}
	if triggerEntity.TimerTriggerOptions.MaxCount != 0 && triggerEntity.NumActions == int32(triggerEntity.TimerTriggerOptions.MaxCount) {
		triggerEntity.TriggerState = jobv1.TriggerState_TriggerState_Completed.String()
		triggerEntity.NextActionTimes = []string{}
	} else if triggerEntity.TimerTriggerOptions.PeriodOfValidity != "" {
		timestamps := strings.Split(trigger.TimeTriggerOptions.PeriodOfValidity, ",")
		if len(timestamps) == 2 {
			// 解析结束时间
			endTimestamp, err := strconv.ParseInt(timestamps[1], 10, 64)
			if err != nil {
				return nil, bcode.ErrorServerInternalError("解析结束时间戳时出错")
			}
			endAt := time.Unix(endTimestamp, 0)
			if endAt.Before(time.Now()) {
				triggerEntity.TriggerState = jobv1.TriggerState_TriggerState_Expired.String()
				triggerEntity.NextActionTimes = []string{}
			}
		}
	} else {
		handle := t.TemporalClient.Client.ScheduleClient().GetHandle(ctx, trigger.TriggerName)
		desc, _ := handle.Describe(ctx)
		if desc.Schedule.State.Paused == false {
			triggerEntity.TriggerState = jobv1.TriggerState_TriggerState_Executing.String()
			triggerEntity.NumActions = int32(desc.Info.NumActions)
		} else {
			triggerEntity.TriggerState = jobv1.TriggerState_TriggerState_Paused.String()
			triggerEntity.NumActions = int32(desc.Info.NumActions)
		}
	}
	err = t.Store.Put(ctx, triggerEntity)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("update trigger[%s] failed, err: %v", triggerEntity.TriggerName, err)
	}

	// 更新 Job 模板
	jobTemplate.UpdateTime = time.Now()
	err = t.Store.Put(ctx, jobTemplate)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("update job template failed:%+v", err)
	}

	return &emptypb.Empty{}, nil
}

func (t *JobService) UpdateTemporalSchedule(ctx context.Context, trigger *jobv1.Trigger) error {
	// 获取现有配置
	handle := t.TemporalClient.Client.ScheduleClient().GetHandle(ctx, trigger.TriggerName)
	desc, _ := handle.Describe(ctx)

	// 更新配置
	if desc != nil {
		spec, err := helper.ConvertTimerTriggerToScheduleSpec(trigger.TimeTriggerOptions)
		if err != nil {
			return bcode.ErrorServerInternalError("convert timer trigger to schedule spec error, err: %v", err)
		}

		if trigger.TimeTriggerOptions != nil && trigger.TimeTriggerOptions.MaxCount != 0 {
			if desc.Schedule.State != nil {
				desc.Schedule.State.RemainingActions = int(trigger.TimeTriggerOptions.MaxCount)
			}
		}

		// 构建更新对象
		newSchedule := temporalSdkClient.Schedule{
			Spec:   &spec,
			Action: desc.Schedule.Action, // 保留原有 action
			Policy: desc.Schedule.Policy, // 保留原有 policy
			State:  desc.Schedule.State,  // 保留原有 state
		}

		// 提交更新
		return handle.Update(ctx, temporalSdkClient.ScheduleUpdateOptions{
			DoUpdate: func(input temporalSdkClient.ScheduleUpdateInput) (*temporalSdkClient.ScheduleUpdate, error) {
				return &temporalSdkClient.ScheduleUpdate{
					Schedule: &newSchedule,
				}, nil
			},
		})
	} else {
		return bcode.ErrorTemporalScheduleNotExists("temporal schedule not exist")
	}
}

func (t *JobService) ListJobTemplateTriggers(ctx context.Context, request *jobv1.ListJobTemplateTriggersRequest) (*jobv1.ListJobTemplateTriggersResponse, error) {
	// 获取模板数据
	jobTemplate, err := repository.GetJobTemplateForWorkspace(ctx, t.Store, request.WorkspaceName, request.JobTemplateName)
	if err != nil {
		if errors.Is(err, datastore.ErrRecordNotExist) {
			return nil, bcode.ErrorServerInternalError("job template[%s:%s] not found", request.WorkspaceName, request.JobTemplateName)
		}
		return nil, bcode.ErrorServerInternalError("get job template[%s:%s] failed:%+v", request.WorkspaceName, request.JobTemplateName, err)
	}

	var triggers []*jobv1.Trigger
	for _, triggerName := range jobTemplate.Triggers {
		triggerEntity, err := repository.GetTriggerForJobTemplate(ctx, t.Store, jobTemplate.Name, triggerName)
		if err != nil {
			if errors.Is(err, datastore.ErrRecordNotExist) {
				return nil, bcode.ErrorServerInternalError("trigger[%s] not found", triggerName)
			}
			return nil, bcode.ErrorServerInternalError("get trigger[%s] failed:%+v", triggerName, err)
		}

		trigger := helper.ConvertFromTriggerModel(triggerEntity)
		if trigger.TimeTriggerOptions != nil && trigger.TimeTriggerOptions.PeriodOfValidity != "" {
			// 拆分字符串
			var startAt, endAt string
			timestamps := strings.Split(trigger.TimeTriggerOptions.PeriodOfValidity, ",")
			if len(timestamps) == 2 {
				// 解析开始时间
				startTimestamp, err := strconv.ParseInt(timestamps[0], 10, 64)
				if err != nil {
					return nil, bcode.ErrorServerInternalError("解析开始时间戳时出错")
				}
				startAt = util.TimeFormat(time.Unix(startTimestamp, 0))

				// 解析结束时间
				endTimestamp, err := strconv.ParseInt(timestamps[1], 10, 64)
				if err != nil {
					return nil, bcode.ErrorServerInternalError("解析结束时间戳时出错")
				}
				endAt = util.TimeFormat(time.Unix(endTimestamp, 0))
			} else {
				return nil, bcode.ErrorInvalidArgument("invalid period of validity format")
			}
			trigger.TimeTriggerOptions.PeriodOfValidity = startAt + " 至 " + endAt
		}

		triggers = append(triggers, trigger)
	}

	return &jobv1.ListJobTemplateTriggersResponse{
		Triggers: triggers,
		Total:    int32(len(triggers)),
	}, nil
}

func (t *JobService) PauseJobTemplateTrigger(ctx context.Context, request *jobv1.PauseJobTemplateTriggerRequest) (*emptypb.Empty, error) {
	l, ok := ctx.Value(constant.UserCtxKey).(string)
	if !ok {
		return nil, bcode.ErrorServerInternalError("get user from context failed")
	}

	trigger, err := repository.GetTriggerForJobTemplate(ctx, t.Store, request.JobTemplateName, request.TriggerName)
	if err != nil {
		if errors.Is(err, datastore.ErrRecordNotExist) {
			return nil, bcode.ErrorServerInternalError("trigger[%s] not found", request.TriggerName)
		}
		return nil, bcode.ErrorServerInternalError("get trigger[%s] failed:%+v", request.TriggerName, err)
	}

	if trigger.TriggerState != jobv1.TriggerState_TriggerState_Executing.String() {
		return nil, bcode.ErrorServerInternalError("trigger[%s] not executing", request.TriggerName)
	}

	handle := t.TemporalClient.Client.ScheduleClient().GetHandle(ctx, trigger.TriggerName)
	err = handle.Pause(ctx, temporalSdkClient.SchedulePauseOptions{
		Note: fmt.Sprintf("user %s paused trigger %s at %s", l, request.TriggerName, time.Now()),
	})
	if err != nil {
		return nil, bcode.ErrorServerInternalError("pause trigger[%s] failed:%+v", request.TriggerName, err)
	}
	desc, err := handle.Describe(ctx)
	if err != nil || desc == nil {
		return nil, bcode.ErrorServerInternalError("get trigger[%s] failed:%+v", request.TriggerName, err)
	}
	if desc.Schedule.State.Paused == false {
		return nil, bcode.ErrorServerInternalError("trigger[%s] not paused", request.TriggerName)
	}
	trigger.TriggerState = jobv1.TriggerState_TriggerState_Paused.String()
	trigger.NumActions = int32(desc.Info.NumActions)
	trigger.NextActionTimes = []string{}
	trigger.Message = fmt.Sprintf("trigger[%s] was paused by user %s at %s", request.TriggerName, l, util.TimeFormat(time.Now()))
	err = t.Store.Put(ctx, trigger)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("update trigger[%s] failed:%+v", request.TriggerName, err)
	}
	return nil, nil
}

func (t *JobService) RecoverJobTemplateTrigger(ctx context.Context, request *jobv1.RecoverJobTemplateTriggerRequest) (*emptypb.Empty, error) {
	l, ok := ctx.Value(constant.UserCtxKey).(string)
	if !ok {
		return nil, bcode.ErrorServerInternalError("get user from context failed")
	}

	trigger, err := repository.GetTriggerForJobTemplate(ctx, t.Store, request.JobTemplateName, request.TriggerName)
	if err != nil {
		if errors.Is(err, datastore.ErrRecordNotExist) {
			return nil, bcode.ErrorServerInternalError("trigger[%s] not found", request.TriggerName)
		}
		return nil, bcode.ErrorServerInternalError("get trigger[%s] failed:%+v", request.TriggerName, err)
	}

	if trigger.TriggerState != jobv1.TriggerState_TriggerState_Paused.String() {
		return nil, bcode.ErrorServerInternalError("trigger[%s] not paused", request.TriggerName)
	}

	handle := t.TemporalClient.Client.ScheduleClient().GetHandle(ctx, trigger.TriggerName)
	err = handle.Unpause(ctx, temporalSdkClient.ScheduleUnpauseOptions{
		Note: fmt.Sprintf("user %s unpaused trigger %s at %s", l, request.TriggerName, time.Now()),
	})
	if err != nil {
		return nil, bcode.ErrorServerInternalError("unpause trigger[%s] failed:%+v", request.TriggerName, err)
	}
	desc, err := handle.Describe(ctx)
	if err != nil || desc == nil {
		return nil, bcode.ErrorServerInternalError("get trigger[%s] failed:%+v", request.TriggerName, err)
	}
	if desc.Schedule.State.Paused == true {
		return nil, bcode.ErrorServerInternalError("trigger[%s] already paused", request.TriggerName)
	}

	trigger.TriggerState = jobv1.TriggerState_TriggerState_Executing.String()
	trigger.NumActions = int32(desc.Info.NumActions)
	trigger.NextActionTimes = util.TimesFormat(desc.Info.NextActionTimes)
	trigger.Message = fmt.Sprintf("trigger[%s] was recoverd by user %s at %s", request.TriggerName, l, util.TimeFormat(time.Now()))
	err = t.Store.Put(ctx, trigger)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("update trigger[%s] failed:%+v", request.TriggerName, err)
	}
	return nil, nil
}

func (t *JobService) CreateTensorboard(ctx context.Context, request *jobv1.CreateTensorboardRequest) (*emptypb.Empty, error) {
	//首先校验job的状态
	jobEntity, err := repository.GetJobForWorkspace(ctx, t.Store, request.WorkspaceName, request.JobName)
	if err != nil {
		if errors.Is(err, datastore.ErrRecordNotExist) {
			return nil, bcode.ErrorJobNotFound("job[%s:%s] not found", request.WorkspaceName, request.JobName)
		}
		return nil, bcode.ErrorServerInternalError("create job tensorboard failed:%+v", err)
	}
	jobStatus, err := repository.GetJobStatusForJobID(ctx, t.Store, jobEntity.ID)
	if err != nil {
		if errors.Is(err, datastore.ErrRecordNotExist) {
			return nil, bcode.ErrorJobNotFound("job[%s:%s] not found", request.WorkspaceName, request.JobName)
		}
		return nil, bcode.ErrorServerInternalError("create job tensorboard failed:%+v", err)
	}
	if jobStatus.ClusterName == "" {
		return nil, bcode.ErrorInvalidArgument("job[%s:%s] not running", request.WorkspaceName, request.JobName)
	}
	var tensorboardEnabled bool
	switch jobEntity.JobType {
	case jobv1.JobType_PyTorchDDP.String():
		tensorboardEnabled = jobEntity.PytorchJobTemplate.TensorboardEnabled
	case jobv1.JobType_DeepSpeed.String():
		tensorboardEnabled = jobEntity.DeepSpeedJobTemplate.TensorboardEnabled
	case jobv1.JobType_SimpleTraining.String():
		tensorboardEnabled = jobEntity.SimpleTrainingJobTemplate.TensorboardEnabled
	}
	if !tensorboardEnabled {
		return nil, bcode.ErrorTensorboardFeatureNotEnabled("tensorboard feature not enabled for job[%s:%s]", request.WorkspaceName, request.JobName)
	}
	cubefsVolume := &cloudfsv1alpha1.CubeFSVolume{}
	err = multicluster.Instance().GetLocalCluster().Direct().Get(ctx, client.ObjectKey{Namespace: getNamespaceName(request.WorkspaceName), Name: "tensorboard"}, cubefsVolume)
	if err != nil {
		if apierrors.IsNotFound(err) {
			return nil, bcode.ErrorCubeFSVolumeNotFound("cubefs volume not found: %s", "tensorboard")
		}
		return nil, bcode.ErrorServerInternalError("get cubefs volume failed: %s", err.Error())
	}
	tensorboard := &automlv1alpha1.Tensorboard{}
	err = multicluster.Instance().GetLocalCluster().Direct().Get(ctx, client.ObjectKey{Namespace: getNamespaceName(request.WorkspaceName), Name: request.JobName}, tensorboard)
	if err != nil {
		if apierrors.IsNotFound(err) {
			tensorboard = &automlv1alpha1.Tensorboard{
				ObjectMeta: metav1.ObjectMeta{
					Name:      request.JobName,
					Namespace: getNamespaceName(request.WorkspaceName),
					Annotations: map[string]string{
						"scheduling.volcano.sh/queue-name": jobEntity.QueueName,
					},
				},
				Spec: automlv1alpha1.TensorboardSpec{
					LogsPath:      fmt.Sprintf("pvc://%s-cfs-pvc/%s", cubefsVolume.Name, request.JobName),
					ExposureMode:  automlv1alpha1.ExposureServiceNodePort,
					Image:         DefaultImage,
					Cluster:       jobStatus.ClusterName,
					SchedulerName: "volcano",
					PathPrefix:    fmt.Sprintf("/tensorboard/proxy/workspace/%s/jobs/%s", request.WorkspaceName, request.JobName),
					Tolerations: []corev1.Toleration{
						{
							Key:      "aicp.group/worker",
							Operator: corev1.TolerationOpExists,
							Effect:   corev1.TaintEffectNoSchedule,
						},
						{
							Key:      constant.AIStudioTaintKey,
							Operator: corev1.TolerationOpEqual,
							Value:    "aistudio",
						},
					},
				},
			}
			err = multicluster.Instance().GetLocalCluster().Direct().Create(ctx, tensorboard)
			if err != nil {
				return nil, bcode.ErrorServerInternalError("create tensorboard failed: %s", err.Error())
			}
			return nil, nil
		}
	}
	klog.Infof("tensorboard [%s:%s] already exists, ignore create it", getNamespaceName(request.WorkspaceName), request.JobName)
	return nil, nil
}

func (t *JobService) GetTensorboard(ctx context.Context, request *jobv1.GetTensorboardRequest) (*jobv1.GetTensorboardResponse, error) {
	tensorboard := &automlv1alpha1.Tensorboard{}
	err := multicluster.Instance().GetLocalCluster().Direct().Get(ctx, client.ObjectKey{Namespace: getNamespaceName(request.WorkspaceName), Name: request.JobName}, tensorboard)
	if err != nil {
		if apierrors.IsNotFound(err) {
			kratoslog.Errorf("tensorboard[%s:%s] not found", getNamespaceName(request.WorkspaceName), request.JobName)
			return &jobv1.GetTensorboardResponse{
				Url:   "",
				State: "NotReady",
			}, nil
		}
		return nil, bcode.ErrorServerInternalError("get tensorboard failed: %s", err.Error())
	}
	domain := t.Properties.GetDefault("application.domain", "aistudio.kcs.ke.com")
	if tensorboard.Status.ReadyReplicas == 1 {
		return &jobv1.GetTensorboardResponse{
			Url:   fmt.Sprintf("http://%s/tensorboard/proxy/workspace/%s/jobs/%s", domain, request.GetWorkspaceName(), request.JobName),
			State: "Ready",
		}, nil
	} else {
		return &jobv1.GetTensorboardResponse{
			Url:   "",
			State: "NotReady",
		}, nil
	}
}

func (t *JobService) RegisterJobProvider(jobType jobv1.JobType, p provider.Provider) {
	t.jobProviders[jobType.String()] = p
}

func (t *JobService) GetTaskNodeMetrics(ctx context.Context, taskSpec *jobv1.TaskSpec) error {
	specificationName := taskSpec.NodeSpecificationName
	nodeMetrics, err := t.getNodeMetricBySpecificationName(ctx, specificationName)
	if err != nil {
		return err
	}
	if nodeMetrics == nil {
		return fmt.Errorf("nodeMetrics is nil")
	}
	// cpu和memory 预留 5%
	if nodeMetrics.ResourceMetrics[constant.ResourceNvidiaGPU] != nil {
		taskSpec.Specification.GpuNum = int32(nodeMetrics.ResourceMetrics[constant.ResourceNvidiaGPU].Allocatable.Value())
	}
	if nodeMetrics.ResourceMetrics[corev1.ResourceCPU] != nil {
		cpuAllocatable := nodeMetrics.ResourceMetrics[corev1.ResourceCPU].Allocatable.Value()
		taskSpec.Specification.CpuNum = int32(float64(cpuAllocatable) * 0.85)
	}
	if nodeMetrics.ResourceMetrics[corev1.ResourceMemory] != nil {
		memoryByteAllocatable := nodeMetrics.ResourceMetrics[corev1.ResourceMemory].Allocatable.Value()
		memoryAllocatable := memoryByteAllocatable / Gibibyte
		taskSpec.Specification.MemoryGiB = int32(float64(memoryAllocatable) * 0.85)
	}
	if nodeMetrics.ResourceMetrics[constant.ResourceNvidiaGPU] != nil {
		taskSpec.Slots = int32(nodeMetrics.ResourceMetrics[constant.ResourceNvidiaGPU].Allocatable.Value())
	}
	taskSpec.Specification.GpuMem = int32(0)
	taskSpec.Specification.CpuNum = int32(2)
	taskSpec.Specification.MemoryGiB = int32(2)
	return nil
}

func (t *JobService) GetTaskResourceQuantity(taskSpec *jobv1.TaskSpec) *model.ResourceRequestQuantity {
	cpuRequestQuantity := resource.Quantity{}
	memoryRequestQuantity := resource.Quantity{}
	gpuRequestQuantity := resource.Quantity{}
	requestCPU := resource.MustParse(fmt.Sprintf("%d", taskSpec.Specification.CpuNum))
	requestGPU := resource.MustParse(fmt.Sprintf("%d", taskSpec.Specification.GpuNum))
	requestMemory := resource.MustParse(fmt.Sprintf("%dGi", taskSpec.Specification.MemoryGiB))
	cpuRequestQuantity.Add(requestCPU)
	memoryRequestQuantity.Add(requestMemory)
	gpuRequestQuantity.Add(requestGPU)
	return &model.ResourceRequestQuantity{
		CPU:    cpuRequestQuantity,
		Memory: memoryRequestQuantity,
		GPU:    gpuRequestQuantity,
	}
}

func (t *JobService) getNodeMetricBySpecificationName(ctx context.Context, nodeSpecificationName string) (*schedulingv1alpha1.NodeMetric, error) {
	nodeMetrics, err := t.ClusterManager.GetNodeMetricsBySpecificationName(ctx, nodeSpecificationName)
	if err != nil {
		return nil, err
	}
	if len(nodeMetrics) == 0 {
		return nil, errors.New("no[%s] node metric found")
	}
	return &nodeMetrics[0], nil
}

func (t *JobService) GetTaskResourceQuantityFromSimpleTrainingJobTemplate(jobTemplate *jobv1.SimpleTrainingJobTemplate) *model.ResourceRequestQuantity {
	cpuRequestQuantity := resource.Quantity{}
	memoryRequestQuantity := resource.Quantity{}
	gpuRequestQuantity := resource.Quantity{}
	requestCPU := resource.MustParse(fmt.Sprintf("%d", jobTemplate.Specification.CpuNum))
	requestGPU := resource.MustParse(fmt.Sprintf("%d", jobTemplate.Specification.GpuNum))
	requestMemory := resource.MustParse(fmt.Sprintf("%dGi", jobTemplate.Specification.MemoryGiB))
	cpuRequestQuantity.Add(requestCPU)
	memoryRequestQuantity.Add(requestMemory)
	gpuRequestQuantity.Add(requestGPU)
	return &model.ResourceRequestQuantity{
		CPU:    cpuRequestQuantity,
		Memory: memoryRequestQuantity,
		GPU:    gpuRequestQuantity,
	}
}

func (t *JobService) SubmitJob(ctx context.Context, request *jobv1.CreateJobRequest) (*jobv1.CreateJobResponse, error) {
	l, ok := ctx.Value(constant.UserCtxKey).(string)
	if !ok {
		return nil, bcode.ErrorServerInternalError("get user from context failed")
	}
	//权限校验
	isPlatformAdmin, err := t.RbacService.IsPlatformAdmin(ctx, l)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("check platform admin error: %v", err)
	}
	if !isPlatformAdmin {
		workspace, err := repository.GetWorkspace(ctx, t.Store, request.WorkspaceName)
		if err != nil {
			return nil, bcode.ErrorServerInternalError("get workspace failed: %v", err)
		}
		if !util.StringInSlice(l, workspace.Members) && !util.StringInSlice(l, workspace.Managers) {
			return nil, bcode.ErrorPermissionNotAllowed("user[%s] has no permission to access workspace[%s]", l, request.WorkspaceName)
		}
		if !util.StringInSlice(l, workspace.Managers) && !util.StringInSlice(l, workspace.Members) {
			return nil, bcode.ErrorPermissionNotAllowed("user[%s] has no permission to access workspace[%s]", l, request.WorkspaceName)
		}
		//非管理员校验队列权限
		if !util.StringInSlice(l, workspace.Managers) {
			queue, err := repository.GetQueueForWorkspace(ctx, t.Store, request.WorkspaceName, request.QueueName)
			if err != nil {
				return nil, bcode.ErrorServerInternalError("get queue failed: %v", err)
			}
			if !util.StringInSlice(l, queue.Members) && !util.StringInSlice(l, queue.Managers) {
				return nil, bcode.ErrorPermissionNotAllowed("user[%s] has no permission to access queue[%s]", l, request.QueueName)
			}
		}
	}

	if !util.StringInSlice(l, request.Members) {
		request.Members = append(request.Members, l)
	}
	workspaceCR, err := GetWorkspaceCR(ctx, request.WorkspaceName)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("get workspace cr failed: %v", err)
	}
	if workspaceCR.Status.State != schedulingv1alpha1.WorkspaceStateSynced {
		return nil, bcode.ErrorWorkspaceNotReady("workspace[%s] is not ready for create DeploymentGroup", request.WorkspaceName)
	}

	// 如果 Name 为空，则生成一个随机的 Name
	if request.Name != "" {
		if exists, err := repository.JobExists(ctx, t.Store, request.WorkspaceName, request.Name); err != nil {
			return nil, bcode.ErrorServerInternalError("check job exists failed,err:%v", err)
		} else if exists {
			return nil, bcode.ErrorJobAlReadyExists("job[%s:%s] already exists", request.WorkspaceName, request.Name)
		}
	}
	queue, err := repository.GetQueueForWorkspace(ctx, t.Store, request.WorkspaceName, request.QueueName)
	if err != nil {
		if errors.Is(err, datastore.ErrRecordNotExist) {
			return nil, bcode.ErrorQueueNotFound("queue[%s:%s] not found", request.WorkspaceName, request.QueueName)
		}
		return nil, bcode.ErrorServerInternalError("get queue failed,err:%v", err)
	}
	err = t.checkJobRequest(ctx, request)
	if err != nil {
		return nil, err
	}
	var totalReplicas int32
	switch request.JobType {
	case jobv1.JobType_PyTorchDDP:
		if request.PyTorchDDPJobTemplate == nil {
			return nil, bcode.ErrorInvalidArgument("PyTorchDDPJobTemplate is nil")
		}
		if err := t.GetTaskNodeMetrics(ctx, request.PyTorchDDPJobTemplate.Master); err != nil {
			return nil, bcode.ErrorServerInternalError("get master node metrics failed,err:%v", err)
		}
		if err := t.GetTaskNodeMetrics(ctx, request.PyTorchDDPJobTemplate.Worker); err != nil {
			return nil, bcode.ErrorServerInternalError("get worker node metrics failed,err:%v", err)
		}
		masterRequestResourceQuantity := t.GetTaskResourceQuantity(request.PyTorchDDPJobTemplate.Master)
		workerRequestResourceQuantity := t.GetTaskResourceQuantity(request.PyTorchDDPJobTemplate.Worker)
		var requestedResource []provider.RequestedResource
		requestedResource = append(requestedResource, provider.RequestedResource{
			ResourceRequestQuantity: masterRequestResourceQuantity,
			NodeSpecificationName:   request.PyTorchDDPJobTemplate.Master.NodeSpecificationName,
		})
		requestedResource = append(requestedResource, provider.RequestedResource{
			ResourceRequestQuantity: workerRequestResourceQuantity,
			NodeSpecificationName:   request.PyTorchDDPJobTemplate.Worker.NodeSpecificationName,
		})
		kratoslog.Infof("DDP Rescource is %+v, Quantity is %+v, Quantity is %+v", requestedResource, requestedResource[0].ResourceRequestQuantity, requestedResource[1].ResourceRequestQuantity)
		err = t.checkResourceSufficient(ctx, request, requestedResource)
		if err != nil {
			return nil, bcode.ErrorInvalidArgument("check requested resource failed,err:%v", err)
		}
		totalReplicas = request.PyTorchDDPJobTemplate.Worker.Replicas + 1
	case jobv1.JobType_DeepSpeed:
		if request.DeepSpeedJobTemplate == nil {
			return nil, bcode.ErrorInvalidArgument("DeepSpeed is nil")
		}
		if err := t.GetTaskNodeMetrics(ctx, request.DeepSpeedJobTemplate.Master); err != nil {
			return nil, bcode.ErrorServerInternalError("get master node metrics failed,err:%v", err)
		}
		if err := t.GetTaskNodeMetrics(ctx, request.DeepSpeedJobTemplate.Worker); err != nil {
			return nil, bcode.ErrorServerInternalError("get worker node metrics failed,err:%v", err)
		}
		masterRequestResourceQuantity := t.GetTaskResourceQuantity(request.DeepSpeedJobTemplate.Master)
		workerRequestResourceQuantity := t.GetTaskResourceQuantity(request.DeepSpeedJobTemplate.Worker)
		var requestedResource []provider.RequestedResource
		requestedResource = append(requestedResource, provider.RequestedResource{
			ResourceRequestQuantity: masterRequestResourceQuantity,
			NodeSpecificationName:   request.DeepSpeedJobTemplate.Master.NodeSpecificationName,
		})
		requestedResource = append(requestedResource, provider.RequestedResource{
			ResourceRequestQuantity: workerRequestResourceQuantity,
			NodeSpecificationName:   request.DeepSpeedJobTemplate.Worker.NodeSpecificationName,
		})
		err = t.checkResourceSufficient(ctx, request, requestedResource)
		if err != nil {
			return nil, bcode.ErrorInvalidArgument("check requested resource failed,err:%v", err)
		}
		totalReplicas = request.DeepSpeedJobTemplate.Worker.Replicas + 1
	case jobv1.JobType_SimpleTraining:
		if request.SimpleTrainingJobTemplate == nil {
			return nil, bcode.ErrorInvalidArgument("SimpleTrainingJobTemplate is nil")
		}
		requestResourceQuantity := t.GetTaskResourceQuantityFromSimpleTrainingJobTemplate(request.SimpleTrainingJobTemplate)
		err = t.checkResourceSufficient(ctx, request, []provider.RequestedResource{
			{
				ResourceRequestQuantity: requestResourceQuantity,
				NodeSpecificationName:   request.SimpleTrainingJobTemplate.NodeSpecificationName,
			},
		})
		if err != nil {
			return nil, bcode.ErrorInvalidArgument("check resource failed,err:%v", err)
		}
		totalReplicas = 1
	default:
		return nil, bcode.ErrorInvalidArgument("job type[%s] not supported", request.JobType.String())
	}
	job := helper.ConvertToJobModel(request)
	job.QueueID = queue.ID
	job.Creator = l
	job.VelaDefaultEnvName = workspaceCR.Status.VelaDefaultEnvName
	job.VelaProjectName = workspaceCR.Status.VelaProjectName
	//request.Members = append(request.Members, l)

	jobStatus := &model.JobStatusEntity{
		ID:            primitive.NewObjectID(),
		JobID:         job.ID,
		WorkspaceName: request.WorkspaceName,
		QueueName:     request.QueueName,
		Priority:      job.Priority,
		SubmitState:   model.WaitForSubmit,
		Reason:        "WaitForSubmit",
		Message:       "WaitForSubmit",
		Phase:         model.JobStatePending,
		Namespace:     helper2.GetClusterNamespaceName(request.WorkspaceName),
		JobName:       job.Name,
		TotalReplicas: totalReplicas,
		Conditions: []metav1.Condition{
			{
				Type:               model.JobConditionTypeSummitToClusterReady,
				Status:             metav1.ConditionFalse,
				Reason:             "WaitForSubmit",
				Message:            "WaitForSubmit",
				LastTransitionTime: metav1.Now(),
			},
			{
				Type:               model.JobConditionTypeInQueueReady,
				Status:             metav1.ConditionFalse,
				Reason:             "Initializing",
				Message:            "Initializing",
				LastTransitionTime: metav1.Now(),
			},
			{
				Type:               model.JobConditionTypeJobScheduledReady,
				Status:             metav1.ConditionFalse,
				Reason:             "Initializing",
				Message:            "Initializing",
				LastTransitionTime: metav1.Now(),
			},
			{
				Type:               model.JobConditionTypeJobFinishedReady,
				Status:             metav1.ConditionFalse,
				Reason:             "Initializing",
				Message:            "Initializing",
				LastTransitionTime: metav1.Now(),
			},
		},
	}
	var entities []datastore.Entity
	entities = append(entities, job, jobStatus)
	if err := t.Store.BatchAdd(ctx, entities); err != nil {
		return nil, bcode.ErrorServerInternalError("create job failed,err:%v", err)
	}
	return &jobv1.CreateJobResponse{
		JobName: job.Name,
	}, nil
}

func (t *JobService) SubmitDirect(ctx context.Context, clusterName string, job *model.JobEntity) error {
	if p, ok := t.jobProviders[job.JobType]; ok {
		err := p.Submit(ctx, clusterName, job)
		if err != nil {
			return err
		}
		return nil
	}
	return bcode.ErrorServerInternalError("job type %s not supported", job.JobType)
}

func (t *JobService) RedeployJob(ctx context.Context, request *jobv1.RedeployJobRequest) (*emptypb.Empty, error) {
	//权限校验
	account, ok := ctx.Value(constant.UserCtxKey).(string)
	if !ok {
		return nil, bcode.ErrorServerInternalError("get user from context failed")
	}
	if ok, err := t.CheckJobPermission(ctx, request.WorkspaceName, request.JobName, account); err != nil || !ok {
		if err != nil {
			return nil, err
		}
		return nil, bcode.ErrorPermissionNotAllowed("user[%s] has no permission to redeploy job[%s:%s]", account, request.WorkspaceName, request.JobName)
	}

	// 从当前空间获取并校验 job，jobStatus
	jobEntity, err := repository.GetJobForWorkspace(ctx, t.Store, request.WorkspaceName, request.JobName)
	if err != nil {
		if errors.Is(datastore.ErrRecordNotExist, err) {
			return &emptypb.Empty{}, bcode.ErrorServerInternalError("Workspace[%s]' Job[%s] not found", request.WorkspaceName, request.JobName)
		}
		return &emptypb.Empty{}, err
	}
	jobStatusEntity, err := repository.GetJobStatusForJobID(ctx, t.Store, jobEntity.ID)
	if err != nil {
		if errors.Is(datastore.ErrRecordNotExist, err) {
			return &emptypb.Empty{}, bcode.ErrorServerInternalError("JobStatus for Job[%s] not found", request.JobName)
		}
		return &emptypb.Empty{}, err
	}
	// 校验 job 状态，只有 Completed、Failed、Aborted、Åborting 的任务可以重新部署
	if jobStatusEntity.Phase != model.JobStateCompleted && jobStatusEntity.Phase != model.JobStateFailed && jobStatusEntity.Phase != model.JobStateAborted && jobStatusEntity.Phase != model.JobStateAborting {
		return &emptypb.Empty{}, bcode.ErrorInvalidArgument("Job[%s] is not in a state that can be redeployed", request.JobName)
	}

	// 通过 vela 重新部署
	// err = helper.Redeploy(ctx, request.WorkspaceName, request.JobName)
	// if err != nil {
	// 	return &emptypb.Empty{}, bcode.ErrorServerInternalError("Redeploy Job[%s] failed, err: %v", request.JobName, err)
	// }

	// 更新 JobStatus 状态为 Pending
	jobStatusEntity.SubmitState = model.WaitForSubmit
	jobStatusEntity.Phase = model.JobStatePending
	jobStatusEntity.Reason = "WaitForSubmit"
	jobStatusEntity.Message = "WaitForSubmit"
	jobStatusEntity.UpdateTime = time.Now()
	jobStatusEntity.Conditions = []metav1.Condition{
		{
			Type:               model.JobConditionTypeSummitToClusterReady,
			Status:             metav1.ConditionFalse,
			Reason:             "WaitForSubmit",
			Message:            "WaitForSubmit",
			LastTransitionTime: metav1.Now(),
		},
		{
			Type:               model.JobConditionTypeInQueueReady,
			Status:             metav1.ConditionFalse,
			Reason:             "Initializing",
			Message:            "Initializing",
			LastTransitionTime: metav1.Now(),
		},
		{
			Type:               model.JobConditionTypeJobScheduledReady,
			Status:             metav1.ConditionFalse,
			Reason:             "Initializing",
			Message:            "Initializing",
			LastTransitionTime: metav1.Now(),
		},
		{
			Type:               model.JobConditionTypeJobFinishedReady,
			Status:             metav1.ConditionFalse,
			Reason:             "Initializing",
			Message:            "Initializing",
			LastTransitionTime: metav1.Now(),
		},
	}
	jobStatusEntity.RunningDuration = ""

	err = t.Store.Put(ctx, jobStatusEntity)
	if err != nil {
		return &emptypb.Empty{}, bcode.ErrorServerInternalError("Update JobStatus for Job[%s] failed, err: %v", request.JobName, err)
	}
	return &emptypb.Empty{}, nil
}

func (t *JobService) ListJobs(ctx context.Context, options *jobv1.ListJobsOptions) (*jobv1.ListJobsResponse, error) {
	var filterOptions datastore.FilterOptions

	if options.WorkspaceName != "" {
		filterOptions.In = append(filterOptions.In, datastore.InQueryOption{
			Key:    "workspaceName",
			Values: []string{options.WorkspaceName},
		})
	}
	if options.Name != "" {
		filterOptions.Queries = append(filterOptions.Queries, datastore.FuzzyQueryOption{
			Key:   "name",
			Query: options.Name,
		})
	}
	if options.DisplayName != "" {
		filterOptions.Queries = append(filterOptions.Queries, datastore.FuzzyQueryOption{
			Key:   "displayName",
			Query: options.DisplayName,
		})
	}
	if options.Region != "" {
		filterOptions.In = append(filterOptions.In, datastore.InQueryOption{
			Key:    "region",
			Values: []string{options.Region},
		})
	}
	if options.QueueName != "" {
		queues := strings.Split(options.QueueName, ",")
		filterOptions.In = append(filterOptions.In, datastore.InQueryOption{
			Key:    "queueName",
			Values: queues,
		})
	}
	if options.Creator != "" {
		filterOptions.In = append(filterOptions.In, datastore.InQueryOption{
			Key:    "creator",
			Values: []string{options.Creator},
		})
	}
	if options.Member != "" {
		filterOptions.In = append(filterOptions.In, datastore.InQueryOption{
			Key:    "members",
			Values: []string{options.Member},
		})
	}
	if options.JobTemplateName != "" {
		filterOptions.In = append(filterOptions.In, datastore.InQueryOption{
			Key:    "jobtemplatename",
			Values: []string{options.JobTemplateName},
		})
	} else {
		nullValueFilter := &datastore.FilterOptions{
			In: []datastore.InQueryOption{
				{Key: "jobtemplatename", Values: []string{""}},
			},
		}
		notExistValueFilter := &datastore.FilterOptions{
			NotExist: []datastore.NotExistQueryOption{
				{Key: "jobtemplatename"},
			},
		}
		// 将两个条件放入 OR 分支
		orFilter := &datastore.FilterOptions{
			Or: []*datastore.FilterOptions{nullValueFilter, notExistValueFilter},
		}
		filterOptions.Or = append(filterOptions.Or, orFilter)
	}

	if options.JobType != "" {
		var jobType string
		switch options.JobType {
		case jobv1.JobType_PyTorchDDP.String():
			jobType = jobv1.JobType_PyTorchDDP.String()
		case jobv1.JobType_DeepSpeed.String():
			jobType = jobv1.JobType_DeepSpeed.String()
		case jobv1.JobType_SimpleTraining.String():
			jobType = jobv1.JobType_SimpleTraining.String()
		default:
			return nil, bcode.ErrorServerInternalError("invalid job type:%s", options.JobType)
		}
		filterOptions.In = append(filterOptions.In, datastore.InQueryOption{
			Key:    "jobType",
			Values: []string{jobType},
		})
	}

	//如果不按照状态查询,就不需要联合查询
	var jobEntities []datastore.Entity
	var totalCount int64
	var err error
	if options.State != "" {
		state := strings.Split(options.State, ",")
		filterOptions.In = append(filterOptions.In, datastore.InQueryOption{
			Key:    "joined.phase",
			Values: state,
		})
		var jobModel = model.JobEntity{}
		var jobStatusModel = model.JobStatusEntity{}
		klog.Infof("==> start to UnionList jobs,now is %v", time.Now().Unix())
		jobEntities, totalCount, err = t.Store.UnionList(ctx, &jobModel, &jobStatusModel, "name", "jobname", &datastore.ListOptions{
			FilterOptions: filterOptions,
			SortBy: []datastore.SortOption{
				{
					Key:   "createTime",
					Order: datastore.SortOrderDescending,
				},
			},
			Page:     int(options.Page),
			PageSize: int(options.PageSize),
		})
		if err != nil {
			return nil, bcode.ErrorServerInternalError("list jobs failed,err:%v", err)
		}
		klog.Infof("==> end to UnionList jobs,now is %v", time.Now().Unix())
	} else {
		var jobModel = model.JobEntity{}
		klog.Infof("==> start to list jobs,now is %v", time.Now().Unix())
		jobEntities, err = t.Store.List(ctx, &jobModel, &datastore.ListOptions{
			FilterOptions: filterOptions,
			SortBy: []datastore.SortOption{
				{
					Key:   "createTime",
					Order: datastore.SortOrderDescending,
				},
			},
			Page:     int(options.Page),
			PageSize: int(options.PageSize),
		})
		if err != nil {
			return nil, bcode.ErrorServerInternalError("list jobs failed,err:%v", err)
		}
		totalCount, err = t.Store.Count(ctx, &model.JobEntity{}, &filterOptions)
		if err != nil {
			klog.Errorf("count jobs failed,err:%v", err)
		}
		klog.Infof("==> end to list jobs,now is %v", time.Now().Unix())
	}

	var result []*jobv1.Job
	for _, job := range jobEntities {
		jobItem := job.(*model.JobEntity)
		jobResult := helper.ConvertFromJobModel(jobItem)

		if options.StatusEnabled {
			if p, ok := t.jobProviders[jobItem.JobType]; ok {
				jobStatus, err := p.GetJobStatus(ctx, jobItem.ID)
				if err != nil {
					t.Logger.Errorf("get job status failed,err:%v", err)
				}
				jobResult.JobStatus = jobStatus
			}
		}
		result = append(result, jobResult)
	}
	return &jobv1.ListJobsResponse{
		Jobs:  result,
		Total: totalCount,
	}, nil
}

func (t *JobService) StopJob(ctx context.Context, request *jobv1.StopJobRequest) (*emptypb.Empty, error) {
	//权限校验
	account, ok := ctx.Value(constant.UserCtxKey).(string)
	if !ok {
		return nil, bcode.ErrorServerInternalError("get user from context failed")
	}
	if ok, err := t.CheckJobPermission(ctx, request.WorkspaceName, request.JobName, account); err != nil || !ok {
		if err != nil {
			return nil, err
		}
		return nil, bcode.ErrorPermissionNotAllowed("user[%s] has no permission to stop job[%s:%s]", account, request.WorkspaceName, request.JobName)
	}

	workspaceName := request.WorkspaceName
	jobName := request.JobName
	jobEntity, err := repository.GetJobForWorkspace(ctx, t.Store, workspaceName, jobName)
	if err != nil {
		return nil, err
	}
	jobStatusEntity := &model.JobStatusEntity{
		JobID: jobEntity.ID,
	}
	err = t.Store.Get(ctx, jobStatusEntity)
	if err != nil {
		if errors.Is(err, datastore.ErrRecordNotExist) {
			t.Logger.Warnf("training job status not found,jobID:%s,jobName:[%s], ignore changed", jobEntity.ID, request.JobName)
			return nil, bcode.ErrorJobNotFound("job[%s] status not found", request.JobName)
		}
	}

	runningDuration := jobStatusEntity.RunningDuration
	condition := jobStatusEntity.Conditions.FindCondition(model.JobConditionTypeJobScheduledReady)
	if condition.Status == metav1.ConditionTrue {
		runningDuration = time.Now().Sub(condition.LastTransitionTime.Time).String()
	}
	jobStatusEntity.RunningDuration = runningDuration
	jobStatusEntity.Phase = model.JobStateTerminated
	jobStatusEntity.Reason = "JobTerminated"
	jobStatusEntity.Message = fmt.Sprintf("Job[%s] terminated By %s", request.JobName, account)
	jobStatusEntity.UpdateTime = time.Now()
	jobStatusEntity.Conditions.UpsertCondition(metav1.Condition{
		Type:               model.JobConditionTypeJobAbortedReady,
		Status:             metav1.ConditionTrue,
		Reason:             "JobTerminated",
		Message:            fmt.Sprintf("Job[%s] aborted By %s", request.JobName, account),
		LastTransitionTime: metav1.Now(),
	})

	if err := t.Store.Put(ctx, jobStatusEntity); err != nil {
		return nil, bcode.ErrorServerInternalError("update job status failed,err:%v", err)
	}
	// 同步清理，还有个定时任务异步清理
	t.recycleTensorboard(ctx, jobEntity, jobStatusEntity)
	t.recycleVelaResource(ctx, jobEntity, jobStatusEntity)

	if jobStatusEntity.GCConditions.FindCondition(model.GCJobTensorboardCondition).Status == metav1.ConditionTrue &&
		jobStatusEntity.GCConditions.FindCondition(model.GCJobVelaApplicationCondition).Status == metav1.ConditionTrue {
		jobStatusEntity.GCState = model.GCStatusCompleted
	}
	if err = t.Store.Put(ctx, jobStatusEntity); err != nil {
		return nil, bcode.ErrorServerInternalError("update job status failed,err:%v", err)
	}
	return &emptypb.Empty{}, nil
}

func (t *JobService) DeleteJob(ctx context.Context, request *jobv1.DeleteJobRequest) (*emptypb.Empty, error) {
	//权限校验
	account, ok := ctx.Value(constant.UserCtxKey).(string)
	if !ok {
		return nil, bcode.ErrorServerInternalError("get user from context failed")
	}
	if ok, err := t.CheckJobPermission(ctx, request.WorkspaceName, request.JobName, account); err != nil || !ok {
		if err != nil {
			return nil, err
		}
		return nil, bcode.ErrorPermissionNotAllowed("user[%s] has no permission to delete job[%s:%s]", account, request.WorkspaceName, request.JobName)
	}

	workspaceName := request.WorkspaceName
	jobName := request.JobName
	jobEntity, err := repository.GetJobForWorkspace(ctx, t.Store, workspaceName, jobName)
	if err != nil {
		return nil, err
	}
	// 如果状态为空,直接删除
	jobStatusEntity, err := repository.GetJobStatusForJobID(ctx, t.Store, jobEntity.ID)
	if err != nil {
		if errors.Is(err, datastore.ErrRecordNotExist) {
			if err := t.Store.Delete(ctx, jobEntity); err != nil {
				return nil, bcode.ErrorServerInternalError("delete job failed,err:%v", err)
			}
			return &emptypb.Empty{}, nil
		} else {
			return nil, bcode.ErrorServerInternalError("get job status failed,err:%v", err)
		}
	}
	if jobStatusEntity.Phase == model.JobStateRunning {
		return nil, bcode.ErrorOperationNotAllowed("job is running,can't delete")
	}
	if p, ok := t.jobProviders[jobEntity.JobType]; ok {
		err = p.Clean(ctx, jobStatusEntity.ClusterName, jobEntity.ID)
		if err != nil {
			return nil, bcode.ErrorServerInternalError("clean job failed,err:%v", err)
		}
	}
	if err := t.Store.Delete(ctx, jobEntity); err != nil {
		return nil, bcode.ErrorServerInternalError("delete job failed,err:%v", err)
	}

	t.recycleTensorboard(ctx, jobEntity, jobStatusEntity)
	t.recycleVelaResource(ctx, jobEntity, jobStatusEntity)

	if jobStatusEntity.GCConditions.FindCondition(model.GCJobTensorboardCondition).Status == metav1.ConditionTrue &&
		jobStatusEntity.GCConditions.FindCondition(model.GCJobVelaApplicationCondition).Status == metav1.ConditionTrue {
		jobStatusEntity.GCState = model.GCStatusCompleted
	}
	if err = t.Store.Put(ctx, jobStatusEntity); err != nil {
		return nil, bcode.ErrorServerInternalError("update job status failed,err:%v", err)
	}

	return &emptypb.Empty{}, nil
}

func (t *JobService) recycleTensorboard(ctx context.Context, job *model.JobEntity, jobStatusEntity *model.JobStatusEntity) {
	var enableTensorboard bool

	switch job.JobType {
	case jobv1.JobType_PyTorchDDP.String():
		enableTensorboard = job.PytorchJobTemplate.TensorboardEnabled
	case jobv1.JobType_DeepSpeed.String():
		enableTensorboard = job.DeepSpeedJobTemplate.TensorboardEnabled
	case jobv1.JobType_SimpleTraining.String():
		enableTensorboard = job.SimpleTrainingJobTemplate.TensorboardEnabled
	}

	if !enableTensorboard {
		jobStatusEntity.GCConditions.UpsertCondition(metav1.Condition{
			Type:    model.GCJobTensorboardCondition,
			Status:  metav1.ConditionTrue,
			Reason:  "TensorboardDisabled",
			Message: "Tensorboard is disabled, skip recycle",
		})
		return
	}

	tensorboard := &automlv1alpha1.Tensorboard{}
	err := multicluster.Instance().GetLocalCluster().Direct().Get(ctx, client.ObjectKey{Namespace: getNamespaceName(job.WorkspaceName), Name: job.Name}, tensorboard)
	if err != nil {
		condition := metav1.Condition{
			Type:   model.GCJobTensorboardCondition,
			Status: metav1.ConditionFalse,
		}
		if apierrors.IsNotFound(err) {
			t.Logger.Infof("[recycleResource] tensorboard not found, skip recycle")
			condition.Status = metav1.ConditionTrue
			condition.Reason = "TensorboardNotFound"
			condition.Message = "Tensorboard not found, skip recycle"
		} else {
			t.Logger.Errorf("[recycleResource] get tensorboard failed, err: %v", err)
			condition.Reason = "GetTensorboardFailed"
			condition.Message = fmt.Sprintf("Get tensorboard failed, err: %v", err)
		}
		jobStatusEntity.GCConditions.UpsertCondition(condition)
		return
	}

	if err = multicluster.Instance().GetLocalCluster().Direct().Delete(ctx, tensorboard); err != nil {
		t.Logger.Errorf("[recycleResource] delete tensorboard failed, err: %v", err)
		jobStatusEntity.GCConditions.UpsertCondition(metav1.Condition{
			Type:    model.GCJobTensorboardCondition,
			Status:  metav1.ConditionFalse,
			Reason:  "DeleteTensorboardFailed",
			Message: fmt.Sprintf("Delete tensorboard failed, err: %v", err),
		})
	} else {
		t.Logger.Infof("[recycleResource] delete tensorboard success")
		jobStatusEntity.GCConditions.UpsertCondition(metav1.Condition{
			Type:    model.GCJobTensorboardCondition,
			Status:  metav1.ConditionTrue,
			Reason:  "DeleteTensorboardSuccess",
			Message: "Delete tensorboard success",
		})
	}
}

func (t *JobService) recycleVelaResource(ctx context.Context, job *model.JobEntity, jobStatus *model.JobStatusEntity) {
	projectName := helper.GetVelaProjectNameFromJob(job)
	appName := job.Name
	envName := helper.GetVelaEnvNameFromJob(job)
	_, err := vela.Instance().GetApplicationStatus(ctx, appName, envName)
	if err != nil {
		if errors.Is(err, vela.ErrorApplicationNotFound) {
			t.Logger.Infof("[recycleResource] application[%s] not created, skip recycle", appName)
			jobStatus.GCConditions.UpsertCondition(metav1.Condition{
				Type:    model.GCJobVelaApplicationCondition,
				Status:  metav1.ConditionTrue,
				Reason:  "ApplicationNotFound",
				Message: fmt.Sprintf("Application[%s] not created, skip recycle", appName),
			})
		} else {
			t.Logger.Errorf("[recycleResource] get vela application status failed, err: %v", err)
			jobStatus.GCConditions.UpsertCondition(metav1.Condition{
				Type:    model.GCJobVelaApplicationCondition,
				Status:  metav1.ConditionFalse,
				Reason:  "GetApplicationStatusFailed",
				Message: fmt.Sprintf("Get vela application status failed, err: %v", err),
			})
		}
		return
	}

	if err = vela.Instance().RecycleApplicationEnv(ctx, appName, envName); err != nil {
		t.Logger.Errorf("[recycleResource] recycle vela env failed, err: %v", err)
		jobStatus.GCConditions.UpsertCondition(metav1.Condition{
			Type:    model.GCJobVelaApplicationCondition,
			Status:  metav1.ConditionFalse,
			Reason:  "RecycleApplicationEnvFailed",
			Message: fmt.Sprintf("Recycle vela env failed, err: %v", err),
		})
		return
	}

	// 等待vela回收完成
	err = wait.PollImmediateWithContext(ctx, 2*time.Second, 60*time.Second, func(ctx context.Context) (done bool, err error) {
		appStatus, err := vela.Instance().GetApplicationStatus(ctx, appName, envName)
		if err != nil {
			return false, err
		}
		if appStatus.Status != nil {
			t.Logger.Infof("wait vela env recycle, appName: %s, envName: %s", appName, envName)
			return false, nil
		}
		return true, nil
	})
	if err != nil {
		t.Logger.Errorf("[recycleResource] wait vela env recycle failed, err: %v", err)
		jobStatus.GCConditions.UpsertCondition(metav1.Condition{
			Type:    model.GCJobVelaApplicationCondition,
			Status:  metav1.ConditionFalse,
			Reason:  "WaitRecycleEnvFailed",
			Message: fmt.Sprintf("Wait vela env recycle failed, err: %v", err),
		})
		return
	}

	if err = vela.Instance().DeleteApplication(ctx, projectName, appName); err != nil {
		t.Logger.Errorf("[recycleResource] delete vela app failed, err: %v", err)
		jobStatus.GCConditions.UpsertCondition(metav1.Condition{
			Type:    model.GCJobVelaApplicationCondition,
			Status:  metav1.ConditionFalse,
			Reason:  "DeleteApplicationFailed",
			Message: fmt.Sprintf("Delete vela app failed, err: %v", err),
		})
	} else {
		t.Logger.Infof("[recycleResource] delete vela app success")
		jobStatus.GCConditions.UpsertCondition(metav1.Condition{
			Type:    model.GCJobVelaApplicationCondition,
			Status:  metav1.ConditionTrue,
			Reason:  "DeleteApplicationSuccess",
			Message: "Delete vela app success",
		})
	}
}

func (t *JobService) GetJobBase(ctx context.Context, request *jobv1.GetJobBaseRequest) (*jobv1.Job, error) {
	workspaceName := request.WorkspaceName
	jobName := request.JobName
	jobEntity, err := repository.GetJobForWorkspace(ctx, t.Store, workspaceName, jobName)
	if err != nil {
		return nil, err
	}
	return helper.ConvertFromJobModel(jobEntity), nil
}

func (t *JobService) GetJobDetail(ctx context.Context, request *jobv1.GetJobRequest) (*jobv1.Job, error) {
	workspaceName := request.WorkspaceName
	jobName := request.JobName
	jobEntity, err := repository.GetJobForWorkspace(ctx, t.Store, workspaceName, jobName)
	if err != nil {
		return nil, err
	}
	jobStatusEntity, err := repository.GetJobStatusForJobID(ctx, t.Store, jobEntity.ID)
	if err != nil {
		return nil, err
	}
	var jobStatus *jobv1.JobStatus
	trainingJob := helper.ConvertFromJobModel(jobEntity)
	//如果任务没有被提交到Volcano，则不需要查询Volcano的Job状态
	if jobStatusEntity.SubmitState != model.Submitted {
		jobStatus = &jobv1.JobStatus{
			State:              string(volcanobatchv1alpha1.Pending),
			CreateTimestamp:    util.TimeFormat(jobEntity.CreateTime),
			LastTransitionTime: util.TimeFormat(jobEntity.UpdateTime),
			Reason:             jobStatusEntity.Reason,
			Message:            "该Job已提交集群, 正在等待集群资源, 排队等待中...",
		}
	} else {
		// 如果是completed状态,过段时间会回收
		if jobProvider, ok := t.jobProviders[jobEntity.JobType]; ok {
			jobStatus, err = jobProvider.GetJobStatus(ctx, jobEntity.ID)
			if err != nil {
				return nil, err
			}
		} else {
			return nil, bcode.ErrorServerInternalError("job provider not found")
		}
	}
	trainingJob.JobStatus = jobStatus
	return trainingJob, nil
}

func (t *JobService) GetJobTasks(ctx context.Context, request *jobv1.GetJobTasksRequest) (*jobv1.ListJobTaskStatus, error) {
	jobEntity, err := repository.GetJobForWorkspace(ctx, t.Store, request.WorkspaceName, request.JobName)
	if err != nil {
		if errors.Is(err, datastore.ErrRecordNotExist) {
			return nil, bcode.ErrorJobNotFound("job not found")
		}
		return nil, bcode.ErrorServerInternalError("get job failed,err:%v", err)
	}
	jobStatusEntity, err := repository.GetJobStatusForJobID(ctx, t.Store, jobEntity.ID)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("get job status failed,err:%v", err)
	}
	var res []*jobv1.TaskStatus
	if p, ok := t.jobProviders[jobEntity.JobType]; ok {
		tasks, err := p.GetJobTaskStatus(ctx, jobEntity.ID, jobStatusEntity)
		if err != nil {
			return nil, bcode.ErrorServerInternalError("get job tasks failed,err:%v", err)
		}
		if request.ShowTheLatestVersion == "" {
			return &jobv1.ListJobTaskStatus{TaskStatus: tasks}, nil
		}

		var latestVersion string
		for _, task := range tasks {
			if task.JobVersion > latestVersion {
				latestVersion = task.JobVersion
			}
		}

		for _, task := range tasks {
			if (request.ShowTheLatestVersion == "true" && task.JobVersion == latestVersion) ||
				(request.ShowTheLatestVersion == "false" && task.JobVersion != latestVersion) {
				res = append(res, task)
			}
		}

		return &jobv1.ListJobTaskStatus{TaskStatus: res}, nil

	}
	return nil, nil
}

func (t *JobService) GetJobVolumes(ctx context.Context, request *jobv1.GetJobVolumeRequest) (*jobv1.ListJobVolumes, error) {
	jobEntity, err := repository.GetJobForWorkspace(ctx, t.Store, request.WorkspaceName, request.JobName)
	if err != nil {
		if errors.Is(err, datastore.ErrRecordNotExist) {
			return nil, bcode.ErrorJobNotFound("job not found")
		}
		return nil, bcode.ErrorServerInternalError("get job failed,err:%v", err)
	}
	if p, ok := t.jobProviders[jobEntity.JobType]; ok {
		volumes, err := p.GetJobVolumes(ctx, jobEntity.ID)
		if err != nil {
			return nil, bcode.ErrorServerInternalError("get job volumes failed,err:%v", err)
		}
		return &jobv1.ListJobVolumes{
			Volumes: volumes,
		}, nil
	}

	return nil, nil
}

func (t *JobService) UpdateJob(ctx context.Context, request *jobv1.UpdateJobRequest) (*emptypb.Empty, error) {
	jobEntity, err := repository.GetJobForWorkspace(ctx, t.Store, request.WorkspaceName, request.JobName)
	if err != nil {
		if errors.Is(err, datastore.ErrRecordNotExist) {
			return nil, bcode.ErrorJobNotFound("job not found")
		}
		return nil, bcode.ErrorServerInternalError("get job failed,err:%v", err)
	}
	if request.Creator != "" {
		jobEntity.Creator = request.Creator
	}
	if request.Description != "" {
		jobEntity.Description = request.Description
	}
	if request.Priority != "" {
		jobEntity.Priority = request.Priority
	}
	if len(request.Members) > 0 {
		jobEntity.Members = request.Members
	}
	if err := t.Store.Put(ctx, jobEntity); err != nil {
		return nil, bcode.ErrorServerInternalError("update job failed,err:%v", err)
	}
	return &emptypb.Empty{}, nil
}

func (t *JobService) StopJobTask(ctx context.Context, request *jobv1.StopJobTaskRequest) (*emptypb.Empty, error) {
	//权限校验
	account, ok := ctx.Value(constant.UserCtxKey).(string)
	if !ok {
		return nil, bcode.ErrorServerInternalError("get user from context failed")
	}
	if ok, err := t.CheckJobPermission(ctx, request.WorkspaceName, request.JobName, account); err != nil || !ok {
		if err != nil {
			return nil, err
		}
		return nil, bcode.ErrorPermissionNotAllowed("user[%s] has no permission to stop job task[%s:%s]", account, request.WorkspaceName, request.JobName)
	}

	jobEntity, err := repository.GetJobForWorkspace(ctx, t.Store, request.WorkspaceName, request.JobName)
	if err != nil {
		if errors.Is(err, datastore.ErrRecordNotExist) {
			return nil, bcode.ErrorJobNotFound("job not found")
		}
		return nil, bcode.ErrorServerInternalError("get job failed,err:%v", err)
	}
	jobStatus, err := repository.GetJobStatusForJobID(ctx, t.Store, jobEntity.ID)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("get job status failed,err:%v", err)
	}
	clusterName := jobStatus.ClusterName
	if clusterName == "" {
		return nil, bcode.ErrorJobNotFound("job cluster name not found")
	}
	k8sCluster := multicluster.Instance().GetCluster(clusterName)
	if k8sCluster == nil {
		return nil, bcode.ErrorInvalidArgument("cluster[%s] not found", clusterName)
	}
	gracePeriodSeconds := int64(0)
	uid := types.UID(request.TaskID)
	propagationPolicy := metav1.DeletePropagationForeground
	deleteOptions := client.DeleteOptions{
		GracePeriodSeconds: &gracePeriodSeconds,
		PropagationPolicy:  &propagationPolicy,
	}

	if err := k8sCluster.Direct().Delete(ctx, &corev1.Pod{
		ObjectMeta: metav1.ObjectMeta{
			Name:      request.PodName,
			UID:       uid,
			Namespace: getNamespaceName(jobEntity.WorkspaceName),
		},
	}, &deleteOptions); err != nil {
		return nil, bcode.ErrorServerInternalError("delete pod force failed,err:%v", err)
	}
	return nil, nil
}

// CheckJobIsExist 检查任务是否已经存在
func (t *JobService) CheckJobIsExist(ctx context.Context, request *jobv1.CheckJobIsExistRequest) (*jobv1.CheckJobIsExistResponse, error) {
	if exists, err := repository.JobExists(ctx, t.Store, request.WorkspaceName, request.JobName); err != nil {
		return nil, bcode.ErrorServerInternalError("check job exists failed,err:%v", err)
	} else if exists {
		return &jobv1.CheckJobIsExistResponse{
			Status: int32(422),
			Errors: "当前任务已存在，请更换任务名称",
		}, nil
	}
	return &jobv1.CheckJobIsExistResponse{
		Status: int32(0),
		Errors: "当前任务不存在，可以创建",
	}, nil
}

func (t *JobService) UpdateMembers(ctx context.Context, request *jobv1.UpdateMembersRequest) (*emptypb.Empty, error) {
	//权限校验
	account, ok := ctx.Value(constant.UserCtxKey).(string)
	if !ok {
		return nil, bcode.ErrorServerInternalError("get user from context failed")
	}
	if ok, err := t.CheckJobPermission(ctx, request.WorkspaceName, request.JobName, account); err != nil || !ok {
		if err != nil {
			return nil, err
		}
		return nil, bcode.ErrorPermissionNotAllowed("user[%s] has no permission to update job[%s:%s]", account, request.WorkspaceName, request.JobName)
	}
	if !util.StringInSlice(account, request.Members) {
		request.Members = append(request.Members, account)
	}

	jobEntity, err := repository.GetJobForWorkspace(ctx, t.Store, request.WorkspaceName, request.JobName)
	if err != nil {
		if errors.Is(err, datastore.ErrRecordNotExist) {
			return nil, bcode.ErrorJobNotFound("job not found")
		}
		return nil, bcode.ErrorServerInternalError("get job failed,err:%v", err)
	}
	jobEntity.Members = request.Members
	if err := t.Store.Put(ctx, jobEntity); err != nil {
		return nil, bcode.ErrorServerInternalError("update job failed,err:%v", err)
	}
	return &emptypb.Empty{}, nil
}

// CheckJobPermission check the permission of the job
func (s *JobService) CheckJobPermission(ctx context.Context, workspaceName, jobName, account string) (bool, error) {
	if ok, err := s.WorkspaceService.IsManager(ctx, workspaceName, account); err != nil || ok {
		if err != nil {
			return false, err
		}
		return true, nil
	}
	jobEntity, err := repository.GetJobForWorkspace(ctx, s.Store, workspaceName, jobName)
	if err != nil {
		if errors.Is(err, datastore.ErrRecordNotExist) {
			return false, bcode.ErrorJobNotFound("job[%s:%s] not found", workspaceName, jobName)
		}
		return false, bcode.ErrorServerInternalError("get job[%s:%s] failed: %v", workspaceName, jobName, err)
	}
	if util.StringInSlice(account, jobEntity.Members) {
		return true, nil
	}
	return false, nil
}
