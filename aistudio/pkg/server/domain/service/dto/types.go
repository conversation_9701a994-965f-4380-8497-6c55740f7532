package dto

import (
	corev1 "k8s.io/api/core/v1"
	"time"
)

type QueryRangeNodeCPUUsageRequest struct {
	IP        string        `json:"ip"`
	Step      time.Duration `json:"step"`
	StartTime time.Time     `json:"startTime"`
	EndTime   time.Time     `json:"endTime"`
}

type QueryRangeResult struct {
	Series []*Series `json:"series"`
}

type Series struct {
	Labels map[string]string `json:"labels"`
	Data   []float64         `json:"data"`
}

type QueryRangeRequest struct {
	MetricsName string        `json:"metricsName"`
	Values      []string      `json:"values"`
	Step        time.Duration `json:"step"`
	StartTime   time.Time     `json:"startTime"`
	EndTime     time.Time     `json:"endTime"`
}

type BindNicaWorkspaceRequest struct {
	WorkspaceName     string `json:"workspaceName"`
	NicaWorkspaceName string `json:"nicaWorkspaceName"`
}

type RemoveClusterQueueRequest struct {
	WorkspaceName string
	QueueName     string
	ClusterName   string
}

type GetPodInsightRecordsRequest struct {
	PodName       string
	WorkspaceName string
	Region        string
	StartTime     int32
	EndTime       int32
	Page          int32
	PageSize      int32
}

type GetPodInsightRecordsResponse struct {
	PodInsightRecords []*PodInsightRecord `json:"podInsightRecords"`
	Total             int32               `json:"total"`
}

type PodInsightRecord struct {
	ClusterName       string           `json:"clusterName"`
	PodName           string           `json:"podName"`
	Namespace         string           `json:"namespace"`
	PodRef            *PodRef          `json:"podRef"`
	PodEvents         []corev1.Event   `json:"podEvents,omitempty"`
	PodStatus         corev1.PodStatus `json:"podStatus,omitempty"`
	ContainerLogs     []ContainerLog   `json:"containerLogs,omitempty"`
	NodeEvents        []corev1.Event   `json:"nodeEvents,omitempty"`
	RestartCount      int32            `json:"restartCount,omitempty"`
	InitContainerLogs []ContainerLog   `json:"initContainerLogs,omitempty"`
	Alerts            []Alert          `json:"alerts,omitempty"` // 本次不实现
	Reason            string           `json:"reason,omitempty"`
	Message           string           `json:"message,omitempty"`
	CreateTimeStamp   float64          `json:"createTimestamp,omitempty"`
}

type ContainerLog struct {
	ContainerName string   `json:"containerName"`
	Logs          []string `json:"logs"` // 最近 500行日志
}

type Alert struct {
	AlertType string `json:"alertType"`
}

type PodKind string

const (
	PodKindDevMachine      PodKind = "devMachine"
	PodKindTrainingJob     PodKind = "trainingJob"
	PodKindDeploymentGroup PodKind = "deploymentGroup"
)

type DevMachinePodRef struct {
	DevMachineName        string `json:"devMachineName"`
	WorkspaceName         string `json:"workspaceName"`
	QueueName             string `json:"queueName"`
	Region                string `json:"region"`
	Creator               string `json:"creator"`
	Updater               string `json:"updater"`
	DevMachineDisplayName string `json:"devMachineDisplayName"`
	StartBy               string `json:"startBy"`
	StopBy                string `json:"stopBy"`
}

type TrainingJobPodRef struct {
	TrainingJobName        string `json:"trainingJobName"`
	WorkspaceName          string `json:"workspaceName"`
	QueueName              string `json:"queueName"`
	Region                 string `json:"region"`
	Creator                string `json:"creator"`
	TrainingJobDisplayName string `json:"trainingJobDisplayName"`
}

type DeploymentGroupPodRef struct {
	DeploymentGroupName        string `json:"deploymentGroupName"`
	WorkspaceName              string `json:"workspaceName"`
	QueueName                  string `json:"queueName"`
	Region                     string `json:"region"`
	Creator                    string `json:"creator"`
	Updater                    string `json:"updater"`
	DeploymentGroupDisplayName string `json:"deploymentGroupDisplayName"`
	ApplicationName            string `json:"applicationName"`
	ApplicationType            string `json:"applicationType"`
	EnvironmentType            string `json:"environmentType"`
}

type PodRef struct {
	PodKind         PodKind               `json:"podKind"`
	DevMachine      DevMachinePodRef      `json:"devMachine"`
	TrainingJob     TrainingJobPodRef     `json:"trainingJob"`
	DeploymentGroup DeploymentGroupPodRef `json:"deploymentGroup"`
}

type CreateCubeFSVolumeRequest struct {
	Name            string `json:"name"`
	Owner           string `json:"owner"`
	Capacity        uint64 `json:"capacity"`
	CrossZone       bool   `json:"crossZone"`
	Business        string `json:"business"`
	DefaultPriority bool   `json:"defaultPriority"`
	VolType         int    `json:"volType"` //0为标准卷， 1为低频卷
	ReplicaNumber   int    `json:"replicaNumber"`
	CacheCap        int    `json:"cacheCap"`
	ZoneName        string `json:"zoneName"`
}

// JobTriggerRecord 任务触发记录
type JobTriggerRecord struct {
	JobName         string `json:"jobName"`
	JobDisplayName  string `json:"jobDisplayName"`
	WorkspaceName   string `json:"workspaceName"`
	JobTemplateName string `json:"jobTemplateName"`
	TriggerName     string `json:"triggerName"`
	TriggerType     string `json:"triggerType"`
	Creator         string `json:"creator"`
	TriggerTime     string `json:"triggerTime"`
	Description     string `json:"description"`
	Status          string `json:"status"`
}
