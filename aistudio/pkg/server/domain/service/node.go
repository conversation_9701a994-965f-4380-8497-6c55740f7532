package service

import (
	"context"
	"fmt"
	"git.lianjia.com/cloudnative/kic/kic-platform/aistudio/pkg/constant"
	appv1 "git.lianjia.com/cloudnative/kic/kic-platform/apis/aistudio/application/v1"
	"git.lianjia.com/cloudnative/kic/kic-platform/apis/aistudio/bcode"
	queuev1 "git.lianjia.com/cloudnative/kic/kic-platform/apis/aistudio/queue/v1"
	v1 "git.lianjia.com/cloudnative/kic/kic-platform/apis/aistudio/resourcegroup/v1"
	"git.lianjia.com/cloudnative/kic/kic-platform/apis/common"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/multicluster"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/property"
	kratoslog "github.com/go-kratos/kratos/v2/log"
	"google.golang.org/protobuf/types/known/emptypb"
	corev1 "k8s.io/api/core/v1"
	policyv1 "k8s.io/api/policy/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/fields"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/apimachinery/pkg/selection"
	"k8s.io/client-go/util/retry"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/controller/controllerutil"
	"strconv"
	"strings"
	vcschedulingv1beta1 "volcano.sh/apis/pkg/apis/scheduling/v1beta1"
)

var _ v1.NodeServiceHTTPServer = (*NodeService)(nil)

var _ Interface = (*NodeService)(nil)

func init() {
}

// NodeService 节点服务, 主要用于做一些节点查询、节点操作等
type NodeService struct {
	ClusterManager     *ClusterManager     `inject:"clusterManager"`
	Logger             *kratoslog.Helper   `inject:"logger"`
	ApplicationService *ApplicationService `inject:"applicationService"`
	QueueService       *QueueService       `inject:"queueService"`
}

func NewNodeService() *NodeService {
	return &NodeService{}
}

// GetNodeMetric 获取节点的监控指标
func (n *NodeService) GetNodeMetric(ctx context.Context, request *v1.GetNodeMetricRequest) (*v1.NodeMetric, error) {
	return n.ClusterManager.GetNodeMetric(ctx, request.GetIp())
}

// GetNodeMetricsView 获取空间节点的视图
func (n *NodeService) GetNodeMetricsView(ctx context.Context, request *v1.GetNodeViewRequest) (*v1.NodeMetricView, error) {
	nodes, err := n.ClusterManager.GetNodeDetails(ctx, &v1.ListNodeDetailOptions{
		WorkspaceName: request.WorkspaceName,
	})
	if err != nil {
		return nil, bcode.ErrorServerInternalError("get workspace[%s] nodes failed: %v", request.WorkspaceName, err)
	}
	var nodeViews []*v1.NodeMetric
	for _, node := range nodes {
		nodeMetrics, err := n.ClusterManager.GetNodeMetric(ctx, node.Ip)
		if err != nil {
			return nil, bcode.ErrorServerInternalError("get workspace[%s] node[%s] metrics failed: %v", request.WorkspaceName, node.Ip, err)
		}
		nodeViews = append(nodeViews, nodeMetrics)
	}
	return &v1.NodeMetricView{
		NodeMetricView: nodeViews,
	}, nil
}

func (n *NodeService) Init(ctx context.Context, properties property.EnvironmentProperty) error {
	return nil
}

// GetNodeDetail 获取节点的详细信息
func (n *NodeService) GetNodeDetail(ctx context.Context, request *v1.GetNodeDetailRequest) (*v1.NodeDetail, error) {
	nodeDetail, err := n.ClusterManager.GetNodeDetailByNodeIP(ctx, request.Ip)
	if err != nil {
		return nil, err
	}
	return nodeDetail, nil
}

func (n *NodeService) ListNodeDetailForFrontend(ctx context.Context, options *v1.ListNodeDetailForFrontendOptions) (*v1.CascadeNodeDetails, error) {
	getNodesOption := &v1.ListNodeDetailOptions{
		WorkspaceName: options.WorkspaceName,
		EnableStatus:  true,
	}
	nodes, err := n.ClusterManager.GetNodeDetails(ctx, getNodesOption)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("list node detail failed, err:%s", err.Error())
	}

	nodeDetailsMap := make(map[string]*v1.CascadeNodeDetail)
	cascadeNodeDetails := make([]*v1.CascadeNodeDetail, 0, len(nodes))
	for _, node := range nodes {
		if node.QueueName != "" && node.QueueName != options.QueueName {
			continue
		}
		if options.Ip != "" {
			if !strings.Contains(node.Ip, options.Ip) {
				continue
			}
		}
		nodeView := fmt.Sprintf("%s(%s)", node.Ip, node.Status)
		child := &v1.NodeDetailChildren{
			Label: nodeView,
			Value: node.Ip,
		}
		if nodeDetails, ok := nodeDetailsMap[node.NodeSpecificationName]; ok {
			nodeDetails.Children = append(nodeDetails.Children, child)
		} else {
			nodeDetailsMap[node.NodeSpecificationName] = &v1.CascadeNodeDetail{
				Label:    node.NodeSpecificationName,
				Children: []*v1.NodeDetailChildren{child},
			}
			cascadeNodeDetails = append(cascadeNodeDetails, nodeDetailsMap[node.NodeSpecificationName])
		}
	}

	return &v1.CascadeNodeDetails{
		Nodes: cascadeNodeDetails,
	}, nil

}

func (n *NodeService) DisableNode(ctx context.Context, request *v1.DisableNodeRequest) (*emptypb.Empty, error) {
	node, err := n.ClusterManager.GetNodeMetricByNodeIP(ctx, request.Ip)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("list node detail failed, err:%s", err.Error())
	}
	clusterName := node.ClusterName
	k8sCluster := multicluster.Instance().GetCluster(clusterName)
	if k8sCluster == nil {
		return nil, bcode.ErrorInvalidArgument("cluster name:%s not found", clusterName)
	}
	var newNode = &corev1.Node{}
	err = k8sCluster.Direct().Get(ctx, client.ObjectKey{Name: node.NodeName}, newNode)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("get node by name:%s failed: %v", node.NodeName, err)
	}
	if request.Disable {
		newNode.Spec.Unschedulable = true
		if err = k8sCluster.Direct().Update(ctx, newNode, &client.UpdateOptions{}); err != nil {
			return nil, bcode.ErrorServerInternalError("update node by name:%s failed: %v", node.NodeName, err)
		}
	} else {
		newNode.Spec.Unschedulable = false
		if err = k8sCluster.Direct().Update(ctx, newNode, &client.UpdateOptions{}); err != nil {
			return nil, bcode.ErrorServerInternalError("update node by name:%s failed: %v", node.NodeName, err)
		}
	}
	return &emptypb.Empty{}, nil
}

func (n *NodeService) GetNodeWorkloads(ctx context.Context, request *v1.GetNodeWorkloadsRequest) (*v1.GetNodeWorkloadsResponse, error) {
	node, err := n.ClusterManager.GetNodeMetricByNodeIP(ctx, request.Ip)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("list node detail failed, err:%s", err.Error())
	}
	clusterName := node.ClusterName
	k8sCluster := multicluster.Instance().GetCluster(clusterName)
	if k8sCluster == nil {
		return nil, bcode.ErrorInvalidArgument("cluster name:%s not found", clusterName)
	}
	var newNode = &corev1.Node{}
	err = k8sCluster.Direct().Get(ctx, client.ObjectKey{Name: node.NodeName}, newNode)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("get node by name:%s failed: %v", node.NodeName, err)
	}
	var workloads []*v1.NodeWorkload
	labelSelector := labels.NewSelector()
	requirement, err := labels.NewRequirement(constant.KICManagedLabelKey, selection.Equals, []string{"kic"})

	if err != nil {
		return nil, bcode.ErrorServerInternalError("create label requirement failed, err:%s", err.Error())
	}
	labelSelector = labelSelector.Add(*requirement)

	fieldSelector, err := fields.ParseSelector(
		fmt.Sprintf("spec.nodeName=%s,status.phase!=Succeeded,status.phase!=Failed",
			node.NodeName),
	)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("parse field selector failed: %v", err)
	}

	// 获取节点上的pod
	pods := &corev1.PodList{}
	err = k8sCluster.Direct().List(ctx, pods, &client.ListOptions{
		LabelSelector: labelSelector,
		FieldSelector: fieldSelector,
	})
	if err != nil {
		return nil, bcode.ErrorServerInternalError("list pod failed, err:%s", err.Error())
	}

	for _, pod := range pods.Items {
		InstanceName := pod.Name
		workloadName := pod.Labels[constant.WorkloadNameLabelKey]
		workloadType := pod.Labels[constant.WorkloadTypeLabelKey]
		workloadStatus := pod.Status.Phase

		if workloadName == "" || workloadType == "" {
			continue
		}

		var appType int32
		if v, exist := pod.Labels[constant.ApplicationTypeLabelKey]; exist {
			switch v {
			case v1.NodeWorkload_WebService.String():
				appType = int32(v1.NodeWorkload_WebService) // 0
			case v1.NodeWorkload_Inference.String():
				appType = int32(v1.NodeWorkload_Inference) // 1
			default:
				appType = int32(v1.NodeWorkload_Unknown) // 2
			}
		} else {
			appType = int32(v1.NodeWorkload_Unknown)
		}

		var (
			totalCpuRequest    resource.Quantity
			totalMemRequest    resource.Quantity
			totalGpuRequest    int64
			totalGpuMemRequest int64
		)

		for _, container := range pod.Spec.Containers {
			// CPU请求
			if req, ok := container.Resources.Requests[corev1.ResourceCPU]; ok {
				totalCpuRequest.Add(req)
			}
			// 内存请求
			if req, ok := container.Resources.Requests[corev1.ResourceMemory]; ok {
				totalMemRequest.Add(req)
			}
			// NVIDIA GPU数量
			if req, ok := container.Resources.Requests[constant.ResourceNvidiaGPU]; ok {
				totalGpuRequest += req.Value()
			}
			// NVIDIA显存
			if req, ok := container.Resources.Requests[constant.ResourceNvidiaGPUMemory]; ok {
				totalGpuMemRequest += req.Value()
			}
		}

		var workloadDisplayName string
		switch workloadType {
		case constant.WorkloadTypeDevMachine:
			workloadDisplayName = pod.Annotations[constant.DevMachineDisplayNameAnnotationKey]
		case constant.WorkloadTypeTrainingJob:
			workloadDisplayName = pod.Annotations[constant.JobDisplayNameAnnotationKey]
		case constant.WorkloadTypeDeploymentGroup:
			workloadDisplayName = pod.Annotations[constant.DeploymentGroupDisplayNameAnnotationKey]
		}

		workloads = append(workloads, &v1.NodeWorkload{
			InstanceName:        InstanceName,
			WorkloadName:        workloadName,
			WorkloadType:        workloadType,
			WorkloadDisplayName: workloadDisplayName,
			WorkloadStatus:      string(workloadStatus),
			Creator:             pod.Labels[constant.CreateByLabelKey],
			QueueName:           pod.Labels[constant.QueueNameLabelKey],
			Region:              pod.Labels[constant.RegionLabelKey],
			AppName:             pod.Labels[constant.ApplicationNameLabelKey],
			AppType:             v1.NodeWorkload_AppType(appType),
			DeploymentGroupName: pod.Labels[constant.DeploymentGroupNameLabelKey],
			DeploymentGroupType: pod.Labels[constant.ComponentTypeLabelKey],
			Specification: &common.Specification{
				CpuNum:    int32(totalCpuRequest.Value()),
				MemoryGiB: int32(totalMemRequest.ScaledValue(resource.Giga)),
				GpuNum:    int32(totalGpuRequest),
				GpuMem:    int32(totalGpuMemRequest),
			},
		})
	}
	return &v1.GetNodeWorkloadsResponse{
		NodeWorkloads: workloads,
		Total:         int32(len(workloads)),
	}, nil
}

func (n *NodeService) EvictNodeWorkloads(ctx context.Context, request *v1.EvictNodeWorkloadsRequest) (*emptypb.Empty, error) {
	// 1. 获取节点工作负载
	workloads, err := n.GetNodeWorkloads(ctx, &v1.GetNodeWorkloadsRequest{
		Ip:            request.Ip,
		WorkspaceName: request.WorkspaceName,
	})
	if err != nil {
		return nil, bcode.ErrorServerInternalError("get nodeMetric workloads failed, err:%s", err.Error())
	}
	// 2. 如果有单副本，直接返回禁止修改
	// 3. 如果有多副本的部署组，需要判断是否有多副本的部署组在一个节点上，如果有，直接返回禁止修改

	deploymentGroupCount := make(map[string]int)
	for _, workload := range workloads.NodeWorkloads {
		if workload.WorkloadType == constant.WorkloadTypeDeploymentGroup {
			deploymentGroupCount[workload.WorkloadName]++
		}
	}
	var info []string
	for k, v := range deploymentGroupCount {
		deploymentGroupDetail, err := n.ApplicationService.GetDeploymentGroupDetail(ctx, &appv1.GetDeploymentStatusRequest{
			DeploymentGroupId: k,
		})
		if err != nil {
			return nil, bcode.ErrorServerInternalError("get deployment group detail failed, err:%s", err.Error())
		}

		// TODO 动态扩缩容和手动扩缩容的情况（目前没有改变ReplicaScheduling，会导致数据不对）
		var replicas int
		if deploymentGroupDetail.ReplicaScheduling != nil && deploymentGroupDetail.ReplicaScheduling.ReplicaSchedulingType == appv1.ReplicaScheduling_Overrides {
			for _, replicasOverrides := range deploymentGroupDetail.ReplicaScheduling.ReplicasOverrides {
				replicas += int(replicasOverrides.Replicas)
			}
		}
		if v == replicas {
			info = append(info, fmt.Sprintf("deployment group:%s has %d replicas on the nodeMetric；", k, replicas))
		}
	}
	if len(info) > 0 {
		return nil, bcode.ErrorForbiddenOperation("不可以删除副本数都在同一节点上的部署组: " + strings.Join(info, ",") + " 请手动回收部署组！")
	}

	// 4. 标记节点不可调度
	nodeMetric, err := n.ClusterManager.GetNodeMetricByNodeIP(ctx, request.Ip)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("list nodeMetric detail failed, err:%s", err.Error())
	}
	clusterName := nodeMetric.ClusterName
	k8sCluster := multicluster.Instance().GetCluster(clusterName)
	if k8sCluster == nil {
		return nil, bcode.ErrorInvalidArgument("cluster name:%s not found", clusterName)
	}

	err = retry.RetryOnConflict(retry.DefaultRetry, func() error {
		var newNode = &corev1.Node{}
		if err = k8sCluster.Direct().Get(ctx, client.ObjectKey{Name: nodeMetric.NodeName}, newNode); err != nil {
			return err
		}
		newNode.Spec.Unschedulable = true
		return k8sCluster.Direct().Update(ctx, newNode, &client.UpdateOptions{})
	})
	if err != nil {
		return nil, bcode.ErrorServerInternalError("cordon nodeMetric failed, err:%s", err.Error())
	}

	// 5. 驱逐节点上的pod（开发机、训练任务、部署组）
	if err = evictAllPods(ctx, k8sCluster.Direct(), nodeMetric.NodeName); err != nil {
		return nil, bcode.ErrorServerInternalError("evict all pods failed, err:%s", err.Error())
	}
	return &emptypb.Empty{}, nil
}

func (n *NodeService) ChangeNodeGpuAllocationMode(ctx context.Context, request *v1.ChangeNodeGpuAllocationModeRequest) (*emptypb.Empty, error) {
	// 1. 获取节点工作负载，如果有工作负载，直接返回禁止修改
	workloads, err := n.GetNodeWorkloads(ctx, &v1.GetNodeWorkloadsRequest{
		Ip:            request.Ip,
		WorkspaceName: request.WorkspaceName,
	})
	if err != nil {
		return nil, bcode.ErrorServerInternalError("get node workloads failed, err:%s", err.Error())
	}
	if workloads.Total > 0 {
		return nil, bcode.ErrorForbiddenOperation("node has workloads, can not change gpu allocation mode")
	}

	// 2. 获取节点信息
	nodeMetric, err := n.ClusterManager.GetNodeMetricByNodeIP(ctx, request.Ip)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("list node detail failed, err:%s", err.Error())
	}
	clusterName := nodeMetric.ClusterName
	k8sCluster := multicluster.Instance().GetCluster(clusterName)
	if k8sCluster == nil {
		return nil, bcode.ErrorInvalidArgument("cluster name:%s not found", clusterName)
	}
	node := &corev1.Node{}
	if err := k8sCluster.Direct().Get(ctx, client.ObjectKey{Name: nodeMetric.NodeName}, node); err != nil {
		return nil, bcode.ErrorServerInternalError("get node failed, err:%s", err.Error())
	}

	// 如果不是GPU节点，直接返回错误
	if _, exist := node.Labels[constant.GPUNodeLabelKey]; !exist {
		return nil, bcode.ErrorInvalidArgument("node:%s has no gpu label", nodeMetric.NodeName)
	}

	// 3. 节点禁止调度
	err = retry.RetryOnConflict(retry.DefaultRetry, func() error {
		var newNode = &corev1.Node{}
		if err = k8sCluster.Direct().Get(ctx, client.ObjectKey{Name: nodeMetric.NodeName}, newNode); err != nil {
			return err
		}
		newNode.Spec.Unschedulable = true
		return k8sCluster.Direct().Update(ctx, newNode, &client.UpdateOptions{})
	})
	if err != nil {
		return nil, bcode.ErrorServerInternalError("cordon node failed, err:%s", err.Error())
	}

	queueName := node.Labels[constant.QueueNameLabelKey]

	var queueType queuev1.WorkloadType
	if queueName != "" {
		queueDetail, err := n.QueueService.GetQueueDetail(ctx, &queuev1.GetQueueDetailRequest{
			Name:          queueName,
			WorkspaceName: request.WorkspaceName,
		})
		if err != nil {
			return nil, bcode.ErrorServerInternalError("get queue detail failed, err:%s", err.Error())
		}
		queueType = queueDetail.WorkloadType
	}

	// 获取节点的gpu数量，后续更新 nodeMetrics
	gpuCount, err := strconv.Atoi(node.Labels[constant.GPUNodeLabelKey])
	if err != nil {
		return nil, bcode.ErrorInvalidArgument("node:%s has invalid gpu label", nodeMetric.NodeName)
	}

	// 4. 修改节点的gpu分配模式（通过标签）
	if request.GpuAllocationMode == v1.GpuAllocationMode_Exclusive {
		err = retry.RetryOnConflict(retry.DefaultRetry, func() error {
			var newNode = &corev1.Node{}
			if err = k8sCluster.Direct().Get(ctx, client.ObjectKey{Name: nodeMetric.NodeName}, newNode); err != nil {
				return err
			}
			delete(newNode.Labels, constant.NodeGPUSharedMemoryLabelKey)
			return k8sCluster.Direct().Update(ctx, newNode, &client.UpdateOptions{})

		})
		if err != nil {
			return nil, bcode.ErrorServerInternalError("recycle node label failed, err:%s", err.Error())
		}

		if queueName != "" && (queueType == queuev1.WorkloadType_Training || queueType == queuev1.WorkloadType_DevMachine) {
			// 如果是训练的和开发机的队列，更新 vcQueue 容量
			vcQueueName := getVolcanoQueueName(request.WorkspaceName, queueName)
			err := retry.RetryOnConflict(retry.DefaultRetry, func() error {
				volcanoQueue := vcschedulingv1beta1.Queue{}
				if err := k8sCluster.Direct().Get(ctx, client.ObjectKey{Name: vcQueueName}, &volcanoQueue); err != nil {
					return fmt.Errorf("get volcano queue failed: %w", err)
				}

				// 获取节点资源
				nodeResources := corev1.ResourceList{
					corev1.ResourceCPU:         nodeMetric.ResourceMetrics[corev1.ResourceCPU].Allocatable,
					corev1.ResourceMemory:      nodeMetric.ResourceMetrics[corev1.ResourceMemory].Allocatable,
					constant.ResourceNvidiaGPU: *resource.NewQuantity(int64(gpuCount), resource.DecimalSI),
				}

				// 创建或更新时的处理函数
				_, err := controllerutil.CreateOrUpdate(ctx, k8sCluster.Direct(), &volcanoQueue, func() error {
					// 初始化容量（如果不存在）
					if volcanoQueue.Spec.Capability == nil {
						volcanoQueue.Spec.Capability = make(corev1.ResourceList)
					}

					// 对每个资源类型进行累加
					for resName, resQuantity := range nodeResources {
						if existing, ok := volcanoQueue.Spec.Capability[resName]; ok {
							// 执行深拷贝后相加，避免修改原始数据
							newQuantity := existing.DeepCopy()
							newQuantity.Add(resQuantity)
							volcanoQueue.Spec.Capability[resName] = newQuantity
						} else {
							// 直接设置新值
							volcanoQueue.Spec.Capability[resName] = resQuantity.DeepCopy()
						}
					}
					return nil
				})
				return err
			})

			if err != nil {
				return nil, bcode.ErrorServerInternalError("update queue failed: %v", err)
			}
		}
	} else if request.GpuAllocationMode == v1.GpuAllocationMode_Shared {
		err = retry.RetryOnConflict(retry.DefaultRetry, func() error {
			var newNode = &corev1.Node{}
			if err = k8sCluster.Direct().Get(ctx, client.ObjectKey{Name: nodeMetric.NodeName}, newNode); err != nil {
				return err
			}
			newNode.Labels[constant.NodeGPUSharedMemoryLabelKey] = "enabled"
			return k8sCluster.Direct().Update(ctx, newNode, &client.UpdateOptions{})
		})

		if err != nil {
			return nil, bcode.ErrorServerInternalError("label node failed, err:%s", err.Error())
		}

		if queueName != "" && (queueType == queuev1.WorkloadType_Training || queueType == queuev1.WorkloadType_DevMachine) {
			// 如果是训练的和开发机的队列，更新 vcQueue 容量
			vcQueueName := getVolcanoQueueName(request.WorkspaceName, queueName)
			err := retry.RetryOnConflict(retry.DefaultRetry, func() error {
				volcanoQueue := vcschedulingv1beta1.Queue{}
				if err := k8sCluster.Direct().Get(ctx, client.ObjectKey{Name: vcQueueName}, &volcanoQueue); err != nil {
					return fmt.Errorf("get volcano queue failed: %w", err)
				}

				// 获取节点资源
				nodeResources := corev1.ResourceList{
					corev1.ResourceCPU:         nodeMetric.ResourceMetrics[corev1.ResourceCPU].Allocatable,
					corev1.ResourceMemory:      nodeMetric.ResourceMetrics[corev1.ResourceMemory].Allocatable,
					constant.ResourceNvidiaGPU: *resource.NewQuantity(int64(gpuCount), resource.DecimalSI),
				}

				// 创建或更新时的处理函数
				_, err := controllerutil.CreateOrUpdate(ctx, k8sCluster.Direct(), &volcanoQueue, func() error {
					// 初始化容量（如果不存在）
					if volcanoQueue.Spec.Capability == nil {
						volcanoQueue.Spec.Capability = make(corev1.ResourceList)
					}

					// 对每个资源类型进行累减
					for resName, resQuantity := range nodeResources {
						if existing, ok := volcanoQueue.Spec.Capability[resName]; ok {
							// 执行深拷贝后相减，避免修改原始数据
							newQuantity := existing.DeepCopy()
							newQuantity.Sub(resQuantity)
							volcanoQueue.Spec.Capability[resName] = newQuantity
						} else {
							// 直接设置新值
							volcanoQueue.Spec.Capability[resName] = resQuantity.DeepCopy()
						}
					}
					return nil
				})
				return err
			})

			if err != nil {
				return nil, bcode.ErrorServerInternalError("update queue failed: %v", err)
			}
		}

	} else {
		return nil, bcode.ErrorInvalidArgument("gpu allocation mode:%s is invalid", request.GpuAllocationMode.String())
	}

	// 5. 恢复节点调度
	err = retry.RetryOnConflict(retry.DefaultRetry, func() error {
		var newNode = &corev1.Node{}
		if err = k8sCluster.Direct().Get(ctx, client.ObjectKey{Name: nodeMetric.NodeName}, newNode); err != nil {
			return err
		}
		newNode.Spec.Unschedulable = false
		return k8sCluster.Direct().Update(ctx, newNode, &client.UpdateOptions{})
	})
	if err != nil {
		return nil, bcode.ErrorServerInternalError("uncordon node failed, err:%s", err.Error())
	}
	return nil, nil
}

func evictAllPods(ctx context.Context, c client.Client, nodeName string) error {
	var pods corev1.PodList
	if err := c.List(ctx, &pods,
		client.MatchingFields{"spec.nodeName": nodeName},
		client.MatchingLabels{constant.KICManagedLabelKey: "kic"},
	); err != nil {
		return err
	}

	for _, pod := range pods.Items {
		if err := c.SubResource("eviction").Create(ctx, &pod, &policyv1.Eviction{
			ObjectMeta: metav1.ObjectMeta{Name: pod.Name, Namespace: pod.Namespace},
		}); err != nil && !apierrors.IsNotFound(err) {
			return err
		}
	}
	return nil
}
