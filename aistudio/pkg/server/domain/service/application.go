package service

import (
	"context"
	"errors"
	"fmt"
	"sort"

	"strings"
	"sync"
	"time"

	"git.lianjia.com/cloudnative/kic/kic-platform/aistudio/pkg/constant"
	"git.lianjia.com/cloudnative/kic/kic-platform/aistudio/pkg/pool/workpool"
	"git.lianjia.com/cloudnative/kic/kic-platform/aistudio/pkg/server/domain/model"
	"git.lianjia.com/cloudnative/kic/kic-platform/aistudio/pkg/server/domain/repository"
	"git.lianjia.com/cloudnative/kic/kic-platform/aistudio/pkg/server/domain/service/application/component"
	apphelper "git.lianjia.com/cloudnative/kic/kic-platform/aistudio/pkg/server/domain/service/application/helper"
	"git.lianjia.com/cloudnative/kic/kic-platform/aistudio/pkg/server/event"
	applicationv1 "git.lianjia.com/cloudnative/kic/kic-platform/apis/aistudio/application/v1"
	"git.lianjia.com/cloudnative/kic/kic-platform/apis/aistudio/bcode"
	gatewayv1 "git.lianjia.com/cloudnative/kic/kic-platform/apis/aistudio/gateway/v1"
	workspacev1 "git.lianjia.com/cloudnative/kic/kic-platform/apis/aistudio/workspace/v1"
	apiscommon "git.lianjia.com/cloudnative/kic/kic-platform/apis/common"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/apis/scheduling.kcs.io/v1alpha1"
	schedulingv1alpha1 "git.lianjia.com/cloudnative/kic/kic-platform/pkg/apis/scheduling.kcs.io/v1alpha1"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/infrastructure/datastore"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/multicluster"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/property"
	vela "git.lianjia.com/cloudnative/kic/kic-platform/pkg/third_party/velaux"
	velav1 "git.lianjia.com/cloudnative/kic/kic-platform/pkg/third_party/velaux/apis/v1"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/util"
	kratoslog "github.com/go-kratos/kratos/v2/log"
	velacommon "github.com/oam-dev/kubevela/apis/core.oam.dev/common"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"google.golang.org/protobuf/types/known/emptypb"
	appsv1 "k8s.io/api/apps/v1"
	autoscalingv1 "k8s.io/api/autoscaling/v1"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/wait"
	"k8s.io/klog/v2"

	"sigs.k8s.io/controller-runtime/pkg/client"
)

var _ Interface = (*ApplicationService)(nil)

var _ applicationv1.ApplicationServiceHTTPServer = (*ApplicationService)(nil)

type ApplicationService struct {
	Store                datastore.DataStore `inject:"datastore"`
	lock                 *sync.Mutex
	WorkspaceService     *WorkspaceService            `inject:"workspaceService"`
	Logger               *kratoslog.Helper            `inject:"logger"`
	QueueService         *QueueService                `inject:"queueService"`
	PlatformService      *PlatformService             `inject:"platformService"`
	RbacService          *RbacService                 `inject:"rbacService"`
	ClusterManager       *ClusterManager              `inject:"clusterManager"`
	EventBridge          *event.Bridge                `inject:""`
	Properties           property.EnvironmentProperty `inject:""`
	ObservabilityService *ObservabilityService        `inject:"observabilityService"`
	DestinationService   *DestinationService          `inject:"destinationService"`
	GatewayService       *GatewayService              `inject:"gatewayService"`
	ModelHubService      *ModelHubService             `inject:"modelHubService"`
}

func (a *ApplicationService) GetDeploymentGroupVolumes(ctx context.Context, request *applicationv1.GetDeploymentGroupVolumesRequest) (*applicationv1.ListVolumes, error) {
	deploymentEntity, err := repository.GetDeploymentGroupById(ctx, a.Store, request.DeploymentGroupId)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("get deployment group failed: %v", err)
	}
	if deploymentEntity == nil || deploymentEntity.ComponentSpec == nil {
		return nil, nil
	}
	var volumes []*applicationv1.Volume
	switch deploymentEntity.ComponentSpec.ComponentType {
	case applicationv1.ComponentType_WebService.String():
		volumeSpecs := deploymentEntity.ComponentSpec.WebServiceTemplate.VolumeSpecs
		configSpecs := deploymentEntity.ComponentSpec.WebServiceTemplate.ConfigSpecs
		volumes = apphelper.VolumeModelToApplicationVolumes(volumeSpecs, configSpecs)
	case applicationv1.ComponentType_Inference.String():
		volumeSpecs := deploymentEntity.ComponentSpec.InferenceTemplate.VolumeSpecs
		configSpecs := deploymentEntity.ComponentSpec.InferenceTemplate.ConfigSpecs
		volumes = apphelper.VolumeModelToApplicationVolumes(volumeSpecs, configSpecs)
	case applicationv1.ComponentType_SglangDistributedInference.String():
		volumeSpecs := deploymentEntity.ComponentSpec.DistributedInferenceTemplate.VolumeSpecs
		configSpecs := deploymentEntity.ComponentSpec.DistributedInferenceTemplate.ConfigSpecs
		volumes = apphelper.VolumeModelToApplicationVolumes(volumeSpecs, configSpecs)
	case applicationv1.ComponentType_LeaderWorkerSet.String():
		volumeSpecs := deploymentEntity.ComponentSpec.LeaderWorkerSetTemplate.VolumeSpecs
		configSpecs := deploymentEntity.ComponentSpec.LeaderWorkerSetTemplate.ConfigSpecs
		volumes = apphelper.VolumeModelToApplicationVolumes(volumeSpecs, configSpecs)
	}
	return &applicationv1.ListVolumes{Volumes: volumes}, nil
}

func (a *ApplicationService) ListServiceExports(ctx context.Context, request *applicationv1.ListServiceExportRequest) (*applicationv1.ListServiceExportResponse, error) {
	dps, err := repository.GetAllDeploymentGroupsByApplicationName(ctx, a.Store, request.AppName)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("get deployment groups failed: %v", err)
	}
	var result []*applicationv1.QueryServiceExportResult
	for _, dp := range dps {
		queryServiceExportResult := &applicationv1.QueryServiceExportResult{
			AppName:             request.AppName,
			DeploymentGroupName: dp.Name,
			DeploymentGroupId:   dp.DeploymentGroupID,
			WorkspaceName:       dp.WorkspaceName,
		}
		if dp.ComponentSpec != nil && dp.ComponentSpec.ServiceExport != nil {
			queryServiceExportResult.ServiceName = dp.ComponentSpec.ServiceExport.ServiceName
			queryServiceExportResult.Enabled = dp.ComponentSpec.ServiceExport.Enabled
		}
		result = append(result, queryServiceExportResult)
	}
	return &applicationv1.ListServiceExportResponse{ServiceExports: result}, nil
}

func NewApplicationService() *ApplicationService {
	return &ApplicationService{
		lock: &sync.Mutex{},
	}
}

func (a *ApplicationService) Init(ctx context.Context, properties property.EnvironmentProperty) error {
	return nil
}

func (a *ApplicationService) CreateApplication(ctx context.Context, request *applicationv1.CreateApplicationRequest) (*emptypb.Empty, error) {
	klog.Infof("CreateApplication request: %v", request)
	a.lock.Lock()
	defer a.lock.Unlock()

	user, ok := ctx.Value(constant.UserCtxKey).(string)
	if !ok {
		return nil, bcode.ErrorUnauthorized("please login first")
	}

	if request.WorkspaceName == "" {
		return nil, bcode.ErrorInvalidArgument("workspace name is required")
	}
	// check workspace exist
	_, err := a.WorkspaceService.GetWorkspaceBase(ctx, &workspacev1.GetWorkspaceBaseRequest{
		Name: request.WorkspaceName,
	})
	if err != nil {
		return nil, bcode.ErrorServerInternalError("get workspace base [%s] failed", request.WorkspaceName)
	}

	applicationEntity := &model.ApplicationEntity{
		ApplicationName: request.AppName,
	}
	// 检查application是否存在
	exist, err := repository.ApplicationExists(ctx, a.Store, request.WorkspaceName, request.AppName)
	if err != nil {
		klog.Errorf("check application name is exist failure %s", err.Error())
		return nil, bcode.ErrorServerInternalError("check application name is exist failure,err:%s", err.Error())
	}
	if exist {
		return nil, bcode.ErrorApplicationExist("application name is exist")
	}

	owner := user
	if !util.StringInSlice(owner, request.Managers) {
		request.Managers = append(request.Managers, owner)
	}
	//for _, manager := range request.Managers {
	//	if !util.StringInSlice(manager, request.Members) {
	//		request.Members = append(request.Members, manager)
	//	}
	//}
	applicationEntity.ID = primitive.NewObjectID()
	applicationEntity.WorkspaceName = request.WorkspaceName
	applicationEntity.ApplicationName = request.AppName
	applicationEntity.DisplayName = request.DisplayName
	applicationEntity.Description = request.Description
	applicationEntity.VelaProjectName = getNamespaceName(request.WorkspaceName)
	applicationEntity.Labels = request.Labels
	applicationEntity.Managers = request.Managers
	applicationEntity.Members = request.Members
	applicationEntity.Creator = user
	applicationEntity.AppType = request.AppType.String()
	applicationEntity.IsDeleted = false
	if request.ServiceConfig != nil {
		applicationEntity.ServiceConfig = &model.ServiceConfigEntity{
			Enabled:  request.ServiceConfig.Enabled,
			Ports:    convertPortsProtoToEntity(request.ServiceConfig.Ports),
			Gateways: request.ServiceConfig.Gateways,
		}
	}

	if err = a.Store.Add(ctx, applicationEntity); err != nil {
		return nil, bcode.ErrorServerInternalError("add application failed: %v", err)
	}
	// 创建应用的时候 默认创建一个服务
	err = a.syncService(ctx, applicationEntity)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("sync service failed: %v", err)
	}
	return &emptypb.Empty{}, nil
}

// 使用默认网关
func (a *ApplicationService) syncService(ctx context.Context, applicationEntity *model.ApplicationEntity) error {
	if applicationEntity.ServiceConfig != nil && applicationEntity.ServiceConfig.Enabled {
		createServiceRequest := &gatewayv1.CreateOrUpdateDestinationServiceRequest{
			Name:            applicationEntity.ApplicationName,
			Description:     applicationEntity.Description,
			DisplayName:     "应用" + applicationEntity.ApplicationName + "默认服务",
			WorkspaceName:   applicationEntity.WorkspaceName,
			Managers:        applicationEntity.Managers,
			Members:         applicationEntity.Members,
			Ports:           applicationEntity.ServiceConfig.PortsToProto(),
			ApplicationName: applicationEntity.ApplicationName,
			IsDefault:       true,
		}
		_, err := a.DestinationService.CreateOrUpdateDestinationService(ctx, createServiceRequest)
		if err != nil {
			return err
		}
		// 创建默认的服务
		for _, gatewayName := range applicationEntity.ServiceConfig.Gateways {
			klog.Infof("create serviceBinding for application %s, gateway %s", applicationEntity.ApplicationName, gatewayName)
			gatewayEntity, err := repository.GetGateway(ctx, a.Store, applicationEntity.WorkspaceName, gatewayName)
			if err != nil {
				return bcode.ErrorServerInternalError("get default gateway failed: %v", err)
			}
			if gatewayEntity == nil {
				return bcode.ErrorServerInternalError("Workspace[%s] default gateway not found", applicationEntity.WorkspaceName)
			}
			_, err = a.GatewayService.CreateServiceBindingIfNotExists(ctx, &gatewayv1.CreateServiceBindingRequest{
				WorkspaceName:   applicationEntity.WorkspaceName,
				Gateway:         gatewayEntity.Name,
				DestinationName: applicationEntity.ApplicationName,
				Description:     fmt.Sprintf("应用%s默认网关[%s]的绑定服务", applicationEntity.ApplicationName, gatewayEntity.Name),
				TopologySpreadConstraints: &gatewayv1.TopologySpreadConstraints{
					Enabled: false,
				},
			})
			if err != nil {
				klog.Errorf("create serviceBinding for application %s, gateway %s failed: %v", applicationEntity.ApplicationName, gatewayName, err)
				return bcode.ErrorServerInternalError("create serviceBinding for application %s, gateway %s failed: %v", applicationEntity.ApplicationName, gatewayName, err)
			}
		}
	}
	return nil
}

func (a *ApplicationService) ListApplications(ctx context.Context, options *applicationv1.ListApplicationOptions) (*applicationv1.ListApplicationResult, error) {
	var filterOptions datastore.FilterOptions

	if options.AppType != "" {
		appTypes := strings.Split(options.AppType, ",")
		filterOptions.In = append(filterOptions.In, datastore.InQueryOption{
			Key:    "appType",
			Values: appTypes,
		})
	}
	if options.WorkspaceName != "" {
		filterOptions.In = append(filterOptions.In, datastore.InQueryOption{
			Key:    "workspaceName",
			Values: []string{options.WorkspaceName},
		})
	}
	if options.AppName != "" {
		filterOptions.Queries = append(filterOptions.Queries, datastore.FuzzyQueryOption{
			Key:   "applicationName",
			Query: options.AppName,
		})
	}
	if options.Creator != "" {
		filterOptions.In = append(filterOptions.In, datastore.InQueryOption{
			Key:    "creator",
			Values: []string{options.Creator},
		})
	}

	if options.DisplayName != "" {
		filterOptions.Queries = append(filterOptions.Queries, datastore.FuzzyQueryOption{
			Key:   "displayName",
			Query: options.DisplayName,
		})
	}
	if len(options.Labels) > 0 {
		var labelOrFilter []*datastore.FilterOptions
		for _, label := range options.Labels {
			if strings.Contains(label, ":") {
				parts := strings.SplitN(label, ":", 2)
				if len(parts) == 2 {
					labelOrFilter = append(labelOrFilter, &datastore.FilterOptions{
						LabelsQuery: []datastore.LabelQueryOption{
							{
								QueryKey: "labels",
								Key:      strings.TrimSpace(parts[0]),
								Value:    strings.TrimSpace(parts[1]),
							},
						},
					})
				}
			}
		}
		if len(labelOrFilter) > 0 {
			filterOptions.Or = append(filterOptions.Or, &datastore.FilterOptions{
				Or: labelOrFilter,
			})
		}
	}

	if options.Member != "" && options.Manager == "" {
		filterOptions.In = append(filterOptions.In, datastore.InQueryOption{
			Key:    "members",
			Values: []string{options.Member},
		})
	} else if options.Manager != "" && options.Member == "" {
		filterOptions.In = append(filterOptions.In, datastore.InQueryOption{
			Key:    "managers",
			Values: []string{options.Manager},
		})
	} else if options.Manager != "" && options.Member != "" {
		memberFilter := &datastore.FilterOptions{
			In: []datastore.InQueryOption{
				{Key: "members", Values: []string{options.Member}},
			},
		}
		managerFilter := &datastore.FilterOptions{
			In: []datastore.InQueryOption{
				{Key: "managers", Values: []string{options.Manager}},
			},
		}
		// 将两个条件放入 OR 分支
		orFilter := &datastore.FilterOptions{
			Or: []*datastore.FilterOptions{memberFilter, managerFilter},
		}
		filterOptions.Or = append(filterOptions.Or, orFilter)
	}

	filterOptions.Booleans = append(filterOptions.Booleans, datastore.BooleansQueryOption{
		Key:   "isDeleted",
		Value: false,
	})

	var applicationModel = model.ApplicationEntity{}
	var appEntities []datastore.Entity
	var totalCount int64
	var err error
	appEntities, err = a.Store.List(ctx, &applicationModel, &datastore.ListOptions{
		FilterOptions: filterOptions,
		SortBy: []datastore.SortOption{
			{
				Key:   "createTime",
				Order: datastore.SortOrderDescending,
			},
		},
		Page:     int(options.Page),
		PageSize: int(options.PageSize),
	})
	if err != nil {
		return nil, bcode.ErrorServerInternalError("list applications failed: %v", err)
	}
	totalCount, err = a.Store.Count(ctx, &model.ApplicationEntity{}, &filterOptions)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("count apps failed: %v", err)
	}
	var result []*applicationv1.ApplicationBase
	for _, entity := range appEntities {
		applicationEntity := entity.(*model.ApplicationEntity)
		app := apphelper.ConvertToApplicationBase(applicationEntity)
		app.DeploymentGroupCount = repository.GetDeploymentGroupCountByApplicationName(ctx, a.Store, applicationEntity.ApplicationName)
		result = append(result, app)
	}
	return &applicationv1.ListApplicationResult{
		Total: totalCount,
		Apps:  result,
	}, nil
}

func (a *ApplicationService) GetApplicationBase(ctx context.Context, request *applicationv1.GetApplicationRequest) (*applicationv1.ApplicationBase, error) {
	app, err := repository.GetApplicationByName(ctx, a.Store, request.WorkspaceName, request.AppName)
	if err != nil {
		if errors.Is(err, datastore.ErrRecordNotExist) {
			return nil, bcode.ErrorApplicationNotFound("application[%s/%s] not found", request.WorkspaceName, request.AppName)
		}
	}
	return apphelper.ConvertToApplicationBase(app), nil
}

func (a *ApplicationService) DeleteApplication(ctx context.Context, request *applicationv1.DeleteApplicationRequest) (*emptypb.Empty, error) {
	// 只有管理员可以删除应用
	account, ok := ctx.Value(constant.UserCtxKey).(string)
	if !ok {
		return nil, bcode.ErrorServerInternalError("get user from context failed")
	}

	isPlatformAdmin, err := a.RbacService.IsPlatformAdmin(ctx, account)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("check platform admin error: %v", err)
	}
	isWorkspaceManager, err := a.WorkspaceService.IsWorkspaceManager(ctx, &workspacev1.IsWorkspaceManagerRequest{
		Account:       account,
		WorkspaceName: request.WorkspaceName,
	})

	appEntity, err := repository.GetApplicationByName(ctx, a.Store, request.WorkspaceName, request.AppName)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("get application failed, err: %v", err)
	}
	owners := appEntity.Managers
	if !isPlatformAdmin && !isWorkspaceManager.IsManager && !util.Contains(owners, account) {
		return nil, bcode.ErrorUnauthorized("only owner can delete application ", account)
	}

	// 有部署组的应用不能删除
	deploys, err := repository.GetAllDeploymentGroupsByApplicationName(ctx, a.Store, request.AppName)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("get deployment groups failed: %v", err)
	}
	if len(deploys) > 0 {
		return nil, bcode.ErrorServerInternalError("application[%s] has deployment groups, can not delete", request.AppName)
	}
	// delete application
	appEntity.IsDeleted = true
	appEntity.Message = fmt.Sprintf("deleted by %s at %s", account, time.Now().Format(time.DateTime))
	if err = a.Store.Put(ctx, appEntity); err != nil {
		return nil, bcode.ErrorServerInternalError("delete application failed, err: %v", err)
	}
	// TODO 联级资源清理

	// TODO event
	return &emptypb.Empty{}, nil
}

func (a *ApplicationService) UpdateApplication(ctx context.Context, request *applicationv1.UpdateApplicationRequest) (*emptypb.Empty, error) {
	exists, err := repository.ApplicationExists(ctx, a.Store, request.WorkspaceName, request.AppName)
	if err != nil {
		return nil, bcode.ErrorUnauthorized("check application exists failed, err: %v", err)
	}
	if !exists {
		return nil, bcode.ErrorApplicationNotFound("application[%s] not found", request.AppName)
	}
	appEntity, err := repository.GetApplicationByName(ctx, a.Store, request.WorkspaceName, request.AppName)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("get application failed, err: %v", err)
	}
	if request.Labels != nil {
		appEntity.Labels = request.Labels
	}
	if request.Description != "" {
		appEntity.Description = request.Description
	}
	if request.DisplayName != "" {
		appEntity.DisplayName = request.DisplayName
	}
	appEntity.Members = request.Members
	if len(request.Managers) > 0 {
		appEntity.Managers = request.Managers
	}
	if request.ServiceConfig != nil {
		appEntity.ServiceConfig = &model.ServiceConfigEntity{
			Enabled:  request.ServiceConfig.Enabled,
			Gateways: request.ServiceConfig.Gateways,
			Ports:    convertPortsProtoToEntity(request.ServiceConfig.Ports),
		}
	}
	err = a.Store.Put(ctx, appEntity)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("update application failed, err: %v", err)
	}
	err = a.syncService(ctx, appEntity)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("sync service failed: %v", err)
	}
	return &emptypb.Empty{}, nil
}

func (a *ApplicationService) CreateDeploymentGroup(ctx context.Context, request *applicationv1.CreateDeploymentGroupRequest) (*applicationv1.DeploymentGroup, error) {
	// Check application
	if request.AppName == "" {
		return nil, bcode.ErrorInvalidArgument("appName is required")
	}
	if request.WorkspaceName == "" {
		return nil, bcode.ErrorInvalidArgument("workspace name is required")
	}
	user, ok := ctx.Value(constant.UserCtxKey).(string)
	if !ok {
		return nil, bcode.ErrorServerInternalError("get user from context failed")
	}

	// Get application
	application, err := repository.GetApplicationByName(ctx, a.Store, request.WorkspaceName, request.AppName)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("get application [%s] failed", request.AppName)
	}
	// Check deploymentGroup
	ok, err = repository.DeploymentGroupExistsByApplicationNameAndDeploymentGroupName(ctx, a.Store, request.AppName, request.Name)
	if err != nil {
		return nil, bcode.ErrorDeploymentGroupExistsAlready("check deploymentGroup exists failed: %v", err)
	}
	if ok {
		return nil, bcode.ErrorDeploymentGroupExistsAlready("deploymentGroup:[%s] is exist", request.Name)
	}

	if request.Component == nil {
		return nil, bcode.ErrorInvalidArgument("component is required")
	}

	// Get workspace cr
	workspaceCR, err := GetWorkspaceCR(ctx, request.WorkspaceName)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("get workspace cr [%s] failed", request.WorkspaceName)
	}
	if workspaceCR.Status.State != v1alpha1.WorkspaceStateSynced {
		return nil, bcode.ErrorWorkspaceNotReady("workspace[%s] is not ready", workspaceCR.Name)
	}
	// Get cluster bindings
	result, err := a.WorkspaceService.ListClusterBindings(ctx, &workspacev1.ListClusterBindingRequest{
		WorkspaceName: workspaceCR.Name,
	})
	if err != nil {
		return nil, bcode.ErrorServerInternalError("list cluster bindings failed: %v", err)
	}
	if len(result.ClusterBindings) == 0 {
		return nil, bcode.ErrorServerInternalError("workspace[%s] no cluster found", workspaceCR.Name)
	}
	ctx = context.WithValue(ctx, constant.WorkspaceCRCtxKey, workspaceCR)

	// 交付目标为可分发的集群, env和 target 初始化
	// 1. 先创建 vela Application
	// 2. 创建 components,components 会创建对应 traits（运维特性）
	// 3. 创建 policies （配置覆盖等策略）
	// 4. 创建 workflow
	deploymentGroupID := util.GenerateRandomStringWithPrefix("s", 6)
	labels := make(map[string]string)
	labels = a.setupDeploymentGroupLabels(request, user, deploymentGroupID, workspaceCR.Name)
	annotations := map[string]string{
		constant.DeploymentGroupDisplayNameAnnotationKey: request.Name,
	}
	//新建应用,会绑定到默认的一个环境中, 在vela侧会同步的创建EnvBinding和工作流配置,默认工作流比较简单,只有一个deploy阶段
	//创建完成后，需要重新生成部署策略以及工作流配置
	labels = util.MergeMaps(application.Labels, labels)
	velaApp := &velav1.CreateApplicationRequest{
		Name:        deploymentGroupID,
		Description: request.Description,
		Alias:       request.Name,
		Labels:      labels,
		Project:     workspaceCR.Status.VelaProjectName,
		EnvBinding:  []*velav1.EnvBinding{{Name: workspaceCR.Status.VelaDefaultEnvName}},
	}
	// If the queueName is not empty, add node affinity for the queueName
	// 设置空间和队列亲和性
	if request.Component.Traits == nil {
		request.Component.Traits = &applicationv1.ComponentTraits{}
	}
	if request.Component.Traits.Affinity == nil {
		request.Component.Traits.Affinity = &applicationv1.AffinityTrait{}
	}
	affinity := request.Component.Traits.Affinity
	if affinity.NodeAffinity == nil {
		affinity.NodeAffinity = &applicationv1.NodeAffinity{}
	}
	if affinity.NodeAffinity.Required == nil {
		affinity.NodeAffinity.Required = []*applicationv1.NodeSelectorRequirement{}
	}
	affinity.NodeAffinity.Required = append(affinity.NodeAffinity.Required, &applicationv1.NodeSelectorRequirement{
		Key:      constant.WorkspaceNameLabelKey,
		Operator: "In",
		Values:   []string{request.WorkspaceName},
	})
	if request.QueueName != "" {
		affinity.NodeAffinity.Required = append(affinity.NodeAffinity.Required, &applicationv1.NodeSelectorRequirement{
			Key:      constant.QueueNameLabelKey,
			Operator: "In",
			Values:   []string{request.QueueName},
		})
	}
	request.Component.Traits.Affinity = affinity
	request.Component.Name = deploymentGroupID
	request.Component.Labels = util.MergeMaps(request.Component.Labels, labels)
	request.Component.Annotations = util.MergeMaps(request.Component.Annotations, annotations)

	var modelTemplate *applicationv1.ModelTemplate
	// If is distributed inference, get inference node metrics
	if request.Component.ComponentType == applicationv1.ComponentType_SglangDistributedInference {
		if err = a.getInferenceNodeMetrics(ctx, request.Component.DistributedInference); err != nil {
			return nil, bcode.ErrorServerInternalError("get master node metrics failed,err:%v", err)
		}
		if request.ReplicaScheduling != nil && len(request.ReplicaScheduling.ReplicasOverrides) > 0 {
			request.Component.DistributedInference.Replicas = request.ReplicaScheduling.ReplicasOverrides[0].Replicas
		}
		modelTemplate = request.Component.DistributedInference.ModelTemplate
	} else if request.Component.ComponentType == applicationv1.ComponentType_LeaderWorkerSet {
		if request.ReplicaScheduling != nil && len(request.ReplicaScheduling.ReplicasOverrides) > 0 {
			request.Component.LeaderWorkerSet.Replicas = request.ReplicaScheduling.ReplicasOverrides[0].Replicas
		}
		modelTemplate = request.Component.LeaderWorkerSet.ModelTemplate
	} else if request.Component.ComponentType == applicationv1.ComponentType_Inference {
		modelTemplate = request.Component.Inference.ModelTemplate
	}
	// 如果模型模板存在，并且模型模板启用，并且模型模板加载类型为Volume，则检查模型是否预热，如果未预热，则返回错误(挂载卷无模型)
	if modelTemplate != nil && modelTemplate.Enabled && modelTemplate.LoadType == model.LoadTypeVolume {
		err = a.checkModelWarmed(ctx, modelTemplate)
		if err != nil {
			return nil, err
		}
	}

	componentRequests, err := component.Convert(ctx, request.Component)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("convert component failed: %v", err)
	}
	// create vela app
	applicationBase, err := vela.Instance().CreateApplication(ctx, workspaceCR.Status.VelaProjectName, velaApp)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("create vela application failed: %v", err)
	}

	// create components
	for _, componentRequest := range componentRequests {
		componentRequest.Alias = request.AppName + "[" + request.Name + "]"
		_, err = vela.Instance().CreateComponent(ctx, velaApp.Name, componentRequest)
		if err != nil {
			return nil, bcode.ErrorServerInternalError("create vela component failed: %v", err)
		}
	}
	// 配置覆盖策略等
	clusterReplications, err := a.ApplyClusterReplications(ctx, request.ReplicaScheduling)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("apply cluster topology policy failed: %v", err)
	}
	for _, clusterReplication := range clusterReplications {
		componentName := request.Component.Name
		replicas := clusterReplication.Replicas
		if request.Component.ComponentType == applicationv1.ComponentType_SglangDistributedInference || request.Component.ComponentType == applicationv1.ComponentType_LeaderWorkerSet {
			// 只覆盖worker
			componentName = fmt.Sprintf("%s-worker", request.Component.Name)
			replicas = util.Max(1, replicas-1)
		}
		err = a.createOrUpdateOverridePolicy(ctx, applicationBase.Name, workspaceCR, componentName, replicas, clusterReplication)
		if err != nil {
			return nil, bcode.ErrorServerInternalError("create override policy failed: %v", err)
		}
		err = a.createOrUpdateTopologyPolicy(ctx, applicationBase.Name, workspaceCR, clusterReplication)
		if err != nil {
			return nil, bcode.ErrorServerInternalError("create topology policy failed: %v", err)
		}
	}
	// update workflow
	workflowResponse, err := a.updateDefaultWorkflow(ctx, applicationBase.Name, workspaceCR, clusterReplications)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("update default workflow failed: %v", err)
	}
	klog.Infof("create deploymentGroup success, application:[%s], workflow: %s", applicationBase.Name, util.ToJSONString(workflowResponse))
	deploymentGroupEntity := apphelper.ConvertToDeploymentGroupEntity(request, a.Properties)
	deploymentGroupEntity.DeploymentGroupID = deploymentGroupID
	deploymentGroupEntity.VelaApplicationName = deploymentGroupID
	deploymentGroupEntity.VelaProjectName = workspaceCR.Status.VelaProjectName
	deploymentGroupEntity.VelaEnvName = workspaceCR.Status.VelaDefaultEnvName
	deploymentGroupEntity.VelaWorkflowName = workflowResponse.Name
	deploymentGroupEntity.Creator = user
	deploymentGroupEntity.Labels = labels
	err = a.Store.Add(ctx, deploymentGroupEntity)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("create deploymentGroup failed: %v", err)
	}
	// TODO  vela Application回收
	return &applicationv1.DeploymentGroup{
		AppName:             request.AppName,
		WorkspaceName:       request.WorkspaceName,
		Name:                request.Name,
		QueueName:           request.QueueName,
		Component:           request.Component,
		EnvType:             request.EnvType,
		PropagationStrategy: request.PropagationStrategy,
		ReplicaScheduling:   request.ReplicaScheduling,
		Id:                  deploymentGroupEntity.DeploymentGroupID,
	}, nil
}

// checkModelWarmed 检查模型是否预热
func (a *ApplicationService) checkModelWarmed(ctx context.Context, modelTemplate *applicationv1.ModelTemplate) error {
	// 兼容前端
	modelName := modelTemplate.ModelName
	modelWorkspace := modelTemplate.ModelWorkspace
	if strings.Contains(modelName, "/") {
		modelSplits := strings.SplitN(modelName, "/", 2)
		if len(modelSplits) == 2 {
			modelWorkspace = modelSplits[0]
			modelName = modelSplits[1]
		}
	}
	warmed, err := a.ModelHubService.IsModelWarmed(ctx, modelWorkspace, modelName)
	if err != nil {
		klog.Errorf("check model warmed failed, err: %v", err)
		return bcode.ErrorServerInternalError("get model failed: %v", err)
	}
	if !warmed {
		klog.Errorf("model %s not warmed", modelName)
		return bcode.ErrorServerInternalError("模型[%s]未预热，请前往模型仓库预热模型", modelName)
	}
	return nil
}

func (a *ApplicationService) setupDeploymentGroupLabels(request *applicationv1.CreateDeploymentGroupRequest, user, deploymentGroupID, workspaceName string) map[string]string {
	labels := make(map[string]string)
	if request.Labels != nil {
		labels = request.Labels
	}
	labels[constant.ApplicationNameLabelKey] = request.AppName
	labels[constant.ApplicationTypeLabelKey] = request.Component.ComponentType.String()
	labels[constant.WorkspaceNameLabelKey] = workspaceName
	labels[constant.RegionLabelKey] = request.Region
	labels[constant.QueueNameLabelKey] = request.QueueName
	labels[constant.CreateByLabelKey] = user
	labels[constant.EnvironmentTypeLabelKey] = request.EnvType.String()
	labels[constant.ComponentTypeLabelKey] = request.Component.ComponentType.String()
	labels[constant.DeploymentGroupNameLabelKey] = deploymentGroupID
	labels[constant.WorkloadNameLabelKey] = deploymentGroupID
	labels[constant.WorkloadTypeLabelKey] = constant.WorkloadTypeDeploymentGroup
	labels[constant.KICManagedLabelKey] = constant.KICManagedLabelValue
	if request.Component.ComponentType == applicationv1.ComponentType_WebService {
		if request.Component.WebService != nil && request.Component.WebService.GpuShareEnabled {
			labels[constant.NodeGPUSharedMemoryLabelKey] = "enabled"
		}
	} else if request.Component.ComponentType == applicationv1.ComponentType_Inference {
		if request.Component.Inference != nil && request.Component.Inference.GpuShareEnabled {
			labels[constant.NodeGPUSharedMemoryLabelKey] = "enabled"
		}
	}
	return labels
}

// DeployDeploymentGroup 部署应用
func (a *ApplicationService) DeployDeploymentGroup(ctx context.Context, request *applicationv1.DeployRequest) (*applicationv1.DeployResponse, error) {

	deploymentGroupEntity, err := repository.GetDeploymentGroupById(ctx, a.Store, request.DeploymentGroupId)
	if err != nil {
		if errors.Is(err, datastore.ErrRecordNotExist) {
			return nil, bcode.ErrorDeploymentGroupNotFound("deployment group[%d] not found", request.DeploymentGroupId)
		}
		return nil, bcode.ErrorServerInternalError("get deployment group failed, err: %v", err)
	}
	workspaceName := deploymentGroupEntity.WorkspaceName
	workspaceCR, err := GetWorkspaceCR(ctx, workspaceName)
	if err != nil {
		return &applicationv1.DeployResponse{}, err
	}
	if workspaceCR.Status.State != v1alpha1.WorkspaceStateSynced {
		return &applicationv1.DeployResponse{}, bcode.ErrorWorkspaceNotReady("workspace[%s] is not ready for create DeploymentGroup", workspaceName)
	}
	klog.Infof("DeploymentGroup[%s/%s] deploying, operator: %s, request: %s", workspaceName, deploymentGroupEntity.Name, request.Operator, util.ToJSONString(request))
	// dry-run
	workflowName := apphelper.ConvertWorkflowName(workspaceCR.Name)
	velaApplicationName := deploymentGroupEntity.VelaApplicationName
	dryRun, err := vela.Instance().DryRunAppOrRevision(ctx, velaApplicationName, workflowName)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("DeploymentGroup dry-run failed, err: %v", err)
	}
	if !dryRun.Success {
		return nil, bcode.ErrorServerInternalError("DeploymentGroup dry-run failed, err: %v", dryRun.Message)
	}

	applyNote := map[string]string{
		"operator": request.Operator,
		"note":     request.Note,
	}
	deployResult, err := vela.Instance().DeployApplication(ctx, workspaceCR.Status.VelaProjectName, deploymentGroupEntity.VelaApplicationName, &velav1.ApplicationDeployRequest{
		WorkflowName: deploymentGroupEntity.VelaWorkflowName,
		Note:         util.ToJSONString(applyNote),
		TriggerType:  request.TriggerType, //触发方式
		Force:        request.Force,
	})
	if err != nil {
		return nil, bcode.ErrorServerInternalError("DeploymentGroup deploy failed, err: %v", err)
	}
	klog.Infof("DeploymentGroup deploy success, result: %s", util.ToJSONString(deployResult))
	return &applicationv1.DeployResponse{
		Revision:       apphelper.ConvertToWorkflowRevision(deployResult.ApplicationRevisionBase),
		WorkflowRecord: apphelper.ConvertToWorkflowRecord(deployResult.WorkflowRecord, request.Operator),
	}, nil
}

func (a *ApplicationService) ListDeploymentGroups(ctx context.Context, options *applicationv1.ListDeploymentGroupOptions) (*applicationv1.ListDeploymentGroupResult, error) {
	if options.AppName == "" {
		return nil, bcode.ErrorInvalidArgument("appName is required")
	}
	var filterOptions datastore.FilterOptions
	filterOptions.Booleans = append(filterOptions.Booleans, datastore.BooleansQueryOption{
		Key:   "isDeleted",
		Value: false,
	})
	if options.Name != "" {
		filterOptions.Queries = append(filterOptions.Queries, datastore.FuzzyQueryOption{
			Key:   "name",
			Query: options.Name,
		})
	}

	if options.EnvType != "" {
		filterOptions.In = append(filterOptions.In, datastore.InQueryOption{
			Key:    "envType",
			Values: []string{options.EnvType},
		})
	}
	if options.Creator != "" {
		filterOptions.In = append(filterOptions.In, datastore.InQueryOption{
			Key:    "creator",
			Values: []string{options.Creator},
		})
	}
	if options.QueueName != "" {
		filterOptions.In = append(filterOptions.In, datastore.InQueryOption{
			Key:    "queueName",
			Values: []string{options.QueueName},
		})
	}

	count, err := a.Store.Count(ctx, &model.DeploymentGroupEntity{
		WorkspaceName:   options.WorkspaceName,
		ApplicationName: options.AppName,
	}, &filterOptions)

	items, err := a.Store.List(ctx, &model.DeploymentGroupEntity{
		WorkspaceName:   options.WorkspaceName,
		ApplicationName: options.AppName,
	}, &datastore.ListOptions{
		FilterOptions: filterOptions,
		SortBy: []datastore.SortOption{
			{
				Key:   "createTime",
				Order: datastore.SortOrderDescending,
			},
		},
		Page:     int(options.Page),
		PageSize: int(options.PageSize),
	})
	if err != nil {
		return nil, err
	}
	var dps []*applicationv1.DeploymentGroup
	wp := workpool.New(4)
	for _, item := range items {
		currentItem := item
		wp.Do(func() error {
			dp := apphelper.ConvertToDeploymentGroupFromEntity(ctx, currentItem.(*model.DeploymentGroupEntity), true)
			a.lock.Lock()
			if options.DeploymentGroupPhase != "" {
				if dp.DeployGroupStatus != nil && dp.DeployGroupStatus.Phase.String() == options.DeploymentGroupPhase {
					dps = append(dps, dp)
				}
			} else {
				dps = append(dps, dp)
			}
			a.lock.Unlock()
			return nil
		})
	}
	wp.Wait()
	// TODO 数据很多时，排序会有性能问题
	sort.Slice(dps, func(i, j int) bool {
		timeI, errI := time.Parse(time.DateTime, dps[i].CreateTime)
		timeJ, errJ := time.Parse(time.DateTime, dps[j].CreateTime)
		if errI != nil || errJ != nil {
			return false
		}
		return timeI.After(timeJ)
	})
	return &applicationv1.ListDeploymentGroupResult{
		DeploymentGroups: dps,
		Total:            count,
	}, nil
}

// GetDeploymentGroupDetail 获取部署组详情,相当于获取 vela application
func (a *ApplicationService) GetDeploymentGroupDetail(ctx context.Context, request *applicationv1.GetDeploymentStatusRequest) (*applicationv1.DeploymentGroup, error) {
	deploymentGroupEntity, err := repository.GetDeploymentGroupById(ctx, a.Store, request.DeploymentGroupId)
	if err != nil {
		if errors.Is(err, datastore.ErrRecordNotExist) {
			return nil, bcode.ErrorDeploymentGroupNotFound("deployment group[%d] not found", request.DeploymentGroupId)
		}
		return nil, bcode.ErrorServerInternalError("get deployment group failed, err: %v", err)
	}
	return apphelper.ConvertToDeploymentGroupFromEntity(ctx, deploymentGroupEntity, true), nil
}

// UpdateDeploymentGroup 更新部署组
func (a *ApplicationService) UpdateDeploymentGroup(ctx context.Context, request *applicationv1.UpdateDeploymentGroupRequest) (*applicationv1.DeploymentGroup, error) {
	l, ok := ctx.Value(constant.UserCtxKey).(string)
	if !ok {
		return nil, bcode.ErrorServerInternalError("get user from context failed")
	}
	deploymentGroupEntity, err := repository.GetDeploymentGroupById(ctx, a.Store, request.DeploymentGroupId)
	if err != nil {
		if errors.Is(err, datastore.ErrRecordNotExist) {
			return nil, bcode.ErrorDeploymentGroupNotFound("deployment group[%d] not found", request.DeploymentGroupId)
		}
		return nil, bcode.ErrorServerInternalError("get deployment group failed, err: %v", err)
	}
	if err := a.checkReplicaScheduling(ctx, request.ReplicaScheduling); err != nil {
		return nil, bcode.ErrorServerInternalError("check replica scheduling failed, err: %v", err)
	}

	labels := make(map[string]string)
	if deploymentGroupEntity.Labels != nil {
		labels = deploymentGroupEntity.Labels
	}
	labels[constant.UpdateByLabelKey] = l

	updateVela := &velav1.UpdateApplicationRequest{
		Description: request.Description,
		Alias:       request.Name,
		Labels:      labels,
	}
	// 更新 application
	_, err = vela.Instance().UpdateApplication(ctx, deploymentGroupEntity.VelaProjectName, deploymentGroupEntity.VelaApplicationName, updateVela)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("update vela application failed, err: %v", err)
	}
	if request.Component.Traits == nil {
		request.Component.Traits = &applicationv1.ComponentTraits{}
	}
	if request.Component.Traits.Affinity == nil {
		request.Component.Traits.Affinity = &applicationv1.AffinityTrait{}
	}
	affinity := request.Component.Traits.Affinity
	if affinity.NodeAffinity == nil {
		affinity.NodeAffinity = &applicationv1.NodeAffinity{}
	}
	if affinity.NodeAffinity.Required == nil {
		affinity.NodeAffinity.Required = []*applicationv1.NodeSelectorRequirement{}
	}
	affinity.NodeAffinity.Required = append(affinity.NodeAffinity.Required, &applicationv1.NodeSelectorRequirement{
		Key:      constant.WorkspaceNameLabelKey,
		Operator: "In",
		Values:   []string{deploymentGroupEntity.WorkspaceName},
	})
	if request.QueueName != "" {
		affinity.NodeAffinity.Required = append(affinity.NodeAffinity.Required, &applicationv1.NodeSelectorRequirement{
			Key:      constant.QueueNameLabelKey,
			Operator: "In",
			Values:   []string{request.QueueName},
		})
	}
	request.Component.Traits.Affinity = affinity
	// get workspace cr
	workspaceCR, err := GetWorkspaceCR(ctx, deploymentGroupEntity.WorkspaceName)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("get workspace cr [%s] failed", deploymentGroupEntity.WorkspaceName)
	}
	ctx = context.WithValue(ctx, constant.WorkspaceCRCtxKey, workspaceCR)
	// 更新component
	componentLabels := deploymentGroupEntity.ComponentSpec.Labels
	if componentLabels == nil {
		componentLabels = make(map[string]string)
		componentLabels[constant.ApplicationNameLabelKey] = deploymentGroupEntity.ApplicationName
		componentLabels[constant.ApplicationTypeLabelKey] = request.Component.ComponentType.String()
		componentLabels[constant.WorkspaceNameLabelKey] = workspaceCR.Name
		componentLabels[constant.QueueNameLabelKey] = request.QueueName
		componentLabels[constant.UpdateByLabelKey] = l
		componentLabels[constant.EnvironmentTypeLabelKey] = request.EnvType.String()
		componentLabels[constant.ComponentTypeLabelKey] = request.Component.ComponentType.String()
		componentLabels[constant.DeploymentGroupNameLabelKey] = deploymentGroupEntity.DeploymentGroupID
		componentLabels[constant.WorkloadNameLabelKey] = deploymentGroupEntity.DeploymentGroupID
		componentLabels[constant.WorkloadTypeLabelKey] = constant.WorkloadTypeDeploymentGroup
		componentLabels[constant.KICManagedLabelKey] = constant.KICManagedLabelValue
	}
	if deploymentGroupEntity.ComponentSpec.ComponentType == applicationv1.ComponentType_WebService.String() {
		if request.Component.WebService != nil && request.Component.WebService.GpuShareEnabled {
			componentLabels[constant.NodeGPUSharedMemoryLabelKey] = "enabled"
		} else {
			// 移除标签
			if _, exists := componentLabels[constant.NodeGPUSharedMemoryLabelKey]; exists {
				delete(componentLabels, constant.NodeGPUSharedMemoryLabelKey)
			}
		}
	}
	if deploymentGroupEntity.ComponentSpec.ComponentType == applicationv1.ComponentType_Inference.String() {
		if request.Component.Inference != nil && request.Component.Inference.GpuShareEnabled {
			componentLabels[constant.NodeGPUSharedMemoryLabelKey] = "enabled"
		} else {
			// 移除标签
			if _, exists := componentLabels[constant.NodeGPUSharedMemoryLabelKey]; exists {
				delete(componentLabels, constant.NodeGPUSharedMemoryLabelKey)
			}
		}
	}
	annotations := deploymentGroupEntity.ComponentSpec.Annotations
	if annotations == nil {
		annotations = make(map[string]string)
		annotations[constant.DeploymentGroupDisplayNameAnnotationKey] = request.Name
	}
	request.Component.Labels = componentLabels
	request.Component.Annotations = annotations
	request.Component.Name = deploymentGroupEntity.DeploymentGroupID

	var modelTemplate *applicationv1.ModelTemplate
	if request.Component.ComponentType == applicationv1.ComponentType_SglangDistributedInference {
		if err = a.getInferenceNodeMetrics(ctx, request.Component.DistributedInference); err != nil {
			return nil, bcode.ErrorServerInternalError("get master node metrics failed,err:%v", err)
		}
		if request.ReplicaScheduling != nil && len(request.ReplicaScheduling.ReplicasOverrides) > 0 {
			request.Component.DistributedInference.Replicas = request.ReplicaScheduling.ReplicasOverrides[0].Replicas
		}
		modelTemplate = request.Component.DistributedInference.ModelTemplate
	} else if request.Component.ComponentType == applicationv1.ComponentType_LeaderWorkerSet {
		if request.ReplicaScheduling != nil && len(request.ReplicaScheduling.ReplicasOverrides) > 0 {
			request.Component.LeaderWorkerSet.Replicas = request.ReplicaScheduling.ReplicasOverrides[0].Replicas
		}
		modelTemplate = request.Component.LeaderWorkerSet.ModelTemplate
	} else if request.Component.ComponentType == applicationv1.ComponentType_Inference {
		modelTemplate = request.Component.Inference.ModelTemplate
	}

	// 如果模型模板存在，并且模型模板启用，并且模型模板加载类型为Volume，则检查模型是否预热，如果未预热，则返回错误(挂载卷无模型)
	if modelTemplate != nil && modelTemplate.Enabled && modelTemplate.LoadType == model.LoadTypeVolume {
		err = a.checkModelWarmed(ctx, modelTemplate)
		if err != nil {
			return nil, err
		}
	}

	componentRequests, err := component.Convert(ctx, request.Component)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("convert component failed, err: %v", err)
	}

	// 更新 components
	for _, componentRequest := range componentRequests {
		updateComponentRequest := &velav1.UpdateApplicationComponentRequest{
			Properties: &componentRequest.Properties,
		}
		klog.Infof("update component name: %v", componentRequest.Name)
		_, err = vela.Instance().UpdateApplicationComponent(ctx, deploymentGroupEntity.VelaProjectName, deploymentGroupEntity.VelaApplicationName, componentRequest.Name, updateComponentRequest)
		if err != nil {
			return nil, bcode.ErrorServerInternalError("update vela component failed, err: %v", err)
		}
		// 更新 trait,不一定是更新，可能有删除和新增
		traits := componentRequest.Traits
		added, updated, deleted, err := a.diffDeploymentGroupTraits(ctx, deploymentGroupEntity.VelaApplicationName, componentRequest.Name, traits)
		if err != nil {
			return nil, bcode.ErrorServerInternalError("diff deployment group traits failed, err: %v", err)
		}
		if len(added) > 0 || len(updated) > 0 || len(deleted) > 0 {
			err = a.updateDeploymentGroupTraits(ctx, deploymentGroupEntity.VelaApplicationName, componentRequest.Name, added, updated, deleted)
			if err != nil {
				return nil, err
			}
		}
	}

	// update 策略 和 集群调度
	clusterReplications, err := a.ApplyClusterReplications(ctx, request.ReplicaScheduling)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("apply cluster topology policy failed: %v", err)
	}
	for _, clusterReplication := range clusterReplications {
		componentName := request.Component.Name
		replicas := clusterReplication.Replicas
		if request.Component.ComponentType == applicationv1.ComponentType_SglangDistributedInference || request.Component.ComponentType == applicationv1.ComponentType_LeaderWorkerSet {
			// 只覆盖worker
			componentName = fmt.Sprintf("%s-worker", request.Component.Name)
			replicas = util.Max(1, replicas-1)
		}
		err = a.createOrUpdateOverridePolicy(ctx, deploymentGroupEntity.VelaApplicationName, workspaceCR, componentName, replicas, clusterReplication)
		if err != nil {
			return nil, bcode.ErrorServerInternalError("create override policy failed: %v", err)
		}
		err = a.createOrUpdateTopologyPolicy(ctx, deploymentGroupEntity.VelaApplicationName, workspaceCR, clusterReplication)
		if err != nil {
			return nil, bcode.ErrorServerInternalError("create topology policy failed: %v", err)
		}
	}
	// update workflow
	workflowResponse, err := a.updateDefaultWorkflow(ctx, deploymentGroupEntity.VelaApplicationName, workspaceCR, clusterReplications)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("update default workflow failed: %v", err)
	}
	klog.Infof("create deploymentGroup success, application:[%s], workflow: %s", deploymentGroupEntity.VelaApplicationName, util.ToJSONString(workflowResponse))
	// 更新数据库
	//deploymentGroupEntity = apphelper.ConvertToUpdateDeploymentGroupEntity(request)
	deploymentGroupEntity.Labels = request.Labels
	deploymentGroupEntity.Description = request.Description
	deploymentGroupEntity.Name = request.Name
	deploymentGroupEntity.QueueName = request.QueueName
	deploymentGroupEntity.EnvType = request.EnvType.String()
	deploymentGroupEntity.ComponentSpec = apphelper.ConvertComponentSpec(request.Component, a.Properties)
	deploymentGroupEntity.ReplicaScheduling = apphelper.ConvertReplicaScheduling(request.ReplicaScheduling)
	deploymentGroupEntity.ClusterPropagationStrategy = apphelper.ConvertPropagationStrategy(request.PropagationStrategy)

	if err = a.Store.Put(ctx, deploymentGroupEntity); err != nil {
		return nil, bcode.ErrorServerInternalError("update deployment group db failed, err: %v", err)
	}
	return apphelper.ConvertToDeploymentGroupFromEntity(ctx, deploymentGroupEntity, false), nil
}

// checkReplicaScheduling 校验副本数配置是否合法：1. 集群不能重复
func (a *ApplicationService) checkReplicaScheduling(ctx context.Context, replica *applicationv1.ReplicaScheduling) error {
	clusterMap := map[string]struct{}{}
	for _, replica := range replica.ReplicasOverrides {
		if _, ok := clusterMap[replica.ClusterName]; ok {
			return fmt.Errorf("请勿重复输入集群[%s]", replica.ClusterName)
		}
		clusterMap[replica.ClusterName] = struct{}{}
	}
	return nil
}

// ScaleDeploymentGroup 扩缩容
func (a *ApplicationService) ScaleDeploymentGroup(ctx context.Context, request *applicationv1.ScaleDeploymentGroupRequest) (*applicationv1.ScaleDeploymentGroupResponse, error) {
	account, ok := ctx.Value(constant.UserCtxKey).(string)
	if !ok {
		return nil, bcode.ErrorServerInternalError("get user from context failed")
	}
	deploymentGroupEntity, err := repository.GetDeploymentGroupById(ctx, a.Store, request.DeploymentGroupId)
	if err != nil {
		if errors.Is(err, datastore.ErrRecordNotExist) {
			return nil, bcode.ErrorDeploymentGroupNotFound("deployment group[%d] not found", request.DeploymentGroupId)
		}
		return nil, bcode.ErrorServerInternalError("get deployment group failed, err: %v", err)
	}
	if err := a.checkReplicaScheduling(ctx, request.ReplicaScheduling); err != nil {
		return nil, bcode.ErrorServerInternalError("check replica scheduling failed, err: %v", err)
	}

	//用户权限
	role, err := a.RbacService.GetUserRoleInWorkspace(ctx, deploymentGroupEntity.WorkspaceName, account)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("get user role in workspace failed, err: %v", err)
	}
	if role == RoleWorkspaceNone {
		return nil, bcode.ErrorServerInternalError("user not have permission")
	}

	// 检查当前已部署
	deploymentGroup := apphelper.ConvertToDeploymentGroupFromEntity(ctx, deploymentGroupEntity, true)
	if deploymentGroup == nil || deploymentGroup.DeployGroupStatus == nil || deploymentGroup.DeployGroupStatus.Phase == applicationv1.DeploymentGroupPhase_NonDeployed {
		return nil, bcode.ErrorServerInternalError("deployment group not running")
	}
	if deploymentGroup.Component.ComponentType == applicationv1.ComponentType_SglangDistributedInference || deploymentGroup.Component.ComponentType == applicationv1.ComponentType_LeaderWorkerSet {
		return nil, bcode.ErrorServerInternalError("当前不支持分布式推理的扩缩容")
	}

	switch request.ReplicaScheduling.ReplicaSchedulingType {
	case applicationv1.ReplicaScheduling_Divided:
		return nil, bcode.ErrorServerInternalError("当前仅支持手动指定集群副本数，不支持自动分配集群副本")
	case applicationv1.ReplicaScheduling_Duplicated:
		return nil, bcode.ErrorServerInternalError("当前仅支持手动指定集群副本数，不支持复制相同副本到不同集群")
	case applicationv1.ReplicaScheduling_Overrides:
		// 获取已部署副本数
		replicas := map[string]int32{}
		for _, scheduling := range deploymentGroupEntity.ReplicaScheduling.ReplicasOverrides {
			replicas[scheduling.ClusterName] = scheduling.Replicas
		}
		for _, scheduling := range request.ReplicaScheduling.ReplicasOverrides {
			if _, ok := replicas[scheduling.ClusterName]; !ok {
				return nil, bcode.ErrorServerInternalError("伸缩集群[%s]未部署", scheduling.ClusterName)
			}
			// 需要更新
			deployment := &appsv1.Deployment{}
			k8sCluster := multicluster.Instance().GetCluster(scheduling.ClusterName)
			if k8sCluster == nil {
				return nil, bcode.ErrorServerInternalError("cluster[%s] not found", scheduling.ClusterName)
			}
			err = k8sCluster.Direct().Get(ctx,
				client.ObjectKey{Namespace: getNamespaceName(deploymentGroupEntity.WorkspaceName),
					Name: deploymentGroupEntity.VelaApplicationName}, deployment)
			if err != nil {
				return nil, bcode.ErrorServerInternalError("get deployment failed, err: %v", err)
			}
			// scale
			scale := &autoscalingv1.Scale{
				Spec: autoscalingv1.ScaleSpec{
					Replicas: scheduling.Replicas,
				},
			}
			err = k8sCluster.Direct().SubResource("scale").Update(ctx,
				&appsv1.Deployment{
					ObjectMeta: metav1.ObjectMeta{
						Name:      deploymentGroupEntity.VelaApplicationName,
						Namespace: getNamespaceName(deploymentGroupEntity.WorkspaceName),
					},
				},
				client.WithSubResourceBody(scale),
			)
			if err != nil {
				return nil, bcode.ErrorServerInternalError("scale deployment failed, err: %v", err)
			}
		}
	}
	return &applicationv1.ScaleDeploymentGroupResponse{}, nil
}

func (a *ApplicationService) DeleteDeploymentGroup(ctx context.Context, request *applicationv1.DeleteDeploymentGroupRequest) (*emptypb.Empty, error) {
	deploymentGroupEntity, err := repository.GetDeploymentGroupById(ctx, a.Store, request.DeploymentGroupId)
	if err != nil {
		if errors.Is(err, datastore.ErrRecordNotExist) {
			return nil, bcode.ErrorDeploymentGroupNotFound("deployment group[%d] not found", request.DeploymentGroupId)
		}
		return nil, bcode.ErrorServerInternalError("get deployment group failed, err: %v", err)
	}
	// check delete permission
	account, ok := ctx.Value(constant.UserCtxKey).(string)
	if !ok {
		return nil, bcode.ErrorServerInternalError("get user from context failed")
	}
	if err = a.checkDeploymentGroupPermission(ctx, account, deploymentGroupEntity); err != nil {
		return nil, err
	}
	// check 是否有路由
	_, err = GetWorkspaceCR(ctx, deploymentGroupEntity.WorkspaceName)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("get workspace cr [%s] failed", deploymentGroupEntity.WorkspaceName)
	}
	appName := deploymentGroupEntity.VelaApplicationName
	// check application status
	// 当正在 Running 状态时，不允许删除
	appStatus, err := vela.Instance().GetApplicationStatus(ctx, appName, deploymentGroupEntity.VelaEnvName)
	if err != nil {
		if errors.Is(err, vela.ErrorApplicationNotFound) {
			a.Logger.Infof("[recycleResource] application[%s] not created, skip recycle", appName)
			deploymentGroupEntity.IsDeleted = true
			deploymentGroupEntity.Message = fmt.Sprintf("deleted by %s at %s", account, time.Now().Format(time.DateTime))
			if err = a.Store.Put(ctx, deploymentGroupEntity); err != nil {
				return nil, bcode.ErrorServerInternalError("delete deployment group db failed, err: %v", err)
			}
			return &emptypb.Empty{}, nil
		}
		a.Logger.Errorf("[recycleResource] get vela application status failed, err: %v", err)
		return nil, bcode.ErrorServerInternalError("get vela application status failed, err: %v", err)
	}

	// appStatus status为空表示没有部署
	if appStatus.Status == nil || (appStatus.Status.Phase != velacommon.ApplicationRunning && appStatus.Status.Phase != velacommon.ApplicationRunningWorkflow) {
		// 删除部署组,需要先回收资源,如果有部署组正在运行中，不允许删除
		if err = vela.Instance().DeleteApplication(ctx, deploymentGroupEntity.VelaProjectName, appName); err != nil {
			return nil, bcode.ErrorServerInternalError(fmt.Sprintf("delete vela application failed, err: %v", err))
		}
	}
	deploymentGroupEntity.IsDeleted = true
	deploymentGroupEntity.Message = fmt.Sprintf("deleted by %s at %s", account, time.Now().Format(time.DateTime))
	if err = a.Store.Put(ctx, deploymentGroupEntity); err != nil {
		return nil, bcode.ErrorServerInternalError("delete deployment group db failed, err: %v", err)
	}
	return &emptypb.Empty{}, nil
}

func (a *ApplicationService) DetailDeploymentGroupRecord(ctx context.Context, request *applicationv1.DetailDeploymentGroupRecordRequest) (*applicationv1.WorkflowRecord, error) {
	deploymentGroupEntity, err := repository.GetDeploymentGroupById(ctx, a.Store, request.DeploymentGroupId)
	if err != nil {
		if errors.Is(err, datastore.ErrRecordNotExist) {
			return nil, bcode.ErrorDeploymentGroupNotFound("deployment group[%d] not found", request.DeploymentGroupId)
		}
		return nil, bcode.ErrorServerInternalError("get deployment group failed, err: %v", err)
	}
	resp, err := vela.Instance().DetailWorkflowRecord(ctx, deploymentGroupEntity.VelaApplicationName, deploymentGroupEntity.VelaWorkflowName, request.RecordName)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("get workflow record failed, err: %v", err)
	}
	return &applicationv1.WorkflowRecord{
		Name:           resp.Name,
		WorkflowName:   resp.WorkflowName,
		WorkflowAlias:  resp.WorkflowAlias,
		Revision:       resp.ApplicationRevision,
		StartTime:      util.TimeFormat(resp.StartTime),
		EndTime:        util.TimeFormat(resp.EndTime),
		WorkflowStatus: resp.Status,
		Message:        resp.Message,
	}, nil
}

func (a *ApplicationService) ListDeploymentGroupRecords(ctx context.Context, request *applicationv1.ListWorkflowRecordsRequest) (*applicationv1.ListWorkflowRecordsResponse, error) {

	deploymentGroupEntity, err := repository.GetDeploymentGroupById(ctx, a.Store, request.DeploymentGroupId)
	if err != nil {
		if errors.Is(err, datastore.ErrRecordNotExist) {
			return nil, bcode.ErrorDeploymentGroupNotFound("deployment group[%d] not found", request.DeploymentGroupId)
		}
		return nil, bcode.ErrorServerInternalError("get deployment group failed, err: %v", err)
	}
	resp, err := vela.Instance().ListWorkflowRecordsByApplicationAndEnvName(ctx, deploymentGroupEntity.VelaApplicationName, deploymentGroupEntity.VelaEnvName)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("list workflow records failed: %v", err)
	}
	var records []velav1.WorkflowRecord
	if request.Revision != "" {
		for _, record := range resp.Records {
			if record.ApplicationRevision == request.Revision {
				records = append(records, record)
			}
		}
	} else {
		records = resp.Records
	}
	return &applicationv1.ListWorkflowRecordsResponse{
		WorkflowRecords: apphelper.ConvertWorkflowRecordToProto(records),
		Total:           int64(len(records)),
	}, nil
}

func (a *ApplicationService) DetailDeploymentGroupRevision(ctx context.Context, request *applicationv1.DetailDeploymentGroupRevisionRequest) (*applicationv1.DeploymentGroupRevision, error) {
	deploymentGroupEntity, err := repository.GetDeploymentGroupById(ctx, a.Store, request.DeploymentGroupId)
	if err != nil {
		if errors.Is(err, datastore.ErrRecordNotExist) {
			return nil, bcode.ErrorDeploymentGroupNotFound("deployment group[%d] not found", request.DeploymentGroupId)
		}
		return nil, bcode.ErrorServerInternalError("get deployment group failed, err: %v", err)
	}
	resp, err := vela.Instance().DetailApplicationRevision(ctx, deploymentGroupEntity.VelaApplicationName, request.Revision)
	if err != nil {
		return nil, err
	}
	revision := apphelper.ConvertApplicationRevisionToProto(resp)
	return revision, nil
}

func (a *ApplicationService) GetDeploymentGroupRecordLogs(ctx context.Context, request *applicationv1.DeploymentGroupRecordLogsRequest) (*applicationv1.DeploymentGroupRecordLogsResponse, error) {
	return nil, bcode.ErrorOperationNotAllowed("not support")
}

// RecycleDeploymentGroupWorkload recycle 回收资源
func (a *ApplicationService) RecycleDeploymentGroupWorkload(ctx context.Context, request *applicationv1.RecycleDeploymentGroupWorkloadRequest) (*emptypb.Empty, error) {
	deploymentGroupEntity, err := repository.GetDeploymentGroupById(ctx, a.Store, request.DeploymentGroupId)
	if err != nil {
		if errors.Is(err, datastore.ErrRecordNotExist) {
			return nil, bcode.ErrorDeploymentGroupNotFound("deployment group[%d] not found", request.DeploymentGroupId)
		}
		return nil, bcode.ErrorServerInternalError("get deployment group failed, err: %v", err)
	}
	// check delete permission
	account, ok := ctx.Value(constant.UserCtxKey).(string)
	if !ok {
		return nil, bcode.ErrorServerInternalError("get user from context failed")
	}
	if err = a.checkDeploymentGroupPermission(ctx, account, deploymentGroupEntity); err != nil {
		return nil, err
	}
	appName := deploymentGroupEntity.VelaApplicationName
	if err = vela.Instance().RecycleApplicationEnv(ctx, appName, deploymentGroupEntity.VelaEnvName); err != nil {
		a.Logger.Errorf("[recycleResource] recycle vela env failed, err: %v", err)
		return &emptypb.Empty{}, bcode.ErrorServerInternalError("recycle vela env failed, err: %v", err)
	}
	// 等待vela回收完成
	err = wait.PollImmediateWithContext(ctx, 2*time.Second, 60*time.Second, func(ctx context.Context) (done bool, err error) {
		appStatus, err := vela.Instance().GetApplicationStatus(ctx, appName, deploymentGroupEntity.VelaEnvName)
		if err != nil {
			return false, err
		}
		if appStatus.Status != nil {
			a.Logger.Infof("wait vela env recycle, appName: %s, envName: %s", appName, deploymentGroupEntity.VelaEnvName)
			return false, nil
		}
		return true, nil
	})
	if err != nil {
		a.Logger.Errorf("[recycleResource] wait vela env recycle failed, err: %v", err)
		return nil, bcode.ErrorServerInternalError("wait vela env recycle failed, err: %v", err)
	}
	klog.Infof("recycle vela env success, appName: %s, envName: %s by %s", appName, deploymentGroupEntity.VelaEnvName, account)

	// TODO 发事件

	return &emptypb.Empty{}, nil
}

func (a *ApplicationService) ResumeDeploymentGroupRecord(ctx context.Context, request *applicationv1.ResumeDeploymentGroupRecordRequest) (*emptypb.Empty, error) {
	deploymentGroupEntity, err := repository.GetDeploymentGroupById(ctx, a.Store, request.DeploymentGroupId)
	if err != nil {
		if errors.Is(err, datastore.ErrRecordNotExist) {
			return nil, bcode.ErrorDeploymentGroupNotFound("deployment group[%d] not found", request.DeploymentGroupId)
		}
		return nil, bcode.ErrorServerInternalError("get deployment group failed, err: %v", err)
	}
	// check delete permission
	account, ok := ctx.Value(constant.UserCtxKey).(string)
	if !ok {
		return nil, bcode.ErrorServerInternalError("get user from context failed")
	}
	if err = a.checkDeploymentGroupPermission(ctx, account, deploymentGroupEntity); err != nil {
		return nil, err
	}

	_, err = vela.Instance().ResumeWorkflowRecord(ctx, deploymentGroupEntity.VelaApplicationName, deploymentGroupEntity.VelaWorkflowName, request.RecordName)
	if err != nil {
		klog.Errorf("resume deployment group record failed, err: %v", err)
		return nil, bcode.ErrorServerInternalError("resume deployment group record failed, err: %v", err)
	}
	return &emptypb.Empty{}, nil
}

func (a *ApplicationService) RollbackDeploymentGroupRecord(ctx context.Context, request *applicationv1.RollbackDeploymentGroupRecordRequest) (*applicationv1.WorkflowRecord, error) {
	deploymentGroupEntity, err := repository.GetDeploymentGroupById(ctx, a.Store, request.DeploymentGroupId)
	if err != nil {
		if errors.Is(err, datastore.ErrRecordNotExist) {
			return nil, bcode.ErrorDeploymentGroupNotFound("deployment group[%d] not found", request.DeploymentGroupId)
		}
		return nil, bcode.ErrorServerInternalError("get deployment group failed, err: %v", err)
	}
	// check delete permission
	account, ok := ctx.Value(constant.UserCtxKey).(string)
	if !ok {
		return nil, bcode.ErrorServerInternalError("get user from context failed")
	}
	if err = a.checkDeploymentGroupPermission(ctx, account, deploymentGroupEntity); err != nil {
		return nil, err
	}

	resp, err := vela.Instance().RollbackWorkflowRecord(ctx, deploymentGroupEntity.VelaApplicationName, deploymentGroupEntity.VelaWorkflowName, request.RecordName)
	if err != nil {
		klog.Errorf("resume deployment group record failed, err: %v", err)
		return nil, bcode.ErrorServerInternalError("resume deployment group record failed, err: %v", err)
	}
	return &applicationv1.WorkflowRecord{
		Name:           resp.Name,
		WorkflowName:   resp.WorkflowName,
		WorkflowAlias:  resp.WorkflowAlias,
		Revision:       resp.ApplicationRevision,
		StartTime:      resp.StartTime.Format(time.DateTime),
		EndTime:        resp.EndTime.Format(time.DateTime),
		WorkflowStatus: resp.Status,
		Message:        resp.Message,
	}, nil
}

func (a *ApplicationService) RollbackDeploymentGroupRevision(ctx context.Context, request *applicationv1.RollbackDeploymentGroupRevisionRequest) (*applicationv1.RollbackDeploymentGroupRevisionResponse, error) {
	deploymentGroupEntity, err := repository.GetDeploymentGroupById(ctx, a.Store, request.DeploymentGroupId)
	if err != nil {
		if errors.Is(err, datastore.ErrRecordNotExist) {
			return nil, bcode.ErrorDeploymentGroupNotFound("deployment group[%d] not found", request.DeploymentGroupId)
		}
		return nil, bcode.ErrorServerInternalError("get deployment group failed, err: %v", err)
	}
	// check delete permission
	account, ok := ctx.Value(constant.UserCtxKey).(string)
	if !ok {
		return nil, bcode.ErrorServerInternalError("get user from context failed")
	}
	if err = a.checkDeploymentGroupPermission(ctx, account, deploymentGroupEntity); err != nil {
		return nil, err
	}
	resp, err := vela.Instance().RollbackApplicationWithRevision(ctx, deploymentGroupEntity.VelaApplicationName, request.Revision)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("rollback application with revision failed, err: %v", err)
	}
	return &applicationv1.RollbackDeploymentGroupRevisionResponse{
		Record: &applicationv1.WorkflowRecord{
			Name:           resp.WorkflowRecord.Name,
			WorkflowName:   resp.WorkflowRecord.WorkflowName,
			WorkflowAlias:  resp.WorkflowRecord.WorkflowAlias,
			Revision:       resp.WorkflowRecord.ApplicationRevision,
			StartTime:      resp.WorkflowRecord.StartTime.Format(time.DateTime),
			EndTime:        resp.WorkflowRecord.EndTime.Format(time.DateTime),
			WorkflowStatus: resp.WorkflowRecord.Status,
			Message:        resp.WorkflowRecord.Message,
		},
	}, nil
}

// TerminateDeploymentGroupRecord  deployment group record
func (a *ApplicationService) TerminateDeploymentGroupRecord(ctx context.Context, request *applicationv1.TerminateDeploymentGroupRecordRequest) (*emptypb.Empty, error) {
	deploymentGroupEntity, err := repository.GetDeploymentGroupById(ctx, a.Store, request.DeploymentGroupId)
	if err != nil {
		if errors.Is(err, datastore.ErrRecordNotExist) {
			return nil, bcode.ErrorDeploymentGroupNotFound("deployment group[%d] not found", request.DeploymentGroupId)
		}
		return nil, bcode.ErrorServerInternalError("get deployment group failed, err: %v", err)
	}
	// check delete permission
	account, ok := ctx.Value(constant.UserCtxKey).(string)
	if !ok {
		return nil, bcode.ErrorServerInternalError("get user from context failed")
	}
	if err = a.checkDeploymentGroupPermission(ctx, account, deploymentGroupEntity); err != nil {
		return nil, err
	}

	_, err = vela.Instance().TerminateWorkflowRecord(ctx, deploymentGroupEntity.VelaApplicationName, deploymentGroupEntity.VelaWorkflowName, request.RecordName)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("rollback application with revision failed, err: %v", err)
	}
	return &emptypb.Empty{}, nil
}

// ListDeploymentGroupRevisions list deployment group revisions,vela version
func (a *ApplicationService) ListDeploymentGroupRevisions(ctx context.Context, options *applicationv1.ListDeploymentGroupRevisionsOptions) (*applicationv1.ListDeploymentGroupRevisionsResult, error) {
	deploymentGroupEntity, err := repository.GetDeploymentGroupById(ctx, a.Store, options.DeploymentGroupId)
	if err != nil {
		if errors.Is(err, datastore.ErrRecordNotExist) {
			return nil, bcode.ErrorDeploymentGroupNotFound("deployment group[%d] not found", options.DeploymentGroupId)
		}
		return nil, bcode.ErrorServerInternalError("get deployment group failed, err: %v", err)
	}
	listRevisionResponse, err := vela.Instance().ListApplicationRevisions(ctx, &velav1.ListApplicationRevisionRequest{
		AppName:  deploymentGroupEntity.VelaApplicationName,
		EnvName:  deploymentGroupEntity.VelaEnvName,
		PageSize: int(options.PageSize),
		Page:     int(options.PageNo),
		Status:   options.DeployGroupStatus,
	})
	if err != nil {
		return nil, bcode.ErrorServerInternalError("list application revisions failed, err: %v", err)
	}

	filteredRevisions := filterRevisionsByStatusAndVersion(listRevisionResponse, options.DeployGroupStatus, options.Version)

	return apphelper.ConvertToDeploymentGroupRevisions(filteredRevisions), nil
}

func filterRevisionsByStatusAndVersion(revisions *velav1.ListApplicationRevisionResponse, status, version string) *velav1.ListApplicationRevisionResponse {
	var filtered []velav1.ApplicationRevisionBase
	for _, revision := range revisions.Revisions {
		if (status == "" || revision.Status == status) && (version == "" || revision.Version == version) {
			filtered = append(filtered, revision)
		}
	}
	return &velav1.ListApplicationRevisionResponse{
		Revisions: filtered,
		Total:     int64(len(filtered)),
	}
}

func (a *ApplicationService) ApplyClusterReplications(ctx context.Context, clusterReplications *applicationv1.ReplicaScheduling) ([]*applicationv1.ClusterReplication, error) {
	if clusterReplications == nil {
		// 忽略集群的副本配置
		return nil, nil
	}
	var result []*applicationv1.ClusterReplication
	switch clusterReplications.ReplicaSchedulingType {
	case applicationv1.ReplicaScheduling_Overrides:
		for _, overrides := range clusterReplications.ReplicasOverrides {
			result = append(result, &applicationv1.ClusterReplication{
				ClusterName: overrides.ClusterName,
				Replicas:    overrides.Replicas,
			})
		}
	default:
		return nil, bcode.ErrorInvalidArgument("replica scheduling type is invalid")
	}
	return result, nil
}

func (a *ApplicationService) updateDefaultWorkflow(ctx context.Context, appName string, workspaceCR *v1alpha1.Workspace, clusterReplications []*applicationv1.ClusterReplication) (*velav1.DetailWorkflowResponse, error) {
	workflowName := apphelper.ConvertWorkflowName(workspaceCR.Name)
	workflowDetail, err := vela.Instance().DetailWorkflow(ctx, appName, workflowName)
	if err != nil {
		return nil, err
	}
	//默认工作流的步骤, 标准化流程，默认工作流的
	steps := make([]velav1.WorkflowStep, 0)
	for _, clusterReplication := range clusterReplications {
		steps = append(steps, velav1.WorkflowStep{
			WorkflowStepBase: velav1.WorkflowStepBase{
				Name:        fmt.Sprintf("deploy-%s", clusterReplication.ClusterName),
				Alias:       fmt.Sprintf("集群[%s]部署", clusterReplication.ClusterName),
				Type:        "deploy",
				Description: fmt.Sprintf("集群[%s]部署步骤", clusterReplication.ClusterName),
				Properties: map[string]interface{}{
					"policies": []string{
						fmt.Sprintf("override-%s", clusterReplication.ClusterName),
						fmt.Sprintf("topology-%s", clusterReplication.ClusterName),
					},
					"parallelism": 4,
				},
			},
		})
	}
	workflowDefinition := velav1.CreateWorkflowRequest{
		Name:        workflowDetail.Name,
		Alias:       fmt.Sprintf("默认环境的工作流-%s", workspaceCR.Name),
		Description: fmt.Sprintf("默认环境的工作流-%s,由AIStudio创建生成,请不要更新它!", workspaceCR.Name),
		EnvName:     workflowDetail.EnvName,
		SubMode:     workflowDetail.SubMode,
		Mode:        workflowDetail.Mode,
		Default:     util.BoolPtr(true),
		Steps:       steps,
	}
	resp, err := vela.Instance().CreateOrUpdateApplicationWorkflow(ctx, appName, &workflowDefinition)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("create workflow failed: %v", err)
	}
	return resp, nil
}

func (a *ApplicationService) createOrUpdateTopologyPolicy(ctx context.Context, velaAppName string, workspaceCR *v1alpha1.Workspace, clusterReplication *applicationv1.ClusterReplication) error {
	policyJsonStruct, err := model.NewJSONStructByStruct(map[string]interface{}{
		"clusters":  []string{clusterReplication.ClusterName},
		"namespace": workspaceCR.Spec.Namespace,
	})
	if err != nil {
		return bcode.ErrorServerInternalError("create policy properties failed: %v", err)
	}
	policyName := fmt.Sprintf("topology-%s", clusterReplication.ClusterName)
	_, err = vela.Instance().DetailApplicationPolicy(ctx, velaAppName, policyName)
	if err != nil {
		if errors.Is(err, vela.ErrorPolicyNotFound) {
			_, err = vela.Instance().CreateApplicationPolicy(ctx, velaAppName, &velav1.CreatePolicyRequest{
				Name:        policyName,
				Type:        "topology",
				Alias:       fmt.Sprintf("默认环境的拓扑-%s", clusterReplication.ClusterName),
				EnvName:     workspaceCR.Status.VelaDefaultEnvName,
				Description: "默认环境的拓扑,由AIStudio创建生成,请不要更新它!",
				Properties:  policyJsonStruct.JSON(),
			})
			if err != nil {
				return bcode.ErrorServerInternalError("create topology policy failed: %v", err)
			}
		} else {
			_, err = vela.Instance().UpdateApplicationPolicy(ctx, velaAppName, policyName, &velav1.UpdatePolicyRequest{
				Alias:       fmt.Sprintf("默认环境的拓扑-%s", clusterReplication.ClusterName),
				Description: "默认环境的拓扑,由AIStudio创建生成,请不要更新它!",
				Properties:  policyJsonStruct.JSON(),
			})
			if err != nil {
				return bcode.ErrorServerInternalError("update topology policy failed: %v", err)
			}
		}
	}
	return nil
}

func (a *ApplicationService) createOrUpdateOverridePolicy(ctx context.Context, velaAppName string, workspaceCR *v1alpha1.Workspace, componentName string, replicas int32, clusterReplication *applicationv1.ClusterReplication) error {
	// 只覆盖 replica配置
	policyJsonStruct, err := model.NewJSONStructByStruct(map[string]interface{}{
		"components": []interface{}{
			map[string]interface{}{
				"name": componentName,
				"traits": []interface{}{
					map[string]interface{}{
						"properties": map[string]interface{}{
							"replicas": replicas,
						},
						"type": "scaler",
					},
				},
			},
		},
	})
	if err != nil {
		return bcode.ErrorServerInternalError("create policy properties failed: %v", err)
	}
	policyName := fmt.Sprintf("override-%s", clusterReplication.ClusterName)
	_, err = vela.Instance().DetailApplicationPolicy(ctx, velaAppName, policyName)
	if err != nil {
		if errors.Is(err, vela.ErrorPolicyNotFound) {
			_, err = vela.Instance().CreateApplicationPolicy(ctx, velaAppName, &velav1.CreatePolicyRequest{
				Name:        policyName,
				Alias:       fmt.Sprintf("集群[%s]的配置覆盖", clusterReplication.ClusterName),
				EnvName:     workspaceCR.Status.VelaDefaultEnvName,
				Description: fmt.Sprintf("集群[%s]的配置覆盖,由AIStudio创建生成,请不要更新它!", clusterReplication.ClusterName),
				Type:        "override",
				Properties:  policyJsonStruct.JSON(),
			})
			if err != nil {
				return bcode.ErrorServerInternalError("create policy failed: %v", err)
			}
		} else {
			return bcode.ErrorServerInternalError("get policy failed: %v", err)
		}
	} else {
		// 更新
		_, err = vela.Instance().UpdateApplicationPolicy(ctx, velaAppName, policyName, &velav1.UpdatePolicyRequest{
			Alias:       fmt.Sprintf("集群[%s]的配置覆盖", clusterReplication.ClusterName),
			Description: fmt.Sprintf("集群[%s]的配置覆盖,由AIStudio创建生成,请不要更新它!", clusterReplication.ClusterName),
			Properties:  policyJsonStruct.JSON(),
			Type:        "override",
		})
		if err != nil {
			return bcode.ErrorServerInternalError("update policy failed: %v", err)
		}
	}
	return nil
}

func (a *ApplicationService) getDeploymentGroupStatus(ctx context.Context, appName, deploymentGroupName string) (*applicationv1.DeploymentGroupStatus, error) {
	deploymentGroupEntity, err := repository.DeploymentGroupByApplicationNameAndDeploymentGroupName(ctx, a.Store, appName, deploymentGroupName)
	if err != nil {
		if errors.Is(err, datastore.ErrRecordNotExist) {
			return nil, bcode.ErrorDeploymentGroupExistsAlready("deploymentGroup[%s/%s] not found", appName, deploymentGroupName)
		}
		return nil, bcode.ErrorServerInternalError("get deploymentGroup[%s/%s] failed: %v", appName, deploymentGroupName, err)
	}
	statusResponse, err := vela.Instance().GetApplicationStatus(ctx, deploymentGroupEntity.VelaApplicationName, deploymentGroupEntity.VelaEnvName)
	if err != nil {
		return nil, err
	}
	if statusResponse.Status == nil {
		return nil, nil
	}
	return apphelper.ConvertApplicationStatusToDeploymentGroupStatus(statusResponse.Status), nil
}

func (a *ApplicationService) checkDeploymentGroupPermission(ctx context.Context, account string, deploymentGroupEntity *model.DeploymentGroupEntity) error {

	isPlatformAdmin, err := a.RbacService.IsPlatformAdmin(ctx, account)
	if err != nil {
		return bcode.ErrorServerInternalError("check platform admin error: %v", err)
	}
	isWorkspaceManager, err := a.WorkspaceService.IsWorkspaceManager(ctx, &workspacev1.IsWorkspaceManagerRequest{
		Account:       account,
		WorkspaceName: deploymentGroupEntity.WorkspaceName,
	})

	appEntity, err := repository.GetApplicationByName(ctx, a.Store, deploymentGroupEntity.WorkspaceName, deploymentGroupEntity.ApplicationName)
	if err != nil {
		return bcode.ErrorServerInternalError("get application failed, err: %v", err)
	}
	owners := appEntity.Managers
	if !isPlatformAdmin && !isWorkspaceManager.IsManager && !util.Contains(owners, account) && !util.Contains(appEntity.Members, account) {
		return bcode.ErrorUnauthorized("user[%s] has no permission to operator deployment group", account)
	}
	return nil
}

// GetWorkspaceApplicationLabels 获取工作空间下的所有应用标签,做标签筛选
func (a *ApplicationService) GetWorkspaceApplicationLabels(ctx context.Context, workspaceName string) ([]string, error) {
	items, err := repository.GetWorkspaceApplications(ctx, a.Store, workspaceName)
	if err != nil && !errors.Is(err, datastore.ErrRecordNotExist) {
		return nil, bcode.ErrorServerInternalError("get workspace applications failed: %v", err)
	}
	if items == nil || len(items) == 0 {
		return []string{}, nil
	}
	labelSet := make(map[string]struct{})
	for _, item := range items {
		app := item.(*model.ApplicationEntity)
		for k, v := range app.Labels {
			label := fmt.Sprintf("%s:%s", k, v)
			labelSet[label] = struct{}{}
		}
	}
	var allLabels []string
	for label := range labelSet {
		allLabels = append(allLabels, label)
	}
	sort.Strings(allLabels)
	return allLabels, nil
}

func (a *ApplicationService) updateDeploymentGroupTraits(ctx context.Context, velaAppName, componentName string, added []*velav1.CreateApplicationTraitRequest, updated []*velav1.UpdateApplicationTraitRequest, deleted []string) error {
	for _, trait := range added {
		if _, err := vela.Instance().AddApplicationTrait(ctx, velaAppName, componentName, trait); err != nil {
			return bcode.ErrorVelaInternalError("failed to add trait %s: %v", trait.Type, err)
		}
	}
	for _, trait := range updated {
		if _, err := vela.Instance().UpdateApplicationTrait(ctx, velaAppName, componentName, trait.TraitType, trait); err != nil {
			return bcode.ErrorVelaInternalError("failed to update trait %s: %v", trait.TraitType, err)
		}
	}

	for _, trait := range deleted {
		if err := vela.Instance().DeleteApplicationTrait(ctx, velaAppName, componentName, trait); err != nil {
			return bcode.ErrorVelaInternalError("failed to delete trait %s: %v", trait, err)
		}
	}
	return nil
}

func (a *ApplicationService) diffDeploymentGroupTraits(ctx context.Context, velaApplicationName, deploymentGroupID string, requestTraits []*velav1.CreateApplicationTraitRequest) (added []*velav1.CreateApplicationTraitRequest, updated []*velav1.UpdateApplicationTraitRequest, deleted []string, err error) {
	currentComponent, err := vela.Instance().DetailComponent(ctx, velaApplicationName, deploymentGroupID)
	if err != nil {
		return nil, nil, nil, err
	}
	if currentComponent == nil {
		return nil, nil, nil, bcode.ErrorDeploymentGroupNotFound("deployment component[%s] not found", deploymentGroupID)
	}
	currentTraits := currentComponent.Traits

	for _, request := range requestTraits {
		if !a.containsTrait(currentTraits, request.Type) {
			added = append(added, request)
		} else {
			updated = append(updated, &velav1.UpdateApplicationTraitRequest{
				Properties: request.Properties,
				TraitType:  request.Type,
			})
		}
	}
	for _, currentTrait := range currentTraits {
		if !a.containsTrait(requestTraits, currentTrait.Type) {
			deleted = append(deleted, currentTrait.Type)
		}
	}
	return added, updated, deleted, nil
}

func (a *ApplicationService) containsTrait(traits interface{}, traitType string) bool {
	switch t := traits.(type) {
	case []velav1.ApplicationTrait:
		for _, trait := range t {
			if trait.Type == traitType {
				return true
			}
		}
	case []*velav1.CreateApplicationTraitRequest:
		for _, trait := range t {
			if trait.Type == traitType {
				return true
			}
		}
	}
	return false
}

func (a *ApplicationService) getInferenceNodeMetrics(ctx context.Context, distributedInferenceTemplate *applicationv1.DistributedInferenceTemplate) error {
	specificationName := distributedInferenceTemplate.NodeSpecificationName
	nodeMetrics, err := a.getNodeMetricBySpecificationName(ctx, specificationName)
	if err != nil {
		return err
	}
	distributedInferenceTemplate.Specification = &apiscommon.Specification{}
	if nodeMetrics == nil {
		return fmt.Errorf("nodeMetrics is nil")
	}
	// Debug
	distributedInferenceTemplate.Specification.CpuNum = int32(120)
	distributedInferenceTemplate.Specification.MemoryGiB = int32(100000)
	distributedInferenceTemplate.Specification.GpuNum = int32(8)
	// cpu和memory
	if nodeMetrics.ResourceMetrics[constant.ResourceNvidiaGPU] != nil {
		distributedInferenceTemplate.Specification.GpuNum = int32(nodeMetrics.ResourceMetrics[constant.ResourceNvidiaGPU].Allocatable.Value())
	}
	if nodeMetrics.ResourceMetrics[corev1.ResourceCPU] != nil {
		allocatable := nodeMetrics.ResourceMetrics[corev1.ResourceCPU].Allocatable.Value()
		distributedInferenceTemplate.Specification.CpuNum = int32(float64(allocatable) * 0.7)
	}
	if nodeMetrics.ResourceMetrics[corev1.ResourceMemory] != nil {
		memoryByteAllocatable := nodeMetrics.ResourceMetrics[corev1.ResourceMemory].Allocatable.Value()
		allocatable := memoryByteAllocatable / Gibibyte
		distributedInferenceTemplate.Specification.MemoryGiB = int32(float64(allocatable) * 0.7)
	}
	return nil
}

func (a *ApplicationService) getNodeMetricBySpecificationName(ctx context.Context, nodeSpecificationName string) (*schedulingv1alpha1.NodeMetric, error) {
	nodeMetrics, err := a.ClusterManager.GetNodeMetricsBySpecificationName(ctx, nodeSpecificationName)
	if err != nil {
		return nil, err
	}
	if len(nodeMetrics) == 0 {
		return nil, errors.New("no[%s] node metric found")
	}
	return &nodeMetrics[0], nil
}
