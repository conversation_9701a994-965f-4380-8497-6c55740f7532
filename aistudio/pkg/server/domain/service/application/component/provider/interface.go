package provider

import (
	"context"
	"fmt"
	"git.lianjia.com/cloudnative/kic/kic-platform/aistudio/pkg/server/domain/model"

	"strings"

	"git.lianjia.com/cloudnative/kic/kic-platform/aistudio/pkg/constant"
	applicationv1 "git.lianjia.com/cloudnative/kic/kic-platform/apis/aistudio/application/v1"
	configv1 "git.lianjia.com/cloudnative/kic/kic-platform/apis/aistudio/config/v1"
	"git.lianjia.com/cloudnative/kic/kic-platform/apis/common"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/apis/scheduling.kcs.io/v1alpha1"
	velaapisv1 "git.lianjia.com/cloudnative/kic/kic-platform/pkg/third_party/velaux/apis/v1"
	velav1 "git.lianjia.com/cloudnative/kic/kic-platform/pkg/third_party/velaux/apis/v1"
	veladef "git.lianjia.com/cloudnative/kic/kic-platform/pkg/third_party/velaux/definitions"
	veladefcomponent "git.lianjia.com/cloudnative/kic/kic-platform/pkg/third_party/velaux/definitions/component"
	veladeftrait "git.lianjia.com/cloudnative/kic/kic-platform/pkg/third_party/velaux/definitions/trait"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/util"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/util/intstr"

	"strconv"
)

type ComponentConverter interface {
	Convert(ctx context.Context, component *applicationv1.ComponentSpec) ([]*velaapisv1.CreateComponentRequest, error)
}

const (
	ScalerTrait              = "scaler"
	KedaAutoScalerTrait      = "ke-keda-autoscale"
	UpdateStrategyTrait      = "k8s-update-strategy"
	LogSidecarTrait          = "kic-logsidecar"
	LifeCycleTrait           = "kcs-lifecycle"
	AffinityTrait            = "affinity"
	AnnotationTrait          = "annotations"
	LabelTrait               = "labels"
	ServiceExportTrait       = "service-export"
	InitContainerTrait       = "init-container"
	KicResourcesTrait        = "kic-resources"
	KeHostNetworkTrait       = "ke-hostnetwork"
	ContainerPrivilegedTrait = "container-priviledged"
	JsonMergePatchTrait      = "json-merge-patch"
	StartupProbeTrait        = "startup-probe"
)

// ConvertVelaApplicationTrait  是从前端传入的运维特性转换为vela的运维特性
func ConvertVelaApplicationTrait(ctx context.Context, component *applicationv1.ComponentSpec, appTraits []*velav1.CreateApplicationTraitRequest) ([]*velav1.CreateApplicationTraitRequest, error) {
	workspaceCR := ctx.Value(constant.WorkspaceCRCtxKey).(*v1alpha1.Workspace)
	traits := component.Traits
	workspaceName := component.Labels[constant.WorkspaceNameLabelKey]
	appName := component.Labels[constant.ApplicationNameLabelKey]
	deploymentGroupId := component.Labels[constant.DeploymentGroupNameLabelKey]
	// auto scale
	if traits.AutoScale != nil && traits.AutoScale.Enabled {
		// 替换query中的变量
		if traits.AutoScale.Triggers != nil {
			if prometheus := traits.AutoScale.Triggers.Prometheus; prometheus != nil && prometheus.Query != "" {
				query := prometheus.Query
				query = strings.Replace(query, "${workspaceName}", workspaceName, -1)
				query = strings.Replace(query, "${deploymentGroupID}", deploymentGroupId, -1)
				prometheus.Query = query
			}
		}
		velaDefParam := veladeftrait.KeDaAutoscaleParameter{
			MinReplicaCount: traits.AutoScale.MinReplicaCount,
			MaxReplicaCount: traits.AutoScale.MaxReplicaCount,
			PollingInterval: traits.AutoScale.PollingIntervalSeconds,
			ScaleTargetRef: veladeftrait.ScaleTargetRef{
				APIVersion: "apps/v1",
				Type:       "Deployment",
				Name:       component.Name,
				Container:  component.Name,
			},
			Triggers: ConvertToVelaDefinitionTriggers(traits.AutoScale.Triggers),
		}
		properties, err := velav1.NewJSONStructByStruct(velaDefParam)
		if err != nil {
			return nil, err
		}
		appTraits, err = UpsertTrait(appTraits, KedaAutoScalerTrait, properties)
		if err != nil {
			return nil, err
		}
	}
	// update strategy
	if traits.UpdateStrategy != nil && traits.UpdateStrategy.Enabled {
		velaDefParam := veladeftrait.UpdateStrategyParameter{
			TargetAPIVersion: "apps/v1",
			TargetKind:       "Deployment",
			Strategy: veladeftrait.Strategy{
				Type: traits.UpdateStrategy.Type.String(),
				RollingStrategy: &veladeftrait.RollingUpdateStrategy{
					MaxSurge:       fmt.Sprintf("%d%%", traits.UpdateStrategy.MaxSurge),
					MaxUnavailable: fmt.Sprintf("%d%%", traits.UpdateStrategy.MaxUnavailable),
					Partition:      traits.UpdateStrategy.Partition,
				}},
		}
		properties, err := velav1.NewJSONStructByStruct(velaDefParam)
		if err != nil {
			return nil, err
		}
		appTraits, err = UpsertTrait(appTraits, UpdateStrategyTrait, properties)
		if err != nil {
			return nil, err
		}
	}
	// log sidecar
	if traits.LogCollect != nil && traits.LogCollect.Enabled {
		nicaOutput := traits.LogCollect.GetNica()
		// 判断一下 traits.LogCollect.Labels 是否有 key 为 app 和 deploymentGroupId
		if traits.LogCollect.Labels == nil {
			traits.LogCollect.Labels = make(map[string]string)
		}
		traits.LogCollect.Labels["app"] = appName
		traits.LogCollect.Labels["deploymentGroupId"] = deploymentGroupId
		traits.LogCollect.Labels["logtype"] = "filelog"

		velaDefParam := veladeftrait.LogSidecarParameter{
			Labels:         traits.LogCollect.Labels,
			LogDir:         traits.LogCollect.LogDir,
			LogFiles:       traits.LogCollect.LogFiles,
			ResourceCPU:    "1",
			ResourceMemory: "2Gi",
			Priority:       "1",
			Loki: veladeftrait.Loki{
				URL:       nicaOutput.LokiURL,
				BatchSize: 30720,
				Job:       fmt.Sprintf("%s/%s/%s", nicaOutput.NicaWorkspaceName, nicaOutput.StreamConfigName, "config"), //nica的workspace名称/stream名称/文件名
			},
		}
		velaDefParam.GenerateLogPaths()
		properties, err := velav1.NewJSONStructByStruct(velaDefParam)
		if err != nil {
			return nil, err
		}
		appTraits, err = UpsertTrait(appTraits, LogSidecarTrait, properties)
		if err != nil {
			return nil, err
		}
	}

	// lifecycle trait
	if traits.Lifecycle != nil && traits.Lifecycle.Enabled {
		velaDefParam := veladeftrait.LifeCycleParameter{
			TerminationGracePeriodSeconds: &traits.Lifecycle.TerminationGracePeriodSeconds,
			PreStop:                       convertToLifeCycleHandler(traits.Lifecycle.PreStop),
			PostStart:                     convertToLifeCycleHandler(traits.Lifecycle.PostStart),
		}
		properties, err := velav1.NewJSONStructByStruct(velaDefParam)
		if err != nil {
			return nil, err
		}
		appTraits, err = UpsertTrait(appTraits, LifeCycleTrait, properties)
		if err != nil {
			return nil, err
		}

	}
	// affinity trait
	velaDefParam := veladeftrait.AffinityParameter{}
	if traits.Affinity != nil {
		if traits.Affinity.NodeAffinity != nil {
			velaDefParam.NodeAffinity = convertNodeAffinity(traits.Affinity.NodeAffinity)
		}
		if traits.Affinity.PodAffinity != nil {
			velaDefParam.PodAffinity = convertPodAffinity(traits.Affinity.PodAffinity)
		}
		if traits.Affinity.PodAntiAffinity != nil {
			velaDefParam.PodAntiAffinity = convertPodAntiAffinity(traits.Affinity.PodAntiAffinity)
		}
		velaDefParam.Tolerations = convertTolerations(traits.Affinity.Tolerations)
		properties, err := velav1.NewJSONStructByStruct(velaDefParam)
		if err != nil {
			return nil, err
		}
		appTraits, err = UpsertTrait(appTraits, AffinityTrait, properties)
		if err != nil {
			return nil, err
		}

	}
	// metrics trait
	var annotations map[string]string
	if component.Annotations != nil {
		annotations = component.Annotations
	}
	if component.Traits.Metrics != nil && component.Traits.Metrics.Enabled {
		metricsAnnotations := map[string]string{
			"prometheus.io/path":   component.Traits.Metrics.MetricsPath,
			"prometheus.io/port":   strconv.Itoa(int(component.Traits.Metrics.MetricsPort)),
			"prometheus.io/scrape": "true",
		}
		if component.Annotations != nil {
			annotations = util.MergeMaps(component.Annotations, metricsAnnotations)
		}
	}
	if annotations != nil {
		properties, err := velav1.NewJSONStructByStruct(annotations)
		if err != nil {
			return nil, err
		}
		appTraits, err = UpsertTrait(appTraits, AnnotationTrait, properties)
		if err != nil {
			return nil, err
		}
	}

	// 处理label trait
	if component.Labels != nil {
		properties, err := velav1.NewJSONStructByStruct(component.Labels)
		if err != nil {
			return nil, err
		}
		appTraits, err = UpsertTrait(appTraits, LabelTrait, properties)
		if err != nil {
			return nil, err
		}
	}

	if component.ServiceExport != nil && component.ServiceExport.Enabled {
		serviceExportTrait := map[string]interface{}{}
		var exportServiceName string
		if component.ServiceExport.ServiceName != "" {
			exportServiceName = component.ServiceExport.ServiceName
		} else {
			exportServiceName = component.Name
		}
		additionalPortNames := component.ServiceExport.AdditionalPortNames
		if additionalPortNames == nil {
			additionalPortNames = []string{}
		}
		if component.ServiceExport.Enabled {
			serviceExportTrait["metaCluster"] = map[string]interface{}{
				"enabled":             true,
				"name":                fmt.Sprintf("%s-apiserver", component.Name),
				"serviceName":         exportServiceName,
				"serviceNamespace":    workspaceCR.Spec.Namespace,
				"portName":            component.ServiceExport.PortName,
				"additionalPortNames": additionalPortNames,
			}
			serviceExportTrait["polaris"] = map[string]interface{}{
				"enabled":             true,
				"name":                fmt.Sprintf("%s-polaris", component.Name),
				"serviceName":         exportServiceName,
				"serviceNamespace":    workspaceCR.Name,
				"portName":            component.ServiceExport.PortName,
				"additionalPortNames": additionalPortNames,
			}
			properties, err := velav1.NewJSONStructByStruct(serviceExportTrait)
			if err != nil {
				return nil, err
			}
			appTraits, err = UpsertTrait(appTraits, ServiceExportTrait, properties)
		}
	}
	return appTraits, nil
}

// ConvertLeaderTrait convert sglang leader trait to vela trait
func ConvertLeaderTrait(ctx context.Context, component *applicationv1.ComponentSpec, affinityTraits *applicationv1.AffinityTrait, appTraits []*velav1.CreateApplicationTraitRequest) ([]*velav1.CreateApplicationTraitRequest, error) {
	workspaceCR := ctx.Value(constant.WorkspaceCRCtxKey).(*v1alpha1.Workspace)
	traits := component.Traits
	// log sidecar
	if traits.LogCollect != nil && traits.LogCollect.Enabled {
		nicaOutput := traits.LogCollect.GetNica()
		velaDefParam := veladeftrait.LogSidecarParameter{
			Labels:         traits.LogCollect.Labels,
			LogDir:         traits.LogCollect.LogDir,
			LogFiles:       traits.LogCollect.LogFiles,
			ResourceCPU:    "1",
			ResourceMemory: "2Gi",
			Priority:       "1",
			Loki: veladeftrait.Loki{
				URL:       nicaOutput.LokiURL,
				BatchSize: 30720,
				Job:       fmt.Sprintf("%s/%s/%s", nicaOutput.NicaWorkspaceName, nicaOutput.StreamConfigName, "config"), //nica的workspace名称/stream名称/文件名
			},
		}
		velaDefParam.GenerateLogPaths()
		properties, err := velav1.NewJSONStructByStruct(velaDefParam)
		if err != nil {
			return nil, err
		}
		appTraits, err = UpsertTrait(appTraits, LogSidecarTrait, properties)
		if err != nil {
			return nil, err
		}
	}

	// lifecycle trait
	if traits.Lifecycle != nil && traits.Lifecycle.Enabled {
		velaDefParam := veladeftrait.LifeCycleParameter{
			PreStop:                       convertToLifeCycleHandler(traits.Lifecycle.PreStop),
			PostStart:                     convertToLifeCycleHandler(traits.Lifecycle.PostStart),
			TerminationGracePeriodSeconds: &traits.Lifecycle.TerminationGracePeriodSeconds,
		}
		properties, err := velav1.NewJSONStructByStruct(velaDefParam)
		if err != nil {
			return nil, err
		}
		appTraits, err = UpsertTrait(appTraits, LifeCycleTrait, properties)
		if err != nil {
			return nil, err
		}

	}
	// affinity trait
	if affinityTraits != nil {
		component.Traits.Affinity = affinityTraits
	}
	velaDefParam := veladeftrait.AffinityParameter{}
	if traits.Affinity != nil {
		if traits.Affinity.NodeAffinity != nil {
			velaDefParam.NodeAffinity = convertNodeAffinity(traits.Affinity.NodeAffinity)
		}
		if traits.Affinity.PodAffinity != nil {
			velaDefParam.PodAffinity = convertPodAffinity(traits.Affinity.PodAffinity)
		}
		if traits.Affinity.PodAntiAffinity != nil {
			velaDefParam.PodAntiAffinity = convertPodAntiAffinity(traits.Affinity.PodAntiAffinity)
		}
		velaDefParam.Tolerations = convertTolerations(traits.Affinity.Tolerations)
		properties, err := velav1.NewJSONStructByStruct(velaDefParam)
		if err != nil {
			return nil, err
		}
		appTraits, err = UpsertTrait(appTraits, AffinityTrait, properties)
		if err != nil {
			return nil, err
		}

	}
	// metrics trait
	var annotations map[string]string
	if component.Annotations != nil {
		annotations = component.Annotations
	}
	if component.Traits.Metrics != nil && component.Traits.Metrics.Enabled {
		metricsAnnotations := map[string]string{
			"prometheus.io/path":   component.Traits.Metrics.MetricsPath,
			"prometheus.io/port":   strconv.Itoa(int(component.Traits.Metrics.MetricsPort)),
			"prometheus.io/scrape": "true",
		}
		if component.Annotations != nil {
			annotations = util.MergeMaps(component.Annotations, metricsAnnotations)
		}
	}
	if annotations != nil {
		properties, err := velav1.NewJSONStructByStruct(annotations)
		if err != nil {
			return nil, err
		}
		appTraits, err = UpsertTrait(appTraits, AnnotationTrait, properties)
		if err != nil {
			return nil, err
		}
	}

	// 处理label trait
	if component.Labels != nil {
		properties, err := velav1.NewJSONStructByStruct(component.Labels)
		if err != nil {
			return nil, err
		}
		appTraits, err = UpsertTrait(appTraits, LabelTrait, properties)
		if err != nil {
			return nil, err
		}
	}

	if component.ServiceExport != nil && component.ServiceExport.Enabled {
		serviceExportTrait := map[string]interface{}{}
		var exportServiceName string
		if component.ServiceExport.ServiceName != "" {
			exportServiceName = component.ServiceExport.ServiceName
		} else {
			exportServiceName = component.Name
		}
		additionalPortNames := component.ServiceExport.AdditionalPortNames
		if additionalPortNames == nil {
			additionalPortNames = []string{}
		}
		if component.ServiceExport.Enabled {
			serviceExportTrait["metaCluster"] = map[string]interface{}{
				"enabled":             true,
				"name":                fmt.Sprintf("%s-apiserver", component.Name),
				"serviceName":         exportServiceName,
				"serviceNamespace":    workspaceCR.Spec.Namespace,
				"portName":            component.ServiceExport.PortName,
				"additionalPortNames": additionalPortNames,
			}
			serviceExportTrait["polaris"] = map[string]interface{}{
				"enabled":             true,
				"name":                fmt.Sprintf("%s-polaris", component.Name),
				"serviceName":         exportServiceName,
				"serviceNamespace":    workspaceCR.Name,
				"portName":            component.ServiceExport.PortName,
				"additionalPortNames": additionalPortNames,
			}
			properties, err := velav1.NewJSONStructByStruct(serviceExportTrait)
			if err != nil {
				return nil, err
			}
			appTraits, err = UpsertTrait(appTraits, ServiceExportTrait, properties)
		}
	}
	return appTraits, nil
}

// ConvertWorkerTrait convert sglang worker trait to vela trait
func ConvertWorkerTrait(ctx context.Context, component *applicationv1.ComponentSpec, affinityTraits *applicationv1.AffinityTrait, appTraits []*velav1.CreateApplicationTraitRequest) ([]*velav1.CreateApplicationTraitRequest, error) {
	traits := component.Traits
	// 只扩缩容 worker
	if traits.AutoScale != nil && traits.AutoScale.Enabled {
		workerName := fmt.Sprintf("%s-worker", component.Name)
		velaDefParam := veladeftrait.KeDaAutoscaleParameter{
			MinReplicaCount: traits.AutoScale.MinReplicaCount,
			MaxReplicaCount: traits.AutoScale.MaxReplicaCount,
			PollingInterval: traits.AutoScale.PollingIntervalSeconds,
			ScaleTargetRef: veladeftrait.ScaleTargetRef{
				APIVersion: "apps/v1",
				Type:       "StatefulSet",
				Name:       workerName,
				Container:  workerName,
			},
			Triggers: ConvertToVelaDefinitionTriggers(traits.AutoScale.Triggers),
		}
		properties, err := velav1.NewJSONStructByStruct(velaDefParam)
		if err != nil {
			return nil, err
		}
		appTraits, err = UpsertTrait(appTraits, KedaAutoScalerTrait, properties)
		if err != nil {
			return nil, err
		}
	}
	// update strategy
	if traits.UpdateStrategy != nil && traits.UpdateStrategy.Enabled {
		velaDefParam := veladeftrait.UpdateStrategyParameter{
			TargetAPIVersion: "apps/v1",
			TargetKind:       "StatefulSet",
			Strategy: veladeftrait.Strategy{
				Type: traits.UpdateStrategy.Type.String(),
				RollingStrategy: &veladeftrait.RollingUpdateStrategy{
					MaxSurge:       fmt.Sprintf("%d%%", traits.UpdateStrategy.MaxSurge),
					MaxUnavailable: fmt.Sprintf("%d%%", traits.UpdateStrategy.MaxUnavailable),
					Partition:      traits.UpdateStrategy.Partition,
				}},
		}
		properties, err := velav1.NewJSONStructByStruct(velaDefParam)
		if err != nil {
			return nil, err
		}
		appTraits, err = UpsertTrait(appTraits, UpdateStrategyTrait, properties)
		if err != nil {
			return nil, err
		}
	}
	// log sidecar
	if traits.LogCollect != nil && traits.LogCollect.Enabled {
		nicaOutput := traits.LogCollect.GetNica()
		velaDefParam := veladeftrait.LogSidecarParameter{
			Labels:         traits.LogCollect.Labels,
			LogDir:         traits.LogCollect.LogDir,
			LogFiles:       traits.LogCollect.LogFiles,
			ResourceCPU:    "1",
			ResourceMemory: "2Gi",
			Priority:       "1",
			Loki: veladeftrait.Loki{
				URL:       nicaOutput.LokiURL,
				BatchSize: 30720,
				Job:       fmt.Sprintf("%s/%s/%s", nicaOutput.NicaWorkspaceName, nicaOutput.StreamConfigName, "config"), //nica的workspace名称/stream名称/文件名
			},
		}
		velaDefParam.GenerateLogPaths()
		properties, err := velav1.NewJSONStructByStruct(velaDefParam)
		if err != nil {
			return nil, err
		}
		appTraits, err = UpsertTrait(appTraits, LogSidecarTrait, properties)
		if err != nil {
			return nil, err
		}
	}

	// affinity trait
	velaDefParam := veladeftrait.AffinityParameter{}
	if affinityTraits != nil {
		component.Traits.Affinity = affinityTraits
	}

	if traits.Affinity != nil {
		if traits.Affinity.NodeAffinity != nil {
			velaDefParam.NodeAffinity = convertNodeAffinity(traits.Affinity.NodeAffinity)
		}
		if traits.Affinity.PodAffinity != nil {
			velaDefParam.PodAffinity = convertPodAffinity(traits.Affinity.PodAffinity)
		}
		if traits.Affinity.PodAntiAffinity != nil {
			velaDefParam.PodAntiAffinity = convertPodAntiAffinity(traits.Affinity.PodAntiAffinity)
		}
		velaDefParam.Tolerations = convertTolerations(traits.Affinity.Tolerations)
		properties, err := velav1.NewJSONStructByStruct(velaDefParam)
		if err != nil {
			return nil, err
		}
		appTraits, err = UpsertTrait(appTraits, AffinityTrait, properties)
		if err != nil {
			return nil, err
		}
	}

	if component.Annotations != nil {
		properties, err := velav1.NewJSONStructByStruct(component.Annotations)
		if err != nil {
			return nil, err
		}
		appTraits, err = UpsertTrait(appTraits, AnnotationTrait, properties)
		if err != nil {
			return nil, err
		}
	}

	// 处理label trait
	if component.Labels != nil {
		properties, err := velav1.NewJSONStructByStruct(component.Labels)
		if err != nil {
			return nil, err
		}
		appTraits, err = UpsertTrait(appTraits, LabelTrait, properties)
		if err != nil {
			return nil, err
		}
	}

	return appTraits, nil
}

func RenderScalerTrait(replicas int32) (*velav1.JSONStruct, error) {
	velaDefParam := veladeftrait.ScalerParameter{
		Replicas: replicas,
	}
	properties, err := velav1.NewJSONStructByStruct(velaDefParam)
	if err != nil {
		return nil, err
	}
	return properties, nil
}

func RenderStartUpProbeTrait(startupProbe *applicationv1.Probe) (*velav1.JSONStruct, error) {
	velaDefParam := ConvertToVelaDefinitionProbe(startupProbe)
	properties, err := velav1.NewJSONStructByStruct(velaDefParam)
	if err != nil {
		return nil, err
	}
	return properties, nil
}

func HostNetWorkTrait() (*velav1.JSONStruct, error) {
	velaDefParam := veladeftrait.HostNetworkParameter{
		HostNetwork: true,
		HostIPC:     true,
		DnsPolicy:   "ClusterFirstWithHostNet",
	}
	properties, err := velav1.NewJSONStructByStruct(velaDefParam)
	if err != nil {
		return nil, err
	}
	return properties, nil
}

func PrivilegedTrait() (*velav1.JSONStruct, error) {
	velaDefParam := veladeftrait.PrivilegedParameter{
		Privileged:    true,
		ContainerName: "",
	}
	properties, err := velav1.NewJSONStructByStruct(velaDefParam)
	if err != nil {
		return nil, err
	}
	return properties, nil
}

func RenderKicResourcesTrait(resources *common.Specification) (*velav1.JSONStruct, error) {
	velaDefParam := veladeftrait.KicResourcesParameter{
		CPU:             resources.CpuNum,
		Memory:          fmt.Sprintf("%sGi", strconv.Itoa(int(resources.MemoryGiB))),
		NvidiaGPU:       resources.GpuNum,
		NvidiaGpuMemory: resources.GpuMem,
	}
	properties, err := velav1.NewJSONStructByStruct(velaDefParam)
	if err != nil {
		return nil, err
	}
	return properties, nil
}
func RenderDefaultTolerations() (*velav1.JSONStruct, error) {
	var tolerations []veladeftrait.Toleration
	tolerations = append(tolerations, veladeftrait.Toleration{
		Key:      "aicp.group/worker",
		Operator: "Exists",
		Effect:   "NoSchedule",
	}, veladeftrait.Toleration{
		Key:      constant.AIStudioTaintKey,
		Operator: "Equal",
		Value:    "aistudio",
	})
	velaDefParam := veladeftrait.AffinityParameter{
		Tolerations: tolerations,
	}
	properties, err := velav1.NewJSONStructByStruct(velaDefParam)
	if err != nil {
		return nil, err
	}
	return properties, nil
}

func RenderApplicationLabelsTrait(labels map[string]string) (*velav1.JSONStruct, error) {
	properties, err := velav1.NewJSONStructByStruct(labels)
	if err != nil {
		return nil, err
	}
	return properties, nil
}

func RenderApplicationAnnotationsTrait(annotations map[string]string) (*velav1.JSONStruct, error) {
	properties, err := velav1.NewJSONStructByStruct(annotations)
	if err != nil {
		return nil, err
	}
	return properties, nil
}

func convertNodeAffinity(nodeAffinity *applicationv1.NodeAffinity) *veladeftrait.NodeAffinity {
	if nodeAffinity == nil {
		return nil
	}
	converted := &veladeftrait.NodeAffinity{}

	if len(nodeAffinity.Required) > 0 {
		reqMap := make(map[string]*veladeftrait.NodeSelectorRequirement)
		for _, req := range nodeAffinity.Required {
			if existing, found := reqMap[req.Key]; found && existing.Operator == req.Operator {
				existing.Values = append(existing.Values, req.Values...)
			} else {
				reqMap[req.Key] = &veladeftrait.NodeSelectorRequirement{
					Key:      req.Key,
					Operator: req.Operator,
					Values:   append([]string{}, req.Values...),
				}
			}
		}

		var terms []veladeftrait.NodeSelectorRequirement
		for _, v := range reqMap {
			terms = append(terms, *v)
		}

		converted.Required = &veladeftrait.NodeSelector{
			NodeSelectorTerms: []veladeftrait.NodeSelectorTerm{
				{
					MatchExpressions: terms,
				},
			},
		}
	}

	if len(nodeAffinity.Preferred) > 0 {
		prefMap := make(map[string]*veladeftrait.NodeSelectorRequirement)
		for _, pref := range nodeAffinity.Preferred {
			if existing, found := prefMap[pref.Key]; found && existing.Operator == pref.Operator {
				existing.Values = append(existing.Values, pref.Values...)
			} else {
				prefMap[pref.Key] = &veladeftrait.NodeSelectorRequirement{
					Key:      pref.Key,
					Operator: pref.Operator,
					Values:   append([]string{}, pref.Values...),
				}
			}
		}

		var weightedPreferences []veladeftrait.WeightedNodePreference
		var terms []veladeftrait.NodeSelectorRequirement
		for _, v := range prefMap {
			terms = append(terms, *v)
		}
		weightedPreferences = append(weightedPreferences, veladeftrait.WeightedNodePreference{
			Weight: 1,
			Preference: veladeftrait.NodeSelectorTerm{
				MatchExpressions: terms,
			},
		})

		converted.Preferred = weightedPreferences
	}

	return converted
}

func convertPodAffinity(podAffinity *applicationv1.PodAffinity) *veladeftrait.PodAffinity {
	if podAffinity == nil {
		return nil
	}
	converted := &veladeftrait.PodAffinity{}
	if len(podAffinity.Required) > 0 {
		converted.Required = convertPodAffinityTerms(podAffinity.Required)
	}
	if len(podAffinity.Preferred) > 0 {
		converted.Preferred = convertWeightedPodAffinityTerms(podAffinity.Preferred)
	}
	return converted
}
func convertPodAffinityTerms(terms []*applicationv1.PodAffinityTerm) []veladeftrait.PodAffinityTerm {
	var convertedTerms []veladeftrait.PodAffinityTerm
	for i, term := range terms {
		convertedTerms[i] = veladeftrait.PodAffinityTerm{
			LabelSelector: convertLabelSelectors(term.LabelSelector),
			TopologyKey:   term.TopologyKey,
		}
	}
	return convertedTerms
}

func convertWeightedPodAffinityTerms(terms []*applicationv1.PodAffinityTerm) []veladeftrait.WeightedPodAffinityTerm {
	var weightedTerms []veladeftrait.WeightedPodAffinityTerm
	for i, term := range terms {
		weightedTerms[i] = veladeftrait.WeightedPodAffinityTerm{
			Weight: 1,
			PodAffinityTerm: veladeftrait.PodAffinityTerm{
				LabelSelector: convertLabelSelectors(term.LabelSelector),
				TopologyKey:   term.TopologyKey,
			},
		}
	}
	return weightedTerms
}

func convertLabelSelectors(selectors []*applicationv1.PodLabelSelector) map[string]string {
	convertedSelectors := make(map[string]string)
	for _, selector := range selectors {
		if selector.Operator == "In" && len(selector.Values) == 1 {
			convertedSelectors[selector.Key] = selector.Values[0]
		}
	}
	return convertedSelectors
}

func convertPodAntiAffinity(podAntiAffinity *applicationv1.PodAntiAffinity) *veladeftrait.PodAntiAffinity {
	if podAntiAffinity == nil {
		return nil
	}
	converted := &veladeftrait.PodAntiAffinity{}
	if len(podAntiAffinity.Required) > 0 {
		converted.Required = convertPodAffinityTerms(podAntiAffinity.Required)
	}
	if len(podAntiAffinity.Preferred) > 0 {
		converted.Preferred = convertWeightedPodAffinityTerms(podAntiAffinity.Preferred)
	}
	return converted
}

func convertTolerations(tolerations []*applicationv1.Toleration) []veladeftrait.Toleration {
	convertedTolerations := make([]veladeftrait.Toleration, len(tolerations))
	for i, tol := range tolerations {
		convertedTolerations[i] = veladeftrait.Toleration{
			Key:               tol.Key,
			Operator:          tol.Operator,
			Value:             tol.Value,
			Effect:            tol.Effect,
			TolerationSeconds: tol.TolerationSeconds,
		}
	}
	return convertedTolerations
}

func convertToLifeCycleHandler(handler *applicationv1.LifecycleHandler) *veladeftrait.LifeCycleHandler {
	if handler == nil {
		return nil
	}
	lifeCycleHandler := &veladeftrait.LifeCycleHandler{}
	if handler.GetExec() != nil {
		lifeCycleHandler.Exec = &veladef.ExecAction{
			Command: handler.Exec.Command,
		}
	}
	if handler.GetHttpGet() != nil {
		var headers []veladef.HTTPHeader
		for _, h := range handler.GetHttpGet().HttpHeaders {
			headers = append(headers, veladef.HTTPHeader{
				Name:  h.Name,
				Value: h.Value,
			})
		}
		lifeCycleHandler.HttpGet = &veladef.HTTPGetAction{
			Path:        handler.HttpGet.Path,
			Port:        handler.HttpGet.Port,
			Host:        handler.HttpGet.Host,
			Scheme:      handler.HttpGet.Scheme.String(),
			HttpHeaders: headers,
		}
	}
	return lifeCycleHandler
}

func ConvertToVelaDefinitionTriggers(tgs *applicationv1.Triggers) veladeftrait.Triggers {
	var triggers veladeftrait.Triggers
	if tgs != nil {
		if tgs.Cpu != nil {
			triggers.CPU = &veladeftrait.CPU{
				Value: fmt.Sprintf("%d", tgs.Cpu.Value),
			}
		}
		if tgs.Memory != nil {
			triggers.Memory = &veladeftrait.Memory{
				Value: fmt.Sprintf("%d", tgs.Cpu.Value),
			}
		}
		if tgs.Cron != nil {
			if tgs.Cron.Timezone != "" && tgs.Cron.Start != "" && tgs.Cron.End != "" && tgs.Cron.DesiredReplicas != 0 {
				triggers.Cron = &veladeftrait.Cron{
					Timezone:        tgs.Cron.Timezone,
					Start:           tgs.Cron.Start,
					End:             tgs.Cron.End,
					DesiredReplicas: strconv.Itoa(int(tgs.Cron.DesiredReplicas)),
				}
			}
		}
		if tgs.Prometheus != nil {
			triggers.Prometheus = &veladeftrait.Prometheus{
				ServerAddress: tgs.Prometheus.ServerAddress,
				Query:         tgs.Prometheus.Query,
				Threshold:     tgs.Prometheus.Threshold,
				MetricName:    tgs.Prometheus.MetricName,
			}
		}
		if tgs.Kafka != nil {
			triggers.Kafka = &veladeftrait.Kafka{
				Topic:             tgs.Kafka.Topic,
				BootstrapServers:  tgs.Kafka.BootstrapServers,
				ConsumerGroup:     tgs.Kafka.ConsumerGroup,
				LagThreshold:      tgs.Kafka.LagThreshold,
				OffsetResetPolicy: tgs.Kafka.OffsetResetPolicy,
			}
		}
		if tgs.MetricApi != nil {
			triggers.MetricAPI = &veladeftrait.MetricAPI{
				TargetValue:           tgs.MetricApi.TargetValue,
				ActivationTargetValue: tgs.MetricApi.ActivationTargetValue,
				URL:                   tgs.MetricApi.Url,
				ValueLocation:         tgs.MetricApi.ValueLocation,
			}
		}
	}
	return triggers
}

func ConvertToVelaDefinitionVolumeMounts(volumeSpecs []*common.VolumeSpec) veladefcomponent.VolumeMounts {
	var pvcs []veladefcomponent.PVCVolume
	var hostPaths []veladefcomponent.HostPathVolume
	pvcMap := make(map[string]veladefcomponent.PVCVolume)
	hostPathMap := make(map[string]veladefcomponent.HostPathVolume)
	// 默认挂载 localtime
	hostPathMap["localtime"] = veladefcomponent.HostPathVolume{
		Name:      "localtime",
		MountPath: "/etc/localtime",
		Path:      "/etc/localtime",
	}
	for _, volumeSpec := range volumeSpecs {
		if volumeSpec.Type == common.VolumeSpec_HostPath {
			hostPath := veladefcomponent.HostPathVolume{}
			hostPath.MountPath = volumeSpec.MountPoint
			subPath := strings.TrimPrefix(volumeSpec.GetHostPathVolumeProperties().SubPath, "/")
			hostPath.SubPath = &subPath
			hostPath.Path = volumeSpec.GetHostPathVolumeProperties().HostPath
			hostPath.Name = "hostpath-" + util.Md5s(volumeSpec.GetHostPathVolumeProperties().HostPath)
			key := hostPath.Name + hostPath.MountPath
			if existing, found := hostPathMap[key]; found {
				hostPathMap[key] = existing
			} else {
				hostPathMap[key] = hostPath
			}
			continue
		}
		pvcVolume := veladefcomponent.PVCVolume{}
		pvcVolume.MountPath = volumeSpec.MountPoint
		switch volumeSpec.Type {
		case common.VolumeSpec_Dataset:
			pvcVolume.Name = volumeSpec.GetDatasetVolumeProperties().DatasetName
			pvcVolume.ClaimName = volumeSpec.GetDatasetVolumeProperties().DatasetName
			pvcVolume.SubPath = &volumeSpec.GetDatasetVolumeProperties().SubPath
		case common.VolumeSpec_CloudFs:
			pvcVolume.Name = volumeSpec.GetCloudFSVolumeProperties().VolumeName
			pvcVolume.ClaimName = volumeSpec.GetCloudFSVolumeProperties().VolumeName
			subPath := strings.TrimPrefix(volumeSpec.GetCloudFSVolumeProperties().SubPath, "/")
			pvcVolume.SubPath = &subPath
		case common.VolumeSpec_CubeFs:
			name := fmt.Sprintf("%s-%s", volumeSpec.GetCubeFSVolumeProperties().VolumeName, "cfs-pvc")
			pvcVolume.Name = name
			pvcVolume.ClaimName = name
			subPath := strings.TrimPrefix(volumeSpec.GetCubeFSVolumeProperties().SubPath, "/")
			pvcVolume.SubPath = &subPath
		}
		key := pvcVolume.Name + pvcVolume.MountPath
		if existing, found := pvcMap[key]; found {
			pvcMap[key] = existing
		} else {
			pvcMap[key] = pvcVolume
		}
	}

	for _, pvc := range pvcMap {
		pvcs = append(pvcs, pvc)
	}
	for _, hostPath := range hostPathMap {
		hostPaths = append(hostPaths, hostPath)
	}
	return veladefcomponent.VolumeMounts{
		PVC:      pvcs,
		HostPath: hostPaths,
	}
}

func ConvertToVelaDefinitionVolumeMountsFromConfigSpecs(configSpecs []*configv1.ConfigSpec) veladefcomponent.VolumeMounts {
	var cms []veladefcomponent.ConfigMapVolume
	var secrets []veladefcomponent.SecretVolume
	for _, configSpec := range configSpecs {
		switch configSpec.ConfigType {
		case model.ConfigTypeConfigMap:
			cms = append(cms, veladefcomponent.ConfigMapVolume{
				Name:      configSpec.ConfigName,
				MountPath: configSpec.MountPoint,
				SubPath:   &configSpec.SubPath,
				CmName:    configSpec.ConfigName,
			})
		case model.ConfigTypeSecret:
			secrets = append(secrets, veladefcomponent.SecretVolume{
				Name:       configSpec.ConfigName,
				MountPath:  configSpec.MountPoint,
				SubPath:    &configSpec.SubPath,
				SecretName: configSpec.ConfigName,
			})
		}
	}
	return veladefcomponent.VolumeMounts{
		ConfigMap: cms,
		Secret:    secrets,
	}
}

func MergeVolumeMounts(vm1, vm2 veladefcomponent.VolumeMounts) veladefcomponent.VolumeMounts {
	return veladefcomponent.VolumeMounts{
		PVC:       append(vm1.PVC, vm2.PVC...),
		HostPath:  append(vm1.HostPath, vm2.HostPath...),
		ConfigMap: append(vm1.ConfigMap, vm2.ConfigMap...),
		Secret:    append(vm1.Secret, vm2.Secret...),
	}
}

func ConvertToVelaDefinitionEnvs(envVars []*common.EnvVar) []veladefcomponent.EnvVar {
	envMap := make(map[string]string)
	for _, envVar := range envVars {
		envMap[envVar.Name] = envVar.Value
	}
	var velaEnvs []veladefcomponent.EnvVar
	for name, value := range envMap {
		velaEnvs = append(velaEnvs, veladefcomponent.EnvVar{
			Name:  name,
			Value: value,
		})
	}
	return velaEnvs
}

func ConvertToNotExposeVelaDefinitionPorts(ports []*common.Port) []veladefcomponent.Port {
	var velaPorts []veladefcomponent.Port
	for _, port := range ports {
		velaPorts = append(velaPorts, veladefcomponent.Port{
			Name:     port.PortName,
			Port:     port.Port,
			Expose:   false,
			Protocol: port.Protocol,
		})
	}
	return velaPorts
}

func ConvertToVelaDefinitionPorts(ports []*common.Port) []veladefcomponent.Port {
	portMap := make(map[string]veladefcomponent.Port)
	for _, port := range ports {
		if _, exists := portMap[port.PortName]; !exists {
			portMap[port.PortName] = veladefcomponent.Port{
				Name:     port.PortName,
				Port:     port.Port,
				Expose:   true,
				Protocol: port.Protocol,
			}
		}
	}
	var velaPorts []veladefcomponent.Port
	for _, vPort := range portMap {
		velaPorts = append(velaPorts, vPort)
	}
	return velaPorts
}

func ConvertToServicePorts(ports []*common.Port) []corev1.ServicePort {
	var servicePorts []corev1.ServicePort
	for _, port := range ports {
		servicePorts = append(servicePorts, corev1.ServicePort{
			Name:       port.PortName,
			Port:       port.Port,
			TargetPort: intstr.FromInt(int(port.Port)),
			Protocol:   corev1.Protocol(port.Protocol),
		})
	}
	return servicePorts
}

func ConvertToVelaDefinitionProbe(probe *applicationv1.Probe) *veladefcomponent.HealthProbe {
	if probe == nil {
		return nil
	}
	if probe.Handler == nil {
		return nil
	}
	var coreProbe = &veladefcomponent.HealthProbe{
		InitialDelaySeconds: probe.InitialDelaySeconds,
		TimeoutSeconds:      probe.TimeoutSeconds,
		PeriodSeconds:       probe.PeriodSeconds,
		SuccessThreshold:    probe.SuccessThreshold,
		FailureThreshold:    probe.FailureThreshold,
	}

	if probe.Handler.Exec != nil {
		coreProbe.Exec = &veladef.ExecAction{
			Command: probe.Handler.Exec.Command,
		}
	}
	if probe.Handler.HttpGet != nil {
		coreProbe.HttpGet = ConvertToVelaHTTPGetAction(probe.Handler.HttpGet)
	}
	if probe.Handler.TcpSocket != nil {
		coreProbe.TcpSocket = &veladef.TCPSocketAction{
			Port: probe.Handler.TcpSocket.Port,
		}
	}
	// TODO add grpc probe
	return coreProbe
}

func ConvertToVelaHTTPGetAction(httpGet *applicationv1.HTTPGetAction) *veladef.HTTPGetAction {
	if httpGet == nil {
		return nil
	}
	var headers []veladef.HTTPHeader
	for _, h := range httpGet.HttpHeaders {
		headers = append(headers, veladef.HTTPHeader{
			Name:  h.Name,
			Value: h.Value,
		})
	}
	return &veladef.HTTPGetAction{
		Path:        httpGet.Path,
		Port:        httpGet.Port,
		Host:        httpGet.Host,
		Scheme:      httpGet.Scheme.String(),
		HttpHeaders: headers,
	}
}

func UpsertTrait(traits []*velav1.CreateApplicationTraitRequest, targetType string, targetProperties *velav1.JSONStruct) ([]*velav1.CreateApplicationTraitRequest, error) {
	for index, trait := range traits {
		properties, err := velav1.NewJSONStructByString(trait.Properties)
		if err != nil {
			return traits, err
		}
		if trait.Type == targetType {
			newProperties := velav1.MergeJSONStructs(properties, targetProperties)
			traits[index].Properties = newProperties.JSON()
			return traits, nil
		}
	}
	traits = append(traits, &velav1.CreateApplicationTraitRequest{
		Type:       targetType,
		Properties: targetProperties.JSON(),
	})
	return traits, nil
}

func RenderInitContainerTrait(ctx context.Context, model *applicationv1.ModelTemplate) (string, string, error) {
	// 兼容前端
	modelName := model.ModelName
	modelWorkspace := model.ModelWorkspace
	if strings.Contains(modelName, "/") {
		modelSplits := strings.SplitN(modelName, "/", 2)
		if len(modelSplits) == 2 {
			modelWorkspace = modelSplits[0]
			modelName = modelSplits[1]
		}
	}
	modelDownloader := fmt.Sprintf("/app/download_model.sh %s %s %s", modelWorkspace, modelName, model.ModelVersion)
	velaDefParam := veladeftrait.InitContainerParameter{
		Name:            "model-downloader",
		Image:           "harbor.intra.ke.com/kcs/kic/aictl:v1.0.5",
		ImagePullPolicy: "Always",
		Cmd: []string{
			"sh",
			"-c",
			modelDownloader,
		},
		InitMountPath: "/mnt/models",
		AppMountPath:  "/mnt/models",
		MountName:     "kic-model",
		Env: []veladeftrait.EnvironmentVar{
			{
				Name: "AISTUDIO_USERNAME",
				ValueFrom: &veladeftrait.ValueFrom{
					SecretKeyRef: &veladeftrait.KeyRef{
						Name: "aistudio-robot-secret",
						Key:  "account",
					},
				},
			},
			{
				Name: "AISTUDIO_PASSWORD",
				ValueFrom: &veladeftrait.ValueFrom{
					SecretKeyRef: &veladeftrait.KeyRef{
						Name: "aistudio-robot-secret",
						Key:  "password",
					},
				},
			},
			{
				Name:  "AISTUDIO_ENV",
				Value: ctx.Value(constant.ApplicationEnvCtxKey).(string),
			},
		},
	}
	properties, err := velav1.NewJSONStructByStruct(velaDefParam)
	if err != nil {
		return "", "", err
	}
	return properties.JSON(), modelName, nil
}

func RenderWarmupVolumeSpec(model *applicationv1.ModelTemplate) *common.VolumeSpec {
	modelName := model.ModelName
	modelWorkspace := model.ModelWorkspace
	if strings.Contains(modelName, "/") {
		modelSplits := strings.SplitN(modelName, "/", 2)
		if len(modelSplits) == 2 {
			modelWorkspace = modelSplits[0]
			modelName = modelSplits[1]
		}
	}
	return &common.VolumeSpec{
		Type:       common.VolumeSpec_HostPath,
		MountPoint: fmt.Sprintf("/mnt/models/%s", modelName),
		VolumeProperties: &common.VolumeSpec_HostPathVolumeProperties{
			HostPathVolumeProperties: &common.HostPathVolumeProperties{
				HostPath: fmt.Sprintf("/data/models-cfs-readonly/aistudio/%s/", modelWorkspace),
				SubPath:  modelName,
			},
		},
	}
}

func AppendNodeSelector(component *applicationv1.ComponentSpec, selector *applicationv1.NodeSelectorRequirement) {
	if component.Traits != nil {
		affinityTrait := component.Traits.Affinity
		if affinityTrait != nil && affinityTrait.NodeAffinity != nil && affinityTrait.NodeAffinity.Required != nil && len(affinityTrait.NodeAffinity.Required) > 0 {
			affinityTrait.NodeAffinity.Required = append(affinityTrait.NodeAffinity.Required, selector)
		} else {
			affinityTrait = &applicationv1.AffinityTrait{
				NodeAffinity: &applicationv1.NodeAffinity{
					Required: []*applicationv1.NodeSelectorRequirement{selector},
				},
			}
		}
		component.Traits.Affinity = affinityTrait
	}
}
