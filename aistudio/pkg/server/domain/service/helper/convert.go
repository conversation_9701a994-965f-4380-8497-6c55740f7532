package helper

import (
	"fmt"
	"strings"

	"git.lianjia.com/cloudnative/kic/kic-platform/aistudio/pkg/server/domain/model"
	"git.lianjia.com/cloudnative/kic/kic-platform/apis/common"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/util"
	"github.com/jinzhu/copier"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	volcanobatchv1alpha1 "volcano.sh/apis/pkg/apis/batch/v1alpha1"
)

// EnvEntityToEnvVars convert env entity to env vars
func EnvEntityToEnvVars(envModel []*model.EnvVar) []*common.EnvVar {
	envVars := make([]*common.EnvVar, 0)
	if envModel != nil {
		for _, env := range envModel {
			e := &common.EnvVar{
				Name:  env.Name,
				Value: env.Value,
			}
			envVars = append(envVars, e)
		}
	}
	return envVars
}

// EnvVarsToEnvEntity convert api env vars to env entity
func EnvVarsToEnvEntity(envVars []*common.EnvVar) []*model.EnvVar {
	if envVars == nil {
		return make([]*model.EnvVar, 0)
	}
	//添加去重
	envMap := map[string]struct{}{}
	envVarsModel := make([]*model.EnvVar, 0)
	for _, env := range envVars {
		if _, ok := envMap[env.Name]; !ok {
			e := &model.EnvVar{
				Name:  env.Name,
				Value: env.Value,
			}
			envVarsModel = append(envVarsModel, e)
			envMap[env.Name] = struct{}{}
		}
	}
	return envVarsModel
}

func SpecificationToEntity(spec *common.Specification) *model.Specification {
	entity := &model.Specification{
		CpuNum:    spec.CpuNum,
		MemoryGiB: spec.MemoryGiB,
		GpuNum:    spec.GpuNum,
		GpuMem:    spec.GpuMem,
	}
	return entity
}

// VolumeSpecToEntity convert api volume spec to volume entity
func VolumeSpecToEntity(volumeSpecs []*common.VolumeSpec) []*model.VolumeSpecEntity {
	var volumeSpecsEntities []*model.VolumeSpecEntity
	//按照mountPoint去重
	mountPointMap := map[string]struct{}{}
	if volumeSpecs != nil {
		for _, spec := range volumeSpecs {
			entity := &model.VolumeSpecEntity{}
			if err := copier.Copy(entity, spec); err != nil {
				return nil
			}
			entity.Type = spec.Type.String()
			if _, ok := mountPointMap[entity.MountPoint]; !ok {
				volumeSpecsEntities = append(volumeSpecsEntities, entity)
				mountPointMap[entity.MountPoint] = struct{}{}
			}
		}
	}
	return volumeSpecsEntities
}

// VolumeEntityToVolumeSpecs convert model volume entity to api volume spec
func VolumeEntityToVolumeSpecs(volumeEntities []*model.VolumeSpecEntity) []*common.VolumeSpec {
	var volumeSpecs []*common.VolumeSpec
	if volumeEntities != nil {
		for _, entity := range volumeEntities {
			spec := &common.VolumeSpec{}
			spec.Type = common.VolumeSpec_VolumeType(common.VolumeSpec_VolumeType_value[entity.Type])
			spec.MountPoint = entity.MountPoint
			switch entity.Type {
			case common.VolumeSpec_CloudFs.String():
				spec.VolumeProperties = &common.VolumeSpec_CloudFSVolumeProperties{
					CloudFSVolumeProperties: &common.CloudFSVolumeProperties{
						VolumeName: entity.CloudFSVolumeProperties.VolumeName,
						SubPath:    entity.CloudFSVolumeProperties.SubPath,
					},
				}
			case common.VolumeSpec_CubeFs.String():
				spec.VolumeProperties = &common.VolumeSpec_CubeFSVolumeProperties{
					CubeFSVolumeProperties: &common.CubeFSVolumeProperties{
						VolumeName: entity.CubeFSVolumeProperties.VolumeName,
						SubPath:    entity.CubeFSVolumeProperties.SubPath,
					},
				}
			case common.VolumeSpec_Dataset.String():
				spec.VolumeProperties = &common.VolumeSpec_DatasetVolumeProperties{
					DatasetVolumeProperties: &common.DatasetVolumeProperties{
						DatasetName: entity.DatasetVolumeProperties.DatasetName,
						SubPath:     entity.DatasetVolumeProperties.SubPath,
					},
				}
			case common.VolumeSpec_HostPath.String():
				spec.VolumeProperties = &common.VolumeSpec_HostPathVolumeProperties{
					HostPathVolumeProperties: &common.HostPathVolumeProperties{
						HostPath: entity.HostPathVolumeProperties.HostPath,
						SubPath:  entity.HostPathVolumeProperties.SubPath,
					},
				}
			}
			volumeSpecs = append(volumeSpecs, spec)
		}
	}
	return volumeSpecs
}

func ConvertToCoreVolumeMounts(volumeSpecs []*model.VolumeSpecEntity) []corev1.VolumeMount {
	var volumeMounts []corev1.VolumeMount
	for _, volumeSpec := range volumeSpecs {
		volumeMount := corev1.VolumeMount{}
		volumeMount.MountPath = volumeSpec.MountPoint
		switch volumeSpec.Type {
		case common.VolumeSpec_Dataset.String():
			volumeMount.Name = volumeSpec.DatasetVolumeProperties.DatasetName
			subPath := strings.TrimPrefix(volumeSpec.DatasetVolumeProperties.SubPath, "/")
			volumeMount.SubPath = subPath
		case common.VolumeSpec_CloudFs.String():
			volumeMount.Name = volumeSpec.CloudFSVolumeProperties.VolumeName
			volumeMount.SubPath = strings.TrimPrefix(volumeSpec.CloudFSVolumeProperties.SubPath, "/")
		case common.VolumeSpec_CubeFs.String():
			name := fmt.Sprintf("%s-%s", volumeSpec.CubeFSVolumeProperties.VolumeName, "cfs-pvc")
			volumeMount.Name = name
			volumeMount.SubPath = strings.TrimPrefix(volumeSpec.CubeFSVolumeProperties.SubPath, "/")
		case common.VolumeSpec_HostPath.String():
			volumeMount.Name = "hostpath-" + util.Md5s(volumeSpec.HostPathVolumeProperties.HostPath)
			volumeMount.SubPath = strings.TrimPrefix(volumeSpec.HostPathVolumeProperties.SubPath, "/")
		}
		volumeMounts = append(volumeMounts, volumeMount)
	}
	return volumeMounts
}

// ConvertToCoreVolumeMountsFromAPIVolumes convert api volume to core volume mounts
func ConvertToCoreVolumeMountsFromAPIVolumes(volumeSpecs []*common.VolumeSpec) []corev1.VolumeMount {
	var volumeMounts []corev1.VolumeMount
	for _, volumeSpec := range volumeSpecs {
		volumeMount := corev1.VolumeMount{}
		volumeMount.MountPath = volumeSpec.MountPoint
		switch volumeSpec.Type {
		case common.VolumeSpec_Dataset:
			volumeMount.Name = volumeSpec.GetDatasetVolumeProperties().DatasetName
		case common.VolumeSpec_CloudFs:
			volumeMount.Name = volumeSpec.GetCloudFSVolumeProperties().VolumeName
		case common.VolumeSpec_CubeFs:
			name := fmt.Sprintf("%s-%s", volumeSpec.GetCubeFSVolumeProperties().GetVolumeName(), "cfs-pvc")
			volumeMount.Name = name
		}
		volumeMounts = append(volumeMounts, volumeMount)
	}
	return volumeMounts
}

func ConvertToVolcanoVolume(volumeSpecs []*model.VolumeSpecEntity) []volcanobatchv1alpha1.VolumeSpec {
	var volumes []volcanobatchv1alpha1.VolumeSpec
	for _, vs := range volumeSpecs {
		volume := volcanobatchv1alpha1.VolumeSpec{}
		volume.MountPath = vs.MountPoint
		switch vs.Type {
		case common.VolumeSpec_Dataset.String():
			volume.VolumeClaimName = vs.DatasetVolumeProperties.DatasetName
		case common.VolumeSpec_CloudFs.String():
			volume.VolumeClaimName = vs.CloudFSVolumeProperties.VolumeName
		case common.VolumeSpec_CubeFs.String():
			pvcName := fmt.Sprintf("%s-%s", vs.CubeFSVolumeProperties.VolumeName, "cfs-pvc")
			volume.VolumeClaimName = pvcName
		}
		volumes = append(volumes, volume)
	}
	return volumes
}

func ConvertToCoreVolume(volumeSpecs []*model.VolumeSpecEntity, tensorboardEnabled bool) []corev1.Volume {
	var volumes []corev1.Volume
	for _, vs := range volumeSpecs {
		volume := &corev1.Volume{}
		switch vs.Type {
		case common.VolumeSpec_CubeFs.String():
			claimName := fmt.Sprintf("%s-%s", vs.CubeFSVolumeProperties.VolumeName, "cfs-pvc")
			volume.VolumeSource.PersistentVolumeClaim = &corev1.PersistentVolumeClaimVolumeSource{
				ClaimName: claimName,
			}
			volume.Name = claimName
		case common.VolumeSpec_CloudFs.String():
			volume.VolumeSource.PersistentVolumeClaim = &corev1.PersistentVolumeClaimVolumeSource{
				ClaimName: vs.CloudFSVolumeProperties.VolumeName,
			}
			volume.Name = vs.CloudFSVolumeProperties.VolumeName
		case common.VolumeSpec_Dataset.String():
			volume.VolumeSource.PersistentVolumeClaim = &corev1.PersistentVolumeClaimVolumeSource{
				ClaimName: vs.DatasetVolumeProperties.DatasetName,
			}
			volume.Name = vs.DatasetVolumeProperties.DatasetName
		case common.VolumeSpec_HostPath.String():
			volume.VolumeSource.HostPath = &corev1.HostPathVolumeSource{
				Path: vs.HostPathVolumeProperties.HostPath,
			}
			volume.Name = "hostpath-" + util.Md5s(vs.HostPathVolumeProperties.HostPath)
		}

		volumes = append(volumes, *volume)
	}
	if tensorboardEnabled {
		volumes = append(volumes, corev1.Volume{
			Name: "tensorboard",
			VolumeSource: corev1.VolumeSource{
				PersistentVolumeClaim: &corev1.PersistentVolumeClaimVolumeSource{
					ClaimName: "tensorboard-cfs-pvc",
				},
			},
		})
	}
	return volumes
}

func CovertToCoreEnvFromEnvVar(envVar []*model.EnvVar, tensorboardEnabled bool) []corev1.EnvVar {
	var envs []corev1.EnvVar
	for _, env := range envVar {
		envs = append(envs, corev1.EnvVar{
			Name:  env.Name,
			Value: env.Value,
		})
	}
	if tensorboardEnabled {
		envs = append(envs, corev1.EnvVar{
			Name:  "TENSORBOARD_LOG_PATH",
			Value: "/opt/tensorboard/logs",
		})
	}
	return envs
}
func GenerateHadoopEnvVars(hadoopUsers []string) []corev1.EnvVar {
	envs := []corev1.EnvVar{
		{
			Name:  "HADOOP_USERS",
			Value: strings.Join(hadoopUsers, ","),
		},
	}
	return envs
}

func CovertToCoreEnvFromAPIEnvVar(envVar []*common.EnvVar) []corev1.EnvVar {
	var envs []corev1.EnvVar
	for _, env := range envVar {
		envs = append(envs, corev1.EnvVar{
			Name:  env.Name,
			Value: env.Value,
		})
	}
	return envs
}

func mergeVolumes(globalVolumes, volumes []*model.VolumeSpecEntity) []*model.VolumeSpecEntity {
	if globalVolumes == nil {
		return volumes
	}
	if volumes == nil {
		return globalVolumes
	}

	volumeMap := make(map[string]*model.VolumeSpecEntity)
	for _, volume := range globalVolumes {
		switch volume.Type {
		case common.VolumeSpec_CloudFs.String():
			volumeMap[fmt.Sprintf("%s-%s", volume.Type, volume.CloudFSVolumeProperties.VolumeName)] = volume
		case common.VolumeSpec_CubeFs.String():
			volumeMap[fmt.Sprintf("%s-%s", volume.Type, volume.CubeFSVolumeProperties.VolumeName)] = volume
		case common.VolumeSpec_Dataset.String():
			volumeMap[fmt.Sprintf("%s-%s", volume.Type, volume.DatasetVolumeProperties.DatasetName)] = volume
		}
	}
	for _, volume := range volumes {
		switch volume.Type {
		case common.VolumeSpec_CloudFs.String():
			volumeMap[fmt.Sprintf("%s-%s", volume.Type, volume.CloudFSVolumeProperties.VolumeName)] = volume
		case common.VolumeSpec_CubeFs.String():
			volumeMap[fmt.Sprintf("%s-%s", volume.Type, volume.CubeFSVolumeProperties.VolumeName)] = volume
		case common.VolumeSpec_Dataset.String():
			volumeMap[fmt.Sprintf("%s-%s", volume.Type, volume.DatasetVolumeProperties.DatasetName)] = volume
		}
	}
	var result []*model.VolumeSpecEntity
	for _, volume := range volumeMap {
		result = append(result, volume)
	}
	return result
}

func MergePorts(ports1 []*common.Port, post2 []*common.Port) []*common.Port {
	var portMap = make(map[string]*common.Port)
	for _, port := range ports1 {
		key := fmt.Sprintf("%s-%d", port.PortName, port.Port)
		portMap[key] = port
	}
	for _, port := range post2 {
		key := fmt.Sprintf("%s-%d", port.PortName, port.Port)
		portMap[key] = port
	}
	var result []*common.Port
	for _, port := range portMap {
		result = append(result, port)
	}
	return result
}

func MergeEnvVars(baseEnv, mainEnv []*common.EnvVar) []*common.EnvVar {
	mergedEnvMap := make(map[string]*common.EnvVar)
	for _, env := range baseEnv {
		mergedEnvMap[env.Name] = env
	}
	for _, env := range mainEnv {
		mergedEnvMap[env.Name] = env
	}
	var mergedEnv []*common.EnvVar
	for _, env := range mergedEnvMap {
		mergedEnv = append(mergedEnv, env)
	}
	return mergedEnv
}

// GetHadoopVolumeName 获取Hadoop用户对应的volume名称
func GetHadoopVolumeName(hadoopUser string) string {
	hadoopUser = strings.ReplaceAll(hadoopUser, "_", "-")
	return fmt.Sprintf("%s-%s", VolumeNameKeytab, hadoopUser)
}
func GenerateHadoopClientInitContainer(initImage string) []corev1.Container {
	cmdArgs := []string{"cp -r /home/<USER>/. /client-volume"}
	volumeMounts := []corev1.VolumeMount{
		{
			Name:      VolumeNameHadoopClient,
			MountPath: "/client-volume",
		},
	}
	container := corev1.Container{
		Name:  "job-hadoop-client",
		Image: initImage,
		Command: []string{
			"/bin/sh",
			"-c",
		},
		Args: []string{
			strings.Join(cmdArgs, ";"),
		},
		Resources: corev1.ResourceRequirements{
			Limits: corev1.ResourceList{
				corev1.ResourceCPU:    resource.MustParse("100m"),
				corev1.ResourceMemory: resource.MustParse("256Mi"),
			},
			Requests: corev1.ResourceList{
				corev1.ResourceCPU:    resource.MustParse("100m"),
				corev1.ResourceMemory: resource.MustParse("256Mi"),
			},
		},
		VolumeMounts:             volumeMounts,
		TerminationMessagePath:   "/dev/termination-log",
		TerminationMessagePolicy: corev1.TerminationMessageReadFile,
		ImagePullPolicy:          corev1.PullAlways,
	}
	return []corev1.Container{container}
}

const (
	VolumeNameHadoopClient = "volume-hadoop-client"
	VolumeNameKeytab       = "volume-keytab"
)

func GenerateHadoopVolumeMounts(hadoopUsers []string) []corev1.VolumeMount {
	volumeMounts := []corev1.VolumeMount{
		{
			Name:      VolumeNameHadoopClient,
			MountPath: "/home/<USER>",
		},
	}
	for _, hadoopUser := range hadoopUsers {
		volumeMounts = append(volumeMounts, corev1.VolumeMount{
			Name:      GetHadoopVolumeName(hadoopUser),
			MountPath: fmt.Sprintf("/etc/keytab-volume/%s", hadoopUser),
		})
	}
	return volumeMounts
}

func GenerateHadoopVolume(hadoopUsers []string) []corev1.Volume {
	quantity10Gi := resource.MustParse("10Gi")
	volumes := []corev1.Volume{
		{
			Name: VolumeNameHadoopClient,
			VolumeSource: corev1.VolumeSource{
				EmptyDir: &corev1.EmptyDirVolumeSource{
					SizeLimit: &quantity10Gi,
				},
			},
		},
	}
	for _, hadoopUser := range hadoopUsers {
		volumes = append(volumes, corev1.Volume{
			Name: GetHadoopVolumeName(hadoopUser),
			VolumeSource: corev1.VolumeSource{
				Secret: &corev1.SecretVolumeSource{
					SecretName:  GetHadoopSecretName(hadoopUser),
					DefaultMode: util.Int32Ptr(0600),
				},
			},
		})
	}
	return volumes
}

// GetHadoopSecretName 获取Hadoop用户对应的keytab文件名
func GetHadoopSecretName(hadoopUser string) string {
	return strings.ReplaceAll(hadoopUser, "_", "-")
}
