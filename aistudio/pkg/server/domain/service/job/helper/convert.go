package helper

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	temporalSdkClient "go.temporal.io/sdk/client"

	"git.lianjia.com/cloudnative/kic/kic-platform/aistudio/pkg/constant"
	"git.lianjia.com/cloudnative/kic/kic-platform/aistudio/pkg/server/domain/model"

	servicehelper "git.lianjia.com/cloudnative/kic/kic-platform/aistudio/pkg/server/domain/service/helper"
	"git.lianjia.com/cloudnative/kic/kic-platform/apis/aistudio/bcode"
	jobv1 "git.lianjia.com/cloudnative/kic/kic-platform/apis/aistudio/job/v1"
	"git.lianjia.com/cloudnative/kic/kic-platform/apis/common"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/property"
	vela "git.lianjia.com/cloudnative/kic/kic-platform/pkg/third_party/velaux"
	velav1 "git.lianjia.com/cloudnative/kic/kic-platform/pkg/third_party/velaux/apis/v1"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/util"
	"go.mongodb.org/mongo-driver/bson/primitive"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/wait"
	"k8s.io/klog/v2"
	volcanobatchv1alpha1 "volcano.sh/apis/pkg/apis/batch/v1alpha1"
	volcanobusv1alpha1 "volcano.sh/apis/pkg/apis/bus/v1alpha1"
)

// the Params of renderDefaultTaskSpec function
type renderTaskSpecParams struct {
	jobName            string
	command            string
	taskName           string
	taskSpec           *model.TaskSpec
	labels             map[string]string
	annotations        map[string]string
	tensorboardEnabled bool
	isDistributed      bool
	hadoopEnabled      bool
	hadoopUsers        []string
	initImage          string
}

// TODO 兼容,运行一段时间可下掉
func GetVelaProjectNameFromJob(jobEntity *model.JobEntity) string {
	if jobEntity.VelaProjectName == "" {
		return constant.AIStudioPrefix + jobEntity.WorkspaceName
	}
	return jobEntity.VelaProjectName
}
func GetVelaEnvNameFromJob(jobEntity *model.JobEntity) string {
	if jobEntity.VelaDefaultEnvName == "" {
		return constant.AIStudioPrefix + jobEntity.WorkspaceName + "-dev"
	}
	return jobEntity.VelaDefaultEnvName
}

func CreateVolcanoTrainingJobWithApplication(ctx context.Context, clusterName string, jobEntity *model.JobEntity, jobStatusEntity *model.JobStatusEntity) error {
	workspaceName := jobEntity.WorkspaceName
	jobName := jobEntity.Name
	var err error
	projectName := GetVelaProjectNameFromJob(jobEntity)
	envName := GetVelaEnvNameFromJob(jobEntity)
	// Step 2: Create Vela application and policies
	description := fmt.Sprintf("任务名称:%s ,别名: %s 工作空间: %s , 任务类型:%s , 创建人:%s , 队列:%s, 描述:%s",
		jobName, jobEntity.DisplayName, workspaceName, jobEntity.JobType, jobEntity.Creator, jobEntity.QueueName,
		jobEntity.Description,
	)
	velaApp := &velav1.CreateApplicationRequest{
		Name:        jobName,
		Description: description,
		Labels:      jobEntity.Labels,
		Project:     projectName,
		EnvBinding:  []*velav1.EnvBinding{{Name: envName}},
	}

	applicationStatus, err := vela.Instance().GetApplicationStatus(ctx, velaApp.Name, envName)
	if err != nil {
		if errors.Is(err, vela.ErrorApplicationNotFound) {
			klog.Infof("application[%s] not found, will create it", velaApp.Name)
		} else {
			return err
		}
	} else {
		klog.Infof("application already exists, ingore create: %v", applicationStatus)
		//如果app存在，重启
		if applicationStatus.Status != nil && (applicationStatus.Status.Phase == "running" || applicationStatus.Status.Phase == "workflowFailed") {
			err = vela.Instance().RecycleApplicationEnv(ctx, jobName, envName)
			if err != nil {
				return err
			}
			// 等待vela回收完成
			err = wait.PollImmediateWithContext(ctx, 3*time.Second, 60*time.Second, func(ctx context.Context) (done bool, err error) {
				appStatus, err := vela.Instance().GetApplicationStatus(ctx, jobName, envName)
				if err != nil {
					return false, err
				}
				if appStatus.Status != nil {
					klog.Infof("wait vela env recycle,appName:%s,envName:%s", jobName, envName)
					return false, nil
				}
				klog.Infof("appStatus:%v", appStatus)
				return true, nil
			})
			if err != nil {
				return bcode.ErrorVelaInternalError("wait vela env recycle failed,err:%v", err)
			}

			_, err = vela.Instance().DeployApplication(ctx, projectName, jobName, &velav1.ApplicationDeployRequest{TriggerType: constant.VelaWorkflowTriggerType})
			if err != nil {
				return err
			}
		} else {
			klog.Infof("application is not running or workflowFailed, can not redeploy: %v", applicationStatus)
		}
		return nil
	}
	if _, err = vela.Instance().CreateApplication(ctx, projectName, velaApp); err != nil {
		return err
	}

	if err = servicehelper.CreateDefaultPolicy(ctx, jobName); err != nil {
		return err
	}
	if err = servicehelper.CreateTopologyPolicy(ctx, jobName, workspaceName, clusterName); err != nil {
		return err
	}

	// Step 3: Create workflow
	defaultW := true
	workflow := &velav1.CreateWorkflowRequest{
		Name: "deploy",
		Steps: []velav1.WorkflowStep{{
			WorkflowStepBase: velav1.WorkflowStepBase{
				Name:       jobName,
				Alias:      fmt.Sprintf("Deploy %s", jobName),
				Type:       "deploy",
				Properties: map[string]interface{}{"policies": []string{jobName}},
			},
		}},
		Default: &defaultW,
		EnvName: envName,
		SubMode: "DAG",
		Mode:    "StepByStep",
	}
	if _, err = vela.Instance().CreateOrUpdateApplicationWorkflow(ctx, jobName, workflow); err != nil {
		return err
	}

	objs := []interface{}{renderVolcanoJobObject(jobEntity, jobStatusEntity)}
	var totalReplicas int32
	if jobEntity.JobType == jobv1.JobType_DeepSpeed.String() {
		totalReplicas += jobEntity.DeepSpeedJobTemplate.Worker.Replicas
		totalReplicas += 1 // 增加一个master节点
		objs = append(objs, renderConfigMapObj(workspaceName, totalReplicas, jobName, jobEntity.DeepSpeedJobTemplate.Master.Slots))
	}
	if jobEntity.JobType == jobv1.JobType_PyTorchDDP.String() {
		totalReplicas += jobEntity.PytorchJobTemplate.Worker.Replicas
		totalReplicas += 1 // 增加一个master节点
		objs = append(objs, renderConfigMapObj(workspaceName, totalReplicas, jobName, jobEntity.PytorchJobTemplate.Master.Slots))
	}
	velaPropertiesObj := map[string]interface{}{"objects": objs}
	velaProperties, err := velav1.NewJSONStructByStruct(velaPropertiesObj)
	if err != nil {
		return fmt.Errorf("convert to JSON string failed, err: %v", err)
	}
	componentReq := &velav1.CreateComponentRequest{
		Name:          jobName,
		Description:   jobEntity.Description,
		ComponentType: "k8s-objects",
		Properties:    velaProperties.JSON(),
	}

	if _, err = vela.Instance().CreateComponent(ctx, jobName, componentReq); err != nil {
		return err
	}

	// Step 5: Deploy Vela application
	if _, err = vela.Instance().DeployApplication(ctx, projectName, jobName, &velav1.ApplicationDeployRequest{TriggerType: constant.VelaWorkflowTriggerType, WorkflowName: "deploy"}); err != nil {
		return err
	}
	return nil
}

// GenerateHosts 主要是生成多机多卡的host文件
func GenerateHosts(replicas int32, jobName string, slots int32) map[string]string {
	hostFile := make(map[string]string, replicas)
	hosts := make([]string, 0, replicas)
	for i := 0; i < int(replicas); i++ {
		if i == 0 {
			// master address
			host := MakePodName(jobName, "master", i) + "." + jobName
			hostFile[ConfigMapMasterHostFmt] = host

			value := fmt.Sprintf("%s slots=%d", host, slots)
			hosts = append(hosts, value)
			continue
		}
		host := MakePodName(jobName, "worker", i-1) + "." + jobName
		value := fmt.Sprintf("%s slots=%d", host, slots)
		hosts = append(hosts, value)
	}
	hostFile[ConfigMapHostFileFmt] = strings.Join(hosts, "\n")
	return hostFile
}

func MakePodName(jobName string, taskName string, index int) string {
	return fmt.Sprintf("%s-%s-%d", jobName, taskName, index)
}

func ConvertFromJobPytorchDDPModel(jobModel *model.PytorchJobTemplate) *jobv1.PyTorchDDPJobTemplate {
	pytorchJobTemplate := &jobv1.PyTorchDDPJobTemplate{}
	if jobModel.SourceCode != nil {
		pytorchJobTemplate.SourceCode = ConvertFromSourceCodeModel(jobModel.SourceCode)
	}
	pytorchJobTemplate.TensorboardEnabled = jobModel.TensorboardEnabled
	pytorchJobTemplate.Command = jobModel.Command
	pytorchJobTemplate.Image = jobModel.Image
	if jobModel.EnvVars != nil {
		pytorchJobTemplate.EnvVars = servicehelper.EnvEntityToEnvVars(jobModel.EnvVars)
	}
	if jobModel.VolumeSpecs != nil {
		pytorchJobTemplate.VolumeSpecs = servicehelper.VolumeEntityToVolumeSpecs(jobModel.VolumeSpecs)
	}
	pytorchJobTemplate.Master = ConvertFromTaskSpecModel(jobModel.Master)
	pytorchJobTemplate.Worker = ConvertFromTaskSpecModel(jobModel.Worker)
	pytorchJobTemplate.ConfigSpecs = model.ConvertToConfigSpec(jobModel.ConfigSpecs)
	return pytorchJobTemplate
}

func ConvertFromJobDeepSpeedModel(jobModel *model.DeepSpeedJobTemplate) *jobv1.DeepSpeedJobTemplate {
	deepSpeedJobTemplate := &jobv1.DeepSpeedJobTemplate{}
	if jobModel.SourceCode != nil {
		deepSpeedJobTemplate.SourceCode = ConvertFromSourceCodeModel(jobModel.SourceCode)
	}
	deepSpeedJobTemplate.TensorboardEnabled = jobModel.TensorboardEnabled
	deepSpeedJobTemplate.Command = jobModel.Command
	deepSpeedJobTemplate.Image = jobModel.Image

	if jobModel.EnvVars != nil {
		deepSpeedJobTemplate.EnvVars = servicehelper.EnvEntityToEnvVars(jobModel.EnvVars)
	}
	if jobModel.VolumeSpecs != nil {
		deepSpeedJobTemplate.VolumeSpecs = servicehelper.VolumeEntityToVolumeSpecs(jobModel.VolumeSpecs)
	}
	deepSpeedJobTemplate.Master = ConvertFromTaskSpecModel(jobModel.Master)
	deepSpeedJobTemplate.Worker = ConvertFromTaskSpecModel(jobModel.Worker)
	deepSpeedJobTemplate.ConfigSpecs = model.ConvertToConfigSpec(jobModel.ConfigSpecs)
	return deepSpeedJobTemplate
}

func ConvertFromJobSimpleTrainingModel(jobModel *model.SimpleTrainingJobTemplate) *jobv1.SimpleTrainingJobTemplate {
	return ConvertFromSimpleTrainingJobTemplateModel(jobModel)
}

func MakeConfigMapName(jobName string) string {
	return fmt.Sprintf(ConfigMapNameFmt, jobName)
}

func ConvertFromJobModel(jobModel *model.JobEntity) *jobv1.Job {
	result := &jobv1.Job{
		Id:              jobModel.ID.Hex(),
		Name:            jobModel.Name,
		DisplayName:     jobModel.DisplayName,
		WorkspaceName:   jobModel.WorkspaceName,
		Region:          jobModel.Region,
		Creator:         jobModel.Creator,
		Description:     jobModel.Description,
		Members:         jobModel.Members,
		JobType:         jobv1.JobType(jobv1.JobType_value[jobModel.JobType]),
		QueueName:       jobModel.QueueName,
		CreateTime:      util.TimeFormat(jobModel.CreateTime),
		UpdateTime:      util.TimeFormat(jobModel.UpdateTime),
		AlarmShielding:  jobModel.AlarmShielding,
		JobTemplateName: jobModel.JobTemplateName,
	}

	// 兼容已有任务
	var restartPolicy *jobv1.RestartPolicy
	if jobModel.RestartPolicy != nil {
		restartPolicy = &jobv1.RestartPolicy{
			MaxRetryCount: jobModel.RestartPolicy.MaxRetryCount,
			Enabled:       jobModel.RestartPolicy.Enabled,
		}
		result.RestartPolicy = restartPolicy
	}

	switch jobModel.Priority {
	case model.PriorityLow:
		result.Priority = model.PriorityLowInt
	case model.PriorityNormal:
		result.Priority = model.PriorityNormalInt
	case model.PriorityHigh:
		result.Priority = model.PriorityHighInt
	}

	switch jobModel.JobType {
	case jobv1.JobType_DeepSpeed.String():
		result.DeepSpeedJobTemplate = ConvertFromJobDeepSpeedModel(jobModel.DeepSpeedJobTemplate)
	case jobv1.JobType_PyTorchDDP.String():
		result.PyTorchDDPJobTemplate = ConvertFromJobPytorchDDPModel(jobModel.PytorchJobTemplate)
	case jobv1.JobType_SimpleTraining.String():
		result.SimpleTrainingJobTemplate = ConvertFromJobSimpleTrainingModel(jobModel.SimpleTrainingJobTemplate)
	}
	return result
}

func ConvertFromJobTemplateModel(jobModel *model.JobTemplateEntity) *jobv1.JobTemplate {
	result := &jobv1.JobTemplate{
		Name:              jobModel.Name,
		DisplayName:       jobModel.DisplayName,
		WorkspaceName:     jobModel.WorkspaceName,
		Region:            jobModel.Region,
		Creator:           jobModel.Creator,
		Description:       jobModel.Description,
		Managers:          jobModel.Managers,
		Members:           jobModel.Members,
		Job:               ConvertFromJobModel(jobModel.Job),
		ExecutionStrategy: jobv1.ExecutionStrategy(jobv1.ExecutionStrategy_value[jobModel.ExecutionStrategy]),
		CreateTime:        util.TimeFormat(jobModel.CreateTime),
		UpdateTime:        util.TimeFormat(jobModel.UpdateTime),
	}

	return result
}

func ConvertFromTriggerModel(trigger *model.TriggerEntity) *jobv1.Trigger {
	if trigger == nil {
		return nil
	}
	result := &jobv1.Trigger{
		TriggerName:     trigger.TriggerName,
		TriggerType:     jobv1.TriggerType(jobv1.TriggerType_value[trigger.TriggerType]),
		NumActions:      trigger.NumActions,
		NextActionTimes: trigger.NextActionTimes,
		State:           jobv1.TriggerState(jobv1.TriggerState_value[trigger.TriggerState]),
		Message:         trigger.Message,
	}
	switch trigger.TriggerType {
	case jobv1.TriggerType_Timer.String():
		options := trigger.TimerTriggerOptions
		result.TimeTriggerOptions = &jobv1.TimerTriggerOptions{
			Type:             jobv1.TimerTriggerOptions_TimerTriggerType(jobv1.TimerTriggerOptions_TimerTriggerType_value[options.TimerTriggerType]),
			Interval:         options.Interval,
			CronExpr:         options.CronExpr,
			PeriodOfValidity: options.PeriodOfValidity,
			Cycle:            ConvertFromCycleModel(options.Cycle),
			MaxCount:         int32(options.MaxCount),
			SkipTime:         ConvertFromCycleModel(options.SkipTime),
		}

	}
	return result
}

func ConvertFromCycleModel(cycle model.Cycle) *jobv1.Cycle {
	result := &jobv1.Cycle{
		Month:      cycle.Month,
		DayOfMonth: cycle.DayOfMonth,
		DayOfWeek:  cycle.DayOfWeek,
		Hour:       cycle.Hour,
		Minute:     cycle.Minute,
	}
	return result
}

func ConvertToCycleModel(cycle *jobv1.Cycle) model.Cycle {
	result := model.Cycle{
		Month:      cycle.Month,
		DayOfMonth: cycle.DayOfMonth,
		DayOfWeek:  cycle.DayOfWeek,
		Hour:       cycle.Hour,
		Minute:     cycle.Minute,
	}
	return result
}

func ConvertToSourceCodeModel(request *jobv1.SourceCode) *model.SourceCodeEntity {
	if request == nil {
		return nil
	}
	sourceCodeSource := &model.SourceCodeEntity{}
	switch sourceCode := request.Source.(type) {
	case *jobv1.SourceCode_GitSource_:
		sourceCodeSource.Source = model.SourceCodeSource{
			GitSource: &model.GitSource{
				Url:    sourceCode.GitSource.Url,
				Branch: sourceCode.GitSource.Branch,
			},
			Type: model.SourceCodeGitType,
		}
	case *jobv1.SourceCode_CloudFsSource_:
		sourceCodeSource.Source = model.SourceCodeSource{
			CloudFsSource: &model.CloudFsSource{
				VolumeName: sourceCode.CloudFsSource.VolumeName,
				SubPath:    sourceCode.CloudFsSource.SubPath,
			},
			Type: model.SourceCodeCloudFSType,
		}
	case *jobv1.SourceCode_CubeFsSource_:
		sourceCodeSource.Source = model.SourceCodeSource{
			CubeFsSource: &model.CubeFsSource{
				VolumeName: sourceCode.CubeFsSource.VolumeName,
			},
			Type: model.SourceCodeCubeFSType,
		}
	}
	sourceCodeSource.MountPoint = request.MountPoint
	return sourceCodeSource
}

func ConvertFromSourceCodeModel(sourceCodeModel *model.SourceCodeEntity) *jobv1.SourceCode {
	result := &jobv1.SourceCode{}
	switch sourceCodeModel.Source.Type {
	case model.SourceCodeGitType:
		result.Source = &jobv1.SourceCode_GitSource_{
			GitSource: &jobv1.SourceCode_GitSource{
				Url:    sourceCodeModel.Source.GitSource.Url,
				Branch: sourceCodeModel.Source.GitSource.Branch,
			},
		}
	case model.SourceCodeCloudFSType:
		result.Source = &jobv1.SourceCode_CloudFsSource_{
			CloudFsSource: &jobv1.SourceCode_CloudFsSource{
				VolumeName: sourceCodeModel.Source.CloudFsSource.VolumeName,
				SubPath:    sourceCodeModel.Source.CloudFsSource.SubPath,
			},
		}
	case model.SourceCodeCubeFSType:
		result.Source = &jobv1.SourceCode_CubeFsSource_{
			CubeFsSource: &jobv1.SourceCode_CubeFsSource{
				VolumeName: sourceCodeModel.Source.CubeFsSource.VolumeName,
			},
		}
	}
	result.MountPoint = sourceCodeModel.MountPoint
	return result
}

func ConvertToJobModel(request *jobv1.CreateJobRequest) *model.JobEntity {
	n := &model.JobEntity{}
	n.JobType = request.JobType.String()
	n.Members = request.Members
	n.Priority = request.Priority.String()
	n.AlarmShielding = request.AlarmShielding
	n.ID = primitive.NewObjectID()
	if request.Name == "" {
		n.Name = util.GenerateRandomStringWithPrefix("t", 4)
	} else {
		n.Name = request.Name
	}
	n.Region = request.Region
	n.Description = request.Description
	n.DisplayName = request.DisplayName
	n.WorkspaceName = request.WorkspaceName
	n.QueueName = request.QueueName
	n.Labels = request.Labels
	n.MaxWaitTime = time.Duration(request.MaxWaitTime) * time.Second
	n.RestartPolicy = &model.RestartPolicy{
		MaxRetryCount: request.RestartPolicy.MaxRetryCount,
		Enabled:       request.RestartPolicy.Enabled,
	}
	n.HadoopEnabled = request.HadoopEnabled
	n.HadoopUsers = request.HadoopUsers
	n.JobTemplateName = request.JobTemplateName

	if request.JobType == jobv1.JobType_DeepSpeed {

		masterCommand := request.DeepSpeedJobTemplate.Command
		if request.DeepSpeedJobTemplate.Master.Command != "" {
			masterCommand = request.DeepSpeedJobTemplate.Master.Command
		}
		masterImage := request.DeepSpeedJobTemplate.Image
		if request.DeepSpeedJobTemplate.Master.Image != "" {
			masterImage = request.DeepSpeedJobTemplate.Master.Image
		}
		workerCommand := request.DeepSpeedJobTemplate.Command
		if request.DeepSpeedJobTemplate.Worker.Command != "" {
			workerCommand = request.DeepSpeedJobTemplate.Worker.Command
		}
		workerImage := request.DeepSpeedJobTemplate.Image
		if request.DeepSpeedJobTemplate.Worker.Image != "" {
			workerImage = request.DeepSpeedJobTemplate.Worker.Image
		}

		envVars := servicehelper.EnvVarsToEnvEntity(request.DeepSpeedJobTemplate.EnvVars)
		masterEnvVars := append(envVars, servicehelper.EnvVarsToEnvEntity(request.DeepSpeedJobTemplate.Master.EnvVars)...)
		workerEnvVars := append(envVars, servicehelper.EnvVarsToEnvEntity(request.DeepSpeedJobTemplate.Worker.EnvVars)...)
		jobTemplate := &model.DeepSpeedJobTemplate{
			TensorboardEnabled: request.DeepSpeedJobTemplate.TensorboardEnabled,
			VolumeSpecs:        servicehelper.VolumeSpecToEntity(request.DeepSpeedJobTemplate.VolumeSpecs),
			ConfigSpecs:        model.ConvertToConfigSpecEntity(request.DeepSpeedJobTemplate.ConfigSpecs),
			SourceCode:         ConvertToSourceCodeModel(request.DeepSpeedJobTemplate.SourceCode),
			Image:              request.DeepSpeedJobTemplate.Image,
			Command:            request.DeepSpeedJobTemplate.Command,
			Worker: &model.TaskSpec{
				Name:                  request.DeepSpeedJobTemplate.Worker.Name,
				Replicas:              request.DeepSpeedJobTemplate.Worker.Replicas,
				Command:               workerCommand,
				Image:                 workerImage,
				EnvVars:               workerEnvVars,
				VolumeSpecs:           servicehelper.VolumeSpecToEntity(request.DeepSpeedJobTemplate.Worker.VolumeSpecs),
				Specification:         servicehelper.SpecificationToEntity(request.DeepSpeedJobTemplate.Worker.Specification),
				NodeSpecificationName: request.DeepSpeedJobTemplate.Worker.NodeSpecificationName,
				Slots:                 request.DeepSpeedJobTemplate.Worker.Slots,
			},
			Master: &model.TaskSpec{
				Name:                  request.DeepSpeedJobTemplate.Master.Name,
				Replicas:              request.DeepSpeedJobTemplate.Master.Replicas,
				Command:               masterCommand,
				Image:                 masterImage,
				EnvVars:               masterEnvVars,
				VolumeSpecs:           servicehelper.VolumeSpecToEntity(request.DeepSpeedJobTemplate.Master.VolumeSpecs),
				Specification:         servicehelper.SpecificationToEntity(request.DeepSpeedJobTemplate.Master.Specification),
				NodeSpecificationName: request.DeepSpeedJobTemplate.Master.NodeSpecificationName,
				Slots:                 request.DeepSpeedJobTemplate.Master.Slots,
			},
		}
		n.DeepSpeedJobTemplate = jobTemplate
		n.DeepSpeedJobTemplate.EnvVars = envVars
	}
	if request.JobType == jobv1.JobType_PyTorchDDP {
		masterCommand := request.PyTorchDDPJobTemplate.Command
		if request.PyTorchDDPJobTemplate.Master.Command != "" {
			masterCommand = request.PyTorchDDPJobTemplate.Master.Command
		}
		masterImage := request.PyTorchDDPJobTemplate.Image
		if request.PyTorchDDPJobTemplate.Master.Image != "" {
			masterImage = request.PyTorchDDPJobTemplate.Master.Image
		}
		workerCommand := request.PyTorchDDPJobTemplate.Command
		if request.PyTorchDDPJobTemplate.Worker.Command != "" {
			workerCommand = request.PyTorchDDPJobTemplate.Worker.Command
		}
		workerImage := request.PyTorchDDPJobTemplate.Image
		if request.PyTorchDDPJobTemplate.Worker.Image != "" {
			workerImage = request.PyTorchDDPJobTemplate.Worker.Image
		}
		envVars := servicehelper.EnvVarsToEnvEntity(request.PyTorchDDPJobTemplate.EnvVars)
		masterEnvVars := append(envVars, servicehelper.EnvVarsToEnvEntity(request.PyTorchDDPJobTemplate.Master.EnvVars)...)
		workerEnvVars := append(envVars, servicehelper.EnvVarsToEnvEntity(request.PyTorchDDPJobTemplate.Worker.EnvVars)...)
		jobTemplate := &model.PytorchJobTemplate{
			TensorboardEnabled: request.PyTorchDDPJobTemplate.TensorboardEnabled,
			VolumeSpecs:        servicehelper.VolumeSpecToEntity(request.PyTorchDDPJobTemplate.VolumeSpecs),
			ConfigSpecs:        model.ConvertToConfigSpecEntity(request.PyTorchDDPJobTemplate.ConfigSpecs),
			SourceCode:         ConvertToSourceCodeModel(request.PyTorchDDPJobTemplate.SourceCode),
			Image:              request.PyTorchDDPJobTemplate.Image,
			Command:            request.PyTorchDDPJobTemplate.Command,
			Worker: &model.TaskSpec{
				Name:                  request.PyTorchDDPJobTemplate.Worker.Name,
				Replicas:              request.PyTorchDDPJobTemplate.Worker.Replicas,
				Command:               workerCommand,
				Image:                 workerImage,
				EnvVars:               workerEnvVars,
				VolumeSpecs:           servicehelper.VolumeSpecToEntity(request.PyTorchDDPJobTemplate.Worker.VolumeSpecs),
				Specification:         servicehelper.SpecificationToEntity(request.PyTorchDDPJobTemplate.Worker.Specification),
				NodeSpecificationName: request.PyTorchDDPJobTemplate.Worker.NodeSpecificationName,
				Slots:                 request.PyTorchDDPJobTemplate.Worker.Slots,
			},
			Master: &model.TaskSpec{
				Name:                  request.PyTorchDDPJobTemplate.Master.Name,
				Replicas:              request.PyTorchDDPJobTemplate.Master.Replicas,
				Command:               masterCommand,
				Image:                 masterImage,
				EnvVars:               masterEnvVars,
				VolumeSpecs:           servicehelper.VolumeSpecToEntity(request.PyTorchDDPJobTemplate.Master.VolumeSpecs),
				Specification:         servicehelper.SpecificationToEntity(request.PyTorchDDPJobTemplate.Master.Specification),
				NodeSpecificationName: request.PyTorchDDPJobTemplate.Master.NodeSpecificationName,
				Slots:                 request.PyTorchDDPJobTemplate.Master.Slots,
			},
		}
		n.PytorchJobTemplate = jobTemplate
		n.PytorchJobTemplate.EnvVars = envVars
	}
	if request.JobType == jobv1.JobType_SimpleTraining {
		jobTemplate := &model.SimpleTrainingJobTemplate{
			TensorboardEnabled:    request.SimpleTrainingJobTemplate.TensorboardEnabled,
			SourceCode:            ConvertToSourceCodeModel(request.SimpleTrainingJobTemplate.SourceCode),
			Command:               request.SimpleTrainingJobTemplate.Command,
			Image:                 request.SimpleTrainingJobTemplate.Image,
			EnvVars:               servicehelper.EnvVarsToEnvEntity(request.SimpleTrainingJobTemplate.EnvVars),
			VolumeSpecs:           servicehelper.VolumeSpecToEntity(request.SimpleTrainingJobTemplate.VolumeSpecs),
			ConfigSpecs:           model.ConvertToConfigSpecEntity(request.SimpleTrainingJobTemplate.ConfigSpecs),
			Specification:         servicehelper.SpecificationToEntity(request.SimpleTrainingJobTemplate.Specification),
			NodeSpecificationName: request.SimpleTrainingJobTemplate.NodeSpecificationName,
		}
		n.SimpleTrainingJobTemplate = jobTemplate
	}

	return n
}

func ConvertTimerTriggerToScheduleSpec(options *jobv1.TimerTriggerOptions) (temporalSdkClient.ScheduleSpec, error) {
	// 默认开始时间是当前时间，结束时间为 100年后，默认永久有效
	startAt := time.Now()
	endAt := time.Now().Add(100 * time.Hour * 24 * 365) // 100年

	spec := temporalSdkClient.ScheduleSpec{
		TimeZoneName: "Asia/Shanghai",
		StartAt:      startAt,
		EndAt:        endAt,
	}

	var Interval []temporalSdkClient.ScheduleIntervalSpec
	var Calendars []temporalSdkClient.ScheduleCalendarSpec
	var CronExpressions []string

	// 根据有效期获取开始时间和结束时间，有效期形式 秒时间戳,秒时间戳
	if options.PeriodOfValidity != "" {
		// 拆分字符串
		timestamps := strings.Split(options.PeriodOfValidity, ",")
		if len(timestamps) == 2 {
			// 解析开始时间
			startTimestamp, err := strconv.ParseInt(timestamps[0], 10, 64)
			if err != nil {
				return temporalSdkClient.ScheduleSpec{}, bcode.ErrorServerInternalError("解析开始时间戳时出错")
			}
			startAt = time.Unix(startTimestamp, 0)

			// 解析结束时间
			endTimestamp, err := strconv.ParseInt(timestamps[1], 10, 64)
			if err != nil {
				return temporalSdkClient.ScheduleSpec{}, bcode.ErrorServerInternalError("解析结束时间戳时出错")
			}
			endAt = time.Unix(endTimestamp, 0)

			spec.StartAt = startAt
			spec.EndAt = endAt

		} else {
			return spec, bcode.ErrorInvalidArgument("invalid period of validity format")
		}
	}

	// 获取跳过时间，跳过时间同周期调度形式一致
	if options.SkipTime != nil {
		var monthScheduleRange []temporalSdkClient.ScheduleRange
		for _, m := range options.SkipTime.Month {
			monthScheduleRange = append(monthScheduleRange, temporalSdkClient.ScheduleRange{
				Start: int(m),
			})
		}
		var dayOfMonthScheduleRange []temporalSdkClient.ScheduleRange
		for _, d := range options.SkipTime.DayOfMonth {
			dayOfMonthScheduleRange = append(dayOfMonthScheduleRange, temporalSdkClient.ScheduleRange{
				Start: int(d),
			})

		}
		var dayOfWeekScheduleRange []temporalSdkClient.ScheduleRange
		for _, d := range options.SkipTime.DayOfWeek {
			dayOfWeekScheduleRange = append(dayOfWeekScheduleRange, temporalSdkClient.ScheduleRange{
				Start: int(d),
			})
		}
		var hourScheduleRange []temporalSdkClient.ScheduleRange
		for _, h := range options.SkipTime.Hour {
			hourScheduleRange = append(hourScheduleRange, temporalSdkClient.ScheduleRange{
				Start: int(h),
			})
		}
		var minuteScheduleRange []temporalSdkClient.ScheduleRange
		for _, m := range options.SkipTime.Minute {
			minuteScheduleRange = append(minuteScheduleRange, temporalSdkClient.ScheduleRange{
				Start: int(m),
			})
		}

		Calendars = append(Calendars, temporalSdkClient.ScheduleCalendarSpec{
			Month:      monthScheduleRange,
			DayOfMonth: dayOfMonthScheduleRange,
			DayOfWeek:  dayOfWeekScheduleRange,
			Hour:       hourScheduleRange,
			Minute:     minuteScheduleRange,
		})
		spec.Skip = Calendars
	}

	// 根据不同类型获取 spec
	switch options.Type {
	case jobv1.TimerTriggerOptions_Interval:
		// 时间间隔，并非严格的按照当前时间+时间间隔执行方式
		//1. **基础间隔示例**
		//	- **参数**: `every = 1小时`, `offset = 0`
		//- **效果**: 每小时整点触发一次（如 09:00, 10:00）。
		//- **公式**: Epoch + n×1小时 + 0 → 从基准时间开始，每小时叠加一次间隔。
		//
		//2. **带偏移量的间隔示例** 目前不支持偏移量
		//	- **参数**: `every = 1小时`, `offset = 19分钟`
		//- **效果**: 每小时第19分钟触发（如 09:19, 10:19）。
		//- **公式**: Epoch + n×1小时 + 19分钟 → 在每小时间隔基础上叠加固定偏移量。
		Interval = append(Interval, temporalSdkClient.ScheduleIntervalSpec{
			Every: time.Duration(options.Interval) * time.Minute,
		})
		spec.Intervals = Interval
		spec.StartAt = time.Now()

	case jobv1.TimerTriggerOptions_Cycle:
		// 周期调度 每 xx 月 xx 日 （或者每周 xx）xx 时 xx 分运行
		var monthScheduleRange []temporalSdkClient.ScheduleRange
		for _, m := range options.Cycle.Month {
			monthScheduleRange = append(monthScheduleRange, temporalSdkClient.ScheduleRange{
				Start: int(m),
			})
		}
		var dayOfMonthScheduleRange []temporalSdkClient.ScheduleRange
		for _, d := range options.Cycle.DayOfMonth {
			dayOfMonthScheduleRange = append(dayOfMonthScheduleRange, temporalSdkClient.ScheduleRange{
				Start: int(d),
			})

		}
		var dayOfWeekScheduleRange []temporalSdkClient.ScheduleRange
		for _, d := range options.Cycle.DayOfWeek {
			dayOfWeekScheduleRange = append(dayOfWeekScheduleRange, temporalSdkClient.ScheduleRange{
				Start: int(d),
			})
		}
		var hourScheduleRange []temporalSdkClient.ScheduleRange
		for _, h := range options.Cycle.Hour {
			hourScheduleRange = append(hourScheduleRange, temporalSdkClient.ScheduleRange{
				Start: int(h),
			})
		}
		var minuteScheduleRange []temporalSdkClient.ScheduleRange
		for _, m := range options.Cycle.Minute {
			minuteScheduleRange = append(minuteScheduleRange, temporalSdkClient.ScheduleRange{
				Start: int(m),
			})
		}

		Calendars = append(Calendars, temporalSdkClient.ScheduleCalendarSpec{
			Month:      monthScheduleRange,
			DayOfMonth: dayOfMonthScheduleRange,
			DayOfWeek:  dayOfWeekScheduleRange,
			Hour:       hourScheduleRange,
			Minute:     minuteScheduleRange,
		})
		spec.Calendars = Calendars

	case jobv1.TimerTriggerOptions_CronExpr:
		// Cron 表达式
		//┌───────────── minute (0 - 59)
		//│ ┌───────────── hour (0 - 23)
		//│ │ ┌───────────── day of the month (1 - 31)
		//│ │ │ ┌───────────── month (1 - 12)
		//│ │ │ │ ┌───────────── day of the week (0 - 6) (Sunday to Saturday)
		//│ │ │ │ │
		//* * * * *
		CronExpressions = append(CronExpressions, options.CronExpr)
		spec.CronExpressions = CronExpressions
	default:
		return spec, bcode.ErrorInvalidArgument("invalid trigger type")
	}

	return spec, nil
}

func ConvertJobToJobModel(request *jobv1.Job) *model.JobEntity {
	n := &model.JobEntity{}
	n.JobType = request.JobType.String()
	n.Members = request.Members
	n.Priority = jobv1.Priority_name[request.Priority]
	n.AlarmShielding = request.AlarmShielding
	n.ID = primitive.NewObjectID()
	if request.Name == "" {
		n.Name = util.GenerateRandomStringWithPrefix("t", 4)
	} else {
		n.Name = request.Name
	}
	n.Region = request.Region
	n.Description = request.Description
	n.DisplayName = request.DisplayName
	n.WorkspaceName = request.WorkspaceName
	n.QueueName = request.QueueName
	n.Labels = request.Labels
	n.MaxWaitTime = time.Duration(request.MaxWaitTime) * time.Second
	n.RestartPolicy = &model.RestartPolicy{
		MaxRetryCount: request.RestartPolicy.MaxRetryCount,
		Enabled:       request.RestartPolicy.Enabled,
	}

	if request.JobType == jobv1.JobType_DeepSpeed {

		masterCommand := request.DeepSpeedJobTemplate.Command
		if request.DeepSpeedJobTemplate.Master.Command != "" {
			masterCommand = request.DeepSpeedJobTemplate.Master.Command
		}
		masterImage := request.DeepSpeedJobTemplate.Image
		if request.DeepSpeedJobTemplate.Master.Image != "" {
			masterImage = request.DeepSpeedJobTemplate.Master.Image
		}
		workerCommand := request.DeepSpeedJobTemplate.Command
		if request.DeepSpeedJobTemplate.Worker.Command != "" {
			workerCommand = request.DeepSpeedJobTemplate.Worker.Command
		}
		workerImage := request.DeepSpeedJobTemplate.Image
		if request.DeepSpeedJobTemplate.Worker.Image != "" {
			workerImage = request.DeepSpeedJobTemplate.Worker.Image
		}

		envVars := servicehelper.EnvVarsToEnvEntity(request.DeepSpeedJobTemplate.EnvVars)
		masterEnvVars := append(envVars, servicehelper.EnvVarsToEnvEntity(request.DeepSpeedJobTemplate.Master.EnvVars)...)
		workerEnvVars := append(envVars, servicehelper.EnvVarsToEnvEntity(request.DeepSpeedJobTemplate.Worker.EnvVars)...)
		jobTemplate := &model.DeepSpeedJobTemplate{
			TensorboardEnabled: request.DeepSpeedJobTemplate.TensorboardEnabled,
			VolumeSpecs:        servicehelper.VolumeSpecToEntity(request.DeepSpeedJobTemplate.VolumeSpecs),
			ConfigSpecs:        model.ConvertToConfigSpecEntity(request.DeepSpeedJobTemplate.ConfigSpecs),
			SourceCode:         ConvertToSourceCodeModel(request.DeepSpeedJobTemplate.SourceCode),
			Image:              request.DeepSpeedJobTemplate.Image,
			Command:            request.DeepSpeedJobTemplate.Command,
			Worker: &model.TaskSpec{
				Name:                  request.DeepSpeedJobTemplate.Worker.Name,
				Replicas:              request.DeepSpeedJobTemplate.Worker.Replicas,
				Command:               workerCommand,
				Image:                 workerImage,
				EnvVars:               workerEnvVars,
				VolumeSpecs:           servicehelper.VolumeSpecToEntity(request.DeepSpeedJobTemplate.Worker.VolumeSpecs),
				Specification:         servicehelper.SpecificationToEntity(request.DeepSpeedJobTemplate.Worker.Specification),
				NodeSpecificationName: request.DeepSpeedJobTemplate.Worker.NodeSpecificationName,
				Slots:                 request.DeepSpeedJobTemplate.Worker.Slots,
			},
			Master: &model.TaskSpec{
				Name:                  request.DeepSpeedJobTemplate.Master.Name,
				Replicas:              request.DeepSpeedJobTemplate.Master.Replicas,
				Command:               masterCommand,
				Image:                 masterImage,
				EnvVars:               masterEnvVars,
				VolumeSpecs:           servicehelper.VolumeSpecToEntity(request.DeepSpeedJobTemplate.Master.VolumeSpecs),
				Specification:         servicehelper.SpecificationToEntity(request.DeepSpeedJobTemplate.Master.Specification),
				NodeSpecificationName: request.DeepSpeedJobTemplate.Master.NodeSpecificationName,
				Slots:                 request.DeepSpeedJobTemplate.Master.Slots,
			},
		}
		n.DeepSpeedJobTemplate = jobTemplate
		n.DeepSpeedJobTemplate.EnvVars = envVars
	}
	if request.JobType == jobv1.JobType_PyTorchDDP {
		masterCommand := request.PyTorchDDPJobTemplate.Command
		if request.PyTorchDDPJobTemplate.Master.Command != "" {
			masterCommand = request.PyTorchDDPJobTemplate.Master.Command
		}
		masterImage := request.PyTorchDDPJobTemplate.Image
		if request.PyTorchDDPJobTemplate.Master.Image != "" {
			masterImage = request.PyTorchDDPJobTemplate.Master.Image
		}
		workerCommand := request.PyTorchDDPJobTemplate.Command
		if request.PyTorchDDPJobTemplate.Worker.Command != "" {
			workerCommand = request.PyTorchDDPJobTemplate.Worker.Command
		}
		workerImage := request.PyTorchDDPJobTemplate.Image
		if request.PyTorchDDPJobTemplate.Worker.Image != "" {
			workerImage = request.PyTorchDDPJobTemplate.Worker.Image
		}
		envVars := servicehelper.EnvVarsToEnvEntity(request.PyTorchDDPJobTemplate.EnvVars)
		masterEnvVars := append(envVars, servicehelper.EnvVarsToEnvEntity(request.PyTorchDDPJobTemplate.Master.EnvVars)...)
		workerEnvVars := append(envVars, servicehelper.EnvVarsToEnvEntity(request.PyTorchDDPJobTemplate.Worker.EnvVars)...)
		jobTemplate := &model.PytorchJobTemplate{
			TensorboardEnabled: request.PyTorchDDPJobTemplate.TensorboardEnabled,
			VolumeSpecs:        servicehelper.VolumeSpecToEntity(request.PyTorchDDPJobTemplate.VolumeSpecs),
			ConfigSpecs:        model.ConvertToConfigSpecEntity(request.PyTorchDDPJobTemplate.ConfigSpecs),
			SourceCode:         ConvertToSourceCodeModel(request.PyTorchDDPJobTemplate.SourceCode),
			Image:              request.PyTorchDDPJobTemplate.Image,
			Command:            request.PyTorchDDPJobTemplate.Command,
			Worker: &model.TaskSpec{
				Name:                  request.PyTorchDDPJobTemplate.Worker.Name,
				Replicas:              request.PyTorchDDPJobTemplate.Worker.Replicas,
				Command:               workerCommand,
				Image:                 workerImage,
				EnvVars:               workerEnvVars,
				VolumeSpecs:           servicehelper.VolumeSpecToEntity(request.PyTorchDDPJobTemplate.Worker.VolumeSpecs),
				Specification:         servicehelper.SpecificationToEntity(request.PyTorchDDPJobTemplate.Worker.Specification),
				NodeSpecificationName: request.PyTorchDDPJobTemplate.Worker.NodeSpecificationName,
				Slots:                 request.PyTorchDDPJobTemplate.Worker.Slots,
			},
			Master: &model.TaskSpec{
				Name:                  request.PyTorchDDPJobTemplate.Master.Name,
				Replicas:              request.PyTorchDDPJobTemplate.Master.Replicas,
				Command:               masterCommand,
				Image:                 masterImage,
				EnvVars:               masterEnvVars,
				VolumeSpecs:           servicehelper.VolumeSpecToEntity(request.PyTorchDDPJobTemplate.Master.VolumeSpecs),
				Specification:         servicehelper.SpecificationToEntity(request.PyTorchDDPJobTemplate.Master.Specification),
				NodeSpecificationName: request.PyTorchDDPJobTemplate.Master.NodeSpecificationName,
				Slots:                 request.PyTorchDDPJobTemplate.Master.Slots,
			},
		}
		n.PytorchJobTemplate = jobTemplate
		n.PytorchJobTemplate.EnvVars = envVars
	}
	if request.JobType == jobv1.JobType_SimpleTraining {
		jobTemplate := &model.SimpleTrainingJobTemplate{
			TensorboardEnabled:    request.SimpleTrainingJobTemplate.TensorboardEnabled,
			SourceCode:            ConvertToSourceCodeModel(request.SimpleTrainingJobTemplate.SourceCode),
			Command:               request.SimpleTrainingJobTemplate.Command,
			Image:                 request.SimpleTrainingJobTemplate.Image,
			EnvVars:               servicehelper.EnvVarsToEnvEntity(request.SimpleTrainingJobTemplate.EnvVars),
			VolumeSpecs:           servicehelper.VolumeSpecToEntity(request.SimpleTrainingJobTemplate.VolumeSpecs),
			ConfigSpecs:           model.ConvertToConfigSpecEntity(request.SimpleTrainingJobTemplate.ConfigSpecs),
			Specification:         servicehelper.SpecificationToEntity(request.SimpleTrainingJobTemplate.Specification),
			NodeSpecificationName: request.SimpleTrainingJobTemplate.NodeSpecificationName,
		}
		n.SimpleTrainingJobTemplate = jobTemplate
	}

	return n
}

func ConvertToJobTemplateModel(request *jobv1.CreateOrUpdateJobTemplateRequest) *model.JobTemplateEntity {
	job := request.Job
	n := &model.JobEntity{}
	n.JobType = job.JobType.String()
	n.Members = job.Members
	n.Creator = job.Creator
	n.Priority = string(job.Priority)
	n.AlarmShielding = job.AlarmShielding
	n.ID = primitive.NewObjectID()
	if job.Name == "" {
		n.Name = util.GenerateRandomStringWithPrefix("t", 4)
	} else {
		n.Name = job.Name
	}
	n.Region = request.Region
	n.Description = request.Description
	n.DisplayName = request.DisplayName
	n.WorkspaceName = request.WorkspaceName
	n.QueueName = job.QueueName
	n.Labels = request.Labels
	n.MaxWaitTime = time.Duration(job.MaxWaitTime) * time.Second
	n.RestartPolicy = &model.RestartPolicy{
		MaxRetryCount: job.RestartPolicy.MaxRetryCount,
		Enabled:       job.RestartPolicy.Enabled,
	}

	if job.JobType == jobv1.JobType_DeepSpeed {
		masterCommand := job.DeepSpeedJobTemplate.Command
		if job.DeepSpeedJobTemplate.Master.Command != "" {
			masterCommand = job.DeepSpeedJobTemplate.Master.Command
		}
		masterImage := job.DeepSpeedJobTemplate.Image
		if job.DeepSpeedJobTemplate.Master.Image != "" {
			masterImage = job.DeepSpeedJobTemplate.Master.Image
		}
		workerCommand := job.DeepSpeedJobTemplate.Command
		if job.DeepSpeedJobTemplate.Worker.Command != "" {
			workerCommand = job.DeepSpeedJobTemplate.Worker.Command
		}
		workerImage := job.DeepSpeedJobTemplate.Image
		if job.DeepSpeedJobTemplate.Worker.Image != "" {
			workerImage = job.DeepSpeedJobTemplate.Worker.Image
		}

		envVars := servicehelper.EnvVarsToEnvEntity(job.DeepSpeedJobTemplate.EnvVars)
		masterEnvVars := append(envVars, servicehelper.EnvVarsToEnvEntity(job.DeepSpeedJobTemplate.Master.EnvVars)...)
		workerEnvVars := append(envVars, servicehelper.EnvVarsToEnvEntity(job.DeepSpeedJobTemplate.Worker.EnvVars)...)
		jobTemplate := &model.DeepSpeedJobTemplate{
			TensorboardEnabled: job.DeepSpeedJobTemplate.TensorboardEnabled,
			VolumeSpecs:        servicehelper.VolumeSpecToEntity(job.DeepSpeedJobTemplate.VolumeSpecs),
			ConfigSpecs:        model.ConvertToConfigSpecEntity(job.DeepSpeedJobTemplate.ConfigSpecs),
			SourceCode:         ConvertToSourceCodeModel(job.DeepSpeedJobTemplate.SourceCode),
			Image:              job.DeepSpeedJobTemplate.Image,
			Command:            job.DeepSpeedJobTemplate.Command,
			Worker: &model.TaskSpec{
				Name:                  job.DeepSpeedJobTemplate.Worker.Name,
				Replicas:              job.DeepSpeedJobTemplate.Worker.Replicas,
				Command:               workerCommand,
				Image:                 workerImage,
				EnvVars:               workerEnvVars,
				VolumeSpecs:           servicehelper.VolumeSpecToEntity(job.DeepSpeedJobTemplate.Worker.VolumeSpecs),
				Specification:         servicehelper.SpecificationToEntity(job.DeepSpeedJobTemplate.Worker.Specification),
				NodeSpecificationName: job.DeepSpeedJobTemplate.Worker.NodeSpecificationName,
				Slots:                 job.DeepSpeedJobTemplate.Worker.Slots,
			},
			Master: &model.TaskSpec{
				Name:                  job.DeepSpeedJobTemplate.Master.Name,
				Replicas:              job.DeepSpeedJobTemplate.Master.Replicas,
				Command:               masterCommand,
				Image:                 masterImage,
				EnvVars:               masterEnvVars,
				VolumeSpecs:           servicehelper.VolumeSpecToEntity(job.DeepSpeedJobTemplate.Master.VolumeSpecs),
				Specification:         servicehelper.SpecificationToEntity(job.DeepSpeedJobTemplate.Master.Specification),
				NodeSpecificationName: job.DeepSpeedJobTemplate.Master.NodeSpecificationName,
				Slots:                 job.DeepSpeedJobTemplate.Master.Slots,
			},
		}
		n.DeepSpeedJobTemplate = jobTemplate
		n.DeepSpeedJobTemplate.EnvVars = envVars
	}
	if job.JobType == jobv1.JobType_PyTorchDDP {
		masterCommand := job.PyTorchDDPJobTemplate.Command
		if job.PyTorchDDPJobTemplate.Master.Command != "" {
			masterCommand = job.PyTorchDDPJobTemplate.Master.Command
		}
		masterImage := job.PyTorchDDPJobTemplate.Image
		if job.PyTorchDDPJobTemplate.Master.Image != "" {
			masterImage = job.PyTorchDDPJobTemplate.Master.Image
		}
		workerCommand := job.PyTorchDDPJobTemplate.Command
		if job.PyTorchDDPJobTemplate.Worker.Command != "" {
			workerCommand = job.PyTorchDDPJobTemplate.Worker.Command
		}
		workerImage := job.PyTorchDDPJobTemplate.Image
		if job.PyTorchDDPJobTemplate.Worker.Image != "" {
			workerImage = job.PyTorchDDPJobTemplate.Worker.Image
		}
		envVars := servicehelper.EnvVarsToEnvEntity(job.PyTorchDDPJobTemplate.EnvVars)
		masterEnvVars := append(envVars, servicehelper.EnvVarsToEnvEntity(job.PyTorchDDPJobTemplate.Master.EnvVars)...)
		workerEnvVars := append(envVars, servicehelper.EnvVarsToEnvEntity(job.PyTorchDDPJobTemplate.Worker.EnvVars)...)
		jobTemplate := &model.PytorchJobTemplate{
			TensorboardEnabled: job.PyTorchDDPJobTemplate.TensorboardEnabled,
			VolumeSpecs:        servicehelper.VolumeSpecToEntity(job.PyTorchDDPJobTemplate.VolumeSpecs),
			ConfigSpecs:        model.ConvertToConfigSpecEntity(job.PyTorchDDPJobTemplate.ConfigSpecs),
			SourceCode:         ConvertToSourceCodeModel(job.PyTorchDDPJobTemplate.SourceCode),
			Image:              job.PyTorchDDPJobTemplate.Image,
			Command:            job.PyTorchDDPJobTemplate.Command,
			Worker: &model.TaskSpec{
				Name:                  job.PyTorchDDPJobTemplate.Worker.Name,
				Replicas:              job.PyTorchDDPJobTemplate.Worker.Replicas,
				Command:               workerCommand,
				Image:                 workerImage,
				EnvVars:               workerEnvVars,
				VolumeSpecs:           servicehelper.VolumeSpecToEntity(job.PyTorchDDPJobTemplate.Worker.VolumeSpecs),
				Specification:         servicehelper.SpecificationToEntity(job.PyTorchDDPJobTemplate.Worker.Specification),
				NodeSpecificationName: job.PyTorchDDPJobTemplate.Worker.NodeSpecificationName,
				Slots:                 job.PyTorchDDPJobTemplate.Worker.Slots,
			},
			Master: &model.TaskSpec{
				Name:                  job.PyTorchDDPJobTemplate.Master.Name,
				Replicas:              job.PyTorchDDPJobTemplate.Master.Replicas,
				Command:               masterCommand,
				Image:                 masterImage,
				EnvVars:               masterEnvVars,
				VolumeSpecs:           servicehelper.VolumeSpecToEntity(job.PyTorchDDPJobTemplate.Master.VolumeSpecs),
				Specification:         servicehelper.SpecificationToEntity(job.PyTorchDDPJobTemplate.Master.Specification),
				NodeSpecificationName: job.PyTorchDDPJobTemplate.Master.NodeSpecificationName,
				Slots:                 job.PyTorchDDPJobTemplate.Master.Slots,
			},
		}
		n.PytorchJobTemplate = jobTemplate
		n.PytorchJobTemplate.EnvVars = envVars
	}
	if job.JobType == jobv1.JobType_SimpleTraining {
		jobTemplate := &model.SimpleTrainingJobTemplate{
			TensorboardEnabled:    job.SimpleTrainingJobTemplate.TensorboardEnabled,
			SourceCode:            ConvertToSourceCodeModel(job.SimpleTrainingJobTemplate.SourceCode),
			Command:               job.SimpleTrainingJobTemplate.Command,
			Image:                 job.SimpleTrainingJobTemplate.Image,
			EnvVars:               servicehelper.EnvVarsToEnvEntity(job.SimpleTrainingJobTemplate.EnvVars),
			VolumeSpecs:           servicehelper.VolumeSpecToEntity(job.SimpleTrainingJobTemplate.VolumeSpecs),
			ConfigSpecs:           model.ConvertToConfigSpecEntity(job.SimpleTrainingJobTemplate.ConfigSpecs),
			Specification:         servicehelper.SpecificationToEntity(job.SimpleTrainingJobTemplate.Specification),
			NodeSpecificationName: job.SimpleTrainingJobTemplate.NodeSpecificationName,
		}
		n.SimpleTrainingJobTemplate = jobTemplate
	}

	var triggers []string
	for _, trigger := range request.Triggers {
		triggers = append(triggers, trigger.TriggerName)
	}

	jobTemplate := &model.JobTemplateEntity{
		ID:                primitive.NewObjectID(),
		Name:              request.Name,
		DisplayName:       request.DisplayName,
		Description:       request.Description,
		WorkspaceName:     request.WorkspaceName,
		Region:            request.Region,
		Labels:            request.Labels,
		Managers:          request.Managers,
		Members:           request.Members,
		Job:               n,
		ExecutionStrategy: jobv1.ExecutionStrategy_name[int32(request.ExecutionStrategy)],
		Triggers:          triggers,
	}

	return jobTemplate
}

func ConvertTriggerToTriggerModel(request *jobv1.Trigger) *model.TriggerEntity {
	if request == nil {
		return nil
	}
	trigger := &model.TriggerEntity{
		ID:              primitive.NewObjectID(),
		TriggerName:     request.TriggerName,
		TriggerType:     jobv1.TriggerType_name[int32(request.TriggerType)],
		Message:         request.Message,
		NumActions:      request.NumActions,
		NextActionTimes: request.NextActionTimes,
		TriggerState:    jobv1.TriggerState_name[int32(request.State)],
	}
	if request.TriggerType == jobv1.TriggerType_Timer {
		options := request.TimeTriggerOptions
		trigger.TimerTriggerOptions = model.TimerTriggerOptions{
			TimerTriggerType: jobv1.TimerTriggerOptions_TimerTriggerType_name[int32(options.Type)],
		}
		if options.Type == jobv1.TimerTriggerOptions_Interval {
			trigger.TimerTriggerOptions.Interval = options.Interval
		} else if options.Type == jobv1.TimerTriggerOptions_CronExpr {
			trigger.TimerTriggerOptions.CronExpr = options.CronExpr
		} else if options.Type == jobv1.TimerTriggerOptions_Cycle {
			trigger.TimerTriggerOptions.Cycle = model.Cycle{
				Month:      options.Cycle.Month,
				DayOfMonth: options.Cycle.DayOfMonth,
				DayOfWeek:  options.Cycle.DayOfWeek,
				Hour:       options.Cycle.Hour,
				Minute:     options.Cycle.Minute,
			}
		}
		trigger.TimerTriggerOptions.PeriodOfValidity = options.PeriodOfValidity
		trigger.TimerTriggerOptions.MaxCount = int(options.MaxCount)
		if request.TimeTriggerOptions.SkipTime != nil {
			trigger.TimerTriggerOptions.SkipTime = model.Cycle{
				Month:      options.SkipTime.Month,
				DayOfMonth: options.SkipTime.DayOfMonth,
				DayOfWeek:  options.SkipTime.DayOfWeek,
				Hour:       options.SkipTime.Hour,
				Minute:     options.SkipTime.Minute,
			}
		}
	}
	return trigger
}

func ConvertFromSimpleTrainingJobTemplateModel(simpleTrainingJobTemplateModel *model.SimpleTrainingJobTemplate) *jobv1.SimpleTrainingJobTemplate {
	template := &jobv1.SimpleTrainingJobTemplate{
		TensorboardEnabled: simpleTrainingJobTemplateModel.TensorboardEnabled,
		Command:            simpleTrainingJobTemplateModel.Command,
		Image:              simpleTrainingJobTemplateModel.Image,
		Specification: &common.Specification{
			CpuNum:    simpleTrainingJobTemplateModel.Specification.CpuNum,
			MemoryGiB: simpleTrainingJobTemplateModel.Specification.MemoryGiB,
			GpuNum:    simpleTrainingJobTemplateModel.Specification.GpuNum,
		},
		NodeSpecificationName: simpleTrainingJobTemplateModel.NodeSpecificationName,
	}
	if simpleTrainingJobTemplateModel.SourceCode != nil {
		template.SourceCode = ConvertFromSourceCodeModel(simpleTrainingJobTemplateModel.SourceCode)
	}
	if simpleTrainingJobTemplateModel.EnvVars != nil {
		template.EnvVars = servicehelper.EnvEntityToEnvVars(simpleTrainingJobTemplateModel.EnvVars)
	}
	if simpleTrainingJobTemplateModel.VolumeSpecs != nil {
		template.VolumeSpecs = servicehelper.VolumeEntityToVolumeSpecs(simpleTrainingJobTemplateModel.VolumeSpecs)
	}
	template.ConfigSpecs = model.ConvertToConfigSpec(simpleTrainingJobTemplateModel.ConfigSpecs)
	return template
}

func ConvertFromTaskSpecModel(taskSpecModel *model.TaskSpec) *jobv1.TaskSpec {
	spec := &jobv1.TaskSpec{
		Name:                  taskSpecModel.Name,
		Replicas:              taskSpecModel.Replicas,
		Command:               taskSpecModel.Command,
		Image:                 taskSpecModel.Image,
		NodeSpecificationName: taskSpecModel.NodeSpecificationName,
	}
	if taskSpecModel.Specification != nil {
		spec.Specification = &common.Specification{
			CpuNum:    taskSpecModel.Specification.CpuNum,
			MemoryGiB: taskSpecModel.Specification.MemoryGiB,
			GpuNum:    taskSpecModel.Specification.GpuNum,
		}
	}
	if taskSpecModel.EnvVars != nil {
		spec.EnvVars = servicehelper.EnvEntityToEnvVars(taskSpecModel.EnvVars)
	}
	if taskSpecModel.VolumeSpecs != nil {
		spec.VolumeSpecs = servicehelper.VolumeEntityToVolumeSpecs(taskSpecModel.VolumeSpecs)
	}
	return spec
}

func VolumeModelToJobVolumes(scope string, volumeEntities []*model.VolumeSpecEntity) []*jobv1.JobVolume {
	var jobVolumes []*jobv1.JobVolume
	if volumeEntities != nil {
		for _, entity := range volumeEntities {
			if entity.MountPoint != "" {
				jobVolume := &jobv1.JobVolume{
					MountPoint: entity.MountPoint,
					VolumeType: entity.Type,
					Scope:      scope,
				}
				switch entity.Type {
				case common.VolumeSpec_CloudFs.String():
					jobVolume.Name = entity.CloudFSVolumeProperties.VolumeName
					jobVolume.SubPath = entity.CloudFSVolumeProperties.SubPath
				case common.VolumeSpec_CubeFs.String():
					jobVolume.Name = entity.CubeFSVolumeProperties.VolumeName
				case common.VolumeSpec_Dataset.String():
					jobVolume.Name = entity.DatasetVolumeProperties.DatasetName
				}
				jobVolumes = append(jobVolumes, jobVolume)
			}
		}
	}
	return jobVolumes
}

const (
	SchedulerName           string = "volcano"
	TTLAfterFinishedSeconds int32  = 86400
	PriorityClassPrefix     string = "priority"
	ConfigMapMasterHostFmt         = "master-host"
	ConfigMapHostFileFmt           = "hostfile"
	ConfigMapNameFmt               = "%s-hosts"
	ConfigMapMountPath             = "/etc/aistudio"
	DeepSpeedMasterCommand         = "mkdir -p /var/run/sshd && /usr/sbin/sshd -p 20023"
	DeepSpeedWorkerCommand         = "mkdir -p /var/run/sshd && /usr/sbin/sshd -D -p 20023"
)

func renderVolcanoJobObject(jobEntity *model.JobEntity, jobStatusEntity *model.JobStatusEntity) volcanobatchv1alpha1.Job {
	properties, err := property.GetEnvironmentProperty("")
	if err != nil {
		klog.Errorf("get initContainerImageName failed: %s", err.Error())
	}
	initContainerImageName := properties.MustGetString("job.initContainerImageWithHadoop")
	workspaceName := jobEntity.WorkspaceName
	jobName := jobEntity.Name
	ttl := TTLAfterFinishedSeconds
	totalReplicas := jobStatusEntity.TotalReplicas
	labels := map[string]string{
		constant.WorkspaceNameLabelKey: workspaceName,
		constant.QueueNameLabelKey:     jobEntity.QueueName,
		constant.JobIDLabelKey:         jobEntity.ID.Hex(),
		constant.JobNameLabelKey:       jobName,
		constant.CreateByLabelKey:      jobEntity.Creator,
		constant.RegionLabelKey:        jobEntity.Region,
		constant.WorkloadNameLabelKey:  jobName,
		constant.WorkloadTypeLabelKey:  constant.WorkloadTypeTrainingJob,
		constant.KICManagedLabelKey:    constant.KICManagedLabelValue,
	}
	annotations := map[string]string{
		constant.JobDisplayNameAnnotationKey: jobEntity.DisplayName,
	}
	var job = volcanobatchv1alpha1.Job{
		TypeMeta: metav1.TypeMeta{
			Kind:       volcanobatchv1alpha1.SchemeGroupVersion.WithKind("Job").Kind,
			APIVersion: volcanobatchv1alpha1.SchemeGroupVersion.String(),
		},
		ObjectMeta: metav1.ObjectMeta{
			Name:        jobName,
			Namespace:   servicehelper.GetClusterNamespaceName(workspaceName),
			Labels:      labels,
			Annotations: annotations,
		},
		Spec: volcanobatchv1alpha1.JobSpec{
			SchedulerName:           SchedulerName,
			Queue:                   fmt.Sprintf("%s-%s", jobEntity.WorkspaceName, jobEntity.QueueName),
			TTLSecondsAfterFinished: &ttl,
			PriorityClassName:       getPriorityClassName(jobEntity.Priority),
			Plugins:                 renderJobPlugins(jobEntity.JobType),
			MinAvailable:            totalReplicas,
			Policies: []volcanobatchv1alpha1.LifecyclePolicy{
				{
					Action: volcanobusv1alpha1.CompleteJobAction,
					Event:  volcanobusv1alpha1.TaskCompletedEvent,
				},
			},
		},
	}
	var policies []volcanobatchv1alpha1.LifecyclePolicy
	if jobEntity.RestartPolicy != nil && jobEntity.RestartPolicy.Enabled {
		policies = append(policies, volcanobatchv1alpha1.LifecyclePolicy{
			Action: volcanobusv1alpha1.RestartJobAction,
			Event:  volcanobusv1alpha1.PodFailedEvent,
		})
		job.Spec.MaxRetry = jobEntity.RestartPolicy.MaxRetryCount
	} else {
		policies = append(policies, volcanobatchv1alpha1.LifecyclePolicy{
			Action: volcanobusv1alpha1.AbortJobAction,
			Event:  volcanobusv1alpha1.PodFailedEvent,
		})
	}
	job.Spec.Policies = policies
	var tasks []volcanobatchv1alpha1.TaskSpec
	var tensorboardEnabled bool
	if jobEntity.JobType == jobv1.JobType_PyTorchDDP.String() {
		job.Spec.Volumes = servicehelper.ConvertToVolcanoVolume(jobEntity.PytorchJobTemplate.VolumeSpecs)
		tensorboardEnabled = jobEntity.PytorchJobTemplate.TensorboardEnabled
		masterParams := renderTaskSpecParams{
			jobName:            jobEntity.Name,
			command:            jobEntity.PytorchJobTemplate.Master.Command,
			taskName:           "master",
			taskSpec:           jobEntity.PytorchJobTemplate.Master,
			labels:             labels,
			annotations:        annotations,
			tensorboardEnabled: tensorboardEnabled,
			isDistributed:      true,
			hadoopEnabled:      jobEntity.HadoopEnabled,
			hadoopUsers:        jobEntity.HadoopUsers,
			initImage:          initContainerImageName,
		}
		masterTask := volcanobatchv1alpha1.TaskSpec{
			Name:         jobEntity.PytorchJobTemplate.Master.Name,
			Replicas:     jobEntity.PytorchJobTemplate.Master.Replicas,
			MinAvailable: &jobEntity.PytorchJobTemplate.Master.Replicas,
			Template:     renderDefaultTaskSpec(masterParams),
		}
		mountConfigmap(jobName, &masterTask)
		mountConfigVolumes(jobEntity.PytorchJobTemplate.ConfigSpecs, &masterTask)
		workerParams := renderTaskSpecParams{
			jobName:            jobEntity.Name,
			command:            jobEntity.PytorchJobTemplate.Worker.Command,
			taskName:           "worker",
			taskSpec:           jobEntity.PytorchJobTemplate.Worker,
			labels:             labels,
			annotations:        annotations,
			tensorboardEnabled: tensorboardEnabled,
			isDistributed:      true,
			hadoopEnabled:      jobEntity.HadoopEnabled,
			hadoopUsers:        jobEntity.HadoopUsers,
			initImage:          initContainerImageName,
		}
		workerTask := volcanobatchv1alpha1.TaskSpec{
			Name:         jobEntity.PytorchJobTemplate.Worker.Name,
			Replicas:     jobEntity.PytorchJobTemplate.Worker.Replicas,
			MinAvailable: &jobEntity.PytorchJobTemplate.Worker.Replicas,
			Template:     renderDefaultTaskSpec(workerParams),
		}
		mountConfigmap(jobName, &workerTask)
		mountConfigVolumes(jobEntity.PytorchJobTemplate.ConfigSpecs, &workerTask)
		tasks = append(tasks, masterTask, workerTask)
	}
	if jobEntity.JobType == jobv1.JobType_DeepSpeed.String() {
		job.Spec.Volumes = servicehelper.ConvertToVolcanoVolume(jobEntity.DeepSpeedJobTemplate.VolumeSpecs)
		masterNewCommand := fmt.Sprintf("%s && %s", DeepSpeedMasterCommand, jobEntity.DeepSpeedJobTemplate.Master.Command)
		tensorboardEnabled = jobEntity.DeepSpeedJobTemplate.TensorboardEnabled
		masterParams := renderTaskSpecParams{
			jobName:            jobEntity.Name,
			command:            masterNewCommand,
			taskName:           "master",
			taskSpec:           jobEntity.DeepSpeedJobTemplate.Master,
			labels:             labels,
			annotations:        annotations,
			tensorboardEnabled: tensorboardEnabled,
			isDistributed:      true,
			hadoopEnabled:      jobEntity.HadoopEnabled,
			hadoopUsers:        jobEntity.HadoopUsers,
			initImage:          initContainerImageName,
		}
		masterTask := volcanobatchv1alpha1.TaskSpec{
			Name:         jobEntity.DeepSpeedJobTemplate.Master.Name,
			Replicas:     jobEntity.DeepSpeedJobTemplate.Master.Replicas,
			MinAvailable: &jobEntity.DeepSpeedJobTemplate.Master.Replicas,
			Template:     renderDefaultTaskSpec(masterParams),
			Policies: []volcanobatchv1alpha1.LifecyclePolicy{
				{
					Action: volcanobusv1alpha1.CompleteJobAction,
					Event:  volcanobusv1alpha1.TaskCompletedEvent,
				},
			},
		}
		mountConfigmap(jobName, &masterTask)
		mountConfigVolumes(jobEntity.DeepSpeedJobTemplate.ConfigSpecs, &masterTask)
		workerNewCommand := DeepSpeedWorkerCommand
		workerParams := renderTaskSpecParams{
			jobName:            jobEntity.Name,
			command:            workerNewCommand,
			taskName:           "worker",
			taskSpec:           jobEntity.DeepSpeedJobTemplate.Worker,
			labels:             labels,
			annotations:        annotations,
			tensorboardEnabled: tensorboardEnabled,
			isDistributed:      true,
			hadoopEnabled:      jobEntity.HadoopEnabled,
			hadoopUsers:        jobEntity.HadoopUsers,
			initImage:          initContainerImageName,
		}
		workerTask := volcanobatchv1alpha1.TaskSpec{
			Name:         jobEntity.DeepSpeedJobTemplate.Worker.Name,
			Replicas:     jobEntity.DeepSpeedJobTemplate.Worker.Replicas,
			MinAvailable: &jobEntity.DeepSpeedJobTemplate.Worker.Replicas,
			Template:     renderDefaultTaskSpec(workerParams),
		}
		mountConfigmap(jobName, &workerTask)
		mountConfigVolumes(jobEntity.DeepSpeedJobTemplate.ConfigSpecs, &workerTask)
		tasks = append(tasks, masterTask, workerTask)
	}
	if jobEntity.JobType == jobv1.JobType_SimpleTraining.String() {
		job.Spec.Volumes = servicehelper.ConvertToVolcanoVolume(jobEntity.SimpleTrainingJobTemplate.VolumeSpecs)
		taskSpec := jobEntity.SimpleTrainingJobTemplate.ToTaskSpec()
		tensorboardEnabled = jobEntity.SimpleTrainingJobTemplate.TensorboardEnabled
		taskSpec.Name = "master"
		taskParams := renderTaskSpecParams{
			jobName:            jobEntity.Name,
			command:            jobEntity.SimpleTrainingJobTemplate.Command,
			taskName:           "master",
			taskSpec:           taskSpec,
			labels:             labels,
			annotations:        annotations,
			tensorboardEnabled: tensorboardEnabled,
			isDistributed:      false,
			hadoopEnabled:      jobEntity.HadoopEnabled,
			hadoopUsers:        jobEntity.HadoopUsers,
			initImage:          initContainerImageName,
		}
		task := volcanobatchv1alpha1.TaskSpec{
			Name:         "master",
			Replicas:     1,
			MinAvailable: func(i int32) *int32 { return &i }(1),
			Template:     renderDefaultTaskSpec(taskParams),
		}
		mountConfigVolumes(jobEntity.SimpleTrainingJobTemplate.ConfigSpecs, &task)
		tasks = append(tasks, task)
	}
	job.Spec.Tasks = tasks
	return job
}

func getPriorityClassName(priority string) string {
	return fmt.Sprintf("%s-%s", strings.ToLower(priority), PriorityClassPrefix)
}

func renderJobPlugins(jobType string) map[string][]string {
	if jobType == jobv1.JobType_PyTorchDDP.String() {
		return map[string][]string{
			"pytorch": {"--master=master", "--worker=worker", "--port=23456"},
			"svc":     {},
		}
	}
	if jobType == jobv1.JobType_DeepSpeed.String() {
		return map[string][]string{
			"ssh": {},
			"svc": {},
		}
	}
	return nil
}

func mergeVolumes(globalVolumes, volumes []*model.VolumeSpecEntity) []*model.VolumeSpecEntity {
	if globalVolumes == nil {
		return volumes
	}
	if volumes == nil {
		return globalVolumes
	}

	volumeMap := make(map[string]*model.VolumeSpecEntity)
	for _, volume := range globalVolumes {
		switch volume.Type {
		case common.VolumeSpec_CloudFs.String():
			volumeMap[fmt.Sprintf("%s-%s", volume.Type, volume.CloudFSVolumeProperties.VolumeName)] = volume
		case common.VolumeSpec_CubeFs.String():
			volumeMap[fmt.Sprintf("%s-%s", volume.Type, volume.CubeFSVolumeProperties.VolumeName)] = volume
		case common.VolumeSpec_Dataset.String():
			volumeMap[fmt.Sprintf("%s-%s", volume.Type, volume.DatasetVolumeProperties.DatasetName)] = volume
		}
	}
	for _, volume := range volumes {
		switch volume.Type {
		case common.VolumeSpec_CloudFs.String():
			volumeMap[fmt.Sprintf("%s-%s", volume.Type, volume.CloudFSVolumeProperties.VolumeName)] = volume
		case common.VolumeSpec_CubeFs.String():
			volumeMap[fmt.Sprintf("%s-%s", volume.Type, volume.CubeFSVolumeProperties.VolumeName)] = volume
		case common.VolumeSpec_Dataset.String():
			volumeMap[fmt.Sprintf("%s-%s", volume.Type, volume.DatasetVolumeProperties.DatasetName)] = volume
		}
	}
	var result []*model.VolumeSpecEntity
	for _, volume := range volumeMap {
		result = append(result, volume)
	}
	return result
}

func renderDefaultTaskSpec(params renderTaskSpecParams) corev1.PodTemplateSpec {
	//init all params
	jobName := params.jobName
	newCommand := params.command
	taskName := params.taskName
	taskSpec := params.taskSpec
	labels := params.labels
	annotations := params.annotations
	tensorboardEnabled := params.tensorboardEnabled
	isDistributed := params.isDistributed
	hadoopEnabled := params.hadoopEnabled
	hadoopUsers := params.hadoopUsers
	initImage := params.initImage

	container := corev1.Container{
		Name:            taskName,
		ImagePullPolicy: corev1.PullIfNotPresent,
		Image:           taskSpec.Image,
		Command:         append([]string{"/bin/bash", "-c"}, newCommand),
		SecurityContext: &corev1.SecurityContext{
			Capabilities: &corev1.Capabilities{
				Add: []corev1.Capability{
					"IPC_LOCK",
					"SYS_ADMIN",
				},
			},
		},
		Env: servicehelper.CovertToCoreEnvFromEnvVar(taskSpec.EnvVars, tensorboardEnabled),
		Resources: corev1.ResourceRequirements{
			Requests: corev1.ResourceList{},
			Limits:   corev1.ResourceList{},
		},
	}

	if isDistributed {
		container.SecurityContext.Privileged = util.BoolPtr(true)
	}
	if tensorboardEnabled {
		container.VolumeMounts = append(container.VolumeMounts, corev1.VolumeMount{
			Name:      "tensorboard",
			MountPath: "/opt/tensorboard/logs",
			SubPath:   jobName,
		})
	}
	if taskSpec.Specification.CpuNum != 0 {
		container.Resources.Requests[corev1.ResourceCPU] = resource.MustParse(strconv.Itoa(int(taskSpec.Specification.CpuNum)))
		container.Resources.Limits[corev1.ResourceCPU] = resource.MustParse(strconv.Itoa(int(taskSpec.Specification.CpuNum)))
	} else {
		container.Resources.Requests[corev1.ResourceCPU] = resource.MustParse("0")
		container.Resources.Limits[corev1.ResourceCPU] = resource.MustParse("0")
	}
	if taskSpec.Specification.MemoryGiB != 0 {
		container.Resources.Requests[corev1.ResourceMemory] = resource.MustParse(fmt.Sprintf("%dGi", taskSpec.Specification.MemoryGiB))
		container.Resources.Limits[corev1.ResourceMemory] = resource.MustParse(fmt.Sprintf("%dGi", taskSpec.Specification.MemoryGiB))
	} else {
		container.Resources.Requests[corev1.ResourceMemory] = resource.MustParse("0")
		container.Resources.Limits[corev1.ResourceMemory] = resource.MustParse("0")
	}
	//可以在选择了机型之后，只设置GPU数量，不设置CPU和内存的资源配额了
	if taskSpec.Specification.GpuNum != 0 {
		container.Resources.Requests[constant.ResourceNvidiaGPU] = resource.MustParse(strconv.Itoa(int(taskSpec.Specification.GpuNum)))
		container.Resources.Limits[constant.ResourceNvidiaGPU] = resource.MustParse(strconv.Itoa(int(taskSpec.Specification.GpuNum)))
	} else {
		container.Resources.Requests[constant.ResourceNvidiaGPU] = resource.MustParse("0")
		container.Resources.Limits[constant.ResourceNvidiaGPU] = resource.MustParse("0")
	}
	var nodeAffinity *corev1.NodeAffinity
	// 不再增加队列和工作空间的标签亲和性了，底层直接使用nodeGroup的标签亲和性, 目前队列关联的节点会增加一个queueId的标签, 直接使用nodeGroup插件来实现亲和性调度
	if taskSpec.NodeSpecificationName != "" {
		nodeSpecificationNameHash := util.Md5s(taskSpec.NodeSpecificationName)
		nodeAffinity = &corev1.NodeAffinity{
			RequiredDuringSchedulingIgnoredDuringExecution: &corev1.NodeSelector{
				NodeSelectorTerms: []corev1.NodeSelectorTerm{
					{
						MatchExpressions: []corev1.NodeSelectorRequirement{
							{
								Key:      constant.NodeSpecificationNameHashLabelKey,
								Operator: corev1.NodeSelectorOpIn,
								Values:   []string{nodeSpecificationNameHash},
							},
							{
								Key:      constant.NodeGPUSharedMemoryLabelKey,
								Operator: corev1.NodeSelectorOpDoesNotExist,
							},
						},
					},
				},
			},
		}
	}
	podSpec := corev1.PodSpec{
		RestartPolicy: corev1.RestartPolicyNever,
		Affinity: &corev1.Affinity{
			NodeAffinity: nodeAffinity, // Assuming nodeAffinity is defined elsewhere
		},
		Volumes:     servicehelper.ConvertToCoreVolume(taskSpec.VolumeSpecs, tensorboardEnabled), // Base volumes
		HostNetwork: true,
		HostIPC:     true,
		Tolerations: []corev1.Toleration{
			{
				Key:      "aicp.group/worker",
				Operator: corev1.TolerationOpExists,
				Effect:   corev1.TaintEffectNoSchedule,
			},
			{
				Key:      constant.AIStudioTaintKey,
				Operator: corev1.TolerationOpEqual,
				Value:    "aistudio",
			},
		},
	}

	if hadoopEnabled {
		container.Env = append(container.Env, servicehelper.GenerateHadoopEnvVars(hadoopUsers)...)
		container.VolumeMounts = append(container.VolumeMounts, servicehelper.GenerateHadoopVolumeMounts(hadoopUsers)...)
		container.Lifecycle = &corev1.Lifecycle{
			PostStart: &corev1.LifecycleHandler{
				Exec: &corev1.ExecAction{
					Command: []string{
						"/bin/bash",
						"-c",
						"echo 'PostStart: Creating hadoop user...' && /home/<USER>/job-hadoop-init/create_hadoop.sh",
					},
				},
			},
		}
		podSpec.InitContainers = servicehelper.GenerateHadoopClientInitContainer(initImage)
		podSpec.Volumes = append(podSpec.Volumes, servicehelper.GenerateHadoopVolume(hadoopUsers)...)
	}
	podSpec.Containers = append(podSpec.Containers, container)

	return corev1.PodTemplateSpec{
		ObjectMeta: metav1.ObjectMeta{
			Labels:      labels,
			Annotations: annotations,
		},
		Spec: podSpec,
	}
}

func renderConfigMapObj(workspaceName string, replicas int32, jobName string, slots int32) corev1.ConfigMap {
	namespace := servicehelper.GetClusterNamespaceName(workspaceName)
	hostFile := GenerateHosts(replicas, jobName, slots)
	return corev1.ConfigMap{
		TypeMeta: metav1.TypeMeta{
			Kind:       corev1.SchemeGroupVersion.WithKind("ConfigMap").Kind,
			APIVersion: corev1.SchemeGroupVersion.Version,
		},
		ObjectMeta: metav1.ObjectMeta{
			Namespace: namespace,
			Name:      MakeConfigMapName(jobName),
		},
		Data: hostFile,
	}
}

func mountConfigmap(jobName string, taskSpec *volcanobatchv1alpha1.TaskSpec) {
	cmName := MakeConfigMapName(jobName)
	cmVolume := corev1.Volume{
		Name: cmName,
	}
	cmVolume.ConfigMap = &corev1.ConfigMapVolumeSource{
		LocalObjectReference: corev1.LocalObjectReference{
			Name: cmName,
		},
	}
	taskSpec.Template.Spec.Volumes = append(taskSpec.Template.Spec.Volumes, cmVolume)

	vm := corev1.VolumeMount{
		MountPath: ConfigMapMountPath,
		Name:      cmName,
	}

	for i, c := range taskSpec.Template.Spec.Containers {
		taskSpec.Template.Spec.Containers[i].VolumeMounts = append(c.VolumeMounts, vm)
	}
	for i, c := range taskSpec.Template.Spec.InitContainers {
		taskSpec.Template.Spec.InitContainers[i].VolumeMounts = append(c.VolumeMounts, vm)
	}
}

func getTotalReplicas(taskSpec []*model.TaskSpec) int32 {
	var totalReplicas int32
	for _, t := range taskSpec {
		totalReplicas += t.Replicas
	}
	return totalReplicas
}

func mountConfigVolumes(configSpecEntitys []*model.ConfigSpecEntity, taskSpec *volcanobatchv1alpha1.TaskSpec) {
	if len(configSpecEntitys) == 0 {
		return
	}
	volumes := make([]corev1.Volume, 0, len(configSpecEntitys))
	volumeMounts := make([]corev1.VolumeMount, 0, len(configSpecEntitys))
	for _, configEntity := range configSpecEntitys {
		volume, volumeMount := model.ConvertToVolume(configEntity)
		if volume != nil && volumeMount != nil {
			volumes = append(volumes, *volume)
			volumeMounts = append(volumeMounts, *volumeMount)
		}
	}
	if len(volumes) == 0 {
		return
	}
	taskSpec.Template.Spec.Volumes = append(taskSpec.Template.Spec.Volumes, volumes...)
	for i, c := range taskSpec.Template.Spec.Containers {
		taskSpec.Template.Spec.Containers[i].VolumeMounts = append(c.VolumeMounts, volumeMounts...)
	}
	for i, c := range taskSpec.Template.Spec.InitContainers {
		taskSpec.Template.Spec.InitContainers[i].VolumeMounts = append(c.VolumeMounts, volumeMounts...)
	}
}

func ConvertJobToCreateJobRequest(job *jobv1.Job) *jobv1.CreateJobRequest {
	return &jobv1.CreateJobRequest{
		DisplayName:               job.DisplayName,
		WorkspaceName:             job.WorkspaceName,
		Region:                    job.Region,
		Labels:                    job.Labels,
		Members:                   job.Members,
		JobType:                   job.JobType,
		QueueName:                 job.QueueName,
		Priority:                  jobv1.Priority(job.Priority),
		PyTorchDDPJobTemplate:     job.PyTorchDDPJobTemplate,
		DeepSpeedJobTemplate:      job.DeepSpeedJobTemplate,
		SimpleTrainingJobTemplate: job.SimpleTrainingJobTemplate,
		MaxWaitTime:               job.MaxWaitTime,
		RestartPolicy:             job.RestartPolicy,
		AlarmShielding:            job.AlarmShielding,
		JobTemplateName:           job.JobTemplateName,
	}
}
