package training

import (
	"context"
	"errors"
	"fmt"
	"sort"
	"strings"
	"sync"
	"time"

	"git.lianjia.com/cloudnative/kic/kic-platform/aistudio/pkg/constant"
	"git.lianjia.com/cloudnative/kic/kic-platform/aistudio/pkg/server/domain/model"
	"git.lianjia.com/cloudnative/kic/kic-platform/aistudio/pkg/server/domain/repository"
	"git.lianjia.com/cloudnative/kic/kic-platform/aistudio/pkg/server/domain/service"
	helper2 "git.lianjia.com/cloudnative/kic/kic-platform/aistudio/pkg/server/domain/service/helper"
	"git.lianjia.com/cloudnative/kic/kic-platform/aistudio/pkg/server/domain/service/job/helper"
	"git.lianjia.com/cloudnative/kic/kic-platform/aistudio/pkg/server/domain/service/job/provider"
	"git.lianjia.com/cloudnative/kic/kic-platform/apis/aistudio/bcode"
	jobv1 "git.lianjia.com/cloudnative/kic/kic-platform/apis/aistudio/job/v1"
	apiscommon "git.lianjia.com/cloudnative/kic/kic-platform/apis/common"
	cloudfsv1alpha1 "git.lianjia.com/cloudnative/kic/kic-platform/pkg/apis/cloudfs.kcs.io/v1alpha1"
	schedulingv1alpha1 "git.lianjia.com/cloudnative/kic/kic-platform/pkg/apis/scheduling.kcs.io/v1alpha1"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/infrastructure/datastore"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/multicluster"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/property"
	vela "git.lianjia.com/cloudnative/kic/kic-platform/pkg/third_party/velaux"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/util"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/watcher"
	kratoslog "github.com/go-kratos/kratos/v2/log"
	"go.mongodb.org/mongo-driver/bson/primitive"
	corev1 "k8s.io/api/core/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/fields"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/klog/v2"
	"sigs.k8s.io/controller-runtime/pkg/cache"
	"sigs.k8s.io/controller-runtime/pkg/client"
	volcanobatchv1alpha1 "volcano.sh/apis/pkg/apis/batch/v1alpha1"
	volcanoschedulingv1beta1 "volcano.sh/apis/pkg/apis/scheduling/v1beta1"
)

var _ provider.Provider = (*JobProvider)(nil)

var _ service.Interface = (*JobProvider)(nil)

var _ watcher.ObjectChangedListener = (*JobProvider)(nil)

type JobProvider struct {
	QueueService     *service.QueueService   `inject:"queueService"`
	Store            datastore.DataStore     `inject:"datastore"`
	Logger           *kratoslog.Helper       `inject:"logger"`
	ClusterManager   *service.ClusterManager `inject:"clusterManager"`
	lock             sync.Mutex
	WorkspaceService *service.WorkspaceService `inject:"workspaceService"`
}

func (p *JobProvider) Clean(ctx context.Context, clusterName string, jobID primitive.ObjectID) error {
	jobStatus := &model.JobStatusEntity{
		JobID: jobID,
	}
	err := p.Store.Delete(ctx, jobStatus)
	if err != nil {
		return err
	}
	items, err := p.Store.List(ctx, &model.TaskEntity{
		JobID: jobID,
	}, &datastore.ListOptions{})
	if err != nil {
		return err
	}
	for _, item := range items {
		taskEntity := item.(*model.TaskEntity)
		err := p.Store.Delete(ctx, taskEntity)
		if err != nil {
			return err
		}
	}
	return nil
}

func (p *JobProvider) GetTaskResourceQuantity(taskSpec *model.TaskSpec) *model.ResourceRequestQuantity {
	cpuRequestQuantity := resource.Quantity{}
	memoryRequestQuantity := resource.Quantity{}
	gpuRequestQuantity := resource.Quantity{}
	requestCPU := resource.MustParse(fmt.Sprintf("%d", taskSpec.Specification.CpuNum))
	requestGPU := resource.MustParse(fmt.Sprintf("%d", taskSpec.Specification.GpuNum))
	requestMemory := resource.MustParse(fmt.Sprintf("%dGi", taskSpec.Specification.MemoryGiB))
	cpuRequestQuantity.Add(requestCPU)
	memoryRequestQuantity.Add(requestMemory)
	gpuRequestQuantity.Add(requestGPU)
	return &model.ResourceRequestQuantity{
		CPU:    cpuRequestQuantity,
		Memory: memoryRequestQuantity,
		GPU:    gpuRequestQuantity,
	}
}

func (p *JobProvider) GetJobTaskStatus(ctx context.Context, jobID primitive.ObjectID, jobStatus *model.JobStatusEntity) ([]*jobv1.TaskStatus, error) {
	taskEntities := &model.TaskEntity{
		JobID: jobID,
	}
	items, err := p.Store.List(ctx, taskEntities, &datastore.ListOptions{})
	if err != nil {
		return nil, err
	}
	//var taskStatuses []*jobv1.TaskStatus
	var runningTaskStatus []*jobv1.TaskStatus
	var otherTaskStatus []*jobv1.TaskStatus
	for _, item := range items {
		taskEntity := item.(*model.TaskEntity)
		pod := taskEntity.Raw.Pod
		var restartCount int32
		if pod.Status.ContainerStatuses != nil {
			restartCount += pod.Status.ContainerStatuses[0].RestartCount
		}
		var jobVersion string
		if _, ok := pod.Annotations[constant.VolcanoJobVersionAnnotationKey]; ok {
			jobVersion = pod.Annotations[constant.VolcanoJobVersionAnnotationKey]
		}
		taskStatus := &jobv1.TaskStatus{
			CreateTimestamp:             util.TimeFormat(pod.CreationTimestamp.Time),
			FinishTimestamp:             util.TimeFormat(taskEntity.BaseModel.UpdateTime),
			HistoricalLogStartTimestamp: pod.CreationTimestamp.Unix() * 1000,
			HistoricalLogEndTimestamp:   taskEntity.BaseModel.UpdateTime.Unix() * 1000,
			Phase:                       string(pod.Status.Phase),
			Image:                       pod.Spec.Containers[0].Image,
			PodName:                     pod.Name,
			PodNamespace:                pod.Namespace,
			PodIP:                       pod.Status.PodIP,
			HostIP:                      pod.Status.HostIP,
			NodeIP:                      pod.Spec.NodeName,
			Cluster:                     taskEntity.Cluster,
			RestartCount:                restartCount,
			Reason:                      pod.Status.Reason,
			Message:                     pod.Status.Message,
			Conditions:                  ConvertPodConditionsToTaskConditions(getPodConditions(pod)),
			TaskName:                    pod.Spec.Containers[0].Name,
			TaskEnv:                     taskEntity.TaskEnv,
			TaskID:                      taskEntity.TaskId,
			JobVersion:                  jobVersion,
		}
		//taskStatuses = append(taskStatuses, taskStatus)
		if taskStatus.Phase == "Running" {
			runningTaskStatus = append(runningTaskStatus, taskStatus)
		} else {
			otherTaskStatus = append(otherTaskStatus, taskStatus)
		}
	}
	taskStatuses := append(runningTaskStatus, otherTaskStatus...)
	return taskStatuses, nil
}

func NewJobProvider() *JobProvider {
	jobProvider := &JobProvider{}
	service.RegisterManagedClusterListener("job-object-watcher", jobProvider)
	return jobProvider
}

func (p *JobProvider) OnObjectAdded(ctx context.Context, clusterName string, newObject client.Object) error {
	if pod, ok := newObject.(*corev1.Pod); ok {
		jobName := pod.Labels[constant.VolcanoJobName]
		volcanoJobNamespace := pod.Labels[constant.VolcanoJobNamespace]
		if jobName == "" || volcanoJobNamespace == "" || !strings.Contains(volcanoJobNamespace, "aistudio-") {
			return nil
		}
		pod.SetManagedFields(nil)
		podData, err := util.JSONMarshal(pod)
		if err != nil {
			return err
		}
		klog.Infof("receive task status add event, pod:%s", string(podData))
		err = p.RefreshJobInstanceStatus(ctx, clusterName, pod)
		if err != nil {
			klog.Errorf("refresh job instance status failed: %v", err)
			return err
		}
	}
	if podGroup, ok := newObject.(*volcanoschedulingv1beta1.PodGroup); ok {
		podGroup.SetManagedFields(nil)
		podGroupData, err := util.JSONMarshal(podGroup)
		if err != nil {
			return err
		}
		klog.Infof("receive pod group add event, podGroup:%s", string(podGroupData))
		err = p.RefreshJobStatus(ctx, clusterName, podGroup)
		if err != nil {
			klog.Errorf("refresh job status failed: %v", err)
			return err
		}
	}
	if job, ok := newObject.(*volcanobatchv1alpha1.Job); ok {
		job.SetManagedFields(nil)
		jobData, err := util.JSONMarshal(job)
		if err != nil {
			return err
		}
		klog.Infof("receive job add event, job:%s", string(jobData))
		err = p.RefreshJobStatus(ctx, clusterName, job)
		if err != nil {
			klog.Errorf("refresh job status failed: %v", err)
			return err
		}
	}
	return nil
}

func (p *JobProvider) RefreshJobStatus(ctx context.Context, clusterName string, obj client.Object) error {
	p.lock.Lock()
	defer p.lock.Unlock()
	jobIDHex := obj.GetLabels()[constant.JobIDLabelKey]
	jobID, err := primitive.ObjectIDFromHex(jobIDHex)
	if err != nil {
		return err
	}
	jobEntity := &model.JobEntity{
		ID: jobID,
	}
	err = p.Store.Get(ctx, jobEntity)
	if err != nil {
		if errors.Is(err, datastore.ErrRecordNotExist) {
			klog.Warningf("training job not found,jobID:%s, ignore changed", jobIDHex)
			return nil
		}
		return err
	}
	jobStatusEntity := &model.JobStatusEntity{
		JobID: jobID,
	}
	err = p.Store.Get(ctx, jobStatusEntity)
	if err != nil {
		if errors.Is(err, datastore.ErrRecordNotExist) {
			klog.Warningf("training job status not found,jobID:%s, ignore changed", jobID)
			return nil
		}
	}
	k8sCluster := multicluster.Instance().GetCluster(clusterName)
	if k8sCluster == nil {
		return fmt.Errorf("cluster not found, clusterName:%s", clusterName)
	}
	volcanoJob := &volcanobatchv1alpha1.Job{}
	err = k8sCluster.Direct().Get(ctx, client.ObjectKey{Namespace: jobStatusEntity.Namespace, Name: jobStatusEntity.JobName}, volcanoJob)
	if err != nil {
		if apierrors.IsNotFound(err) {
			klog.Warningf("volcano job not found,jobID:%s, ignore changed", jobID)
			return nil
		}
	}
	// update podGroup
	podGroupList := &volcanoschedulingv1beta1.PodGroupList{}
	err = k8sCluster.Direct().List(ctx, podGroupList, client.MatchingLabels{constant.JobIDLabelKey: jobIDHex}, client.InNamespace(jobStatusEntity.Namespace))
	if err != nil {
		klog.Errorf("list podGroup failed: %v", err)
		return err
	}
	var podGroup *volcanoschedulingv1beta1.PodGroup
	if len(podGroupList.Items) > 0 {
		podGroup = &podGroupList.Items[0]
	} else {
		klog.Errorf("podGroup not found for job %s, ignore update", jobIDHex)
	}

	if podGroup != nil && podGroup.Status.Phase == volcanoschedulingv1beta1.PodGroupPending {
		jobStatusEntity.Phase = model.JobStatePending
		jobStatusEntity.Reason = volcanoJob.Status.State.Reason
		jobStatusEntity.Message = volcanoJob.Status.State.Message
		jobStatusEntity.UpdateTime = volcanoJob.Status.State.LastTransitionTime.Time
		jobStatusEntity.Conditions.UpsertCondition(metav1.Condition{
			Type:               model.JobConditionTypeInQueueReady,
			Status:             metav1.ConditionTrue,
			Reason:             "PodGroupPending",
			Message:            "Waiting For PodGroup InQueue",
			LastTransitionTime: metav1.Now(),
		})
	} else if podGroup != nil && podGroup.Status.Phase == volcanoschedulingv1beta1.PodGroupInqueue {
		jobStatusEntity.Phase = model.JobStateInQueue
		jobStatusEntity.Reason = volcanoJob.Status.State.Reason
		jobStatusEntity.Message = volcanoJob.Status.State.Message
		jobStatusEntity.UpdateTime = volcanoJob.Status.State.LastTransitionTime.Time
		jobStatusEntity.Conditions.UpsertCondition(metav1.Condition{
			Type:               model.JobConditionTypeInQueueReady,
			Status:             metav1.ConditionTrue,
			Reason:             "PodGroupInQueue",
			Message:            "PodGroup Status is InQueue",
			LastTransitionTime: metav1.Now(),
		})
	} else if podGroup != nil && podGroup.Status.Phase == volcanoschedulingv1beta1.PodGroupRunning {
		jobStatusEntity.Phase = model.JobStateInQueue
		jobStatusEntity.Reason = volcanoJob.Status.State.Reason
		jobStatusEntity.Message = volcanoJob.Status.State.Message
		jobStatusEntity.UpdateTime = volcanoJob.Status.State.LastTransitionTime.Time
		jobStatusEntity.Conditions.UpsertConditionIfStatusChanged(metav1.Condition{
			Type:               model.JobConditionTypeJobScheduledReady,
			Status:             metav1.ConditionTrue,
			Reason:             "PodGroupRunning",
			Message:            "PodGroup is Running",
			LastTransitionTime: metav1.Now(),
		})
	}
	if err := p.Store.Put(ctx, jobStatusEntity); err != nil {
		klog.Errorf("put job status entity failed: %v", err)
	}

	// update volcanoJob
	runningDuration := jobStatusEntity.RunningDuration
	if volcanoJob.Status.State.Phase == volcanobatchv1alpha1.Running {
		jobStatusEntity.Phase = model.JobStateRunning
		jobStatusEntity.JobVersion = volcanoJob.Status.Version
		jobStatusEntity.Reason = volcanoJob.Status.State.Reason
		jobStatusEntity.Message = volcanoJob.Status.State.Message
		jobStatusEntity.UpdateTime = volcanoJob.Status.State.LastTransitionTime.Time
		jobStatusEntity.Conditions.UpsertConditionIfStatusChanged(metav1.Condition{
			Type:               model.JobConditionTypeJobScheduledReady,
			Status:             metav1.ConditionTrue,
			Reason:             "Scheduled",
			Message:            "Job Scheduled",
			LastTransitionTime: metav1.Now(),
		})

	} else if volcanoJob.Status.State.Phase == volcanobatchv1alpha1.Completed {
		jobStatusEntity.Phase = model.JobStateCompleted
		jobStatusEntity.JobVersion = volcanoJob.Status.Version
		// 运行时间
		runningDuration = getVolcanoJobRunningDuration(volcanoJob.Status.Conditions, volcanobatchv1alpha1.Completed)
		jobStatusEntity.RunningDuration = runningDuration
		jobStatusEntity.Reason = volcanoJob.Status.State.Reason
		jobStatusEntity.Message = volcanoJob.Status.State.Message
		jobStatusEntity.UpdateTime = volcanoJob.Status.State.LastTransitionTime.Time
		jobStatusEntity.Conditions.UpsertConditionIfStatusChanged(metav1.Condition{
			Type:               model.JobConditionTypeJobFinishedReady,
			Status:             metav1.ConditionTrue,
			Reason:             "Completed",
			Message:            "Job Completed",
			LastTransitionTime: metav1.Now(),
		})
	} else if volcanoJob.Status.State.Phase == volcanobatchv1alpha1.Failed {
		runningDuration = getVolcanoJobRunningDuration(volcanoJob.Status.Conditions, volcanobatchv1alpha1.Failed)
		jobStatusEntity.Phase = model.JobStateFailed
		jobStatusEntity.JobVersion = volcanoJob.Status.Version
		jobStatusEntity.Reason = volcanoJob.Status.State.Reason
		jobStatusEntity.Message = volcanoJob.Status.State.Message
		jobStatusEntity.UpdateTime = volcanoJob.Status.State.LastTransitionTime.Time
		jobStatusEntity.Conditions.UpsertConditionIfStatusChanged(metav1.Condition{
			Type:               model.JobConditionTypeJobFinishedReady,
			Status:             metav1.ConditionFalse,
			Reason:             "JobFailed",
			Message:            "JobFailed",
			LastTransitionTime: metav1.Now(),
		})
	} else if volcanoJob.Status.State.Phase == volcanobatchv1alpha1.Aborted {
		// 有一个pod失败就中止
		runningDuration = getVolcanoJobRunningDuration(volcanoJob.Status.Conditions, volcanobatchv1alpha1.Aborted)
		jobStatusEntity.Phase = model.JobStateAborted
		if volcanoJob.Status.Failed > 0 {
			jobStatusEntity.Phase = model.JobStateFailed
		}
		jobStatusEntity.JobVersion = volcanoJob.Status.Version
		jobStatusEntity.Reason = volcanoJob.Status.State.Reason
		jobStatusEntity.Message = fmt.Sprintf("Job aborted by system, failed count: %v", volcanoJob.Status.Failed)
		jobStatusEntity.UpdateTime = volcanoJob.Status.State.LastTransitionTime.Time
		jobStatusEntity.Conditions.UpsertConditionIfStatusChanged(metav1.Condition{
			Type:               model.JobConditionTypeJobFinishedReady,
			Status:             metav1.ConditionFalse,
			Reason:             "JobAborted",
			Message:            "JobAborted",
			LastTransitionTime: metav1.Now(),
		})
	} else if volcanoJob.Status.State.Phase == volcanobatchv1alpha1.Aborting {
		jobStatusEntity.Phase = model.JobStateAborting
		jobStatusEntity.JobVersion = volcanoJob.Status.Version
		jobStatusEntity.Reason = volcanoJob.Status.State.Reason
		jobStatusEntity.Message = volcanoJob.Status.State.Message
		jobStatusEntity.UpdateTime = volcanoJob.Status.State.LastTransitionTime.Time
		jobStatusEntity.Conditions.UpsertConditionIfStatusChanged(metav1.Condition{
			Type:               model.JobConditionTypeJobFinishedReady,
			Status:             metav1.ConditionFalse,
			Reason:             "JobAborting",
			Message:            "JobAborting",
			LastTransitionTime: metav1.Now(),
		})
	} else if volcanoJob.Status.State.Phase == volcanobatchv1alpha1.Restarting {
		jobStatusEntity.Phase = model.JobStateRestarting
		jobStatusEntity.JobVersion = volcanoJob.Status.Version
		jobStatusEntity.Reason = volcanoJob.Status.State.Reason
		jobStatusEntity.Message = volcanoJob.Status.State.Message
		jobStatusEntity.UpdateTime = volcanoJob.Status.State.LastTransitionTime.Time
		jobStatusEntity.Conditions.UpsertConditionIfStatusChanged(metav1.Condition{
			Type:               model.JobConditionTypeJobFinishedReady,
			Status:             metav1.ConditionFalse,
			Reason:             "JobRestarting",
			Message:            "Job Restarting",
			LastTransitionTime: metav1.Now(),
		})
	} else if volcanoJob.Status.State.Phase == volcanobatchv1alpha1.Terminating {
		jobStatusEntity.Phase = model.JobStateTerminating
		jobStatusEntity.JobVersion = volcanoJob.Status.Version
		jobStatusEntity.Reason = volcanoJob.Status.State.Reason
		jobStatusEntity.Message = volcanoJob.Status.State.Message
		jobStatusEntity.UpdateTime = volcanoJob.Status.State.LastTransitionTime.Time
		jobStatusEntity.Conditions.UpsertConditionIfStatusChanged(metav1.Condition{
			Type:               model.JobConditionTypeJobFinishedReady,
			Status:             metav1.ConditionFalse,
			Reason:             "JobTerminating",
			Message:            "Job Terminating",
			LastTransitionTime: metav1.Now(),
		})
	} else if volcanoJob.Status.State.Phase == volcanobatchv1alpha1.Terminated {
		runningDuration = getVolcanoJobRunningDuration(volcanoJob.Status.Conditions, volcanobatchv1alpha1.Terminated)
		jobStatusEntity.Phase = model.JobStateTerminated
		jobStatusEntity.JobVersion = volcanoJob.Status.Version
		jobStatusEntity.Reason = volcanoJob.Status.State.Reason
		jobStatusEntity.Message = volcanoJob.Status.State.Message
		jobStatusEntity.UpdateTime = volcanoJob.Status.State.LastTransitionTime.Time
		jobStatusEntity.Conditions.UpsertConditionIfStatusChanged(metav1.Condition{
			Type:               model.JobConditionTypeJobFinishedReady,
			Status:             metav1.ConditionFalse,
			Reason:             "JobTerminated",
			Message:            "Job Terminated",
			LastTransitionTime: metav1.Now(),
		})
	}
	jobStatusEntity.RetryCount = volcanoJob.Status.RetryCount
	jobStatusEntity.VolcanoJobConditions = volcanoJob.Status.Conditions
	jobStatusEntity.RunningDuration = runningDuration
	return p.Store.Put(ctx, jobStatusEntity)
}

// RefreshJobInstanceStatus 刷新任务实例状态
func (p *JobProvider) RefreshJobInstanceStatus(ctx context.Context, clusterName string, pod *corev1.Pod) error {
	p.lock.Lock()
	defer p.lock.Unlock()

	jobName := pod.Labels[constant.VolcanoJobName]
	jobWorkspace := pod.Labels[constant.VolcanoJobNamespace][len("aistudio-"):]
	jobEntity, err := repository.GetJobForWorkspace(ctx, p.Store, jobWorkspace, jobName)
	if err != nil {
		if errors.Is(datastore.ErrRecordNotExist, err) {
			klog.Infof("job %s/%s is not exist, ignore update", jobWorkspace, jobName)
			return nil
		}
		return err
	}
	nodeMetricList := &schedulingv1alpha1.NodeMetricList{}
	var nodeMetric *schedulingv1alpha1.NodeMetric
	if pod.Spec.NodeName != "" {
		err = multicluster.Instance().GetLocalCluster().Direct().List(ctx, nodeMetricList, client.MatchingFields{
			"nodeName": pod.Spec.NodeName,
		})
		if err != nil {
			klog.Errorf("get node metric failed, err: %v", err)
			nodeMetricList.Items = []schedulingv1alpha1.NodeMetric{}
		}
		if len(nodeMetricList.Items) > 0 {
			nodeMetric = &nodeMetricList.Items[0]
		}
	}
	jobTaskEntity := &model.TaskEntity{
		TaskId: string(pod.UID),
		JobID:  jobEntity.ID,
	}
	err = p.Store.Get(ctx, jobTaskEntity)
	jobTaskEntity = PodToTaskEntity(clusterName, jobEntity, nodeMetric, *pod)
	if err != nil {
		if errors.Is(err, datastore.ErrRecordNotExist) {
			err = p.Store.Add(ctx, jobTaskEntity)
			if err != nil {
				return err
			}
		}
		return err
	}
	return p.Store.Put(ctx, jobTaskEntity)
}

func PodToTaskEntity(clusterName string, jobEntity *model.JobEntity, node *schedulingv1alpha1.NodeMetric, pod corev1.Pod) *model.TaskEntity {
	var zone string
	if node != nil {
		zone = node.Labels[constant.ZoneLabelKey]
	}
	podCopy := pod.DeepCopy()
	podCopy.ObjectMeta.ManagedFields = nil
	taskEnv := helper.GetVelaEnvNameFromJob(jobEntity)
	taskEntity := &model.TaskEntity{
		TaskId:        string(pod.UID),
		PodName:       pod.Name,
		PodNamespace:  pod.Namespace,
		JobID:         jobEntity.ID,
		WorkspaceName: jobEntity.WorkspaceName,
		QueueName:     jobEntity.QueueName,
		JobName:       jobEntity.Name,
		Cluster:       clusterName,
		Region:        jobEntity.Region,
		Raw:           &model.TaskRaw{Pod: podCopy},
		Zone:          zone,
		IsDeleted:     false,
		TaskEnv:       taskEnv,
	}
	return taskEntity
}

func (p *JobProvider) DeleteJobInstance(ctx context.Context, pod *corev1.Pod) error {
	taskEntity := model.TaskEntity{
		TaskId:  string(pod.UID),
		PodName: pod.Name,
	}
	err := p.Store.Get(ctx, &taskEntity)
	if err != nil {
		return err
	}
	taskEntity.IsDeleted = true
	if taskEntity.Raw != nil {
		taskEntity.Raw.Pod.Status.Phase = corev1.PodFailed
	}
	return p.Store.Put(ctx, &taskEntity)
}

func (p *JobProvider) OnObjectUpdated(ctx context.Context, clusterName string, newObject, oldObject client.Object) error {
	if pod, ok := newObject.(*corev1.Pod); ok {
		jobName := pod.Labels[constant.VolcanoJobName]
		volcanoJobNamespace := pod.Labels[constant.VolcanoJobNamespace]
		if jobName == "" || volcanoJobNamespace == "" || !strings.Contains(volcanoJobNamespace, "aistudio-") {
			return nil
		}
		pod.SetManagedFields(nil)
		podData, err := util.JSONMarshal(pod)
		if err != nil {
			return err
		}
		klog.Infof("receive task status add event, pod:%s", string(podData))
		err = p.RefreshJobInstanceStatus(ctx, clusterName, pod)
		if err != nil {
			klog.Errorf("refresh job instance status failed: %v", err)
			return err
		}
	}
	if podGroup, ok := newObject.(*volcanoschedulingv1beta1.PodGroup); ok {
		podGroup.SetManagedFields(nil)
		podGroupData, err := util.JSONMarshal(podGroup)
		if err != nil {
			return err
		}
		klog.Infof("receive pod group update event, podGroup:%s", string(podGroupData))
		err = p.RefreshJobStatus(ctx, clusterName, podGroup)
		if err != nil {
			klog.Errorf("refresh job status failed: %v", err)
			return err
		}
	}
	if job, ok := newObject.(*volcanobatchv1alpha1.Job); ok {
		job.SetManagedFields(nil)
		jobData, err := util.JSONMarshal(job)
		if err != nil {
			return err
		}
		klog.Infof("receive job update event, job:%s", string(jobData))
		err = p.RefreshJobStatus(ctx, clusterName, job)
		if err != nil {
			klog.Errorf("refresh job status failed: %v", err)
			return err
		}
	}
	return nil
}

func (p *JobProvider) OnObjectDeleted(ctx context.Context, clusterName string, oldObject client.Object) error {
	if pod, ok := oldObject.(*corev1.Pod); ok {
		klog.Infof("receive pod delete event, pod:%s", pod.Name)
		err := p.DeleteJobInstance(ctx, pod)
		if err != nil {
			klog.Errorf("delete job instance failed: %v", err)
			return err
		}
	}
	if podGroup, ok := oldObject.(*volcanoschedulingv1beta1.PodGroup); ok {
		klog.Infof("pod group deleted: %s", podGroup.Name)
	}
	if job, ok := oldObject.(*volcanobatchv1alpha1.Job); ok {
		klog.Infof("job deleted: %s", job.Name)
		// TODO 资源回收
	}
	return nil
}

func (p *JobProvider) GetWatchedObjectList() []watcher.WatchObject {
	return []watcher.WatchObject{
		{
			Object: &volcanobatchv1alpha1.Job{},
			ObjectSelector: &cache.ObjectSelector{
				Label: labels.SelectorFromSet(map[string]string{
					constant.KICManagedLabelKey: "kic",
				}),
			},
		},
		{
			Object: &volcanoschedulingv1beta1.PodGroup{},
		},
		{
			Object: &corev1.Pod{},
			ObjectSelector: &cache.ObjectSelector{
				Label: labels.SelectorFromSet(map[string]string{
					constant.KICManagedLabelKey: "kic",
				}),
			},
		},
	}
}

func (p *JobProvider) Init(ctx context.Context, properties property.EnvironmentProperty) error {
	return nil
}

func (p *JobProvider) CheckRequestValidation(ctx context.Context, request *jobv1.CreateJobRequest) error {
	err := p.checkVolumesIsAvailable(ctx, request)
	if err != nil {
		return bcode.ErrorInvalidArgument("volumes is not available: %v", err)
	}
	return nil
}

func (p *JobProvider) MergeVolumeSpecs(globalVolumes []*apiscommon.VolumeSpec, volumes []*apiscommon.VolumeSpec) []*apiscommon.VolumeSpec {
	if globalVolumes == nil {
		return volumes
	}
	if volumes == nil {
		return globalVolumes
	}
	var mergedVolumes = make(map[string]*apiscommon.VolumeSpec, 2)
	for _, v := range globalVolumes {
		switch v.Type {
		case apiscommon.VolumeSpec_CloudFs:
			mergedVolumes[fmt.Sprintf("%s:%s", v.Type, v.GetCloudFSVolumeProperties().VolumeName)] = v
		case apiscommon.VolumeSpec_Dataset:
			mergedVolumes[fmt.Sprintf("%s:%s", v.Type, v.GetDatasetVolumeProperties().DatasetName)] = v
		case apiscommon.VolumeSpec_CubeFs:
			mergedVolumes[fmt.Sprintf("%s:%s", v.Type, v.GetCubeFSVolumeProperties().VolumeName)] = v
		}
	}
	for _, v := range volumes {
		switch v.Type {
		case apiscommon.VolumeSpec_CloudFs:
			key := fmt.Sprintf("%s:%s", v.Type, v.GetCloudFSVolumeProperties().VolumeName)
			mergedVolumes[key] = v
		case apiscommon.VolumeSpec_CubeFs:
			mergedVolumes[fmt.Sprintf("%s:%s", v.Type, v.GetCubeFSVolumeProperties().VolumeName)] = v
		case apiscommon.VolumeSpec_Dataset:
			mergedVolumes[fmt.Sprintf("%s:%s", v.Type, v.GetDatasetVolumeProperties().DatasetName)] = v
		}
	}
	var result []*apiscommon.VolumeSpec
	for _, v := range mergedVolumes {
		result = append(result, v)
	}
	return result
}

func checkCubeVolumes(ctx context.Context, workspace string, volumes []*apiscommon.VolumeSpec) error {
	if volumes == nil {
		return nil
	}
	for _, v := range volumes {
		if v.Type != apiscommon.VolumeSpec_CubeFs {
			return bcode.ErrorInvalidArgument("volume type[%s] is not supported", v.Type)
		}
		cubeFSVolume := &cloudfsv1alpha1.CubeFSVolume{}
		err := multicluster.Instance().GetLocalCluster().Direct().Get(ctx, client.ObjectKey{Namespace: helper2.GetClusterNamespaceName(workspace), Name: v.GetCubeFSVolumeProperties().VolumeName}, cubeFSVolume)
		if err != nil {
			if apierrors.IsNotFound(err) {
				return bcode.ErrorCubeFSVolumeNotFound("cube fs volume[%s] not found", v.GetCubeFSVolumeProperties().VolumeName)
			}
			return bcode.ErrorServerInternalError("get cube fs volume[%s] failed: %v", v.GetCubeFSVolumeProperties().VolumeName, err)
		}
	}
	return nil
}

func (p *JobProvider) checkVolumesIsAvailable(ctx context.Context, request *jobv1.CreateJobRequest) error {
	switch request.JobType {
	case jobv1.JobType_PyTorchDDP:
		masterVolumes := p.MergeVolumeSpecs(request.PyTorchDDPJobTemplate.VolumeSpecs, request.PyTorchDDPJobTemplate.Master.VolumeSpecs)
		err := checkCubeVolumes(ctx, request.WorkspaceName, masterVolumes)
		if err != nil {
			return err
		}
		workerVolumes := p.MergeVolumeSpecs(request.PyTorchDDPJobTemplate.VolumeSpecs, request.PyTorchDDPJobTemplate.Worker.VolumeSpecs)
		return checkCubeVolumes(ctx, request.WorkspaceName, workerVolumes)
	case jobv1.JobType_SimpleTraining:
		return checkCubeVolumes(ctx, request.WorkspaceName, request.SimpleTrainingJobTemplate.VolumeSpecs)
	case jobv1.JobType_DeepSpeed:
		masterVolumes := p.MergeVolumeSpecs(request.DeepSpeedJobTemplate.VolumeSpecs, request.DeepSpeedJobTemplate.Master.VolumeSpecs)
		err := checkCubeVolumes(ctx, request.WorkspaceName, masterVolumes)
		if err != nil {
			return err
		}
		workerVolumes := p.MergeVolumeSpecs(request.DeepSpeedJobTemplate.VolumeSpecs, request.DeepSpeedJobTemplate.Worker.VolumeSpecs)
		return checkCubeVolumes(ctx, request.WorkspaceName, workerVolumes)
	}
	return nil
}

func (p *JobProvider) Submit(ctx context.Context, clusterName string, jobEntity *model.JobEntity) error {
	jobStatusEntity := &model.JobStatusEntity{
		JobID: jobEntity.ID,
	}
	err := p.Store.Get(ctx, jobStatusEntity)
	if err != nil {
		return err
	}
	return helper.CreateVolcanoTrainingJobWithApplication(ctx, clusterName, jobEntity, jobStatusEntity)
}

func (p *JobProvider) Cancel(ctx context.Context, jobID string) error {
	//TODO implement me
	panic("implement me")
}

func (p *JobProvider) GetJobStatus(ctx context.Context, jobID primitive.ObjectID) (*jobv1.JobStatus, error) {
	jobEntity, err := repository.GetJobForJobID(ctx, p.Store, jobID)
	if err != nil {
		if errors.Is(datastore.ErrRecordNotExist, err) {
			klog.Infof("job[%s] not found", jobID.Hex())
			return nil, nil
		}
		return nil, err
	}

	jobStatusEntity, err := repository.GetJobStatusForJobID(ctx, p.Store, jobID)
	if err != nil {
		return nil, err
	}
	jobStatus := &jobv1.JobStatus{
		RunningDuration:    jobStatusEntity.RunningDuration,
		State:              jobStatusEntity.Phase,
		Reason:             jobStatusEntity.Reason,
		Message:            jobStatusEntity.Message,
		Conditions:         ConvertJobConditionsToJobStatusConditions(jobStatusEntity.Conditions),
		CreateTimestamp:    util.TimeFormat(jobStatusEntity.CreateTime),
		LastTransitionTime: util.TimeFormat(jobStatusEntity.UpdateTime),
		ClusterName:        jobStatusEntity.ClusterName,
	}

	if jobStatusEntity.SubmitState != model.Submitted {
		return jobStatus, nil
	}
	// 当任务 pending 时，查询 vela状态,可能会是 vela 创建失败导致
	if jobStatusEntity.Phase == model.JobStatePending {
		if err = p.updateJobStatusByVelaApp(ctx, jobEntity, jobStatusEntity); err != nil {
			return jobStatus, err
		}
	}
	var runningDuration string
	if jobStatusEntity.RunningDuration != "" {
		runningDuration = jobStatusEntity.RunningDuration
	} else {
		runningDuration = getJobRunningDuration(jobStatusEntity, runningDuration)
	}
	jobStatus.RunningDuration = runningDuration
	jobStatus.RetryCount = jobStatusEntity.RetryCount
	return jobStatus, nil
}

func (p *JobProvider) updateJobStatusByVelaApp(ctx context.Context, jobEntity *model.JobEntity, jobStatusEntity *model.JobStatusEntity) error {
	appName := jobStatusEntity.JobName
	envName := helper.GetVelaEnvNameFromJob(jobEntity)
	appStatus, err := vela.Instance().GetApplicationStatus(ctx, appName, envName)
	if err != nil {
		if errors.Is(err, vela.ErrorApplicationNotFound) {
			jobStatusEntity.Reason = "VelaJobNotFound"
		} else {
			klog.Errorf("failed to get application %s status: %v", appName, err)
			jobStatusEntity.Message = "VelaJobGetStatusFailed, err: %v"
			jobStatusEntity.Reason = "VelaJobGetStatusFailed"
		}
	}
	if appStatus != nil && appStatus.Status != nil && appStatus.Status.Phase == "workflowFailed" {
		if jobStatusEntity.Phase == model.JobStateFailed {
			return nil
		}
		jobStatusEntity.Phase = model.JobStateFailed
		jobStatusEntity.Reason = "VelaWorkflowFailed"
		name := fmt.Sprintf("deploy-%s", jobStatusEntity.JobName)
		stepStatus := helper2.FindWorkflowStepStatusByName(appStatus.Status.Workflow.Steps, name)
		if stepStatus != nil {
			jobStatusEntity.Message = stepStatus.StepStatus.Message
		} else {
			jobStatusEntity.Message = "vela workflow failed"
		}
		if err = p.Store.Put(ctx, jobStatusEntity); err != nil {
			return err
		}
	}
	return nil
}

func (p *JobProvider) GetJobVolumes(ctx context.Context, jobID primitive.ObjectID) ([]*jobv1.JobVolume, error) {
	jobEntity, err := repository.GetJobForJobID(ctx, p.Store, jobID)
	if err != nil {
		if errors.Is(datastore.ErrRecordNotExist, err) {
			klog.Infof("job[%s] not found", jobID.Hex())
			return nil, nil
		}
		return nil, err
	}
	var jobVolumes []*jobv1.JobVolume
	switch jobEntity.JobType {
	case jobv1.JobType_PyTorchDDP.String():
		globalJobVolumes := helper.VolumeModelToJobVolumes("global", jobEntity.PytorchJobTemplate.VolumeSpecs)
		jobVolumes = append(jobVolumes, globalJobVolumes...)

		masterJobVolumes := helper.VolumeModelToJobVolumes("master", jobEntity.PytorchJobTemplate.Master.VolumeSpecs)
		jobVolumes = append(jobVolumes, masterJobVolumes...)

		workerJobVolumes := helper.VolumeModelToJobVolumes("worker", jobEntity.PytorchJobTemplate.Worker.VolumeSpecs)
		jobVolumes = append(jobVolumes, workerJobVolumes...)

	case jobv1.JobType_DeepSpeed.String():
		globalJobVolumes := helper.VolumeModelToJobVolumes("global", jobEntity.DeepSpeedJobTemplate.VolumeSpecs)
		jobVolumes = append(jobVolumes, globalJobVolumes...)

		masterJobVolumes := helper.VolumeModelToJobVolumes("master", jobEntity.DeepSpeedJobTemplate.Master.VolumeSpecs)
		jobVolumes = append(jobVolumes, masterJobVolumes...)

		workerJobVolumes := helper.VolumeModelToJobVolumes("worker", jobEntity.DeepSpeedJobTemplate.Worker.VolumeSpecs)
		jobVolumes = append(jobVolumes, workerJobVolumes...)
	case jobv1.JobType_SimpleTraining.String():
		globalJobVolumes := helper.VolumeModelToJobVolumes("global", jobEntity.SimpleTrainingJobTemplate.VolumeSpecs)
		jobVolumes = append(jobVolumes, globalJobVolumes...)
	}
	return jobVolumes, nil
}

func getJobRunningDuration(jobStatusEntity *model.JobStatusEntity, runningDuration string) string {
	vcJobConditions := jobStatusEntity.VolcanoJobConditions
	jobPhase := jobStatusEntity.Phase
	if jobPhase == model.JobStateRunning {
		condition := model.FindVolcanoLatestCondition(vcJobConditions, volcanobatchv1alpha1.Running)
		lastTransitionTime := condition.LastTransitionTime
		if lastTransitionTime == nil {
			lastTransitionTime = &metav1.Time{Time: jobStatusEntity.UpdateTime}
		}
		runningDuration = time.Now().Sub(lastTransitionTime.Time).String()
	} else {
		runningDuration = getVolcanoJobRunningDuration(vcJobConditions, convertToVolcanoJobPhase(jobPhase))
	}
	return runningDuration
}

func convertToVolcanoJobPhase(jobPhase string) volcanobatchv1alpha1.JobPhase {
	phaseMap := map[string]volcanobatchv1alpha1.JobPhase{
		model.JobStatePending:     volcanobatchv1alpha1.Pending,
		model.JobStateRunning:     volcanobatchv1alpha1.Running,
		model.JobStateCompleted:   volcanobatchv1alpha1.Completed,
		model.JobStateInQueue:     volcanobatchv1alpha1.Pending,
		model.JobStateFailed:      volcanobatchv1alpha1.Failed,
		model.JobStateAborted:     volcanobatchv1alpha1.Aborted,
		model.JobStateAborting:    volcanobatchv1alpha1.Aborting,
		model.JobStateRestarting:  volcanobatchv1alpha1.Restarting,
		model.JobStateTerminating: volcanobatchv1alpha1.Terminating,
		model.JobStateTerminated:  volcanobatchv1alpha1.Terminated,
	}
	if phase, ok := phaseMap[jobPhase]; ok {
		return phase
	}
	return volcanobatchv1alpha1.Pending
}

func ConvertJobConditionsToJobStatusConditions(conditions model.Conditions) []*jobv1.JobCondition {
	jobStatusConditions := make([]*jobv1.JobCondition, 0)
	for _, condition := range conditions {
		jobStatusConditions = append(jobStatusConditions, &jobv1.JobCondition{
			Phase:              condition.Type,
			ConditionStatus:    ConvertConditionStatusToJobConditionStatus(condition.Status),
			LastTransitionTime: util.TimeFormat(condition.LastTransitionTime.Time),
			Reason:             condition.Reason,
			Message:            condition.Message,
		})
	}
	return jobStatusConditions
}

func ConvertConditionStatusToJobConditionStatus(conditionStatus metav1.ConditionStatus) jobv1.JobCondition_ConditionStatus {
	switch conditionStatus {
	case metav1.ConditionTrue:
		return jobv1.JobCondition_True
	case metav1.ConditionFalse:
		return jobv1.JobCondition_False
	case metav1.ConditionUnknown:
		return jobv1.JobCondition_Unknown
	}
	return jobv1.JobCondition_Unknown
}

func ConvertPodConditionsToTaskConditions(conditions []corev1.PodCondition) []*jobv1.TaskCondition {
	taskConditions := make([]*jobv1.TaskCondition, 0)
	for _, condition := range conditions {
		taskConditions = append(taskConditions, &jobv1.TaskCondition{
			Type:               string(condition.Type),
			Status:             string(condition.Status),
			LastTransitionTime: util.TimeFormat(condition.LastTransitionTime.Time),
			Reason:             condition.Reason,
			Message:            condition.Message,
		})
	}
	return taskConditions
}

func getVolcanoJobRunningDuration(conditions []volcanobatchv1alpha1.JobCondition, jobPhase volcanobatchv1alpha1.JobPhase) string {
	lastCondition := model.FindVolcanoLatestCondition(conditions, jobPhase)
	runningCondition := model.FindVolcanoLatestCondition(conditions, volcanobatchv1alpha1.Running)
	if lastCondition.LastTransitionTime != nil && runningCondition.LastTransitionTime != nil {
		duration := &metav1.Duration{Duration: lastCondition.LastTransitionTime.Time.Sub(runningCondition.LastTransitionTime.Time)}
		return duration.Duration.String()
	}
	return ""
}

func getPodConditions(pod *corev1.Pod) []corev1.PodCondition {

	odlConditions := model.PodConditions(pod.Status.Conditions)
	conditions := odlConditions.CopyConditions()

	podScheduledCondition := conditions.FindCondition(corev1.PodScheduled)
	if podScheduledCondition != nil {
		switch podScheduledCondition.Status {
		case corev1.ConditionTrue:
			conditions.UpsertCondition(corev1.PodCondition{
				Type:               corev1.PodScheduled,
				Status:             "True",
				Reason:             "PodScheduled",
				Message:            "Pod已调度到节点",
				LastTransitionTime: conditions[0].LastTransitionTime,
			})
		case corev1.ConditionFalse:
			conditions.UpsertCondition(corev1.PodCondition{
				Type:               corev1.PodScheduled,
				Status:             "False",
				Reason:             "PodScheduled",
				Message:            "Pod调度到节点失败",
				LastTransitionTime: conditions[0].LastTransitionTime,
			})
		default:
			conditions.UpsertCondition(corev1.PodCondition{
				Type:               corev1.PodScheduled,
				Status:             "Unknown",
				Reason:             "PodScheduled",
				Message:            "Pod调度到节点未知",
				LastTransitionTime: conditions[0].LastTransitionTime,
			})
		}
	}

	// 处理 PodInitialized 条件
	initializedCondition := conditions.FindCondition(corev1.PodInitialized)
	if initializedCondition != nil {
		switch initializedCondition.Status {
		case corev1.ConditionTrue:
			conditions.UpsertCondition(corev1.PodCondition{
				Type:               corev1.PodInitialized,
				Status:             corev1.ConditionTrue,
				Reason:             "PodInitialized",
				Message:            "Pod已初始化",
				LastTransitionTime: initializedCondition.LastTransitionTime,
			})
		case corev1.ConditionFalse:
			message := "Pod初始化容器失败"
			if ok, res := checkInitContainer(pod); !ok {
				message = fmt.Sprintf("Pod初始化容器失败：%s", res)
			}
			conditions.UpsertCondition(corev1.PodCondition{
				Type:               corev1.PodInitialized,
				Status:             corev1.ConditionFalse,
				Reason:             "PodInitialized",
				Message:            message,
				LastTransitionTime: initializedCondition.LastTransitionTime,
			})
		default:
			conditions.UpsertCondition(corev1.PodCondition{
				Type:               corev1.PodInitialized,
				Status:             corev1.ConditionUnknown,
				Reason:             "PodInitialized",
				Message:            "Pod初始化状态未知",
				LastTransitionTime: initializedCondition.LastTransitionTime,
			})
		}
	}

	// 处理 ContainersReady 条件
	containersReadyCondition := conditions.FindCondition(corev1.ContainersReady)
	if containersReadyCondition != nil {
		switch containersReadyCondition.Status {
		case corev1.ConditionTrue:
			conditions.UpsertCondition(corev1.PodCondition{
				Type:               corev1.ContainersReady,
				Status:             corev1.ConditionTrue,
				Reason:             "ContainersReady",
				Message:            "所有容器已就绪",
				LastTransitionTime: containersReadyCondition.LastTransitionTime,
			})
		case corev1.ConditionFalse:
			if containersReadyCondition.Reason == "PodCompleted" {
				conditions.UpsertCondition(corev1.PodCondition{
					Type:               corev1.ContainersReady,
					Status:             corev1.ConditionTrue,
					Reason:             "ContainersReady",
					Message:            "容器已运行完成",
					LastTransitionTime: conditions[3].LastTransitionTime,
				})
			} else {
				message := "容器准备失败"
				if ok, res := checkContainer(pod); !ok {
					message = fmt.Sprintf("容器准备失败：%s", res)
				}
				conditions.UpsertCondition(corev1.PodCondition{
					Type:               corev1.ContainersReady,
					Status:             corev1.ConditionFalse,
					Reason:             "ContainersReady",
					Message:            message,
					LastTransitionTime: containersReadyCondition.LastTransitionTime,
				})
			}
		default:
			conditions.UpsertCondition(corev1.PodCondition{
				Type:               corev1.ContainersReady,
				Status:             corev1.ConditionUnknown,
				Reason:             "ContainersReady",
				Message:            "容器就绪状态未知",
				LastTransitionTime: containersReadyCondition.LastTransitionTime,
			})
		}
	}

	// 处理 PodReady 条件
	podReadyCondition := conditions.FindCondition(corev1.PodReady)
	if podReadyCondition != nil {
		switch podReadyCondition.Status {
		case corev1.ConditionTrue:
			conditions.UpsertCondition(corev1.PodCondition{
				Type:               corev1.PodReady,
				Status:             corev1.ConditionTrue,
				Reason:             "PodReady",
				Message:            "Pod已就绪",
				LastTransitionTime: podReadyCondition.LastTransitionTime,
			})
		case corev1.ConditionFalse:
			if podReadyCondition.Reason == "PodCompleted" {
				conditions.UpsertCondition(corev1.PodCondition{
					Type:               corev1.PodReady,
					Status:             corev1.ConditionTrue,
					Reason:             "PodReady",
					Message:            "Pod已运行完成",
					LastTransitionTime: podReadyCondition.LastTransitionTime,
				})
			} else {
				message := "Pod准备失败"
				conditions.UpsertCondition(corev1.PodCondition{
					Type:               corev1.PodReady,
					Status:             corev1.ConditionFalse,
					Reason:             "PodReady",
					Message:            message,
					LastTransitionTime: podReadyCondition.LastTransitionTime,
				})
			}
		default:
			conditions.UpsertCondition(corev1.PodCondition{
				Type:               corev1.PodReady,
				Status:             corev1.ConditionUnknown,
				Reason:             "PodReady",
				Message:            "Pod就绪状态未知",
				LastTransitionTime: podReadyCondition.LastTransitionTime,
			})
		}
	}

	return sortPodConditions(conditions)
}

// sortPodConditions 确保条件列表中包含四个特定的条件类型，并对其进行排序
func sortPodConditions(conditions []corev1.PodCondition) []corev1.PodCondition {
	// 定义需要保留的条件类型及其顺序
	conditionOrder := map[corev1.PodConditionType]int{
		corev1.PodScheduled:    0,
		corev1.PodInitialized:  1,
		corev1.ContainersReady: 2,
		corev1.PodReady:        3,
	}

	// 创建一个映射以便快速查找现有条件
	existingConditions := make(map[corev1.PodConditionType]corev1.PodCondition)
	for _, cond := range conditions {
		if _, exists := conditionOrder[cond.Type]; exists {
			existingConditions[cond.Type] = cond
		}
	}

	// 确保包含所有需要的条件类型
	for condType := range conditionOrder {
		if _, exists := existingConditions[condType]; !exists {
			// 如果条件不存在，创建一个默认的条件
			existingConditions[condType] = corev1.PodCondition{
				Type:               condType,
				Status:             corev1.ConditionUnknown,
				LastTransitionTime: metav1.NewTime(time.Now()),
				Reason:             "ConditionNotFound",
				Message:            "Condition not found in pod status",
			}
		}
	}

	// 将条件映射转换为切片
	filteredConditions := make([]corev1.PodCondition, 0, len(conditionOrder))
	for _, cond := range existingConditions {
		filteredConditions = append(filteredConditions, cond)
	}

	// 对过滤后的条件进行排序
	sort.Slice(filteredConditions, func(i, j int) bool {
		return conditionOrder[filteredConditions[i].Type] < conditionOrder[filteredConditions[j].Type]
	})

	return filteredConditions
}

// checkInitContainerFailed 检查初始化容器失败的原因
func checkInitContainer(pod *corev1.Pod) (bool, string) {
	for _, containerStatus := range pod.Status.InitContainerStatuses {
		if containerStatus.State.Terminated != nil && containerStatus.State.Terminated.ExitCode != 0 {
			return false, fmt.Sprintf("Init container %s failed with reason: %s, message: %s", containerStatus.Name, containerStatus.State.Terminated.Reason, containerStatus.State.Terminated.Message)
		}
		if containerStatus.State.Waiting != nil && containerStatus.State.Waiting.Reason != "" {
			return false, fmt.Sprintf("Init container %s is waiting with reason: %s, message: %s", containerStatus.Name, containerStatus.State.Waiting.Reason, containerStatus.State.Waiting.Message)
		}
	}
	return true, "init container ready"
}

// exitCodeMessages 是一个映射，将常见的退出码翻译成中文描述及建议
var exitCodeMessages = map[int]string{
	0:   "已完成，无需进一步操作。",
	1:   "通用错误，检查应用日志以获取详细信息。",
	2:   "误用的 shell 内置命令，检查命令语法。",
	125: "容器未能运行，检查容器配置。",
	126: "命令调用不能执行，确认文件权限和路径。",
	127: "找不到文件或目录，检查环境变量和路径。",
	128: "无效的退出参数，检查传递给退出的参数。",
	134: "异常终止 (SIGABRT), 检查应用程序错误。",
	137: "OOM 退出，考虑增加容器内存限制。",
	139: "段错误 (内存访问错误)，检查内存访问操作。",
	143: "因 SIGTERM 信号终止，检查是否有外部中断操作。",
	255: "退出码超出范围，检查应用程序错误。",
}

// getExitCodeMessage 返回退出码的中文描述
func getExitCodeMessage(exitCode int) string {
	if message, exists := exitCodeMessages[exitCode]; exists {
		return message
	}
	return "未知错误，建议检查容器日志以诊断问题。"
}

// checkContainer 检查容器是否准备就绪，如果有容器未就绪，返回所有失败的原因和退出码
func checkContainer(pod *corev1.Pod) (bool, string) {
	var errors []string
	for _, containerStatus := range pod.Status.ContainerStatuses {
		if !containerStatus.Ready {
			if containerStatus.State.Waiting != nil {
				errors = append(errors, fmt.Sprintf("原因: %s - 容器 %s 未就绪 - 等待中: %s", containerStatus.State.Waiting.Reason, containerStatus.Name, containerStatus.State.Waiting.Message))
			}
			if containerStatus.State.Terminated != nil {
				exitCode := containerStatus.State.Terminated.ExitCode
				exitMessage := getExitCodeMessage(int(exitCode))
				if exitCode != 0 {
					errors = append(errors, fmt.Sprintf("原因: %s - 容器 %s 未就绪 - 已终止: %s, 退出码: %d (%s)", containerStatus.State.Terminated.Reason, containerStatus.Name, containerStatus.State.Terminated.Message, exitCode, exitMessage))
				}
			}
		}
	}
	if len(errors) > 0 {
		return false, strings.Join(errors, "; ")
	}
	return true, "所有容器均已就绪"
}

// checkPodReady 检查 Pod 是否准备就绪，如果未就绪，返回失败的原因
func checkPodReady(cluster multicluster.ClusterInterface, pod *corev1.Pod) (bool, string) {
	// 检查 Pod 的事件
	events := &corev1.EventList{}
	err := cluster.Direct().List(context.TODO(), events, &client.ListOptions{
		Namespace:     pod.Namespace,
		FieldSelector: fields.OneTermEqualSelector("involvedObject.name", pod.Name),
	})
	if err != nil {
		return false, fmt.Sprintf("列出 Pod %s 的事件失败: %v", pod.Name, err)
	}

	for _, event := range events.Items {
		if event.Reason == "Failed" {
			return false, fmt.Sprintf("Pod 事件失败 - 信息: %s", event.Message)
		}
	}

	return true, "Pod 已就绪"
}
