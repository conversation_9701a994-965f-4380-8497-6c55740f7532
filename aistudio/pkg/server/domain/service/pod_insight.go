package service

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"strings"
	"sync"
	"time"

	"git.lianjia.com/cloudnative/kic/kic-platform/aistudio/pkg/constant"
	"git.lianjia.com/cloudnative/kic/kic-platform/aistudio/pkg/server/domain/service/dto"
	"git.lianjia.com/cloudnative/kic/kic-platform/apis/aistudio/bcode"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/multicluster"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/property"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/third_party/openobserve"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/watcher"
	"golang.org/x/time/rate"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/fields"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/util/workqueue"
	"k8s.io/klog/v2"
	"k8s.io/utils/pointer"
	"sigs.k8s.io/controller-runtime/pkg/cache"
	"sigs.k8s.io/controller-runtime/pkg/client"
)

var _ watcher.ObjectChangedListener = (*PodInsightService)(nil)

var _ Interface = (*PodInsightService)(nil)

// 1. pod 部署时间超过 x 分钟 并且状态 crash
// 2. 重启次数超过 1 次
// 3. 没有调度成功的 pod
// 4. 被驱逐状态的 pod

const (
	DefaultWorkers       int = 5
	DefaultQueryDuration     = time.Duration(60) * time.Minute * 24 * 180
)

type PodInsightService struct {
	queue             workqueue.RateLimitingInterface
	wg                sync.WaitGroup
	Properties        property.EnvironmentProperty `inject:""`
	openobserveClient openobserve.OpenobserveService
}

func (p *PodInsightService) OnLeader(ctx context.Context, properties property.EnvironmentProperty) error {
	for i := 0; i < DefaultWorkers; i++ {
		p.wg.Add(1)
		go p.RunWorker(ctx)
	}
	return nil
}

func (p *PodInsightService) Init(ctx context.Context, properties property.EnvironmentProperty) error {
	return nil
}

func NewPodInsightService() *PodInsightService {
	rateLimiter := workqueue.NewMaxOfRateLimiter(
		workqueue.NewItemExponentialFailureRateLimiter(5*time.Millisecond, 1000*time.Second),
		&workqueue.BucketRateLimiter{Limiter: rate.NewLimiter(rate.Limit(100), 1000)},
	)
	podInsightService := &PodInsightService{
		queue:             workqueue.NewNamedRateLimitingQueue(rateLimiter, "pod-insight"),
		wg:                sync.WaitGroup{},
		openobserveClient: openobserve.Instance(),
	}
	RegisterManagedClusterListener("managed-pod-insight-object-watcher", podInsightService)
	return podInsightService
}

func (p *PodInsightService) OnObjectAdded(ctx context.Context, clusterName string, newObject client.Object) error {
	return nil
}

func (p *PodInsightService) OnObjectUpdated(ctx context.Context, clusterName string, newObject, oldObject client.Object) error {
	// 1. 判断是否是Pod
	if pod, ok := newObject.(*corev1.Pod); ok {
		// 2. 判断是否需要处理
		err := p.InsightIntoPod(pod, clusterName)
		if err != nil {
			klog.Errorf("failed to insight into pod %s/%s: %v", pod.Namespace, pod.Name, err)
			return err
		}
		return nil
	}
	return nil

}

func (p *PodInsightService) OnObjectDeleted(ctx context.Context, clusterName string, oldObject client.Object) error {
	return nil
}

func (p *PodInsightService) GetWatchedObjectList() []watcher.WatchObject {
	return watcher.WatchObjectList{
		{
			Object: &corev1.Pod{},
			ObjectSelector: &cache.ObjectSelector{
				Label: labels.SelectorFromSet(map[string]string{
					constant.KICManagedLabelKey: "kic",
				}),
			},
		},
	}
}

type PodClusterItem struct {
	Pod         *corev1.Pod
	ClusterName string
	Reason      string
	Message     string
}

func (p *PodInsightService) InsightIntoPod(pod *corev1.Pod, clusterName string) error {
	// 1. 判断是否是需要处理的Pod, 只需要处理 trainingJob、devMachine、deploymentGroup 三种类型工作负载
	if _, exist := pod.Labels[constant.WorkloadTypeLabelKey]; !exist {
		return nil
	}
	// 2. 判断是否有异常
	checks := []func(pod *corev1.Pod) (bool, string, string){
		//isPodScheduleFailed,
		isDeployingTimedOutAndPodCrash,
		hasPodRestarted,
		isPodEvicted,
	}

	for _, check := range checks {
		isAbnormal, reason, message := check(pod)
		if isAbnormal {
			klog.Infof("pod %s/%s is abnormal, reason: %s, message: %s", pod.Namespace, pod.Name, reason, message)
			p.queue.Add(PodClusterItem{
				Pod:         pod,
				ClusterName: clusterName,
				Reason:      reason,
				Message:     message,
			})
			return nil
		}
	}
	return nil
}

func isDeployingTimedOutAndPodCrash(pod *corev1.Pod) (bool, string, string) {
	runningDuration := time.Since(pod.CreationTimestamp.Time)
	if runningDuration.Seconds() < 60 {
		return false, "", ""
	}
	isCrash := false
	var message []string
	for _, containerStatus := range pod.Status.ContainerStatuses {
		if containerStatus.State.Waiting != nil && containerStatus.State.Waiting.Reason == "CrashLoopBackOff" {
			isCrash = true
			message = append(message, fmt.Sprintf("container %s is in CrashLoopBackOff: %s", containerStatus.Name, containerStatus.State.Waiting.Message))
		}
	}
	if isCrash {
		return true, "DeployingTimedOutAndPodCrash", strings.Join(message, ",")
	}
	return false, "", ""
}

func hasPodRestarted(pod *corev1.Pod) (bool, string, string) {
	isRestarted := false
	var message []string
	for _, containerStatus := range pod.Status.ContainerStatuses {
		if containerStatus.RestartCount > 0 {
			isRestarted = true
			message = append(message, fmt.Sprintf("container %s has restarted %d times", containerStatus.Name, containerStatus.RestartCount))
		}
	}
	if isRestarted {
		return true, "PodRestarted", strings.Join(message, ",")
	}
	return false, "", ""
}

func isPodScheduleFailed(pod *corev1.Pod) (bool, string, string) {
	if pod.Status.Phase != corev1.PodPending {
		return false, "", ""
	}
	isScheduledFailed := false
	message := []string{}
	for _, condition := range pod.Status.Conditions {
		if condition.Type == corev1.PodScheduled && condition.Status == corev1.ConditionFalse {
			isScheduledFailed = true
			message = append(message, fmt.Sprintf("pod %s scheduling failed: %s", pod.Name, condition.Message))
		}
	}
	if isScheduledFailed {
		return true, "PodScheduleFailed", strings.Join(message, ",")
	}
	return false, "", ""
}

func isPodEvicted(pod *corev1.Pod) (bool, string, string) {
	if pod.Status.Phase == corev1.PodFailed && pod.Status.Reason == "Evicted" {
		return true, "PodEvicted", fmt.Sprintf("pod %s is evicted: %s", pod.Name, pod.Status.Message)
	}
	return false, "", ""
}

func (p *PodInsightService) RunWorker(ctx context.Context) {
	defer p.wg.Done()

	for {
		select {
		case <-ctx.Done():
			return
		default:
			p.handleTask(ctx)
		}
	}
}

func (p *PodInsightService) Shutdown() {
	p.queue.ShutDown()
	p.wg.Wait()
}

func (p *PodInsightService) handleTask(ctx context.Context) {
	item, shutdown := p.queue.Get()
	if shutdown {
		return
	}
	defer p.queue.Done(item)

	err := p.processItem(ctx, item)
	if err != nil {
		klog.Errorf("failed to process item: %v", err)
		p.queue.AddRateLimited(item)
	} else {
		p.queue.Forget(item)
	}
}

func (p *PodInsightService) processItem(ctx context.Context, item interface{}) error {
	podClusterItem, ok := item.(PodClusterItem)
	if !ok {
		return fmt.Errorf("failed to convert item to PodClusterItem")
	}

	pod := podClusterItem.Pod
	clusterName := podClusterItem.ClusterName
	reason := podClusterItem.Reason
	message := podClusterItem.Message

	podInsightRecord := dto.PodInsightRecord{
		CreateTimeStamp:   float64(time.Now().Unix()),
		ClusterName:       clusterName,
		PodName:           pod.Name,
		Namespace:         pod.Namespace,
		PodRef:            getPodRef(pod),
		PodEvents:         getPodEvents(ctx, pod, clusterName),
		PodStatus:         pod.Status,
		ContainerLogs:     getContainerLogs(ctx, pod, clusterName),
		NodeEvents:        getNodeEvents(ctx, pod, clusterName),
		RestartCount:      getRestartCount(pod),
		InitContainerLogs: getInitContainerLogs(ctx, pod, clusterName),
		Reason:            reason,
		Message:           message,
	}

	// 存储到 openobserve
	klog.Infof("[POD_INSIGHT] write pod insight record: %v", podInsightRecord.PodName)
	err := p.openobserveClient.WriteLogByJson(podInsightRecord)
	if err != nil {
		return fmt.Errorf("failed to write pod insight record: %v", err)
	}
	return nil
}

func getPodRef(pod *corev1.Pod) *dto.PodRef {
	workloadType := pod.Labels[constant.WorkloadTypeLabelKey]
	switch workloadType {
	case string(dto.PodKindTrainingJob):
		return &dto.PodRef{
			PodKind: dto.PodKindTrainingJob,
			TrainingJob: dto.TrainingJobPodRef{
				TrainingJobName:        pod.Labels[constant.JobNameLabelKey],
				WorkspaceName:          pod.Labels[constant.WorkspaceNameLabelKey],
				QueueName:              pod.Labels[constant.QueueNameLabelKey],
				Region:                 pod.Labels[constant.RegionLabelKey],
				Creator:                pod.Labels[constant.CreateByLabelKey],
				TrainingJobDisplayName: pod.Annotations[constant.JobDisplayNameAnnotationKey],
			},
		}
	case string(dto.PodKindDevMachine):
		return &dto.PodRef{
			PodKind: dto.PodKindDevMachine,
			DevMachine: dto.DevMachinePodRef{
				DevMachineName:        pod.Labels[constant.DevMachineNameLabelKey],
				WorkspaceName:         pod.Labels[constant.WorkspaceNameLabelKey],
				Region:                pod.Labels[constant.RegionLabelKey],
				QueueName:             pod.Labels[constant.QueueNameLabelKey],
				Creator:               pod.Labels[constant.CreateByLabelKey],
				Updater:               pod.Labels[constant.UpdateByLabelKey],
				DevMachineDisplayName: pod.Annotations[constant.DevMachineDisplayNameAnnotationKey],
				StartBy:               pod.Annotations[constant.DevMachineStartAnnotationKey],
				StopBy:                pod.Annotations[constant.DevMachineStoppedAnnotationKey],
			},
		}
	case string(dto.PodKindDeploymentGroup):
		return &dto.PodRef{
			PodKind: dto.PodKindDeploymentGroup,
			DeploymentGroup: dto.DeploymentGroupPodRef{
				DeploymentGroupName:        pod.Labels[constant.DeploymentGroupNameLabelKey],
				WorkspaceName:              pod.Labels[constant.WorkspaceNameLabelKey],
				Region:                     pod.Labels[constant.RegionLabelKey],
				QueueName:                  pod.Labels[constant.QueueNameLabelKey],
				Creator:                    pod.Labels[constant.CreateByLabelKey],
				Updater:                    pod.Labels[constant.UpdateByLabelKey],
				ApplicationName:            pod.Labels[constant.ApplicationNameLabelKey],
				ApplicationType:            pod.Labels[constant.ApplicationTypeLabelKey],
				EnvironmentType:            pod.Labels[constant.EnvironmentTypeLabelKey],
				DeploymentGroupDisplayName: pod.Annotations[constant.DeploymentGroupDisplayNameAnnotationKey],
			},
		}
	default:
		klog.Warningf("unknown workload type: %s", workloadType)
		return &dto.PodRef{}
	}
}

func getPodEvents(ctx context.Context, pod *corev1.Pod, clusterName string) []corev1.Event {
	k8sCluster := multicluster.Instance().GetCluster(clusterName)
	if k8sCluster == nil {
		klog.Errorf("failed to get cluster %s", clusterName)
		return nil
	}
	events := &corev1.EventList{}
	err := k8sCluster.Direct().List(context.TODO(), events, &client.ListOptions{
		Namespace:     pod.Namespace,
		FieldSelector: fields.OneTermEqualSelector("involvedObject.name", pod.Name),
	})
	if err != nil {
		klog.Errorf("failed to list events: %v", err)
		return nil
	}
	return events.Items
}

func getContainerLogs(ctx context.Context, pod *corev1.Pod, clusterName string) []dto.ContainerLog {
	k8sCluster := multicluster.Instance().GetCluster(clusterName)
	if k8sCluster == nil {
		klog.Errorf("failed to get cluster %s", clusterName)
		return nil
	}
	var containerLogs []dto.ContainerLog

	for _, container := range pod.Spec.Containers {
		containerLog := getLogs(ctx, k8sCluster.ClientSet(), pod, container.Name)
		containerLogs = append(containerLogs, containerLog)
	}

	return containerLogs
}

func getNodeEvents(ctx context.Context, pod *corev1.Pod, clusterName string) []corev1.Event {
	k8sCluster := multicluster.Instance().GetCluster(clusterName)
	if k8sCluster == nil {
		klog.Errorf("failed to get cluster %s", clusterName)
		return nil
	}

	events := &corev1.EventList{}
	err := k8sCluster.Direct().List(context.TODO(), events, &client.ListOptions{
		FieldSelector: fields.OneTermEqualSelector("involvedObject.name", pod.Spec.NodeName),
	})
	if err != nil {
		klog.Errorf("failed to list events: %v", err)
		return nil
	}
	return events.Items
}

func getRestartCount(pod *corev1.Pod) int32 {
	var totalRestarts int32

	for _, containerStatus := range pod.Status.ContainerStatuses {
		totalRestarts += containerStatus.RestartCount
	}

	for _, initContainerStatus := range pod.Status.InitContainerStatuses {
		totalRestarts += initContainerStatus.RestartCount
	}

	return totalRestarts
}

func getInitContainerLogs(ctx context.Context, pod *corev1.Pod, clusterName string) []dto.ContainerLog {
	k8sCluster := multicluster.Instance().GetCluster(clusterName)
	if k8sCluster == nil {
		klog.Errorf("failed to get cluster %s", clusterName)
		return nil
	}
	var containerLogs []dto.ContainerLog

	for _, container := range pod.Spec.InitContainers {
		containerLog := getLogs(ctx, k8sCluster.ClientSet(), pod, container.Name)
		containerLogs = append(containerLogs, containerLog)
	}

	return containerLogs
}

func getLogs(ctx context.Context, clientSet *kubernetes.Clientset, pod *corev1.Pod, containerName string) dto.ContainerLog {
	req := clientSet.CoreV1().Pods(pod.Namespace).GetLogs(pod.Name, &corev1.PodLogOptions{
		Container: containerName,
		TailLines: pointer.Int64(500),
	})

	podLogs, err := req.Stream(ctx)
	if err != nil {
		klog.Errorf("failed to get logs for pod %s/%s container %s: %v", pod.Namespace, pod.Name, containerName, err)
		return dto.ContainerLog{
			ContainerName: containerName,
			Logs:          nil,
		}
	}
	defer func(podLogs io.ReadCloser) {
		err := podLogs.Close()
		if err != nil {
			klog.Errorf("failed to close pod logs stream: %v", err)
		}
	}(podLogs)

	buf := new(bytes.Buffer)
	_, err = io.Copy(buf, podLogs)
	if err != nil {
		klog.Errorf("failed to copy logs: %v", err)
		return dto.ContainerLog{
			ContainerName: containerName,
			Logs:          nil,
		}
	}

	logLines := strings.Split(buf.String(), "\n")

	return dto.ContainerLog{
		ContainerName: containerName,
		Logs:          logLines,
	}
}

func (p *PodInsightService) GetPodInsightRecords(ctx context.Context, request *dto.GetPodInsightRecordsRequest) (dto.GetPodInsightRecordsResponse, error) {
	searchRequestBody := &openobserve.SearchRequestBody{
		Query:   nil,
		Timeout: 100,
	}

	podName := request.PodName
	namespace := getNamespaceName(request.WorkspaceName)
	stream := p.Properties.MustGetString("openobserve.stream")
	sql := fmt.Sprintf("SELECT * FROM \"%s\" WHERE podname = '%s' AND namespace='%s'", stream, podName, namespace)

	searchQuery := &openobserve.SearchQuery{
		Sql:            sql,
		Size:           100,
		TrackTotalHits: false,
	}

	// 默认查询过去 180 天的数据
	searchQuery.StartTime = time.Now().Add(-DefaultQueryDuration).UnixMicro()
	searchQuery.EndTime = time.Now().UnixMicro()

	if request.StartTime != 0 {
		searchQuery.StartTime = int64(request.StartTime)
	}
	if request.EndTime != 0 {
		searchQuery.EndTime = int64(request.EndTime)
	}

	if request.Page > 0 {
		searchQuery.From = int64((request.Page - 1) * request.PageSize)
		searchQuery.Size = int64(request.PageSize)
	}

	searchRequestBody.Query = searchQuery

	searchResponseBody, err := p.openobserveClient.GetOpenObserveSearchData(ctx, searchRequestBody)
	if err != nil {
		klog.Errorf("Error getting openobserve search data: %v", err)
		return dto.GetPodInsightRecordsResponse{}, bcode.ErrorServerInternalError("Error getting openobserve search data")
	}

	var podInsightRecords []*dto.PodInsightRecord

	if searchResponseBody != nil && len(searchResponseBody.Hits) > 0 {
		for _, hit := range searchResponseBody.Hits {
			podInsightRecords = append(podInsightRecords, formatSearchResponseToPodInsightRecord(hit))
		}
	}

	return dto.GetPodInsightRecordsResponse{
		PodInsightRecords: podInsightRecords,
		Total:             int32(len(podInsightRecords)),
	}, nil
}

// formatSearchResponseToPodInsightRecord formats the search response to PodInsightRecord
func formatSearchResponseToPodInsightRecord(hit map[string]interface{}) *dto.PodInsightRecord {
	podInsightRecord := &dto.PodInsightRecord{}
	extractStringField(hit, "podname", &podInsightRecord.PodName)
	extractStringField(hit, "namespace", &podInsightRecord.Namespace)
	extractStringField(hit, "clustername", &podInsightRecord.ClusterName)
	extractStringField(hit, "reason", &podInsightRecord.Reason)
	extractStringField(hit, "message", &podInsightRecord.Message)

	podInsightRecord.PodStatus = buildPodStatus(hit)
	podInsightRecord.PodRef = buildPodRef(hit)

	parseJSONField(hit, "podevents", &podInsightRecord.PodEvents)
	parseJSONField(hit, "containerlogs", &podInsightRecord.ContainerLogs)
	parseJSONField(hit, "nodeevents", &podInsightRecord.NodeEvents)
	parseJSONField(hit, "initcontainerlogs", &podInsightRecord.InitContainerLogs)

	if createTimeStampStr, ok := hit["createtimestamp"].(float64); ok {
		podInsightRecord.CreateTimeStamp = createTimeStampStr
	}

	return podInsightRecord
}

// extractStringField extracts the string field from the hit and assigns it to the target.
func extractStringField(hit map[string]interface{}, key string, target *string) {
	if value, ok := hit[key].(string); ok {
		*target = value
	}
}

// parseJSONField parses the JSON field from the hit and unmarshals it into the target.
func parseJSONField(hit map[string]interface{}, key string, target interface{}) {
	if jsonString, ok := hit[key].(string); ok {
		err := json.Unmarshal([]byte(jsonString), target)
		if err != nil {
			klog.Errorf("Error unmarshalling %s: %v", key, err)
		}
	}
}

// buildPodRef builds a corev1.ObjectReference from the given hit.
func buildPodStatus(hit map[string]interface{}) corev1.PodStatus {
	podStatus := corev1.PodStatus{}
	extractStringField(hit, "podstatus_phase", (*string)(&podStatus.Phase))
	extractStringField(hit, "podstatus_message", &podStatus.Message)
	extractStringField(hit, "podstatus_reason", &podStatus.Reason)
	extractStringField(hit, "podstatus_nominatednodename", &podStatus.NominatedNodeName)
	extractStringField(hit, "podstatus_hostip", &podStatus.HostIP)
	extractStringField(hit, "podstatus_podip", &podStatus.PodIP)
	extractStringField(hit, "podstatus_qosclass", (*string)(&podStatus.QOSClass))

	if podIpsStr, ok := hit["podstatus_podips"].(string); ok {
		var podIps []corev1.PodIP
		err := json.Unmarshal([]byte(podIpsStr), &podIps)
		if err != nil {
			klog.Errorf("Error unmarshalling PodIps: %v", err)
		} else {
			podStatus.PodIPs = podIps
		}
	}

	if conditionsStr, ok := hit["podstatus_conditions"].(string); ok {
		var conditions []corev1.PodCondition
		err := json.Unmarshal([]byte(conditionsStr), &conditions)
		if err != nil {
			klog.Errorf("Error unmarshalling PodConditions: %v", err)
		} else {
			podStatus.Conditions = conditions
		}
	}

	if initContainerStatusesStr, ok := hit["podstatus_initcontainerstatuses"].(string); ok {
		var initContainerStatuses []corev1.ContainerStatus
		err := json.Unmarshal([]byte(initContainerStatusesStr), &initContainerStatuses)
		if err != nil {
			klog.Errorf("Error unmarshalling InitContainerStatuses: %v", err)
		} else {
			podStatus.InitContainerStatuses = initContainerStatuses
		}
	}

	if containerStatusesStr, ok := hit["podstatus_containerstatuses"].(string); ok {
		var containerStatuses []corev1.ContainerStatus
		err := json.Unmarshal([]byte(containerStatusesStr), &containerStatuses)
		if err != nil {
			klog.Errorf("Error unmarshalling ContainerStatuses: %v", err)
		} else {
			podStatus.ContainerStatuses = containerStatuses
		}
	}

	if ephemeralContainerStatusesStr, ok := hit["podstatus_ephemeralcontainerstatuses"].(string); ok {
		var ephemeralContainerStatuses []corev1.ContainerStatus
		err := json.Unmarshal([]byte(ephemeralContainerStatusesStr), &ephemeralContainerStatuses)
		if err != nil {
			klog.Errorf("Error unmarshalling EphemeralContainerStatuses: %v", err)
		} else {
			podStatus.EphemeralContainerStatuses = ephemeralContainerStatuses
		}
	}

	if startTimeStr, ok := hit["podstatus_starttime"].(string); ok {
		startTime, err := time.Parse(time.RFC3339, startTimeStr)
		if err != nil {
			klog.Errorf("Error parsing StartTime: %v", err)
		} else {
			podStatus.StartTime = &metav1.Time{Time: startTime}
		}
	}

	return podStatus
}

// 从 map 中提取字段并构建 PodRef 结构体
func buildPodRef(hit map[string]interface{}) *dto.PodRef {
	podRef := &dto.PodRef{}
	if podKind, ok := hit["podref_podkind"].(string); ok {
		podRef.PodKind = dto.PodKind(podKind)
	}

	// 根据 PodKind 解析相应的 PodRef 类型
	switch podRef.PodKind {
	case dto.PodKindDevMachine:
		extractStringField(hit, "podref_devmachine_devmachinename", &podRef.DevMachine.DevMachineName)
		extractStringField(hit, "podref_devmachine_workspacename", &podRef.DevMachine.WorkspaceName)
		extractStringField(hit, "podref_devmachine_queuename", &podRef.DevMachine.QueueName)
		extractStringField(hit, "podref_devmachine_region", &podRef.DevMachine.Region)
		extractStringField(hit, "podref_devmachine_creator", &podRef.DevMachine.Creator)
		extractStringField(hit, "podref_devmachine_updater", &podRef.DevMachine.Updater)
		extractStringField(hit, "podref_devmachine_devmachinedisplayname", &podRef.DevMachine.DevMachineDisplayName)
		extractStringField(hit, "podref_devmachine_startby", &podRef.DevMachine.StartBy)
		extractStringField(hit, "podref_devmachine_stopby", &podRef.DevMachine.StopBy)
	case dto.PodKindTrainingJob:
		extractStringField(hit, "podref_trainingjob_trainingjobname", &podRef.TrainingJob.TrainingJobName)
		extractStringField(hit, "podref_trainingjob_workspacename", &podRef.TrainingJob.WorkspaceName)
		extractStringField(hit, "podref_trainingjob_queuename", &podRef.TrainingJob.QueueName)
		extractStringField(hit, "podref_trainingjob_region", &podRef.TrainingJob.Region)
		extractStringField(hit, "podref_trainingjob_creator", &podRef.TrainingJob.Creator)
		extractStringField(hit, "podref_trainingjob_trainingjobdisplayname", &podRef.TrainingJob.TrainingJobDisplayName)
	case dto.PodKindDeploymentGroup:
		extractStringField(hit, "podref_deploymentgroup_deploymentgroupname", &podRef.DeploymentGroup.DeploymentGroupName)
		extractStringField(hit, "podref_deploymentgroup_workspacename", &podRef.DeploymentGroup.WorkspaceName)
		extractStringField(hit, "podref_deploymentgroup_queuename", &podRef.DeploymentGroup.QueueName)
		extractStringField(hit, "podref_deploymentgroup_region", &podRef.DeploymentGroup.Region)
		extractStringField(hit, "podref_deploymentgroup_creator", &podRef.DeploymentGroup.Creator)
		extractStringField(hit, "podref_deploymentgroup_updater", &podRef.DeploymentGroup.Updater)
		extractStringField(hit, "podref_deploymentgroup_deploymentgroupdisplayname", &podRef.DeploymentGroup.DeploymentGroupDisplayName)
		extractStringField(hit, "podref_deploymentgroup_applicationname", &podRef.DeploymentGroup.ApplicationName)
		extractStringField(hit, "podref_deploymentgroup_applicationtype", &podRef.DeploymentGroup.ApplicationType)
		extractStringField(hit, "podref_deploymentgroup_environmenttype", &podRef.DeploymentGroup.EnvironmentType)
	}

	return podRef
}
