package service

import (
	"context"
	"errors"
	"fmt"
	"sort"
	"strings"
	"time"

	"git.lianjia.com/cloudnative/kic/kic-platform/aistudio/pkg/constant"
	"git.lianjia.com/cloudnative/kic/kic-platform/aistudio/pkg/server/domain/model"
	"git.lianjia.com/cloudnative/kic/kic-platform/apis/aistudio/bcode"
	v1 "git.lianjia.com/cloudnative/kic/kic-platform/apis/aistudio/platform/v1"
	cloudfsv1alpha1 "git.lianjia.com/cloudnative/kic/kic-platform/pkg/apis/cloudfs.kcs.io/v1alpha1"
	schedulingv1alpha1 "git.lianjia.com/cloudnative/kic/kic-platform/pkg/apis/scheduling.kcs.io/v1alpha1"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/infrastructure/datastore"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/multicluster"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/property"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/third_party/distributedcache"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/util"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"google.golang.org/protobuf/types/known/emptypb"
	corev1 "k8s.io/api/core/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/util/retry"
	"k8s.io/klog"
	controllerruntime "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"
)

const (
	DefaultDashboardName = "kic-default"
)

var _ v1.PlatformServiceHTTPServer = (*PlatformService)(nil)

var _ Interface = (*PlatformService)(nil)

type PlatformService struct {
	Store            datastore.DataStore       `inject:"datastore"`
	WorkspaceService *WorkspaceService         `inject:""`
	ClusterManager   *ClusterManager           `inject:"clusterManager"`
	Manager          controllerruntime.Manager `inject:""`
	RbacService      *RbacService              `inject:"rbacService"`
}

// OnLeader 当leader时，启动一个goroutine来定时同步分布式缓存容量
func (p *PlatformService) OnLeader(ctx context.Context, properties property.EnvironmentProperty) error {
	// 启动一个goroutine来定时同步分布式缓存容量
	go p.TimedSynchronizeDistributedCacheCapacity(ctx, properties)
	return nil
}

// TimedSynchronizeDistributedCacheCapacity 定时同步分布式缓存容量
func (p *PlatformService) TimedSynchronizeDistributedCacheCapacity(ctx context.Context, properties property.EnvironmentProperty) {
	err := p.SynchronizeDistributedCacheCapacity(ctx, properties)
	if err != nil {
		klog.Errorf("synchronize distributed cache capacity failed, err: %v", err)
		return
	}
	klog.Info("synchronize distributed cache capacity success")
	timer := time.NewTimer(time.Minute * 30)
	defer timer.Stop()
	for {
		select {
		case <-ctx.Done():
			return
		case <-timer.C:
			err := p.SynchronizeDistributedCacheCapacity(ctx, properties)
			if err != nil {
				klog.Errorf("synchronize distributed cache capacity failed, err: %v", err)
				timer.Reset(10 * time.Minute)
				continue
			}
			klog.Info("synchronize distributed cache capacity success")
			timer.Reset(time.Minute * 30)
		}
	}
}

// SynchronizeDistributedCacheCapacity 同步分布式缓存容量
func (p *PlatformService) SynchronizeDistributedCacheCapacity(ctx context.Context, properties property.EnvironmentProperty) error {
	// 获取现有分布式缓存
	klog.Infof("start to synchronize distributed cache capacity at %v", time.Now())
	distributedCache, err := p.Store.List(ctx, &model.DistributedCacheRegistrationEntity{}, nil)
	if err != nil {
		klog.Errorf("get distributed cache failed, err: %v", err)
		return err
	}
	// 获取分布式缓存容量
	for _, cache := range distributedCache {
		cacheEntity := cache.(*model.DistributedCacheRegistrationEntity)
		status, err := p.getDistributedCacheStatus(ctx, cacheEntity.Region, cacheEntity.Zone, cacheEntity.Cluster)
		if err != nil {
			klog.Errorf("get distributed cache status failed, err: %v", err)
			continue
		}
		cacheEntity.Status = status
		err = p.Store.Put(ctx, cacheEntity)
		if err != nil {
			klog.Errorf("update distributed cache capacity failed, err: %v", err)
			return err
		}
	}
	klog.Infof("synchronize distributed cache capacity success at %v", time.Now())
	return nil
}

func (p *PlatformService) AddOrUpdateCubeFSCluster(ctx context.Context, request *v1.AddOrUpdateCubeFSClusterRequest) (*emptypb.Empty, error) {
	cubeFSCluster := &cloudfsv1alpha1.CubeFSCluster{}
	err := multicluster.Instance().GetLocalCluster().Direct().Get(ctx, client.ObjectKey{
		Name: request.Name,
	}, cubeFSCluster)
	if err != nil {
		if apierrors.IsNotFound(err) {
			cubeFSCluster = &cloudfsv1alpha1.CubeFSCluster{
				ObjectMeta: metav1.ObjectMeta{
					Name: request.Name,
				},
				DisplayName:   request.DisplayName,
				Description:   request.Description,
				Region:        request.Region,
				Enabled:       true,
				Zones:         request.Zones,
				DashboardRef:  DefaultDashboardName,
				MasterAddr:    request.MasterAddress,
				Specification: request.Specification,
			}
			err = multicluster.Instance().GetLocalCluster().Direct().Create(ctx, cubeFSCluster)
			if err != nil {
				return nil, bcode.ErrorServerInternalError("create cluster failed, err:%s", err.Error())
			}
		}
		return nil, bcode.ErrorServerInternalError("get cluster failed, err:%v", err)
	}
	cubeFSCluster.DisplayName = request.DisplayName
	cubeFSCluster.Description = request.Description
	cubeFSCluster.Zones = request.Zones
	cubeFSCluster.MasterAddr = request.MasterAddress
	err = multicluster.Instance().GetLocalCluster().Direct().Update(ctx, cubeFSCluster)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("update cluster failed, err:%s", err.Error())
	}
	return &emptypb.Empty{}, nil
}

func (p *PlatformService) CubeFSClusterUpdateStatus(ctx context.Context, request *v1.CubeFSClusterSetStatusRequest) (*emptypb.Empty, error) {
	cubeFSCluster := &cloudfsv1alpha1.CubeFSCluster{}
	err := multicluster.Instance().GetLocalCluster().Direct().Get(ctx, client.ObjectKey{
		Name: request.Name,
	}, cubeFSCluster)
	switch request.Status {
	case "enabled":
		cubeFSCluster.Enabled = true
		err = multicluster.Instance().GetLocalCluster().Direct().Update(ctx, cubeFSCluster)
		if err != nil {
			return nil, bcode.ErrorServerInternalError("update cluster failed, err:%s", err.Error())
		}
		return &emptypb.Empty{}, nil
	case "disabled":
		cubeFSCluster.Enabled = false
		err = multicluster.Instance().GetLocalCluster().Direct().Update(ctx, cubeFSCluster)
		if err != nil {
			return nil, bcode.ErrorServerInternalError("update cluster failed, err:%s", err.Error())
		}
		return &emptypb.Empty{}, nil
	}
	return &emptypb.Empty{}, nil
}

func (p *PlatformService) DeleteCubeFSCluster(ctx context.Context, request *v1.DeleteCubeFSClusterRequest) (*emptypb.Empty, error) {
	cubeFSCluster := &cloudfsv1alpha1.CubeFSCluster{}
	err := multicluster.Instance().GetLocalCluster().Direct().Get(ctx, client.ObjectKey{
		Name: request.Name,
	}, cubeFSCluster)
	if err != nil {
		if apierrors.IsNotFound(err) {
			return &emptypb.Empty{}, nil
		}
		return nil, bcode.ErrorServerInternalError("get cluster failed, err:%v", err)
	}
	err = multicluster.Instance().GetLocalCluster().Direct().Delete(ctx, cubeFSCluster)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("delete cluster failed, err:%s", err.Error())
	}
	return &emptypb.Empty{}, nil
}

func (p *PlatformService) SetCubeFSDashboard(ctx context.Context, request *v1.SetCubeFSDashboardRequest) (*emptypb.Empty, error) {
	if ok := p.Manager.GetCache().WaitForCacheSync(ctx); ok {
		cubeFSDashboard := &cloudfsv1alpha1.CubeFSDashboard{}
		err := multicluster.Instance().GetLocalCluster().Direct().Get(ctx, client.ObjectKey{
			Name: DefaultDashboardName,
		}, cubeFSDashboard)
		if err != nil {
			if apierrors.IsNotFound(err) {
				cubeFSDashboard = &cloudfsv1alpha1.CubeFSDashboard{
					ObjectMeta: metav1.ObjectMeta{
						Name: DefaultDashboardName,
					},
					User:     request.User,
					Password: request.Password,
					URL:      request.Url,
				}
				err = multicluster.Instance().GetLocalCluster().Direct().Create(ctx, cubeFSDashboard)
				if err != nil {
					return nil, bcode.ErrorServerInternalError("create dashboard failed, err:%s", err.Error())
				}
				return &emptypb.Empty{}, nil
			}
			return nil, bcode.ErrorServerInternalError("get dashboard failed, err:%v", err)
		}
		cubeFSDashboard.User = request.User
		cubeFSDashboard.Password = request.Password
		cubeFSDashboard.URL = request.Url
		err = multicluster.Instance().GetLocalCluster().Direct().Update(ctx, cubeFSDashboard)
		if err != nil {
			return nil, bcode.ErrorServerInternalError("update dashboard failed, err:%s", err.Error())
		}
		return &emptypb.Empty{}, nil
	}
	return nil, bcode.ErrorServerInternalError("wait for cache sync failed")
}

func (p *PlatformService) AddOrUpdateNodeSpecification(ctx context.Context, specification *v1.NodeSpecification) (*v1.NodeSpecification, error) {
	specificationEntity := &model.NodeSpecificationEntity{
		Name: specification.Name,
	}
	err := p.Store.Get(ctx, specificationEntity)
	specificationEntity.ToModel(specification)
	if err != nil {
		if errors.Is(err, datastore.ErrRecordExist) {
			err = p.Store.Put(ctx, specificationEntity)
			if err != nil {
				return nil, bcode.ErrorServerInternalError("update cluster failed, err:%s", err.Error())
			}
			return specificationEntity.ToNodeSpecification(), nil
		} else if errors.Is(err, datastore.ErrRecordNotExist) {
			err = p.Store.Add(ctx, specificationEntity)
			if err != nil {
				return nil, bcode.ErrorServerInternalError("add cluster failed, err:%s", err.Error())
			}
			return specificationEntity.ToNodeSpecification(), nil
		}
		return nil, bcode.ErrorServerInternalError("get cluster from datastore failed, err:%s", err.Error())
	}
	return specificationEntity.ToNodeSpecification(), nil

}

func (p *PlatformService) DeleteNodeSpecification(ctx context.Context, request *v1.DeleteNodeSpecificationRequest) (*emptypb.Empty, error) {
	specificationEntity := &model.NodeSpecificationEntity{
		Name: request.Name,
	}
	if err := p.Store.Get(ctx, specificationEntity); err != nil {
		if errors.Is(err, datastore.ErrRecordNotExist) {
			return &emptypb.Empty{}, nil
		}
		return nil, bcode.ErrorServerInternalError("get node specification,name:%s failed: %v", request.Name, err)
	}
	if err := p.Store.Delete(ctx, specificationEntity); err != nil {
		return nil, bcode.ErrorServerInternalError("delete node specification,name:%s failed: %v", request.Name, err)
	}
	return &emptypb.Empty{}, nil
}

func (p *PlatformService) GetNodeSpecification(ctx context.Context, name string) (*v1.NodeSpecification, error) {
	specificationEntity := &model.NodeSpecificationEntity{
		Name: name,
	}
	if err := p.Store.Get(ctx, specificationEntity); err != nil {
		if errors.Is(err, datastore.ErrRecordNotExist) {
			return nil, bcode.ErrorServerInternalError("get node specification,id:%s failed: %v", name, err)
		}
	}
	return specificationEntity.ToNodeSpecification(), nil

}

func (p *PlatformService) ListNodeSpecifications(ctx context.Context, options *v1.ListNodeSpecificationOptions) (*v1.ListNodeSpecificationsResult, error) {
	var filterOptions datastore.FilterOptions

	// 查询是否为gpu类型，gpuNum的为0则为非gpu类型
	if options.GpuEnabled {
		filterOptions.NotIn = append(filterOptions.NotIn, datastore.NotInQueryOption{
			Key:    "gpuNum",
			Values: []string{"0"},
		})
	}

	if options.GpuProduct != "" {
		filterOptions.In = append(filterOptions.In, datastore.InQueryOption{
			Key:    "gpuProduct",
			Values: strings.Split(options.GpuProduct, ","),
		})
	}

	nodes, err := p.Store.List(ctx, &model.NodeSpecificationEntity{}, &datastore.ListOptions{
		FilterOptions: filterOptions,
	})
	if err != nil {
		return nil, bcode.ErrorServerInternalError("list node specifications failed: %v", err)
	}
	var result []*v1.NodeSpecification
	for _, node := range nodes {
		result = append(result, node.(*model.NodeSpecificationEntity).ToNodeSpecification())
	}
	return &v1.ListNodeSpecificationsResult{NodeSpecifications: result}, nil
}

func (p *PlatformService) Init(ctx context.Context, properties property.EnvironmentProperty) error {
	_, err := p.SetCubeFSDashboard(ctx, &v1.SetCubeFSDashboardRequest{
		User:     properties.MustGetString("cubefs.dashboardUser"),
		Password: properties.MustGetString("cubefs.dashboardPassword"),
		Url:      properties.MustGetString("cubefs.dashboardUrl"),
	})
	if err != nil {
		klog.Fatalf("init cubefs dashboard failed, err:%v", err)
	}
	return nil
}

func NewPlatformService() *PlatformService {
	return &PlatformService{}
}

func (p *PlatformService) ListKubernetesClusters(ctx context.Context, options *v1.ListKubernetesClustersOptions) (*v1.ListKubernetesClustersResponse, error) {
	var clusterImportList = &schedulingv1alpha1.ClusterImportList{}
	err := multicluster.Instance().GetLocalCluster().Direct().List(ctx, clusterImportList)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("list kubernetes clusters failed, err:%s", err.Error())
	}
	var clusters = make([]*v1.KubernetesCluster, 0)
	for _, clusterImport := range clusterImportList.Items {
		clusters = append(clusters, entityToKubernetesCluster(&clusterImport))
	}
	return &v1.ListKubernetesClustersResponse{Clusters: clusters}, nil
}

func (p *PlatformService) GetKubernetesCluster(ctx context.Context, request *v1.GetKubernetesClusterRequest) (*v1.KubernetesCluster, error) {
	var clusterImport = &schedulingv1alpha1.ClusterImport{}
	err := multicluster.Instance().GetLocalCluster().Direct().Get(ctx, client.ObjectKey{
		Name: request.Name,
	}, clusterImport)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("get kubernetes cluster failed, err:%s", err.Error())
	}
	return entityToKubernetesCluster(clusterImport), nil
}

func entityToKubernetesCluster(clusterImport *schedulingv1alpha1.ClusterImport) *v1.KubernetesCluster {
	var usages = make([]v1.ClusterUsage, 0)
	if len(clusterImport.Spec.Usages) > 0 {
		for _, u := range clusterImport.Spec.Usages {
			if strings.EqualFold(u, "training") {
				usages = append(usages, v1.ClusterUsage_Training)
			} else if strings.EqualFold(u, "inference") {
				usages = append(usages, v1.ClusterUsage_Inference)
			}
		}
	}
	return &v1.KubernetesCluster{
		Name:        clusterImport.Name,
		Region:      clusterImport.Spec.Region,
		Zone:        clusterImport.Spec.Zone,
		Idc:         clusterImport.Spec.IDC,
		DisplayName: clusterImport.Spec.DisplayName,
		Description: clusterImport.Spec.Description,
		Usages:      usages,
	}
}

func (p *PlatformService) AddOrUpdateKubernetesCluster(ctx context.Context, request *v1.CreateKubernetesClusterRequest) (*v1.KubernetesCluster, error) {
	clusters, err := multicluster.Instance().ListClusterGateways(ctx)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("get cluster failed, err:%s", err.Error())
	}
	var exists bool
	for _, cls := range clusters {
		if cls.Name == request.Name {
			exists = true
			break
		}
	}
	if !exists {
		return nil, bcode.ErrorKubernetesClusterNotFound("cluster not exists")
	}
	var usages []string
	for _, usage := range request.Usages {
		usages = append(usages, usage.String())
	}
	var clusterImport = &schedulingv1alpha1.ClusterImport{}
	err = multicluster.Instance().GetLocalCluster().Direct().Get(ctx, client.ObjectKey{
		Name: request.Name,
	}, clusterImport)
	if err != nil {
		if apierrors.IsNotFound(err) {
			clusterImport = &schedulingv1alpha1.ClusterImport{
				ObjectMeta: metav1.ObjectMeta{
					Name: request.Name,
				},
				Spec: schedulingv1alpha1.ClusterImportSpec{
					Region:      request.Region,
					Zone:        request.Zone,
					IDC:         request.Idc,
					DisplayName: request.DisplayName,
					Usages:      usages,
					Description: request.Description,
				},
			}
			err = multicluster.Instance().GetLocalCluster().Direct().Create(ctx, clusterImport)
			if err != nil {
				return nil, bcode.ErrorServerInternalError("create kubernetes cluster failed, err:%s", err.Error())
			}
		}
		return nil, bcode.ErrorServerInternalError("get kubernetes cluster failed, err:%s", err.Error())
	}
	clusterImport.Spec.Region = request.Region
	clusterImport.Spec.Zone = request.Zone
	clusterImport.Spec.IDC = request.Idc
	clusterImport.Spec.DisplayName = request.DisplayName
	clusterImport.Spec.Usages = usages
	clusterImport.Spec.Description = request.Description
	err = multicluster.Instance().GetLocalCluster().Direct().Update(ctx, clusterImport)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("update kubernetes cluster failed, err:%s", err.Error())
	}
	return entityToKubernetesCluster(clusterImport), nil
}

func (p *PlatformService) RemoveKubernetesCluster(ctx context.Context, request *v1.RemoveKubernetesClusterRequest) (*v1.KubernetesCluster, error) {
	return nil, fmt.Errorf("目前不支持删除集群,该操作是一个非常危险的行为,请谨慎操作")
}

// AssignNode assign node to workspace,平台管理员操作
func (p *PlatformService) AssignNode(ctx context.Context, request *v1.AssignNodeRequest) (*emptypb.Empty, error) {
	if request.Ips == nil || len(request.Ips) == 0 {
		return nil, bcode.ErrorInvalidArgument("node ip is empty")
	}
	if request.WorkspaceName == "" {
		return nil, bcode.ErrorInvalidArgument("workspace name is empty")
	}
	k8sNodes, err := p.ClusterManager.GetAllNodes(ctx)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("get k8s nodes failed, err:%s", err.Error())
	}
	k8sNodeIpMaps := make(map[string]*corev1.Node)
	for _, node := range k8sNodes {
		nodeCopy := node
		if node.Status.Addresses != nil {
			for _, addr := range node.Status.Addresses {
				if addr.Type == corev1.NodeInternalIP {
					k8sNodeIpMaps[addr.Address] = &nodeCopy
				}
			}
		}
	}

	for _, ip := range request.Ips {
		node, ok := k8sNodeIpMaps[ip]
		if !ok {
			klog.Errorf("node ip %s not found", ip)
			continue
		}
		//新增加Node资源的workspaceName的Annotation即可
		err = retry.RetryOnConflict(retry.DefaultRetry, func() error {
			var newNode = &corev1.Node{}
			cluster := node.Annotations[constant.ClusterAnnotationKey]
			k8sCluster := multicluster.Instance().GetCluster(cluster)
			if k8sCluster == nil {
				return fmt.Errorf("cluster %s not found", cluster)
			}
			err = k8sCluster.Direct().Get(ctx, client.ObjectKey{Name: node.Name}, newNode)
			if err != nil {
				return err
			}
			newNode.Labels[constant.WorkspaceNameLabelKey] = request.WorkspaceName
			nodeSpecificationName := p.ClusterManager.GetNodeSpecificationName(newNode)
			newNode.Annotations[constant.NodeSpecificationNameLabelKey] = nodeSpecificationName
			newNode.Labels[constant.NodeSpecificationNameHashLabelKey] = util.Md5s(nodeSpecificationName)
			klog.Infof("assign node:%s to workspace:%s,nodeSpecificationName:%s ", ip, request.WorkspaceName, nodeSpecificationName)
			newNode.Labels[constant.ComponentCubeFsLabelKey] = "enabled"
			newNode.Labels[constant.NotebookNodePortLabelKey] = "enabled"

			err = k8sCluster.Direct().Update(ctx, newNode, &client.UpdateOptions{})
			if err != nil {
				return err
			}
			return nil
		})
		if err != nil {
			return nil, bcode.ErrorServerInternalError("assign node:%s to workspace:%s failed: %v", ip, request.WorkspaceName, err)
		}
	}
	return &emptypb.Empty{}, nil
}

// RecycleNode auto recycling node,平台管理员操作
func (p *PlatformService) RecycleNode(ctx context.Context, request *v1.RecycleNodeRequest) (*emptypb.Empty, error) {
	if request.Ips == nil || len(request.Ips) == 0 {
		return nil, bcode.ErrorInvalidArgument("node ip is empty")
	}
	for _, ip := range request.Ips {
		nodeMetric, err := p.ClusterManager.GetNodeMetricByNodeIP(ctx, ip)
		if err != nil {
			return nil, err
		}
		//清空掉Node资源的workspaceName的Annotation即可
		err = retry.RetryOnConflict(retry.DefaultRetry, func() error {
			var newNode = &corev1.Node{}
			k8sCluster := multicluster.Instance().GetCluster(nodeMetric.ClusterName)
			if k8sCluster == nil {
				return fmt.Errorf("cluster %s not found", nodeMetric.ClusterName)
			}
			err = k8sCluster.Direct().Get(ctx, client.ObjectKey{Name: nodeMetric.Name}, newNode)
			if err != nil {
				return err
			}
			if newNode.Labels != nil {
				delete(newNode.Labels, constant.WorkspaceNameLabelKey)
				delete(newNode.Labels, constant.NodeSpecificationNameHashLabelKey)
				delete(newNode.Annotations, constant.NodeSpecificationNameLabelKey)
				delete(newNode.Labels, constant.ComponentCubeFsLabelKey)
				delete(newNode.Labels, constant.NotebookNodePortLabelKey)
			}
			err = k8sCluster.Direct().Update(ctx, newNode, &client.UpdateOptions{})
			if err != nil {
				return err
			}
			return nil
		})
		if err != nil {
			return nil, bcode.ErrorServerInternalError("recycle node[%s] failed: %v", ip, err)
		}
	}
	return &emptypb.Empty{}, nil
}

func (p *PlatformService) ListNodes(ctx context.Context, options *v1.ListNodesOptions) (*v1.ListNodesResult, error) {
	nodes, err := p.ClusterManager.GetAllNodes(ctx)
	if err != nil {
		return nil, err
	}
	var nodesResults []*v1.Node
	workspaces, err := p.WorkspaceService.ListEnabledWorkspaces(ctx)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("list workspaces failed when list nodes, err:%s", err.Error())
	}
	for _, node := range nodes {
		nodeIP := getNodeInternalIP(&node)
		if options.Ip != "" && !strings.Contains(nodeIP, options.Ip) {
			continue
		}
		cluster := node.Annotations[constant.ClusterAnnotationKey]
		if options.Cluster != "" && !strings.Contains(cluster, options.Cluster) {
			continue
		}
		workspaceName := node.Labels[constant.WorkspaceNameLabelKey]
		if options.Assigned {
			if workspaceName == "" || (options.WorkspaceName != "" && !strings.Contains(workspaceName, options.WorkspaceName)) {
				continue
			}
			if !util.StringInSlice(workspaceName, workspaces) {
				continue
			}
		} else {
			if workspaceName != "" {
				continue
			}
		}

		queueName := node.Labels[constant.QueueNameLabelKey]
		if options.Assigned {
			if options.QueueName != "" && !strings.Contains(queueName, options.QueueName) {
				continue
			}
		}

		var specification string
		gpuProduct := node.Labels[constant.GPUProductLabelKey]
		cpuNum := node.Annotations[constant.CPUNumAnnotationKey]
		mem := node.Annotations[constant.MemoryAnnotationKey]
		if node.Labels != nil && node.Labels[constant.GPUNvidiaFamilyLabelKey] != "" {
			gpuNum := node.Annotations[constant.GPUNumAnnotationKey]
			specification = fmt.Sprintf("%s-%s卡 | %svCPU | %s GiB", gpuProduct, gpuNum, cpuNum, mem)
		} else {
			specification = fmt.Sprintf("CPU机型 ｜ %svCPU | %s GiB", cpuNum, mem)
		}
		nodeResult := &v1.Node{
			Ip:            nodeIP,
			NodeName:      node.Name,
			Status:        getNodeStatus(&node),
			Cluster:       cluster,
			WorkspaceName: workspaceName,
			GpuProduct:    gpuProduct,
			Labels:        node.Labels,
			Annotations:   node.Annotations,
			Specification: specification,
			QueueName:     queueName,
		}
		nodesResults = append(nodesResults, nodeResult)
	}

	sort.Slice(nodesResults, func(i, j int) bool {
		return nodesResults[i].Ip < nodesResults[j].Ip
	})

	if options.Page == 0 && options.PageSize == 0 {
		return &v1.ListNodesResult{Nodes: nodesResults}, nil
	}
	total := len(nodesResults)
	start := int((options.Page - 1) * options.PageSize)
	end := start + int(options.PageSize)
	if start > len(nodesResults) {
		start = total
	}
	if end > len(nodesResults) {
		end = total
	}
	return &v1.ListNodesResult{Nodes: nodesResults[start:end], Total: int32(total)}, nil
}

func getNodeStatus(node *corev1.Node) string {
	var status []string
	if IsNodeReady(node) {
		status = append(status, "Ready")
	} else {
		status = append(status, "NotReady")
	}
	if node.Spec.Unschedulable {
		status = append(status, "SchedulingDisabled")
	}
	return strings.Join(status, ",")
}

func (p *PlatformService) GetNodesInfoStatistics(ctx context.Context, request *v1.GetNodesInfoStatisticsRequest) (*v1.NodesInfoStatisticsResponse, error) {
	nodes, err := p.ClusterManager.GetAllNodes(ctx)
	if err != nil {
		return nil, err
	}

	nodeTotal := len(nodes)
	gpuNodeTotal := 0
	gpuMap := make(map[string]int32)
	readyNodeNum, notReadyNodeNum := 0, 0
	assignedNodeNum, notAssignedNodeNum := 0, 0

	for _, node := range nodes {
		gpuProduct := node.Annotations[constant.GPUProductAnnotationKey]
		if gpuProduct != "" {
			gpuNodeTotal++
			gpuNum := node.Annotations[constant.GPUNumAnnotationKey]
			gpuSpecification := fmt.Sprintf("%s-%s卡", gpuProduct, gpuNum)
			gpuMap[gpuSpecification]++
		}
		status := getNodeStatus(&node)
		if status == "Ready" {
			readyNodeNum++
		} else {
			notReadyNodeNum++
		}
		if node.Labels[constant.WorkspaceNameLabelKey] != "" {
			assignedNodeNum++
		} else {
			notAssignedNodeNum++
		}
	}
	return &v1.NodesInfoStatisticsResponse{
		NodeTotal:          int32(nodeTotal),
		GpuNodeTotal:       int32(gpuNodeTotal),
		GpuMap:             gpuMap,
		ReadyNodeNum:       int32(readyNodeNum),
		NotReadyNodeNum:    int32(notReadyNodeNum),
		AssignedNodeNum:    int32(assignedNodeNum),
		NotAssignedNodeNum: int32(notAssignedNodeNum),
	}, nil
}

func getNodeInternalIP(node *corev1.Node) string {
	for _, addr := range node.Status.Addresses {
		if addr.Type == corev1.NodeInternalIP {
			return addr.Address
		}
	}
	return node.Name
}

// AddDragonfly添加Dragonfly配置
func (p *PlatformService) AddDragonfly(ctx context.Context, request *v1.AddDragonflyRequest) (*emptypb.Empty, error) {
	user, ok := ctx.Value(constant.UserCtxKey).(string)
	if !ok {
		return nil, bcode.ErrorInvalidArgument("user not found")
	}
	dragonflyEntity := model.DragonflyEntity{
		ID:          primitive.NewObjectID(),
		Cluster:     request.Cluster,
		Region:      request.Region,
		Zone:        request.Zone,
		Description: request.Description,
		Address:     request.Address,
		Creator:     user,
	}
	err := p.Store.Add(ctx, &dragonflyEntity)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("add dragonfly failed, err:%s", err.Error())
	}
	return &emptypb.Empty{}, nil
}

// DeleteDragonfly删除Dragonfly配置
func (p *PlatformService) DeleteDragonfly(ctx context.Context, request *v1.DeleteDragonflyRequest) (*emptypb.Empty, error) {
	id, err := primitive.ObjectIDFromHex(request.Id)
	if err != nil {
		return nil, bcode.ErrorInvalidArgument("invalid id")
	}
	err = p.Store.Delete(ctx, &model.DragonflyEntity{
		ID: id,
	})
	if err != nil {
		return nil, bcode.ErrorServerInternalError("delete dragonfly failed, err:%s", err.Error())
	}
	return &emptypb.Empty{}, nil
}

// ListDragonfly获取Dragonfly配置
func (p *PlatformService) ListDragonfly(ctx context.Context, request *v1.ListDragonflyRequest) (*v1.ListDragonflyResponse, error) {
	filter := datastore.FilterOptions{}
	if request.Cluster != "" {
		filter.Queries = append(filter.Queries, datastore.FuzzyQueryOption{
			Key:   "cluster",
			Query: request.Cluster,
		})
	}
	if request.Creator != "" {
		filter.In = append(filter.In, datastore.InQueryOption{
			Key:    "creator",
			Values: []string{request.Creator},
		})
	}
	total, err := p.Store.Count(ctx, &model.DragonflyEntity{}, &filter)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("count dragonfly failed, err:%s", err.Error())
	}
	if total == 0 {
		return &v1.ListDragonflyResponse{
			Dragonflies: []*v1.Dragonfly{},
			Total:       0,
		}, nil
	}
	dragonflyEntities, err := p.Store.List(ctx, &model.DragonflyEntity{}, &datastore.ListOptions{
		FilterOptions: filter,
		Page:          int(request.Page),
		PageSize:      int(request.PageSize),
		SortBy: []datastore.SortOption{
			{
				Key:   "createTime",
				Order: datastore.SortOrderDescending,
			},
		},
	})
	if err != nil {
		return nil, bcode.ErrorServerInternalError("list dragonfly failed, err:%s", err.Error())
	}
	var dragonflies []*v1.Dragonfly
	for _, entity := range dragonflyEntities {
		en := entity.(*model.DragonflyEntity)
		dragonflies = append(dragonflies, &v1.Dragonfly{
			Id:          en.ID.Hex(),
			Cluster:     en.Cluster,
			Region:      en.Region,
			Zone:        en.Zone,
			Description: en.Description,
			Address:     en.Address,
			Creator:     en.Creator,
			CreateTime:  en.CreateTime.Format(time.DateTime),
			UpdateTime:  en.UpdateTime.Format(time.DateTime),
		})
	}
	return &v1.ListDragonflyResponse{
		Dragonflies: dragonflies,
		Total:       int32(total),
	}, nil
}

// SetDragonfly更新Dragonfly配置
func (p *PlatformService) SetDragonfly(ctx context.Context, request *v1.SetDragonflyRequest) (*emptypb.Empty, error) {
	id, err := primitive.ObjectIDFromHex(request.Id)
	if err != nil {
		return nil, bcode.ErrorInvalidArgument("invalid id")
	}
	user, ok := ctx.Value(constant.UserCtxKey).(string)
	if !ok {
		return nil, bcode.ErrorInvalidArgument("user not found")
	}
	dragonflyEntity := model.DragonflyEntity{
		ID:          id,
		Cluster:     request.Cluster,
		Region:      request.Region,
		Zone:        request.Zone,
		Description: request.Description,
		Address:     request.Address,
		Creator:     user,
	}
	err = p.Store.Put(ctx, &dragonflyEntity)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("update dragonfly failed, err:%s", err.Error())
	}
	return &emptypb.Empty{}, nil
}

// CreateDistributedCacheRegistration 创建分布式缓存注册
func (p *PlatformService) CreateDistributedCacheRegistration(ctx context.Context, request *v1.CreateDistributedCacheRegistrationRequest) (*v1.CreateDistributedCacheRegistrationResponse, error) {
	// 用户是平台管理员
	account, ok := ctx.Value(constant.UserCtxKey).(string)
	if !ok {
		return nil, bcode.ErrorInvalidArgument("user not found")
	}
	if ok, err := p.RbacService.IsPlatformAdmin(ctx, account); err != nil || !ok {
		return nil, fmt.Errorf("user is not platform admin")
	}
	// 重复检查
	filter := datastore.FilterOptions{}
	filter.In = append(filter.In, datastore.InQueryOption{
		Key:    "region",
		Values: []string{request.CacheRegion},
	})
	filter.In = append(filter.In, datastore.InQueryOption{
		Key:    "zone",
		Values: []string{request.CacheZone},
	})
	count, err := p.Store.Count(ctx, &model.DistributedCacheRegistrationEntity{}, &filter)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("count distributed cache registration failed, err:%s", err.Error())
	}
	if count > 0 {
		return nil, bcode.ErrorInvalidArgument("storage framework already exists")
	}

	status, err := p.getDistributedCacheStatus(ctx, request.CacheRegion, request.CacheZone, request.Cluster)
	if err != nil {
		klog.Errorf("get distributed cache status failed, err:%s", err.Error())
	}

	entity := model.DistributedCacheRegistrationEntity{
		ID:          primitive.NewObjectID(),
		Region:      request.CacheRegion,
		RegionName:  request.CacheRegionName,
		Zone:        request.CacheZone,
		ZoneName:    request.CacheZoneName,
		Cluster:     request.Cluster,
		ClusterName: request.ClusterName,
		Creator:     account,
		Framework:   model.CacheFramework(request.CacheFramework),
		Enabled:     request.Enabled,
		Status:      status,
	}
	err = p.Store.Add(ctx, &entity)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("create distributed cache registration failed, err:%s", err.Error())
	}
	return &v1.CreateDistributedCacheRegistrationResponse{}, nil
}

func (p *PlatformService) getDistributedCacheStatus(ctx context.Context, region, zone, cluster string) (model.DistributedCacheStatus, error) {
	capacity, err := distributedcache.Instance().GetFlashnodeInfo(ctx, &distributedcache.GetFlashnodeInfoRequest{
		Cluster: cluster,
		Zone:    zone,
	})
	if err != nil {
		return model.DistributedCacheStatus{}, err
	}
	totalCapacity := int64(0)
	totalUsedCapacity := int64(0)
	for _, instance := range capacity.Instances {
		totalCapacity += instance.TotalWeight
		totalUsedCapacity += instance.UsedWeight
	}
	return model.DistributedCacheStatus{
		Capacity: totalCapacity,
		Used:     totalUsedCapacity,
	}, nil
}

// DeleteDistributedCacheRegistration 删除分布式缓存注册
func (p *PlatformService) DeleteDistributedCacheRegistration(ctx context.Context, request *v1.DeleteDistributedCacheRegistrationRequest) (*v1.DeleteDistributedCacheRegistrationResponse, error) {
	// 用户是平台管理员
	account, ok := ctx.Value(constant.UserCtxKey).(string)
	if !ok {
		return nil, bcode.ErrorInvalidArgument("user not found")
	}
	if ok, err := p.RbacService.IsPlatformAdmin(ctx, account); err != nil || !ok {
		return nil, fmt.Errorf("user is not platform admin")
	}
	id, err := primitive.ObjectIDFromHex(request.Id)
	if err != nil {
		return nil, bcode.ErrorInvalidArgument("invalid id")
	}
	err = p.Store.Delete(ctx, &model.DistributedCacheRegistrationEntity{
		ID: id,
	})
	if err != nil {
		return nil, bcode.ErrorServerInternalError("delete distributed cache registration failed, err:%s", err.Error())
	}
	return &v1.DeleteDistributedCacheRegistrationResponse{}, nil
}

// UpdateDistributedCacheRegistration 更新分布式缓存注册
func (p *PlatformService) UpdateDistributedCacheRegistration(ctx context.Context, request *v1.UpdateDistributedCacheRegistrationRequest) (*v1.UpdateDistributedCacheRegistrationResponse, error) {
	// 用户是平台管理员
	account, ok := ctx.Value(constant.UserCtxKey).(string)
	if !ok {
		return nil, bcode.ErrorInvalidArgument("user not found")
	}
	if ok, err := p.RbacService.IsPlatformAdmin(ctx, account); err != nil || !ok {
		return nil, fmt.Errorf("user is not platform admin")
	}
	// 重复检查
	filter := datastore.FilterOptions{}
	filter.In = append(filter.In, datastore.InQueryOption{
		Key:    "region",
		Values: []string{request.CacheRegion},
	})
	filter.In = append(filter.In, datastore.InQueryOption{
		Key:    "zone",
		Values: []string{request.CacheZone},
	})
	entities, err := p.Store.List(ctx, &model.DistributedCacheRegistrationEntity{}, &datastore.ListOptions{
		FilterOptions: filter,
	})
	if err != nil {
		return nil, bcode.ErrorServerInternalError("count storage framework failed, err:%s", err.Error())
	}
	if len(entities) > 0 {
		entity := entities[0].(*model.DistributedCacheRegistrationEntity)
		if entity.ID.Hex() != request.Id {
			return nil, bcode.ErrorInvalidArgument("distributed cache registration already exists")
		}
	}
	id, err := primitive.ObjectIDFromHex(request.Id)
	if err != nil {
		return nil, bcode.ErrorInvalidArgument("invalid id")
	}
	status, err := p.getDistributedCacheStatus(ctx, request.CacheRegion, request.CacheZone, request.Cluster)
	if err != nil {
		klog.Errorf("get distributed cache status failed, err:%s", err.Error())
	}
	entity := model.DistributedCacheRegistrationEntity{
		ID:          id,
		Region:      request.CacheRegion,
		RegionName:  request.CacheRegionName,
		Zone:        request.CacheZone,
		ZoneName:    request.CacheZoneName,
		Cluster:     request.Cluster,
		ClusterName: request.ClusterName,
		Creator:     account,
		Framework:   model.CacheFramework(request.CacheFramework),
		Enabled:     request.Enabled,
		Status:      status,
	}
	err = p.Store.Put(ctx, &entity)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("update distributed cache registration failed, err:%s", err.Error())
	}
	return &v1.UpdateDistributedCacheRegistrationResponse{}, nil
}

// ListDistributedCacheRegistrations 获取分布式缓存注册列表
func (p *PlatformService) ListDistributedCacheRegistrations(ctx context.Context, request *v1.ListDistributedCacheRegistrationsRequest) (*v1.ListDistributedCacheRegistrationsResponse, error) {
	filter := datastore.FilterOptions{}
	total, err := p.Store.Count(ctx, &model.DistributedCacheRegistrationEntity{}, &filter)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("count storage framework failed, err:%s", err.Error())
	}
	if total == 0 {
		return &v1.ListDistributedCacheRegistrationsResponse{
			DistributedCacheRegistrations: []*v1.DistributedCacheRegistration{},
			Total:                         0,
		}, nil
	}
	entities, err := p.Store.List(ctx, &model.DistributedCacheRegistrationEntity{}, &datastore.ListOptions{
		FilterOptions: filter,
		Page:          int(request.Page),
		PageSize:      int(request.PageSize),
		SortBy: []datastore.SortOption{
			{
				Key:   "createTime",
				Order: datastore.SortOrderDescending,
			},
		},
	})
	if err != nil {
		return nil, bcode.ErrorServerInternalError("list storage framework failed, err:%s", err.Error())
	}
	var distributedCacheRegistrations []*v1.DistributedCacheRegistration
	for _, entity := range entities {
		en := entity.(*model.DistributedCacheRegistrationEntity)
		distributedCacheRegistrations = append(distributedCacheRegistrations, &v1.DistributedCacheRegistration{
			Id:              en.ID.Hex(),
			CacheRegion:     en.Region,
			CacheRegionName: en.RegionName,
			CacheZone:       en.Zone,
			CacheZoneName:   en.ZoneName,
			CacheFramework:  string(en.Framework),
			Creator:         en.Creator,
			Cluster:         en.Cluster,
			ClusterName:     en.ClusterName,
			Enabled:         en.Enabled,
			Status: &v1.DistributedCacheRegistrationStatus{
				Capacity: en.Status.Capacity,
				Used:     en.Status.Used,
			},
		})
	}
	return &v1.ListDistributedCacheRegistrationsResponse{
		DistributedCacheRegistrations: distributedCacheRegistrations,
		Total:                         int32(total),
	}, nil
}

// ListDistributedCacheRegistrationRegions 获取分布式缓存注册地区
func (p *PlatformService) ListDistributedCacheRegistrationRegions(ctx context.Context, request *v1.ListDistributedCacheRegistrationRegionsRequest) (*v1.ListDistributedCacheRegistrationRegionsResponse, error) {
	entities, err := p.Store.List(ctx, &model.DistributedCacheRegistrationEntity{
		Enabled: true,
	}, nil)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("list storage framework region failed, err:%s", err.Error())
	}
	regionsMap := map[string]*model.DistributedCacheRegistrationEntity{}
	for _, entity := range entities {
		en := entity.(*model.DistributedCacheRegistrationEntity)
		regionsMap[en.Region] = en
	}
	regions := make([]*v1.DistributedCacheRegistrationRegion, 0, len(regionsMap))
	for region := range regionsMap {
		regions = append(regions, &v1.DistributedCacheRegistrationRegion{
			CacheRegion:     region,
			CacheRegionName: regionsMap[region].RegionName,
		})
	}
	return &v1.ListDistributedCacheRegistrationRegionsResponse{
		DistributedCacheRegistrationRegions: regions,
	}, nil
}

// ListDistributedCacheRegistrationZones 获取分布式缓存注册可用区
func (p *PlatformService) ListDistributedCacheRegistrationZones(ctx context.Context, request *v1.ListDistributedCacheRegistrationZonesRequest) (*v1.ListDistributedCacheRegistrationZonesResponse, error) {
	entities, err := p.Store.List(ctx, &model.DistributedCacheRegistrationEntity{
		Region:  request.CacheRegion,
		Enabled: true,
	}, nil)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("list cache zone failed, err:%s", err.Error())
	}
	zonesMap := map[string]*model.DistributedCacheRegistrationEntity{}
	for _, entity := range entities {
		en := entity.(*model.DistributedCacheRegistrationEntity)
		zonesMap[en.Zone] = en
	}
	zones := make([]*v1.DistributedCacheRegistrationZone, 0, len(zonesMap))
	for zone := range zonesMap {
		zones = append(zones, &v1.DistributedCacheRegistrationZone{
			CacheZone:     zone,
			CacheZoneName: zonesMap[zone].ZoneName,
		})
	}
	return &v1.ListDistributedCacheRegistrationZonesResponse{
		DistributedCacheRegistrationZones: zones,
	}, nil
}

// ListDistributedCacheRegistrationClusters 获取分布式缓存注册集群
func (p *PlatformService) ListDistributedCacheRegistrationClusters(ctx context.Context, request *v1.ListDistributedCacheRegistrationClustersRequest) (*v1.ListDistributedCacheRegistrationClustersResponse, error) {
	entities, err := p.Store.List(ctx, &model.DistributedCacheRegistrationEntity{}, &datastore.ListOptions{
		FilterOptions: datastore.FilterOptions{
			In: []datastore.InQueryOption{
				{
					Key:    "region",
					Values: []string{request.CacheRegion},
				},
				{
					Key:    "zone",
					Values: []string{request.CacheZone},
				},
			},
		},
	})
	if err != nil {
		return nil, bcode.ErrorServerInternalError("list cache cluster failed, err:%s", err.Error())
	}
	clusters := make([]*v1.DistributedCacheRegistrationCluster, 0, len(entities))
	for _, entity := range entities {
		en := entity.(*model.DistributedCacheRegistrationEntity)
		clusters = append(clusters, &v1.DistributedCacheRegistrationCluster{
			Cluster:     en.Cluster,
			ClusterName: en.ClusterName,
		})
	}
	return &v1.ListDistributedCacheRegistrationClustersResponse{
		DistributedCacheRegistrationClusters: clusters,
	}, nil
}

// ListAvailableRegions 获取所有可用区域
func (p *PlatformService) ListAvailableRegions(ctx context.Context) (map[string]*model.DistributedCacheRegistrationEntity, error) {
	entities, err := p.Store.List(ctx, &model.DistributedCacheRegistrationEntity{
		Enabled: true,
	}, nil)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("list available regions failed, err:%s", err.Error())
	}
	regions := make(map[string]*model.DistributedCacheRegistrationEntity)
	for _, entity := range entities {
		en := entity.(*model.DistributedCacheRegistrationEntity)
		regions[fmt.Sprintf("%s-%s", en.Region, en.Zone)] = en
	}
	return regions, nil
}
