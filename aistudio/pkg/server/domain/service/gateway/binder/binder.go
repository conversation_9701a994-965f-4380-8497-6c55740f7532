package binder

import (
	"context"
	"git.lianjia.com/cloudnative/kic/kic-platform/aistudio/pkg/server/domain/model"
	"git.lianjia.com/cloudnative/kic/kic-platform/aistudio/pkg/server/domain/service/gateway/binder/service"
	"git.lianjia.com/cloudnative/kic/kic-platform/aistudio/pkg/server/domain/service/gateway/binder/traefik"
	"git.lianjia.com/cloudnative/kic/kic-platform/apis/aistudio/bcode"
	v1 "git.lianjia.com/cloudnative/kic/kic-platform/apis/aistudio/gateway/v1"
)

var binders map[string]service.Binder

func init() {
	binders = map[string]service.Binder{
		"traefik": &traefik.Binder{},
	}
}

func Bind(ctx context.Context, request *v1.CreateOrUpdateGatewayRequest) (*v1.GatewayStatus, error) {
	binder, ok := binders[request.GatewayClass]
	if !ok {
		return nil, bcode.ErrorServerInternalError("gateway binder[%s] not found", request.GatewayClass)
	}
	return binder.Bind(ctx, request)
}

func GetStatus(ctx context.Context, gatewayEntity *model.GatewayEntity) (*v1.GatewayStatus, error) {
	binder, ok := binders[gatewayEntity.GatewayClass]
	if !ok {
		return nil, bcode.ErrorServerInternalError("gateway binder[%s] not found", gatewayEntity.GatewayClass)
	}
	return binder.GetStatus(ctx, gatewayEntity)
}
