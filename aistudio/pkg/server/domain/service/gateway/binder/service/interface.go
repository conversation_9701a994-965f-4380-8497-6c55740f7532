package service

import (
	"context"
	"git.lianjia.com/cloudnative/kic/kic-platform/aistudio/pkg/server/domain/model"
	v1 "git.lianjia.com/cloudnative/kic/kic-platform/apis/aistudio/gateway/v1"
)

type Binder interface {
	// Bind 绑定一个网关内容
	Bind(ctx context.Context, request *v1.CreateOrUpdateGatewayRequest) (*v1.GatewayStatus, error)
	// GetStatus 获取网关状态
	GetStatus(ctx context.Context, gatewayEntity *model.GatewayEntity) (*v1.GatewayStatus, error)
}

type BindRequest struct {
	ClusterName  string
	Namespace    string
	GatewayClass string
	GatewayName  string
}
