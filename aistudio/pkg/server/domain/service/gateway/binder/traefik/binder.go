package traefik

import (
	"context"
	"git.lianjia.com/cloudnative/kic/kic-platform/aistudio/pkg/server/domain/model"
	"git.lianjia.com/cloudnative/kic/kic-platform/aistudio/pkg/server/domain/service/gateway/binder/service"
	"git.lianjia.com/cloudnative/kic/kic-platform/apis/aistudio/bcode"
	v1 "git.lianjia.com/cloudnative/kic/kic-platform/apis/aistudio/gateway/v1"
	"git.lianjia.com/cloudnative/kic/kic-platform/apis/common"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/apis/scheduling.kcs.io/v1alpha1"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/multicluster"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/gateway-api/apis/v1beta1"
	"time"
)

var _ service.Binder = (*Binder)(nil)

type Binder struct {
}

func (p *Binder) GetStatus(ctx context.Context, gatewayEntity *model.GatewayEntity) (*v1.GatewayStatus, error) {
	targetCluster := multicluster.Instance().GetCluster(gatewayEntity.Cluster)
	if targetCluster == nil {
		return nil, bcode.ErrorKubernetesClusterNotFound("cluster[%s] not found", gatewayEntity.Cluster)
	}
	workspaceCR, err := p.getWorkspaceCR(ctx, gatewayEntity.Workspace)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("failed to get workspace", err)
	}
	getGatewayRequest := &GetGatewayRequest{
		ClusterName:  gatewayEntity.Cluster,
		Namespace:    workspaceCR.Spec.Namespace,
		GatewayClass: gatewayEntity.GatewayClass,
		GatewayName:  gatewayEntity.Name,
	}
	gatewayCR, err := p.getGatewayCR(ctx, getGatewayRequest)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("failed to get gateway", err)
	}
	return ConvertToGatewayStatus(gatewayCR), nil
}

func (p *Binder) Bind(ctx context.Context, request *v1.CreateOrUpdateGatewayRequest) (*v1.GatewayStatus, error) {
	targetCluster := multicluster.Instance().GetCluster(request.Cluster)
	if targetCluster == nil {
		return nil, bcode.ErrorKubernetesClusterNotFound("cluster[%s] not found", request.Cluster)
	}
	workspaceCR, err := p.getWorkspaceCR(ctx, request.WorkspaceName)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("failed to get workspace", err)
	}
	getGatewayRequest := &GetGatewayRequest{
		ClusterName:  request.Cluster,
		Namespace:    workspaceCR.Spec.Namespace,
		GatewayClass: request.GatewayClass,
		GatewayName:  request.Name,
	}
	gatewayCR, err := p.getGatewayCR(ctx, getGatewayRequest)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("failed to get gateway", err)
	}
	return ConvertToGatewayStatus(gatewayCR), nil
}

type GetGatewayRequest struct {
	ClusterName  string
	Namespace    string
	GatewayClass string
	GatewayName  string
}

func (p *Binder) getGatewayCR(ctx context.Context, request *GetGatewayRequest) (*v1beta1.Gateway, error) {
	targetCluster := multicluster.Instance().GetCluster(request.ClusterName)
	if targetCluster == nil {
		return nil, bcode.ErrorKubernetesClusterNotFound("cluster[%s] not found", request.ClusterName)
	}
	var gw = v1beta1.Gateway{}
	err := targetCluster.Direct().Get(ctx, client.ObjectKey{Name: request.GatewayName, Namespace: request.Namespace}, &gw)
	if err != nil {
		if apierrors.IsNotFound(err) {
			return nil, bcode.ErrorGatewayNotFound("gateway[%s/%s] not found", request.Namespace, request.GatewayName)
		}
		return nil, bcode.ErrorServerInternalError("failed to get gateway", err)
	}
	return &gw, nil
}

func (p *Binder) getWorkspaceCR(ctx context.Context, workspaceName string) (*v1alpha1.Workspace, error) {
	workspace := &v1alpha1.Workspace{}
	err := multicluster.Instance().GetLocalCluster().Direct().Get(ctx, client.ObjectKey{Name: workspaceName}, workspace)
	if err != nil {
		if apierrors.IsNotFound(err) {
			return nil, bcode.ErrorWorkspaceNotFound("workspace[%s] not found", workspaceName)
		}
		return nil, bcode.ErrorServerInternalError("get workspace[%s] error, err: %v", workspaceName, err)
	}
	return workspace, nil
}

func ConvertToGatewayStatus(gateway *v1beta1.Gateway) *v1.GatewayStatus {
	gatewayStatus := &v1.GatewayStatus{
		Conditions: ConvertToGatewayCondition(gateway.Status.Conditions),
	}
	return gatewayStatus
}

func ConvertToGatewayCondition(conditions []metav1.Condition) []*common.Condition {
	var result []*common.Condition
	for _, c := range conditions {
		result = append(result, &common.Condition{
			Type:               c.Type,
			Status:             ConvertConditionStatusToCommonConditionStatus(c.Status),
			Reason:             c.Reason,
			Message:            c.Message,
			LastTransitionTime: c.LastTransitionTime.Format(time.DateTime),
		})
	}
	return result
}

func ConvertConditionStatusToCommonConditionStatus(conditionStatus metav1.ConditionStatus) common.Condition_ConditionStatus {
	switch conditionStatus {
	case metav1.ConditionTrue:
		return common.Condition_TRUE
	case metav1.ConditionFalse:
		return common.Condition_FALSE
	case metav1.ConditionUnknown:
		return common.Condition_UNKNOWN
	}
	return common.Condition_UNKNOWN
}
