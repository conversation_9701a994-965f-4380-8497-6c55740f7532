package service

import (
	"context"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/property"
)

var serviceBeans map[string]Interface

func init() {
	serviceBeans = make(map[string]Interface)
}

func RegisterServiceBean(beanName string, bean Interface) {
	serviceBeans[beanName] = bean
}

func GetAllServiceBeans() map[string]interface{} {
	beans := make(map[string]interface{})
	for k, bean := range serviceBeans {
		beans[k] = bean
	}
	return beans
}

func GetAllServiceBeanInterface() map[string]Interface {
	return serviceBeans
}

func GetBean(beanName string) interface{} {
	return serviceBeans[beanName]
}

type Interface interface {
	Init(ctx context.Context, properties property.EnvironmentProperty) error
}
