package service

import (
	"context"
	"fmt"
	"git.lianjia.com/cloudnative/kic/kic-platform/aistudio/pkg/server/domain/model"
	"git.lianjia.com/cloudnative/kic/kic-platform/apis/aistudio/bcode"
	v1 "git.lianjia.com/cloudnative/kic/kic-platform/apis/aistudio/cost/v1"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/infrastructure/datastore"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/property"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/third_party/oceanbase"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/util"
	kratoslog "github.com/go-kratos/kratos/v2/log"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"google.golang.org/protobuf/types/known/emptypb"
	"net/http"
	"strconv"
	"strings"
	"time"
)

var _ v1.CostServiceHTTPServer = (*CostService)(nil)

func NewCostService() *CostService {
	return &CostService{}
}

type CostService struct {
	Logger *kratoslog.Helper   `inject:"logger"`
	Store  datastore.DataStore `inject:"datastore"`
}

func (c *CostService) ListProductSpecificationsUsagesForKcsCost(ctx context.Context, option *v1.ListProductSpecificationsUsagesForKcsCostOption) (*v1.ListProductSpecificationsUsagesForKcsCostResult, error) {
	startTime, endTime, queryTable, err := getTimeRangeAndQueryTableForPeriod(option.TimePeriod)
	if err != nil {
		return nil, bcode.ErrorInvalidArgument("invalid time range: %v", err)
	}

	var filterOptions oceanbase.FilterOptions
	if option.ServiceSpecificationName != "" {
		filterOptions.PreciseQuery = append(filterOptions.PreciseQuery, oceanbase.PreciseQueryOption{
			Key:   "service_specification_name",
			Value: option.ServiceSpecificationName,
		})
	}

	filterOptions.RangeQuery = append(filterOptions.RangeQuery, oceanbase.RangeQueryOption{
		Key:       "time",
		StartTime: startTime,
		EndTime:   endTime,
	})

	currentPage, pageSize := int32(1), int32(10)
	if option.CurrentPage != 0 {
		currentPage = option.CurrentPage
	}

	if option.PageSize != 0 {
		pageSize = option.PageSize
	}

	var data []*model.CostData
	if err := oceanbase.Instance().QueryData(ctx, queryTable, &oceanbase.QueryOptions{
		FilterOptions: filterOptions,
		Page:          int(currentPage),
		PageSize:      int(pageSize),
	}, &data); err != nil {
		return nil, bcode.ErrorServerInternalError("failed to query cost data from oceanBase, err: %v", err)
	}

	totalCount, err := oceanbase.Instance().Count(ctx, queryTable, &oceanbase.QueryOptions{
		FilterOptions: filterOptions,
	})
	if err != nil {
		return nil, bcode.ErrorServerInternalError("failed to count cost data from oceanBase, err: %v", err)
	}

	var usages []*v1.KcsCostServiceUsage

	for _, d := range data {
		usages = append(usages, &v1.KcsCostServiceUsage{
			ServiceUsageId:            fmt.Sprintf("%s,%s", d.ResourceId, d.Workspace),
			ServiceUsageIdDescription: fmt.Sprintf("%s,空间", d.ResourceIdExplanation),
			Usage:                     d.Usage,
			Owner:                     d.Admin,
		})
	}

	return &v1.ListProductSpecificationsUsagesForKcsCostResult{
		Message: "success",
		Code:    http.StatusOK,
		Data: &v1.KcsCostServiceUsageData{
			CurrentPage: currentPage,
			PageSize:    pageSize,
			TotalCount:  int32(totalCount),
			List:        usages,
		},
	}, nil
}

func (c *CostService) GetProductSpecificationsTotalCost(ctx context.Context, option *v1.ListProductSpecificationsCostDetailOption) (*v1.GetProductSpecificationsTotalCostResult, error) {
	var queryTable string
	var startTime, endTime time.Time
	var err error
	wt := option.WindowType
	switch wt {
	case v1.WindowType_Day:
		queryTable = model.ResourceDailyCostTableName
		startTime, endTime, err = convertTimeByLayout(option.DayTimestampRange, "2006-01-02")
	case v1.WindowType_Month:
		queryTable = model.ResourceMonthlyCostTableName
		startTime, endTime, err = convertTimeByLayout(option.MonthTimestampRange, "2006-01")
	default:
		return nil, bcode.ErrorInvalidArgument("invalid windowType argument")
	}
	if err != nil {
		return nil, bcode.ErrorInvalidArgument("invalid time range: %v", err)
	}

	var filterOptions oceanbase.FilterOptions

	if option.ServiceSpecificationName != "" {
		filterOptions.PreciseQuery = append(filterOptions.PreciseQuery, oceanbase.PreciseQueryOption{
			Key:   "service_specification_name",
			Value: option.ServiceSpecificationName,
		})
	}
	if option.WorkspaceName != "" {
		filterOptions.PreciseQuery = append(filterOptions.PreciseQuery, oceanbase.PreciseQueryOption{
			Key:   "workspace",
			Value: option.WorkspaceName,
		})
	}

	if option.ResourceId != "" {
		filterOptions.PreciseQuery = append(filterOptions.PreciseQuery, oceanbase.PreciseQueryOption{
			Key:   "resource_id",
			Value: option.ResourceId,
		})
	}

	if option.ResourceType != "" {
		filterOptions.PreciseQuery = append(filterOptions.PreciseQuery, oceanbase.PreciseQueryOption{
			Key:   "resource_type",
			Value: option.ResourceType,
		})
	}

	if option.ResourceSpecification != "" {
		filterOptions.PreciseQuery = append(filterOptions.PreciseQuery, oceanbase.PreciseQueryOption{
			Key:   "resource_specification",
			Value: option.ResourceSpecification,
		})
	}

	if option.Admin != "" {
		filterOptions.PreciseQuery = append(filterOptions.PreciseQuery, oceanbase.PreciseQueryOption{
			Key:   "admin",
			Value: option.Admin,
		})
	}

	filterOptions.RangeQuery = append(filterOptions.RangeQuery, oceanbase.RangeQueryOption{
		Key:       "time",
		StartTime: startTime,
		EndTime:   endTime,
	})

	var data []*model.TotalCost
	if err := oceanbase.Instance().QueryData(ctx, queryTable, &oceanbase.QueryOptions{
		FilterOptions: filterOptions,
		SelectField:   "sum(`cost`) as total_cost",
	}, &data); err != nil {
		return nil, bcode.ErrorServerInternalError("failed to query cost data from oceanBase, err: %v", err)
	}
	totalCost := float64(0)
	if len(data) != 0 {
		totalCost = data[0].TotalCost
	}
	return &v1.GetProductSpecificationsTotalCostResult{
		TotalCost: totalCost,
	}, nil
}

func (c *CostService) ListProductSpecifications(ctx context.Context, option *v1.ListProductSpecificationsOption) (*v1.ListProductSpecificationsResult, error) {
	productSpecificationEntities, err := c.Store.List(ctx, &model.ProductSpecificationEntity{}, &datastore.ListOptions{
		Page:     int(option.Page),
		PageSize: int(option.PageSize),
	})
	if err != nil {
		return nil, bcode.ErrorServerInternalError("failed to list product specifications", err)
	}
	totalCount, err := c.Store.Count(ctx, &model.ProductSpecificationEntity{}, &datastore.FilterOptions{})
	if err != nil {
		return nil, bcode.ErrorServerInternalError("failed to count product specifications", err)
	}
	var result []*v1.ProductSpecification
	for _, entity := range productSpecificationEntities {
		productSpecification := entity.(*model.ProductSpecificationEntity)
		result = append(result, convertToProductSpecificationProto(productSpecification))
	}
	return &v1.ListProductSpecificationsResult{
		Page:                  option.Page,
		PageSize:              option.PageSize,
		ProductSpecifications: result,
		Total:                 totalCount,
	}, nil
}

func (c *CostService) Init(_ context.Context, _ property.EnvironmentProperty) error {
	return nil
}

func (c *CostService) CreateProductSpecification(ctx context.Context, request *v1.CreateOrUpdateProductSpecificationRequest) (*emptypb.Empty, error) {

	ok, err := productSpecificationExists(ctx, c.Store, request.ProductName, request.ServiceSpecificationName)
	if err != nil {
		return nil, bcode.ErrorServerInternalError("failed to check product specification exists", err)
	}
	if ok {
		return nil, bcode.ErrorProductSpecificationAlreadyExists("product specification[%s/%s] already exists", request.ProductName, request.ServiceSpecificationName)
	}
	productSpecificationEntity := &model.ProductSpecificationEntity{
		ID:                         primitive.NewObjectID(),
		ProductName:                request.ProductName,
		ProductNameCn:              request.ProductNameCn,
		ServiceSpecificationName:   request.ServiceSpecificationName,
		ServiceSpecificationNameCn: request.ServiceSpecificationNameCn,
		Price:                      request.Price,
		PriceUnit:                  request.PriceUnit,
		PriceType:                  request.PriceType.String(),
		UsageType:                  request.UsageType.String(),
		BillingMode:                request.BillingMode.String(),
		BillingCycle:               request.BillingCycle.String(),
		SpecificationExplanation:   request.SpecificationExplanation,
		IsEnabled:                  util.IsBeforeNow(request.EffectiveTimestamp),
		EffectiveResource:          request.EffectiveResource,
		EffectiveTimestamp:         request.EffectiveTimestamp,
	}
	if err := c.Store.Add(ctx, productSpecificationEntity); err != nil {
		return nil, bcode.ErrorServerInternalError("failed to create product specification", err)
	}
	return &emptypb.Empty{}, nil
}

func (c *CostService) DeleteProductSpecification(ctx context.Context, request *v1.DeleteProductSpecificationRequest) (*emptypb.Empty, error) {
	productSpecificationEntity, err := getProductSpecification(ctx, c.Store, request.ProductName, request.ServiceSpecificationName)
	if err != nil {
		return nil, bcode.ErrorProductSpecificationNotFound("failed to get product specification[%s/%s], err: %v", request.ProductName, request.ServiceSpecificationName, err)
	}

	if err := c.Store.Delete(ctx, productSpecificationEntity); err != nil {
		return nil, bcode.ErrorServerInternalError("failed to delete product specification", err)
	}
	return &emptypb.Empty{}, nil
}

func (c *CostService) GetProductSpecification(ctx context.Context, request *v1.GetProductSpecificationRequest) (*v1.ProductSpecification, error) {
	productSpecificationEntity, err := getProductSpecification(ctx, c.Store, request.ProductName, request.ServiceSpecificationName)
	if err != nil {
		return nil, bcode.ErrorProductSpecificationNotFound("failed to get product specification[%s/%s], err: %v", request.ProductName, request.ServiceSpecificationName, err)
	}
	return convertToProductSpecificationProto(productSpecificationEntity), nil
}

func (c *CostService) ListProductSpecificationsCostDetail(ctx context.Context, option *v1.ListProductSpecificationsCostDetailOption) (*v1.ListProductSpecificationsCostDetailResult, error) {
	var queryTable string
	var startTime, endTime time.Time
	var err error
	wt := option.WindowType
	switch wt {
	case v1.WindowType_Day:
		queryTable = model.ResourceDailyCostTableName
		startTime, endTime, err = convertTimeByLayout(option.DayTimestampRange, "2006-01-02")
	case v1.WindowType_Month:
		queryTable = model.ResourceMonthlyCostTableName
		startTime, endTime, err = convertTimeByLayout(option.MonthTimestampRange, "2006-01")
	default:
		return nil, bcode.ErrorInvalidArgument("invalid windowType argument")
	}

	if err != nil {
		return nil, bcode.ErrorInvalidArgument("invalid time range: %v", err)
	}
	var filterOptions oceanbase.FilterOptions

	if option.ServiceSpecificationName != "" {
		filterOptions.PreciseQuery = append(filterOptions.PreciseQuery, oceanbase.PreciseQueryOption{
			Key:   "service_specification_name",
			Value: option.ServiceSpecificationName,
		})
	}
	if option.WorkspaceName != "" {
		filterOptions.PreciseQuery = append(filterOptions.PreciseQuery, oceanbase.PreciseQueryOption{
			Key:   "workspace",
			Value: option.WorkspaceName,
		})
	}

	if option.ResourceId != "" {
		filterOptions.PreciseQuery = append(filterOptions.PreciseQuery, oceanbase.PreciseQueryOption{
			Key:   "resource_id",
			Value: option.ResourceId,
		})
	}

	if option.ResourceType != "" {
		filterOptions.PreciseQuery = append(filterOptions.PreciseQuery, oceanbase.PreciseQueryOption{
			Key:   "resource_type",
			Value: option.ResourceType,
		})
	}

	if option.ResourceSpecification != "" {
		filterOptions.PreciseQuery = append(filterOptions.PreciseQuery, oceanbase.PreciseQueryOption{
			Key:   "resource_specification",
			Value: option.ResourceSpecification,
		})
	}

	if option.Admin != "" {
		filterOptions.PreciseQuery = append(filterOptions.PreciseQuery, oceanbase.PreciseQueryOption{
			Key:   "admin",
			Value: option.Admin,
		})
	}

	filterOptions.RangeQuery = append(filterOptions.RangeQuery, oceanbase.RangeQueryOption{
		Key:       "time",
		StartTime: startTime,
		EndTime:   endTime,
	})

	//从oceanBase表中获取相关成本用量数据明细
	costTotalSelectField := "*,sum(`usage`) as total_usage, sum(`cost`) as total_cost"
	costTotalGroupBy := "resource_id,resource_type,resource_specification"
	var data []*model.CostDataTotal
	if err := oceanbase.Instance().QueryData(ctx, queryTable, &oceanbase.QueryOptions{
		FilterOptions: filterOptions,
		SelectField:   costTotalSelectField,
		GroupBy:       costTotalGroupBy,
		Page:          int(option.Page),
		PageSize:      int(option.PageSize),
	}, &data); err != nil {
		return nil, bcode.ErrorServerInternalError("failed to query cost data from oceanBase, err: %v", err)
	}

	totalCount, err := oceanbase.Instance().Count(ctx, queryTable, &oceanbase.QueryOptions{
		FilterOptions: filterOptions,
		SelectField:   costTotalSelectField,
		GroupBy:       costTotalGroupBy,
	})
	if err != nil {
		return nil, bcode.ErrorServerInternalError("failed to count cost data from oceanBase, err: %v", err)
	}

	var details []*v1.ProductSpecificationCostDetail
	var specCache = make(map[string]*model.ProductSpecificationEntity)
	for _, d := range data {
		cacheKey := fmt.Sprintf("%s/%s", d.ProductName, d.ServiceSpecificationName)
		entity, exists := specCache[cacheKey]
		if !exists {
			var err error
			if entity, err = getProductSpecification(ctx, c.Store, d.ProductName, d.ServiceSpecificationName); err != nil {
				c.Logger.Errorf("failed to get product specification[%s], err: %v", cacheKey, err)
				continue
			}
			specCache[cacheKey] = entity
		}
		details = append(details, convertToProductSpecificationCostDetailProto(entity, d))
	}

	return &v1.ListProductSpecificationsCostDetailResult{
		Page:                            option.Page,
		PageSize:                        option.PageSize,
		ProductSpecificationCostDetails: details,
		Total:                           totalCount,
	}, nil
}

func (c *CostService) ListResourceStatisticsUsages(ctx context.Context, option *v1.ListResourceStatisticsUsagesOption) (*v1.ListResourceStatisticsUsagesResult, error) {
	startTime, endTime, err := convertTimestamp(option.TimestampRange)
	if err != nil {
		return nil, bcode.ErrorInvalidArgument("invalid timestamp range: %v", err)
	}

	var filterOptions oceanbase.FilterOptions

	if option.WorkspaceName != "" {
		filterOptions.PreciseQuery = append(filterOptions.PreciseQuery, oceanbase.PreciseQueryOption{
			Key:   "workspace",
			Value: option.WorkspaceName,
		})
	}

	if option.ResourceType != "" {
		filterOptions.PreciseQuery = append(filterOptions.PreciseQuery, oceanbase.PreciseQueryOption{
			Key:   "resource_type",
			Value: option.ResourceType,
		})
	}

	if option.ResourceSpecification != "" {
		filterOptions.PreciseQuery = append(filterOptions.PreciseQuery, oceanbase.PreciseQueryOption{
			Key:   "resource_specification",
			Value: option.ResourceSpecification,
		})
	}

	if option.ResourceId != "" {
		filterOptions.PreciseQuery = append(filterOptions.PreciseQuery, oceanbase.PreciseQueryOption{
			Key:   "resource_id",
			Value: option.ResourceId,
		})
	}

	if option.Admin != "" {
		filterOptions.PreciseQuery = append(filterOptions.PreciseQuery, oceanbase.PreciseQueryOption{
			Key:   "admin",
			Value: option.Admin,
		})
	}

	filterOptions.RangeQuery = append(filterOptions.RangeQuery, oceanbase.RangeQueryOption{
		Key:       "time",
		StartTime: startTime,
		EndTime:   endTime,
	})

	//从oceanBase表中获取相关资源用量数据明细
	usageTotalSelectField := "*,sum(`usage`) as total_usage"
	usageTotalGroupBy := "resource_id,resource_type,resource_specification"
	var data []*model.ResourceUsageDataTotal
	if err := oceanbase.Instance().QueryData(ctx, model.ResourceUsageTableName, &oceanbase.QueryOptions{
		FilterOptions: filterOptions,
		SelectField:   usageTotalSelectField,
		GroupBy:       usageTotalGroupBy,
		Page:          int(option.Page),
		PageSize:      int(option.PageSize),
	}, &data); err != nil {
		return nil, bcode.ErrorServerInternalError("failed to query resource usage data from oceanBase, err: %v", err)
	}

	totalCount, err := oceanbase.Instance().Count(ctx, model.ResourceUsageTableName, &oceanbase.QueryOptions{
		FilterOptions: filterOptions,
		SelectField:   usageTotalSelectField,
		GroupBy:       usageTotalGroupBy,
	})
	if err != nil {
		return nil, bcode.ErrorServerInternalError("failed to count resource usage data from oceanBase, err: %v", err)
	}

	var result []*v1.ResourceUsage
	for _, d := range data {
		result = append(result, convertToResourceUsageProto(d))
	}

	return &v1.ListResourceStatisticsUsagesResult{
		Page:           option.Page,
		PageSize:       option.PageSize,
		ResourceUsages: result,
		Total:          totalCount,
	}, nil
}

func (c *CostService) UpdateProductSpecification(ctx context.Context, request *v1.CreateOrUpdateProductSpecificationRequest) (*emptypb.Empty, error) {
	productSpecificationEntity, err := getProductSpecification(ctx, c.Store, request.ProductName, request.ServiceSpecificationName)
	if err != nil {
		return nil, bcode.ErrorProductSpecificationNotFound("failed to get product specification[%s/%s], err: %v", request.ProductName, request.ServiceSpecificationName, err)
	}
	updateEntity := convertToUpdateProductSpecificationEntity(productSpecificationEntity, request)
	if err := c.Store.Put(ctx, updateEntity); err != nil {
		return nil, bcode.ErrorServerInternalError("failed to update product specification", err)
	}
	return &emptypb.Empty{}, nil
}

func convertTimestamp(timeStr string) (time.Time, time.Time, error) {
	if timeStr == "" {
		return time.Time{}, time.Time{}, fmt.Errorf("empty timestamp range string")
	}
	times := strings.Split(timeStr, ",")
	if len(times) != 2 {
		return time.Time{}, time.Time{}, fmt.Errorf("invalid timestamp range format, expected start,end")
	}
	startInt64, _ := strconv.ParseInt(times[0], 10, 64)
	endInt64, _ := strconv.ParseInt(times[1], 10, 64)
	startTime := time.Unix(startInt64, 0)
	endTime := time.Unix(endInt64, 0)
	return startTime, endTime, nil
}

func getTimeRangeAndQueryTableForPeriod(periodStr string) (time.Time, time.Time, string, error) {
	var startTime, endTime time.Time
	var queryTable string
	switch periodStr {
	case "lastmonth":
		startTime, endTime = GetLastMonthTimeRange()
		queryTable = model.ResourceMonthlyCostTableName
	case "yesterday":
		startTime, endTime = GetYesterdayTimeRange()
		queryTable = model.ResourceDailyCostTableName
	default:
		return time.Time{}, time.Time{}, "", fmt.Errorf("error timePeriod format, expected lastmonth,yesterday")
	}
	return startTime, endTime, queryTable, nil
}

func convertTimeByLayout(timeStr string, layout string) (time.Time, time.Time, error) {
	if timeStr == "" {
		return time.Time{}, time.Time{}, fmt.Errorf("empty time range string")
	}
	times := strings.Split(timeStr, ",")
	if len(times) != 2 {
		return time.Time{}, time.Time{}, fmt.Errorf("invalid time range format, expected start,end")
	}

	startTime, err := time.Parse(layout, times[0])
	if err != nil {
		return time.Time{}, time.Time{}, fmt.Errorf("invalid start time format, err: %v", err)
	}
	endTime, err := time.Parse(layout, times[1])
	if err != nil {
		return time.Time{}, time.Time{}, fmt.Errorf("invalid end time format, err: %v", err)
	}
	return startTime, endTime, nil
}

func convertToResourceUsageProto(dbData *model.ResourceUsageDataTotal) *v1.ResourceUsage {
	return &v1.ResourceUsage{
		ResourceId:            dbData.ResourceId,
		ResourceIdExplanation: dbData.ResourceIdExplanation,
		ResourceName:          dbData.ResourceName,
		Region:                dbData.Region,
		Workspace:             dbData.Workspace,
		ResourceType:          dbData.ResourceType,
		Usage:                 dbData.TotalUsage,
		UsageUnit:             dbData.UsageUnit,
		ResourceSpecification: dbData.ResourceSpecification,
		Admin:                 dbData.Admin,
		ExtensionField1:       dbData.ExtensionField1,
		ExtensionField2:       dbData.ExtensionField2,
		ExtensionField3:       dbData.ExtensionField3,
	}
}

func convertToProductSpecificationCostDetailProto(productSpecification *model.ProductSpecificationEntity, cost *model.CostDataTotal) *v1.ProductSpecificationCostDetail {
	return &v1.ProductSpecificationCostDetail{
		ProductNameCn:              cost.ProductNameCn,
		ServiceSpecificationNameCn: cost.ServiceSpecificationNameCn,
		UsageUnit:                  cost.UsageUnit,
		Usage:                      cost.TotalUsage,
		Cost:                       cost.TotalCost,
		Region:                     cost.Region,
		Workspace:                  cost.Workspace,
		ResourceId:                 cost.ResourceId,
		ResourceIdExplanation:      cost.ResourceIdExplanation,
		Admin:                      cost.Admin,
		BillingMode:                productSpecification.BillingMode,
		Price:                      productSpecification.Price,
		PriceUnit:                  productSpecification.PriceUnit,
		ResourceType:               cost.ResourceType,
		ResourceSpecification:      cost.ResourceSpecification,
	}
}

func productSpecificationExists(ctx context.Context, ds datastore.DataStore, pName, ssName string) (bool, error) {
	var productSpecificationEntity = &model.ProductSpecificationEntity{
		ProductName:              pName,
		ServiceSpecificationName: ssName,
	}
	count, err := ds.Count(ctx, productSpecificationEntity, &datastore.FilterOptions{})
	if err != nil {
		return false, err
	}
	if count == 0 {
		return false, nil
	}
	return true, nil
}

func getProductSpecification(ctx context.Context, ds datastore.DataStore, pName, ssName string) (*model.ProductSpecificationEntity, error) {
	var productSpecificationEntity = &model.ProductSpecificationEntity{
		ProductName:              pName,
		ServiceSpecificationName: ssName,
	}
	items, err := ds.List(ctx, productSpecificationEntity, &datastore.ListOptions{})
	if err != nil {
		return nil, err
	}
	if len(items) == 0 {
		return nil, datastore.ErrRecordNotExist
	}
	return items[0].(*model.ProductSpecificationEntity), nil
}

func convertToProductSpecificationProto(entity *model.ProductSpecificationEntity) *v1.ProductSpecification {
	productSpecification := &v1.ProductSpecification{
		Id:                         entity.ID.Hex(),
		CreateTime:                 util.TimeFormat(entity.CreateTime),
		UpdateTime:                 util.TimeFormat(entity.UpdateTime),
		ProductName:                entity.ProductName,
		ProductNameCn:              entity.ProductNameCn,
		ServiceSpecificationName:   entity.ServiceSpecificationName,
		ServiceSpecificationNameCn: entity.ServiceSpecificationNameCn,
		Price:                      entity.Price,
		PriceUnit:                  entity.PriceUnit,
		PriceType:                  v1.PriceType(v1.PriceType_value[entity.PriceType]),
		UsageType:                  v1.UsageType(v1.UsageType_value[entity.UsageType]),
		BillingMode:                v1.BillingMode(v1.BillingMode_value[entity.BillingMode]),
		BillingCycle:               v1.BillingCycle(v1.BillingCycle_value[entity.BillingCycle]),
		SpecificationExplanation:   entity.SpecificationExplanation,
		IsEnabled:                  entity.IsEnabled,
		EffectiveResource:          entity.EffectiveResource,
		EffectiveTimestamp:         entity.EffectiveTimestamp,
	}
	return productSpecification
}

func convertToUpdateProductSpecificationEntity(entity *model.ProductSpecificationEntity, request *v1.CreateOrUpdateProductSpecificationRequest) *model.ProductSpecificationEntity {
	entity.UpdateTime = time.Now()
	entity.ProductNameCn = request.ProductNameCn
	entity.ServiceSpecificationNameCn = request.ServiceSpecificationNameCn
	entity.Price = request.Price
	entity.PriceUnit = request.PriceUnit
	entity.PriceType = request.PriceType.String()
	entity.UsageType = request.UsageType.String()
	entity.BillingMode = request.BillingMode.String()
	entity.BillingCycle = request.BillingCycle.String()
	entity.SpecificationExplanation = request.SpecificationExplanation
	entity.IsEnabled = util.IsBeforeNow(request.EffectiveTimestamp)
	entity.EffectiveResource = request.EffectiveResource
	entity.EffectiveTimestamp = request.EffectiveTimestamp
	return entity
}
