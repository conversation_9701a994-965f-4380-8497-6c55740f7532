package repository

import (
	"context"
	"time"

	"git.lianjia.com/cloudnative/kic/kic-platform/aistudio/pkg/server/domain/model"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/infrastructure/datastore"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"k8s.io/klog/v2"
)

func GetAccessToken(ctx context.Context, ds datastore.DataStore, username, region string) (*model.AccessTokenEntity, error) {
	tokenEntity := &model.AccessTokenEntity{
		UserName: username,
		Region:   region,
	}
	tokens, err := ds.List(ctx, tokenEntity, nil)
	if err != nil {
		klog.Errorf("AccessTokenExists failed: %v", err)
		return nil, err
	}
	if len(tokens) == 0 {
		return nil, nil
	}
	return tokens[0].(*model.AccessTokenEntity), nil
}

func AccessTokenExists(ctx context.Context, ds datastore.DataStore, username, region string) (bool, error) {
	tokenEntity := &model.AccessTokenEntity{
		UserName: username,
		Region:   region,
	}
	tokens, err := ds.List(ctx, tokenEntity, nil)
	if err != nil {
		klog.Errorf("AccessTokenExists failed: %v", err)
		return false, err
	}
	return len(tokens) > 0, nil
}

func AddAccessToken(ctx context.Context, ds datastore.DataStore, username, region, token string) error {
	timeNow := time.Now()
	tokenEntity := &model.AccessTokenEntity{
		BaseModel: model.BaseModel{
			CreateTime: timeNow,
			UpdateTime: timeNow,
		},
		ID:       primitive.NewObjectID(),
		UserName: username,
		Region:   region,
		Token:    token,
	}
	err := ds.Add(ctx, tokenEntity)
	if err != nil {
		klog.Errorf("AddAccessToken failed: %v", err)
		return err
	}
	return nil
}

func GetUserByAccessToken(ctx context.Context, ds datastore.DataStore, token, region string) (*model.AccessTokenEntity, error) {
	tokenEntity := &model.AccessTokenEntity{
		Token:  token,
		Region: region,
	}
	tokens, err := ds.List(ctx, tokenEntity, nil)
	if err != nil {
		klog.Errorf("GetUserByAccessToken failed: %v", err)
		return nil, err
	}
	if len(tokens) == 0 {
		return nil, nil
	}
	return tokens[0].(*model.AccessTokenEntity), nil
}
