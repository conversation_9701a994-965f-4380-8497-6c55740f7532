package repository

import (
	"context"
	"git.lianjia.com/cloudnative/kic/kic-platform/aistudio/pkg/server/domain/model"
	v1 "git.lianjia.com/cloudnative/kic/kic-platform/apis/aistudio/gateway/v1"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/infrastructure/datastore"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"strconv"
)

func GatewayExists(ctx context.Context, ds datastore.DataStore, workspaceName, gatewayName string) (bool, error) {
	var gatewayModel = &model.GatewayEntity{
		Workspace: workspaceName,
		Name:      gatewayName,
	}
	items, err := ds.Count(ctx, gatewayModel, &datastore.FilterOptions{})
	if err != nil {
		return false, err
	}
	if items == 0 {
		return false, nil
	}
	return true, nil
}

func GetDefaultGateway(ctx context.Context, ds datastore.DataStore, workspaceName string) (*model.GatewayEntity, error) {
	var gatewayModel = &model.GatewayEntity{
		Workspace: workspaceName,
	}
	items, err := ds.List(ctx, gatewayModel, &datastore.ListOptions{
		FilterOptions: datastore.FilterOptions{
			Booleans: []datastore.BooleansQueryOption{
				{
					Key:   "isDefault",
					Value: true,
				},
			},
		},
	})
	if err != nil {
		return nil, err
	}
	if len(items) == 0 {
		return nil, datastore.ErrRecordNotExist
	}
	return items[0].(*model.GatewayEntity), nil
}

func GetGateway(ctx context.Context, ds datastore.DataStore, workspaceName, gatewayName string) (*model.GatewayEntity, error) {
	var gatewayModel = &model.GatewayEntity{
		Workspace: workspaceName,
		Name:      gatewayName,
	}
	items, err := ds.List(ctx, gatewayModel, &datastore.ListOptions{})
	if err != nil {
		return nil, err
	}
	if len(items) == 0 {
		return nil, datastore.ErrRecordNotExist
	}
	return items[0].(*model.GatewayEntity), nil
}

func ListGateway(ctx context.Context, ds datastore.DataStore, options *v1.ListGatewayOptions) ([]*model.GatewayEntity, error) {
	var gatewayModel = &model.GatewayEntity{
		Workspace: options.WorkspaceName,
	}
	if options.Zone != "" {
		gatewayModel.Zone = options.Zone
	}
	if options.WorkspaceName != "" {
		gatewayModel.Workspace = options.WorkspaceName
	}
	if options.Region != "" {
		gatewayModel.Region = options.Region
	}
	if options.Creator != "" {
		gatewayModel.Creator = options.Creator
	}
	if options.Idc != "" {
		gatewayModel.IDC = options.Idc
	}
	var filterOptions datastore.FilterOptions
	if options.Enabled != "" {
		enabled, err := strconv.ParseBool(options.Enabled)
		if err != nil {
			return nil, err
		}
		filterOptions.Booleans = append(filterOptions.Booleans, datastore.BooleansQueryOption{
			Key:   "enabled",
			Value: enabled,
		})
	}
	if options.IsDefault != "" {
		isDefault, err := strconv.ParseBool(options.IsDefault)
		if err != nil {
			return nil, err
		}
		filterOptions.Booleans = append(filterOptions.Booleans, datastore.BooleansQueryOption{
			Key:   "isDefault",
			Value: isDefault,
		})
	}

	items, err := ds.List(ctx, gatewayModel, &datastore.ListOptions{
		FilterOptions: datastore.FilterOptions{
			Booleans: filterOptions.Booleans,
		},
	})
	if err != nil {
		return nil, err
	}
	var result []*model.GatewayEntity
	for _, i := range items {
		result = append(result, i.(*model.GatewayEntity))
	}
	return result, nil
}

func ListGatewayServiceBindings(ctx context.Context, ds datastore.DataStore, options *v1.ListServiceBindingOptions) ([]*model.GatewayServiceBindingEntity, error) {
	var gatewayServiceBindingModel = &model.GatewayServiceBindingEntity{}
	if options.GatewayId != "" {
		id, err := primitive.ObjectIDFromHex(options.GatewayId)
		if err != nil {
			return nil, err
		}
		gatewayServiceBindingModel.GatewayId = id
	}
	if options.WorkspaceName != "" {
		gatewayServiceBindingModel.Workspace = options.WorkspaceName
	}
	if options.DestinationName != "" {
		gatewayServiceBindingModel.DestinationName = options.DestinationName
	}
	items, err := ds.List(ctx, gatewayServiceBindingModel, &datastore.ListOptions{
		SortBy: []datastore.SortOption{
			{
				Key:   "createTime",
				Order: datastore.SortOrderDescending,
			},
		},
	})
	if err != nil {
		return nil, err
	}
	var result []*model.GatewayServiceBindingEntity
	for _, i := range items {
		result = append(result, i.(*model.GatewayServiceBindingEntity))
	}
	return result, nil
}

func GetGatewayServiceBinding(ctx context.Context, ds datastore.DataStore, serviceBindingId string) (*model.GatewayServiceBindingEntity, error) {
	id, err := primitive.ObjectIDFromHex(serviceBindingId)
	if err != nil {
		return nil, err
	}
	var gatewayServiceBindingModel = &model.GatewayServiceBindingEntity{
		ID: id,
	}
	err = ds.Get(ctx, gatewayServiceBindingModel)
	if err != nil {
		return nil, err
	}
	return gatewayServiceBindingModel, nil
}
