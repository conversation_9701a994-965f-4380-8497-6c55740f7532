package repository

import (
	"context"
	"git.lianjia.com/cloudnative/kic/kic-platform/aistudio/pkg/server/domain/model"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/infrastructure/datastore"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func GetJobForWorkspace(ctx context.Context, ds datastore.DataStore, workspaceName, jobName string) (*model.JobEntity, error) {
	var jobModel = model.JobEntity{
		WorkspaceName: workspaceName,
		Name:          jobName,
	}
	items, err := ds.List(ctx, &jobModel, &datastore.ListOptions{})
	if err != nil {
		return nil, err
	}
	if len(items) == 0 {
		return nil, datastore.ErrRecordNotExist
	}
	return items[0].(*model.JobEntity), nil
}

func GetJobForJobID(ctx context.Context, ds datastore.DataStore, jobId primitive.ObjectID) (*model.JobEntity, error) {
	var jobModel = model.JobEntity{
		ID: jobId,
	}
	if err := ds.Get(ctx, &jobModel); err != nil {
		return nil, err
	}
	return &jobModel, nil
}

func JobExists(ctx context.Context, ds datastore.DataStore, workspaceName, jobName string) (bool, error) {

	var jobModel = model.JobEntity{
		WorkspaceName: workspaceName,
		Name:          jobName,
	}
	items, err := ds.List(ctx, &jobModel, &datastore.ListOptions{})
	if err != nil {
		return false, err
	}
	if len(items) == 0 {
		return false, nil
	}
	return true, nil
}

func GetJobStatusForJobID(ctx context.Context, ds datastore.DataStore, jobID primitive.ObjectID) (*model.JobStatusEntity, error) {
	var jobModel = model.JobStatusEntity{
		JobID: jobID,
	}
	if err := ds.Get(ctx, &jobModel); err != nil {
		return nil, err
	}
	return &jobModel, nil
}

func ListJobStatusForPhases(ctx context.Context, ds datastore.DataStore, workspaceName string, phases []string) ([]*model.JobStatusEntity, error) {
	jobStatusEntities, err := ds.List(ctx, &model.JobStatusEntity{
		WorkspaceName: workspaceName,
	}, &datastore.ListOptions{
		FilterOptions: datastore.FilterOptions{
			In: []datastore.InQueryOption{
				{
					Key:    "phase",
					Values: phases,
				},
			},
		},
	})
	if err != nil {
		return nil, err
	}
	var jss []*model.JobStatusEntity
	for _, entity := range jobStatusEntities {
		jss = append(jss, entity.(*model.JobStatusEntity))
	}
	return jss, nil
}

func GetJobTemplateForWorkspace(ctx context.Context, ds datastore.DataStore, workspaceName, jobTemplateName string) (*model.JobTemplateEntity, error) {
	var jobTemplateModel = model.JobTemplateEntity{
		WorkspaceName: workspaceName,
		Name:          jobTemplateName,
	}
	items, err := ds.List(ctx, &jobTemplateModel, &datastore.ListOptions{})
	if err != nil {
		return nil, err
	}
	if len(items) == 0 {
		return nil, datastore.ErrRecordNotExist
	}
	return items[0].(*model.JobTemplateEntity), nil
}

func GetTriggerForJobTemplate(ctx context.Context, ds datastore.DataStore, jobTemplateName, triggerName string) (*model.TriggerEntity, error) {
	var triggerEntity = model.TriggerEntity{
		TriggerName:     triggerName,
		JobTemplateName: jobTemplateName,
	}
	items, err := ds.List(ctx, &triggerEntity, &datastore.ListOptions{})
	if err != nil {
		return nil, err
	}
	if len(items) == 0 {
		return nil, datastore.ErrRecordNotExist
	}
	return items[0].(*model.TriggerEntity), nil
}
