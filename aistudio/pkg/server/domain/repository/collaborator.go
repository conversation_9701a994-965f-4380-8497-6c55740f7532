package repository

import (
	"context"
	"time"

	"git.lianjia.com/cloudnative/kic/kic-platform/aistudio/pkg/server/domain/model"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/infrastructure/datastore"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/third_party/gitea"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"k8s.io/klog/v2"
)

const (
	// AccessModeNone no access
	AccessModeNone = "none"
	// AccessModeRead read access
	AccessModeRead = "read"
	// AccessModeWrite write access
	AccessModeWrite = "write"
	// AccessModeAdmin admin access
	AccessModeAdmin = "admin"
	// AccessModeOwner owner
	AccessModeOwner = "owner"
)

func AddCollaborator(ctx context.Context, ds datastore.DataStore, workspaceName, repoType, repo, region, collaborator, mode string) error {
	owner := gitea.GetOwner(workspaceName, repoType)
	timeNow := time.Now()
	collaboratorEntity := &model.CollaboratorEntity{
		BaseModel: model.BaseModel{
			CreateTime: timeNow,
			UpdateTime: timeNow,
		},
		ID:           primitive.NewObjectID(),
		Owner:        owner,
		Repo:         repo,
		Collaborator: collaborator,
		AccessMode:   mode,
		Region:       region,
	}
	err := ds.Add(ctx, collaboratorEntity)
	if err != nil {
		klog.Errorf("AddCollaborator failed: %v", err)
		return err
	}
	return nil
}

func GetCollaborator(ctx context.Context, ds datastore.DataStore, workspaceName, repoType, repo, region, collaborator string) (*model.CollaboratorEntity, error) {
	owner := gitea.GetOwner(workspaceName, repoType)
	collaboratorEntity := &model.CollaboratorEntity{
		Owner:        owner,
		Repo:         repo,
		Region:       region,
		Collaborator: collaborator,
	}
	tokens, err := ds.List(ctx, collaboratorEntity, nil)
	if err != nil {
		klog.Errorf("GetCollaborator failed: %v", err)
		return nil, err
	}
	if len(tokens) == 0 {
		return nil, nil
	}
	return tokens[0].(*model.CollaboratorEntity), nil
}

func DeleteCollaborator(ctx context.Context, ds datastore.DataStore, workspaceName, repoType, repo, region, collaborator string) error {
	collaboratorEntity, err := GetCollaborator(ctx, ds, workspaceName, repoType, repo, region, collaborator)
	if err != nil {
		return err
	}
	err = ds.Delete(ctx, collaboratorEntity)
	if err != nil {
		klog.Errorf("DeleteCollaborator failed: %v", err)
		return err
	}
	return nil
}

func DeleteAllCollaborator(ctx context.Context, ds datastore.DataStore, workspaceName, repoType, repo, region string) error {
	owner := gitea.GetOwner(workspaceName, repoType)
	collaboratorEntity := model.CollaboratorEntity{
		Owner:  owner,
		Repo:   repo,
		Region: region,
	}
	collaboratorList, err := ds.List(ctx, &collaboratorEntity, nil)
	if err != nil {
		return err
	}
	if len(collaboratorList) == 0 {
		return nil
	}
	for _, collaborator := range collaboratorList {
		err := ds.Delete(ctx, collaborator)
		if err != nil {
			klog.Errorf("DeleteAllCollaborator failed: %v", err)
			return err
		}
	}
	return nil
}

func ChangeCollaborator(ctx context.Context, ds datastore.DataStore, workspaceName, repoType, repo, region, collaborator, mode string) error {
	collaboratorEntity, err := GetCollaborator(ctx, ds, workspaceName, repoType, repo, region, collaborator)
	if err != nil {
		return err
	}
	collaboratorEntity.AccessMode = mode
	err = ds.Put(ctx, collaboratorEntity)
	if err != nil {
		klog.Errorf("ChangeCollaborator failed: %v", err)
		return err
	}
	return nil
}

func ListCollaborator(ctx context.Context, ds datastore.DataStore, workspaceName, repoType, repo, region, collaborator string, page, pageSize int) ([]*model.CollaboratorEntity, int64, error) {
	owner := gitea.GetOwner(workspaceName, repoType)
	collaboratorEntity := model.CollaboratorEntity{
		Owner:        owner,
		Repo:         repo,
		Region:       region,
		Collaborator: collaborator,
	}
	options := &datastore.ListOptions{
		Page:     page,
		PageSize: pageSize,
	}
	cnt, err := ds.Count(ctx, &collaboratorEntity, nil)
	if err != nil {
		klog.Errorf("ListSSHKey failed: %v", err)
		return nil, 0, err
	}
	collaboratorList, err := ds.List(ctx, &collaboratorEntity, options)
	if err != nil {
		return nil, 0, err
	}
	if len(collaboratorList) == 0 {
		return nil, 0, nil
	}
	list := make([]*model.CollaboratorEntity, 0, len(collaboratorList))
	for _, item := range collaboratorList {
		list = append(list, item.(*model.CollaboratorEntity))
	}
	return list, cnt, nil
}

// GetCollaborators 获取多个模型的角色
func GetCollaborators(ctx context.Context, ds datastore.DataStore, workspaceName, repoType string, repos []string, region, collaborator string) (map[string]string, error) {
	owner := gitea.GetOwner(workspaceName, repoType)
	var collaboratorEntity model.CollaboratorEntity
	var filterOptions datastore.FilterOptions
	filterOptions.In = append(filterOptions.In, datastore.InQueryOption{
		Key:    "repo",
		Values: repos,
	})
	filterOptions.In = append(filterOptions.In, datastore.InQueryOption{
		Key:    "owner",
		Values: []string{owner},
	})
	collaborators, err := ds.List(ctx, &collaboratorEntity, &datastore.ListOptions{
		FilterOptions: filterOptions,
	})
	if err != nil {
		klog.Errorf("GetCollaborators failed: %v", err)
		return nil, err
	}
	collaboratorMap := map[string]string{}
	for _, collaborator := range collaborators {
		repo := collaborator.(*model.CollaboratorEntity).Repo
		accessMode := collaborator.(*model.CollaboratorEntity).AccessMode
		collaboratorMap[repo] = GetMaxAuth(collaboratorMap[repo], accessMode)
	}
	return collaboratorMap, nil
}

// CheckAuth 检查权限
func CheckAuth(needAuth, accessMode string) bool {
	switch needAuth {
	case AccessModeAdmin:
		if accessMode == AccessModeAdmin ||
			accessMode == AccessModeOwner {
			return true
		}
	case AccessModeWrite:
		if accessMode == AccessModeAdmin ||
			accessMode == AccessModeOwner ||
			accessMode == AccessModeWrite {
			return true
		}
	case AccessModeRead:
		if accessMode == AccessModeAdmin ||
			accessMode == AccessModeOwner ||
			accessMode == AccessModeWrite ||
			accessMode == AccessModeRead {
			return true
		}
	case AccessModeNone:
		return true
	case "":
		return true
	}
	return false
}

func GetMaxAuth(auth1, auth2 string) string {
	mp := map[string]int{
		AccessModeOwner: 5,
		AccessModeAdmin: 4,
		AccessModeWrite: 3,
		AccessModeRead:  2,
		AccessModeNone:  1,
		"":              0,
	}
	if mp[auth1] > mp[auth2] {
		return auth1
	}
	return auth2
}
