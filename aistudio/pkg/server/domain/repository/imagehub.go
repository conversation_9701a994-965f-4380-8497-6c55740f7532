package repository

import (
	"context"
	"errors"
	"fmt"

	"git.lianjia.com/cloudnative/kic/kic-platform/aistudio/pkg/server/domain/model"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/infrastructure/datastore"
	kratoslog "github.com/go-kratos/kratos/v2/log"
)

func GetImageForImageTag(ctx context.Context, ds datastore.DataStore, namespace, imageName, tag string) (*model.ImageHubEntity, error) {
	var imageModel model.ImageHubEntity
	var filterOptions datastore.FilterOptions
	filterOptions.In = append(filterOptions.In, datastore.InQueryOption{
		Key:    "namespace",
		Values: []string{namespace},
	})
	filterOptions.In = append(filterOptions.In, datastore.InQueryOption{
		Key:    "name",
		Values: []string{imageName},
	})
	filterOptions.In = append(filterOptions.In, datastore.InQueryOption{
		Key:    "tag",
		Values: []string{tag},
	})
	images, err := ds.List(ctx, &imageModel, &datastore.ListOptions{
		FilterOptions: filterOptions,
	})
	if err != nil {
		return nil, err
	}
	if len(images) == 0 {
		return nil, datastore.ErrRecordNotExist
	} else if len(images) > 1 {
		kratoslog.Errorf("multiple images found for image %s:%s", imageName, tag)
		return nil, errors.New("multiple images found")
	} else {
		return images[0].(*model.ImageHubEntity), nil
	}
}

func GetImageHubStatus(ctx context.Context, ds datastore.DataStore, workspaceName, region string) (*model.ImageHubStatusEntity, error) {
	var filterOptions datastore.FilterOptions
	filterOptions.In = append(filterOptions.In, datastore.InQueryOption{
		Key:    "workspaceName",
		Values: []string{workspaceName},
	})
	filterOptions.In = append(filterOptions.In, datastore.InQueryOption{
		Key:    "region",
		Values: []string{region},
	})
	var imageHubStatus model.ImageHubStatusEntity
	images, err := ds.List(ctx, &imageHubStatus, &datastore.ListOptions{
		FilterOptions: filterOptions,
	})
	if err != nil {
		return nil, err
	}
	if len(images) == 0 {
		return nil, datastore.ErrRecordNotExist
	} else if len(images) > 1 {
		return nil, fmt.Errorf("multiple image hub status found in workspace[%s] and region[%s]", workspaceName, region)
	}
	return images[0].(*model.ImageHubStatusEntity), nil
}
