package model

import (
	"fmt"
	"time"
)

func init() {
}

type Type string

type EventEntity struct {
	Source      string    `json:"source"`      //事件源
	Name        string    `json:"name"`        //事件名称
	DisplayName string    `json:"displayName"` //事件的展示名称
	CreateAt    time.Time `json:"createTime"`  //创建时间
	Message     string    `json:"message"`     //事件内容
	Reason      string    `json:"reason"`      //事件原因
	Scope       string    `json:"scope"`       //事件范围,如果是平台事件,可以是platform:*;如果是workspace事件,可以是workspace:${workspaceName}
	Type        string    `json:"type"`        //事件类型
	Operator    string    `json:"operator"`
}

func (e *EventEntity) SetCreateTime(time time.Time) {
	return
}

func (e *EventEntity) SetUpdateTime(time time.Time) {
	return
}

//func (e *EventEntity) String(timeStr string) string {
//	layout := time.RFC3339Nano
//
//	// 使用time.Parse将时间字符串解析为time.Time类型
//	t, err := time.Parse(layout, timeStr)
//	if err != nil {
//		fmt.Println(err)
//		return ""
//	}
//
//	// 使用UnixNano将time.Time类型转换为Unix时间戳（纳秒）
//	unixNano := t.UnixNano()
//
//	// 使用strconv.FormatInt将整数转换为字符串
//	unixNanoStr := strconv.FormatInt(unixNano, 10)
//	return unixNanoStr
//
//}

func (e *EventEntity) PrimaryKey() string {
	return e.Scope + ":" + e.Name + ":" + fmt.Sprintf("%v", e.CreateAt)
}

func (e *EventEntity) TableName() string {
	return tableNamePrefix + "event"
}

func (e *EventEntity) ShortTableName() string {
	return "event"
}

func (e *EventEntity) Index() map[string]interface{} {
	index := make(map[string]interface{})
	return index
}
