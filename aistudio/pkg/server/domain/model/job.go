package model

import (
	"encoding/json"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	corev1 "k8s.io/api/core/v1"
)

const (
	SourceCodeGitType     string = "git"
	SourceCodeCloudFSType string = "cloudFS"
	SourceCodeCubeFSType  string = "cubeFS"

	PriorityHigh   string = "HIGH"
	PriorityNormal string = "NORMAL"
	PriorityLow    string = "LOW"

	PriorityHighInt   = 2
	PriorityNormalInt = 1
	PriorityLowInt    = 0
)

type JobEntity struct {
	BaseModel                 `json:",inline" bson:",inline"`
	ID                        primitive.ObjectID         `json:"id" bson:"_id"`
	Name                      string                     `json:"name"`
	DisplayName               string                     `json:"displayName"`
	Description               string                     `json:"description"`
	WorkspaceName             string                     `json:"workspaceName"`
	Region                    string                     `json:"region"`
	Labels                    map[string]string          `json:"labels"`
	QueueName                 string                     `json:"queueName"`
	Priority                  string                     `json:"priority"`
	JobType                   string                     `json:"jobType"`
	Members                   []string                   `json:"members"`
	Creator                   string                     `json:"creator"`
	MaxWaitTime               time.Duration              `json:"maxWaitTime"`
	QueueID                   primitive.ObjectID         `json:"queueID,omitempty"`
	PytorchJobTemplate        *PytorchJobTemplate        `json:"pytorchJobTemplate,omitempty"`
	DeepSpeedJobTemplate      *DeepSpeedJobTemplate      `json:"deepSpeedJobTemplate,omitempty"`
	SimpleTrainingJobTemplate *SimpleTrainingJobTemplate `json:"simpleTrainingJobTemplate,omitempty"`
	Affinity                  *Affinity                  `json:"affinity,omitempty"`
	RestartPolicy             *RestartPolicy             `json:"restartPolicy,omitempty"`
	AlarmShielding            bool                       `json:"alarmShielding"`
	VelaDefaultEnvName        string                     `json:"velaDefaultEnvName,omitempty"`
	VelaProjectName           string                     `json:"velaProjectName,omitempty"`
	JobTemplateName           string                     `json:"jobTemplateName,omitempty"`
	HadoopEnabled             bool                       `json:"hadoopEnabled,"`
	HadoopUsers               []string                   `json:"hadoopUsers,omitempty"`
}

type RestartPolicy struct {
	Enabled       bool  `json:"enabled"`
	MaxRetryCount int32 `json:"maxRetryCount"`
}

type Affinity struct {
	ClusterAffinity ClusterAffinity `json:"clusterAffinity,omitempty"`
}

type ClusterAffinity struct {
	Preferred []string `json:"preferred"`
	Required  []string `json:"required"`
}

type SourceCodeEntity struct {
	Source     SourceCodeSource `json:",inline" bson:",inline"`
	MountPoint string           `json:"mountPoint"`
}

type CloudFsSource struct {
	VolumeName string `json:"volumeName,omitempty"`
	SubPath    string ` json:"subPath,omitempty"`
}
type GitSource struct {
	Url    string `json:"url,omitempty"`
	Branch string `json:"branch,omitempty"`
}

type CubeFsSource struct {
	VolumeName string `json:"volumeName,omitempty"`
}
type SourceCodeSource struct {
	Type          string         `json:"type,omitempty"`
	CloudFsSource *CloudFsSource `json:"cloudfssource,omitempty"`
	GitSource     *GitSource     `json:"gitsource,omitempty"`
	CubeFsSource  *CubeFsSource  `json:"cubefssource,omitempty"`
}

type TaskSpec struct {
	Name                  string              `json:"name"`
	Replicas              int32               `json:"replicas"`
	Command               string              `json:"command"`
	Image                 string              `json:"image"`
	EnvVars               []*EnvVar           `json:"envVars"`
	VolumeSpecs           []*VolumeSpecEntity `json:"volumeSpecs"`
	Specification         *Specification      `json:"specification"`
	NodeSpecificationName string              `json:"nodeSpecificationName,omitempty"`
	Slots                 int32               `json:"slots,omitempty"`
	ConfigSpecs           []*ConfigSpecEntity `json:"configSpecs,omitempty"`
}

func (n *JobEntity) PrimaryKey() string {
	return n.ID.Hex() //name自动生成
}

func (n *JobEntity) TableName() string {
	return tableNamePrefix + "jobs"
}

func (n *JobEntity) ShortTableName() string {
	return "jobs"
}

func (n *JobEntity) Index() map[string]interface{} {
	index := make(map[string]interface{})
	if n.Name != "" {
		index["name"] = n.Name
	}
	if n.WorkspaceName != "" {
		index["workspaceName"] = n.WorkspaceName
	}
	if n.QueueName != "" {
		index["queueName"] = n.QueueName
	}
	if n.Creator != "" {
		index["creator"] = n.Creator
	}
	if n.Members != nil {
		index["members"] = n.Members
	}
	return index
}

type TaskEntity struct {
	BaseModel
	TaskId        string             `json:"taskId"`
	PodName       string             `json:"podName"`
	PodNamespace  string             `json:"podNamespace"`
	JobID         primitive.ObjectID `json:"jobID"`
	WorkspaceName string             `json:"workspaceName"`
	QueueName     string             `json:"queueName"`
	JobName       string             `json:"jobName"`
	Cluster       string             `json:"cluster"`
	Region        string             `json:"region"`
	Raw           *TaskRaw           `json:"raw,omitempty"`
	Zone          string             `json:"zone"`
	IsDeleted     bool               `json:"isDeleted"` //是否被删除了, 该字段用于标记是否被删除了，默认元数据不会被删除
	TaskEnv       string             `json:"taskEnv,omitempty"`
}

func (t *TaskEntity) PrimaryKey() string {
	return t.TaskId
}

func (t *TaskEntity) TableName() string {
	return tableNamePrefix + "job_tasks"
}

func (t *TaskEntity) ShortTableName() string {
	return "job_tasks"
}

func (t *TaskEntity) Index() map[string]interface{} {
	indexes := make(map[string]interface{})
	if t.WorkspaceName != "" {
		indexes["workspaceName"] = t.WorkspaceName
	}
	if t.QueueName != "" {
		indexes["queueName"] = t.QueueName
	}
	if t.Region != "" {
		indexes["region"] = t.Region
	}
	if t.JobName != "" {
		indexes["jobName"] = t.JobName
	}
	if t.Zone != "" {
		indexes["zone"] = t.Zone
	}
	if t.Cluster != "" {
		indexes["cluster"] = t.Cluster
	}
	if t.JobID != primitive.NilObjectID {
		indexes["jobID"] = t.JobID
	}
	if t.TaskId != "" {
		indexes["taskId"] = t.TaskId
	}
	return indexes
}

type PytorchJobTemplate struct {
	TensorboardEnabled bool                `json:"tensorboard_enabled"`
	VolumeSpecs        []*VolumeSpecEntity `json:"volume_specs"`
	Master             *TaskSpec           `json:"master"`
	Worker             *TaskSpec           `json:"worker"`
	SourceCode         *SourceCodeEntity   `json:"sourcecode"`
	Datasets           []string            `json:"datasets"`
	EnvVars            []*EnvVar           `json:"envVars"`
	Command            string              `json:"command,omitempty"`
	Image              string              `json:"image,omitempty"`
	ConfigSpecs        []*ConfigSpecEntity `json:"configSpecs,omitempty"`
}

type DeepSpeedJobTemplate struct {
	TensorboardEnabled bool                `json:"tensorboard_enabled"`
	VolumeSpecs        []*VolumeSpecEntity `json:"volume_specs"`
	Master             *TaskSpec           `json:"master"`
	Worker             *TaskSpec           `json:"worker"`
	SourceCode         *SourceCodeEntity   `json:"sourcecode"`
	Datasets           []string            `json:"datasets"`
	EnvVars            []*EnvVar           `json:"env_vars"`
	Command            string              `json:"command,omitempty"`
	Image              string              `json:"image,omitempty"`
	ConfigSpecs        []*ConfigSpecEntity `json:"configSpecs,omitempty"`
}

type SimpleTrainingJobTemplate struct {
	TensorboardEnabled    bool                `json:"tensorboard_enabled"`
	SourceCode            *SourceCodeEntity   `json:"sourcecode"`
	Command               string              `json:"command"`
	Image                 string              `json:"image"`
	EnvVars               []*EnvVar           `json:"envVars"`
	VolumeSpecs           []*VolumeSpecEntity `json:"volumeSpecs"`
	Specification         *Specification      `json:"specification"`
	NodeSpecificationName string              `json:"nodeSpecificationName"`
	ConfigSpecs           []*ConfigSpecEntity `json:"configSpecs,omitempty"`
}

func (s *SimpleTrainingJobTemplate) ToTaskSpec() *TaskSpec {
	return &TaskSpec{
		EnvVars:               s.EnvVars,
		Command:               s.Command,
		Image:                 s.Image,
		Replicas:              1,
		Specification:         s.Specification,
		NodeSpecificationName: s.NodeSpecificationName,
	}
}

type TaskRaw struct {
	Pod *corev1.Pod `json:"pod,omitempty"`
}

func (r TaskRaw) MarshalBSON() ([]byte, error) {
	jsonData, err := json.Marshal(&r.Pod)
	if err != nil {
		return nil, err
	}
	return bson.Marshal(bson.M{"pod": string(jsonData)})
}

func (r *TaskRaw) UnmarshalBSON(data []byte) error {
	var doc bson.M
	if err := bson.Unmarshal(data, &doc); err != nil {
		return err
	}
	if value, ok := doc["pod"].(string); ok {
		var err error
		pod := &corev1.Pod{}
		if err = json.Unmarshal([]byte(value), pod); err != nil {
			return err
		}
		r.Pod = pod
	}
	return nil
}

type JobTemplateEntity struct {
	BaseModel         `json:",inline" bson:",inline"`
	ID                primitive.ObjectID `json:"id" bson:"_id"`
	Name              string             `json:"name"`
	DisplayName       string             `json:"displayName"`
	Description       string             `json:"description"`
	WorkspaceName     string             `json:"workspaceName"`
	Region            string             `json:"region"`
	Labels            map[string]string  `json:"labels"`
	Job               *JobEntity         `json:"job"`
	Triggers          []string           `json:"triggers"`
	ExecutionStrategy string             `json:"executionStrategy,omitempty"`
	Creator           string             `json:"creator"`
	Managers          []string           `json:"managers"`
	Members           []string           `json:"members"`
}

func (n *JobTemplateEntity) PrimaryKey() string {
	return n.ID.Hex()
}

func (n *JobTemplateEntity) TableName() string {
	return tableNamePrefix + "job_templates"
}

func (n *JobTemplateEntity) ShortTableName() string {
	return "job_templates"
}

func (n *JobTemplateEntity) Index() map[string]interface{} {
	index := make(map[string]interface{})
	if n.Name != "" {
		index["name"] = n.Name
	}
	if n.WorkspaceName != "" {
		index["workspaceName"] = n.WorkspaceName
	}
	if n.Region != "" {
		index["region"] = n.Region
	}
	return index
}

type TriggerEntity struct {
	BaseModel           `json:",inline" bson:",inline"`
	ID                  primitive.ObjectID  `json:"id" bson:"_id"`
	TriggerName         string              `json:"triggerName"`
	TriggerType         string              `json:"triggerType"`
	TimerTriggerOptions TimerTriggerOptions `json:"timerTriggerOptions"`
	NextActionTimes     []string            `json:"nextActionTimes"`
	NumActions          int32               `json:"numActions"`
	TriggerState        string              `json:"triggerState"`
	JobTemplateName     string              `json:"jobTemplateName"`
	Message             string              `json:"message"`
}

type TimerTriggerOptions struct {
	TimerTriggerType string `json:"timerTriggerType"`
	Interval         int32  `json:"interval"`
	CronExpr         string `json:"cronExpr"`
	Cycle            Cycle  `json:"calendars"`
	PeriodOfValidity string `json:"periodOfValidity"`
	MaxCount         int    `json:"maxCount"`
	SkipTime         Cycle  `json:"skipTime"`
}

// Cycle 代表周期性触发器的时间设置， 每 xx 月、xx 日、xx 周、xx 时、xx 分触发
type Cycle struct {
	Month      []int32 `json:"month"`
	DayOfMonth []int32 `json:"day"`
	DayOfWeek  []int32 `json:"week"`
	Hour       []int32 `json:"hour"`
	Minute     []int32 `json:"minute"`
}

func (t *TriggerEntity) PrimaryKey() string {
	return t.ID.Hex()
}

func (t *TriggerEntity) TableName() string {
	return tableNamePrefix + "job_triggers"
}

func (t *TriggerEntity) ShortTableName() string {
	return "job_triggers"
}

func (t *TriggerEntity) Index() map[string]interface{} {
	index := make(map[string]interface{})
	if t.TriggerName != "" {
		index["triggerName"] = t.TriggerName
	}
	if t.TriggerType != "" {
		index["triggerType"] = t.TriggerType
	}
	if t.TriggerState != "" {
		index["triggerState"] = t.TriggerState
	}
	return index
}
