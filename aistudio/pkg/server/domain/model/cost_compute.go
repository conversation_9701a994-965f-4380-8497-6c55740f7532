package model

import (
	"time"
)

const (
	ResourceHourlyCostTableName  = "resource_hourly_cost"
	ResourceDailyCostTableName   = "resource_daily_cost"
	ResourceMonthlyCostTableName = "resource_monthly_cost"
	ResourceUsageTableName       = "resource_usage"

	NodeResourceType                   = "node"
	NodeExclusiveResourceSpecification = "独占"
	NodeResourceIdExplanation          = "节点IP"
	CPUNode                            = "CPU"
	GPUNode                            = "GPU"

	DevMachineResourceType          = "devMachine"
	DevMachineResourceIdExplanation = "开发机ID"
	GpuExclusiveDevDevMachine       = "GpuExclusive"
	GpuShareDevDevMachine           = "GpuShare"

	JobResourceType          = "job"
	JobResourceIdExplanation = "任务ID"

	ApplicationResourceType          = "application"
	ApplicationResourceIdExplanation = "应用部署组ID"

	ComputeResourceUsageDuration     = 1
	ComputeResourceUsageDurationUnit = "h"
	HourUsageUnit                    = "小时"
)

type ResourceUsageData struct {
	Time                  time.Time `gorm:"column:time"`
	ResourceId            string    `gorm:"column:resource_id"`
	ResourceIdExplanation string    `gorm:"column:resource_id_explanation"`
	ResourceName          string    `gorm:"column:resource_name"`
	Region                string    `gorm:"column:region"`
	Workspace             string    `gorm:"column:workspace"`
	Usage                 float32   `gorm:"column:usage"`
	UsageUnit             string    `gorm:"column:usage_unit"`
	UsageDuration         int       `gorm:"column:usage_duration"`
	DurationUnit          string    `gorm:"column:duration_unit"`
	ResourceSpecification string    `gorm:"column:resource_specification"`
	ResourceType          string    `gorm:"column:resource_type"`
	Admin                 string    `gorm:"column:admin"`
	ExtensionField1       string    `gorm:"column:extension_field1"`
	ExtensionField2       string    `gorm:"column:extension_field2"`
	ExtensionField3       string    `gorm:"column:extension_field3"`
}

type ResourceUsageDataTotal struct {
	Time                  time.Time `gorm:"column:time"`
	ResourceId            string    `gorm:"column:resource_id"`
	ResourceIdExplanation string    `gorm:"column:resource_id_explanation"`
	ResourceName          string    `gorm:"column:resource_name"`
	Region                string    `gorm:"column:region"`
	Workspace             string    `gorm:"column:workspace"`
	Usage                 float32   `gorm:"column:usage"`
	UsageUnit             string    `gorm:"column:usage_unit"`
	UsageDuration         int       `gorm:"column:usage_duration"`
	DurationUnit          string    `gorm:"column:duration_unit"`
	ResourceSpecification string    `gorm:"column:resource_specification"`
	ResourceType          string    `gorm:"column:resource_type"`
	Admin                 string    `gorm:"column:admin"`
	ExtensionField1       string    `gorm:"column:extension_field1"`
	ExtensionField2       string    `gorm:"column:extension_field2"`
	ExtensionField3       string    `gorm:"column:extension_field3"`
	TotalUsage            float32   `gorm:"column:total_usage"`
}

type CostData struct {
	Time                       time.Time `gorm:"column:time"`
	ProductName                string    `gorm:"column:product_name"`
	ProductNameCn              string    `gorm:"column:product_name_cn"`
	ServiceSpecificationName   string    `gorm:"column:service_specification_name"`
	ServiceSpecificationNameCn string    `gorm:"column:service_specification_name_cn"`
	ResourceId                 string    `gorm:"column:resource_id"`
	ResourceIdExplanation      string    `gorm:"column:resource_id_explanation"`
	Region                     string    `gorm:"column:region"`
	Workspace                  string    `gorm:"column:workspace"`
	Usage                      float32   `gorm:"column:usage"`
	UsageUnit                  string    `gorm:"column:usage_unit"`
	Admin                      string    `gorm:"column:admin"`
	Cost                       float64   `gorm:"column:cost"`
	ResourceSpecification      string    `gorm:"column:resource_specification"`
	ResourceType               string    `gorm:"column:resource_type"`
}

type CostDataTotal struct {
	Time                       time.Time `gorm:"column:time"`
	ProductName                string    `gorm:"column:product_name"`
	ProductNameCn              string    `gorm:"column:product_name_cn"`
	ServiceSpecificationName   string    `gorm:"column:service_specification_name"`
	ServiceSpecificationNameCn string    `gorm:"column:service_specification_name_cn"`
	ResourceId                 string    `gorm:"column:resource_id"`
	ResourceIdExplanation      string    `gorm:"column:resource_id_explanation"`
	Region                     string    `gorm:"column:region"`
	Workspace                  string    `gorm:"column:workspace"`
	Usage                      float32   `gorm:"column:usage"`
	UsageUnit                  string    `gorm:"column:usage_unit"`
	Admin                      string    `gorm:"column:admin"`
	Cost                       float64   `gorm:"column:cost"`
	ResourceSpecification      string    `gorm:"column:resource_specification"`
	ResourceType               string    `gorm:"column:resource_type"`
	TotalUsage                 float32   `gorm:"column:total_usage"`
	TotalCost                  float64   `gorm:"column:total_cost"`
}

type TotalCost struct {
	TotalCost float64 `gorm:"column:total_cost"`
}
