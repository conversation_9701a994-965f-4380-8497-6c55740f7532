package model

import (
	"git.lianjia.com/cloudnative/kic/kic-platform/apis/common"
)

type Port struct {
	PortName string `json:"portName"`
	Port     int32  `json:"port"`
	Protocol string `json:"protocol"`
}

func ConvertPortEntityToProto(p *Port) *common.Port {
	return &common.Port{
		PortName: p.PortName,
		Port:     p.Port,
		Protocol: p.Protocol}
}

func ConvertPortEntitiesToProto(ports []*Port) []*common.Port {
	var result []*common.Port
	for _, port := range ports {
		result = append(result, ConvertPortEntityToProto(port))
	}
	return result
}
