package model

import (
	applicationv1 "git.lianjia.com/cloudnative/kic/kic-platform/apis/aistudio/application/v1"
	"git.lianjia.com/cloudnative/kic/kic-platform/apis/common"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type ApplicationEntity struct {
	BaseModel       `bson:",inline"`
	ID              primitive.ObjectID   `json:"id" bson:"_id"`
	ApplicationName string               `json:"applicationName"`
	WorkspaceName   string               `json:"workspaceName"`
	Description     string               `json:"description,omitempty"`
	DisplayName     string               `json:"displayName"`
	VelaProjectName string               `json:"velaProjectName"`
	Labels          map[string]string    `json:"labels"`
	Managers        []string             `json:"managers,omitempty"`
	Members         []string             `json:"members,omitempty"`
	Creator         string               `json:"creator,omitempty"`
	AppType         string               `json:"appType"`
	IsDeleted       bool                 `json:"isDeleted"`
	Message         string               `json:"message,omitempty"`
	ServiceConfig   *ServiceConfigEntity `json:"serviceConfig"`
}

type ServiceConfigEntity struct {
	Enabled  bool     `json:"enabled"`
	Ports    []*Port  `json:"port"`
	Gateways []string `json:"gateways"`
}

func (s *ServiceConfigEntity) ToProto() *applicationv1.ServiceConfig {
	if s == nil {
		return nil
	}
	return &applicationv1.ServiceConfig{
		Enabled:  s.Enabled,
		Gateways: s.Gateways,
		Ports:    ConvertPortEntitiesToProto(s.Ports),
	}
}

func (s *ServiceConfigEntity) PortsToProto() []*common.Port {
	if s == nil {
		return nil
	}
	return ConvertPortEntitiesToProto(s.Ports)
}

func (a *ApplicationEntity) PrimaryKey() string {
	return a.ID.Hex()
}

func (a *ApplicationEntity) TableName() string {
	return tableNamePrefix + "application"
}

func (a *ApplicationEntity) ShortTableName() string {
	return "application"
}

func (a *ApplicationEntity) Index() map[string]interface{} {
	index := make(map[string]interface{})
	if a.WorkspaceName != "" {
		index["workspaceName"] = a.WorkspaceName
	}
	if a.ApplicationName != "" {
		index["applicationName"] = a.ApplicationName
	}
	return index
}
