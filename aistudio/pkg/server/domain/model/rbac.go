package model

type Effect string

const (
	Deny  Effect = "Deny"
	Allow Effect = "Allow"
)

func (e Effect) String() string {
	return string(e)
}

func init() {

}

type RoleEntity struct {
	BaseModel   `json:",inline" bson:",inline"`
	ID          string   `json:"id"`
	Name        string   `json:"name"`
	Scope       string   `json:"scope"`
	Description string   `json:"description"`
	Permissions []string `json:"permissions"`
	Alias       string   `json:"alias"`
}

func (s *RoleEntity) PrimaryKey() string {
	return s.ID
}

func (s *RoleEntity) TableName() string {
	return tableNamePrefix + "role"
}

func (s *RoleEntity) ShortTableName() string {
	return "role"
}

func (s *RoleEntity) Index() map[string]interface{} {
	index := make(map[string]interface{})
	if s.Name != "" {
		index["name"] = s.Name
	}
	if s.Scope != "" {
		index["scope"] = s.Scope
	}
	return index
}

type PermissionEntity struct {
	BaseModel
	ID          string   `json:"id"`
	Name        string   `json:"name"`
	Description string   `json:"description"`
	Resources   []string `json:"resources"`
	Actions     []string `json:"actions"`
	Effect      string   `json:"effect"`
	Scope       string   `json:"scope"` //workspace:xxx or platform
	//Type        string   `json:"type"`  //权限类型 role or user
}

func (p *PermissionEntity) PrimaryKey() string {
	return p.ID
}

func (p *PermissionEntity) TableName() string {
	return tableNamePrefix + "permission"
}

func (p *PermissionEntity) ShortTableName() string {
	return "permission"
}

func (p *PermissionEntity) Index() map[string]interface{} {
	index := make(map[string]interface{})
	if p.Scope != "" {
		index["scope"] = p.Scope
	}
	return index
}

type RoleBindingEntity struct {
	BaseModel
	ID          string   `json:"id"`
	Account     string   `json:"account"`
	Scope       string   `json:"scope"` //生效范围，workspace:${workspaceName} or platform
	Roles       []string `json:"roles"`
	Description string   `json:"description"`
}

func (r *RoleBindingEntity) PrimaryKey() string {
	return r.ID
}

func (r *RoleBindingEntity) TableName() string {
	return tableNamePrefix + "role-binding"
}

func (r *RoleBindingEntity) ShortTableName() string {
	return "role-binding"
}

func (r *RoleBindingEntity) Index() map[string]interface{} {
	index := make(map[string]interface{})
	if r.Account != "" {
		index["account"] = r.Account
	}
	if r.Scope != "" {
		index["scope"] = r.Scope
	}
	return index
}

type UserBindingEntity struct {
	BaseModel
	ID          string   `json:"id"`
	Account     string   `json:"account"`
	Scope       string   `json:"scope"` //生效范围，workspace:${workspaceName} or platform
	Permissions []string `json:"permissions"`
	Description string   `json:"description"`
}

func (u *UserBindingEntity) PrimaryKey() string {
	return u.ID
}

func (u *UserBindingEntity) TableName() string {
	return tableNamePrefix + "userbinding"
}

func (u *UserBindingEntity) ShortTableName() string {
	return "userbinding"
}

func (u *UserBindingEntity) Index() map[string]interface{} {
	index := make(map[string]interface{})
	if u.Account != "" {
		index["account"] = u.Account
	}
	if u.Scope != "" {
		index["scope"] = u.Scope
	}
	return index
}
