package model

import "go.mongodb.org/mongo-driver/bson/primitive"

type RobotUserEntity struct {
	BaseModel
	Account          string   `json:"account"`
	Password         string   `json:"password"`
	DisplayName      string   `json:"displayName"`
	Description      string   `json:"description"`
	AccessToken      string   `json:"accessToken"`
	WeChatRobotToken string   `json:"wechatToken"`
	Creator          string   `json:"creator"`
	Updater          string   `json:"updater"`
	Workspaces       []string `json:"workspaces"`
	OriginWorkspace  string   `json:"originWorkspace"`
	IsDefault        string   `json:"isDefault"`
}

func (r *RobotUserEntity) PrimaryKey() string {
	return r.Account
}

func (r *RobotUserEntity) TableName() string {
	return tableNamePrefix + "robot_user"
}

func (r *RobotUserEntity) ShortTableName() string {
	return "robot_user"
}

func (r *RobotUserEntity) Index() map[string]interface{} {
	index := make(map[string]interface{})
	if r.Account != "" {
		index["account"] = r.Account
	}
	if r.AccessToken != "" {
		index["accessToken"] = r.AccessToken
	}
	if r.IsDefault != "" {
		index["isDefault"] = r.IsDefault
	}
	return index
}

type UserAccessTokenEntity struct {
	BaseModel
	ID          primitive.ObjectID `json:"id"`
	Account     string             `json:"account"`
	AccessToken string             `json:"accessToken"`
	Deadline    string             `json:"deadline"`
}

func (a *UserAccessTokenEntity) PrimaryKey() string {
	return a.ID.Hex()
}

func (a *UserAccessTokenEntity) TableName() string {
	return tableNamePrefix + "user_access_token"
}

func (a *UserAccessTokenEntity) ShortTableName() string {
	return "user_access_token"
}

func (a *UserAccessTokenEntity) Index() map[string]interface{} {
	index := make(map[string]interface{})
	return index
}

type SSHKeyEntity struct {
	BaseModel     `json:",inline" bson:",inline"`
	ID            primitive.ObjectID `json:"id" bson:"_id"`
	UserName      string             `json:"username"`
	Region        string             `json:"region"`
	Name          string             `json:"name"`
	Token         string             `json:"token"`
	EncodePrivate string             `json:"encodePrivate"`
}

func (i *SSHKeyEntity) PrimaryKey() string {
	return i.ID.Hex()
}

func (i *SSHKeyEntity) TableName() string {
	return tableNamePrefix + "ssh_key"
}

func (i *SSHKeyEntity) ShortTableName() string {
	return "ssh_key"
}

func (i *SSHKeyEntity) Index() map[string]interface{} {
	index := make(map[string]interface{})
	if i.UserName != "" {
		index["username"] = i.UserName
	}
	if i.Region != "" {
		index["region"] = i.Region
	}
	if i.Name != "" {
		index["name"] = i.Name
	}
	if i.Token != "" {
		index["token"] = i.Token
	}
	return index
}
