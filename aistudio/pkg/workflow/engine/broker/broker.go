package broker

import (
	"context"
	"fmt"
	"git.lianjia.com/cloudnative/kic/kic-platform/aistudio/pkg/workflow/engine/executor"
	"git.lianjia.com/cloudnative/kic/kic-platform/aistudio/pkg/workflow/engine/workflow"
	"git.lianjia.com/cloudnative/kic/kic-platform/aistudio/pkg/workflow/eventbus"
	"git.lianjia.com/cloudnative/kic/kic-platform/aistudio/pkg/workflow/storage"
	"git.lianjia.com/cloudnative/kic/kic-platform/aistudio/pkg/workflow/util"
	workflowv1 "git.lianjia.com/cloudnative/kic/kic-platform/apis/aistudio/workflow/v1"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/property"
	kratoslog "github.com/go-kratos/kratos/v2/log"
	"golang.org/x/time/rate"
	"io"
	"k8s.io/client-go/util/workqueue"
	"sync"
	"time"
)

var _ workflowv1.WorkflowBrokerServiceServer = (*Broker)(nil)

func NewBroker(ctx context.Context, properties property.EnvironmentProperty, logger *kratoslog.Helper) (*Broker, error) {
	rateMaxLimit := properties.MustGetFloat64("broker.rate.maxLimit")
	rateBurst := properties.MustGetInt("broker.rate.burst")
	queueLimit := workqueue.NewMaxOfRateLimiter(
		workqueue.NewItemExponentialFailureRateLimiter(5*time.Millisecond, 1000*time.Second),
		&workqueue.BucketRateLimiter{Limiter: rate.NewLimiter(rate.Limit(rateMaxLimit), rateBurst)},
	)
	queue := workqueue.NewNamedRateLimitingQueue(queueLimit, "KIC-BROKER-EXECUTOR")
	dataDir := properties.MustGetString("broker.data.dir")
	workflowStore, err := storage.NewLocalStore(ctx, storage.LocalOptions{
		DBPath: fmt.Sprintf("%s/workflow.db", dataDir),
		Logger: logger,
	})
	if err != nil {
		return nil, err
	}
	taskExecutor := executor.NewAsyncExecutor(ctx, properties, logger)
	b := &Broker{
		RateBurst:         rateBurst,
		RateMaxLimit:      rateMaxLimit,
		workflows:         &sync.Map{},
		executorWorkQueue: queue,
		Logger:            logger,
		store:             workflowStore,
		DataDir:           dataDir,
		ctx:               ctx,
		taskExecutor:      taskExecutor,
	}
	b.startWorkflowCleanTask(ctx)
	b.globalConsumers = append(b.globalConsumers, &eventbus.ConsumeFuncs{
		ConsumeTaskEventFunc: func(taskEvent *workflowv1.TaskEvent) {
			var workflowId = taskEvent.WorkflowId
			if v, ok := b.workflows.Load(workflowId); ok {
				wf := v.(*workflow.DAG).GetWorkflow()
				err = b.store.SaveWorkflow(ctx, wf)
				if err != nil {
					b.Logger.Errorf("get and modify executor status error: %v", err)
				}
			}

		},
		ConsumeWorkflowEventFunc: func(workflowEvent *workflowv1.WorkflowEvent) {
			var workflowId = workflowEvent.WorkflowId
			if v, ok := b.workflows.Load(workflowId); ok {
				wf := v.(*workflow.DAG).GetWorkflow()
				err = b.store.SaveWorkflow(ctx, wf)
				if err != nil {
					b.Logger.Errorf("get and modify executor status error: %v", err)
				}
			}
		},
	})
	b.globalConsumers = append(b.globalConsumers, &eventbus.ConsumeFuncs{
		ConsumeTaskEventFunc: func(taskEvent *workflowv1.TaskEvent) {
			wf, ok := b.workflows.Load(taskEvent.GetWorkflowId())
			wfDto := wf.(*workflowv1.Workflow)
			if ok {
				if len(b.eventStreams) > 0 {
					for _, es := range b.eventStreams {
						err := es.Send(&workflowv1.WorkflowRunEvent{
							WorkflowId:        taskEvent.WorkflowId,
							WorkflowNamespace: wfDto.GetNamespace(),
							WorkflowName:      wfDto.GetName(),
							PlayLoad: &workflowv1.WorkflowRunEvent_TaskEvent{
								TaskEvent: taskEvent,
							},
						})
						if err != nil {
							b.Logger.Errorf("send workflowRunEvent.taskEvent error: %v", err)
						}
					}
				}
			}
		},
		ConsumeWorkflowEventFunc: func(workflowEvent *workflowv1.WorkflowEvent) {
			wf, ok := b.workflows.Load(workflowEvent.GetWorkflowId())
			wfDto := wf.(*workflowv1.Workflow)
			if ok {
				if len(b.eventStreams) > 0 {
					for _, es := range b.eventStreams {
						err := es.Send(&workflowv1.WorkflowRunEvent{
							WorkflowId:        workflowEvent.WorkflowId,
							WorkflowNamespace: wfDto.GetNamespace(),
							WorkflowName:      wfDto.GetName(),
							PlayLoad: &workflowv1.WorkflowRunEvent_WorkflowEvent{
								WorkflowEvent: workflowEvent,
							},
						})
						if err != nil {
							b.Logger.Errorf("send workflowRunEvent.taskEvent error: %v", err)
						}
					}
				}
			}
		},
	})
	return b, nil
}

type Broker struct {
	*workflowv1.UnimplementedWorkflowBrokerServiceServer
	RateMaxLimit      float64
	RateBurst         int
	DataDir           string
	workflows         *sync.Map
	executorWorkQueue workqueue.RateLimitingInterface
	store             storage.WorkflowStore
	Logger            *kratoslog.Helper `inject:"logger"`
	lock              sync.Mutex
	ctx               context.Context
	globalConsumers   []eventbus.Consumer
	taskExecutor      executor.Executor
	eventStreams      []workflowv1.WorkflowBrokerService_ExchangeServer
}

func (w *Broker) SubmitWorkflow(ctx context.Context, wf *workflowv1.Workflow) (*workflowv1.Workflow, error) {
	wf, err := util.InitWorkflow(wf)
	if err != nil {
		return nil, err
	}
	err = w.store.SaveWorkflow(ctx, wf)
	if err != nil {
		return nil, err
	}
	eventBus := eventbus.NewDirectEventBus(ctx)
	for _, gc := range w.globalConsumers {
		eventBus.AddLocalConsumer(gc)
	}
	workflowRun, err := workflow.NewWorkflow(wf, eventBus, w.taskExecutor, w.Logger)
	if err != nil {
		return nil, err
	}
	w.workflows.Store(fmt.Sprintf("%s:%s", wf.Namespace, wf.Name), workflowRun)
	return wf, nil
}

func (w *Broker) Exchange(server workflowv1.WorkflowBrokerService_ExchangeServer) error {
	w.eventStreams = append(w.eventStreams, server)
	for {
		select {
		case <-w.ctx.Done():
			w.Logger.Infof("broker context done")
			return nil
		default:
			req, err := server.Recv()
			if err != nil {
				if err == io.EOF {
					return nil
				}
				w.Logger.Errorf("receive workflowRun request error: %v", err)
				return err
			}
			switch req.PayLoad.(type) {
			case *workflowv1.ExchangeRequest_WorkflowStartRequest:
				wf, ok := w.workflows.Load(req.GetWorkflowStartRequest().WorkflowId)
				if ok {
					dag := wf.(*workflow.DAG)
					go func() {
						err = dag.Start(context.Background())
						if err != nil {
							//一般情况,不会出现workflow启动失败的情况
							w.Logger.Errorf("dag start failed:%v", err)
						}
					}()
				}
			case *workflowv1.ExchangeRequest_WorkflowCancelRequest:
				w.WorkflowCancel(req.GetWorkflowCancelRequest())
			case *workflowv1.ExchangeRequest_WorkflowResumeRequest:
				w.WorkflowResume(req.GetWorkflowResumeRequest())
			case *workflowv1.ExchangeRequest_WorkflowSuspendRequest:
				w.WorkflowSuspend(req.GetWorkflowSuspendRequest())
			}
		}

	}
}

func (w *Broker) GetTaskStatus(ctx context.Context, request *workflowv1.GetTaskStatusRequest) (*workflowv1.TaskStatus, error) {
	//TODO implement me
	panic("implement me")
}

func (w *Broker) startWorkflowCleanTask(ctx context.Context) {
	go func() {
		ticker := time.NewTicker(5 * time.Minute)
		for {
			select {
			case <-ctx.Done():
				w.Logger.Infof("Broker WorkflowCleanTask exit")
				ticker.Stop()
				return
			default:
				err := w.store.RangeAndCleanWorkflow(ctx, func(workflow *workflowv1.Workflow) bool {
					if workflow.Status.State == workflowv1.Workflow_Success && time.Now().Add(3*24*time.Hour).After(workflow.Status.FinishAt.AsTime()) {
						return true
					}
					return false
				})
				if err != nil {
					//todo 发报警
					w.Logger.Errorf("Broker WorkflowCleanTask error: %v", err)
				}
			}
		}
	}()
}

func (w *Broker) GetTasksByTopologicalSort(ctx context.Context, request *workflowv1.GetTasksByTopologicalSortRequest) (*workflowv1.GetTasksByTopologicalSortResponse, error) {
	//TODO implement me
	panic("implement me")
}

func (w *Broker) GetWorkflowDetail(ctx context.Context, request *workflowv1.GetWorkflowDetailRequest) (*workflowv1.Workflow, error) {
	return w.store.GetWorkflowDetail(ctx, request.WorkflowId)
}

func (w *Broker) WorkflowCancel(request *workflowv1.WorkflowCancelRequest) {
	if wf, ok := w.workflows.Load(request.WorkflowId); ok {
		dag := wf.(*workflow.DAG)
		dag.Stop()
		w.workflows.LoadAndDelete(request.WorkflowId)
	}
}

func (w *Broker) WorkflowResume(request *workflowv1.WorkflowResumeRequest) {
	if w, ok := w.workflows.Load(request.WorkflowId); ok {
		w.(*workflow.DAG).Resume()
	}
}

func (w *Broker) WorkflowSuspend(request *workflowv1.WorkflowSuspendRequest) {
	if w, ok := w.workflows.Load(request.WorkflowId); ok {
		w.(*workflow.DAG).Suspend()
	}
}

func (w *Broker) handleError(err error) {
	w.Logger.Errorf("unexpect error:%v", err)
}
