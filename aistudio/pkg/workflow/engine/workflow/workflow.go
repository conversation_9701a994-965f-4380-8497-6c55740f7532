package workflow

import (
	"context"
	"fmt"
	"git.lianjia.com/cloudnative/kic/kic-platform/aistudio/pkg/workflow/engine/executor"
	"git.lianjia.com/cloudnative/kic/kic-platform/aistudio/pkg/workflow/engine/executor/options"
	"git.lianjia.com/cloudnative/kic/kic-platform/aistudio/pkg/workflow/eventbus"
	"git.lianjia.com/cloudnative/kic/kic-platform/aistudio/pkg/workflow/storage"
	workflowv1 "git.lianjia.com/cloudnative/kic/kic-platform/apis/aistudio/workflow/v1"
	kratoslog "github.com/go-kratos/kratos/v2/log"
	"github.com/pkg/errors"
	"github.com/spf13/viper"
	"google.golang.org/protobuf/types/known/durationpb"
	"google.golang.org/protobuf/types/known/timestamppb"
	"sync"
	"time"

	"github.com/Knetic/govaluate"
	"github.com/dominikbraun/graph"
)

// NewWorkflow create a new workflow, 依赖于Eventbus的调度，此处Eventbus的要求是必须支持强制顺序事件
func NewWorkflow(workflow *workflowv1.Workflow, eventBus eventbus.EventBus, executor executor.Executor, logger *kratoslog.Helper) (*DAG, error) {
	taskHash := func(taskName string) string {
		return taskName
	}
	g := graph.New(taskHash, graph.Directed())
	if workflow.Spec.Timeout.AsDuration() == 0 {
		workflow.Spec.Timeout = durationpb.New(60 * time.Second)
	}
	globalContext := workflow.Spec.Arguments
	if globalContext == nil {
		globalContext = make(map[string]string, 4)
	}
	d := &DAG{
		graph:      g,
		globalCtx:  globalContext,
		eventBus:   eventBus,
		createAt:   time.Now(),
		executor:   executor,
		workflow:   workflow,
		timeout:    workflow.Spec.Timeout.AsDuration(),
		workflowId: workflow.Status.Id,
		logger:     logger,
	}
	err := d.From(workflow)
	if err != nil {
		return nil, err
	}
	return d, nil
}

type DAG struct {
	graph           graph.Graph[string, string] //有向图, 都是taskId
	globalCtx       map[string]string
	executor        executor.Executor
	workflowId      string //workflow id 工作流的id
	eventBus        eventbus.EventBus
	inDegree        map[string]map[string]graph.Edge[string] //入度
	outDegree       map[string]map[string]graph.Edge[string] //出度
	workflow        *workflowv1.Workflow                     //工作流的实例
	ctx             context.Context
	cancel          func()
	isSuspend       bool
	outDegreeIsZero []string
	createAt        time.Time
	timeout         time.Duration
	store           storage.WorkflowStore
	logger          *kratoslog.Helper
	lock            sync.Mutex
}

func (d *DAG) AddConsumer(consumer eventbus.Consumer) {
	d.eventBus.AddLocalConsumer(consumer)
}

func (d *DAG) generateParamsContext() map[string]interface{} {
	result := make(map[string]interface{}, 8)
	if len(d.globalCtx) > 0 {
		params := make(map[string]interface{})
		for k, v := range d.globalCtx {
			params[k] = v
		}
		result["context"] = params
	}
	outputs := make(map[string]interface{}, 2)
	for k, t := range d.workflow.Status.Tasks {
		if len(t.Outputs.Items) > 0 {
			outputsParams := make(map[string]interface{})
			for _, item := range t.Outputs.Items {
				outputsParams[item.Key] = item.Value
			}
			outputs[k] = outputsParams
		}
	}
	if len(outputs) > 0 {
		result["outputs"] = outputs
	}
	return result
}

func (d *DAG) generateParamsViper() *viper.Viper {
	result := d.generateParamsContext()
	v := viper.New()
	v.Set("workflow", result)
	return v
}

func (d *DAG) ConsumeTaskEvent(taskEvent *workflowv1.TaskEvent) {
	switch taskEvent.State {
	case workflowv1.TaskStatus_Error, workflowv1.TaskStatus_Failure:
		d.handleEvent(taskEvent)
		d.handleWorkflowFailed(taskEvent)
		return
	case workflowv1.TaskStatus_Success, workflowv1.TaskStatus_Skip:
		d.handleEvent(taskEvent)
		d.triggerNext(taskEvent.TaskId)
		d.handleWorkflowFinished()
		return
	case workflowv1.TaskStatus_Running:
		d.handleEvent(taskEvent)
		return
	default:
		d.logger.Warnf("unknown state:%s", taskEvent.State)
	}
}

func (d *DAG) handleWorkflowFailed(failedTask *workflowv1.TaskEvent) {
	workflowEvent := &workflowv1.WorkflowEvent{
		State:         workflowv1.Workflow_Failure,
		WorkflowId:    d.workflowId,
		GlobalContext: d.globalCtx,
		Timestamp:     timestamppb.Now(),
		Progress:      d.getProgress(),
		Reason:        fmt.Sprintf("executor[%s] execute failed, reason:%s", failedTask.ExecutableName, failedTask.Reason),
	}
	err := d.eventBus.PublishWorkflowEvent(workflowEvent)
	if err != nil {
		d.logger.Errorf("publish workflow event failed:%v", err)
	}
}

func (d *DAG) getProgress() string {
	total := len(d.workflow.Status.Tasks)
	success := 0
	for _, s := range d.workflow.Status.Tasks {
		if isSuccessful(s.State) {
			success++
		}
	}
	return fmt.Sprintf("%d/%d", success, total)
}

func (d *DAG) handleEvent(event *workflowv1.TaskEvent) {
	if len(event.GlobalContext) > 0 {
		d.mergeGlobalContext(event.GlobalContext)
	}
	taskStatus := d.workflow.Status.Tasks[event.TaskId]
	if taskStatus == nil {
		return
	}
	taskStatus.State = event.State
	taskStatus.ModifyAt = event.Timestamp
	taskStatus.Reason = event.Reason
	taskStatus.Message = event.Message
	taskStatus.GlobalContext = event.GlobalContext
	if event.Outputs != nil {
		taskStatus.Outputs = event.Outputs
	} else {
		taskStatus.Outputs = &workflowv1.Outputs{}
	}
	//todo 如果是StepGroup的场景，需要处理
	d.workflow.Status.Tasks[event.TaskId] = taskStatus
}

func (d *DAG) handleWorkflowFinished() {
	for _, tn := range d.outDegreeIsZero {
		taskStatus := d.workflow.Status.Tasks[tn]
		//出度为0的节点，没有全部是完成状态的话，表示
		if !isSuccessful(taskStatus.State) {
			return
		}
	}
	//如果没有退出表示全部完成
	err := d.eventBus.PublishWorkflowEvent(&workflowv1.WorkflowEvent{
		State:         workflowv1.Workflow_Success,
		WorkflowId:    d.workflowId,
		GlobalContext: d.globalCtx,
		Timestamp:     timestamppb.Now(),
		Progress:      d.getProgress(),
		Reason:        "workflow execute success",
		Message:       "workflow execute success",
	})
	if err != nil {
		d.logger.Errorf("publish workflow event failed:%v", err)
	}
}

func isSuccessful(state workflowv1.TaskStatus_TaskState) bool {
	return state == workflowv1.TaskStatus_Success || state == workflowv1.TaskStatus_Skip
}

func (d *DAG) mergeGlobalContext(params map[string]string) {
	if len(params) > 0 {
		for k, v := range params {
			d.globalCtx[k] = v
		}
	}
}

func (d *DAG) triggerNext(taskId string) {
	//如果任务暂定了，每5秒钟，重新判断一下是否还处于暂停状态
	if d.isSuspend {
		time.AfterFunc(5*time.Second, func() {
			d.triggerNext(taskId)
		})
		return
	}
	outDegree := d.outDegree[taskId]
	//获取所有出度的节点
	for _, v := range outDegree {
		in := d.inDegree[v.Target]
		for _, inV := range in {
			inStatus := d.workflow.Status.Tasks[inV.Source]
			//如果有任何一个出度节点的入度节点为非执行完成状态, 则不执行该节点
			if inStatus.State != workflowv1.TaskStatus_Success {
				break
			}
		}
		//如果没有退出就执行该任务
		d.executeDAGTask(v.Target)
	}
}

func (d *DAG) ConsumeWorkflowEvent(workflowEvent *workflowv1.WorkflowEvent) {
	switch workflowEvent.State {
	case workflowv1.Workflow_Error, workflowv1.Workflow_Failure, workflowv1.Workflow_Success:
		d.logger.Infof("workflow event received, state: %s, workflowNamespace :%s workflowName :%s", workflowEvent.State, d.workflow.Namespace, d.workflow.Name)
		d.cancel()
		return
	default:
		d.logger.Infof("workflow event:%v", workflowEvent)
	}
}

func (d *DAG) From(workflow *workflowv1.Workflow) error {
	var tasks []*workflowv1.TaskStatus
	for _, dagT := range workflow.Status.Tasks {
		tasks = append(tasks, dagT)
	}
	err := d.addTasks(tasks)
	if err != nil {
		return err
	}
	d.globalCtx = workflow.Spec.Arguments
	inDegree, err := d.graph.PredecessorMap()
	if err != nil {
		return err
	}
	d.inDegree = inDegree
	outDegree, err := d.graph.AdjacencyMap()
	if err != nil {
		return err
	}
	d.outDegree = outDegree
	for _, t := range d.workflow.Status.Tasks {
		taskOutDegree := d.outDegree[t.Id]
		if len(taskOutDegree) == 0 {
			d.outDegreeIsZero = append(d.outDegreeIsZero, t.Id)
		}
	}
	return nil
}

func (d *DAG) addTasks(tasks []*workflowv1.TaskStatus) error {

	for _, task := range tasks {
		err := d.graph.AddVertex(task.Id)
		if err != nil {
			return err
		}
	}
	for _, task := range tasks {
		if len(task.Dependencies) > 0 {
			for _, dep := range task.Dependencies {
				err := d.graph.AddEdge(dep, task.Id)
				if err != nil {
					return err
				}
			}
		}
	}
	return nil
}

func (d *DAG) GetWorkflow() *workflowv1.Workflow {
	return d.workflow
}

func (d *DAG) Start(ctx context.Context) error {
	rootCtx, cancel := context.WithTimeout(ctx, d.timeout)
	d.ctx = rootCtx
	d.cancel = cancel
	d.eventBus.AddLocalConsumer(d)
	err := d.eventBus.PublishWorkflowEvent(&workflowv1.WorkflowEvent{
		State:         workflowv1.Workflow_Running,
		WorkflowId:    d.workflowId,
		GlobalContext: d.globalCtx,
		Timestamp:     timestamppb.Now(),
	})
	//如果没有退出表示全部完成
	d.executeDAGTask(d.workflow.Status.EntryPointTaskId)
	if err != nil {
		d.logger.Errorf("publish workflow event failed:%v", err)
		return err
	}
	go func() {
		for {
			select {
			case <-d.ctx.Done():
				if t, ok := d.ctx.Deadline(); ok {
					if time.Now().After(t) {
						d.logger.Errorf("workflow[%s:%s] execute timeout", d.workflow.Namespace, d.workflow.Name)
						err := d.eventBus.PublishWorkflowEvent(&workflowv1.WorkflowEvent{
							State:         workflowv1.Workflow_Failure,
							WorkflowId:    d.workflowId,
							GlobalContext: d.globalCtx,
							Timestamp:     timestamppb.Now(),
						})
						if err != nil {
							d.logger.Errorf("publish workflow event failed:%v", err)
						}
					}
				}
				d.logger.Infof("workflow [%s:%s] stopped .", d.workflow.Namespace, d.workflow.Name)
				return
			}
		}
	}()
	<-d.ctx.Done()
	d.logger.Infof("workflow [%s:%s] execute finished", d.workflow.Namespace, d.workflow.Name)
	return nil
}

func (d *DAG) Stop() {
	d.logger.Infof("workflow [%s:%s] quitting", d.workflow.Namespace, d.workflow.Name)
	d.cancel()
}

func (d *DAG) Suspend() {
	d.isSuspend = true
}

func (d *DAG) Resume() {
	d.isSuspend = false
}

func (d *DAG) submitOne(ctx context.Context, dagTask *workflowv1.TaskStatus) error {
	var inputs = &workflowv1.Inputs{}
	for _, input := range dagTask.Inputs.Items {
		v, err := GetTemplateText(d.generateParamsViper(), input.Value)
		if err != nil {
			return err
		}
		inputs.Items = append(inputs.Items, &workflowv1.Input{
			Key:   input.Key,
			Value: v,
		})
	}
	req := &workflowv1.Executable{
		TaskId:         dagTask.Id,
		GlobalContext:  d.globalCtx,
		Inputs:         inputs,
		ExecutableName: dagTask.Spec.ExecutableName,
		TaskName:       dagTask.Spec.Name,
		WorkerName:     dagTask.Spec.Worker,
	}
	stream := executor.NewExecutorStream(req, d.eventBus)
	d.executor.Submit(ctx, req, stream, &options.SubmitOptions{
		Timeout:       dagTask.Spec.Timeout.AsDuration(),
		RetryStrategy: dagTask.Spec.RetryStrategy,
	})
	return nil
}

func (d *DAG) executeDAGTask(taskId string) {
	//hash计算方式，输入id，返回id
	taskId, err := d.graph.Vertex(taskId)
	if err != nil {
		dagTask := d.workflow.Status.Tasks[taskId]
		d.handleError(dagTask, err)
		return
	}
	//如果taskId存在的时候才会，逻辑上任务不可能不存在
	if dagTask, ok := d.workflow.Status.Tasks[taskId]; ok {
		isRun := true
		if dagTask.Spec.Skip {
			isRun = false
		} else if dagTask.Spec.When != "" {
			isRun, err = d.conditionWhen(dagTask.Spec.When)
			if err != nil {
				d.logger.Errorf("handle when expression failed ", err)
				d.handleError(dagTask, err)
				return
			}
		}
		//如果可执行的话提交执行任务
		if isRun {
			err := d.submitOne(d.ctx, dagTask)
			if err != nil {
				d.logger.Errorf("submit executor failed", err)
				d.handleError(dagTask, err)
				return
			}
		} else {
			taskEvent := &workflowv1.TaskEvent{
				State:          workflowv1.TaskStatus_Skip,
				TaskId:         taskId,
				Inputs:         dagTask.Spec.Inputs,
				GlobalContext:  d.globalCtx,
				ExecutableName: dagTask.Spec.ExecutableName,
				Message:        "",
				Reason:         "Skip",
				Timestamp:      timestamppb.Now(),
			}
			err := d.eventBus.PublishTaskEvent(taskEvent)
			if err != nil {
				d.logger.Errorf("publish executor event failed:%v", err)
			}
		}
	}
}

func (d *DAG) handleError(dagTask *workflowv1.TaskStatus, err error) {
	taskEvent := &workflowv1.TaskEvent{
		TaskId:         dagTask.Id,
		ExecutableName: dagTask.Spec.ExecutableName,
		State:          workflowv1.TaskStatus_Error,
		GlobalContext:  d.globalCtx,
		Message:        "",
		Timestamp:      timestamppb.Now(),
		Inputs:         dagTask.Spec.Inputs,
		Reason:         err.Error(),
		Progress:       "1/1",
	}
	err = d.eventBus.PublishTaskEvent(taskEvent)
	if err != nil {
		d.logger.Errorf("handle executor event failed:%v", err)
	}
}

func (d *DAG) conditionWhen(whenExpression string) (bool, error) {
	v := d.generateParamsViper()
	whenContent, err := GetTemplateText(v, whenExpression)
	if err != nil {
		return false, err
	}
	expression, err := govaluate.NewEvaluableExpression(whenContent)
	if err != nil {
		return false, err
	}
	result, err := expression.Eval(&VipMapParameters{v: v})
	if err != nil {
		d.logger.Errorf("error:%v", err)
		return false, err
	}
	return result.(bool), nil
}

type VipMapParameters struct {
	v *viper.Viper
}

func (v *VipMapParameters) Get(name string) (interface{}, error) {
	value := v.v.Get(name)
	if value == nil {
		errorMessage := "No parameter '" + name + "' found."
		return nil, errors.New(errorMessage)
	}
	return value, nil
}
