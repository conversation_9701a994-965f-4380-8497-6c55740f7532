package options

import (
	workflowv1 "git.lianjia.com/cloudnative/kic/kic-platform/apis/aistudio/workflow/v1"
	"time"
)

// SubmitOptions 提交的选项
type SubmitOptions struct {
	ExecuteTime   time.Time                 `json:"executeTime,omitempty"`   //执行事件
	Timeout       time.Duration             `json:"timeout,omitempty"`       //超时时间
	RetryStrategy *workflowv1.RetryStrategy `json:"retryStrategy,omitempty"` //重试策略
}
