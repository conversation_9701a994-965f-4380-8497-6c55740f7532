package job

import (
	"context"

	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/util"
	"github.com/spf13/cobra"
)

const (
	cmdName               = "job"
	cmdShort              = "任务管理"
	listJobCmdName        = "list"
	submitJobCmdName      = "submit"
	createTemplateCmdName = "create"
)

// NewJobCmdGroup 创建任务命令组
func NewJobCmdGroup(ctx context.Context, ioStream util.IOStreams) *cobra.Command {
	cmdGroup := &cobra.Command{
		Use:   cmdName,
		Short: cmdShort,
	}
	cmdGroup.AddCommand(newListJobCmd(ctx, ioStream))
	// cmdGroup.AddCommand(newCreateTemplateCmd(ctx, ioStream))
	// cmdGroup.AddCommand(newSubmitJobCmd(ctx, ioStream))
	return cmdGroup
}
