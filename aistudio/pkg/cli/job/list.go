package job

import (
	"context"
	"encoding/json"
	"fmt"

	"git.lianjia.com/cloudnative/kic/kic-platform/aistudio/pkg/cli/base"
	v1 "git.lianjia.com/cloudnative/kic/kic-platform/apis/aistudio/job/v1"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/util"
	"github.com/go-kratos/kratos/v2/middleware"
	"github.com/jedib0t/go-pretty/v6/table"
	"github.com/spf13/cobra"
	"gopkg.in/yaml.v2"
)

type listJobOptions struct {
	Req      v1.ListJobsOptions
	Out      string
	EndPoint string
	Cmd      *cobra.Command
}

func (opt *listJobOptions) AddFlags() {
	opt.Cmd.Flags().StringVarP(&opt.Req.Creator, "creator", "c", "", "创建者")
	opt.Cmd.Flags().StringVarP(&opt.Req.Workspace<PERSON>ame, "workspace", "w", "", "工作空间")
	opt.Cmd.Flags().StringVarP(&opt.Req.QueueName, "queue", "q", "", "队列名称")
	opt.Cmd.Flags().StringVarP(&opt.Req.JobType, "type", "t", "", "任务类型")
	opt.Cmd.Flags().StringVarP(&opt.Req.Name, "name", "n", "", "pod名称")
	opt.Cmd.Flags().StringVarP(&opt.Req.DisplayName, "display", "d", "", "任务名称")
	opt.Cmd.Flags().StringVar(&opt.Req.Region, "region", "region_bj", "地域")
	opt.Cmd.Flags().BoolVar(&opt.Req.StatusEnabled, "status", true, "是否启用")
	opt.Cmd.Flags().StringVarP(&opt.Req.Member, "member", "m", "", "包含成员")
	opt.Cmd.Flags().Int32Var(&opt.Req.Page, "page", 1, "页码,页码不能小于1")
	opt.Cmd.Flags().Int32Var(&opt.Req.PageSize, "size", 20, "每页数量,每页数量必须在1-50之间")
	opt.Cmd.Flags().StringVar(&opt.Req.State, "state", "", "阶段")
	opt.Cmd.Flags().StringVarP(&opt.Out, "out", "o", "table", "输出样式,支持table、yaml、json格式,yaml展示更多字段")
	opt.Cmd.Flags().StringVarP(&opt.EndPoint, "server", "s", base.Host, "AIStudio服务的域名,默认为http://aistudio.kcs.ke.com")
	opt.Cmd.MarkFlagRequired("workspace")
	opt.Cmd.RegisterFlagCompletionFunc("out", func(cmd *cobra.Command, args []string, toComplete string) ([]string, cobra.ShellCompDirective) {
		return []string{"table", "yaml", "json"}, cobra.ShellCompDirectiveDefault
	})
}

func (opt *listJobOptions) Validate() error {
	if opt.Req.WorkspaceName == "" {
		return fmt.Errorf("工作空间不能为空")
	}
	if opt.Req.Page < 1 {
		return fmt.Errorf("页码不能小于1")
	}
	if opt.Req.PageSize < 1 || opt.Req.PageSize > 50 {
		return fmt.Errorf("每页数量必须在1-50之间")
	}
	return nil
}

// newListJobCmd 创建获取任务列表命令
func newListJobCmd(ctx context.Context, ioStream util.IOStreams) *cobra.Command {
	opt := &listJobOptions{}
	opt.Cmd = &cobra.Command{
		Use:     listJobCmdName,
		Short:   "获取任务列表",
		Example: fmt.Sprintf("%s %s %s --workspace=xxx", "aictl", cmdName, listJobCmdName),
		Run: func(cmd *cobra.Command, args []string) {
			if err := opt.Validate(); err != nil {
				ioStream.Errorf("参数校验失败: %v\n", err)
				return
			}
			apiClient, err := base.GetClient(ctx, opt.EndPoint, ioStream, []middleware.Middleware{
				base.ReplacePathMiddleware("/apis/v1/workspace/{workspaceName}/jobs",
					map[string]string{
						"workspaceName": opt.Req.WorkspaceName,
					}),
			})
			if err != nil {
				ioStream.Errorf("创建API客户端失败: %v\n", err)
				return
			}
			resp, err := apiClient.JobClient().ListJobs(ctx, &opt.Req)
			if err != nil {
				ioStream.Errorf("获取任务列表失败: %v\n", err)
				return
			}
			switch opt.Out {
			case "table":
				t := table.NewWriter()
				t.AppendHeader(table.Row{"任务名称", "Pod", "任务状态", "任务类型", "队列名称", "副本数", "创建时间"})
				for _, v := range resp.Jobs {
					t.AppendRow([]interface{}{v.DisplayName, v.Name, v.JobStatus.State, v.JobType, v.QueueName, v.TotalReplicas, v.CreateTime})
				}
				ioStream.Infof(t.Render())
			case "yaml":
				y, _ := yaml.Marshal(resp)
				ioStream.Info(string(y))
			case "json":
				j, _ := json.Marshal(resp)
				ioStream.Info(string(j))
			default:
				ioStream.Errorf("输出样式不支持: %v\n", opt.Out)
			}
		},
	}
	opt.AddFlags()
	return opt.Cmd
}
