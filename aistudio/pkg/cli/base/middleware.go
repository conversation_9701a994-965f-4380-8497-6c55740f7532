package base

import (
	"context"
	"fmt"
	"strings"

	"github.com/go-kratos/kratos/v2/middleware"
	"github.com/go-kratos/kratos/v2/transport"
	"github.com/go-kratos/kratos/v2/transport/http"
)

// ReplacePathMiddleware 替换请求路径中的参数
func ReplacePathMiddleware(path string, params map[string]string) middleware.Middleware {
	return func(handler middleware.Handler) middleware.Handler {
		return func(ctx context.Context, req interface{}) (interface{}, error) {
			if ts, ok := transport.FromClientContext(ctx); ok {
				if ht, ok := ts.(http.Transporter); ok {
					for k, v := range params {
						path = strings.ReplaceAll(path, fmt.Sprintf("{%s}", k), v)
					}
					ht.Request().URL.Path = path
				}
				return handler(ctx, req)
			}
			return handler(ctx, req)
		}
	}
}

// PathParams 用于存储路径参数信息
// FullPath 表示完整的路径模板，包含占位符
// Params 是一个map，存储路径参数名和对应的值
// 例如：
//
//	PathParams{
//	    FullPath: "/apis/v1/model-hub/{workspaceName}/{modelName}/warmupjob/start",
//	    Params: map[string]string{
//	        "workspaceName": "my-workspace",
//	        "modelName":     "my-model",
//	    },
//	}
//
// 这个结构体主要用于IncompletePathMiddleware中，用于动态替换路径中的占位符
type PathParams struct {
	FullPath string            //完整路径
	Params   map[string]string //路径参数
}

// IncompletePathMiddleware 路径中间件，用于处理请求路径
// IncompletePathMiddleware 用于处理不完整的请求路径，根据传入的路径和路径参数映射表动态替换路径中的占位符
// 参数说明：
//   - path: 当前请求的路径
//   - pathMap: 路径映射表，key为请求路径，value为PathParams结构体，包含完整路径模板和参数映射
//
// 使用场景：
//
//	当请求路径中包含动态参数时，可以通过该中间件将路径中的占位符替换为实际值
//
// 例如：
//
//	pathMap = map[string]PathParams{
//	    "/apis/v1/model-hub///warmupjob/start": {
//	        FullPath: "/apis/v1/model-hub/{workspaceName}/{modelName}/warmupjob/start",
//	        Params: map[string]string{
//	            "workspaceName": "my-workspace",
//	            "modelName":     "my-model",
//	        },
//	    },
//	}
//
// 该中间件会将请求路径中的{workspaceName}和{modelName}替换为实际值
func IncompletePathMiddleware(pathMap map[string]PathParams) middleware.Middleware {
	return func(handler middleware.Handler) middleware.Handler {
		return func(ctx context.Context, req interface{}) (interface{}, error) {
			if ts, ok := transport.FromClientContext(ctx); ok {
				if ht, ok := ts.(http.Transporter); ok {
					if pathParams, ok := pathMap[ht.Request().URL.Path]; ok {
						for k, v := range pathParams.Params {
							pathParams.FullPath = strings.ReplaceAll(pathParams.FullPath, fmt.Sprintf("{%s}", k), v)
						}
						ht.Request().URL.Path = pathParams.FullPath
					}
				}
				return handler(ctx, req)
			}
			return handler(ctx, req)
		}
	}
}
