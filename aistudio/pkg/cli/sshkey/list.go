package sshkey

import (
	"context"
	"encoding/json"
	"fmt"

	"git.lianjia.com/cloudnative/kic/kic-platform/aistudio/pkg/cli/base"
	v1 "git.lianjia.com/cloudnative/kic/kic-platform/apis/usercenter/v1"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/util"
	"github.com/go-kratos/kratos/v2/middleware"
	"github.com/jedib0t/go-pretty/v6/table"
	"github.com/spf13/cobra"
	"gopkg.in/yaml.v2"
)

type getSSHKeyOptions struct {
	Req      v1.GetSSHKeysForUserRequest
	Out      string
	EndPoint string
	Cmd      *cobra.Command
}

func (opt *getSSHKeyOptions) AddFlags() {
	opt.Cmd.Flags().Int32Var(&opt.Req.Page, "page", 1, "页码")
	opt.Cmd.Flags().Int32Var(&opt.Req.Per<PERSON>age, "size", 20, "每页数量,每页数量必须在1-50之间")
	opt.Cmd.Flags().StringVarP(&opt.Req.Region, "region", "r", "region_bj", "地域,当前仅支持region_bj")
	opt.Cmd.Flags().StringVarP(&opt.Out, "out", "o", "table", "输出样式,支持table、yaml、json格式,yaml展示更多字段")
	opt.Cmd.Flags().StringVarP(&opt.EndPoint, "server", "s", base.Host, "AIStudio服务的域名,默认为http://aistudio.kcs.ke.com")
	opt.Cmd.RegisterFlagCompletionFunc("out", func(cmd *cobra.Command, args []string, toComplete string) ([]string, cobra.ShellCompDirective) {
		return []string{"table", "yaml", "json"}, cobra.ShellCompDirectiveDefault
	})
}

func (opt *getSSHKeyOptions) Validate() error {
	if opt.Req.Page < 1 {
		return fmt.Errorf("页码不能小于1")
	}
	if opt.Req.PerPage < 1 || opt.Req.PerPage > 50 {
		return fmt.Errorf("每页数量必须在1-50之间")
	}
	if opt.Req.Region != "region_bj" {
		return fmt.Errorf("地域不支持,当前仅支持region_bj")
	}
	return nil
}

// newListSSHKeyCmd 获取ssh-key列表
func newListSSHKeyCmd(ctx context.Context, ioStream util.IOStreams) *cobra.Command {
	opt := &getSSHKeyOptions{}
	opt.Cmd = &cobra.Command{
		Use:     listSSHKeyCmdName,
		Short:   "获取ssh-key列表",
		Example: fmt.Sprintf("%s %s %s", "aictl", cmdName, listSSHKeyCmdName),
		Run: func(cmd *cobra.Command, args []string) {
			if err := opt.Validate(); err != nil {
				ioStream.Errorf("参数校验失败: %v\n", err)
				return
			}
			//获取当前用户
			apiClient, err := base.GetClient(ctx, opt.EndPoint, ioStream, nil)
			if err != nil {
				ioStream.Errorf("创建API客户端失败: %v\n", err)
				return
			}
			userInfo, err := apiClient.UserCenter().Whoami(ctx, &v1.WhoamiRequest{})
			if err != nil {
				ioStream.Errorf("获取用户信息失败: %v\n", err)
				return
			}
			opt.Req.Account = userInfo.Account
			apiClient, err = base.GetClient(ctx, opt.EndPoint, ioStream, []middleware.Middleware{
				base.ReplacePathMiddleware("/apis/v1/user/{account}/ssh-keys",
					map[string]string{
						"account": opt.Req.Account,
					}),
			})
			if err != nil {
				ioStream.Errorf("创建API客户端失败: %v\n", err)
				return
			}
			resp, err := apiClient.UserCenter().GetSSHKeysForUser(ctx, &opt.Req)
			if err != nil {
				ioStream.Errorf("获取ssh-key列表失败: %v\n", err)
				return
			}
			switch opt.Out {
			case "table":
				t := table.NewWriter()
				t.AppendHeader(table.Row{"ssh-key名称", "ssh-key"})
				for _, v := range resp.Items {
					t.AppendRow([]interface{}{v.Name, v.Content})
				}
				ioStream.Infof(t.Render())
			case "yaml":
				y, _ := yaml.Marshal(resp)
				ioStream.Info(string(y))
			case "json":
				j, _ := json.Marshal(resp)
				ioStream.Info(string(j))
			default:
				ioStream.Errorf("输出样式不支持: %v\n", opt.Out)
			}
		},
	}
	opt.AddFlags()
	return opt.Cmd
}
