package model

import (
	"context"
	"fmt"
	"os"
	"strings"

	"git.lianjia.com/cloudnative/kic/kic-platform/aistudio/pkg/cli/base"
	v1 "git.lianjia.com/cloudnative/kic/kic-platform/apis/aistudio/modelhub/v1"
	platformv1 "git.lianjia.com/cloudnative/kic/kic-platform/apis/aistudio/platform/v1"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/util"
	"github.com/go-kratos/kratos/v2/middleware"
	"github.com/spf13/cobra"
)

type warmupModelOption struct {
	WorkspaceName      string
	EndPoint           string
	Region             string
	ModelWorkspaceName string
	ModelName          string
	WarmupRegions      string
	Cmd                *cobra.Command
}

func (opt *warmupModelOption) AddFlags() {
	opt.Cmd.Flags().StringVarP(&opt.WorkspaceName, "workspace", "w", "", "工作空间")
	opt.Cmd.Flags().StringVarP(&opt.Region, "region", "r", "region_bj", "地域,当前仅支持region_bj")
	opt.Cmd.Flags().StringVar(&opt.ModelWorkspaceName, "modelworkspace", "", "模型工作空间")
	opt.Cmd.Flags().StringVar(&opt.ModelName, "modelname", "", "模型名称")
	opt.Cmd.Flags().StringVar(&opt.WarmupRegions, "warmupregions", "region_bj:bj-cloud-6", "预热地域,多个地域以逗号分隔,例如:region_bj:bj-cloud-6,region_sh:sh-cloud-3")
	opt.Cmd.Flags().StringVarP(&opt.EndPoint, "server", "s", base.Host, "AIStudio服务的域名,默认为http://aistudio.kcs.ke.com")
	opt.Cmd.MarkFlagRequired("workspace")
}

func (opt *warmupModelOption) Validate() error {
	if opt.WorkspaceName == "" {
		return fmt.Errorf("工作空间不能为空")
	}
	if opt.Region != "region_bj" {
		return fmt.Errorf("地域不支持,当前仅支持region_bj")
	}
	if opt.ModelWorkspaceName == "" {
		return fmt.Errorf("模型工作空间不能为空")
	}
	if opt.ModelName == "" {
		return fmt.Errorf("模型名称不能为空")
	}
	if opt.WarmupRegions == "" {
		return fmt.Errorf("预热地域不能为空")
	}
	return nil
}

// newWarmupModelCmd 预热模型
func newWarmupModelCmd(ctx context.Context, ioStream util.IOStreams) *cobra.Command {
	opt := &warmupModelOption{}
	opt.Cmd = &cobra.Command{
		Use:     warmupModelCmdName,
		Short:   "预热模型",
		Example: fmt.Sprintf("%s %s %s --workspace=xxx --modelworkspace=xxx --modelname=xxx --warmupregions=xxx", "aictl", cmdName, warmupModelCmdName),
		Run: func(cmd *cobra.Command, args []string) {
			if err := opt.Validate(); err != nil {
				ioStream.Errorf("参数校验失败: %v\n", err)
				os.Exit(1) // 返回错误码 1
			}
			//创建模型
			opt.ModelName = GetModelName(opt.ModelName)
			apiClient, err := base.GetClient(ctx, opt.EndPoint, ioStream, []middleware.Middleware{
				base.IncompletePathMiddleware(map[string]base.PathParams{
					"/apis/v1/model-hub///warmupjob/start": {
						FullPath: "/apis/v1/model-hub/{workspaceName}/{modelName}/warmupjob/start",
						Params: map[string]string{
							"workspaceName": opt.WorkspaceName,
							"modelName":     opt.ModelName,
						},
					},
				}),
			})
			if err != nil {
				ioStream.Errorf("创建API客户端失败: %v\n", err)
				os.Exit(1) // 返回错误码 1
			}
			//验证区域可用
			warmupRegions, err := opt.getWarmupRegions(opt.WarmupRegions)
			if err != nil {
				ioStream.Errorf("预热地域错误: %v\n", err)
				os.Exit(1) // 返回错误码 1
			}

			//获取所有可用预热区域
			availableRegions, err := apiClient.Platform().ListDistributedCacheRegistrations(ctx, &platformv1.ListDistributedCacheRegistrationsRequest{
				Page:     1,
				PageSize: 1000,
			})
			if err != nil {
				ioStream.Errorf("获取所有可用预热区域失败: %v\n", err)
				os.Exit(1) // 返回错误码 1
			}

			//检查是否所有区域可用
			availableRegionsMap := make(map[string]bool)
			for _, region := range availableRegions.DistributedCacheRegistrations {
				availableRegionsMap[fmt.Sprintf("%s-%s", region.CacheRegion, region.CacheZone)] = region.Enabled
			}
			for _, warmupRegion := range warmupRegions {
				if !availableRegionsMap[fmt.Sprintf("%s-%s", warmupRegion.WarmupRegion, warmupRegion.WarmupZone)] {
					ioStream.Errorf("预热地域%s-%s不可用\n", warmupRegion.WarmupRegion, warmupRegion.WarmupZone)
					os.Exit(1) // 返回错误码 1
				}
			}

			resp, err := apiClient.ModelHubClient().StartWarmupJob(ctx, &v1.StartWarmupJobRequest{
				WorkspaceName:      opt.WorkspaceName,
				Region:             opt.Region,
				ModelWorkspaceName: opt.ModelWorkspaceName,
				ModelName:          opt.ModelName,
				WarmupRegions:      warmupRegions,
				Branch:             "master",
			})
			if err != nil {
				ioStream.Errorf("预热模型失败: %v\n", err)
				os.Exit(1) // 返回错误码 1
			}
			ioStream.Infof("预热模型中,请稍后查看预热结果,jobName:%s\n", resp.JobName)
			os.Exit(0)
		},
	}
	opt.AddFlags()
	return opt.Cmd
}

func (opt *warmupModelOption) getWarmupRegions(warmupRegions string) ([]*v1.WarmupRegion, error) {
	warmupRegionsArr := make([]*v1.WarmupRegion, 0)
	regions := strings.Split(warmupRegions, ",")
	for _, region := range regions {
		arr := strings.Split(region, ":")
		if len(arr) != 2 {
			return nil, fmt.Errorf("预热地域格式错误,输入地域:%s,正确格式为:region_bj:bj-cloud-6", warmupRegions)
		}
		warmupRegionsArr = append(warmupRegionsArr, &v1.WarmupRegion{
			WarmupRegion: arr[0],
			WarmupZone:   arr[1],
		})
	}
	return warmupRegionsArr, nil
}
