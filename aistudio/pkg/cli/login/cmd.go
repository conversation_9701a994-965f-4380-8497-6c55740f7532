package login

import (
	"context"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/util"

	"github.com/spf13/cobra"
)

// NewLoginCmd 创建登录命令
func NewLoginCmd(ctx context.Context, ioStream util.IOStreams) *cobra.Command {
	loginOpts := NewLoginOptions()
	cmd := &cobra.Command{
		Use:   "login ",
		Short: "登录aistudio",
		Example: ` 
		Login to AIStudio By UserName and Password, UserName is LDAP account, Password is LDAP password
  		aictl login -u xxxx -p xxxxx -s http://aistudio.kcs.ke.com
		`,
		RunE: func(cmd *cobra.Command, args []string) error {
			if err := loginOpts.Validate(); err != nil {
				ioStream.Errorf("validate login options failed: %v\n", err)
				return err
			}
			if err := Login(ctx, loginOpts, ioStream); err != nil {
				ioStream.Errorf("login failed: %v\n", err)
				return nil
			}
			return nil
		},
	}
	loginOpts.AddFlags(cmd.Flags())
	return cmd
}
