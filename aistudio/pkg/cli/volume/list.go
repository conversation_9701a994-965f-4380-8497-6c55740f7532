package volume

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"

	"git.lianjia.com/cloudnative/kic/kic-platform/aistudio/pkg/cli/base"
	v1 "git.lianjia.com/cloudnative/kic/kic-platform/apis/aistudio/cubefs/v1"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/util"
	"github.com/go-kratos/kratos/v2/middleware"
	"github.com/jedib0t/go-pretty/v6/table"
	"github.com/spf13/cobra"
	"github.com/spf13/pflag"
	"gopkg.in/yaml.v2"
)

type listVolumeCmd struct {
	Req      v1.ListVolumeOptions
	Out      string
	EndPoint string
}

func (c *listVolumeCmd) AddFlags(flags *pflag.FlagSet) {
	flags.StringVarP(&c.Req.WorkspaceName, "workspace", "w", "", "工作空间")
	flags.Int32Var(&c.<PERSON>q.<PERSON>, "page", 1, "页码")
	flags.Int32Var(&c.Req.PageSize, "size", 20, "每页数量,每页数量必须在1-50之间")
	flags.StringVarP(&c.Req.Region, "region", "r", "region_bj", "地域,当前仅支持region_bj")
	flags.StringVarP(&c.Out, "out", "o", "table", "输出样式,支持table、yaml、json格式,yaml展示更多字段")
	flags.StringVarP(&c.EndPoint, "server", "s", base.Host, "AIStudio服务的域名,默认为http://aistudio.kcs.ke.com")
}

func (c *listVolumeCmd) Validate() error {
	if c.Req.WorkspaceName == "" {
		return fmt.Errorf("工作空间不能为空")
	}
	if c.Req.Page < 1 {
		return fmt.Errorf("页码不能小于1")
	}
	if c.Req.PageSize < 1 || c.Req.PageSize > 50 {
		return fmt.Errorf("每页数量必须在1-50之间")
	}
	if c.Req.Region != "region_bj" {
		return fmt.Errorf("地域不支持,当前仅支持region_bj")
	}
	return nil
}

// newListVolumeCmd 获取卷列表
func newListVolumeCmd(ctx context.Context, ioStream util.IOStreams) *cobra.Command {
	c := &listVolumeCmd{}
	cmd := &cobra.Command{
		Use:     listVolumeCmdName,
		Short:   "获取卷列表",
		Example: fmt.Sprintf("%s %s %s", "aictl", cmdName, listVolumeCmdName),
		Run: func(cmd *cobra.Command, args []string) {
			if err := c.Validate(); err != nil {
				ioStream.Errorf("参数校验失败: %v\n", err)
				return
			}
			apiClient, err := base.GetClient(ctx, c.EndPoint, ioStream, []middleware.Middleware{
				base.ReplacePathMiddleware("/apis/v1/workspace/{workspace}/cubefs/volumes",
					map[string]string{
						"workspace": c.Req.WorkspaceName,
					}),
			})
			if err != nil {
				ioStream.Errorf("创建API客户端失败: %v\n", err)
				return
			}
			resp, err := apiClient.CubeFSClient().ListVolumes(ctx, &c.Req)
			if err != nil {
				ioStream.Errorf("获取卷列表失败: %v\n", err)
				return
			}
			switch c.Out {
			case "table":
				t := table.NewWriter()
				t.AppendHeader(table.Row{"卷名称", "卷别名", "状态", "分区", "规格", "容量(g)",
					"绑定已有卷", "利用率", "文件数", "创建人", "创建时间"})
				for _, v := range resp.Items {
					usage := strings.ReplaceAll(v.VolumeStatus.UsagePercent, "%", "")
					t.AppendRow([]interface{}{v.Name, v.DisplayName, v.VolumeStatus.State, v.Zone, v.Specification, fmt.Sprint(v.Capacity) + "G",
						v.Binding.Enabled, usage, v.VolumeStatus.InodeCount, v.Creator, v.Timestamp.CreateTime, v.Description})
				}
				ioStream.Infof(t.Render())
			case "yaml":
				y, _ := yaml.Marshal(resp)
				ioStream.Info(string(y))
			case "json":
				j, _ := json.Marshal(resp)
				ioStream.Info(string(j))
			default:
				ioStream.Errorf("输出样式不支持: %v\n", c.Out)
			}
		},
	}
	c.AddFlags(cmd.Flags())
	cmd.MarkFlagRequired("workspace")
	cmd.RegisterFlagCompletionFunc("out", func(cmd *cobra.Command, args []string, toComplete string) ([]string, cobra.ShellCompDirective) {
		return []string{"table", "yaml", "json"}, cobra.ShellCompDirectiveDefault
	})
	return cmd
}
