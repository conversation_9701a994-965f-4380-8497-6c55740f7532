package scheduler

import (
	"context"
	"fmt"
	"git.lianjia.com/cloudnative/kic/kic-platform/aistudio/pkg/scheduler/core"
	"git.lianjia.com/cloudnative/kic/kic-platform/federation/pkg/queue"
	"git.lianjia.com/cloudnative/kic/kic-platform/federation/pkg/workload"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/infrastructure/datastore"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/property"
	"k8s.io/client-go/tools/record"
	"k8s.io/klog/v2"
	"sync"
)

type ScheduleType string

var ErrorNotFoundMatchedCluster = fmt.Errorf("not found matched cluster")

const (
	ReconcileScheduled ScheduleType = "ReconcileScheduled"
	ScaleScheduled     ScheduleType = "ScaleScheduled"
)

// Scheduler 多集群联邦调度器
type Scheduler struct {
	eventRecorder record.EventRecorder    //事件记录器
	Algorithm     core.SchedulerAlgorithm //调度算法
	ctx           context.Context
	cancel        func()
	priorityQueue *queue.IndexedPriorityQueue //优先级队列
	Store         datastore.DataStore         `inject:"datastore"`
	lock          sync.Mutex
	cond          *sync.Cond
}

func NewScheduler(ctx context.Context, properties property.EnvironmentProperty) (*Scheduler, error) {
	//todo 创建调度器
	return nil, nil
}

func (s *Scheduler) Run() error {
	//todo 调度
	//调度器启动工作
	//启动优先级队列轮训任务
	//启动队列消费任务
	//等待退出信号
	return nil
}

func (s *Scheduler) scheduleNext() bool {
	for {
		select {
		case <-s.ctx.Done():
			return false
		default:
			//Get方法会阻塞查询,直到队列获取到数据为止
			item := s.priorityQueue.Get(context.Background())
			if item == nil {
				return false
			}
			err := s.doSchedule(item.Data.(workload.Component))
			if err != nil {
				s.handleError(err)
			}
			return true
		}
	}
}

func (s *Scheduler) handleError(err error) {
	klog.Errorf("scheduler error: %v", err)
}

func (s *Scheduler) doSchedule(component workload.Component) error {
	//scheduleResult, err := s.Algorithm.Schedule(s.ctx, component, &core.SchedulerAlgorithmOption{})
	//todo 把推理的结果写入到回调中
	return nil
}
