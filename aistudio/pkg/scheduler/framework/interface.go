package framework

import (
	"context"
	"git.lianjia.com/cloudnative/kic/kic-platform/federation/pkg/cluster"
	"git.lianjia.com/cloudnative/kic/kic-platform/federation/pkg/workload"
)

type Framework interface {

	//RunFilterPlugins 运行过滤插件
	RunFilterPlugins(ctx context.Context, component workload.Component, clusterObserver cluster.Observer) *Result
	//RunScorePlugins 运行评分插件
	RunScorePlugins(ctx context.Context, component workload.Component, clusters []cluster.Observer) (PluginToClusterScores, *Result)
}

type Plugin interface {
	Name() string
}

type FilterPlugin interface {
	Plugin
	Filter(ctx context.Context, component workload.Component, clusterObserver cluster.Observer) *Result
}

type Result struct {
	code    Code
	reasons []string
	err     error
}

type Code int

const (
	Success Code = iota
	Unschedulable
	Error
)

var codes = []string{"Success", "Unschedulable", "Error"}

func (c Code) String() string {
	return codes[c]
}

func NewResult(code Code, reasons ...string) *Result {
	s := &Result{
		code: code,
	}
	return s
}

type PluginToResult map[string]*Result

func (p PluginToResult) Merge() *Result {
	if len(p) == 0 {
		return nil
	}
	finalStatus := NewResult(Success)
	var hasUnschedulable bool
	for _, s := range p {
		if s.code == Error {
			finalStatus.err = s.err
		} else if s.code == Unschedulable {
			hasUnschedulable = true
		}
		finalStatus.code = s.code
		finalStatus.reasons = append(finalStatus.reasons, s.reasons...)
	}

	if finalStatus.err != nil {
		finalStatus.code = Error
	} else if hasUnschedulable {
		finalStatus.code = Unschedulable
	}
	return finalStatus
}

func (s *Result) IsSuccess() bool {
	return s == nil || s.code == Success
}

func (s *Result) AsError() error {
	if s.IsSuccess() {
		return nil
	}
	if s.err != nil {
		return s.err
	}
	return nil
}

func (s *Result) Reasons() []string {
	return s.reasons
}

func (s *Result) Code() Code {
	if s == nil {
		return Success
	}
	return s.code
}

func AsResult(err error) *Result {
	return &Result{
		code:    Error,
		reasons: []string{err.Error()},
		err:     err,
	}
}

type ScorePlugin interface {
	Plugin
	Score(ctx context.Context, component workload.Component, clusterObserver cluster.Observer) (int64, *Result)
	ScoreExtensions() ScoreExtensions
}

type ScoreExtensions interface {

	//NormalizeScore 标准化分数
	NormalizeScore(ctx context.Context, scores ClusterScoreList) *Result
}

type ClusterScore struct {
	Cluster cluster.Observer
	Score   int64
}

type ClusterScoreList []ClusterScore

type PluginToClusterScores map[string]ClusterScoreList
