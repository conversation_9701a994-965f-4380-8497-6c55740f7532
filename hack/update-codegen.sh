#!/usr/bin/env bash

# Copyright 2017 The Kubernetes Authors.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

set -x

set -o errexit
set -o nounset
set -o pipefail
SCRIPT_ROOT=$(dirname ${BASH_SOURCE[0]})/..
CODEGEN_PKG=${CODEGEN_PKG:-$(cd ${SCRIPT_ROOT}; ls -d -1 ./pkg/vendor/k8s.io/code-generator 2>/dev/null || echo ../code-generator)}
CODE_REPO=git.lianjia.com/cloudnative/kic/kic-platform
GV="cloudfs.kcs.io:v1alpha1 scheduling.kcs.io:v1alpha1 automl.kcs.io:v1alpha1 multicluster.kcs.io:v1alpha1 managed.kcs.io:v1alpha1"

TEMP_DIR=$(mktemp -d)
cleanup() {
    echo ">> Removing ${TEMP_DIR}"
    rm -rf ${TEMP_DIR}
}
trap "cleanup" EXIT SIGINT
# generate the code with:
# --output-base    because this script should also be able to run inside the vendor dir of
#                  k8s.io/kubernetes. The output-base is needed for the generators to output into the vendor dir
#                  instead of the $GOPATH directly. For normal projects this can be dropped.
bash ${CODEGEN_PKG}/generate-groups.sh "client,lister,informer" \
  ${CODE_REPO}/pkg/generated ${CODE_REPO}/pkg/apis \
  "${GV}" \
  --output-base "${TEMP_DIR}" \
  --v 2 \
  --go-header-file ${SCRIPT_ROOT}/hack/boilerplate.go.txt

# To use your own boilerplate text use:
#   --go-header-file ${SCRIPT_ROOT}/hack/custom-boilerplate.go.txt
# 先删在拷贝，避免拷贝失败
rm -rf "${SCRIPT_ROOT}/pkg/generated"
cp -r "${TEMP_DIR}/git.lianjia.com/cloudnative/kic/kic-platform/pkg/generated" "${SCRIPT_ROOT}/pkg/"
