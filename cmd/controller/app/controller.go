package app

import (
	"context"
	"fmt"
	"git.lianjia.com/cloudnative/kcs/cmd/controller/app/options"
	"git.lianjia.com/cloudnative/kcs/pkg/agent/ctrlmgr"
	schedulerscheme "git.lianjia.com/cloudnative/kcs/pkg/scheduler/plugins/apis/policy/scheme"
	"git.lianjia.com/cloudnative/kcs/pkg/sharedcli/klogflag"
	"git.lianjia.com/cloudnative/kcs/pkg/sharedcli/profileflag"
	"git.lianjia.com/cloudnative/kcs/pkg/version"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/apis/managed.kcs.io/v1alpha1"
	mcsapiv1alpha1 "git.lianjia.com/cloudnative/kic/kic-platform/pkg/apis/multicluster.kcs.io/v1alpha1"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/multicluster"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/multicluster/clients"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/property"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/watcher"
	"github.com/pkg/errors"
	"github.com/spf13/cobra"
	corev1 "k8s.io/api/core/v1"
	apiextensionsv1 "k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1"
	"k8s.io/apimachinery/pkg/runtime"
	utilruntime "k8s.io/apimachinery/pkg/util/runtime"
	clientgoscheme "k8s.io/client-go/kubernetes/scheme"
	"k8s.io/client-go/rest"
	"k8s.io/klog/v2"
	controllerruntime "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/cache"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/healthz"
)

const KcsUserAgent string = "kcs-controller"

var scheme = runtime.NewScheme()

func init() {
	utilruntime.Must(clientgoscheme.AddToScheme(scheme))
	utilruntime.Must(apiextensionsv1.AddToScheme(scheme))
	utilruntime.Must(mcsapiv1alpha1.AddToScheme(scheme))
	utilruntime.Must(v1alpha1.AddToScheme(scheme))
	schedulerscheme.AddToScheme(scheme)
}

func NewControllerManagerCommand(ctx context.Context) *cobra.Command {
	opts := options.NewOptions()
	cmd := &cobra.Command{
		Use:  "the ke container service controller manager",
		Long: "The Kcs Controller Manager is a Kubernetes CRD Controller, which is used for controller cluster status for control the cluster",
		RunE: func(cmd *cobra.Command, args []string) error {
			if err := opts.Validate(); err != nil {
				return errors.Wrap(err, "args validate failed")
			}
			return Run(ctx, opts)
		},
	}
	opts.AddFlags(cmd.Flags())
	klogflag.Add(cmd.Flags())
	return cmd
}

func Run(ctx context.Context, opts *options.Options) error {
	klog.Infof("kcs-controller version: %s, side: controller", version.Get())
	profileflag.ListenAndServe(opts.ProfileOpts)
	config, err := controllerruntime.GetConfig()
	if err != nil {
		panic(err)
	}
	config.UserAgent = KcsUserAgent
	config.QPS, config.Burst = opts.KubeAPIQPS, opts.KubeAPIBurst
	ctrlOpts := controllerruntime.Options{
		Logger:                  klog.Background(),
		Scheme:                  scheme,
		LeaderElection:          opts.EnabledLeaderElection,
		LeaderElectionID:        "kcs-controller",
		LeaderElectionNamespace: opts.Namespace,
		MetricsBindAddress:      opts.MetricsBindAddress,
		HealthProbeBindAddress:  opts.ProbeBindAddress,
		Port:                    opts.Port,
		NewCache: func(config *rest.Config, opts cache.Options) (cache.Cache, error) {
			objectWatcher, err := watcher.NewClusterObjectWatcher(ctx, "local", clients.WithCacheOptions(opts), clients.WithRestConfig(config), clients.WithScheme(scheme))
			if err != nil {
				return nil, err
			}
			objectWatcher.AddWatchObject(watcher.WatchObject{
				Object: &corev1.Pod{},
				FieldIndexers: []watcher.FieldIndexer{
					{
						Field: "status.podIP",
						Func: func(object client.Object) []string {
							var result []string
							pod, ok := object.(*corev1.Pod)
							if ok {
								result = append(result, pod.Status.PodIP)
							}
							return result
						},
					},
				},
			})
			err = objectWatcher.Start(ctx, false)
			if err != nil {
				return nil, err
			}
			return objectWatcher.GetCache(), nil
		},
	}
	if opts.WebhookCertDir != "" {
		ctrlOpts.CertDir = opts.WebhookCertDir
	}

	controllerMgr, err := controllerruntime.NewManager(config, ctrlOpts)
	if err != nil {
		klog.Errorf("unable to set up ctrlmgr manager: %v", err)
		return err
	}
	properties, err := property.NewEnvironmentPropertyFromConfigFile(opts.ConfigFile)
	if err != nil {
		klog.Errorf("not found controller config file: %v", err)
		return err
	}
	multiclusterOpts := &multicluster.Options{
		ClusterName:   properties.MustGetString("kubernetes.multicluster.clusterName"),
		MetaEndpoint:  properties.MustGetString("kubernetes.multicluster.hubEndpoint"),
		MetaToken:     properties.MustGetString("kubernetes.multicluster.hubToken"),
		User:          properties.MustGetString("kubernetes.multicluster.user"),
		Impersonation: properties.MustGetBool("kubernetes.multicluster.impersonation"),
		ClientQps:     properties.GetFloat64Default("kubernetes.multicluster.clientQps", 100),
		ClientBurst:   properties.GetIntDefault("kubernetes.multicluster.clientBurst", 200),
	}
	err = multicluster.InitWithManager(ctx, multiclusterOpts, controllerMgr)
	if err != nil {
		return errors.Wrap(err, "init multiCluster client failed")
	}

	if err := controllerMgr.AddHealthzCheck("healthz", healthz.Ping); err != nil {
		return fmt.Errorf("unable to set up health check: %s", err)
	}
	if err := controllerMgr.AddReadyzCheck("readyz", healthz.Ping); err != nil {
		return fmt.Errorf("unable to set up ready check: %s", err)
	}

	err = ctrlmgr.Initialize(ctx, controllerMgr, properties)
	if err != nil {
		return err
	}
	//controllerMgr.GetWebhookServer().Register("", admission.ValidatingWebhookFor())
	if err = controllerMgr.Start(ctx); err != nil {
		klog.Errorf("controller manager exits unexpectedly: %v", err)
		return err

	}
	return nil
}
