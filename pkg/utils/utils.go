package utils

import (
	"crypto/sha256"
	"encoding/hex"
)

func WithString(s string) *string {
	return &s
}

func WithBool(b bool) *bool {
	return &b
}

func WithInt(i int) *int {
	return &i
}

// ContainsString checks if a given slice of strings contains the provided string.
func ContainsString(slice []string, s string) bool {
	for _, item := range slice {
		if item == s {
			return true
		}
	}
	return false
}

// RemoveString returns a newly created []string that contains all items from slice that
// are not equal to s.
func RemoveString(slice []string, s string) []string {
	newSlice := make([]string, 0)
	for _, item := range slice {
		if item == s {
			continue
		}
		newSlice = append(newSlice, item)
	}
	if len(newSlice) == 0 {
		// Sanitize for unit tests so we don't need to distinguish empty array
		// and nil.
		newSlice = nil
	}
	return newSlice
}

func StringPtr(s string) *string {
	return &s
}

func BooleanPtr(b bool) *bool {
	return &b
}

func Sha256Hash(data []byte) string {
	hasher := sha256.New()
	hasher.Write(data)
	hashValue := hasher.Sum(nil)
	return hex.EncodeToString(hashValue)
}
