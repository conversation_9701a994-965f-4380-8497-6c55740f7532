package checker

import (
	"errors"
	"fmt"
	"strings"

	"github.com/miekg/dns"
)

var ResolverConfigError = errors.New("resolver config error")
var ResolverTypeError = errors.New("not found the resolver type")

type Resolver interface {
	Resolve(domain string) ([]string, error)
}

func CreateResolver(resolverName string, properties map[string]string) (Resolver, error) {
	if strings.EqualFold(resolverName, "dns") {
		dnsHost, ok := properties["host"]
		if !ok {
			return nil, ResolverConfigError
		}
		dnsPort, ok := properties["port"]
		if !ok {
			return nil, ResolverConfigError
		}
		return &DNSResolver{
			DNSServerHost: dnsHost,
			DNSServerPort: dnsPort,
		}, nil
	}
	return nil, ResolverTypeError
}

type DNSResolver struct {
	DNSServerHost string
	DNSServerPort string
}

func (d *DNSResolver) Resolve(domain string) ([]string, error) {
	var ips []string
	c := new(dns.Client)
	m := new(dns.Msg)
	m.SetQuestion(domain, dns.TypeA)
	r, _, err := c.Exchange(m, fmt.Sprintf("%s:%s", d.DNSServerHost, d.DNSServerPort))
	if err != nil {
		return nil, err
	}
	for _, rr := range r.Answer {
		switch rr.Header().Rrtype {
		case dns.TypeA:
			if a, ok := rr.(*dns.A); ok {
				ips = append(ips, a.A.String())
			}
		default:
		}
	}
	return ips, nil
}
