package multicluster

import (
	"context"
	"fmt"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/util"
	"github.com/oam-dev/cluster-gateway/pkg/apis/cluster/v1alpha1"
	"github.com/oam-dev/cluster-gateway/pkg/generated/clientset/versioned"
	kubevelav1alpha1 "github.com/oam-dev/kubevela/apis/core.oam.dev/v1alpha1"
	kubevelav1beta1 "github.com/oam-dev/kubevela/apis/core.oam.dev/v1beta1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	utilruntime "k8s.io/apimachinery/pkg/util/runtime"
	clientgoscheme "k8s.io/client-go/kubernetes/scheme"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/clientcmd"
	"k8s.io/klog"
	metricsv1beta1 "k8s.io/metrics/pkg/apis/metrics/v1beta1"
	"math/rand"
	sdkhttp "net/http"
	"net/url"
	controllerruntime "sigs.k8s.io/controller-runtime"
	ctrlruntimemanager "sigs.k8s.io/controller-runtime/pkg/manager"
	"strings"
	"sync"
)

const (
	MetaCluster              = "meta"
	LocalCluster             = "local"
	ConstantHubClusterServer = "hub-cluster"
)

var mgr Manager

var (
	metaClusterScheme *runtime.Scheme
)

func init() {
	metaClusterScheme = initScheme()
}

func GetMetaClusterScheme() *runtime.Scheme {
	return metaClusterScheme
}

func initScheme() *runtime.Scheme {
	s := runtime.NewScheme()
	utilruntime.Must(clientgoscheme.AddToScheme(s))
	utilruntime.Must(metricsv1beta1.AddToScheme(s))
	utilruntime.Must(kubevelav1beta1.AddToScheme(s))
	utilruntime.Must(kubevelav1alpha1.AddToScheme(s))
	return s
}

type Options struct {
	ClusterName      string                                                  `mapstructure:"clusterName"`   //当前集群的名称
	MetaEndpoint     string                                                  `mapstructure:"metaEndpoint"`  //元集群的API Server地址
	MetaToken        string                                                  `mapstructure:"metaToken"`     //元集群的API Server Token
	User             string                                                  `mapstructure:"user"`          //当前集群的用户名
	Impersonation    bool                                                    `mapstructure:"impersonation"` //是否开启用户伪装, 默认为false
	ClientQps        float64                                                 `mapstructure:"clientQps"`
	ClientBurst      int                                                     `mapstructure:"clientBurst"`
	WrapperTransport func(tripper sdkhttp.RoundTripper) sdkhttp.RoundTripper //包装的transport
}

type ClusterInfo struct {
	Name        string            `json:"clusterName"`
	Region      string            `json:"region"`
	Zone        string            `json:"zone"`
	IDC         string            `json:"idc"`
	Labels      map[string]string `json:"labels,omitempty"`
	Annotations map[string]string `json:"annotations,omitempty"`
}

type Manager interface {
	ListClusterGateways(ctx context.Context) ([]v1alpha1.ClusterGateway, error)
	AddManagedCluster(ctx context.Context, clusterName string, scheme *runtime.Scheme) error
	RemoveManagedCluster(ctx context.Context, clusterName string)
	GetMetaCluster() ClusterInterface
	GetAllManagedCluster() []ClusterInterface
	GetCluster(clusterName string) ClusterInterface
	GetLocalCluster() ClusterInterface
	GetRestConfigForCluster(clusterName string) *rest.Config
	GetClusterName() string
}

func InitWithManager(ctx context.Context, options *Options, manager ctrlruntimemanager.Manager) error {
	localRestConfig := manager.GetConfig()
	var hubRestConfig *rest.Config
	var clusterGatewayClient versioned.Interface
	hubAPIServerEndpoint := options.MetaEndpoint
	//hubAPIServerEndpoints := strings.Split(hubAPIServerEndpoint, ",")
	hubAPIServerToken := options.MetaToken
	hubKubeConfig := util.CreateKubeConfigWithToken(options.User, MetaCluster, hubAPIServerEndpoint, hubAPIServerToken, nil)
	hubRestConfig, err := clientcmd.NewDefaultClientConfig(*hubKubeConfig, nil).ClientConfig()
	if err != nil {
		klog.Errorf("init hub RestConfig failed: %v", err)
		return err
	}
	//hubRestConfig.Wrap(func(rt sdkhttp.RoundTripper) sdkhttp.RoundTripper {
	//	return &resolverTransport{
	//		rt:        rt,
	//		Endpoints: hubAPIServerEndpoints,
	//		Host:      ConstantHubClusterServer,
	//	}
	//})
	clientQPS := options.ClientQps
	clientBurst := options.ClientBurst
	hubRestConfig.QPS = float32(clientQPS)
	hubRestConfig.Burst = clientBurst
	gatewayClient, err := versioned.NewForConfig(hubRestConfig)
	if err != nil {
		return err
	}
	clusterGatewayClient = gatewayClient
	if options.WrapperTransport != nil {
		hubRestConfig.WrapTransport = options.WrapperTransport
	}
	mgr = &managerImpl{
		ctx:                  ctx,
		localRestCfg:         localRestConfig,
		localScheme:          manager.GetScheme(),
		hubRestCfg:           hubRestConfig,
		clusters:             make(map[string]*Cluster, 2),
		clusterGatewayClient: clusterGatewayClient,
		options:              options,
	}
	err = mgr.AddManagedCluster(ctx, MetaCluster, metaClusterScheme)
	if err != nil {
		return err
	}
	localCluster, err := NewClusterWithClient(ctx, ClusterOptions{
		ClusterName: LocalCluster,
		Scheme:      manager.GetScheme(),
		RestConfig:  localRestConfig,
	}, manager.GetClient())
	if err != nil {
		return err
	}
	mgr.(*managerImpl).clusters[LocalCluster] = localCluster
	klog.Infof("manager initialized")
	return nil
}

type resolverTransport struct {
	Host      string
	Endpoints []string
	rt        sdkhttp.RoundTripper
}

func SelectOne(endpoints []string) (*url.URL, error) {
	if len(endpoints) == 0 {
		return nil, nil
	}
	u := strings.TrimSpace(endpoints[rand.Intn(len(endpoints))])
	targetURL, err := url.Parse(u)
	if err != nil {
		return nil, err
	}
	return targetURL, nil
}

func (r *resolverTransport) RoundTrip(req *sdkhttp.Request) (*sdkhttp.Response, error) {
	hostname := req.URL.Hostname()
	if hostname == r.Host {
		endpoint, err := SelectOne(r.Endpoints)
		if err != nil {
			return nil, err
		}
		req.URL.Host = fmt.Sprintf("%s:%s", endpoint.Hostname(), endpoint.Port())
		req.Host = endpoint.Host
		req.Header.Set("Host", endpoint.Host)
		req.URL.Scheme = endpoint.Scheme
	}
	return r.rt.RoundTrip(req)
}

func Init(ctx context.Context, options *Options, localScheme *runtime.Scheme) error {
	localRestConfig, err := controllerruntime.GetConfig()
	if err != nil {
		return err
	}
	var hubRestConfig *rest.Config
	var clusterGatewayClient versioned.Interface
	hubAPIServerEndpoint := options.MetaEndpoint
	//hubAPIServerEndpoints := strings.Split(hubAPIServerEndpoint, ",")
	hubAPIServerToken := options.MetaToken
	hubKubeConfig := util.CreateKubeConfigWithToken(options.User, MetaCluster, hubAPIServerEndpoint, hubAPIServerToken, nil)
	hubRestConfig, err = clientcmd.NewDefaultClientConfig(*hubKubeConfig, nil).ClientConfig()
	if err != nil {
		klog.Errorf("init hub RestConfig failed: %v", err)
		return err
	}
	//hubRestConfig.Wrap(func(rt sdkhttp.RoundTripper) sdkhttp.RoundTripper {
	//	return &resolverTransport{
	//		rt:        rt,
	//		Endpoints: hubAPIServerEndpoints,
	//		Host:      ConstantHubClusterServer,
	//	}
	//})
	clientQPS := options.ClientQps
	clientBurst := options.ClientBurst
	hubRestConfig.QPS = float32(clientQPS)
	hubRestConfig.Burst = clientBurst
	localRestConfig.QPS = float32(clientQPS)
	localRestConfig.Burst = clientBurst
	gatewayClient, err := versioned.NewForConfig(hubRestConfig)
	if err != nil {
		return err
	}
	clusterGatewayClient = gatewayClient
	if options.WrapperTransport != nil {
		hubRestConfig.WrapTransport = options.WrapperTransport
	}
	mgr = &managerImpl{
		ctx:                  ctx,
		localRestCfg:         localRestConfig,
		localScheme:          localScheme,
		hubRestCfg:           hubRestConfig,
		clusters:             make(map[string]*Cluster, 2),
		clusterGatewayClient: clusterGatewayClient,
		options:              options,
	}
	err = mgr.AddManagedCluster(ctx, MetaCluster, metaClusterScheme)
	if err != nil {
		return err
	}
	localCluster, err := NewCluster(ctx, ClusterOptions{
		ClusterName: LocalCluster,
		Scheme:      localScheme,
		RestConfig:  localRestConfig,
	})
	if err != nil {
		return err
	}
	mgr.(*managerImpl).clusters[LocalCluster] = localCluster
	klog.Infof("manager initialized")
	return nil
}

func Instance() Manager {
	if mgr == nil {
		klog.Errorf("not initialized")
	}
	return mgr
}

type managerImpl struct {
	hubRestCfg           *rest.Config
	localRestCfg         *rest.Config
	localScheme          *runtime.Scheme
	clusterGatewayClient versioned.Interface
	lock                 sync.Mutex
	clusters             map[string]*Cluster
	ctx                  context.Context
	options              *Options
}

func (m *managerImpl) GetClusterName() string {
	return m.options.ClusterName
}

func (m *managerImpl) GetAllManagedCluster() []ClusterInterface {
	var clusters []ClusterInterface
	for _, c := range m.clusters {
		if c.ClusterName != LocalCluster && c.ClusterName != MetaCluster {
			clusters = append(clusters, c)
		}
	}
	return clusters
}

func (m *managerImpl) RemoveManagedCluster(ctx context.Context, clusterName string) {
	if v, ok := m.clusters[clusterName]; ok {
		v.Close()
		delete(m.clusters, clusterName)
	}
}

func (m *managerImpl) GetLocalCluster() ClusterInterface {
	m.lock.Lock()
	defer m.lock.Unlock()
	return m.clusters[LocalCluster]
}

func (m *managerImpl) GetCluster(clusterName string) ClusterInterface {
	m.lock.Lock()
	defer m.lock.Unlock()
	if clusterName == m.GetClusterName() {
		return m.GetLocalCluster()
	}
	c, ok := m.clusters[clusterName]
	if ok {
		return c
	}
	return nil
}

func (m *managerImpl) AddManagedCluster(ctx context.Context, clusterName string, scheme *runtime.Scheme) error {
	m.lock.Lock()
	defer m.lock.Unlock()
	if _, ok := m.clusters[clusterName]; ok {
		return nil
	}
	if strings.EqualFold(clusterName, MetaCluster) {
		cls, err := NewCluster(ctx, ClusterOptions{
			ClusterName: MetaCluster,
			Scheme:      scheme,
			RestConfig:  m.hubRestCfg,
		})
		if err != nil {
			return err
		}
		m.clusters[clusterName] = cls
		return nil
	}
	_, err := m.clusterGatewayClient.ClusterV1alpha1().ClusterGateways().Get(ctx, clusterName, metav1.GetOptions{})
	if err != nil {
		if apierrors.IsNotFound(err) {
			return fmt.Errorf("cluster %s not found", clusterName)
		}
		return err
	}
	restCfg := m.GetRestConfigForCluster(clusterName)
	cls, err := NewCluster(ctx, ClusterOptions{
		ClusterName: clusterName,
		Scheme:      scheme,
		RestConfig:  restCfg,
	})
	if err != nil {
		klog.Errorf("create cluster %s failed: %v", clusterName, err)
		return err
	}
	m.clusters[clusterName] = cls
	return nil
}

func (m *managerImpl) ListClusterGateways(ctx context.Context) ([]v1alpha1.ClusterGateway, error) {
	list, err := m.clusterGatewayClient.ClusterV1alpha1().ClusterGateways().List(ctx, metav1.ListOptions{})
	if err != nil {
		return nil, err
	}
	return list.Items, err
}

func (m *managerImpl) GetMetaCluster() ClusterInterface {
	return m.clusters[MetaCluster]
}

func (m *managerImpl) GetRestConfigForCluster(clusterName string) *rest.Config {
	restCfg := &rest.Config{
		Host: m.hubRestCfg.Host,
		WrapTransport: func(rt sdkhttp.RoundTripper) sdkhttp.RoundTripper {
			if m.options.WrapperTransport != nil {
				t := m.options.WrapperTransport(rt)
				return m.clusterGatewayClient.ClusterV1alpha1().ClusterGateways().RoundTripperForClusterWrapperGenerator(clusterName)(t)
			} else {
				return m.clusterGatewayClient.ClusterV1alpha1().ClusterGateways().RoundTripperForClusterWrapperGenerator(clusterName)(rt)
			}
		},
		QPS:   m.hubRestCfg.QPS,
		Burst: m.hubRestCfg.Burst,
	}
	if strings.EqualFold(clusterName, MetaCluster) {
		restCfg.Impersonate = rest.ImpersonationConfig{
			UserName: "kcs-control",
		}
	} else {
		restCfg.Impersonate = rest.ImpersonationConfig{
			UserName: "kcs-discovery",
		}
	}
	return restCfg
}
