package multicluster

import (
	"context"
	"fmt"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/multicluster/clients"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/watcher"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/meta"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"k8s.io/cli-runtime/pkg/resource"
	"k8s.io/client-go/discovery"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/restmapper"
	"k8s.io/klog/v2"
	pkgscheme "k8s.io/kubectl/pkg/scheme"
	"k8s.io/metrics/pkg/client/clientset/versioned"
	"sigs.k8s.io/controller-runtime/pkg/cache"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/cluster"
	"sigs.k8s.io/yaml"
	"sync"
)

var _ ClusterInterface = &Cluster{}

var metadataAccessor = meta.NewAccessor()

type ClusterInterface interface {
	clients.ClientFactory
	cluster.Cluster
	ApplyYaml(yamlData []byte, dryRun bool) (runtime.Object, error)
	GetGVK(obj runtime.Object) (schema.GroupVersionKind, error)
	WatchObjects(ctx context.Context, cacheOptions cache.Options, listeners ...watcher.ObjectChangedListener) (*watcher.ClusterObjectWatcher, error)
	WrapClientWithCache(cacheClient cache.Cache) error
	Close()
}

type ClusterOptions struct {
	ClusterName string
	Scheme      *runtime.Scheme
	RestConfig  *rest.Config
}

type Cluster struct {
	cluster.Cluster
	restCfg         *rest.Config
	ClusterName     string
	schema          *runtime.Scheme
	ctx             context.Context
	lock            sync.Mutex
	clientSet       *kubernetes.Clientset
	restMapper      meta.RESTMapper
	discoveryClient *discovery.DiscoveryClient
	metricsClient   *versioned.Clientset
	directClient    client.Client
	cancel          func()
}

func (c *Cluster) NewCache(options ...clients.CacheBuilderOption) (*clients.Cache, error) {
	cacheBuilder := clients.NewCacheBuilder(c.ClusterName, options...)
	return cacheBuilder.Build()
}

func (c *Cluster) WrapClientWithCache(cacheClient cache.Cache) error {
	directClient, err := client.NewDelegatingClient(client.NewDelegatingClientInput{
		CacheReader: cacheClient,
		Client:      c.directClient,
	})
	if err != nil {
		return err
	}
	c.directClient = directClient
	return nil
}

func (c *Cluster) Direct() client.Client {
	if c.directClient == nil {
		return nil
	}
	return c.directClient
}

func (c *Cluster) Client() client.Client {
	return c.Cluster.GetClient()
}

func (c *Cluster) Discovery() *discovery.DiscoveryClient {
	if c.discoveryClient == nil {
		return nil
	}
	return c.discoveryClient
}

func (c *Cluster) RESTMapper() meta.RESTMapper {
	return c.restMapper
}

func (c *Cluster) ClientSet() *kubernetes.Clientset {
	if c.clientSet == nil {
		return nil
	}
	return c.clientSet
}

func (c *Cluster) Metrics() versioned.Interface {
	if c.metricsClient == nil {
		return nil
	}
	return c.metricsClient
}

func (c *Cluster) GetRestConfig() *rest.Config {
	return c.restCfg
}

func (c *Cluster) GetScheme() *runtime.Scheme {
	return c.schema
}

func (c *Cluster) GetGVK(obj runtime.Object) (schema.GroupVersionKind, error) {
	gvk, _, err := c.schema.ObjectKinds(obj)
	if err != nil {
		return schema.GroupVersionKind{}, err
	}
	return gvk[0], nil
}

func (c *Cluster) WatchObjects(ctx context.Context, cacheOptions cache.Options, listeners ...watcher.ObjectChangedListener) (*watcher.ClusterObjectWatcher, error) {
	objectWatcher, err := watcher.NewClusterObjectWatcher(ctx, c.ClusterName, clients.WithRestConfig(c.GetRestConfig()), clients.WithCacheOptions(cacheOptions))
	if err != nil {
		klog.Errorf("failed to create object watcher: %v", err)
		return nil, err
	}
	err = objectWatcher.Start(ctx, true, listeners...)
	if err != nil {
		return nil, err
	}
	return objectWatcher, nil
}

func NewClusterWithClient(ctx context.Context, options ClusterOptions, directClient client.Client) (*Cluster, error) {
	klog.Infof("NewCluster: %s", options.ClusterName)
	var err error
	c := &Cluster{}
	clusterCtx, clusterCancel := context.WithCancel(ctx)
	c.ClusterName = options.ClusterName
	c.schema = options.Scheme
	c.restCfg = options.RestConfig
	if directClient == nil {
		c.directClient, err = client.New(options.RestConfig, client.Options{Scheme: options.Scheme})
		if err != nil {
			clusterCancel()
			return nil, fmt.Errorf("new k8s client failed: %v", err)
		}
	} else {
		c.directClient = directClient
	}
	c.metricsClient, err = versioned.NewForConfig(options.RestConfig)
	if err != nil {
		clusterCancel()
		return nil, fmt.Errorf("new metrics client failed: %v", err)
	}
	c.clientSet, err = kubernetes.NewForConfig(options.RestConfig)
	if err != nil {
		clusterCancel()
		return nil, fmt.Errorf("new raw k8s clientSet failed: %v", err)
	}
	c.Cluster, err = cluster.New(options.RestConfig, func(opts *cluster.Options) {
		opts.Scheme = options.Scheme
	})
	if err != nil {
		clusterCancel()
		return nil, fmt.Errorf("new cluster client failed: %v", err)
	}
	c.restMapper = c.Cluster.GetRESTMapper()
	c.discoveryClient, err = discovery.NewDiscoveryClientForConfig(options.RestConfig)
	if err != nil {
		clusterCancel()
		return nil, fmt.Errorf("new discovery client failed: %v", err)
	}
	go func() {
		err = c.Cluster.Start(ctx)
		if err != nil {
			klog.Errorf("cluster start failed: %v", err)
		}
	}()
	if ok := c.Cluster.GetCache().WaitForCacheSync(ctx); ok {
		klog.Infof("cluster cache sync success")
	} else {
		clusterCancel()
		return nil, fmt.Errorf("cluster cache sync failed")
	}
	c.ctx = clusterCtx
	c.cancel = clusterCancel
	return c, err
}

func NewCluster(ctx context.Context, options ClusterOptions) (*Cluster, error) {
	return NewClusterWithClient(ctx, options, nil)
}

func (c *Cluster) Close() {
	//todo 用于清理一下其他资源, 后续可以把统计类实现放到这里
	c.cancel()
}

func (c *Cluster) ApplyYaml(yamlData []byte, dryRun bool) (runtime.Object, error) {
	bodyJson, err := yaml.YAMLToJSON(yamlData)
	if err != nil {
		return nil, err
	}
	obj, gvk, err := unstructured.UnstructuredJSONScheme.Decode(bodyJson, nil, nil)
	if err != nil {
		return nil, err
	}
	restClient, err := NewRestClient(c.restCfg, gvk)
	if err != nil {
		return nil, err
	}
	namespace, err := metadataAccessor.Namespace(obj)
	if err != nil {
		return nil, err
	}
	restMapping, err := c.InitRestMapper(gvk)
	if err != nil {
		return nil, err
	}

	result, err := Create(restClient, restMapping, namespace, fmt.Sprintf("%t", dryRun), obj)
	if err != nil {
		return nil, err
	}
	return result, nil
}

func Create(restClient *rest.RESTClient, mapping *meta.RESTMapping, namespace string, dryRun string, obj runtime.Object) (runtime.Object, error) {
	restHelper := resource.NewHelper(restClient, mapping)
	result, err := restHelper.DryRun(dryRun == "true").Create(namespace, true, obj)
	if err != nil {
		return nil, fmt.Errorf("%v", err)
	}
	return result, nil
}

func (c *Cluster) InitRestMapper(gvk *schema.GroupVersionKind) (*meta.RESTMapping, error) {
	groupResources, err := restmapper.GetAPIGroupResources(c.Discovery())
	if err != nil {
		klog.Errorf("restmapper get api group resources fail, %v", err)
		return nil, err
	}
	mapping, err := restmapper.NewDiscoveryRESTMapper(groupResources).RESTMapping(gvk.GroupKind(), gvk.Version)
	if err != nil {
		klog.Errorf("create rest mapping fail, %v", err)
		return nil, err
	}

	return mapping, nil
}

func NewRestClient(config *rest.Config, gvk *schema.GroupVersionKind) (*rest.RESTClient, error) {
	config.APIPath = "/apis"
	if gvk.Group == corev1.GroupName {
		config.APIPath = "/api"
	}
	if config.NegotiatedSerializer == nil {
		// This codec factory ensures the resources are not converted. Therefore, resources
		// will not be round-tripped through internal versions. Defaulting does not happen
		// on the client.
		config.NegotiatedSerializer = pkgscheme.Codecs.WithoutConversion()
	}
	config.ContentConfig = resource.UnstructuredPlusDefaultContentConfig()
	config.GroupVersion = &schema.GroupVersion{Group: gvk.Group, Version: gvk.Version}
	restClient, err := rest.RESTClientFor(config)
	if err != nil {
		klog.Warningf("create rest client fail, %v", err)
		return nil, err
	}
	return restClient, nil
}

func (c *Cluster) GetClusterName() string {
	return c.ClusterName
}
