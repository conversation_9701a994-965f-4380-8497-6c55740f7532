package v1alpha1

import (
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/util"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

// +genclient
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object
// +k8s:openapi-gen=true
// +kubebuilder:resource:scope=Namespaced
// +kubebuilder:resource:shortName=fdcm
// +kubebuilder:object:root=true
// +kubebuilder:subresource:status
// +kubebuilder:printcolumn:name="Age",type="date",JSONPath=".metadata.creationTimestamp"
// +kubebuilder:printcolumn:name="Namespace",type="string",JSONPath=".metadata.namespace"
// +kubebuilder:printcolumn:name="State",type="string",JSONPath=".status.state"

type FederationConfigMap struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty"`
	Spec              FederationConfigMapSpec   `json:"spec,omitempty"`
	Status            FederationConfigMapStatus `json:"status,omitempty"`
}

func (fc *FederationConfigMap) AddFinalizer(finalizerName string) {
	fc.ObjectMeta.Finalizers = append(fc.ObjectMeta.Finalizers, finalizerName)
}

func (fc *FederationConfigMap) RemoveFinalizer(finalizerName string) {
	fc.ObjectMeta.Finalizers = util.Remove(fc.ObjectMeta.Finalizers, finalizerName)
}

func (fc *FederationConfigMap) HasFinalizer(finalizerName string) bool {
	return util.Contains(fc.ObjectMeta.Finalizers, finalizerName)
}

type FederationConfigMapSpec struct {
	WorkspaceName string            `json:"workspaceName,omitempty"`
	Data          map[string]string `json:"data,omitempty"`
	BinaryData    map[string][]byte `json:"binaryData,omitempty"`
}

type FederationState string

const (
	FederationStateSyncing FederationState = "Syncing"
	FederationStateSynced  FederationState = "Synced"
	FederationStateFailed  FederationState = "Failed"
)

type FederationConfigMapStatus struct {
	State              FederationState `json:"state"`
	SyncedClusters     int             `json:"syncedClusters,omitempty"`
	TotalClusters      int             `json:"totalClusters,omitempty"`
	LastTransitionTime metav1.Time     `json:"lastTransitionTime,omitempty"`
	Message            string          `json:"message,omitempty"`
	Reason             string          `json:"reason,omitempty"`
}

// +kubebuilder:object:root=true

type FederationConfigMapList struct {
	metav1.TypeMeta `json:",inline"`
	metav1.ListMeta `json:"metadata,omitempty"`
	Items           []FederationConfigMap `json:"items,omitempty"`
}

func init() {
	SchemeBuilder.Register(&FederationConfigMap{}, &FederationConfigMapList{})
}
