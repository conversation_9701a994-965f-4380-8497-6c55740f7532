package v1alpha1

import (
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/util"
	kruiseapiv1alpha1 "git.lianjia.com/cloudnative/kic/kruise-api/apps/v1alpha1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

type MultiClusterImagePullJobSpec struct {
	Clusters             []string                               `json:"clusters"`
	Images               []string                               `json:"images"`
	ImagePullJobTemplate kruiseapiv1alpha1.ImagePullJobTemplate `json:"imagePullJobTemplate"`
}

type ClusterImagePullJobStatus struct {
	Cluster                string                                   `json:"cluster"`
	ImagePullJobStatuses   []ImagePullJobStatus                     `json:"imagePullJobStatuses,omitempty"`
	ImageListPullJobStatus kruiseapiv1alpha1.ImageListPullJobStatus `json:"imageListPullJobStatus"`
}

type ImagePullJobStatus struct {
	Name               string                               `json:"name"`
	Image              string                               `json:"images"`
	ImagePullJobStatus kruiseapiv1alpha1.ImagePullJobStatus `json:"imagePullJobStatus"`
}

type JobState string

const (
	JobPending   = "Pending"
	JobRunning   = "Running"
	JobSucceeded = "Succeeded"
	JobCompleted = "Completed"
	JobFailed    = "Failed"
	JobUnknown   = "Unknown"
)

type MultiClusterImagePullJobStatus struct {
	State              JobState                    `json:"state"`
	LastTransitionTime metav1.Time                 `json:"lastTransitionTime"`
	Message            string                      `json:"message,omitempty"`
	Reason             string                      `json:"reason,omitempty"`
	Total              int32                       `json:"total"`                 // 全部的节点数量
	Active             int32                       `json:"active"`                // 正在运行的节点数量（全部）
	Succeeded          int32                       `json:"succeeded"`             // 成功的节点数量（全部）
	Failed             int32                       `json:"failed"`                // 失败的节点数量（全部）
	FailedNodes        []string                    `json:"failedNodes,omitempty"` // 失败的节点名称
	JobStatuses        []ClusterImagePullJobStatus `json:"JobStatuses,omitempty"`
}

// MultiClusterImagePullJob +genclient
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object
// +k8s:openapi-gen=true
// +kubebuilder:resource:scope=Namespaced
// +kubebuilder:object:root=true
// +kubebuilder:subresource:status
// +kubebuilder:printcolumn:name="Image",type="string",JSONPath=".spec.image"
// +kubebuilder:printcolumn:name="Clusters",type="string",JSONPath=".spec.clusters"
type MultiClusterImagePullJob struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty"`
	Spec              MultiClusterImagePullJobSpec   `json:"spec,omitempty"`
	Status            MultiClusterImagePullJobStatus `json:"status,omitempty"`
}

// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

type MultiClusterImagePullJobList struct {
	metav1.TypeMeta `json:",inline"`
	metav1.ListMeta `json:"metadata,omitempty"`
	Items           []MultiClusterImagePullJob `json:"items,omitempty"`
}

func (mip *MultiClusterImagePullJob) HasFinalizer(finalizerName string) bool {
	return util.Contains(mip.Finalizers, finalizerName)
}

func (mip *MultiClusterImagePullJob) AddFinalizer(finalizerName string) {
	mip.ObjectMeta.Finalizers = append(mip.ObjectMeta.Finalizers, finalizerName)
}

func (mip *MultiClusterImagePullJob) RemoveFinalizer(finalizerName string) {
	mip.ObjectMeta.Finalizers = util.Remove(mip.ObjectMeta.Finalizers, finalizerName)
}

func init() {
	SchemeBuilder.Register(&MultiClusterImagePullJob{}, &MultiClusterImagePullJobList{})
}
