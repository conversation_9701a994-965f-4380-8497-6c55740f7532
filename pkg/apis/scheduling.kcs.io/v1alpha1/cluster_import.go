package v1alpha1

import (
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/util"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/version"
)

type ClusterState string

const (
	ClusterStatePending  ClusterState = "Pending"
	ClusterStateRunning  ClusterState = "Running"
	ClusterStateCritical ClusterState = "Critical"
)

type ClusterSyncMode string

const (
	Push ClusterState = "Push"
	Pull ClusterState = "Pull"
)

type ClusterImportSpec struct {
	Description string   `json:"description,omitempty"`
	DisplayName string   `json:"displayName,omitempty"`
	Region      string   `json:"region"`
	Zone        string   `json:"zone"`
	Usages      []string `json:"usages,omitempty"`
	IDC         string   `json:"idc,omitempty"`
}

type PrometheusConfiguration struct {
	Enabled            bool   `json:"enabled"`
	PrometheusQueryURL string `json:"prometheusQueryURL,omitempty"`
	ExternalLabels     string `json:"externalLabels,omitempty"` //查询时的标签
}

type ClusterImportStatus struct {
	State             ClusterState    `json:"state"`
	Message           string          `json:"message,omitempty"`
	KubernetesVersion *version.Info   `json:"kubernetesVersion,omitempty"`
	APIEnablements    []APIEnablement `json:"apiEnablements,omitempty"`
	NodeSummary       NodeSummary     `json:"nodeSummary,omitempty"`
	ResourceSummary   ResourceSummary `json:"resourceSummary,omitempty"`
	LatestModifyTime  metav1.Time     `json:"latestModifyTime,omitempty"`
}

type APIEnablement struct {
	GroupVersion string        `json:"groupVersion,omitempty"`
	Resources    []APIResource `json:"apiResources,omitempty"`
}

// APIResource specifies the name and kind names for the resource.
type APIResource struct {
	// Name is the plural name of the resource.
	// +required
	Name string `json:"name"`

	// Kind is the kind for the resource (e.g. 'Deployment' is the kind for resource 'deployments')
	// +required
	Kind string `json:"kind"`
}

type NodeSummary struct {
	TotalNum int32 `json:"totalNum,omitempty"`
	ReadyNum int32 `json:"readyNum,omitempty"`
}

type ResourceSummary struct {
	Allocatable          corev1.ResourceList   `json:"allocatable,omitempty"`
	Allocating           corev1.ResourceList   `json:"allocating,omitempty"`
	Allocated            corev1.ResourceList   `json:"allocated,omitempty"`
	AllocatableModelings []AllocatableModeling `json:"allocatableModelings,omitempty"`
}

type AllocatableModeling struct {
	Grade uint `json:"grade"`
	Count int  `json:"count"`
}

// +genclient
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object
// +k8s:openapi-gen=true
// +kubebuilder:resource:scope=Cluster
// +kubebuilder:object:root=true
// +kubebuilder:printcolumn:name="DisplayName",type="string",JSONPath=".spec.displayName",description="displayName"
// +kubebuilder:printcolumn:name="Region",type="string",JSONPath=".spec.region",description="Region"
// +kubebuilder:printcolumn:name="Zone",type="string",JSONPath=".spec.zone",description="Zone"
// +kubebuilder:printcolumn:name="IDC",type="string",JSONPath=".spec.idc",description="IDC"
// +kubebuilder:printcolumn:name="Usages",type="string",JSONPath=".spec.usages",description="Usages"
// +kubebuilder:printcolumn:name="Age",type="date",JSONPath=".metadata.creationTimestamp"

type ClusterImport struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty"`
	Spec              ClusterImportSpec   `json:"spec,omitempty"`
	Status            ClusterImportStatus `json:"status,omitempty"`
}

func (in *ClusterImport) AddFinalizer(finalizerName string) {
	in.ObjectMeta.Finalizers = append(in.ObjectMeta.Finalizers, finalizerName)
}

func (in *ClusterImport) RemoveFinalizer(finalizerName string) {
	in.ObjectMeta.Finalizers = util.Remove(in.ObjectMeta.Finalizers, finalizerName)
}

func (in *ClusterImport) HasFinalizer(finalizerName string) bool {
	return util.Contains(in.ObjectMeta.Finalizers, finalizerName)
}

// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

type ClusterImportList struct {
	metav1.TypeMeta `json:",inline"`
	metav1.ListMeta `json:"metadata,omitempty"`
	Items           []ClusterImport `json:"items,omitempty"`
}

func init() {
	SchemeBuilder.Register(&ClusterImport{}, &ClusterImportList{})
}
