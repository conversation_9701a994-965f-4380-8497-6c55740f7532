package v1alpha1

type NotebookConditions []NotebookCondition

func (cs *NotebookConditions) UpsertCondition(condition NotebookCondition) {
	for index, wfCondition := range *cs {
		if wfCondition.Type == condition.Type {
			(*cs)[index] = condition
			return
		}
	}
	*cs = append(*cs, condition)
}

func (cs *NotebookConditions) FindCondition(conditionType string) *NotebookCondition {
	for _, c := range *cs {
		if c.Type == conditionType {
			return &c
		}
	}
	return nil
}

func (cs *NotebookConditions) UpsertConditionMessage(condition NotebookCondition) {
	for index, wfCondition := range *cs {
		if wfCondition.Type == condition.Type {
			(*cs)[index].Message += ", " + condition.Message
			return
		}
	}
	*cs = append(*cs, condition)
}
