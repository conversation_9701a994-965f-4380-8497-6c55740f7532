/*
Copyright 2020 The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package v1alpha1

import (
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

type ServiceExportState string

const (
	Pending ServiceExportState = "Pending"
	Syncing ServiceExportState = "Syncing"
)

func (s ServiceExportState) String() string {
	return string(s)
}

// +genclient
// +kubebuilder:object:root=true
// +kubebuilder:subresource:status

// ServiceExport declares that the Service with the same name and namespace
// as this export should be consumable from other clusters.
type ServiceExport struct {
	metav1.TypeMeta `json:",inline"`
	// +optional
	metav1.ObjectMeta `json:"metadata,omitempty"`

	Spec ServiceExportSpec `json:"spec,omitempty"`
	// status describes the current state of an exported service.
	// Service configuration comes from the Service that had the same
	// name and namespace as this ServiceExport.
	// Populated by the multi-cluster service implementation's controller.
	// +optional
	Status ServiceExportStatus `json:"status,omitempty"`
}

// ServiceExportSpec 导出定义
type ServiceExportSpec struct {
	ExportDestinations []ExportDestination `json:"destinations,omitempty"`
}

type ExportDestination struct {
	Enabled             bool              `json:"enabled"`                       //同步器名称
	ServiceName         string            `json:"serviceName"`                   //服务名
	ServiceNamespace    string            `json:"serviceNamespace"`              //服务空间
	AdditionalLabels    map[string]string `json:"additionalLabels,omitempty"`    //扩展的metadata
	PortName            string            `json:"portName"`                      //主端口
	AdditionalPortNames []string          `json:"additionalPortNames,omitempty"` //扩展端口
	// +kubebuilder:validation:Enum=APIServer;Polaris
	RegistrationType RegistrationType  `json:"registrationType"`     //导入类型
	Properties       map[string]string `json:"properties,omitempty"` //属性，扩展字段
}

type RegistrationType string

const (
	Polaris   RegistrationType = "Polaris"
	APIServer RegistrationType = "APIServer"
)

// ServiceExportStatus contains the current status of an export.
type ServiceExportStatus struct {
	State             ServiceExportState `json:"state"`
	StartTime         *metav1.Time       `json:"startTime,omitempty"`
	ModifyTime        *metav1.Time       `json:"modifyTime,omitempty"`
	Message           string             `json:"message,omitempty"`
	MultiExportStatus MultiExportStatus  `json:"multiExportStatus,omitempty"`
}

type MultiExportStatus []ExportStatus

func (s *MultiExportStatus) Upsert(ref *ExportStatus) {
	for i, r := range *s {
		if r.Name == ref.Name {
			(*s)[i] = *ref
			return
		}
	}
	*s = append(*s, *ref)
}

func (s *MultiExportStatus) FindByName(name string) *ExportStatus {
	for _, r := range *s {
		if r.Name == name {
			return &r
		}
	}
	return nil
}

func (s *MultiExportStatus) Remove(name string) {
	for i, r := range *s {
		if r.Name == name {
			*s = append((*s)[:i], (*s)[i+1:]...)
			return
		}
	}
}

func (s *MultiExportStatus) Len() int {
	return len(*s)
}

// +kubebuilder:object:generate:=true

type ExportStatus struct {
	Name               string             `json:"name,omitempty"` //唯一标识
	ServiceName        string             `json:"serviceName,omitempty"`
	ServiceNamespace   string             `json:"serviceNamespace,omitempty"`
	RegistrationType   RegistrationType   `json:"registrationType,omitempty"`
	Conditions         []metav1.Condition `json:"conditions,omitempty"`
	State              string             `json:"state"`
	LastTransitionTime metav1.Time        `json:"lastTransitionTime,omitempty"`
}

// +kubebuilder:object:root=true

// ServiceExportList represents a list of endpoint slices
type ServiceExportList struct {
	metav1.TypeMeta `json:",inline"`
	// Standard list metadata.
	// +optional
	metav1.ListMeta `json:"metadata,omitempty"`
	// List of endpoint slices
	// +listType=set
	Items []ServiceExport `json:"items"`
}

func init() {
	SchemeBuilder.Register(&ServiceExport{}, &ServiceExportList{})
}
