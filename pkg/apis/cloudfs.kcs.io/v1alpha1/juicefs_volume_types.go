package v1alpha1

import (
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/util"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

const (
	JuiceFSVolumeConditionMetaReady          string = "MetaReady"
	JuiceFSVolumeConditionBucketReady        string = "BucketReady"
	JuiceFSVolumeConditionFormatReady        string = "FormatReady"
	JuiceFSVolumeConditionClusterSyncedReady string = "ClusterSyncedReady"
)

type JuiceFSVolumeSpec struct {
	DisplayName   string              `json:"displayName,omitempty"`
	Description   string              `json:"description,omitempty"`
	Region        string              `json:"region"`
	Zone          string              `json:"zone"`
	Limit         *JuiceFSVolumeLimit `json:"limit"`
	SyncClusters  []string            `json:"syncClusters,omitempty"` //需要同步的集群/**/
	VirtualBucket string              `json:"virtualBucket"`          //桶名称, 用于存储持久化数据
	Owner         string              `json:"owner,omitempty"`        //创建者
	// +kubebuilder:validation:Enum=none;lz4;zstd
	Compress       string `json:"compress,omitempty"` //压缩方式, 默认为none, 支持lz4, zstd
	ReadOnly       string `json:"readOnly,omitempty"` //只读模式, 默认为false
	MetaClusterRef string `json:"metaClusterRef"`     //元数据集群模式
}

type JuiceFSVolumeLimit struct {
	QuotaLimit    *JuiceFSQuotaLimit `json:"quotaLimit"`
	UploadLimit   uint64             `json:"uploadLimit"`   //Mbps 默认为0
	DownloadLimit uint64             `json:"downloadLimit"` //Mbps 默认为0
	TrashDays     uint32             `json:"trashDays"`     //回收站保留天数 默认为0
}

type JuiceFSQuotaLimit struct {
	Capacity uint64 `json:"capacity"` //容量限制 默认为0
	Inodes   uint64 `json:"inodes"`   //inodes限制 默认为0
}

func NewDefaultJuiceFSQuotaLimit() *JuiceFSQuotaLimit {
	return &JuiceFSQuotaLimit{
		Capacity: 1024,      //默认1T
		Inodes:   100000000, //默认1亿文件
	}
}

func NewDefaultJuiceFSVolumeLimit() *JuiceFSVolumeLimit {
	return &JuiceFSVolumeLimit{
		QuotaLimit:    NewDefaultJuiceFSQuotaLimit(),
		UploadLimit:   1024 * 10, //默认10Gbps
		DownloadLimit: 1024 * 10, //默认10Gbps
		TrashDays:     7,         //默认7天
	}
}

type JuiceFSVolumeStatus struct {
	State              VolumeState               `json:"state"`
	MetaStatus         JuiceFSVolumeMetaStatus   `json:"meta,omitempty"`
	ObjectStatus       JuiceFSVolumeObjectStatus `json:"bucket,omitempty"`
	LastTransitionTime metav1.Time               `json:"lastTransitionTime"`
	SecretRef          string                    `json:"secretRef,omitempty"`
	Conditions         Conditions                `json:"conditions,omitempty"`
	Message            string                    `json:"message,omitempty"`
	Reason             string                    `json:"reason,omitempty"`
	SyncStatus         SyncStatus                `json:"syncStatus,omitempty"`
}

type JuiceFSVolumeMetaStatus struct {
	MetaURL            string      `json:"metaUrl,omitempty"`
	State              string      `json:"state"`
	Message            string      `json:"message,omitempty"`
	LastTransitionTime metav1.Time `json:"lastTransitionTime"` //更新时间
}

type JuiceFSVolumeObjectStatus struct {
	BucketName string `json:"bucketName,omitempty"` //桶名称
	State      string `json:"state"`
	AccessKey  string `json:"accessKey,omitempty"`
	SecretKey  string `json:"secretKey,omitempty"`
	AccessURL  string `json:"accessUrl,omitempty"`
	Message    string `json:"message,omitempty"`
}

// +genclient
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object
// +k8s:openapi-gen=true
// +kubebuilder:resource:scope=Namespaced
// +kubebuilder:object:root=true
// +kubebuilder:subresource:status

type JuiceFSVolume struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty"`
	Spec              JuiceFSVolumeSpec   `json:"spec"`
	Status            JuiceFSVolumeStatus `json:"status,omitempty"`
}

func (in *JuiceFSVolume) HasFinalizer(finalizerName string) bool {
	return util.Contains(in.ObjectMeta.Finalizers, finalizerName)
}

func (in *JuiceFSVolume) AddFinalizer(finalizerName string) {
	in.ObjectMeta.Finalizers = append(in.ObjectMeta.Finalizers, finalizerName)
}

func (in *JuiceFSVolume) RemoveFinalizer(finalizerName string) {
	in.ObjectMeta.Finalizers = util.Remove(in.ObjectMeta.Finalizers, finalizerName)
}

// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

type JuiceFSVolumeList struct {
	metav1.TypeMeta `json:",inline"`
	metav1.ListMeta `json:"metadata,omitempty"`
	Items           []JuiceFSVolume `json:"items"`
}

func init() {
	SchemeBuilder.Register(&JuiceFSVolume{}, &JuiceFSVolumeList{})
}
