package v1alpha1

import metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

type CubeFSSpecification string

// +genclient
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object
// +k8s:openapi-gen=true
// +kubebuilder:resource:scope=Cluster
// +kubebuilder:object:root=true
// +kubebuilder:printcolumn:name="Cluster",type="string",JSONPath=".meta.name",description="Cluster Of CubeFS"
// +kubebuilder:printcolumn:name="Region",type="string",JSONPath=".region",description="Region Of CubeFS Cluster"
// +kubebuilder:printcolumn:name="Zones",type="string",JSONPath=".zones",description="Zones Of CubeFS Cluster"
// +kubebuilder:printcolumn:name="DisplayName",type="string",JSONPath=".displayName",description="DisplayName Of CubeFS Cluster"
// +kubebuilder:printcolumn:name="Description",type="string",JSONPath=".description",description="Description Of CubeFS Cluster"
// +kubebuilder:printcolumn:name="DashboardRef",type="string",JSONPath=".dashboardRef",description="Dashboard Of CubeFS Cluster"
// +kubebuilder:printcolumn:name="MasterAddr",type="string",JSONPath=".spec.zone",description="MasterAddr Of CubeFS Cluster"
// +kubebuilder:printcolumn:name="Age",type="date",JSONPath=".metadata.creationTimestamp"

type CubeFSCluster struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty"`
	Region            string   `json:"region"`
	Zones             []string `json:"zones"`
	DisplayName       string   `json:"displayName,omitempty"`
	Description       string   `json:"description,omitempty"`
	DashboardRef      string   `json:"dashboardRef"`
	Enabled           bool     `json:"enabled"`
	MasterAddr        string   `json:"masterAddr"`
	//+kubebuilder:validation:Enum=powerful;normal;capacity
	Specification string `json:"specification"`
}

// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

type CubeFSClusterList struct {
	metav1.TypeMeta `json:",inline"`
	metav1.ListMeta `json:"metadata,omitempty"`
	Items           []CubeFSCluster `json:"items"`
}

// +genclient
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object
// +k8s:openapi-gen=true
// +kubebuilder:resource:scope=Cluster
// +kubebuilder:object:root=true
// +kubebuilder:printcolumn:name="User",type="string",JSONPath=".user",description="Cluster Of CubeFS Dashboard"
// +kubebuilder:printcolumn:name="Password",type="string",JSONPath=".password",description="Password Of CubeFS Dashboard"
// +kubebuilder:printcolumn:name="URL",type="string",JSONPath=".url",description="URL Of CubeFS Dashboard"

type CubeFSDashboard struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty"`
	User              string `json:"user"`
	Password          string `json:"password"`
	URL               string `json:"url"`
}

// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

type CubeFSDashboardList struct {
	metav1.TypeMeta `json:",inline"`
	metav1.ListMeta `json:"metadata,omitempty"`
	Items           []CubeFSDashboard `json:"items"`
}

func init() {
	SchemeBuilder.Register(&CubeFSCluster{}, &CubeFSClusterList{})
	SchemeBuilder.Register(&CubeFSDashboard{}, &CubeFSDashboardList{})
}
