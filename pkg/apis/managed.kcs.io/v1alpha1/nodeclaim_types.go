package v1alpha1

import (
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	v1 "open-cluster-management.io/api/cluster/v1"
)

type NodeType string

const (
	NodeTypeNormal    NodeType = "Normal"
	NodeTypeSuperNode NodeType = "SuperNode"
)

type NodeClaimSpec struct {
	ResourceAllocations []*ResourceAllocation `json:"resourceAllocations,omitempty"` // 已分配的资源, 可以按照队列更细粒度的进行资源分配
}

type ResourceAllocation struct {
	QueueRef   QueueRef               `json:"queueRef"`            // 队列引用
	Available  corev1.ResourceList    `json:"available"`           // 已分配的资源
	Conditions []*AllocationCondition `json:"condition,omitempty"` //资源出借的条件
}

type AllocationConditions []AllocationCondition

func (a AllocationConditions) FindByType(t AllocationConditionType) *AllocationCondition {
	for _, item := range a {
		if item.Type == t {
			return &item
		}
	}
	return nil
}

type AllocationConditionType string

const (
	Cron    string = "Cron"
	Metrics string = "Metrics"
)

// AllocationCondition 分配条件
type AllocationCondition struct {
	Type    AllocationConditionType  `json:"type"`
	Cron    AllocateConditionCron    `json:"cron,omitempty"`    //定时出借
	Metrics AllocateConditionMetrics `json:"metrics,omitempty"` //指标出借
}

type AllocateConditionMetrics struct {
	Promql             string          `json:"promql"`
	CooldownPeriod     int             `json:"cooldownPeriod"`     // 冷却时间, 单位秒, 默认为30秒
	Duration           metav1.Duration `json:"duration"`           // 持续时间, 默认为30秒
	Interval           metav1.Duration `json:"interval"`           // 间隔时间, 默认为30秒
	MinimumLendingTime metav1.Duration `json:"minimumLendingTime"` // 最小出借时间, 默认为1h
}

type ResourceAllocationStatus struct {
	Allocated []*ResourceAllocated `json:"allocated"` //队列真实的资源分配
}

type ResourceAllocated struct {
	QueueRef  *QueueRef           `json:"queueRef"`
	Allocated corev1.ResourceList `json:"allocated"`
}

type AllocateConditionCron struct {
	Start string `json:"start"` //Cron表达式
	End   string `json:"end"`   //Cron表达式
}

type QueueRef struct {
	Name      string `json:"name"`
	Namespace string `json:"namespace"`
}

type NodeClaimStatus struct {
	corev1.NodeStatus `json:",inline"`
	PodMetrics        map[string]*PodMetric `json:"podMetrics,omitempty"`
	PodCount          *PodCount             `json:"podCount,omitempty"`
	NodeIP            string                `json:"nodeIP,omitempty"`
	HostName          string                `json:"hostName,omitempty"`
	NodeType          NodeType              `json:"nodeType,omitempty"` //节点类型
}

type PodCount struct {
	Current     int64 `json:"current"`
	Allocatable int64 `json:"allocatable"`
}

type PodMetric struct {
	Name             string                                `json:"name"`
	Namespace        string                                `json:"namespace"`
	Request          map[v1.ResourceName]resource.Quantity `json:"request,omitempty"`
	Limit            map[v1.ResourceName]resource.Quantity `json:"limit,omitempty"`
	ContainerMetrics map[string]ContainerMetric            `json:"containerMetrics,omitempty"`
	OwnReferences    []metav1.OwnerReference               `json:"ownReferences,omitempty"`
	QueueRef         *QueueRef                             `json:"queueRef,omitempty"`
}

type ContainerMetric struct {
	Name    string                                `json:"name"`
	Request map[v1.ResourceName]resource.Quantity `json:"request,omitempty"`
	Limit   map[v1.ResourceName]resource.Quantity `json:"limit,omitempty"`
}

// +genclient
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object
// +genclient:nonNamespaced
// +kubebuilder:resource:scope=Cluster
// +kubebuilder:object:root=true
// +kubebuilder:subresource:status

type NodeClaim struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty"`
	Spec              NodeClaimSpec   `json:"spec,omitempty"`
	Status            NodeClaimStatus `json:"status,omitempty"`
}

// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

type NodeClaimList struct {
	metav1.TypeMeta `json:",inline"`
	metav1.ListMeta `json:"metadata,omitempty"`
	Items           []NodeClaim `json:"items,omitempty"`
}

func init() {
	SchemeBuilder.Register(&NodeClaim{}, &NodeClaimList{})

}
