package service

import (
	"context"
	"errors"
	"fmt"
	"git.lianjia.com/cloudnative/kcs/pkg/exporter/task"
	"git.lianjia.com/cloudnative/kcs/pkg/exporter/types"
	multiclusterv1alpha1 "git.lianjia.com/cloudnative/kic/kic-platform/pkg/apis/multicluster.kcs.io/v1alpha1"
)

type IExporter interface {
	OnServiceChanged(ctx context.Context, t *task.Task) error
	OnServiceRemoved(ctx context.Context, t *task.Task) error
	SubscribeServiceChangedEvent(ctx context.Context, subscription *types.Subscription, eventChannel chan<- types.ServiceChangedEvent)
	UnSubscribeServiceChangedEvent(ctx context.Context, subscription *types.Subscription)
}

func GetExporterFromServiceExport(serviceExport *multiclusterv1alpha1.ServiceExport, t *task.Task) (*multiclusterv1alpha1.ExportDestination, error) {
	for _, item := range serviceExport.Spec.ExportDestinations {
		if t.Name == GenerateServiceExportName(&item) {
			return item.DeepCopy(), nil
		}
	}
	return nil, errors.New("not found exporter")
}

func GenerateServiceExportName(serviceExporter *multiclusterv1alpha1.ExportDestination) string {
	return fmt.Sprintf("%s-%s-%s", serviceExporter.ServiceNamespace, serviceExporter.ServiceName, string(serviceExporter.RegistrationType))
}
