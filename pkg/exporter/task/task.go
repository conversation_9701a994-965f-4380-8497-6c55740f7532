package task

import (
	"fmt"
	multiclusterv1alpha1 "git.lianjia.com/cloudnative/kic/kic-platform/pkg/apis/multicluster.kcs.io/v1alpha1"
)

type Task struct {
	Name             string
	ServiceName      string
	ServiceNamespace string
	RegistrationType multiclusterv1alpha1.RegistrationType
}

func (t *Task) String() string {
	return fmt.Sprintf("SyncTask{Name: %s ServiceExporter: [%s:%s], Exporter: %s", t.Name, t.ServiceNamespace, t.ServiceName, t.RegistrationType)
}

func (t *Task) Key() string {
	return fmt.Sprintf("%s|%s|%s|%s", t.Name, t.ServiceNamespace, t.ServiceName, t.RegistrationType)
}
