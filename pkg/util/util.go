package util

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/md5"
	"crypto/rand"
	"crypto/sha256"
	"encoding/base32"
	"encoding/base64"
	"encoding/hex"
	"fmt"
	mrand "math/rand"
	"reflect"
	"sort"
	"strconv"
	"strings"
	"time"

	"golang.org/x/exp/slices"
	"google.golang.org/protobuf/types/known/timestamppb"
)

const (
	SshKeyPrivateKey = "gwQYbDWjDJXBzRDMcRsYKawUggFjOZjX"
)

func GenerateKey(length int) string {
	b := make([]byte, length)
	_, err := rand.Read(b)
	if err != nil {
		return ""
	}
	return base64.URLEncoding.EncodeToString(b)
}

func GenerateHash(name string, length int) string {
	hash := sha256.New()
	hash.Write([]byte(name))
	hashName := strings.ToLower(base32.HexEncoding.WithPadding(base32.NoPadding).EncodeToString(hash.Sum(nil)))[:length]
	return hashName
}

func Contains(slice []string, item string) bool {
	for _, i := range slice {
		if i == item {
			return true
		}
	}
	return false
}

func Remove(slice []string, item string) []string {
	var result []string
	for _, i := range slice {
		if i != item {
			result = append(result, i)
		}
	}
	return result
}

// TimeToTimestamp Time.time to *timestamppb.Timestamp
func TimeToTimestamp(t time.Time) *timestamppb.Timestamp {
	return timestamppb.New(t)
}

// SliceIncludeSlice the slice include the b slice
func SliceIncludeSlice(a, b []string) bool {
	if EqualSlice(a, b) {
		return true
	}
	for _, item := range b {
		if !slices.Contains(a, item) {
			return false
		}
	}
	return true
}

// EqualSlice checks if two slice are equal
func EqualSlice(a, b []string) bool {
	sort.Strings(a)
	sort.Strings(b)
	return reflect.DeepEqual(a, b)
}

func RemoveDuplicate(arr []string) (newArr []string) {
	newArr = make([]string, 0)
	for i := 0; i < len(arr); i++ {
		repeat := false
		for j := i + 1; j < len(arr); j++ {
			if arr[i] == arr[j] {
				repeat = true
				break
			}
		}
		if !repeat {
			newArr = append(newArr, arr[i])
		}
	}
	return newArr
}

func MergeMaps(map1, map2 map[string]string) map[string]string {
	result := make(map[string]string)
	for k, v := range map1 {
		result[k] = v
	}
	for k, v := range map2 {
		result[k] = v
	}
	return result
}

func DiffStringSlice(a, b []string) []string {
	mb := make(map[string]bool, len(b))
	for _, x := range b {
		mb[x] = true
	}

	var diff []string
	for _, x := range a {
		if !mb[x] {
			diff = append(diff, x)
		}
	}
	return diff
}

func TimeFormat(t time.Time) string {
	if t.Location() == time.UTC {
		// 如果是 UTC，设置为北京时间（中国标准时间，UTC+8）
		t = t.In(time.FixedZone("CST", 8*3600))
	}
	return t.Format(time.DateTime)
}

func TimesFormat(times []time.Time) []string {
	beijingLoc, _ := time.LoadLocation("Asia/Shanghai")
	formatTime := make([]string, 0, len(times))
	for _, t := range times {
		beijingTime := t.In(beijingLoc)
		formatTime = append(formatTime, beijingTime.Format(time.DateTime))
	}
	return formatTime
}

func BoolPtr(b bool) *bool {
	return &b
}

func Int32Ptr(a int32) *int32 {
	return &a
}

func StringPtr(s string) *string {
	return &s
}

func Int64Ptr(a int64) *int64 {
	return &a
}

func Md5s(s string) string {
	hasher := md5.New()
	hasher.Write([]byte(s))
	return hex.EncodeToString(hasher.Sum(nil))
}

func StringInSlice(s string, list []string) bool {
	for _, v := range list {
		if v == s {
			return true
		}
	}
	return false
}

func RemoveStringsInSlice(ss []string, list []string) []string {
	for _, s := range ss {
		list = RemoveStringInSlice(s, list)
	}
	return list
}

func RemoveStringInSlice(s string, list []string) []string {
	for i, v := range list {
		if v == s {
			return append(list[:i], list[i+1:]...)
		}
	}
	return list
}

func AddStringsInSlice(ss []string, list []string) []string {
	for _, s := range ss {
		list = AddStringInSlice(s, list)
	}
	return list
}

func AddStringInSlice(s string, list []string) []string {
	if !StringInSlice(s, list) {
		list = append(list, s)
	}
	return list
}

func GenerateRandomStringWithPrefix(prefix string, n int) string {
	now := time.Now()
	timeStr := now.Format("20060102150405")
	return fmt.Sprintf("%s-%s-%s", prefix, timeStr, GenerateRandomString(n))
}

func GenerateRandomString(n int) string {
	const letters = "abcdefghijklmnopqrstuvwxyz0123456789"
	mrand.NewSource(time.Now().UnixNano())
	b := make([]byte, n)
	for i := range b {
		b[i] = letters[mrand.Intn(len(letters))]
	}
	return string(b)
}

func GenerateAlphabeticString(n int) string {
	const letters = "abcdefghijklmnopqrstuvwxyz"
	mrand.NewSource(time.Now().UnixNano())
	b := make([]byte, n)
	for i := range b {
		b[i] = letters[mrand.Intn(len(letters))]
	}
	return string(b)
}

func DiffSlice(old, new []string) (add, del []string) {
	oldMap := map[string]bool{}
	for _, v := range old {
		oldMap[v] = true
	}
	for _, v := range new {
		if !oldMap[v] {
			add = append(add, v)
		}
	}
	newMap := map[string]bool{}
	for _, v := range new {
		newMap[v] = true
	}
	for _, v := range old {
		if !newMap[v] {
			del = append(del, v)
		}
	}
	return
}

// AESEncrypt AES 加密
// text 待加密的文本
// key 加密密钥
func AESEncrypt(text string, key string) (string, error) {
	if text == "" {
		return "", nil
	}
	if key == "" {
		return "", fmt.Errorf("key is empty")
	}
	plainText := []byte(text)
	keyByte := []byte(key)
	// 创建 AES 加密算法块
	block, err := aes.NewCipher(keyByte)
	if err != nil {
		return "", err
	}

	// 生成一个 IV（初始化向量），使用 AES 算法要求块大小与密钥匹配
	blockSize := block.BlockSize()
	iv := make([]byte, blockSize)
	_, err = rand.Read(iv)
	if err != nil {
		return "", err
	}

	// 创建 AES CBC 模式
	mode := cipher.NewCBCEncrypter(block, iv)

	// 填充 plainText，确保长度是块大小的倍数
	plainText = pad(plainText, blockSize)

	// 加密数据
	cipherText := make([]byte, len(plainText))
	mode.CryptBlocks(cipherText, plainText)

	// 返回加密后的数据（包括 iv）
	return string(append(iv, cipherText...)), nil
}

// AESDecrypt AES 解密
// text 待解密的文本
// key 解密密钥
func AESDecrypt(text string, key string) (string, error) {
	if text == "" {
		return "", nil
	}
	if key == "" {
		return "", fmt.Errorf("key is empty")
	}
	cipherText := []byte(text)
	keyByte := []byte(key)
	// 获取 iv 和密文
	block, err := aes.NewCipher(keyByte)
	if err != nil {
		return "", err
	}

	blockSize := block.BlockSize()
	if len(cipherText) < blockSize {
		return "", fmt.Errorf("ciphertext too short")
	}

	iv := cipherText[:blockSize]
	cipherText = cipherText[blockSize:]

	// 创建 AES CBC 解密模式
	mode := cipher.NewCBCDecrypter(block, iv)

	// 解密数据
	plainText := make([]byte, len(cipherText))
	mode.CryptBlocks(plainText, cipherText)

	// 去掉填充
	plainText = unpad(plainText)

	return string(plainText), nil
}

func pad(src []byte, blockSize int) []byte {
	padding := blockSize - len(src)%blockSize
	padText := make([]byte, padding)
	for i := 0; i < padding; i++ {
		padText[i] = byte(padding)
	}
	return append(src, padText...)
}

func unpad(src []byte) []byte {
	padding := src[len(src)-1]
	return src[:len(src)-int(padding)]
}

func Max(a, b int32) int32 {
	if a > b {
		return a
	}
	return b
}

func IsBeforeNow(timestamp string) bool {
	if timestamp == "" {
		return false
	}
	timestampInt64, _ := strconv.ParseInt(timestamp, 10, 64)
	targetTime := time.Unix(timestampInt64, 0)
	return targetTime.Before(time.Now())
}
