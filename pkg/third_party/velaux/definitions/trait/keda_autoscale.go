package trait

type KeDaAutoscaleParameter struct {
	ScaleTargetRef  ScaleTargetRef `json:"scaleTargetRef"`
	MinReplicaCount int32          `json:"minReplicaCount"`
	MaxReplicaCount int32          `json:"maxReplicaCount"`
	Fallback        *Fallback      `json:"fallback,omitempty"`
	PollingInterval int32          `json:"pollingInterval"`
	Triggers        Triggers       `json:"triggers"`
}

type ScaleTargetRef struct {
	APIVersion string `json:"apiVersion"`
	Type       string `json:"type"`
	Name       string `json:"name,omitempty"`
	Container  string `json:"container,omitempty"`
}

type Fallback struct {
	Replicas         int `json:"replicas"`
	FailureThreshold int `json:"failureThreshold"`
}

type Triggers struct {
	CPU        *CPU        `json:"cpu,omitempty"`
	Memory     *Memory     `json:"memory,omitempty"`
	Cron       *Cron       `json:"cron,omitempty"`
	Prometheus *Prometheus `json:"prometheus,omitempty"`
	Kafka      *Kafka      `json:"kafka,omitempty"`
	MetricAPI  *MetricAPI  `json:"metricApi,omitempty"`
}

type CPU struct {
	Value string `json:"value"`
}

type Memory struct {
	Value string `json:"value"`
}

type Cron struct {
	Timezone        string `json:"timezone"`
	Start           string `json:"start"`
	End             string `json:"end"`
	DesiredReplicas string `json:"desiredReplicas"`
}

type Prometheus struct {
	ServerAddress string `json:"serverAddress"`
	MetricName    string `json:"metricName"`
	Query         string `json:"query"`
	Threshold     string `json:"threshold"`
}

type Kafka struct {
	BootstrapServers  string `json:"bootstrapServers"`
	ConsumerGroup     string `json:"consumerGroup"`
	Topic             string `json:"topic"`
	LagThreshold      string `json:"lagThreshold,omitempty"`
	OffsetResetPolicy string `json:"offsetResetPolicy,omitempty"`
}

type MetricAPI struct {
	TargetValue           float32 `json:"targetValue"`
	ActivationTargetValue float32 `json:"activationTargetValue"`
	URL                   string  `json:"url"`
	ValueLocation         string  `json:"valueLocation"`
}
