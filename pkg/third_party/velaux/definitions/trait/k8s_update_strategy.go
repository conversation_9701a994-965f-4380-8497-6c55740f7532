package trait

type UpdateStrategyParameter struct {
	TargetAPIVersion string   `json:"targetAPIVersion"`
	TargetKind       string   `json:"targetKind"`
	Strategy         Strategy `json:"strategy"`
}

type Strategy struct {
	Type            string                 `json:"type"`
	RollingStrategy *RollingUpdateStrategy `json:"rollingStrategy,omitempty"`
}

type RollingUpdateStrategy struct {
	MaxSurge       string `json:"maxSurge"`
	MaxUnavailable string `json:"maxUnavailable"`
	Partition      int32  `json:"partition"`
}
