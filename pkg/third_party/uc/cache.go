package uc

import (
	"context"
	cache "github.com/patrickmn/go-cache"
	"time"
)

type cachedManager struct {
	client Client
	cache  *cache.Cache
}

func (c *cachedManager) CheckUserExist(ctx context.Context, account string) (bool, error) {
	return c.client.CheckUserExist(ctx, account)
}

func (c *cachedManager) GetUser(ctx context.Context, account string) (*User, error) {
	v, ok := c.cache.Get(account)
	if ok {
		return v.(*User), nil
	}
	user, err := c.client.GetUser(ctx, account)
	if err != nil {
		return nil, err
	}
	c.cache.Set(account, user, 6*time.Hour)
	return user, nil
}

func (c *cachedManager) GetRealName(ctx context.Context, account string) string {
	user, err := c.GetUser(ctx, account)
	if err != nil {
		return account + "(未找到)"
	}
	fullName := user.Name + "(" + account + ")"
	if user.Status == 0 {
		fullName += "(已离职)"
	}
	return fullName
}

func (c *cachedManager) GetDepartment(ctx context.Context, account string) string {
	user, err := c.GetUser(ctx, account)
	if err != nil || user.Status == 0 {
		return ""
	}
	return user.OrgName
}

func (c *cachedManager) GetOrg(ctx context.Context, orgcode string) (*Organization, error) {
	v, ok := c.cache.Get(orgcode)
	if ok {
		return v.(*Organization), nil
	}
	org, err := c.client.GetOrg(ctx, orgcode)
	if err != nil {
		return nil, err
	}
	c.cache.Set(orgcode, org, 6*time.Hour)
	return org, nil
}

func (c cachedManager) GetUserByID(ctx context.Context, userid string) (*User, error) {
	v, ok := c.cache.Get(userid)
	if ok {
		return v.(*User), nil
	}
	user, err := c.client.GetUserByID(ctx, userid)
	if err != nil {
		return nil, err
	}
	c.cache.Set(userid, user, 6*time.Hour)
	return user, nil
}
