package prom

import (
	"context"
	"git.lianjia.com/cloudnative/kic/kic-platform/apis/aistudio/bcode"
	prometheusapi "github.com/prometheus/client_golang/api"
	prometheusv1 "github.com/prometheus/client_golang/api/prometheus/v1"
	"github.com/prometheus/common/model"
	"math"
	"time"
)

type client struct {
	prometheusAPI         prometheusv1.API
	defaultTimeoutSeconds time.Duration
}

func NewClient(url string, timeout time.Duration) (Client, error) {
	c, err := prometheusapi.NewClient(prometheusapi.Config{
		Address: url,
	})
	if err != nil {
		return nil, err
	}
	return &client{
		prometheusAPI:         prometheusv1.NewAPI(c),
		defaultTimeoutSeconds: timeout,
	}, nil
}

type SeriesVectorDataPoint struct {
	Labels    map[string]string
	Value     float64
	Timestamp time.Time
}

func (s *SeriesVectorDataPoint) DeepCopy() *SeriesVectorDataPoint {
	return &SeriesVectorDataPoint{
		Labels:    s.Labels,
		Value:     s.Value,
		Timestamp: s.Timestamp,
	}
}

type QueryResult struct {
	DataPoints []SeriesVectorDataPoint
}

type QueryRequest struct {
	Promql         string
	TimeoutSeconds time.Duration
}

func (p *client) Query(ctx context.Context, request *QueryRequest) (*QueryResult, error) {
	if request.TimeoutSeconds == 0 {
		request.TimeoutSeconds = p.defaultTimeoutSeconds
	}
	queryResult, warnings, err := p.prometheusAPI.Query(ctx, request.Promql, time.Now(), prometheusv1.WithTimeout(request.TimeoutSeconds))
	if err != nil {
		return nil, bcode.ErrorServerInternalError("query metrics[%s] failed, error:%v", request.Promql, err)
	}
	if len(warnings) > 0 {
		return nil, bcode.ErrorServerInternalError("unexpected warnings: %v", warnings)
	}
	if queryResult.Type() != model.ValVector {
		return nil, bcode.ErrorServerInternalError("unexpected result type: %v", queryResult.Type())
	}
	var dataPoints []SeriesVectorDataPoint
	for _, elem := range queryResult.(model.Vector) {
		if float64(elem.Value) < float64(0) || math.IsNaN(float64(elem.Value)) {
			elem.Value = 0
		}
		var labels = make(map[string]string)
		for k, v := range elem.Metric {
			labels[string(k)] = string(v)
		}
		dataPoints = append(dataPoints, SeriesVectorDataPoint{
			Labels:    labels,
			Value:     float64(elem.Value),
			Timestamp: elem.Timestamp.Time(),
		})
	}
	return &QueryResult{
		DataPoints: dataPoints,
	}, nil
}
