package cmdb

import (
	"time"
)

type Page struct {
	PageNum  int32 `json:"page_num"`
	PageSize int32 `json:"page_size"`
}

type ResponseBase struct {
	Code    int32  `json:"code"`
	Message string `json:"message"`
}

type ListHostResponse struct {
	ResponseBase
	Data []HostDetail `json:"data"`
}

type HostDetailResponse struct {
	ResponseBase
	Data HostDetail `json:"data"`
}

// HostDetail 主机详情
type HostDetail struct {
	BkHostID      int32  `json:"bk_host_id"`         //主机id
	BkHostInnerIP string `json:"bk_host_innerip"`    //内网ip
	BkHostOuterIP string `json:"bk_host_outerip"`    //外网ip
	BkSN          string `json:"bk_sn"`              //sn
	Region        string `json:"region"`             //地区
	Operator      string `json:"operator"`           //管理员
	Zone          string `json:"zone"`               //区域
	IdcName       string `json:"idc_name,omitempty"` //机房名称
	Room          string `json:"room"`               //机房
	Rack          string `json:"rack"`               //机架
	Package       string `json:"package"`            //机型配置
	HostResourceDetail
	UtilizationType string              `json:"utilization_type"`   //主机分类
	Type            string              `json:"type"`               //主机类型
	DiskInfo        string              `json:"disk_info"`          //磁盘信息
	BizInfo         string              `json:"biz_info"`           //业务信息
	Queue           []HostQueueDetail   `json:"queue"`              //队列
	K8sCluster      []HostClusterDetail `json:"kubernetes_cluster"` //k8s集群
	IdcInfo         []HostIdcInfo       `json:"idc_info,omitempty"` //机房信息

}

type HostResourceDetail struct {
	CpuCores   int32  `json:"cpu_cores"`             //CPU核数
	MemorySize int32  `json:"mem"`                   //内存大小(MB)
	GpuModel   string `json:"gpu_model,omitempty"`   //GPU型号
	GpuNum     int32  `json:"gpu_num,omitempty"`     //GPU数量
	GpuMemory  int32  `json:"gpu_memory,omitempty"`  //gpu显存
	GpuProduct string `json:"gpu_product,omitempty"` //gpu厂商
}

type blueKingBase struct {
	BkInstID          int32  `json:"bk_inst_id"`
	BkInstName        string `json:"bk_inst_name"`
	BkObjID           string `json:"bk_obj_id"`
	BkSupplierAccount string `json:"bk_supplier_account"`
	CreateTime        string `json:"create_time"`
	LastTime          string `json:"last_time"`
}

type HostQueueDetail struct {
	App string `json:"app,omitempty"`
	blueKingBase
	Description string `json:"description,omitempty"`
}

type HostClusterDetail struct {
	blueKingBase
	K8sClusterAlias  string `json:"k8s_cluster_alias,omitempty"`
	K8sClusterDesc   string `json:"k8s_cluster_desc,omitempty"`
	K8sClusterIDC    string `json:"k8s_cluster_idc,omitempty"`
	K8sClusterRegion string `json:"k8s_cluster_region,omitempty"`
	K8sClusterZone   string `json:"k8s_cluster_zone,omitempty"`
}

type HostIdcInfo struct {
	blueKingBase
	IDCAlias string `json:"idc_alias,omitempty"`
	IDCDesc  string `json:"idc_desc,omitempty"`
}

// ListSetsByBizIDResponse 获取集合信息通过业务ID
type ListSetsByBizIDResponse struct {
	ResponseBase
	Data *ListSetsByBizIDData `json:"data"`
}
type ListSetsByBizIDData struct {
	Count int32      `json:"count"`
	Info  []*SetInfo `json:"info"`
}
type SetInfo struct {
	BkBizId           int32     `json:"bk_biz_id"`
	BkParentId        int32     `json:"bk_parent_id"`
	BkServiceStatus   string    `json:"bk_service_status"`
	BkSetDesc         string    `json:"bk_set_desc"`
	BkSetEnv          string    `json:"bk_set_env"`
	BkSetId           int32     `json:"bk_set_id"`
	BkSetName         string    `json:"bk_set_name"`
	BkSupplierAccount string    `json:"bk_supplier_account"`
	CreateTime        time.Time `json:"create_time"`
	Default           int32     `json:"default"`
	Description       string    `json:"description"`
	LastTime          time.Time `json:"last_time"`
	SetTemplateId     int32     `json:"set_template_id"`
	TopoModuleName    string    `json:"topo_module_name"`
}

// ListModulesBySetIDResponse 获取模块信息通过集合ID
type ListModulesBySetIDResponse struct {
	ResponseBase
	Data *ListModulesBySetIDData `json:"data"`
}
type ListModulesBySetIDData struct {
	Count int32         `json:"count"`
	Info  []*ModuleInfo `json:"info"`
}
type ModuleInfo struct {
	BkBizId           int32     `json:"bk_biz_id"`
	BkModuleId        int32     `json:"bk_module_id"`
	BkModuleName      string    `json:"bk_module_name"`
	BkModuleType      string    `json:"bk_module_type"`
	BkParentId        int32     `json:"bk_parent_id"`
	BkSetId           int32     `json:"bk_set_id"`
	BkSupplierAccount string    `json:"bk_supplier_account"`
	CreateTime        time.Time `json:"create_time"`
	Default           int32     `json:"default"`
	HostApplyEnabled  bool      `json:"host_apply_enabled"`
	LastTime          time.Time `json:"last_time"`
	ServiceCategoryId int32     `json:"service_category_id"`
	ServiceTemplateId int32     `json:"service_template_id"`
	SetTemplateId     int32     `json:"set_template_id"`
	TopoModuleName    string    `json:"topo_module_name"`
	Use               string    `json:"use"`
	Origin            string    `json:"origin"`
	Operator          string    `json:"operator"`
}

// ListHostsByModuleIDResponse 获取主机信息通过模块ID
type ListHostsByModuleIDResponse struct {
	ResponseBase
	Data *ListHostsByModuleIDData `json:"data"`
}
type ListHostsByModuleIDData struct {
	Count int32       `json:"count"`
	Info  []*HostInfo `json:"info"`
}
type HostInfo struct {
	BkHostId        int32  `json:"bk_host_id"`
	BkHostInnerip   string `json:"bk_host_innerip"`
	Operator        string `json:"operator"`
	BkSn            string `json:"bk_sn"`
	IdcName         string `json:"idc_name"`
	Room            string `json:"room"`
	Rack            string `json:"rack"`
	Package         string `json:"package"`
	CpuCores        int32  `json:"cpu_cores"`
	Mem             int32  `json:"mem"`
	DiskInfo        string `json:"disk_info"`
	Type            string `json:"type"`
	GpuModel        string `json:"gpu_model"`
	GpuNum          int32  `json:"gpu_num"`
	GpuAvailableNum int32  `json:"gpu_available_num"`
	BizInfo         string `json:"biz_info"`
	GpuProduct      string `json:"gpu_product"`
}

// ListDomainModelResponse 获取域名模块信息
type ListDomainModelResponse struct {
	ResponseBase
	Data *ListDomainModelResponseData `json:"data"`
}

type ListDomainModelResponseData struct {
	Count int32              `json:"count"`
	Info  []*DomainModelInfo `json:"info"`
}

type DomainModelInfo struct {
	BackendServers    string              `json:"backend_servers"`
	BkInstId          int32               `json:"bk_inst_id"`
	BkInstName        string              `json:"bk_inst_name"`
	BkObjId           string              `json:"bk_obj_id"`
	BkSupplierAccount string              `json:"bk_supplier_account"`
	CreateTime        time.Time           `json:"create_time"`
	Description       string              `json:"description"`
	LastTime          time.Time           `json:"last_time"`
	Owner             string              `json:"owner"`
	Associations      *DomainAssociations `json:"associations"`
}

type DomainAssociations struct {
	Applications []*DomainAssociationApplication `json:"applications"`
}
type DomainAssociationApplication struct {
	Id           int32  `json:"id"`
	BkInstId     int32  `json:"bk_inst_id"`
	BkObjId      string `json:"bk_obj_id"`
	BkAsstInstId int32  `json:"bk_asst_inst_id"`
	BkAsstObjId  string `json:"bk_asst_obj_id"`
	BkObjAsstId  string `json:"bk_obj_asst_id"`
	BkAsstId     string `json:"bk_asst_id"`
}

// ListApplicationModelResponse 获取应用模块信息
type ListApplicationModelResponse struct {
	ResponseBase
	Data *ListApplicationModelResponseData `json:"data"`
}

type ListApplicationModelResponseData struct {
	Count int32                   `json:"count"`
	Info  []*ApplicationModelInfo `json:"info"`
}
type ApplicationModelInfo struct {
	BkInstId          int32     `json:"bk_inst_id"`
	BkInstName        string    `json:"bk_inst_name"`
	BkObjId           string    `json:"bk_obj_id"`
	BkSupplierAccount string    `json:"bk_supplier_account"`
	CreateTime        time.Time `json:"create_time"`
	Description       string    `json:"description"`
	DisplayName       string    `json:"display_name"`
	LastTime          time.Time `json:"last_time"`
	Owner             string    `json:"owner"`
}
