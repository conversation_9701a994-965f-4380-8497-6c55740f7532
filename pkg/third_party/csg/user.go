package csg

import (
	"context"
	"github.com/pkg/errors"
)

type CreateUserRequest struct {
	// Display name of the user
	Name string `json:"name"`
	// the login name
	Username string `json:"username"`
	Email    string `json:"email"`
	Phone    string `json:"phone"`
	UUID     string `json:"uuid"`
	// user registered from default login page, from casdoor, etc. Possible values:
	//
	// - "default"
	// - "casdoor"
	RegProvider string `json:"reg_provider"`
}

type CreateUserResponse struct {
	Msg  string      `json:"msg"`
	Data interface{} `json:"data"`
}

func (c *Client) CreateUser(ctx context.Context, req *CreateUserRequest) (*CreateUserResponse, error) {
	var response CreateUserResponse
	res, err := c.http.R().
		SetContext(ctx).
		SetBody(req).
		SetResult(&response).
		Post("/api/v1/users")
	if err != nil {
		return nil, err
	}
	if res.StatusCode() != 200 {
		return nil, errors.Errorf("CreateUser fail,err:%s", string(res.Body()))
	}
	return &response, nil
}
