package tcr

import (
	"context"
	"testing"
	"time"
)

const (
	secretId   = "AKID9BzfDeT9dt3ziJitpX6RYktFTghU1Ukr"
	secretKey  = "AGZlrljwytcGoFal3NWo18Of3LXfgnrp"
	region_bj  = "ap-beijing"
	region_sh  = "ap-shanghai"
	registryId = "tcr-m446wuql"
)

func TestClient_CreateNamespace(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	client, err := NewClient(ctx, &Config{
		SecretId:   secretId,
		SecretKey:  secretKey,
		Region:     region_bj,
		RegistryId: registryId,
	})
	if err != nil {
		t.Fatal(err)
	}

	res, err := client.CreateNamespace(ctx, "test")
	if err != nil {
		t.Fatal(err)
	}
	t.Log(res)
}

func TestClient_DeleteNamespace(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	client, err := NewClient(ctx, &Config{
		SecretId:   secretId,
		SecretKey:  secretKey,
		Region:     region_bj,
		RegistryId: registryId,
	})
	if err != nil {
		t.Fatal(err)
	}

	res, err := client.DeleteNamespace(ctx, "test")
	if err != nil {
		t.Fatal(err)
	}
	t.Log(res)
}

func TestClient_CreateServiceAccount(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	client, err := NewClient(ctx, &Config{
		SecretId:   secretId,
		SecretKey:  secretKey,
		Region:     region_bj,
		RegistryId: registryId,
	})
	if err != nil {
		t.Fatal(err)
	}

	res, err := client.CreateServiceAccount(ctx, "test", "test", "test", ReadOnlyServiceAccountType)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(*res.Response.Name)
	t.Log(*res.Response.Password)
}

func TestClient_DeleteServiceAccount(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	client, err := NewClient(ctx, &Config{
		SecretId:   secretId,
		SecretKey:  secretKey,
		Region:     region_bj,
		RegistryId: registryId,
	})
	if err != nil {
		t.Fatal(err)
	}

	res, err := client.DeleteServiceAccount(ctx, "test")
	if err != nil {
		t.Fatal(err)
	}
	t.Log(res)
}

func TestClient_GetTrigger(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	client, err := NewClient(ctx, &Config{
		SecretId:   secretId,
		SecretKey:  secretKey,
		Region:     region_bj,
		RegistryId: registryId,
	})
	if err != nil {
		t.Fatal(err)
	}

	res, err := client.GetTrigger(ctx, "aistudio-localtest-0606")
	if err != nil {
		t.Fatal(err)
	}
	for _, v := range res.Response.Triggers {
		t.Log(*v.Id)
	}
}

func TestClient_CreateTrigger(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	client, err := NewClient(ctx, &Config{
		SecretId:   secretId,
		SecretKey:  secretKey,
		Region:     region_bj,
		RegistryId: registryId,
	})
	if err != nil {
		t.Fatal(err)
	}

	header := map[string]string{
		"Content-Type": "application/json",
	}

	res, err := client.CreateTrigger(ctx, "test", "aistudio-localtest-0606", header)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(res)
}

func TestClient_DeleteTrigger(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	client, err := NewClient(ctx, &Config{
		SecretId:   secretId,
		SecretKey:  secretKey,
		Region:     region_bj,
		RegistryId: registryId,
	})
	if err != nil {
		t.Fatal(err)
	}

	err = client.DeleteTrigger(ctx, "aistudio-localtest-0606", 5)
	if err != nil {
		t.Fatal(err)
	}
}
