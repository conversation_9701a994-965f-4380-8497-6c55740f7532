package ldap

import (
	"context"
	"crypto/tls"
	"fmt"
	"github.com/go-ldap/ldap/v3"
	"sync"
	"time"
)

type Config struct {
	Host              string
	Port              int
	BaseDn            string
	BindUser          string
	BindPass          string
	AuthFilter        string
	Attributes        Attributes
	TLS               bool
	StartTLS          bool
	CheckConnInterval time.Duration
}

type Attributes struct {
	Name  string `yaml:"name"`
	Id    string `yaml:"id"`
	Email string `yaml:"email"`
}

type Client struct {
	ldapConn *ldap.Conn
	config   *Config
	ctx      context.Context
	mu       sync.RWMutex
}

func New(ctx context.Context, cf *Config) (*Client, error) {
	var s = &Client{
		config: cf,
		ctx:    ctx,
	}
	if err := s.connect(); err != nil {
		return nil, err
	}
	if s.config.BindUser != "" {
		// BindSearch mode
		if err := s.ldapConn.Bind(s.config.BindUser, s.config.BindPass); err != nil {
			return nil, fmt.Errorf("ldap.error: bind ldap fail: %v, use user(%s) to bind", err, s.config.BindUser)
		}
	}
	go func() {
		for {
			select {
			case <-ctx.Done():
				s.ldapConn.Close()
			}
		}
	}()
	return s, nil
}

//func (s *Client) getConnect() *ldap.Conn {
//	if s.ldapConn.IsClosing() {
//		s.ldapConn.Start()
//	}
//	return s.ldapConn
//}

func (s *Client) getConnect() *ldap.Conn {
	s.mu.Lock()
	defer s.mu.Unlock()

	if s.ldapConn != nil && s.ldapConn.IsClosing() {
		s.ldapConn = nil
	}

	if s.ldapConn == nil {
		var err error
		s.ldapConn, err = ldap.DialURL(fmt.Sprintf("ldap://%s:%d", s.config.Host, s.config.Port))
		if err != nil {
			fmt.Printf("ldap.error: dial ldap fail: %v", err)
			return nil
		}
		if s.config.BindUser != "" {
			// BindSearch mode
			if err := s.ldapConn.Bind(s.config.BindUser, s.config.BindPass); err != nil {
				fmt.Printf("ldap.error: bind ldap fail: %v, use user(%s) to bind", err, s.config.BindUser)
				return nil
			}
		}
	}

	return s.ldapConn
}

func (s *Client) connect() error {
	var conn *ldap.Conn
	var err error
	addr := fmt.Sprintf("%s:%d", s.config.Host, s.config.Port)
	if s.config.TLS {
		conn, err = ldap.DialTLS("tcp", addr, &tls.Config{InsecureSkipVerify: true})
	} else {
		conn, err = ldap.DialURL(fmt.Sprintf("ldap://%s:%d", s.config.Host, s.config.Port))
	}
	if err != nil {
		return fmt.Errorf("ldap.error: dial ldap fail: %v", err)
	}
	s.ldapConn = conn
	return nil
}

func (s *Client) genLdapAttributeSearchList() []string {
	return []string{s.config.Attributes.Name, s.config.Attributes.Email, s.config.Attributes.Id}
}

func (s *Client) LdapReq(user, pass string) (*ldap.SearchResult, error) {
	authFilter := ""
	if s.config.AuthFilter != "" {
		authFilter = fmt.Sprintf(s.config.AuthFilter, user)
	}
	searchRequest := ldap.NewSearchRequest(
		s.config.BaseDn, // The base dn to search
		ldap.ScopeWholeSubtree, ldap.NeverDerefAliases, 0, 0, false,
		authFilter,                     // The filter to apply
		s.genLdapAttributeSearchList(), // A list attributes to retrieve
		nil,
	)
	sr, err := s.getConnect().Search(searchRequest)
	if err != nil {
		return nil, fmt.Errorf("ldap.error: ldap search fail: %v", err)
	}
	if len(sr.Entries) == 0 {
		return nil, fmt.Errorf("ldap.error: user invalid")
	}
	if len(sr.Entries) > 1 {
		return nil, fmt.Errorf("ldap.error: search user(%s), multi entries found", user)
	}
	if err := s.ldapConn.Bind(sr.Entries[0].DN, pass); err != nil {
		return nil, fmt.Errorf("ldap.error: wrong password")
	}
	return sr, nil
}

// AuthenticateUser 方法用于验证用户名和密码是否正确
func (s *Client) AuthenticateUser(username, password string) (bool, error) {
	conn := s.getConnect() // 获取现有的LDAP连接
	if conn == nil {
		return false, fmt.Errorf("ldap.error: unable to get LDAP connection")
	}

	authFilter := ""
	if s.config.AuthFilter != "" {
		authFilter = fmt.Sprintf(s.config.AuthFilter, username)
	}
	searchRequest := ldap.NewSearchRequest(
		s.config.BaseDn, // The base dn to search
		ldap.ScopeWholeSubtree, ldap.NeverDerefAliases, 0, 0, false,
		authFilter,                     // The filter to apply
		s.genLdapAttributeSearchList(), // A list attributes to retrieve
		nil,
	)
	sr, err := conn.Search(searchRequest)
	if err != nil {
		return false, fmt.Errorf("ldap.error: ldap search fail: %v", err)
	}
	if len(sr.Entries) == 0 {
		return false, fmt.Errorf("ldap.error: user invalid")
	}
	if len(sr.Entries) > 1 {
		return false, fmt.Errorf("ldap.error: search user(%s), multi entries found", username)
	}

	// 尝试使用提供的用户名和密码绑定
	err = conn.Bind(sr.Entries[0].DN, password)
	if err != nil {
		if ldap.IsErrorWithCode(err, ldap.LDAPResultInvalidCredentials) {
			// 重新绑定到初始的绑定用户
			if s.config.BindUser != "" {
				if bindErr := conn.Bind(s.config.BindUser, s.config.BindPass); bindErr != nil {
					return false, fmt.Errorf("ldap.error: rebind ldap fail: %v", bindErr)
				}
			}
			return false, fmt.Errorf("ldap.error: wrong password") // 认证失败，但无需进一步处理
		}
		return false, fmt.Errorf("ldap.error: bind ldap fail: %v", err) // 其他错误
	}

	// 认证成功后，重新绑定到初始的绑定用户
	if s.config.BindUser != "" {
		if bindErr := conn.Bind(s.config.BindUser, s.config.BindPass); bindErr != nil {
			return false, fmt.Errorf("ldap.error: rebind ldap fail: %v", bindErr)
		}
	}

	// 认证成功
	return true, nil
}
