package multichannel_notification

import (
	"context"
	"fmt"
	"github.com/go-resty/resty/v2"
	"net/http"
)

const (
	WechatOfficialAccount = "/channels/wepub"
	WechatRobot           = "/channels/wecom"
	Phone                 = "/channels/phone"
	Email                 = "/channels/email"
)

type Config struct {
	URL             string `json:"url"`
	DefaultAppId    string `json:"default_app_id"`
	DefaultAppToken string `json:"default_app_token"`
}

type Client struct {
	client *resty.Client
	cfg    *Config
}

func NewClient(ctx context.Context, cfg *Config) (*Client, error) {
	c := &Client{
		cfg:    cfg,
		client: resty.New().SetBaseURL(cfg.URL),
	}
	c.client.SetPreRequestHook(func(c *resty.Client, request *http.Request) error {
		request.Header.Set("Content-Type", "application/json")
		return nil
	})
	return c, nil
}

func (c *Client) SendWechatOfficialAccountMessage(ctx context.Context, content, users string) error {
	res, err := c.client.R().
		SetBody(WechatOfficialAccountMessage{
			Content: content,
			Users:   users,
			Wepub: WechatConfig{
				WebUrl:   "https://wxwork.ke.com",
				AppId:    c.cfg.DefaultAppId,
				AppToken: c.cfg.DefaultAppToken,
			},
			Mode: "mail_prefix",
		}).
		Post(WechatOfficialAccount)

	if err != nil {
		return err
	}
	if res.StatusCode() != http.StatusOK {
		return fmt.Errorf("failed to send message: %s", res.Status())
	}
	return nil
}

func (c *Client) SendWechatRobotMessage(ctx context.Context, content, url string) error {
	res, err := c.client.R().
		SetBody(WechatRobotMessage{
			Content: content,
			Url:     url,
		}).
		Post(WechatRobot)
	if err != nil {
		return err
	}
	if res.StatusCode() != http.StatusOK {
		return fmt.Errorf("failed to send message: %s", res.Status())
	}
	return nil
}

func (c *Client) SendPhoneMessage(ctx context.Context, content, phones string) error {
	res, err := c.client.R().
		SetBody(PhoneMessage{
			Content: content,
			Phones:  phones,
		}).
		Post(Phone)
	if err != nil {
		return err
	}
	if res.StatusCode() != http.StatusOK {
		return fmt.Errorf("failed to send message: %s", res.Status())
	}
	return nil
}

func (c *Client) SendEmailMessage(ctx context.Context, subject, content, users string) error {
	res, err := c.client.R().
		SetBody(EmailMessage{
			Subject: subject,
			Content: content,
			Users:   users,
		}).
		Post(Email)
	if err != nil {
		return err
	}
	if res.StatusCode() != http.StatusOK {
		return fmt.Errorf("failed to send message: %s", res.Status())
	}
	return nil
}
