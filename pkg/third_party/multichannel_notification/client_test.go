package multichannel_notification

import (
	"context"
	"testing"
)

func TestClient(t *testing.T) {
	// 创建测试用的配置
	cfg := &Config{
		URL:             "http://ruler-preview.kcs.ke.com",
		DefaultAppId:    "kic",
		DefaultAppToken: "4lkMhcsiZSUPuwfTjpGg1TTuF6dLKAWP9Z",
	}

	// 测试正常发送微信公众号消息
	t.Run("SendWechatOfficialAccountMessage_Success", func(t *testing.T) {
		client, _ := NewClient(context.Background(), cfg)

		err := client.SendWechatOfficialAccountMessage(
			context.Background(),
			"test content",
			"lvhuawei003",
		)

		if err != nil {
			t.<PERSON><PERSON>("Unexpected error: %v", err)
		}
	})
}
