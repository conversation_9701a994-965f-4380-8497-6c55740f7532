package multichannel_notification

import (
	"context"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/property"
)

var mgr *Client

func Init(ctx context.Context, properties property.EnvironmentProperty) error {
	cfg := &Config{
		URL:             properties.MustGetString("url"),
		DefaultAppId:    properties.MustGetString("defaultAppId"),
		DefaultAppToken: properties.MustGetString("defaultAppToken"),
	}
	c, err := NewClient(ctx, cfg)
	if err != nil {
		return err
	}
	mgr = c
	return nil
}

func Instance() *Client {
	return mgr
}
