package multichannel_notification

type WechatOfficialAccountMessage struct {
	Content string       `json:"content"` // 消息内容
	Users   string       `json:"users"`   // 用户列表
	Wepub   WechatConfig `json:"wepub"`   // 公众号配置
	Mode    string       `json:"mode"`    // 用户模式，支持user_code和mail_prefix两种模式。user_code模式下，users字段为贝壳用户code,按|分隔，如xxx|yyy|zzz；mail_prefix模式下，users字段为贝壳邮箱前缀，按逗号分隔，如xxx001,yyy002,zzz003；
}

type WechatConfig struct {
	WebUrl   string `json:"web_url"`
	AppId    string `json:"app_id"`
	AppToken string `json:"app_token"`
}

type NotificationResponse struct {
	Code   int    `json:"code"`
	Status string `json:"status"`
	Data   string `json:"data"`
}

type WechatRobotMessage struct {
	Content string `json:"content"` // 消息内容
	Url     string `json:"url"`
}

type PhoneMessage struct {
	Content string `json:"content"` // 必填, 语音通知内容 最长100个字符
	Phones  string `json:"phones"`  // 必填, 手机号 1开头，11位纯数字，只能为国内手机号,多个按英文逗号','分隔
}

type EmailMessage struct {
	Subject string `json:"subject"` // 必填, 主题
	Content string `json:"content"` // 必填, 邮件通知内容 最长100个字符
	Users   string `json:"users"`   // 必填, 邮箱，多个按英文逗号','分隔
}
