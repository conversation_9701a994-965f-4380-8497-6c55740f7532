package aistudio2

import (
	"context"
	"fmt"
	aistudio2api "git.lianjia.com/cloudnative/kic/kic-platform/apis"
	platformv1 "git.lianjia.com/cloudnative/kic/kic-platform/apis/aistudio/platform/v1"
	"testing"
)

func Test_Client(t *testing.T) {
	clientOptions := aistudio2api.NewClientOptions()
	clientOptions.Endpoint = "http://aistudio.kcs.ke.com"
	clientOptions.AccessTokenProvider = func() string {
		return "***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
	}
	aistudio2Cli, err := aistudio2api.NewClient(context.Background(), clientOptions)
	if err != nil {
		t.Errorf("NewClient error: %v", err)
		return
	}
	res, err := aistudio2Cli.Platform().ListNodes(context.Background(), &platformv1.ListNodesOptions{
		PageSize: 10,
		Page:     1,
		Assigned: true,
	})
	if err != nil {
		t.Errorf("GetQueueDetail error: %v", err)
		return
	}
	fmt.Println(res.Total)
}
