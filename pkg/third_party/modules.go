package thirdpart

import (
	"context"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/third_party/multichannel_notification"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/third_party/temporal"

	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/property"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/third_party/bkticket"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/third_party/cmdb"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/third_party/csg"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/third_party/distributedcache"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/third_party/dragonfly"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/third_party/gitea"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/third_party/keycloak"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/third_party/ldap"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/third_party/nightingale"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/third_party/notice"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/third_party/oceanbase"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/third_party/openobserve"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/third_party/prometheus"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/third_party/s3"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/third_party/tcr"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/third_party/uc"
	vela "git.lianjia.com/cloudnative/kic/kic-platform/pkg/third_party/velaux"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/third_party/wxwork"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/util"
	//gocloak "github.com/Nerzal/gocloak/v13"
)

//var cloakClient *gocloak.Client

var modules []*Module

func init() {
	modules = append(modules, &Module{Init: uc.Init, Name: "uc"})
	modules = append(modules, &Module{Init: keycloak.Init, Name: "keycloak"})
	modules = append(modules, &Module{Init: vela.Init, Name: "velaux"})
	modules = append(modules, &Module{Init: cmdb.Init, Name: "cmdb"})
	modules = append(modules, &Module{Init: tcr.Init, Name: "tcr"})
	modules = append(modules, &Module{Init: dragonfly.Init, Name: "dragonfly"})
	modules = append(modules, &Module{Init: csg.Init, Name: "csg"})
	modules = append(modules, &Module{Init: gitea.Init, Name: "gitea"})
	modules = append(modules, &Module{Init: notice.Init, Name: "notice"})
	modules = append(modules, &Module{Init: ldap.Init, Name: "ldap"})
	modules = append(modules, &Module{Init: s3.Init, Name: "s3"})
	modules = append(modules, &Module{Init: openobserve.Init, Name: "openobserve"})
	modules = append(modules, &Module{Init: oceanbase.Init, Name: "oceanbase"})
	modules = append(modules, &Module{Init: nightingale.Init, Name: "nightingale"})
	modules = append(modules, &Module{Init: bkticket.Init, Name: "bkticket"})
	modules = append(modules, &Module{Init: distributedcache.Init, Name: "distributedcache"})
	modules = append(modules, &Module{Init: prometheus.Init, Name: "prometheus"})
	modules = append(modules, &Module{Init: wxwork.Init, Name: "wxwork"})
	modules = append(modules, &Module{Init: temporal.Init, Name: "temporal"})
	modules = append(modules, &Module{Init: multichannel_notification.Init, Name: "multichannel_notification"})
}

type Module struct {
	Init func(ctx context.Context, properties property.EnvironmentProperty) error
	Name string
}

func SetupManager(ctx context.Context, properties property.EnvironmentProperty) error {
	usedModules := properties.GetStringSlice("application.modules")
	for _, m := range modules {
		if util.Contains(usedModules, m.Name) {
			prop := properties.Sub(m.Name)
			err := m.Init(ctx, prop)
			if err != nil {
				return err
			}
		}
	}
	return nil
}
