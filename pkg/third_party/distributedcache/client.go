package distributedcache

import (
	"context"
	"strconv"

	stdhttp "net/http"

	"github.com/go-resty/resty/v2"
)

// Config 配置
type Config struct {
	URL  string `json:"url"`
	Port int    `json:"port"`
}

// Client 客户端
type Client struct {
	cfg  *Config
	http *resty.Client
}

// NewClient 创建一个新客户端
func NewClient(ctx context.Context, cfg *Config) (*Client, error) {
	dc := &Client{
		cfg:  cfg,
		http: resty.New().SetBaseURL(cfg.URL + ":" + strconv.Itoa(cfg.Port)),
	}
	dc.http.SetPreRequestHook(func(c *resty.Client, request *stdhttp.Request) error {
		request.Header.Set("Content-Type", "application/json")
		return nil
	})
	return dc, nil
}
