package s3

import (
	"context"

	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/property"
)

var mgr *Client

func Init(ctx context.Context, properties property.EnvironmentProperty) error {
	cfg := &Config{
		Endpoint:         properties.MustGetString("endpoint"),
		AccessKey:        properties.MustGetString("accessKey"),
		SecretKey:        properties.MustGetString("secretKey"),
		Region:           properties.MustGetString("region"),
		S3ForcePathStyle: properties.MustGetBool("s3ForcePathStyle"),
		BuildImageBucket: properties.MustGetString("buildImageBucket"),
	}
	c, err := NewClient(ctx, cfg)
	if err != nil {
		return err
	}
	mgr = c
	return nil
}

func Instance() *Client {
	return mgr
}
