package gitea

import (
	"context"

	"github.com/OpenCSGs/gitea-go-sdk/gitea"
)

func (c *Client) AddCollaborator(ctx context.Context, namespace, repoType, repo, collaborator string, accessMode string) error {
	mode := gitea.AccessMode(accessMode)
	opt := gitea.AddCollaboratorOption{
		Permission: &mode,
	}
	owner := GetOwner(namespace, repoType)
	_, err := c.giteaClient.AddCollaborator(owner, repo, collaborator, opt)
	if err != nil {
		return err
	}
	return nil
}

func (c *Client) DeleteCollaborator(ctx context.Context, namespace, repoType, repo, collaborator string) error {
	owner := GetOwner(namespace, repoType)
	_, err := c.giteaClient.DeleteCollaborator(owner, repo, collaborator)
	if err != nil {
		return err
	}
	return nil
}
