package gitea

import (
	"context"
	"fmt"
	"io"
	"strings"

	"k8s.io/klog"
)

type GetAllFilesReq struct {
	Namespace string `json:"namespace"`
	Name      string `json:"name"`
	RepoType  string `json:"repo_type"`
	Ref       string `json:"ref"`
	Path      string `json:"path"`
}
type GetAllFilesResp struct {
	Data []*File `json:"data"`
}

func (c *Client) GetAllFiles(ctx context.Context, req *GetAllFilesReq) (*GetAllFilesResp, error) {
	allFiles, err := getAllFiles(ctx, req.Namespace, req.Name, req.Ref, req.Path, req.RepoType, c.GetRepoFileTree)
	if err != nil {
		return nil, err
	}
	resp := GetAllFilesResp{
		Data: allFiles,
	}
	return &resp, nil
}

type GetRepoInfoByPathReq struct {
	Namespace string `json:"namespace"`
	Name      string `json:"name"`
	Ref       string `json:"ref"`
	Path      string `json:"path"`
	RepoType  string `json:"repo_type"`
}

func getAllFiles(ctx context.Context, namespace, repoName, ref, folder string, repoType string, gsTree func(ctx context.Context, req GetRepoInfoByPathReq) ([]*File, error)) ([]*File, error) {
	var files []*File

	getRepoFileTree := GetRepoInfoByPathReq{
		Namespace: namespace,
		Name:      repoName,
		Ref:       ref,
		Path:      folder,
		RepoType:  repoType,
	}
	gitFiles, err := gsTree(context.Background(), getRepoFileTree)
	if err != nil {
		return files, fmt.Errorf("failed to get repo file tree,%w", err)
	}
	for _, file := range gitFiles {
		if file.Type == "dir" {
			subFiles, err := getAllFiles(ctx, namespace, repoName, ref, file.Path, repoType, gsTree)
			if err != nil {
				return files, err
			}
			files = append(files, subFiles...)
		} else {
			files = append(files, file)
		}
	}
	return files, nil
}

type File struct {
	Name string `json:"name"`
	Type string `json:"type"`
	Size int64  `json:"size"`
	Path string `json:"path"`
	Mode string `json:"mode"`
	SHA  string `json:"sha"`
	// URL to browse the file
	URL            string `json:"url"`
	Content        string `json:"content"`
	Lfs            bool   `json:"lfs"`
	LfsPointerSize int64  `json:"lfs_pointer_size"`
	// relative path in lfs storage
	LfsRelativePath string `json:"lfs_relative_path"`
	LastCommitSHA   string `json:"last_commit_sha"`
}

func (c *Client) getRepoDir(owner, name, ref, path string) (files []*File, err error) {
	giteaEntries, _, err := c.giteaClient.GetDir(owner, name, ref, path)
	if err != nil {
		return
	}
	for _, entry := range giteaEntries {
		f := &File{
			Name:            entry.Name,
			Path:            strings.TrimPrefix(entry.Path, "/"),
			Type:            entry.Type,
			Lfs:             entry.IsLfs,
			LfsRelativePath: entry.LfsRelativePath,
			Size:            entry.Size,
			Mode:            entry.Mode,
			SHA:             entry.SHA,
			URL:             entry.URL,
			LastCommitSHA:   entry.LastCommitSHA,
		}
		if entry.Type == "tree" {
			f.Type = "dir"
		} else {
			f.Type = "file"
		}

		files = append(files, f)

	}

	return files, nil
}

func (c *Client) GetRepoFileTree(ctx context.Context, req GetRepoInfoByPathReq) ([]*File, error) {
	owner := GetOwner(req.Namespace, req.RepoType)
	return c.getRepoDir(owner, req.Name, req.Ref, req.Path)
}

type GetBranchResp struct {
	Name   string
	Commit *PayloadCommit `json:"commit"`
}
type PayloadCommit struct {
	ID      string `json:"id"`
	Message string `json:"message"`
}

func (c *Client) GetBranch(ctx context.Context, namespace, repoType, name, branch string) (*GetBranchResp, error) {
	owner := GetOwner(namespace, repoType)
	branchInfo, _, _ := c.giteaClient.GetRepoBranch(owner, name, branch)
	if branchInfo == nil {
		return nil, nil
	}
	return &GetBranchResp{
		Name: branchInfo.Name,
		Commit: &PayloadCommit{
			ID:      branchInfo.Commit.ID,
			Message: branchInfo.Commit.Message,
		},
	}, nil
}

// DownloadReq 下载请求
type DownloadReq struct {
	Namespace string `json:"namespace"`
	RepoType  string `json:"repo_type"`
	Name      string `json:"name"`
	Ref       string `json:"ref"`
	Path      string `json:"path"`
	Lfs       bool   `json:"lfs"`
}

// Download 下载文件
func (c *Client) Download(ctx context.Context, req *DownloadReq) (io.ReadCloser, error) {
	owner := GetOwner(req.Namespace, req.RepoType)
	reader, _, err := c.giteaClient.GetFileReader(owner, req.Name, req.Ref, req.Path, req.Lfs)
	if err != nil {
		klog.Errorf("failed to get file reader, err: %v", err)
		return nil, err
	}
	return reader, nil
}
