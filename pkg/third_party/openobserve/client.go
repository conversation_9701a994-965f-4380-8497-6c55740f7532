package openobserve

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/go-resty/resty/v2"
	"io"
	"net/http"
	"time"
)

const (
	defaultOpenobserveUserRole = "admin"
	OpenObserveTraefikOrg      = "traefik"
	OpenObserveTraefikStream   = "traefik_accesslog"
)

type OpenobserveConfig struct {
	Host          string
	Authorization string
	Org           string
	Stream        string
	NotifyStream  string
}

func (obc OpenobserveConfig) Modify(req *http.Request) error {
	req.Header.Set("Authorization", obc.Authorization)
	return nil
}

type OpenobserveService interface {
	WriteLogByJson(data any, streams ...string) error
	GetOpenObserveSearchData(ctx context.Context, searchBody *SearchRequestBody) (*SearchResponseBody, error)
	GetOpenObserveSearchDataWithOrg(ctx context.Context, searchBody *SearchRequestBody, org string) (*SearchResponseBody, error)
}

type openobserveService struct {
	config OpenobserveConfig
	client *resty.Client
	ctx    context.Context
}

func NewOpenobserveService(ctx context.Context, config OpenobserveConfig) OpenobserveService {
	return &openobserveService{
		config: config,
		client: resty.New().
			SetBaseURL(fmt.Sprintf("http://%s", config.Host)).
			SetHeader("Content-Type", "application/json").
			SetHeader("Authorization", config.Authorization).
			SetRetryCount(3).
			SetRetryWaitTime(1 * time.Second).
			SetRetryMaxWaitTime(3 * time.Second),
		ctx: ctx,
	}
}

type OpenobserveUser struct {
	Email     string `json:"email"`
	FirstName string `json:"first_name"`
	LastName  string `json:"last_name"`
	Password  string `json:"password"`
	Role      string `json:"role"`
}

type OpenobserveApiResponse struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
}

type OpenobserveDataApiResponse struct {
	Code   int                                `json:"code"`
	Status []OpenobserveDataApiResponseStatus `json:"status"`
}

type OpenobserveDataApiResponseStatus struct {
	Name       string `json:"name"`
	Successful int    `json:"successful"`
	Failed     int    `json:"failed"`
	Error      string `json:"error"`
}

func (obs *openobserveService) WriteLogByJson(data any, streams ...string) error {
	stream := obs.config.Stream
	if len(streams) > 0 && streams[0] != "" {
		stream = streams[0]
	}

	url := fmt.Sprintf("/api/%s/%s/_json", obs.config.Org, stream)
	resp, err := obs.client.R().
		SetContext(obs.ctx).
		SetBody(data).
		SetResult(&OpenobserveDataApiResponse{}).
		Post(url)

	if err != nil {
		return fmt.Errorf("write log failed: %v", err)
	}

	if resp.IsError() {
		return handleObsError(resp)
	}

	// 类型断言获取结果
	result, ok := resp.Result().(*OpenobserveDataApiResponse)
	if !ok {
		return fmt.Errorf("unexpected response type")
	}

	for _, status := range result.Status {
		if status.Failed > 0 {
			return fmt.Errorf("write failed for stream %s: %s", status.Name, status.Error)
		}
	}
	return nil
}

func (obs *openobserveService) GetOpenObserveSearchData(ctx context.Context, searchBody *SearchRequestBody) (*SearchResponseBody, error) {
	url := fmt.Sprintf("/api/%s/_search", obs.config.Org)

	resp, err := obs.client.R().
		SetContext(ctx).
		SetBody(searchBody).
		SetResult(&SearchResponseBody{}).
		Post(url)

	if err != nil {
		return nil, fmt.Errorf("search failed: %v", err)
	}

	if resp.IsError() {
		return nil, handleObsError(resp)
	}

	return resp.Result().(*SearchResponseBody), nil
}

func handleObsError(resp *resty.Response) error {
	if resp.StatusCode() == http.StatusOK {
		return nil
	}

	var apiErr OpenobserveApiResponse
	if err := json.Unmarshal(resp.Body(), &apiErr); err != nil {
		return fmt.Errorf("request failed (status %d), error parsing: %v", resp.StatusCode(), err)
	}

	return fmt.Errorf("request failed (status %d): %s", resp.StatusCode(), apiErr.Message)
}

func (obs *openobserveService) GetOpenObserveSearchDataWithOrg(ctx context.Context, searchBody *SearchRequestBody, org string) (*SearchResponseBody, error) {
	url := fmt.Sprintf("/api/%s/_search", org)
	resp, err := obs.client.R().SetContext(ctx).SetBody(searchBody).SetResult(&SearchResponseBody{}).Post(url)

	if err != nil {
		return nil, fmt.Errorf("search failed: %v", err)
	}

	if resp.IsError() {
		return nil, handleObsError(resp)
	}

	return resp.Result().(*SearchResponseBody), nil
}

func getObsHttpApiStatusNotOKError(respBody io.ReadCloser) error {
	data, err := io.ReadAll(respBody)
	if err != nil {
		return fmt.Errorf("read response body failed, err: %v", err)
	}
	body := &OpenobserveApiResponse{}
	if err := json.Unmarshal(data, body); err != nil {
		return fmt.Errorf("unmarshal response body failed, err: %v", err)
	}
	return fmt.Errorf("err message: %s", body.Message)
}
