package main

import (
	"context"
	schedule "git.lianjia.com/cloudnative/kic/kic-platform/pkg/third_party/temporal/schedule_test"
	"github.com/pborman/uuid"
	"go.temporal.io/sdk/client"
	"log"
	"time"
)

func main() {
	ctx := context.Background()
	c, err := client.Dial(client.Options{
		HostPort: client.DefaultHostPort,
	})
	if err != nil {
		log.Fatalln("无法创建客户端", err)
	}
	defer c.Close()

	//handle := c.ScheduleClient().GetHandle(ctx, "schedule_1")
	//desc, err := handle.Describe(ctx)
	//if err != nil {
	//	fmt.Println(desc)
	//	fmt.Println(err)
	//}

	scheduleID := "schedule_" + uuid.New()
	workflowID := "schedule_workflow_" + uuid.New()

	// 定义开始和结束时间
	startTime := time.Now().Add(1 * time.Minute) // 1min后开始
	endTime := startTime.Add(10 * time.Minute)   // 10min后结束

	// 创建 Schedule
	scheduleHandle, err := c.ScheduleClient().Create(ctx, client.ScheduleOptions{
		ID: scheduleID,
		Spec: client.ScheduleSpec{
			Intervals: []client.ScheduleIntervalSpec{
				{Every: 1 * time.Minute}, // 每分钟运行一次
			},
			StartAt: startTime,
			EndAt:   endTime,
		},
		Action: &client.ScheduleWorkflowAction{
			ID:        workflowID,
			Workflow:  schedule.SampleScheduleWorkflow,
			TaskQueue: "schedule",
		},
	})
	if err != nil {
		log.Fatalln("无法创建 Schedule", err)
	}
	defer func() {
		log.Println("删除 Schedule", "ScheduleID", scheduleHandle.GetID())
		err = scheduleHandle.Delete(ctx)
		if err != nil {
			log.Fatalln("无法删除 Schedule", err)
		}
	}()

	// 取消暂停 Schedule
	log.Println("取消暂停 Schedule", "ScheduleID", scheduleHandle.GetID())
	err = scheduleHandle.Unpause(ctx, client.ScheduleUnpauseOptions{})
	if err != nil {
		log.Fatalln("无法取消暂停 Schedule", err)
	}

	// 等待 Schedule 完成
	log.Println("等待 Schedule 完成", "ScheduleID", scheduleHandle.GetID())
	time.Sleep(25 * time.Minute) // 等待超过结束时间
}
