package main

import (
	schedule "git.lianjia.com/cloudnative/kic/kic-platform/pkg/third_party/temporal/schedule_test"
	"go.temporal.io/sdk/client"
	"go.temporal.io/sdk/worker"
	"log"
)

func main() {
	// 创建 Temporal 客户端
	c, err := client.Dial(client.Options{
		HostPort: client.DefaultHostPort,
	})
	if err != nil {
		log.Fatalln("无法创建客户端", err)
	}
	defer c.Close()

	// 创建 Worker 并注册工作流和活动
	w := worker.New(c, "schedule", worker.Options{})
	w.RegisterWorkflow(schedule.SampleScheduleWorkflow)
	w.RegisterActivity(schedule.DoSomething)

	// 启动 Worker
	err = w.Run(worker.InterruptCh())
	if err != nil {
		log.Fatalln("无法启动 Worker", err)
	}
}
