package schedule

import (
	"context"
	"go.temporal.io/sdk/activity"
	"go.temporal.io/sdk/workflow"
	"time"
)

// SampleScheduleWorkflow 是一个示例工作流
func SampleScheduleWorkflow(ctx workflow.Context) error {
	workflow.GetLogger(ctx).Info("计划工作流已启动。", "开始时间", workflow.Now(ctx))

	ao := workflow.ActivityOptions{
		StartToCloseTimeout: 10 * time.Second,
	}
	ctx1 := workflow.WithActivityOptions(ctx, ao)

	// 执行活动
	err := workflow.ExecuteActivity(ctx1, DoSomething).Get(ctx, nil)
	if err != nil {
		workflow.GetLogger(ctx).Error("计划工作流失败。", "错误", err)
		return err
	}

	return nil
}

// DoSomething 是一个示例活动
func DoSomething(ctx context.Context) error {
	activity.GetLogger(ctx).Info("计划任务正在运行。")
	// 在这里执行实际的任务逻辑
	return nil
}
