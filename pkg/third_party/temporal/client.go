package temporal

import (
	"fmt"
	"go.temporal.io/sdk/client"
)

type Config struct {
	HostPort  string
	Namespace string
	TaskQueue string
	Retention string
}

type Client struct {
	Cfg    Config
	Client client.Client
}

func NewClient(cfg Config) (*Client, error) {
	if cfg.HostPort == "" {
		return nil, fmt.Errorf("empty HostPort")
	}
	c, err := client.Dial(client.Options{
		HostPort:  cfg.HostPort,
		Namespace: cfg.Namespace,
	})
	if err != nil {
		return nil, fmt.Errorf("unable to create Temporal client: %w", err)
	}
	return &Client{
		Cfg:    cfg,
		Client: c,
	}, nil
}
