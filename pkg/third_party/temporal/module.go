package temporal

import (
	"context"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/property"
)

var mgr *Client

func Init(ctx context.Context, properties property.EnvironmentProperty) error {
	cfg := &Config{
		HostPort:  properties.MustGetString("hostport"),
		TaskQueue: properties.MustGetString("taskqueue"),
		Namespace: properties.MustGetString("namespace"),
	}
	c, err := NewClient(*cfg)
	if err != nil {
		return err
	}
	mgr = c
	return nil
}

func Instance() *Client {
	return mgr
}
