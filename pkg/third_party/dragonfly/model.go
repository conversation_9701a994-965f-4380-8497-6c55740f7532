package dragonfly

import "time"

type PreheatTask struct {
	ID                  int         `json:"id"`
	TaskID              string      `json:"task_id"`
	Type                string      `json:"type"`
	State               string      `json:"state"`
	Args                PreheatArgs `json:"args"`
	SchedulerClusterIDs []uint      `json:"scheduler_cluster_ids" binding:"omitempty"`
	CreatedAt           time.Time   `json:"created_at"`
	UpdatedAt           time.Time   `json:"updated_at"`
}

type PreheatArgs struct {
	// Type is the preheating type, support image and file.
	Type string `json:"type" binding:"required,oneof=image file"`

	// URL is the image url for preheating.
	URL string `json:"url" binding:"required"`

	// Tag is the tag for preheating.
	Tag string `json:"tag" binding:"omitempty"`

	// FilteredQueryParams is the filtered query params for preheating.
	FilteredQueryParams string `json:"filteredQueryParams" binding:"omitempty"`

	// PieceLength is the piece length for preheating.
	PieceLength uint32 `json:"pieceLength" binding:"omitempty"`

	// Headers is the http headers for authentication.
	Headers map[string]string `json:"headers" binding:"omitempty"`

	// Username is the username for authentication.
	Username string `json:"username" binding:"omitempty"`

	// Password is the password for authentication.
	Password string `json:"password" binding:"omitempty"`

	// The image type preheating task can specify the image architecture type. eg: linux/amd64.
	Platform string `json:"platform" binding:"omitempty"`
}

type GetClusterResponse struct {
	ID                     uint                          `json:"id"`
	Name                   string                        `json:"name"`
	BIO                    string                        `json:"bio"`
	Scopes                 *SchedulerClusterScopes       `json:"scopes"`
	SchedulerClusterID     uint                          `json:"scheduler_cluster_id"`
	SeedPeerClusterID      uint                          `json:"seed_peer_cluster_id"`
	SchedulerClusterConfig *SchedulerClusterConfig       `json:"scheduler_cluster_config"`
	SeedPeerClusterConfig  *SeedPeerClusterConfig        `json:"seed_peer_cluster_config"`
	PeerClusterConfig      *SchedulerClusterClientConfig `json:"peer_cluster_config"`
	CreatedAt              time.Time                     `json:"created_at"`
	UpdatedAt              time.Time                     `json:"updated_at"`
	IsDefault              bool                          `json:"is_default"`
}

type GetSchedulerClustersQuery struct {
	Name    string `form:"name" binding:"omitempty"`
	Page    int    `form:"page" binding:"omitempty,gte=1"`
	PerPage int    `form:"per_page" binding:"omitempty,gte=1,lte=10000000"`
}

type SchedulerClusterConfig struct {
	CandidateParentLimit uint32 `yaml:"candidateParentLimit" mapstructure:"candidateParentLimit" json:"candidate_parent_limit" binding:"omitempty,gte=1,lte=20"`
	FilterParentLimit    uint32 `yaml:"filterParentLimit" mapstructure:"filterParentLimit" json:"filter_parent_limit" binding:"omitempty,gte=10,lte=1000"`
}

type SchedulerClusterClientConfig struct {
	LoadLimit uint32 `yaml:"loadLimit" mapstructure:"loadLimit" json:"load_limit" binding:"omitempty,gte=1,lte=2000"`
}

type SchedulerClusterScopes struct {
	IDC       string   `yaml:"idc" mapstructure:"idc" json:"idc" binding:"omitempty"`
	Location  string   `yaml:"location" mapstructure:"location" json:"location" binding:"omitempty"`
	CIDRs     []string `yaml:"cidrs" mapstructure:"cidrs" json:"cidrs" binding:"omitempty"`
	Hostnames []string `yaml:"hostnames" mapstructure:"hostnames" json:"hostnames" binding:"omitempty"`
}

type SeedPeerClusterConfig struct {
	LoadLimit uint32 `yaml:"loadLimit" mapstructure:"loadLimit" json:"load_limit" binding:"omitempty,gte=1,lte=50000"`
}
