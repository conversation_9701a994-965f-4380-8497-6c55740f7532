package models

type Downloader interface {
	// DownloadModel 下载模型
	DownloadModel(modelName string, workspace string, commitID string, branch string) (string, error) // Download model from remote repository
}

type DownloadRequest struct {
	ModelName    string `json:"modelName"`
	Workspace    string `json:"workspace"`
	Parallel     int    `json:"parallel,omitempty"`
	Server       string `json:"server,omitempty"`
	DragonflyURL string `json:"dragonflyURL,omitempty"`
}
