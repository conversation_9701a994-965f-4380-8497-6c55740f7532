package loadbalancer

import (
	"context"
	"errors"
	"fmt"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/apis/managed.kcs.io/v1alpha1"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/property"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/klog/v2"

	"git.lianjia.com/cloudnative/kcs/pkg/agent/ctrlmgr/util"
	"git.lianjia.com/cloudnative/kcs/pkg/featuregates"
	"git.lianjia.com/cloudnative/kcs/pkg/known"

	"git.lianjia.com/cloudnative/kcs/pkg/utils/cidr"
	"github.com/go-co-op/gocron"
	corev1 "k8s.io/api/core/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/types"
	toolscache "k8s.io/client-go/tools/cache"
	"k8s.io/client-go/tools/record"
	"k8s.io/client-go/util/retry"
	"net"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/cache"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/log"
	"sigs.k8s.io/controller-runtime/pkg/manager"
	"time"
)

const (
	DefaultServiceReSyncInternal                 = 12 * time.Hour
	LoadBalancerUnbalanceEventReason      string = "LoadBalancerIPUnbalanced"
	LoadBalancerClassName                        = "kcs.io/lb-controller"
	CiliumSvcLBAnnouncementCIDRAnnotation        = "io.cilium/lb-ipam-announcement-cidr"
	CiliumSvcLBIPSAnnotation                     = "io.cilium/lb-ipam-ips"
)

var (
	RackIPV4Mask           = net.IPv4Mask(255, 255, 255, 0)
	ErrIPPoolNotFound      = errors.New("ippool not found")
	ErrIPPoolNotAssignable = errors.New("ippool not assignable")
)

// Controller endpoint导入控制器
type Controller struct {
	client        client.Client
	eventRecorder record.EventRecorder
	scheme        *runtime.Scheme
}

func SetupWithManager(ctx context.Context, manager manager.Manager, properties property.EnvironmentProperty) error {
	if featuregates.FeatureGates.Enabled(featuregates.VirtualIPAddress) {
		controller := NewController(manager.GetClient(), manager.GetEventRecorderFor("LoadBalancerIPAMIPPool-Controller"), manager.GetScheme())
		serviceInformer, err := manager.GetCache().GetInformer(ctx, &corev1.Service{})
		if err != nil {
			klog.Errorf("setup virtual-ipaddress ctrlmgr failed, error:%v ", err)
			return err
		}
		scheduler := gocron.NewScheduler(time.UTC)
		initServiceInformer(ctx, serviceInformer, manager.GetClient(), scheduler, manager.GetEventRecorderFor("load-balancer-ip-checker"))
		scheduler.StartAsync()
		return ctrl.NewControllerManagedBy(manager).For(&v1alpha1.LoadBalancerIPAMIPPool{}).
			Complete(controller)
	}
	return nil
}

func initServiceInformer(ctx context.Context, serviceInformer cache.Informer, k8sClient client.Client, scheduler *gocron.Scheduler, eventRecorder record.EventRecorder) {
	_, err := serviceInformer.AddEventHandler(toolscache.ResourceEventHandlerFuncs{
		AddFunc: func(obj interface{}) {
			srv := obj.(*corev1.Service)
			//表示未被Service控制器创建LoadBalancerIP
			if (srv.Spec.Type == corev1.ServiceTypeLoadBalancer && *srv.Spec.LoadBalancerClass == "") || (srv.Spec.Type == corev1.ServiceTypeLoadBalancer && *srv.Spec.LoadBalancerClass == LoadBalancerClassName) {
				if srv.Spec.LoadBalancerIP == "" || srv.Annotations[CiliumSvcLBAnnouncementCIDRAnnotation] == "" {
					ippoolList := &v1alpha1.LoadBalancerIPAMIPPoolList{}
					err := k8sClient.List(ctx, ippoolList, &client.ListOptions{})
					if err != nil {
						klog.Errorf("get ippool failed: %v", err)
						return
					}
					for _, ippool := range ippoolList.Items {
						if ippool.Status.State == v1alpha1.LoadBalancerStateAllocatable {
							err := allocateCIDR(ctx, k8sClient, &ippool, srv)
							if err != nil {
								klog.Errorf("allocate LoadBalancerIP failed, error:%v", err)
								return
							}
						}
					}
				}
				_, err := scheduler.Every("30m").Tag(fmt.Sprintf("%s-%s", srv.Namespace, srv.Name)).Do(func() {
					newService := corev1.Service{}
					err := k8sClient.Get(ctx, types.NamespacedName{Name: srv.Name, Namespace: srv.Namespace}, &newService)
					if err != nil {
						klog.Errorf("get service:%s failed, namespace:%s, error:%v", srv.Name, srv.Namespace, err)
						return
					}
					podList := &corev1.PodList{}
					var nodeIPs []net.IP
					if newService.Spec.Selector != nil {
						err = k8sClient.List(ctx, podList, &client.ListOptions{
							LabelSelector: labels.SelectorFromSet(newService.Spec.Selector),
							Namespace:     newService.Namespace,
						})
						if err != nil {
							klog.Errorf("get service:%s failed, namespace:%s, error:%v", srv.Name, srv.Namespace, err)
							return
						}
						for _, pod := range podList.Items {
							if pod.Status.HostIP != "" {
								nodeIPs = append(nodeIPs, net.ParseIP(pod.Status.HostIP))
							}
						}
						result := cidr.IsIPAddressesInSameSubNetting(nodeIPs, RackIPV4Mask)
						if !result {
							eventRecorder.Eventf(&newService, corev1.EventTypeWarning, LoadBalancerUnbalanceEventReason, "pods of the service[%s:%s] not in the same rack, the flow may be uneven ", newService.Namespace, newService.Name)
							return
						}
					}

				})
				if err != nil {
					klog.Errorf("add service rack check task failed")
				}
			}

		},
		UpdateFunc: func(oldObj, newObj interface{}) {
			//更新后不做任何操作
		},
		DeleteFunc: func(obj interface{}) {
			srv := obj.(*corev1.Service)
			if srv.Spec.Type == corev1.ServiceTypeLoadBalancer {
				if _, ok := srv.Labels[known.LoadBalancer_Manager_By]; ok {
					err := scheduler.RemoveByTag(fmt.Sprintf("%s-%s", srv.Namespace, srv.Name))
					if err != nil {
						klog.Errorf("remove service rack check task failed, error: %v", err)
						return
					}
					if ippoolName, ok := srv.Labels[known.LoadBalancer_IPPOOL_NAME]; ok {
						err = retry.RetryOnConflict(retry.DefaultRetry, func() error {
							ippool := &v1alpha1.LoadBalancerIPAMIPPool{}
							err = k8sClient.Get(ctx, client.ObjectKey{Name: ippoolName}, ippool)
							if err != nil {
								if apierrors.IsNotFound(err) {
									return nil
								}
								klog.Errorf("get ippool %s failed: %v", ippoolName, err)
								return err
							}
							newAllocated := v1alpha1.NewAllocated()
							for _, item := range ippool.Status.Allocated {
								if item.Ref.ServiceName == srv.Name && item.Ref.ServiceNamespace == srv.Namespace {
									continue
								}
								newAllocated.Upsert(item)
							}
							ippool.Status.Allocated = newAllocated
							return k8sClient.Status().Update(ctx, ippool)
						})
					}
				}
			}
		},
	})
	if err != nil {
		klog.Errorf("add service event handler failed: %v", err)
		return
	}
}

func NewController(c client.Client, recorder record.EventRecorder, scheme *runtime.Scheme) *Controller {
	return &Controller{
		client:        c,
		eventRecorder: recorder,
		scheme:        scheme,
	}
}

func (v *Controller) Reconcile(ctx context.Context, request ctrl.Request) (ctrl.Result, error) {
	clog := log.FromContext(ctx, "ctrlmgr", "load-balancer-ipam-ippool-ctrlmgr")
	loadBalancerIpamIPPool := &v1alpha1.LoadBalancerIPAMIPPool{}
	err := v.client.Get(ctx, request.NamespacedName, loadBalancerIpamIPPool)
	if err != nil {
		if apierrors.IsNotFound(err) {
			clog.Info("not found endpointImport:" + request.NamespacedName.String())
			return ctrl.Result{}, nil
		}
		return ctrl.Result{}, err
	}
	if !loadBalancerIpamIPPool.DeletionTimestamp.IsZero() {
		//endpointImport被删除,此时需要进行数据清理
		if err := v.handleFinalizer(ctx, loadBalancerIpamIPPool); err != nil {
			return ctrl.Result{}, fmt.Errorf("error when handling finalizer: %v", err)
		}
		return ctrl.Result{}, nil
	}

	if !loadBalancerIpamIPPool.HasFinalizer(known.AgentFinalizer) {
		if err := v.addFinalizer(ctx, loadBalancerIpamIPPool); err != nil {
			return ctrl.Result{}, fmt.Errorf("error when adding finalizer: %v", err)
		}
	}

	var state v1alpha1.LoadBalancerIPAMIPPoolState
	//当手工指定暂停或者可分配的子网为空的时候，状态更新为不可分配状态
	if loadBalancerIpamIPPool.Spec.Pause && loadBalancerIpamIPPool.Status.State != v1alpha1.LoadBalancerStatePause {
		state = v1alpha1.LoadBalancerStatePause
	} else if !loadBalancerIpamIPPool.Spec.Pause && loadBalancerIpamIPPool.Status.State == v1alpha1.LoadBalancerStatePause {
		if loadBalancerIpamIPPool.Status.ResidueAllocatableNumber == 0 {
			state = v1alpha1.LoadBalancerStateUNAllocated
		} else {
			state = v1alpha1.LoadBalancerStateAllocatable
		}
	} else {
		state = v1alpha1.LoadBalancerStateAllocatable
	}
	err = retry.RetryOnConflict(retry.DefaultRetry, func() error {
		err = v.client.Get(ctx, types.NamespacedName{Name: loadBalancerIpamIPPool.Name}, loadBalancerIpamIPPool)
		if err != nil {
			return err
		}
		loadBalancerIpamIPPool.Status.State = state
		err = v.client.Status().Update(ctx, loadBalancerIpamIPPool)
		if err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		klog.Errorf("update virtualNetworkCIDE failed, error:%v", err)
		return ctrl.Result{Requeue: true, RequeueAfter: 10 * time.Second}, nil
	}
	return ctrl.Result{}, nil
}

func allocateCIDR(ctx context.Context, k8sClient client.Client, loadBalancerIPAMIPPool *v1alpha1.LoadBalancerIPAMIPPool, srv *corev1.Service) error {
	return retry.RetryOnConflict(retry.DefaultRetry, func() error {
		ippool := &v1alpha1.LoadBalancerIPAMIPPool{}
		err := k8sClient.Get(ctx, client.ObjectKey{Name: loadBalancerIPAMIPPool.Name}, ippool)
		if err != nil {
			if apierrors.IsNotFound(err) {
				return ErrIPPoolNotFound
			}
		}
		if !ippool.DeletionTimestamp.IsZero() {
			return ErrIPPoolNotFound
		}
		c := cidr.ParseNoError(loadBalancerIPAMIPPool.Spec.CIDR)
		subCidrList, err := c.SubNetting(cidr.MethodHostNum, loadBalancerIPAMIPPool.Spec.IPSegmentSize)
		if err != nil {
			return err
		}
		residueCIDRNum := len(subCidrList) - len(loadBalancerIPAMIPPool.Spec.Excludes) - loadBalancerIPAMIPPool.Status.Allocated.Len()
		if residueCIDRNum <= 0 {
			return ErrIPPoolNotAssignable
		}
		var allocatedCidrList []*cidr.CIDR
		for _, item := range loadBalancerIPAMIPPool.Status.Allocated {
			allocatedCidrList = append(allocatedCidrList, cidr.ParseNoError(item.CIDR))
		}
		for _, item := range loadBalancerIPAMIPPool.Spec.Excludes {
			allocatedCidrList = append(allocatedCidrList, cidr.ParseNoError(item))
		}
		residueCIDRList := util.SliceGetSubtractionCIDR(subCidrList, allocatedCidrList)
		cidr.SortCIDRAsc(residueCIDRList)
		if len(residueCIDRList) > 0 {
			allocatedCidr := residueCIDRList[0]
			ippool.Status.Allocated.Upsert(v1alpha1.Allocation{
				LoadBalancerIP: allocatedCidr.CIDR().IP.To4().String(),
				Ref: v1alpha1.AllocationRef{
					ServiceName:      srv.Name,
					ServiceNamespace: srv.Namespace,
				},
				CIDR: allocatedCidr.CIDR().String(),
			})
			if len(residueCIDRList) == 1 {
				ippool.Status.State = v1alpha1.LoadBalancerStateUNAllocated //没有空余资源了，标记ippool为不可分配状态
				ippool.Status.ResidueAllocatableNumber = 0
				ippool.Status.Message = fmt.Sprintf("not assignable cidr")
			}
			ippool.Status.State = v1alpha1.LoadBalancerStateAllocatable
			ippool.Status.ResidueAllocatableNumber = len(residueCIDRList) - 1
			err = k8sClient.Status().Update(ctx, ippool)
			if err != nil {
				return err
			}
			srvCopy := srv.DeepCopy()
			if srvCopy.Labels == nil {
				srvCopy.Labels = make(map[string]string)
			}
			srvCopy.Labels[known.LoadBalancer_Manager_By] = "kcs"
			srvCopy.Labels[known.LoadBalancer_IPPOOL_NAME] = loadBalancerIPAMIPPool.Name
			if srvCopy.Annotations == nil {
				srvCopy.Annotations = make(map[string]string)
			}
			srvCopy.Annotations[known.LoadBalancer_ALLOCATED_CIDR] = allocatedCidr.CIDR().String()
			if srvCopy.Spec.ExternalTrafficPolicy == "" && srvCopy.Spec.Selector != nil {
				srvCopy.Spec.ExternalTrafficPolicy = corev1.ServiceExternalTrafficPolicyTypeLocal
			}
			srvCopy.Spec.LoadBalancerIP = allocatedCidr.CIDR().IP.To4().String()
			srvCopy.Status.LoadBalancer.Ingress = []corev1.LoadBalancerIngress{{IP: allocatedCidr.CIDR().IP.To4().String()}}
			srvCopy.Status.Conditions = append(srvCopy.Status.Conditions, metav1.Condition{
				Type:               "LoadBalancerIPAllocated",
				Message:            "allocate loadbalancer ip success",
				LastTransitionTime: metav1.Now(),
				Status:             metav1.ConditionTrue,
			})
			//兼容Cilium1.14版本的服务注解
			srvCopy.Annotations[CiliumSvcLBAnnouncementCIDRAnnotation] = allocatedCidr.CIDR().String()
			return k8sClient.Update(ctx, srvCopy)
		} else {
			srvCopy := srv.DeepCopy()
			srvCopy.Status.Conditions = append(srvCopy.Status.Conditions, metav1.Condition{
				Type:               "LoadBalancerIPAllocated",
				Message:            "allocate loadbalancer ip failed, no assignable ip",
				LastTransitionTime: metav1.Now(),
				Status:             metav1.ConditionFalse,
			})
			return k8sClient.Status().Update(ctx, srvCopy)
		}
	})
}

func (v *Controller) addFinalizer(ctx context.Context, loadBalancerIpamIPPool *v1alpha1.LoadBalancerIPAMIPPool) error {
	loadBalancerIpamIPPool.AddFinalizer(known.AgentFinalizer)
	return v.client.Update(ctx, loadBalancerIpamIPPool)
}

func (v *Controller) handleFinalizer(ctx context.Context, virtualNetworkCIDR *v1alpha1.LoadBalancerIPAMIPPool) error {
	//todo 对于CIDR的删除不做任何处理，只标记状态为删除状态,需要手工接入做真实的资源释放，此处一定要注意检查被分配的IP
	return nil
}
