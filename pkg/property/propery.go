package property

import (
	"errors"
	"fmt"
	"log"
	"time"

	"github.com/fsnotify/fsnotify"

	"github.com/spf13/viper"
)

var staticViper EnvironmentProperty

func GetEnvironmentProperty(configFile string) (EnvironmentProperty, error) {
	if staticViper == nil {
		var err error
		staticViper, err = NewEnvironmentPropertyFromConfigFile(configFile)
		if err != nil {
			return nil, fmt.Errorf("parser config file failed:%s", err.Error())
		}
	}
	return staticViper, nil
}

type EnvironmentProperty interface {
	IsSet(key string) bool
	AllSettings() map[string]interface{}
	GetStringMap(key string) map[string]interface{}
	GetStringSlice(key string) []string
	GetString(key string) (string, error)
	GetDefault(key, defaultValue string) string
	GetDuration(key string) (time.Duration, error)
	GetDurationDefault(key string, defaultValue time.Duration) time.Duration
	GetInt(key string) (int, error)
	GetIntDefault(key string, defaultValue int) int
	GetInt32(key string) (int32, error)
	GetInt32Default(key string, defaultValue int32) int32
	GetInt64(key string) (int64, error)
	GetInt64Default(key string, defaultValue int64) int64
	GetBool(key string) (bool, error)
	GetBoolDefault(key string, defaultValue bool) bool
	GetFloat64(key string) (float64, error)
	GetFloat64Default(key string, defaultValue float64) float64
	MustGetString(key string) string
	MustGetDuration(key string) time.Duration
	MustGetInt(key string) int
	MustGetInt32(key string) int32
	MustGetInt64(key string) int64
	MustGetFloat64(key string) float64
	MustGetBool(key string) bool
	Sub(key string) EnvironmentProperty
	UnmarshalKey(key string, rawVal interface{}) error
	GetDefaultDuration(key string, defaultValue time.Duration) time.Duration
}

type viperEnvironmentProperty struct {
	viper *viper.Viper
}

func (v *viperEnvironmentProperty) GetDefaultDuration(key string, defaultValue time.Duration) time.Duration {
	if v.IsSet(key) {
		duration, err := v.GetDuration(key)
		if err != nil {
			log.Fatalf("MustGetDuration failed, values not found, key: %s ", key)
		}
		return duration
	}
	return defaultValue
}

func (v *viperEnvironmentProperty) GetDuration(key string) (time.Duration, error) {
	if v.IsSet(key) {
		return v.viper.GetDuration(key), nil
	}
	return 0, errors.New("not found config, key:" + key)
}

func (v *viperEnvironmentProperty) GetDurationDefault(key string, defaultValue time.Duration) time.Duration {
	if v.IsSet(key) {
		return v.viper.GetDuration(key)
	}
	return defaultValue
}

func (v *viperEnvironmentProperty) MustGetDuration(key string) time.Duration {
	value, err := v.GetDuration(key)
	if err != nil {
		log.Fatalf("MustGetDuration failed, values not found, key: %s ", key)
	}
	return value
}

func (v *viperEnvironmentProperty) GetInt32Default(key string, defaultValue int32) int32 {
	if v.IsSet(key) {
		return v.MustGetInt32(key)
	}
	return defaultValue
}

func (v *viperEnvironmentProperty) GetInt64Default(key string, defaultValue int64) int64 {
	if v.IsSet(key) {
		return v.MustGetInt64(key)
	}
	return defaultValue
}

func (v *viperEnvironmentProperty) GetBoolDefault(key string, defaultValue bool) bool {
	if v.IsSet(key) {
		return v.MustGetBool(key)
	}
	return defaultValue
}

// IsSet 是否存在配置
func (v *viperEnvironmentProperty) IsSet(key string) bool {
	return v.viper.IsSet(key)
}

func (v *viperEnvironmentProperty) AllSettings() map[string]interface{} {
	return v.viper.AllSettings()
}

func (v *viperEnvironmentProperty) GetStringMap(key string) map[string]interface{} {
	return v.viper.GetStringMap(key)
}

func (v *viperEnvironmentProperty) GetStringSlice(key string) []string {
	return v.viper.GetStringSlice(key)
}

func (v *viperEnvironmentProperty) GetString(key string) (string, error) {
	if v.IsSet(key) {
		return v.viper.GetString(key), nil
	}
	return "", errors.New("not found config, key:" + key)
}

func (v *viperEnvironmentProperty) MustGetString(key string) string {
	value, err := v.GetString(key)
	if err != nil {
		log.Fatalf("MustGetString failed, values not found,key: %s ", key)
	}
	return value
}

func (v *viperEnvironmentProperty) GetDefault(key, defaultValue string) string {
	if v.IsSet(key) {
		return v.viper.GetString(key)
	}
	return defaultValue
}

func (v *viperEnvironmentProperty) GetIntDefault(key string, defaultValue int) int {
	if v.IsSet(key) {
		return v.viper.GetInt(key)
	}
	return defaultValue
}

func (v *viperEnvironmentProperty) GetInt(key string) (int, error) {
	if v.IsSet(key) {
		return v.viper.GetInt(key), nil
	}
	return 0, errors.New("not found config, key:" + key)
}

func (v *viperEnvironmentProperty) MustGetInt(key string) int {
	value, err := v.GetInt(key)
	if err != nil {
		log.Fatalf("MustGetInt failed, values not found, key: %s ", key)
	}
	return value
}

func (v *viperEnvironmentProperty) GetInt32(key string) (int32, error) {
	if v.IsSet(key) {
		return v.viper.GetInt32(key), nil
	}
	return 0, errors.New("not found config, key:" + key)
}

func (v *viperEnvironmentProperty) MustGetInt32(key string) int32 {
	value, err := v.GetInt32(key)
	if err != nil {
		log.Fatalf("MustGetInt32 failed, values not found, key: %s ", key)
	}
	return value
}

func (v *viperEnvironmentProperty) GetInt64(key string) (int64, error) {
	if v.IsSet(key) {
		return v.viper.GetInt64(key), nil
	}
	return 0, errors.New("not found config, key:" + key)
}

func (v *viperEnvironmentProperty) MustGetInt64(key string) int64 {
	value, err := v.GetInt64(key)
	if err != nil {
		log.Fatalf("MustGetInt64 failed, values not found, key: %s ", key)
	}
	return value
}

func (v *viperEnvironmentProperty) GetFloat64(key string) (float64, error) {
	if v.IsSet(key) {
		return v.viper.GetFloat64(key), nil
	}
	return 0, errors.New("not found config, key:" + key)
}

func (v *viperEnvironmentProperty) GetFloat64Default(key string, defaultValue float64) float64 {
	if v.IsSet(key) {
		return v.viper.GetFloat64(key)
	}
	return defaultValue
}

func (v *viperEnvironmentProperty) MustGetFloat64(key string) float64 {
	value, err := v.GetFloat64(key)
	if err != nil {
		log.Fatalf("MustGetFloat64 failed, values not found, key: %s ", key)
	}
	return value
}

func (v *viperEnvironmentProperty) GetBool(key string) (bool, error) {
	if v.IsSet(key) {
		return v.viper.GetBool(key), nil
	}
	return false, errors.New("not found config, key:" + key)
}

func (v *viperEnvironmentProperty) MustGetBool(key string) bool {
	value, err := v.GetBool(key)
	if err != nil {
		log.Fatalf("MustGetBool failed, values not found, key: %s ", key)
	}
	return value
}

func (v *viperEnvironmentProperty) UnmarshalKey(key string, obj interface{}) error {
	return v.viper.UnmarshalKey(key, &obj)
}

func (v *viperEnvironmentProperty) Sub(key string) EnvironmentProperty {
	return &viperEnvironmentProperty{
		viper: v.viper.Sub(key),
	}
}

type ApolloConfig struct {
	AppID       string `json:"appId"`
	Namespace   string `json:"namespace"`
	Endpoint    string `json:"endpoint"`
	Cluster     string `json:"cluster"`
	Environment string `json:"environment"`
	ConfigType  string `json:"configType"`
}

func NewEnvironmentPropertyFromConfigFile(configFile string) (EnvironmentProperty, error) {
	viperConfig := viper.New()
	viperConfig.SetConfigFile(configFile)
	err := viperConfig.ReadInConfig()
	if err != nil {
		return nil, fmt.Errorf("load config failed: %v\n", err)
	}
	viper.OnConfigChange(func(e fsnotify.Event) {
		if e.Op == fsnotify.Write {
			err = viperConfig.ReadInConfig()
			if err != nil {
				fmt.Printf("[ERROR]load config failed: %v\n", err)
				return
			}
		}
	})
	go viper.WatchConfig()
	return &viperEnvironmentProperty{
		viper: viperConfig,
	}, nil
}

func Default() EnvironmentProperty {
	viperConfig := viper.New()
	return &viperEnvironmentProperty{viper: viperConfig}
}
