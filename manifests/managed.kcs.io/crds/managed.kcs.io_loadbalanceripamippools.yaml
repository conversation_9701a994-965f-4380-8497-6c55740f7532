---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.11.4
  name: loadbalanceripamippools.managed.kcs.io
spec:
  group: managed.kcs.io
  names:
    kind: LoadBalancerIPAMIPPool
    listKind: LoadBalancerIPAMIPPoolList
    plural: loadbalanceripamippools
    singular: loadbalanceripamippool
  scope: Cluster
  versions:
  - name: v1alpha1
    schema:
      openAPIV3Schema:
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          spec:
            properties:
              cidr:
                type: string
              excludes:
                items:
                  type: string
                type: array
              ipSegmentSize:
                type: integer
              pause:
                type: boolean
            required:
            - cidr
            type: object
          status:
            properties:
              allocated:
                items:
                  properties:
                    cidr:
                      type: string
                    loadBalancerIP:
                      type: string
                    ref:
                      properties:
                        serviceName:
                          type: string
                        serviceNamespace:
                          type: string
                      required:
                      - serviceName
                      - serviceNamespace
                      type: object
                  required:
                  - cidr
                  - loadBalancerIP
                  - ref
                  type: object
                type: array
              message:
                type: string
              residueAllocatableNumber:
                type: integer
              state:
                type: string
            required:
            - state
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
