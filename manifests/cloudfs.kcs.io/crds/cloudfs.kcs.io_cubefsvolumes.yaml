---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.11.4
  name: cubefsvolumes.cloudfs.kcs.io
spec:
  group: cloudfs.kcs.io
  names:
    kind: CubeFSVolume
    listKind: CubeFSVolumeList
    plural: cubefsvolumes
    singular: cubefsvolume
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - description: Cluster Of CubeFS
      jsonPath: .status.cluster
      name: Cluster
      type: string
    - description: ID Of CubeFS Volume
      jsonPath: .status.volumeID
      name: VolumeID
      type: string
    - description: Capacity Of CubeFS Volume
      jsonPath: .status.capacity
      name: Capacity
      type: string
    - description: State Of CubeFS Volume
      jsonPath: .status.state
      name: State
      type: string
    - description: Creator Of CubeFS Volume
      jsonPath: .spec.creator
      name: Creator
      type: string
    - description: Region Of CubeFS Volume
      jsonPath: .spec.region
      name: Region
      type: string
    - description: Zone Of CubeFS Volume
      jsonPath: .spec.zone
      name: Zone
      type: string
    - jsonPath: .metadata.creationTimestamp
      name: Age
      type: date
    name: v1alpha1
    schema:
      openAPIV3Schema:
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          spec:
            properties:
              capacity:
                format: int64
                type: integer
              creator:
                type: string
              description:
                type: string
              displayName:
                type: string
              managers:
                items:
                  type: string
                type: array
              members:
                items:
                  type: string
                type: array
              region:
                type: string
              specification:
                type: string
              volumeBinding:
                properties:
                  accessKey:
                    type: string
                  cluster:
                    type: string
                  enabled:
                    type: boolean
                  owner:
                    type: string
                  secretKey:
                    type: string
                  volume_id:
                    type: string
                required:
                - accessKey
                - cluster
                - enabled
                - owner
                - secretKey
                - volume_id
                type: object
              zone:
                type: string
            type: object
          status:
            properties:
              accessKey:
                type: string
              capacity:
                format: int64
                type: integer
              cluster:
                type: string
              conditions:
                items:
                  description: "Condition contains details for one aspect of the current
                    state of this API Resource. --- This struct is intended for direct
                    use as an array at the field path .status.conditions.  For example,
                    \n type FooStatus struct{ // Represents the observations of a
                    foo's current state. // Known .status.conditions.type are: \"Available\",
                    \"Progressing\", and \"Degraded\" // +patchMergeKey=type // +patchStrategy=merge
                    // +listType=map // +listMapKey=type Conditions []metav1.Condition
                    `json:\"conditions,omitempty\" patchStrategy:\"merge\" patchMergeKey:\"type\"
                    protobuf:\"bytes,1,rep,name=conditions\"` \n // other fields }"
                  properties:
                    lastTransitionTime:
                      description: lastTransitionTime is the last time the condition
                        transitioned from one status to another. This should be when
                        the underlying condition changed.  If that is not known, then
                        using the time when the API field changed is acceptable.
                      format: date-time
                      type: string
                    message:
                      description: message is a human readable message indicating
                        details about the transition. This may be an empty string.
                      maxLength: 32768
                      type: string
                    observedGeneration:
                      description: observedGeneration represents the .metadata.generation
                        that the condition was set based upon. For instance, if .metadata.generation
                        is currently 12, but the .status.conditions[x].observedGeneration
                        is 9, the condition is out of date with respect to the current
                        state of the instance.
                      format: int64
                      minimum: 0
                      type: integer
                    reason:
                      description: reason contains a programmatic identifier indicating
                        the reason for the condition's last transition. Producers
                        of specific condition types may define expected values and
                        meanings for this field, and whether the values are considered
                        a guaranteed API. The value should be a CamelCase string.
                        This field may not be empty.
                      maxLength: 1024
                      minLength: 1
                      pattern: ^[A-Za-z]([A-Za-z0-9_,:]*[A-Za-z0-9_])?$
                      type: string
                    status:
                      description: status of the condition, one of True, False, Unknown.
                      enum:
                      - "True"
                      - "False"
                      - Unknown
                      type: string
                    type:
                      description: type of condition in CamelCase or in foo.example.com/CamelCase.
                        --- Many .condition.type values are consistent across resources
                        like Available, but because arbitrary conditions can be useful
                        (see .node.status.conditions), the ability to deconflict is
                        important. The regex it matches is (dns1123SubdomainFmt/)?(qualifiedNameFmt)
                      maxLength: 316
                      pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                      type: string
                  required:
                  - lastTransitionTime
                  - message
                  - reason
                  - status
                  - type
                  type: object
                type: array
              lastTransitionTime:
                format: date-time
                type: string
              message:
                type: string
              owner:
                type: string
              reason:
                type: string
              secretKey:
                type: string
              state:
                type: string
              syncStatus:
                properties:
                  applicationName:
                    type: string
                  applicationNamespace:
                    type: string
                  lastTransitionTime:
                    format: date-time
                    type: string
                  message:
                    type: string
                  reason:
                    type: string
                  state:
                    type: string
                  syncClusters:
                    items:
                      type: string
                    type: array
                type: object
              volumeID:
                type: string
            required:
            - lastTransitionTime
            - state
            type: object
        required:
        - spec
        type: object
    served: true
    storage: true
    subresources:
      status: {}
