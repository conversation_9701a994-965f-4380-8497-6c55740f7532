apiVersion: gateway.networking.k8s.io/v1alpha2
kind: GRPCRoute
metadata:
  name: inspector-grpc-route
  namespace: default
spec:
  parentRefs:
    - name: my-gateway
      namespace: default
  hostnames:
    - inspector.example.com              # 这个需要 跟HTTP ROUTE一样
  rules:
    - matches:                          # matches是一个数组  method 匹配规则  主要是方法匹配【method】和 headers 头 匹配
        - method:
            service: inspector.InspectorService
            method: SayHello
          headers:
            - name: x-env
              value: prod
            - name: x-user
              value: admin
      filters:                          # 过滤规则 跟 HTTPRoute不一样
        - type: RequestHeaderModifier   # 修改请求头
          requestHeaderModifier:
            add:  # 添加新的 header（即使已存在）
              - name: x-trace-id
                value: "${request_id}"
              - name: x-product
                value: "inspector"
            set:  # 设置 header，如果已存在则覆盖
              - name: x-user
                value: "overridden-admin"
            remove:  # 移除头
              - x-temp
        - type: ResponseHeaderModifier
          responseHeaderModifier:
            add:
              - name: x-resp
                value: ok
            set:
              - name: x-version
                value: v1
            remove:
              - x-remove-this-too
      backendRefs:                 # 跟HTTPRoute一样
        - name: inspector-service
          port: 50051