apiVersion: cloudfs.kcs.io/v1alpha1
kind: CubeFSVolume
metadata:
  name: demo-vol
  namespace: aistudio-local-test
spec:
  capacity: 1
  displayName: 测试卷
  description: 用于测试cubefs的自动化交付
  region: region_bj
  zone: wq
  creator: wangtianqing
  specification: powerful

---

apiVersion: cloudfs.kcs.io/v1alpha1
kind: CubeFSVolume
metadata:
  name: demo-vol-1
  namespace: aistudio-local-test
spec:
  capacity: 1
  displayName: 测试卷
  description: 用于测试cubefs的自动化交付
  region: region_bj
  zone: wq
  creator: wangtianqing
  specification: powerful


---

apiVersion: cloudfs.kcs.io/v1alpha1
kind: CubeFSVolume
metadata:
  name: demo-vol-wn-3
  namespace: aistudio-local-test
spec:
  capacity: 1
  displayName: 测试卷
  description: 用于测试cubefs的自动化交付
  region: region_bj
  zone: wq
  creator: wangtianqing
  specification: powerful
  volumeBinding:
    cluster: cubefs-wq-prod-01
    enabled: true
    volume_id: aistudio-local-test-demo-vol-wn
    owner: "v66tsdc819l"
    accessKey: RLLvtQZJibiLmrme
    secretKey: uu
