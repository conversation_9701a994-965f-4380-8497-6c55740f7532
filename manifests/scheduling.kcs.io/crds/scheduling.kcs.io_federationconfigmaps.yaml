---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.14.0
  name: federationconfigmaps.scheduling.kcs.io
spec:
  group: scheduling.kcs.io
  names:
    kind: FederationConfigMap
    listKind: FederationConfigMapList
    plural: federationconfigmaps
    shortNames:
    - fdcm
    singular: federationconfigmap
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - jsonPath: .metadata.creationTimestamp
      name: Age
      type: date
    - jsonPath: .metadata.namespace
      name: Namespace
      type: string
    - jsonPath: .status.state
      name: State
      type: string
    name: v1alpha1
    schema:
      openAPIV3Schema:
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            properties:
              binaryData:
                additionalProperties:
                  format: byte
                  type: string
                type: object
              data:
                additionalProperties:
                  type: string
                type: object
              workspaceName:
                type: string
            type: object
          status:
            properties:
              lastTransitionTime:
                format: date-time
                type: string
              message:
                type: string
              reason:
                type: string
              state:
                type: string
              syncedClusters:
                type: integer
              totalClusters:
                type: integer
            required:
            - state
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
