---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.14.0
  name: nodegrades.scheduling.kcs.io
spec:
  group: scheduling.kcs.io
  names:
    kind: NodeGrade
    listKind: NodeGradeList
    plural: nodegrades
    singular: nodegrade
  scope: Cluster
  versions:
  - additionalPrinterColumns:
    - description: displayName
      jsonPath: .spec.displayName
      name: DisplayName
      type: string
    name: v1alpha1
    schema:
      openAPIV3Schema:
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            properties:
              description:
                type: string
              displayName:
                type: string
              idc:
                type: string
              region:
                type: string
              usages:
                items:
                  type: string
                type: array
              zone:
                type: string
            required:
            - region
            - zone
            type: object
        type: object
    served: true
    storage: true
    subresources: {}
