---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.14.0
  name: federationconfigmaptrackers.scheduling.kcs.io
spec:
  group: scheduling.kcs.io
  names:
    kind: FederationConfigMapTracker
    listKind: FederationConfigMapTrackerList
    plural: federationconfigmaptrackers
    shortNames:
    - fdcmt
    singular: federationconfigmaptracker
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - jsonPath: .spec.targetWorkspaceName
      name: TargetWorkspaceName
      type: string
    - jsonPath: .spec.targetCluster
      name: TargetCluster
      type: string
    - jsonPath: .spec.configMapName
      name: ConfigMapName
      type: string
    - jsonPath: .status.reason
      name: Reason
      type: string
    name: v1alpha1
    schema:
      openAPIV3Schema:
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            properties:
              binaryData:
                additionalProperties:
                  format: byte
                  type: string
                type: object
              configMapName:
                type: string
              data:
                additionalProperties:
                  type: string
                type: object
              targetCluster:
                type: string
              targetNamespace:
                type: string
              targetWorkspaceName:
                type: string
            type: object
          status:
            properties:
              lastTransitionTime:
                format: date-time
                type: string
              message:
                type: string
              reason:
                type: string
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
