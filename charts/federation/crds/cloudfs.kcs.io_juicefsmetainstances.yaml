---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.11.4
  name: juicefsmetainstances.cloudfs.kcs.io
spec:
  group: cloudfs.kcs.io
  names:
    kind: JuiceFSMetaInstance
    listKind: JuiceFSMetaInstanceList
    plural: juicefsmetainstances
    singular: juicefsmetainstance
  scope: Namespaced
  versions:
  - name: v1alpha1
    schema:
      openAPIV3Schema:
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          spec:
            properties:
              engineType:
                type: string
              mysql:
                properties:
                  jdbcUrl:
                    type: string
                required:
                - jdbcUrl
                type: object
              oceanBase:
                properties:
                  obProxyHost:
                    type: string
                  obProxyPort:
                    type: integer
                  password:
                    type: string
                  user:
                    type: string
                required:
                - obProxyHost
                - obProxyPort
                - password
                - user
                type: object
              region:
                type: string
              tikv:
                properties:
                  pdServers:
                    items:
                      type: string
                    type: array
                required:
                - pdServers
                type: object
              zones:
                items:
                  type: string
                type: array
            required:
            - engineType
            type: object
          status:
            properties:
              metaUrl:
                type: string
              state:
                type: string
            type: object
        required:
        - spec
        type: object
    served: true
    storage: true
    subresources:
      status: {}
