//go:build !ignore_autogenerated
// +build !ignore_autogenerated

// Code generated by conversion-gen. DO NOT EDIT.

package v1beta1

import (
	url "net/url"
	unsafe "unsafe"

	clusterpedia "github.com/clusterpedia-io/api/clusterpedia"
	conversion "k8s.io/apimachinery/pkg/conversion"
	runtime "k8s.io/apimachinery/pkg/runtime"
)

func init() {
	localSchemeBuilder.Register(RegisterConversions)
}

// RegisterConversions adds conversion functions to the given scheme.
// Public to allow building arbitrary schemes.
func RegisterConversions(s *runtime.Scheme) error {
	if err := s.AddGeneratedConversionFunc((*CollectionResource)(nil), (*clusterpedia.CollectionResource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1beta1_CollectionResource_To_clusterpedia_CollectionResource(a.(*CollectionResource), b.(*clusterpedia.CollectionResource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*clusterpedia.CollectionResource)(nil), (*CollectionResource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_clusterpedia_CollectionResource_To_v1beta1_CollectionResource(a.(*clusterpedia.CollectionResource), b.(*CollectionResource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*CollectionResourceList)(nil), (*clusterpedia.CollectionResourceList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1beta1_CollectionResourceList_To_clusterpedia_CollectionResourceList(a.(*CollectionResourceList), b.(*clusterpedia.CollectionResourceList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*clusterpedia.CollectionResourceList)(nil), (*CollectionResourceList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_clusterpedia_CollectionResourceList_To_v1beta1_CollectionResourceList(a.(*clusterpedia.CollectionResourceList), b.(*CollectionResourceList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*CollectionResourceType)(nil), (*clusterpedia.CollectionResourceType)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1beta1_CollectionResourceType_To_clusterpedia_CollectionResourceType(a.(*CollectionResourceType), b.(*clusterpedia.CollectionResourceType), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*clusterpedia.CollectionResourceType)(nil), (*CollectionResourceType)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_clusterpedia_CollectionResourceType_To_v1beta1_CollectionResourceType(a.(*clusterpedia.CollectionResourceType), b.(*CollectionResourceType), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*url.Values)(nil), (*ListOptions)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_url_Values_To_v1beta1_ListOptions(a.(*url.Values), b.(*ListOptions), scope)
	}); err != nil {
		return err
	}
	if err := s.AddConversionFunc((*clusterpedia.ListOptions)(nil), (*ListOptions)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_clusterpedia_ListOptions_To_v1beta1_ListOptions(a.(*clusterpedia.ListOptions), b.(*ListOptions), scope)
	}); err != nil {
		return err
	}
	if err := s.AddConversionFunc((*url.Values)(nil), (*ListOptions)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_url_Values_To_v1beta1_ListOptions(a.(*url.Values), b.(*ListOptions), scope)
	}); err != nil {
		return err
	}
	if err := s.AddConversionFunc((*ListOptions)(nil), (*clusterpedia.ListOptions)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1beta1_ListOptions_To_clusterpedia_ListOptions(a.(*ListOptions), b.(*clusterpedia.ListOptions), scope)
	}); err != nil {
		return err
	}
	return nil
}

func autoConvert_v1beta1_CollectionResource_To_clusterpedia_CollectionResource(in *CollectionResource, out *clusterpedia.CollectionResource, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	out.ResourceTypes = *(*[]clusterpedia.CollectionResourceType)(unsafe.Pointer(&in.ResourceTypes))
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]runtime.Object, len(*in))
		for i := range *in {
			if err := runtime.Convert_runtime_RawExtension_To_runtime_Object(&(*in)[i], &(*out)[i], s); err != nil {
				return err
			}
		}
	} else {
		out.Items = nil
	}
	out.Continue = in.Continue
	out.RemainingItemCount = (*int64)(unsafe.Pointer(in.RemainingItemCount))
	return nil
}

// Convert_v1beta1_CollectionResource_To_clusterpedia_CollectionResource is an autogenerated conversion function.
func Convert_v1beta1_CollectionResource_To_clusterpedia_CollectionResource(in *CollectionResource, out *clusterpedia.CollectionResource, s conversion.Scope) error {
	return autoConvert_v1beta1_CollectionResource_To_clusterpedia_CollectionResource(in, out, s)
}

func autoConvert_clusterpedia_CollectionResource_To_v1beta1_CollectionResource(in *clusterpedia.CollectionResource, out *CollectionResource, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	out.ResourceTypes = *(*[]CollectionResourceType)(unsafe.Pointer(&in.ResourceTypes))
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]runtime.RawExtension, len(*in))
		for i := range *in {
			if err := runtime.Convert_runtime_Object_To_runtime_RawExtension(&(*in)[i], &(*out)[i], s); err != nil {
				return err
			}
		}
	} else {
		out.Items = nil
	}
	out.Continue = in.Continue
	out.RemainingItemCount = (*int64)(unsafe.Pointer(in.RemainingItemCount))
	return nil
}

// Convert_clusterpedia_CollectionResource_To_v1beta1_CollectionResource is an autogenerated conversion function.
func Convert_clusterpedia_CollectionResource_To_v1beta1_CollectionResource(in *clusterpedia.CollectionResource, out *CollectionResource, s conversion.Scope) error {
	return autoConvert_clusterpedia_CollectionResource_To_v1beta1_CollectionResource(in, out, s)
}

func autoConvert_v1beta1_CollectionResourceList_To_clusterpedia_CollectionResourceList(in *CollectionResourceList, out *clusterpedia.CollectionResourceList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]clusterpedia.CollectionResource, len(*in))
		for i := range *in {
			if err := Convert_v1beta1_CollectionResource_To_clusterpedia_CollectionResource(&(*in)[i], &(*out)[i], s); err != nil {
				return err
			}
		}
	} else {
		out.Items = nil
	}
	return nil
}

// Convert_v1beta1_CollectionResourceList_To_clusterpedia_CollectionResourceList is an autogenerated conversion function.
func Convert_v1beta1_CollectionResourceList_To_clusterpedia_CollectionResourceList(in *CollectionResourceList, out *clusterpedia.CollectionResourceList, s conversion.Scope) error {
	return autoConvert_v1beta1_CollectionResourceList_To_clusterpedia_CollectionResourceList(in, out, s)
}

func autoConvert_clusterpedia_CollectionResourceList_To_v1beta1_CollectionResourceList(in *clusterpedia.CollectionResourceList, out *CollectionResourceList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]CollectionResource, len(*in))
		for i := range *in {
			if err := Convert_clusterpedia_CollectionResource_To_v1beta1_CollectionResource(&(*in)[i], &(*out)[i], s); err != nil {
				return err
			}
		}
	} else {
		out.Items = nil
	}
	return nil
}

// Convert_clusterpedia_CollectionResourceList_To_v1beta1_CollectionResourceList is an autogenerated conversion function.
func Convert_clusterpedia_CollectionResourceList_To_v1beta1_CollectionResourceList(in *clusterpedia.CollectionResourceList, out *CollectionResourceList, s conversion.Scope) error {
	return autoConvert_clusterpedia_CollectionResourceList_To_v1beta1_CollectionResourceList(in, out, s)
}

func autoConvert_v1beta1_CollectionResourceType_To_clusterpedia_CollectionResourceType(in *CollectionResourceType, out *clusterpedia.CollectionResourceType, s conversion.Scope) error {
	out.Group = in.Group
	out.Version = in.Version
	out.Kind = in.Kind
	out.Resource = in.Resource
	return nil
}

// Convert_v1beta1_CollectionResourceType_To_clusterpedia_CollectionResourceType is an autogenerated conversion function.
func Convert_v1beta1_CollectionResourceType_To_clusterpedia_CollectionResourceType(in *CollectionResourceType, out *clusterpedia.CollectionResourceType, s conversion.Scope) error {
	return autoConvert_v1beta1_CollectionResourceType_To_clusterpedia_CollectionResourceType(in, out, s)
}

func autoConvert_clusterpedia_CollectionResourceType_To_v1beta1_CollectionResourceType(in *clusterpedia.CollectionResourceType, out *CollectionResourceType, s conversion.Scope) error {
	out.Group = in.Group
	out.Version = in.Version
	out.Kind = in.Kind
	out.Resource = in.Resource
	return nil
}

// Convert_clusterpedia_CollectionResourceType_To_v1beta1_CollectionResourceType is an autogenerated conversion function.
func Convert_clusterpedia_CollectionResourceType_To_v1beta1_CollectionResourceType(in *clusterpedia.CollectionResourceType, out *CollectionResourceType, s conversion.Scope) error {
	return autoConvert_clusterpedia_CollectionResourceType_To_v1beta1_CollectionResourceType(in, out, s)
}

func autoConvert_v1beta1_ListOptions_To_clusterpedia_ListOptions(in *ListOptions, out *clusterpedia.ListOptions, s conversion.Scope) error {
	// FIXME: Provide conversion function to convert v1.ListOptions to internalversion.ListOptions
	compileErrorOnMissingConversion()
	// WARNING: in.Names requires manual conversion: inconvertible types (string vs []string)
	// WARNING: in.ClusterNames requires manual conversion: inconvertible types (string vs []string)
	// WARNING: in.Namespaces requires manual conversion: inconvertible types (string vs []string)
	// WARNING: in.OrderBy requires manual conversion: inconvertible types (string vs []github.com/clusterpedia-io/api/clusterpedia.OrderBy)
	out.OwnerUID = in.OwnerUID
	out.OwnerName = in.OwnerName
	// WARNING: in.Since requires manual conversion: inconvertible types (string vs *k8s.io/apimachinery/pkg/apis/meta/v1.Time)
	// WARNING: in.Before requires manual conversion: inconvertible types (string vs *k8s.io/apimachinery/pkg/apis/meta/v1.Time)
	// WARNING: in.OwnerGroupResource requires manual conversion: inconvertible types (string vs k8s.io/apimachinery/pkg/runtime/schema.GroupResource)
	out.OwnerSeniority = in.OwnerSeniority
	out.WithContinue = (*bool)(unsafe.Pointer(in.WithContinue))
	out.WithRemainingCount = (*bool)(unsafe.Pointer(in.WithRemainingCount))
	out.OnlyMetadata = in.OnlyMetadata
	// WARNING: in.urlQuery requires manual conversion: does not exist in peer-type
	return nil
}

func autoConvert_clusterpedia_ListOptions_To_v1beta1_ListOptions(in *clusterpedia.ListOptions, out *ListOptions, s conversion.Scope) error {
	// FIXME: Provide conversion function to convert internalversion.ListOptions to v1.ListOptions
	compileErrorOnMissingConversion()
	if err := runtime.Convert_Slice_string_To_string(&in.Names, &out.Names, s); err != nil {
		return err
	}
	if err := runtime.Convert_Slice_string_To_string(&in.ClusterNames, &out.ClusterNames, s); err != nil {
		return err
	}
	if err := runtime.Convert_Slice_string_To_string(&in.Namespaces, &out.Namespaces, s); err != nil {
		return err
	}
	// WARNING: in.OrderBy requires manual conversion: inconvertible types ([]github.com/clusterpedia-io/api/clusterpedia.OrderBy vs string)
	out.OwnerName = in.OwnerName
	out.OwnerUID = in.OwnerUID
	// WARNING: in.OwnerGroupResource requires manual conversion: inconvertible types (k8s.io/apimachinery/pkg/runtime/schema.GroupResource vs string)
	out.OwnerSeniority = in.OwnerSeniority
	// WARNING: in.Since requires manual conversion: inconvertible types (*k8s.io/apimachinery/pkg/apis/meta/v1.Time vs string)
	// WARNING: in.Before requires manual conversion: inconvertible types (*k8s.io/apimachinery/pkg/apis/meta/v1.Time vs string)
	out.WithContinue = (*bool)(unsafe.Pointer(in.WithContinue))
	out.WithRemainingCount = (*bool)(unsafe.Pointer(in.WithRemainingCount))
	// WARNING: in.EnhancedFieldSelector requires manual conversion: does not exist in peer-type
	// WARNING: in.ExtraLabelSelector requires manual conversion: does not exist in peer-type
	// WARNING: in.URLQuery requires manual conversion: does not exist in peer-type
	out.OnlyMetadata = in.OnlyMetadata
	return nil
}

func autoConvert_url_Values_To_v1beta1_ListOptions(in *url.Values, out *ListOptions, s conversion.Scope) error {
	// WARNING: Field ListOptions does not have json tag, skipping.

	if values, ok := map[string][]string(*in)["names"]; ok && len(values) > 0 {
		if err := runtime.Convert_Slice_string_To_string(&values, &out.Names, s); err != nil {
			return err
		}
	} else {
		out.Names = ""
	}
	if values, ok := map[string][]string(*in)["clusters"]; ok && len(values) > 0 {
		if err := runtime.Convert_Slice_string_To_string(&values, &out.ClusterNames, s); err != nil {
			return err
		}
	} else {
		out.ClusterNames = ""
	}
	if values, ok := map[string][]string(*in)["namespaces"]; ok && len(values) > 0 {
		if err := runtime.Convert_Slice_string_To_string(&values, &out.Namespaces, s); err != nil {
			return err
		}
	} else {
		out.Namespaces = ""
	}
	if values, ok := map[string][]string(*in)["orderby"]; ok && len(values) > 0 {
		if err := runtime.Convert_Slice_string_To_string(&values, &out.OrderBy, s); err != nil {
			return err
		}
	} else {
		out.OrderBy = ""
	}
	if values, ok := map[string][]string(*in)["ownerUID"]; ok && len(values) > 0 {
		if err := runtime.Convert_Slice_string_To_string(&values, &out.OwnerUID, s); err != nil {
			return err
		}
	} else {
		out.OwnerUID = ""
	}
	if values, ok := map[string][]string(*in)["ownerName"]; ok && len(values) > 0 {
		if err := runtime.Convert_Slice_string_To_string(&values, &out.OwnerName, s); err != nil {
			return err
		}
	} else {
		out.OwnerName = ""
	}
	if values, ok := map[string][]string(*in)["since"]; ok && len(values) > 0 {
		if err := runtime.Convert_Slice_string_To_string(&values, &out.Since, s); err != nil {
			return err
		}
	} else {
		out.Since = ""
	}
	if values, ok := map[string][]string(*in)["before"]; ok && len(values) > 0 {
		if err := runtime.Convert_Slice_string_To_string(&values, &out.Before, s); err != nil {
			return err
		}
	} else {
		out.Before = ""
	}
	if values, ok := map[string][]string(*in)["ownerGR"]; ok && len(values) > 0 {
		if err := runtime.Convert_Slice_string_To_string(&values, &out.OwnerGroupResource, s); err != nil {
			return err
		}
	} else {
		out.OwnerGroupResource = ""
	}
	if values, ok := map[string][]string(*in)["ownerSeniority"]; ok && len(values) > 0 {
		if err := runtime.Convert_Slice_string_To_int(&values, &out.OwnerSeniority, s); err != nil {
			return err
		}
	} else {
		out.OwnerSeniority = 0
	}
	if values, ok := map[string][]string(*in)["withContinue"]; ok && len(values) > 0 {
		if err := runtime.Convert_Slice_string_To_Pointer_bool(&values, &out.WithContinue, s); err != nil {
			return err
		}
	} else {
		out.WithContinue = nil
	}
	if values, ok := map[string][]string(*in)["withRemainingCount"]; ok && len(values) > 0 {
		if err := runtime.Convert_Slice_string_To_Pointer_bool(&values, &out.WithRemainingCount, s); err != nil {
			return err
		}
	} else {
		out.WithRemainingCount = nil
	}
	if values, ok := map[string][]string(*in)["onlyMetadata"]; ok && len(values) > 0 {
		if err := runtime.Convert_Slice_string_To_bool(&values, &out.OnlyMetadata, s); err != nil {
			return err
		}
	} else {
		out.OnlyMetadata = false
	}
	// WARNING: Field urlQuery does not have json tag, skipping.

	return nil
}
