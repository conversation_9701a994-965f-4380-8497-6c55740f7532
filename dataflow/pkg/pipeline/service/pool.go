package service

import (
	"context"
	"fmt"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/slog"
	"reflect"
	"sync"
)

var poolOnce sync.Once

var pool *FunctionPool

func Pool() *FunctionPool {
	poolOnce.Do(func() {
		pool = &FunctionPool{}
		pool.fnRouter = make(funcRouter)
	})
	return pool
}

type FunctionPool struct {
	fnLock   sync.RWMutex
	fnRouter funcRouter
}

func (p *FunctionPool) CallFunction(ctx context.Context, functionName string, pipeline Pipeline) error {
	p.fnLock.RLock()
	defer p.fnLock.RUnlock()
	if funcDesc, ok := p.fnRouter[functionName]; ok {
		params := make([]reflect.Value, 0, funcDesc.ArgNum)
		for _, argType := range funcDesc.ArgsType {
			if isPipelineType(argType) {
				params = append(params, reflect.ValueOf(pipeline))
				continue
			}

			if isContextType(argType) {
				params = append(params, reflect.ValueOf(ctx))
				continue
			}

			if isSliceType(argType) {
				value, err := funcDesc.Serialize.UnMarshal(pipeline.Input(), argType)
				if err != nil {
					slog.Logger().Errorf("funcDesc.Serialize.DecodeParam err=%v", err)
				} else {
					params = append(params, value)
					continue
				}
			}
			params = append(params, reflect.Zero(argType))
		}

		retValues := funcDesc.FunctionValue.Call(params)

		ret := retValues[0].Interface()
		if ret == nil {
			return nil
		}
		return retValues[0].Interface().(error)

	} else {
		errString := fmt.Sprintf("Poll CallFunction Not Found FuncName=%s", functionName)
		panic(errString)
	}
}

func (p *FunctionPool) FaaS(fnName string, f FaaS) {
	faaSDesc, err := NewFaaSDesc(fnName, f)
	if err != nil {
		panic(err)
	}
	p.fnLock.Lock()
	defer p.fnLock.Unlock()
	if _, ok := p.fnRouter[fnName]; !ok {
		p.fnRouter[fnName] = faaSDesc
	} else {
		errString := fmt.Sprintf("Poll FaaS Repeat FuncName=%s", fnName)
		panic(errString)
	}
	slog.Logger().Infof("Add Pool FuncName=%s", fnName)
}

type funcRouter map[string]*FaaSDesc
