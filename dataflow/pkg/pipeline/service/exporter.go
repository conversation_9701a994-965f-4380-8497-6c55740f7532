package service

import (
	"context"
)

func NewExporter(funcName string) Exporter {
	return &BaseExporter{
		FuncName: funcName,
	}
}

type Exporter interface {
	SetPipeline(p Pipeline)
	GetPipeline() Pipeline
	Call(ctx context.Context) error
}

var _ Exporter = &BaseExporter{}

type BaseExporter struct {
	Pipeline Pipeline
	FuncName string
}

func (b *BaseExporter) GetPipeline() Pipeline {
	return b.Pipeline
}

func (b *BaseExporter) Call(ctx context.Context) error {
	return Pool().CallFunction(ctx, b.FuncName, b.Pipeline)
}

func (b *BaseExporter) SetPipeline(p Pipeline) {
	b.Pipeline = p
}
