exporter:
  polaris:
    enabled: true
    accessToken: nu/0WRA4EqSR1FagrjRj0fZwPXuGlMpX+zCuWu4uMqy8xr1vRjisSbA25aAC3mtU8MeeRsKhQiDAynUR09I=
    concurrentSyncs: 5
    concurrentSize: 100
    healthCheckDuration: 5s
    # dns or mesh
    sidecarMode: dns
    resyncDuration: 120s
    operator: 65e4789a6d5b49669adf1e9e8387549c
    syncConfigMap: false
    address: ***********
    grpc:
      port: 25813
    http:
      port: 25811
controller:
  annotator:
    policy-config-path: /home/<USER>/go-project/kcs/examples/scheduler/policy.yaml
    prometheus-addr: http://localhost:9090
    concurrent-syncs: 1
    binding-heap-size: 1024
hub:
  enabled: true
  endpoint: https://0.0.0.0:34661
  token: ************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

metadata:
  cluster: demo-cluster
  zone: default
  region: beijing


