version: '3'
services:
  kong-database:
    image: postgres:9.6
    restart: always
    networks:
      - kong-net
    environment:
      POSTGRES_USER: kong
      POSTGRES_DB: kong
      POSTGRES_PASSWORD: kong
    ports:
      - "5432:5432"
  kong-migration:
    image: kong:latest
    command: "kong migrations bootstrap"
    networks:
      - kong-net
    restart: on-failure
    environment:
      - KONG_DATABASE=postgres
      - KONG_PG_DATABASE=kong
      - KONG_PG_PASSWORD=kong
      - KONG_PG_HOST=kong-database
    links:
      - kong-database
    depends_on:
      - kong-database
  kong:
    image: kong:latest
    restart: always
    networks:
      - kong-net
    environment:
      KONG_DATABASE: postgres
      KONG_PG_HOST: kong-database
      KONG_PG_PASSWORD: kong
      KONG_PROXY_LISTEN: 0.0.0.0:8000
      KONG_PROXY_LISTEN_SSL: 0.0.0.0:8443
      KONG_ADMIN_LISTEN: 0.0.0.0:8001
    depends_on:
      - kong-migration
    links:
      - kong-database
    healthcheck:
      test: ["CMD", "curl", "-f", "http://kong:8001"]
      interval: 5s
      timeout: 2s
      retries: 15
    ports:
      - "8001:8001"
      - "8000:8000"
      - "8443:8443"
  konga-prepare:
    image: pantsel/konga:latest
    command: "-c prepare -a postgres -u *****************************************/konga"
    networks:
      - kong-net
    restart: on-failure
    links:
      - kong-database
    depends_on:
      - kong
      - kong-database

  konga:
    image: pantsel/konga:latest
    restart: always
    networks:
      - kong-net
    environment:
      DB_ADAPTER: postgres
      DB_HOST: kong-database
      DB_USER: kong
      DB_DATABASE: konga
      DB_PASSWORD: kong
    depends_on:
      - kong
      - kong-database
    ports:
      - "1337:1337"
networks:
  kong-net:
    driver: bridge