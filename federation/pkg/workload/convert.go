package workload

import (
	"fmt"
	v1 "git.lianjia.com/cloudnative/kic/kic-platform/apis/aistudio/application/v1"
	velaapisv1 "git.lianjia.com/cloudnative/kic/kic-platform/pkg/third_party/velaux/apis/v1"
)

var converts map[string]Converter

func init() {
	converts = make(map[string]Converter)
}

func RegisterConverter(componentType string, converter Converter) {
	converts[componentType] = converter
}

func ConvertToWorkload(component *v1.ComponentSpec) (*velaapisv1.CreateComponentRequest, error) {
	converter, ok := converts[component.ComponentType]
	if !ok {
		return nil, fmt.Errorf("no converter for component type %s", component.ComponentType)
	}
	return converter.ConvertToWorkload(component)
}

type Converter interface {
	ConvertToWorkload(component *v1.ComponentSpec) (*velaapisv1.CreateComponentRequest, error)
}
