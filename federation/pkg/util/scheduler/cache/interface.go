package cache

import (
	"git.lianjia.com/cloudnative/kic/kic-platform/federation/pkg/util/scheduler/framework"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/util/sets"
)

type Cache interface {
	//NodeCount 返回缓存中的节点数量
	NodeCount() int
	//PodCount 返回缓存中的Pod数量
	PodCount() (int, error)
	//AssumePod 假设一个Pod已经被调度，并将Pod的信息聚合到其节点中
	AssumePod(pod *corev1.Pod) error
	//FinishBinding 被调度的Pod的缓存可以被清除
	FinishBinding(pod *corev1.Pod) error
	//ForgetPod remove a assumed pod from cache.
	ForgetPod(pod *corev1.Pod) error
	//AddPod 添加一个Pod, either confirms a pod if it's assumed, or adds it back if it's expired.
	//If added back, the pod's information would be added again.
	AddPod(pod *corev1.Pod) error
	//UpdatePod 更新Pod
	UpdatePod(oldPod, newPod *corev1.Pod) error
	//RemovePod 移除Pod, the pod`s information would be subtracted from assigned node.
	RemovePod(pod *corev1.Pod) error
	//GetPod 返回缓存中与指定Pod具有相同命名空间和名称的Pod
	GetPod(pod *corev1.Pod) (*corev1.Pod, error)
	//IsAssumedPod 如果Pod被假设且未过期，则返回true
	IsAssumedPod(pod *corev1.Pod) (bool, error)
	//AddNode 添加关于节点的整体信息
	AddNode(node *corev1.Node) *framework.NodeInfo
	//UpdateNode 更新关于节点的整体信息
	UpdateNode(oldNode, newNode *corev1.Node) *framework.NodeInfo
	//RemoveNode 移除关于节点的整体信息
	RemoveNode(node *corev1.Node) error
	//UpdateSnapshot 更新传递的infoSnapshot以反映Cache的当前内容
	UpdateSnapshot(nodeSnapshot *Snapshot) error
}

type Dump struct {
	AssumedPods sets.Set[string]
	Nodes       map[string]*framework.NodeInfo
}
