package estimator

import (
	"context"
	"fmt"
	"git.lianjia.com/cloudnative/kic/kic-platform/federation/pkg/estimator/framework"
	"git.lianjia.com/cloudnative/kic/kic-platform/federation/pkg/estimator/metrics"
	"git.lianjia.com/cloudnative/kic/kic-platform/federation/pkg/estimator/nodes"
	"git.lianjia.com/cloudnative/kic/kic-platform/federation/pkg/estimator/types"
	"git.lianjia.com/cloudnative/kic/kic-platform/federation/pkg/util"
	schedcache "git.lianjia.com/cloudnative/kic/kic-platform/federation/pkg/util/scheduler/cache"
	schedframework "git.lianjia.com/cloudnative/kic/kic-platform/federation/pkg/util/scheduler/framework"
	"git.lianjia.com/cloudnative/kic/kic-platform/federation/pkg/util/scheduler/framework/parallelize"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/apis/scheduling.kcs.io/v1alpha1"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/multicluster"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/property"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/watcher"
	"github.com/kr/pretty"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/klog/v2"
	"sigs.k8s.io/controller-runtime/pkg/cache"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sync"
	"time"
)

const (
	DefaultParallelism = 16 //默认并行度
)

type AccurateSchedulerClusterChangedListener struct {
}

func (a *AccurateSchedulerEstimator) OnAddOrUpdate(ctx context.Context, obj *v1alpha1.ClusterImport) {
	clusterEstimator, err := NewClusterScopeEstimator(ctx, obj.Name)
	if err != nil {
		klog.Errorf("create cluster estimator failed, cluster: %s, err: %v", obj.Name, err)
		return
	}
	if _, ok := a.clusters.Load(obj.Name); !ok {
		a.clusters.Store(obj.Name, clusterEstimator)
	}
}

func (a *AccurateSchedulerEstimator) OnDelete(ctx context.Context, obj *v1alpha1.ClusterImport) {
	if _, ok := a.clusters.Load(obj.Name); ok {
		a.clusters.Delete(obj.Name)
	}
}

func (a *AccurateSchedulerEstimator) Name() string {
	return "accurate-scheduler-estimator"
}

type AccurateSchedulerEstimator struct {
	clusters sync.Map
}

func NewAccurateSchedulerEstimator(ctx context.Context, properties property.EnvironmentProperty) (*AccurateSchedulerEstimator, error) {
	es := &AccurateSchedulerEstimator{
		clusters: sync.Map{},
	}
	return es, nil
}

func (a *AccurateSchedulerEstimator) MaxAvailableReplicas(ctx context.Context, request *types.MaxAvailableReplicasRequest) (replicas int32, err error) {
	clusterName := request.Cluster
	clusterEstimator, ok := a.clusters.Load(clusterName)
	if !ok {
		return 0, fmt.Errorf("cluster estimator not found, cluster: %s", clusterName)
	}
	startTime := time.Now()
	klog.V(4).Infof("Begin calculating cluster available replicas of resource(%s) request: %s", request.Component, pretty.Sprint(*request))
	defer func(start time.Time) {
		metrics.CountRequests(err, metrics.EstimatingStepMaxAvailableReplicas)
		metrics.UpdateEstimatingAlgorithmLatency(err, metrics.EstimatingTypeMaxAvailableReplicas, metrics.EstimatingStepTotal, startTime)
		if err != nil {
			klog.Errorf("Failed to calculate cluster available replicas: %v", err)
			return
		}
		klog.V(2).Infof("Finished calculating cluster available replicas of resource(%s) request: %s, time elapsed: %v", request.Component, pretty.Sprint(*request), time.Since(start))
	}(startTime)
	return clusterEstimator.(*ClusterScopeSchedulerEstimator).EstimateReplicas(ctx, request)
}

type ClusterScopeSchedulerEstimator struct {
	clusterName       string
	estimateFramework framework.Framework
	parallelizer      parallelize.Parallelizer
	Cache             schedcache.Cache
}

func (a *ClusterScopeSchedulerEstimator) OnObjectAdded(ctx context.Context, clusterName string, newObject client.Object) error {
	if pod, ok := newObject.(*corev1.Pod); ok {
		if len(pod.Spec.NodeName) == 0 {
			//未绑定节点的pod,先忽略
			return nil
		}
		a.addPodToCache(pod)
	}
	if node, ok := newObject.(*corev1.Node); ok {
		a.addNodeToCache(node)
	}
	return nil
}

func (a *ClusterScopeSchedulerEstimator) OnObjectUpdated(ctx context.Context, clusterName string, newObject, oldObject client.Object) error {
	oldPod, oldOk := oldObject.(*corev1.Pod)
	newPod, newOk := newObject.(*corev1.Pod)
	if oldOk && newOk {
		if len(oldPod.Spec.NodeName) > 0 && len(newPod.Spec.NodeName) > 0 {
			//pod节点发生变化
			a.updatePodInCache(oldPod, newPod)
		}
	}
	oldNode, oldOk := oldObject.(*corev1.Node)
	newNode, newOk := newObject.(*corev1.Node)
	if oldOk && newOk {
		a.updateNodeInCache(oldNode, newNode)
	}
	return nil
}

func (a *ClusterScopeSchedulerEstimator) OnObjectDeleted(ctx context.Context, clusterName string, oldObject client.Object) error {
	if pod, ok := oldObject.(*corev1.Pod); ok {
		if len(pod.Spec.NodeName) == 0 {
			//未绑定节点的pod,先忽略
			return nil
		}
		a.deletePodFromCache(pod)
	}
	if node, ok := oldObject.(*corev1.Node); ok {
		a.deleteNodeFromCache(node)
	}
	return nil
}

func (a *ClusterScopeSchedulerEstimator) GetWatchedObjectList() []watcher.WatchObject {
	return []watcher.WatchObject{
		{
			Object: &corev1.Node{},
		},
		{
			Object: &corev1.Pod{},
		},
	}
}

func NewClusterScopeEstimator(ctx context.Context, clusterName string) (*ClusterScopeSchedulerEstimator, error) {
	managedCluster := multicluster.Instance().GetCluster(clusterName)
	if managedCluster == nil {
		return nil, fmt.Errorf("cluster %s not found", clusterName)
	}
	es := &ClusterScopeSchedulerEstimator{
		clusterName:  clusterName,
		parallelizer: parallelize.NewParallelizer(DefaultParallelism),
	}
	err := managedCluster.WatchObjects(ctx, cache.Options{
		Scheme: managedCluster.GetScheme(),
	}, es)
	if err != nil {
		return nil, err
	}
	return es, nil
}

func (a *ClusterScopeSchedulerEstimator) EstimateReplicas(ctx context.Context, request *types.MaxAvailableReplicasRequest) (int32, error) {
	snapshot := schedcache.NewEmptySnapshot()
	if err := a.Cache.UpdateSnapshot(snapshot); err != nil {
		return 0, err
	}
	if snapshot.NumNodes() == 0 {
		return 0, nil
	}
	maxAvailableReplicas, err := a.estimateReplicas(ctx, snapshot, request.ReplicaRequirements)
	if err != nil {
		return 0, err
	}
	return maxAvailableReplicas, nil
}

func (a *ClusterScopeSchedulerEstimator) estimateReplicas(ctx context.Context, snapshot *schedcache.Snapshot, requirements types.ReplicaRequirements) (int32, error) {
	allNodes, err := snapshot.NodeInfos().List()
	if err != nil {
		return 0, err
	}
	var (
		affinity    = nodes.GetRequiredNodeAffinity(requirements)
		tolerations []corev1.Toleration
	)
	if requirements.NodeClaim != nil {
		tolerations = requirements.NodeClaim.Tolerations
	}
	var res int32
	replicas, ret := a.estimateFramework.RunEstimateReplicasPlugins(ctx, snapshot, &requirements)
	if ret.IsUnschedulable() {
		return 0, nil
	}

	if !ret.IsSuccess() && !ret.IsNoOperation() {
		return replicas, fmt.Errorf("estimate replicas plugins fails with %s", ret.Reasons())
	}
	processNode := func(i int) {
		node := allNodes[i]
		if !nodes.IsNodeAffinityMatched(node.Node(), affinity) || !nodes.IsTolerationMatched(node.Node(), tolerations) {
			return
		}
		res += a.nodeMaxAvailableReplica(node, requirements.ResourceRequest)
	}
	a.parallelizer.Until(ctx, len(allNodes), processNode)
	if ret.IsSuccess() && replicas < res {
		res = replicas
	}
	return res, nil
}

func (a *ClusterScopeSchedulerEstimator) nodeMaxAvailableReplica(node *schedframework.NodeInfo, rl corev1.ResourceList) int32 {
	rest := node.Allocatable.Clone().SubResource(node.Requested)
	// The number of pods in a node is a kind of resource in node allocatable resources.
	// However, total requested resources of all pods on this node, i.e. `node.Requested`,
	// do not contain pod resources. So after subtraction, we should cope with allowed pod
	// number manually which is the upper bound of this node available replicas.
	rest.AllowedPodNumber = util.MaxInt64(rest.AllowedPodNumber-int64(len(node.Pods)), 0)
	return int32(rest.MaxDivided(rl))
}
