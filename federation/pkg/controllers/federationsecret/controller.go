package federationsecret

import (
	"context"
	"fmt"
	"git.lianjia.com/cloudnative/kic/kic-platform/federation/pkg/constant"
	"git.lianjia.com/cloudnative/kic/kic-platform/federation/pkg/reconcilehelper"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/apis/scheduling.kcs.io/v1alpha1"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/property"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/util"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/client-go/tools/record"
	"k8s.io/client-go/util/retry"
	"k8s.io/klog/v2"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/handler"
	"sigs.k8s.io/controller-runtime/pkg/log"
	"sigs.k8s.io/controller-runtime/pkg/manager"
	"sigs.k8s.io/controller-runtime/pkg/reconcile"
	"sigs.k8s.io/controller-runtime/pkg/source"
)

type Controller struct {
	client   client.Client
	recorder record.EventRecorder
}

func SetupWithManager(ctx context.Context, manager manager.Manager, properties property.EnvironmentProperty) error {
	controller := &Controller{
		client:   manager.GetClient(),
		recorder: manager.GetEventRecorderFor("fd-secret-controller"),
	}
	return ctrl.NewControllerManagedBy(manager).
		For(&v1alpha1.FederationSecret{}).
		Watches(&source.Kind{Type: &v1alpha1.FederationSecretTracker{}},
			&handler.EnqueueRequestForOwner{OwnerType: &v1alpha1.FederationSecret{}}).
		Watches(&source.Kind{Type: &v1alpha1.WorkspaceClusterBinding{}},
			controller.enqueueRequestForWorkspaceClusterBindingChanged()).
		Complete(controller)
}

func (c *Controller) enqueueRequestForWorkspaceClusterBindingChanged() handler.EventHandler {
	return handler.EnqueueRequestsFromMapFunc(func(object client.Object) []reconcile.Request {
		workspaceClusterBindings, ok := object.(*v1alpha1.WorkspaceClusterBinding)
		if ok {
			workspaceName := workspaceClusterBindings.Labels[constant.WorkspaceLabelKey]
			cms := &v1alpha1.FederationSecretList{}
			var result []reconcile.Request
			err := c.client.List(context.Background(), cms, client.InNamespace(constant.GetAIStudioNamespaceName(workspaceName)))
			if err != nil {
				klog.Errorf("failed to list federation configmaps: %v", err)
				return []reconcile.Request{}
			}
			for _, cm := range cms.Items {
				result = append(result, reconcile.Request{
					NamespacedName: types.NamespacedName{
						Name:      cm.Name,
						Namespace: cm.Namespace,
					},
				})
			}
			return result
		}
		return []reconcile.Request{}
	})
}

func (c *Controller) Reconcile(ctx context.Context, request ctrl.Request) (ctrl.Result, error) {
	_ = log.FromContext(ctx)
	fsecret := &v1alpha1.FederationSecret{}
	err := c.client.Get(ctx, request.NamespacedName, fsecret)
	if err != nil {
		if client.IgnoreNotFound(err) == nil {
			klog.Info("not found federation secret:" + request.NamespacedName.String())
			return ctrl.Result{}, nil
		}
		return ctrl.Result{}, err
	}
	if !fsecret.DeletionTimestamp.IsZero() {
		if err := c.handleFinalizer(ctx, fsecret); err != nil {
			return ctrl.Result{}, fmt.Errorf("error when handling finalizer: %v", err)
		}
		return ctrl.Result{}, nil
	}
	if !fsecret.HasFinalizer(constant.AppFinalizer) {
		if err := c.addFinalizer(ctx, fsecret); err != nil {
			return ctrl.Result{}, fmt.Errorf("error adding finalizer: %v", err)
		}
	}

	syncClusters, err := reconcilehelper.GetSyncedClusters(ctx, c.client, fsecret.Spec.WorkspaceName)
	if err != nil {
		c.recorder.Eventf(fsecret, "Warning", "GetSyncedClustersFailed", "failed to get synced clusters: %v", err)
		return ctrl.Result{}, err
	}

	added, updated, removed, err := c.diffTrackers(ctx, fsecret, syncClusters)
	if err != nil {
		c.recorder.Eventf(fsecret, "Warning", "DiffTrackersFailed", "failed to diff trackers: %v", err)
		return ctrl.Result{}, err
	}
	if len(added) > 0 {
		for _, addedTracker := range added {
			if err = c.client.Create(ctx, addedTracker); err != nil {
				c.recorder.Eventf(fsecret, "Warning", "CreateTrackerFailed", "failed to create tracker: %v", err)
				fsecret.Status.Message = fmt.Sprintf("create tracker %s failed: %v", addedTracker.Name, err)
				fsecret.Status.Reason = "CreateFailed"
			} else {
				fsecret.Status.Message = fmt.Sprintf("create tracker %s success", addedTracker.Name)
				fsecret.Status.Reason = "CreateSuccess"
			}
			fsecret.Status.LastTransitionTime = metav1.Now()
			if err = c.client.Status().Update(ctx, fsecret); err != nil {
				return ctrl.Result{}, err
			}
		}
	}
	if len(updated) > 0 {
		for _, updatedTracker := range updated {
			if err = c.client.Update(ctx, updatedTracker); err != nil {
				c.recorder.Eventf(fsecret, "Warning", "UpdateTrackerFailed", "failed to update tracker: %v", err)
				fsecret.Status.Message = fmt.Sprintf("update tracker %s failed: %v", updatedTracker.Name, err)
				fsecret.Status.Reason = "UpdateFailed"
			} else {
				fsecret.Status.Message = fmt.Sprintf("update tracker %s success", updatedTracker.Name)
				fsecret.Status.Reason = "UpdateSuccess"
			}
			fsecret.Status.LastTransitionTime = metav1.Now()
			if err = c.client.Status().Update(ctx, fsecret); err != nil {
				return ctrl.Result{}, err
			}
		}
	}

	if len(removed) > 0 {
		for _, removedTracker := range removed {
			if err = c.client.Delete(ctx, removedTracker); err != nil {
				c.recorder.Eventf(fsecret, "Warning", "DeleteTrackerFailed", "failed to delete tracker: %v", err)
				fsecret.Status.Message = fmt.Sprintf("delete tracker %s failed: %v", removedTracker.Name, err)
				fsecret.Status.Reason = "DeleteFailed"
			} else {
				fsecret.Status.Message = fmt.Sprintf("delete tracker %s success", removedTracker.Name)
				fsecret.Status.Reason = "DeleteSuccess"
			}
			fsecret.Status.LastTransitionTime = metav1.Now()
			if err = c.client.Status().Update(ctx, fsecret); err != nil {
				return ctrl.Result{}, err
			}
		}
	}
	updateErr := retry.RetryOnConflict(retry.DefaultRetry, func() error {
		federationSecretStatus := fsecret.Status
		totalClusters := len(syncClusters)
		syncedClusters := 0
		// 获取当前同步成功的tracker
		var trackerList v1alpha1.FederationSecretTrackerList
		err = c.client.List(ctx, &trackerList, client.MatchingLabels{
			constant.WorkspaceLabelKey:        fsecret.Spec.WorkspaceName,
			constant.FederationSecretLabelKey: fsecret.Name,
		})
		if err != nil {
			return err
		}
		for _, tracker := range trackerList.Items {
			if tracker.Status.Reason == "Success" {
				syncedClusters++
			}
		}
		federationSecretStatus.TotalClusters = totalClusters
		federationSecretStatus.SyncedClusters = syncedClusters
		if syncedClusters == totalClusters {
			federationSecretStatus.State = v1alpha1.FederationStateSynced
		} else {
			federationSecretStatus.State = v1alpha1.FederationStateSyncing
		}
		fsecret.Status = federationSecretStatus
		return c.client.Status().Update(ctx, fsecret)
	})
	return ctrl.Result{}, updateErr

}

func (c *Controller) diffTrackers(ctx context.Context, secret *v1alpha1.FederationSecret, syncClusters []string) (added, updated, removed []*v1alpha1.FederationSecretTracker, err error) {
	var trackerList v1alpha1.FederationSecretTrackerList
	err = c.client.List(ctx, &trackerList,
		client.InNamespace(secret.Namespace),
		client.MatchingLabels{
			constant.WorkspaceLabelKey:        secret.Spec.WorkspaceName,
			constant.FederationSecretLabelKey: secret.Name,
		})
	if err != nil {
		return nil, nil, nil, err
	}

	existingTrackers := make(map[string]*v1alpha1.FederationSecretTracker)
	for _, tracker := range trackerList.Items {
		existingTrackers[tracker.Spec.TargetCluster] = &tracker
	}

	for _, clusterName := range syncClusters {
		if tracker, exists := existingTrackers[clusterName]; !exists {
			added = append(added, &v1alpha1.FederationSecretTracker{
				ObjectMeta: metav1.ObjectMeta{
					Name:      fmt.Sprintf("%s-%s", secret.Name, util.GenerateHash(clusterName, 8)),
					Namespace: secret.Namespace,
					Labels: map[string]string{
						constant.WorkspaceLabelKey:        secret.Spec.WorkspaceName,
						constant.FederationSecretLabelKey: secret.Name,
						constant.ClusterLabelKey:          clusterName,
					},
					OwnerReferences: []metav1.OwnerReference{
						{
							APIVersion: secret.APIVersion,
							Kind:       secret.Kind,
							Name:       secret.Name,
							UID:        secret.UID,
						},
					},
				},
				Spec: v1alpha1.FederationSecretTrackerSpec{
					TargetWorkspaceName: secret.Spec.WorkspaceName,
					TargetNamespace:     secret.Namespace,
					TargetCluster:       clusterName,
					SecretName:          secret.Name,
					Data:                secret.Spec.Data,
					StringData:          secret.Spec.StringData,
					Type:                secret.Spec.Type,
				},
			})
		} else {
			tracker.Spec.Data = secret.Spec.Data
			tracker.Spec.StringData = secret.Spec.StringData
			tracker.Spec.Type = secret.Spec.Type
			updated = append(updated, tracker)
		}
	}

	for _, tracker := range trackerList.Items {
		if !util.Contains(syncClusters, tracker.Spec.TargetCluster) {
			removed = append(removed, &tracker)
		}
	}

	return added, updated, removed, nil
}

func (c *Controller) handleFinalizer(ctx context.Context, secret *v1alpha1.FederationSecret) error {
	klog.Infof("delete federation secert %s", secret.Name)
	secret.RemoveFinalizer(constant.AppFinalizer)
	return c.client.Update(ctx, secret)
}

func (c *Controller) addFinalizer(ctx context.Context, secret *v1alpha1.FederationSecret) error {
	secret.AddFinalizer(constant.AppFinalizer)
	return c.client.Update(ctx, secret)
}
