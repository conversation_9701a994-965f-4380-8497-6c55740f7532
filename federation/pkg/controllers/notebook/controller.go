package notebook

import (
	"context"
	"fmt"
	"math/rand"
	"os"
	"reflect"
	"strconv"
	"strings"
	"sync"
	"time"

	"git.lianjia.com/cloudnative/kic/kic-platform/federation/pkg/constant"
	"git.lianjia.com/cloudnative/kic/kic-platform/federation/pkg/controllers/task"
	"git.lianjia.com/cloudnative/kic/kic-platform/federation/pkg/metrics"
	"git.lianjia.com/cloudnative/kic/kic-platform/federation/pkg/objectwatcher"
	"git.lianjia.com/cloudnative/kic/kic-platform/federation/pkg/reconcilehelper"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/apis/automl.kcs.io/v1alpha1"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/multicluster"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/property"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/watcher"
	"github.com/go-co-op/gocron/v2"
	"github.com/google/go-cmp/cmp"
	"github.com/google/go-cmp/cmp/cmpopts"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/client-go/tools/record"
	"k8s.io/client-go/util/retry"
	"k8s.io/klog/v2"
	"sigs.k8s.io/controller-runtime/pkg/cache"
	"sigs.k8s.io/controller-runtime/pkg/log"
	"sigs.k8s.io/controller-runtime/pkg/manager"

	appsv1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	apierrs "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/apimachinery/pkg/util/intstr"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"
)

const DefaultNotebookPort = 8888
const DefaultCodeServerPort = 8889
const DefaultNotebookServiceName = "notebook-service"
const DefaultVsCodeServiceName = "vscode-service"

const PrefixEnvVar = "NB_PREFIX"

// DefaultFSGroup The default fsGroup of PodSecurityContext.
// https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.11/#podsecuritycontext-v1-core
const DefaultFSGroup = int64(100)

func ignoreNotFound(err error) error {
	if apierrs.IsNotFound(err) {
		return nil
	}
	return err
}

type Controller struct {
	client.Client
	Scheme        *runtime.Scheme
	Metrics       *metrics.Metrics
	EventRecorder record.EventRecorder
	Scheduler     gocron.Scheduler
	jobs          sync.Map
	ctx           context.Context
}

func (r *Controller) OnObjectAdded(ctx context.Context, clusterName string, newObject client.Object) error {
	if statefulSet, ok := newObject.(*appsv1.StatefulSet); ok {
		if v, ok := statefulSet.Labels["automl.kcs.io/notebook-name"]; ok {
			_, err := r.Reconcile(ctx, ctrl.Request{
				NamespacedName: types.NamespacedName{
					Namespace: newObject.GetNamespace(),
					Name:      v,
				},
			})
			return err
		}
	}
	if pod, ok := newObject.(*corev1.Pod); ok {
		if v, ok := pod.Labels["automl.kcs.io/notebook-name"]; ok {
			_, err := r.Reconcile(ctx, ctrl.Request{
				NamespacedName: types.NamespacedName{
					Namespace: newObject.GetNamespace(),
					Name:      v,
				},
			})
			return err
		}
	}
	if service, ok := newObject.(*corev1.Service); ok {
		if v, ok := service.Labels["automl.kcs.io/notebook-name"]; ok {
			_, err := r.Reconcile(ctx, ctrl.Request{
				NamespacedName: types.NamespacedName{
					Namespace: newObject.GetNamespace(),
					Name:      v,
				},
			})
			return err
		}
	}
	return nil
}

func (r *Controller) OnObjectUpdated(ctx context.Context, clusterName string, newObject, oldObject client.Object) error {
	switch newObj := newObject.(type) {
	case *corev1.Pod:
		if oldObj, ok := oldObject.(*corev1.Pod); ok {
			// 忽略 PodStatus.Conditions 中每个 PodCondition 的 LastProbeTime 和 LastTransitionTime
			if !cmp.Equal(oldObj.Status, newObj.Status, cmpopts.IgnoreFields(corev1.PodStatus{}, "Conditions")) {
				return r.OnObjectAdded(ctx, clusterName, newObject)
			}

			if !cmp.Equal(oldObj.Status.Conditions, newObj.Status.Conditions, cmpopts.IgnoreFields(corev1.PodCondition{}, "LastProbeTime", "LastTransitionTime", "Message")) {
				return r.OnObjectAdded(ctx, clusterName, newObject)
			}

			return nil
		}
	case *appsv1.StatefulSet:
		if oldObj, ok := oldObject.(*appsv1.StatefulSet); ok {
			// 先比较 StatefulSetStatus 除了 Conditions 外的部分
			if !cmp.Equal(oldObj.Status, newObj.Status, cmpopts.IgnoreFields(appsv1.StatefulSetStatus{}, "Conditions")) {
				return r.OnObjectAdded(ctx, clusterName, newObject)
			}
			// 再比较 Conditions，忽略其中的 LastTransitionTime 字段
			if !cmp.Equal(oldObj.Status.Conditions, newObj.Status.Conditions, cmpopts.IgnoreFields(appsv1.StatefulSetCondition{}, "LastTransitionTime", "Message")) {
				return r.OnObjectAdded(ctx, clusterName, newObject)
			}
			return nil
		}
	case *corev1.Service:
		if oldObj, ok := oldObject.(*corev1.Service); ok {
			if !cmp.Equal(oldObj.Status, newObj.Status) {
				return r.OnObjectAdded(ctx, clusterName, newObject)
			}
			return nil
		}
	}
	return r.OnObjectAdded(ctx, clusterName, newObject)
}

func (r *Controller) OnObjectDeleted(ctx context.Context, clusterName string, oldObject client.Object) error {
	return nil
}

func (r *Controller) GetWatchedObjectList() []watcher.WatchObject {
	return []watcher.WatchObject{
		{
			Object: &appsv1.StatefulSet{},
			ObjectSelector: &cache.ObjectSelector{
				Label: labels.SelectorFromSet(map[string]string{
					constant.KICManagedLabelKey: "kic",
				}),
			},
		},
		{
			Object: &corev1.Pod{},
			ObjectSelector: &cache.ObjectSelector{
				Label: labels.SelectorFromSet(map[string]string{
					constant.KICManagedLabelKey: "kic",
				}),
			},
		},
		{
			Object: &corev1.Service{},
			ObjectSelector: &cache.ObjectSelector{
				Label: labels.SelectorFromSet(map[string]string{
					constant.KICManagedLabelKey: "kic",
				}),
			},
		},
	}
}

func SetupWithManager(ctx context.Context, manager manager.Manager, properties property.EnvironmentProperty) error {
	controller := &Controller{
		Client:        manager.GetClient(),
		Scheme:        manager.GetScheme(),
		Metrics:       metrics.NewMetrics(manager.GetClient()),
		EventRecorder: manager.GetEventRecorderFor("notebook-controller"),
		jobs:          sync.Map{},
		ctx:           ctx,
	}
	controller.Scheduler = reconcilehelper.GetScheduler()
	err := manager.Add(&task.RunnableTask{
		Task: func(ctx context.Context) error {
			// 全局定时更新Notebook
			_, err := controller.Scheduler.NewJob(
				gocron.DurationJob(time.Duration(rand.Intn(30)+15)*time.Second),
				gocron.NewTask(func() {
					var nbList v1alpha1.NotebookList
					if err := controller.List(ctx, &nbList); err != nil {
						klog.Errorf("[Notebook] Failed to list Notebooks: %v", err)
						return
					}
					for i := range nbList.Items {
						nb := &nbList.Items[i]
						if !nb.DeletionTimestamp.IsZero() || nb.Spec.Shutdown {
							continue
						}
						clusterName := nb.Spec.Cluster
						if clusterName == "" {
							klog.Warningf("[Notebook] Cluster not specified for Notebook %s", nb.Name)
							continue
						}
						targetCluster := multicluster.Instance().GetCluster(clusterName)
						if targetCluster == nil {
							klog.Warningf("[Notebook] Cluster %s not found for Notebook %s", clusterName, nb.Name)
							continue
						}
						sts := &appsv1.StatefulSet{}
						if err := targetCluster.Direct().Get(ctx, types.NamespacedName{Name: nb.Name, Namespace: nb.Namespace}, sts); err != nil {
							klog.Warningf("[Notebook] Failed to get StatefulSet for Notebook %s: %v", nb.Name, err)
							continue
						}
						pod := &corev1.Pod{}
						podName := nb.Name + "-0"
						if err := targetCluster.Direct().Get(ctx, types.NamespacedName{Name: podName, Namespace: nb.Namespace}, pod); err != nil {
							klog.Warningf("[Notebook] Failed to get Pod for Notebook %s: %v", nb.Name, err)
							continue
						}
						// 更新 Notebook 状态（内部会比较变化，只有有变化时才更新）
						klog.Infof("[Notebook] Scheduled update Notebook %s, workspace %s", nb.Name, nb.Labels["kic-aistudio.kcs.com/workspace-name"])
						if err := updateNotebookStatus(ctx, controller, nb, sts, pod); err != nil {
							klog.Errorf("[Notebook] Failed to update status for Notebook %s: %v", nb.Name, err)
						}
					}
				}),
				gocron.WithTags("global-notebook-status-update"),
			)
			if err != nil {
				klog.Errorf("[Notebook] Failed to set up global scheduled job: %v", err)
				return err
			}
			// 监听 Context 变化，清理所有定时任务
			go func() {
				for {
					select {
					case <-ctx.Done():
						controller.jobs.Range(func(key, value interface{}) bool {
							if _, ok := value.(*gocron.Job); ok {
								controller.Scheduler.RemoveByTags(key.(string))
							}
							return true
						})
					}
				}
			}()
			return nil
		},
		LeaderElectionEnabled: true,
	})
	objectwatcher.RegisterObjectChangedListener("notebook-controller", controller)
	if err != nil {
		return err
	}
	return ctrl.NewControllerManagedBy(manager).For(&v1alpha1.Notebook{}).Complete(controller)
}

// +kubebuilder:rbac:groups=core,resources=pods,verbs=get;list;watch
// +kubebuilder:rbac:groups=core,resources=events,verbs=get;list;watch;create;patch
// +kubebuilder:rbac:groups=core,resources=services,verbs="*"
// +kubebuilder:rbac:groups=apps,resources=statefulsets,verbs="*"

func (r *Controller) Reconcile(ctx context.Context, req ctrl.Request) (ctrl.Result, error) {
	_ = log.FromContext(ctx)
	instance := &v1alpha1.Notebook{}
	if err := r.Get(ctx, req.NamespacedName, instance); err != nil {
		if apierrs.IsNotFound(err) {
			return ctrl.Result{}, nil
		}
		klog.Errorf("Error getting Notebook %s: %v", req.Name, err)
		return ctrl.Result{}, err
	}

	// so when Notebook CR is terminating, reconcile loop should do nothing
	if !instance.DeletionTimestamp.IsZero() {
		if _, ok := r.jobs.Load(fmt.Sprintf("%s-%s", instance.Namespace, instance.Name)); ok {
			r.Scheduler.RemoveByTags(fmt.Sprintf("corn-update-%s-%s", instance.Namespace, instance.Name))
			r.jobs.Delete(fmt.Sprintf("%s-%s", instance.Namespace, instance.Name))
		}
		if err := r.handleFinalizer(ctx, instance); err != nil {
			return ctrl.Result{}, fmt.Errorf("error when handling finalizer: %v", err)
		}
		return ctrl.Result{}, nil
	}
	// shutdown notebook
	if instance.Spec.Shutdown {
		if _, ok := r.jobs.Load(fmt.Sprintf("%s-%s", instance.Namespace, instance.Name)); ok {
			r.Scheduler.RemoveByTags(fmt.Sprintf("corn-update-%s-%s", instance.Namespace, instance.Name))
			r.jobs.Delete(fmt.Sprintf("%s-%s", instance.Namespace, instance.Name))
		}
		if err := r.handleFinalizer(ctx, instance); err != nil {
			return ctrl.Result{}, fmt.Errorf("error when handling finalizer: %v", err)
		}

		if err := updateNotebookStatusForShutdown(ctx, r, instance); err != nil {
			return ctrl.Result{}, fmt.Errorf("error during shutdown status update: %v", err)
		}
		return ctrl.Result{}, nil

	}

	if !instance.HasFinalizer(constant.AppFinalizer) {
		if err := r.addFinalizer(ctx, instance); err != nil {
			return ctrl.Result{}, fmt.Errorf("error when adding finalizer: %v", err)
		}
		return ctrl.Result{}, nil
	}

	targetCluster := multicluster.Instance().GetCluster(instance.Spec.Cluster)
	if targetCluster == nil {
		klog.Errorf("[NoteBook] target cluster %s not found", instance.Spec.Cluster)
		return ctrl.Result{}, nil
	}
	sshConfigSpec := instance.Spec.SSHConfigSpec
	if sshConfigSpec.SSHEnabled && sshConfigSpec.SSHKeys != nil && len(sshConfigSpec.SSHKeys) > 0 {
		cm := generateSSHKeysConfigMap(instance)
		foundConfigMap := &corev1.ConfigMap{}
		justCreated := false
		if err := targetCluster.Direct().Get(ctx, types.NamespacedName{Name: cm.Name, Namespace: cm.Namespace}, foundConfigMap); err != nil {
			if apierrs.IsNotFound(err) {
				r.Metrics.NotebookCreation.WithLabelValues(cm.Namespace).Inc()
				err = targetCluster.Direct().Create(ctx, cm)
				justCreated = true
				if err != nil {
					klog.Errorf("[NoteBook] error creating configMap %s: %v", cm.Name, err)
					r.Metrics.NotebookFailCreation.WithLabelValues(cm.Namespace).Inc()
					return ctrl.Result{}, err
				}
			} else {
				klog.Errorf("[NoteBook] error getting configMap %s: %v", cm.Name, err)
				return ctrl.Result{}, err
			}
		}
		if !justCreated && reconcilehelper.CopyConfigMapFields(cm, foundConfigMap) {
			err := retry.RetryOnConflict(retry.DefaultRetry, func() error {
				err := targetCluster.Direct().Get(ctx, types.NamespacedName{Name: cm.Name, Namespace: cm.Namespace}, foundConfigMap)
				if err != nil {
					return err
				}
				return targetCluster.Direct().Update(ctx, foundConfigMap)
			})
			if err != nil {
				klog.Errorf("[NoteBook] error updating configMap %s: %v", cm.Name, err)
				return ctrl.Result{}, err
			}
		}
	}
	ss := generateStatefulSet(instance)
	//该StatefulSet在远端集群进行创建
	foundStateful := &appsv1.StatefulSet{}
	justCreated := false
	err := targetCluster.Direct().Get(ctx, types.NamespacedName{Name: ss.Name, Namespace: ss.Namespace}, foundStateful)
	if err != nil {
		if apierrs.IsNotFound(err) {
			r.Metrics.NotebookCreation.WithLabelValues(ss.Namespace).Inc()
			err = targetCluster.Direct().Create(ctx, ss)
			justCreated = true
			if err != nil {
				klog.Errorf("[NoteBook] error creating statefulSet %s: %v", ss.Name, err)
				r.Metrics.NotebookFailCreation.WithLabelValues(ss.Namespace).Inc()
				return ctrl.Result{}, err
			}
		} else {
			klog.Errorf("[Notebook] error getting statefulSet %s: %v", ss.Name, err)
			return ctrl.Result{}, err
		}
	}

	// Update the foundStateful object and write the result back if there are any changes
	if !justCreated && reconcilehelper.CopyStatefulSetFields(ss, foundStateful) {
		err = retry.RetryOnConflict(retry.DefaultRetry, func() error {
			err = targetCluster.Direct().Get(ctx, types.NamespacedName{Name: ss.Name, Namespace: ss.Namespace}, foundStateful)
			if err != nil {
				return err
			}
			return targetCluster.Direct().Update(ctx, foundStateful)
		})
		if err != nil {
			klog.Errorf("[NoteBook] error updating statefulSet %s: %v", ss.Name, err)
			return ctrl.Result{}, err
		}
	}
	var service = generateService(instance)
	switch instance.Spec.ExposureMode {
	case v1alpha1.ExposureIngress:
		return ctrl.Result{}, fmt.Errorf("ingress exposure mode is not supported")
	case v1alpha1.ExposureServiceNodePort:
		service.Spec.Type = corev1.ServiceTypeNodePort
		addServiceExposePort(instance, service)

	case v1alpha1.ExposurePodIP:
		klog.Infof("it will create nothing for notebook 【%s:%s]", instance.Namespace, instance.Name)
	case v1alpha1.ExposureHttpRoute:
		return ctrl.Result{}, fmt.Errorf("http route exposure mode is not supported")
	case v1alpha1.ExposureServiceClusterIP:
		service.Spec.Type = corev1.ServiceTypeClusterIP
	}

	// Check if the Service already exists
	foundService := &corev1.Service{}
	justCreated = false
	err = targetCluster.Direct().Get(ctx, types.NamespacedName{Name: service.Name, Namespace: service.Namespace}, foundService)
	if err != nil {
		if apierrs.IsNotFound(err) {
			err = targetCluster.Direct().Create(ctx, service)
			justCreated = true
			if err != nil {
				klog.Errorf("[Notebook] error creating service %s: %v", service.Name, err)
				r.EventRecorder.Event(instance, corev1.EventTypeWarning, "FailedCreateService", err.Error())
				return ctrl.Result{}, err
			}
		} else {
			klog.Errorf("[Notebook] error getting service %s: %v", service.Name, err)
			return ctrl.Result{}, err
		}
	}

	// Update the foundService object and write the result back if there are any changes
	if !justCreated && reconcilehelper.CopyServiceFields(service, foundService) {
		err = retry.RetryOnConflict(retry.DefaultRetry, func() error {
			err = targetCluster.Direct().Get(ctx, types.NamespacedName{Name: foundService.Name, Namespace: foundService.Namespace}, foundService)
			if err != nil {
				return err
			}
			return targetCluster.Direct().Update(ctx, foundService)
		})
		if err != nil {
			klog.Errorf("[Notebook] error updating Service %s: %v", service.Name, err)
			return ctrl.Result{}, err
		}
	}

	if instance.Spec.ExposureMode == v1alpha1.ExposureServiceNodePort && foundService.Spec.Type == corev1.ServiceTypeNodePort {
		var nodeList = &corev1.NodeList{}
		err = targetCluster.Direct().List(ctx, nodeList, client.MatchingLabels{
			"node-port.kcs.io/status": "enabled",
		})
		if err != nil {
			return ctrl.Result{}, err
		}
		if len(nodeList.Items) > 0 {
			instance.Status.VSCodeAccessURL = fmt.Sprintf("%s:%d", getNodeIPByK8sNode(&nodeList.Items[rand.Intn(len(nodeList.Items))]), getServicePortByName(DefaultVsCodeServiceName, foundService))
			workspacesName := strings.TrimPrefix(instance.Namespace, "aistudio-")
			jupyterPrefix := fmt.Sprintf("notebook/proxy/workspace/%s/dev-machine/%s", workspacesName, instance.Name)
			instance.Status.JupyterAccessURL = fmt.Sprintf("%s:%d/%s", getNodeIPByK8sNode(&nodeList.Items[rand.Intn(len(nodeList.Items))]), getServicePortByName(DefaultNotebookServiceName, foundService), jupyterPrefix)
		}
	}

	foundPod := &corev1.Pod{}
	err = targetCluster.Direct().Get(ctx, types.NamespacedName{Name: ss.Name + "-0", Namespace: ss.Namespace}, foundPod)
	if err != nil {
		if apierrs.IsNotFound(err) {
			klog.Infof("[Notebook] No Pods are currently running for Notebook Server: %s in namespace: %s.", instance.Name, instance.Namespace)

		} else {
			klog.Errorf("[Notebook] error getting pod %s: %v", ss.Name+"-0", err)
			return ctrl.Result{}, err
		}
	}

	// Update Notebook CR status
	if err = updateNotebookStatus(ctx, r, instance, foundStateful, foundPod); err != nil {
		return ctrl.Result{}, err
	}

	if foundPod.Status.Phase == corev1.PodPending && time.Since(foundPod.CreationTimestamp.Time) > 10*time.Minute {
		klog.Infof("[Notebook] Pod %s is still in Pending state after 10 minutes, requeue the request", foundPod.Name)
		return ctrl.Result{RequeueAfter: 1 * time.Minute}, nil
	}

	//if _, ok := r.jobs.Load(fmt.Sprintf("%s-%s", instance.Namespace, instance.Name)); !ok {
	//	job, err := r.Scheduler.NewJob(
	//		gocron.DurationJob(time.Duration(rand.Intn(30)+15)*time.Second),
	//		gocron.NewTask(func() {
	//			err = cronUpdateNotebookStatus(ctx, r, req)
	//			if err != nil {
	//				klog.Errorf("[Notebook] error updating Notebook CR status: %v", err)
	//			}
	//		}),
	//		gocron.WithTags(fmt.Sprintf("corn-update-%s-%s", instance.Namespace, instance.Name)),
	//	)
	//	if err != nil {
	//		klog.Errorf("Failed to set up scheduled job: %v", err)
	//		return ctrl.Result{}, err
	//	}
	//	r.jobs.Store(fmt.Sprintf("%s-%s", instance.Namespace, instance.Name), job)
	//}
	return ctrl.Result{}, nil
}

func (r *Controller) addFinalizer(ctx context.Context, notebook *v1alpha1.Notebook) error {
	notebook.AddFinalizer(constant.AppFinalizer)
	return r.Update(ctx, notebook)
}

func (r *Controller) handleFinalizer(ctx context.Context, notebook *v1alpha1.Notebook) error {
	klog.Infof("delete notebook %s, will actually delete", notebook.Name)
	k8sCluster := multicluster.Instance().GetCluster(notebook.Spec.Cluster)
	if k8sCluster == nil {
		return nil
	}

	// 删除 StatefulSet
	statefulSet := &appsv1.StatefulSet{ObjectMeta: metav1.ObjectMeta{Name: notebook.Name, Namespace: notebook.Namespace}}
	if err := k8sCluster.Direct().Get(ctx, types.NamespacedName{Name: statefulSet.Name, Namespace: statefulSet.Namespace}, statefulSet); err != nil && !apierrs.IsNotFound(err) {
		return err
	} else if err == nil {
		if err := k8sCluster.Direct().Delete(ctx, statefulSet); err != nil {
			return err
		}
	}

	// 删除 Service
	if notebook.Spec.ExposureMode == v1alpha1.ExposureServiceNodePort || notebook.Spec.ExposureMode == v1alpha1.ExposureServiceClusterIP {
		service := &corev1.Service{ObjectMeta: metav1.ObjectMeta{Name: notebook.Name, Namespace: notebook.Namespace}}
		if err := k8sCluster.Direct().Get(ctx, types.NamespacedName{Name: service.Name, Namespace: service.Namespace}, service); err != nil && !apierrs.IsNotFound(err) {
			return err
		} else if err == nil {
			if err := k8sCluster.Direct().Delete(ctx, service); err != nil {
				return err
			}
		}
	}

	// 删除 ConfigMap
	configMap := &corev1.ConfigMap{ObjectMeta: metav1.ObjectMeta{Name: generateConfigMapName(notebook.Name), Namespace: notebook.Namespace}}
	if err := k8sCluster.Direct().Get(ctx, types.NamespacedName{Name: configMap.Name, Namespace: configMap.Namespace}, configMap); err != nil && !apierrs.IsNotFound(err) {
		return err
	} else if err == nil {
		if err := k8sCluster.Direct().Delete(ctx, configMap); err != nil {
			return err
		}
	}

	// 移除 Finalizer 并更新 Notebook
	notebook.RemoveFinalizer(constant.AppFinalizer)
	return r.Update(ctx, notebook)
}
func generateConfigMapName(name string) string {
	return name + "-ssh-keys"
}

func getServicePortByName(name string, srv *corev1.Service) int {
	for _, port := range srv.Spec.Ports {
		if port.Name == name {
			return int(port.NodePort)
		}
	}
	return 8000
}

func updateNotebookStatus(ctx context.Context, r *Controller, nb *v1alpha1.Notebook,
	sts *appsv1.StatefulSet, pod *corev1.Pod) error {
	status := nb.Status
	var err error
	status, err = createNotebookStatus(r, nb, sts, pod)
	if err != nil {
		return err
	}
	vscodeAddUrl := nb.Status.VSCodeAccessURL
	jupyterAddUrl := nb.Status.JupyterAccessURL
	status.VSCodeReady = reconcilehelper.HTTPGetCheck(vscodeAddUrl, 10*time.Second)
	status.JupyterReady = reconcilehelper.HTTPGetCheck(jupyterAddUrl, 10*time.Second)

	// If the status is the same, do not update
	if cmp.Equal(nb.Status, status, cmpopts.IgnoreFields(v1alpha1.NotebookCondition{}, "LastProbeTime", "LastTransitionTime", "Message")) {
		klog.Infof("Notebook %s status is the same, no need to update", nb.Name)
		return nil
	}

	return retry.RetryOnConflict(retry.DefaultRetry, func() error {
		latest := &v1alpha1.Notebook{}
		if err = r.Get(ctx, types.NamespacedName{Namespace: nb.Namespace, Name: nb.Name}, latest); err != nil {
			return err
		}
		latest.Status = status
		return r.Status().Update(ctx, latest)
	})
}

func updateNotebookStatusForShutdown(ctx context.Context, r *Controller, nb *v1alpha1.Notebook) error {
	targetCluster := multicluster.Instance().GetCluster(nb.Spec.Cluster)
	if targetCluster == nil {
		return fmt.Errorf("target cluster %s not found", nb.Spec.Cluster)
	}
	status := nb.Status
	status = v1alpha1.NotebookStatus{
		Conditions:     make(v1alpha1.NotebookConditions, 0),
		ContainerState: corev1.ContainerState{},
	}

	foundSts := &appsv1.StatefulSet{}
	err := targetCluster.Direct().Get(ctx, types.NamespacedName{Name: nb.Name, Namespace: nb.Namespace}, foundSts)
	if err != nil && apierrs.IsNotFound(err) {
		podList := &corev1.PodList{}
		labelSelector := client.MatchingLabels{"kic-aistudio.kcs.com/dev-machine-name": nb.Name}
		listErr := targetCluster.Direct().List(ctx, podList, client.InNamespace(nb.Namespace), labelSelector)
		if listErr != nil {
			return listErr
		}
		if len(podList.Items) == 0 {
			status.ReadyReplicas = 0
		}
	} else if err != nil {
		return err
	}

	return retry.RetryOnConflict(retry.DefaultRetry, func() error {
		latest := &v1alpha1.Notebook{}
		if err = r.Get(ctx, types.NamespacedName{Namespace: nb.Namespace, Name: nb.Name}, latest); err != nil {
			return err
		}
		latest.Status = status
		return r.Status().Update(ctx, latest)
	})
}

func cronUpdateNotebookStatus(ctx context.Context, r *Controller, req ctrl.Request) error {
	klog.Infof("[Notebook] cron updating notebook cr status,in namespace: %s, name: %s", req.Namespace, req.Name)
	err := retry.RetryOnConflict(retry.DefaultRetry, func() error {
		instance := &v1alpha1.Notebook{}
		if err := r.Get(ctx, req.NamespacedName, instance); err != nil {
			klog.Errorf("Error getting Notebook %s: %v", req.Name, err)
			return err
		}
		status := instance.Status
		vscodeAddUrl := status.VSCodeAccessURL
		jupyterAddUrl := status.JupyterAccessURL
		status.VSCodeReady = reconcilehelper.HTTPGetCheck(vscodeAddUrl, 10*time.Second)
		status.JupyterReady = reconcilehelper.HTTPGetCheck(jupyterAddUrl, 10*time.Second)

		if reflect.DeepEqual(instance.Status, status) {
			return nil
		}
		instance.Status = status
		return r.Status().Update(ctx, instance)
	})
	if err != nil {
		klog.Errorf("[Notebook] Error Cron updating Notebook CR Status: %v", err)
	}
	return nil
}

func getNodeIPByK8sNode(k8sNode *corev1.Node) string {
	for _, address := range k8sNode.Status.Addresses {
		if address.Type == corev1.NodeInternalIP {
			return address.Address
		}
	}
	return k8sNode.Name
}

func createNotebookStatus(r *Controller, nb *v1alpha1.Notebook,
	sts *appsv1.StatefulSet, pod *corev1.Pod) (v1alpha1.NotebookStatus, error) {
	// Initialize Notebook CR Status
	status := v1alpha1.NotebookStatus{
		Conditions:       make([]v1alpha1.NotebookCondition, 0),
		ReadyReplicas:    sts.Status.ReadyReplicas,
		ContainerState:   corev1.ContainerState{},
		VSCodeAccessURL:  nb.Status.VSCodeAccessURL,
		JupyterAccessURL: nb.Status.JupyterAccessURL,
		VSCodeReady:      false,
		JupyterReady:     false,
		NodeName:         pod.Spec.NodeName,
		NodeIP:           pod.Status.HostIP,
		PodName:          pod.GetName(),
	}

	// Update the status based on the Pod's status
	if reflect.DeepEqual(pod.Status, corev1.PodStatus{}) {
		klog.Infof("[Notebook]No pod.Status found. Won't update notebook conditions and containerState")
		return status, nil
	}

	// Update status of the CR using the ContainerState of
	// the container that has the same name as the CR.
	// If no container of same name is found, the state of the CR is not updated.
	notebookContainerFound := false
	for i := range pod.Status.ContainerStatuses {
		if pod.Status.ContainerStatuses[i].Name != nb.Name {
			continue
		}

		if pod.Status.ContainerStatuses[i].State == nb.Status.ContainerState {
			continue
		}
		// Update Notebook CR's status.ContainerState
		cs := pod.Status.ContainerStatuses[i].State
		status.ContainerState = cs
		notebookContainerFound = true
		break
	}

	if !notebookContainerFound {
		klog.Errorf("[Notebook] No notebook container found with name %s in Pod %s", nb.Name, pod.Name)
	}

	var notebookConditions []v1alpha1.NotebookCondition
	for i := range pod.Status.Conditions {
		condition := PodCondToNotebookCond(pod.Status.Conditions[i])
		notebookConditions = append(notebookConditions, condition)
	}

	status.Conditions = notebookConditions
	return status, nil
}

func PodCondToNotebookCond(podc corev1.PodCondition) v1alpha1.NotebookCondition {

	condition := v1alpha1.NotebookCondition{}

	if len(podc.Type) > 0 {
		condition.Type = string(podc.Type)
	}

	if len(podc.Status) > 0 {
		condition.Status = string(podc.Status)
	}

	if len(podc.Message) > 0 {
		condition.Message = podc.Message
	}

	if len(podc.Reason) > 0 {
		condition.Reason = podc.Reason
	}

	// check if podc.LastProbeTime is null. If so initialize
	// the field with metav1.Now()
	check := podc.LastProbeTime.Time.Equal(time.Time{})
	if !check {
		condition.LastProbeTime = podc.LastProbeTime
	} else {
		condition.LastProbeTime = metav1.Now()
	}

	// check if podc.LastTransitionTime is null. If so initialize
	// the field with metav1.Now()
	check = podc.LastTransitionTime.Time.Equal(time.Time{})
	if !check {
		condition.LastTransitionTime = podc.LastTransitionTime
	} else {
		condition.LastTransitionTime = metav1.Now()
	}
	return condition
}

func setPrefixEnvVar(instance *v1alpha1.Notebook, container *corev1.Container) {
	workspacesName := strings.TrimPrefix(instance.Namespace, "aistudio-")
	prefix := fmt.Sprintf("/notebook/proxy/workspace/%s/dev-machine/%s", workspacesName, instance.Name)

	for _, envVar := range container.Env {
		if envVar.Name == PrefixEnvVar {
			envVar.Value = prefix
			return
		}
	}

	container.Env = append(container.Env, corev1.EnvVar{
		Name:  PrefixEnvVar,
		Value: prefix,
	})
	// 设置 ssh的环境变量
	sshConfig := instance.Spec.SSHConfigSpec
	if sshConfig.SSHEnabled {
		container.Env = append(container.Env, corev1.EnvVar{
			Name:  "SSH_ENABLED",
			Value: "true",
		})
		if sshConfig.SSHUser != "" {
			container.Env = append(container.Env, corev1.EnvVar{
				Name:  "SSH_USER",
				Value: sshConfig.SSHUser,
			})
		}
		if sshConfig.SSHPassword != "" {
			container.Env = append(container.Env, corev1.EnvVar{
				Name:  "SSH_PASSWORD",
				Value: sshConfig.SSHPassword,
			})
		}
		if sshConfig.SSHPort != 0 {
			container.Env = append(container.Env, corev1.EnvVar{
				Name:  "SSH_PORT",
				Value: strconv.Itoa(int(sshConfig.SSHPort)),
			})
		}

	}

	// 设置集群的环境变量
	if instance.Spec.Cluster != "" {
		container.Env = append(container.Env, corev1.EnvVar{
			Name:  "CLUSTER",
			Value: instance.Spec.Cluster,
		})
	}
}

func generateStatefulSet(instance *v1alpha1.Notebook) *appsv1.StatefulSet {
	replicas := instance.Spec.Replicas
	ss := &appsv1.StatefulSet{
		ObjectMeta: metav1.ObjectMeta{
			Name:      instance.Name,
			Namespace: instance.Namespace,
			Labels: map[string]string{
				constant.KICManagedLabelKey:   "kic",
				"automl.kcs.io/notebook-name": instance.Name,
			},
		},
		Spec: appsv1.StatefulSetSpec{
			Replicas: replicas,
			Selector: &metav1.LabelSelector{
				MatchLabels: map[string]string{
					"automl.kcs.io/notebook-name": instance.Name,
				},
			},
			Template: corev1.PodTemplateSpec{
				ObjectMeta: metav1.ObjectMeta{
					Labels: map[string]string{
						"automl.kcs.io/notebook-name": instance.Name,
						constant.KICManagedLabelKey:   "kic",
					},
					Annotations: map[string]string{},
				},
				Spec: *instance.Spec.Template.Spec.DeepCopy(),
			},
			PodManagementPolicy: appsv1.OrderedReadyPodManagement,
			UpdateStrategy: appsv1.StatefulSetUpdateStrategy{
				Type: appsv1.RollingUpdateStatefulSetStrategyType,
				RollingUpdate: &appsv1.RollingUpdateStatefulSetStrategy{
					Partition: replicas,
				},
			},
		},
	}

	// copy all of the Notebook labels to the pod including poddefault related labels
	l := &ss.Spec.Template.ObjectMeta.Labels
	for k, v := range instance.ObjectMeta.Labels {
		(*l)[k] = v
	}

	// copy all of the Notebook annotations to the pod.
	a := &ss.Spec.Template.ObjectMeta.Annotations
	for k, v := range instance.ObjectMeta.Annotations {
		if !strings.Contains(k, "kubectl") && !strings.Contains(k, "notebook") {
			(*a)[k] = v
		}
	}

	podSpec := &ss.Spec.Template.Spec
	container := &podSpec.Containers[0]
	if container.WorkingDir == "" {
		container.WorkingDir = "/home/<USER>"
	}
	if container.Ports == nil {
		container.Ports = []corev1.ContainerPort{
			{
				ContainerPort: DefaultNotebookPort,
				Name:          "notebook-port",
				Protocol:      "TCP",
			},
			{
				ContainerPort: DefaultCodeServerPort,
				Name:          "coder-port",
				Protocol:      "TCP",
			},
		}
	}
	setSSHVolumes(instance, podSpec, container)
	setPrefixEnvVar(instance, container)
	// For some platforms (like OpenShift), adding fsGroup: 100 is troublesome.
	// This allows for those platforms to bypass the automatic addition of the fsGroup
	// and will allow for the Pod Security Policy controller to make an appropriate choice
	// https://github.com/kubernetes-sigs/controller-runtime/issues/4617
	if value, exists := os.LookupEnv("ADD_FSGROUP"); !exists || value == "true" {
		if podSpec.SecurityContext == nil {
			fsGroup := DefaultFSGroup
			podSpec.SecurityContext = &corev1.PodSecurityContext{
				FSGroup: &fsGroup,
			}
		}
	}
	return ss
}

func setSSHVolumes(instance *v1alpha1.Notebook, podSpec *corev1.PodSpec, container *corev1.Container) {
	if instance.Spec.SSHConfigSpec.SSHEnabled && instance.Spec.SSHConfigSpec.SSHKeys != nil && len(instance.Spec.SSHConfigSpec.SSHKeys) > 0 {
		var defaultMode int32 = 0420
		podSpec.Volumes = append(podSpec.Volumes, corev1.Volume{
			Name: "ssh-keys",
			VolumeSource: corev1.VolumeSource{
				ConfigMap: &corev1.ConfigMapVolumeSource{
					LocalObjectReference: corev1.LocalObjectReference{
						Name: instance.Name + "-ssh-keys",
					},
					DefaultMode: &defaultMode,
				},
			},
		})
		container.VolumeMounts = append(container.VolumeMounts, corev1.VolumeMount{
			Name:      "ssh-keys",
			MountPath: "/config/authorized_keys", // 看这个文件manifests/samples/notebook-aicp-init.sh
			SubPath:   "authorized_keys",
			ReadOnly:  false,
		})
	}

}

func generateSSHKeysConfigMap(instance *v1alpha1.Notebook) *corev1.ConfigMap {
	sshKeys := strings.Join(instance.Spec.SSHConfigSpec.SSHKeys, "\n")
	return &corev1.ConfigMap{
		ObjectMeta: metav1.ObjectMeta{
			Name:      generateConfigMapName(instance.Name),
			Namespace: instance.Namespace,
		},
		Data: map[string]string{
			"authorized_keys": sshKeys,
		},
	}
}

func findNodePort(service *corev1.Service, portName string) int32 {
	for _, port := range service.Spec.Ports {
		if port.Name == portName {
			return port.NodePort
		}
	}
	return 0
}

func generateService(instance *v1alpha1.Notebook) *corev1.Service {
	// Define the desired Service object
	svc := &corev1.Service{
		ObjectMeta: metav1.ObjectMeta{
			Name:      instance.Name,
			Namespace: instance.Namespace,
			Labels: map[string]string{
				"automl.kcs.io/notebook-name": instance.Name,
				constant.KICManagedLabelKey:   "kic",
			},
		},
		Spec: corev1.ServiceSpec{
			Type: "ClusterIP",
			Selector: map[string]string{
				"automl.kcs.io/notebook-name": instance.Name,
			},
			Ports: []corev1.ServicePort{
				{
					// Make port name follow Istio pattern so it can be managed by istio rbac
					Name:       DefaultNotebookServiceName,
					Port:       DefaultNotebookPort,
					TargetPort: intstr.FromInt(DefaultNotebookPort),
					Protocol:   "TCP",
				},
				{
					// Make port name follow Istio pattern so it can be managed by istio rbac
					Name:       DefaultVsCodeServiceName,
					Port:       DefaultCodeServerPort,
					TargetPort: intstr.FromInt(DefaultCodeServerPort),
					Protocol:   "TCP",
				},
			},
		},
	}

	return svc
}

func addServiceExposePort(instance *v1alpha1.Notebook, service *corev1.Service) {
	if instance.Spec.ExposePortConfigs != nil {
		for _, exposePortConfig := range instance.Spec.ExposePortConfigs {
			service.Spec.Ports = append(service.Spec.Ports, corev1.ServicePort{
				Name:       exposePortConfig.Name,
				Port:       exposePortConfig.TargetPort,
				TargetPort: intstr.FromInt(int(exposePortConfig.TargetPort)),
				Protocol:   corev1.Protocol(exposePortConfig.Protocol),
				NodePort:   0,
			})
		}
	}
}
