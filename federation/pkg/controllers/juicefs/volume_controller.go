package juicefs

import (
	"bytes"
	"context"
	"crypto/x509"
	"encoding/json"
	"encoding/pem"
	"fmt"
	"git.lianjia.com/cloudnative/kic/kic-platform/federation/pkg/constant"
	"git.lianjia.com/cloudnative/kic/kic-platform/federation/pkg/meta"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/apis/cloudfs.kcs.io/v1alpha1"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/multicluster"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/property"
	"git.lianjia.com/cloudnative/kic/kic-platform/pkg/util"
	kosv1alpha1 "git.lianjia.com/cloudnative/kos/pkg/apis/storage.kcs.io/v1alpha1"
	juicefsmeta "github.com/juicedata/juicefs/pkg/meta"
	"github.com/juicedata/juicefs/pkg/object"
	osync "github.com/juicedata/juicefs/pkg/sync"
	"github.com/juicedata/juicefs/pkg/version"
	workflowv1alpha1 "github.com/kubevela/workflow/api/v1alpha1"
	"github.com/oam-dev/kubevela/apis/core.oam.dev/common"
	kubevelav1beta1 "github.com/oam-dev/kubevela/apis/core.oam.dev/v1beta1"
	kubevelautil "github.com/oam-dev/kubevela/pkg/oam/util"
	"io"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/equality"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/client-go/tools/record"
	"k8s.io/client-go/util/retry"
	"k8s.io/klog/v2"
	"math/rand"
	"net/http"
	"net/url"
	"os"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/builder"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/controller/controllerutil"
	"sigs.k8s.io/controller-runtime/pkg/handler"
	"sigs.k8s.io/controller-runtime/pkg/log"
	"sigs.k8s.io/controller-runtime/pkg/manager"
	"sigs.k8s.io/controller-runtime/pkg/predicate"
	"sigs.k8s.io/controller-runtime/pkg/reconcile"
	"sigs.k8s.io/controller-runtime/pkg/source"
	"strconv"
	"strings"
	"time"
)

const (
	DefaultKosProxyURL = "http://kos-proxy.kcs.ke.com"
	LastModifiedKey    = "cloudfs.kcs.io/latest-modified"
)

const (
	OceanBaseUserKey     string = "ob-user"
	OceanBasePasswordKey string = "ob-password"
	DefaultCSIDriverName string = "csi.juicefs.com"
)

type VolumeController struct {
	client             client.Client
	metaManager        meta.Manager
	defaultCloudBucket string //云存储绑定的bucket
	recorder           record.EventRecorder
}

func (c *VolumeController) Reconcile(ctx context.Context, request ctrl.Request) (ctrl.Result, error) {
	_ = log.FromContext(ctx)
	volume := &v1alpha1.JuiceFSVolume{}
	err := c.client.Get(ctx, request.NamespacedName, volume)
	if err != nil {
		if apierrors.IsNotFound(err) {
			klog.Info("not found virtualBucket:" + request.NamespacedName.String())
			return ctrl.Result{}, nil
		}
		return ctrl.Result{}, err
	}
	if !volume.DeletionTimestamp.IsZero() {
		if err := c.handleFinalizer(ctx, volume); err != nil {
			return ctrl.Result{}, fmt.Errorf("error when handling finalizer: %v", err)
		}
		return ctrl.Result{}, nil
	}
	//只处理删除, 真正删除之前，需要做一些手动回收的处理，目前只标记为Terminating
	if !volume.HasFinalizer(constant.AppFinalizer) {
		if err := c.addFinalizer(ctx, volume); err != nil {
			return ctrl.Result{}, fmt.Errorf("error adding finalizer: %v", err)
		}
	}
	//如果Volume的状态为空，说明是新创建的Volume,那么走新建流程，优先创建元数据引擎
	if volume.Status.State == "" {
		return c.initVolume(ctx, volume)
	}
	//判断Volume的定义是否发生变化，如果发生变化，那么需要更新卷的相关信息
	if v, ok := volume.Annotations[LastModifiedKey]; ok {
		spec := v1alpha1.JuiceFSVolumeSpec{}
		err = json.Unmarshal([]byte(v), &spec)
		if err != nil {
			return ctrl.Result{}, err
		}
		if !equality.Semantic.DeepEqual(&volume.Spec, &spec) {
			err = retry.RetryOnConflict(retry.DefaultRetry, func() error {
				err = c.client.Get(ctx, client.ObjectKey{Namespace: volume.Namespace, Name: volume.Name}, volume)
				if err != nil {
					return err
				}
				if equality.Semantic.DeepEqual(volume.Spec.SyncClusters, spec.SyncClusters) {
					volume.Status.State = v1alpha1.VolumeStatePending
					volume.Status.Conditions.UpsertCondition(metav1.Condition{
						Type:               v1alpha1.JuiceFSVolumeConditionClusterSyncReady,
						Status:             metav1.ConditionFalse,
						LastTransitionTime: metav1.Now(),
						Reason:             "ClusterSyncChanged",
						Message:            "Sync Cluster List Changed",
					})
					volume.Status.Conditions.UpsertCondition(metav1.Condition{
						Type:               v1alpha1.JuiceFSVolumeConditionSyncPVReady,
						Status:             metav1.ConditionFalse,
						LastTransitionTime: metav1.Now(),
						Reason:             "ClusterSyncChanged",
						Message:            "Sync Cluster List Changed",
					})
					volume.Status.Conditions.UpsertCondition(metav1.Condition{
						Type:               v1alpha1.JuiceFSVolumeConditionSyncPVCReady,
						Status:             metav1.ConditionFalse,
						LastTransitionTime: metav1.Now(),
						Reason:             "ClusterSyncChanged",
						Message:            "Sync Cluster List Changed",
					})
				}
				if equality.Semantic.DeepEqual(volume.Spec.Limit, spec.Limit) {
					volume.Status.State = v1alpha1.VolumeStatePending
					volume.Status.Conditions.UpsertCondition(metav1.Condition{
						Type:               v1alpha1.JuiceFSVolumeConditionFormatReady,
						Status:             metav1.ConditionFalse,
						LastTransitionTime: metav1.Now(),
						Reason:             "LimitChanged",
						Message:            "Limit Changed",
					})
				}
				if equality.Semantic.DeepEqual(volume.Spec.BucketConfig, spec.BucketConfig) {
					volume.Status.State = v1alpha1.VolumeStatePending
					volume.Status.Conditions.UpsertCondition(metav1.Condition{
						Type:               v1alpha1.JuiceFSVolumeConditionBucketReady,
						Status:             metav1.ConditionFalse,
						LastTransitionTime: metav1.Now(),
						Reason:             "BucketConfigChanged",
						Message:            "Bucket Config Changed",
					})
				}

				data, err := json.Marshal(volume.Spec)
				if err != nil {
					return fmt.Errorf("failed to marshal volume spec: %v", err)
				}
				volume.Annotations[LastModifiedKey] = string(data)
				return c.client.Update(ctx, volume)
			})
		}
	}
	//如果Volume的状态是Pending，那么需要判断元数据引擎是否已经准备好,如果没有准备好，那么去创建元数据引擎
	if volume.Status.State == v1alpha1.VolumeStatePending && c.VolumeMetaNotReady(volume) {
		err = c.createVolumeMeta(ctx, volume)
		if err != nil {
			volume.Status.Reason = "VolumeMetaCreateFailed"
			volume.Status.Message = err.Error()
			volume.Status.Conditions.UpsertCondition(metav1.Condition{
				Type:               v1alpha1.JuiceFSVolumeConditionMetaReady,
				Status:             metav1.ConditionFalse,
				LastTransitionTime: metav1.Now(),
				Reason:             "VolumeMetaCreateFailed",
				Message:            err.Error(),
			})
			return ctrl.Result{}, c.client.Status().Update(ctx, volume)
		}
		volume.Status.Conditions.UpsertCondition(metav1.Condition{
			Type:               v1alpha1.JuiceFSVolumeConditionMetaReady,
			Status:             metav1.ConditionTrue,
			LastTransitionTime: metav1.Now(),
			Reason:             "VolumeMetaCreateSuccess",
			Message:            "",
		})
		return ctrl.Result{}, c.client.Status().Update(ctx, volume)
	}

	if volume.Status.State == v1alpha1.VolumeStatePending && c.VolumeBucketNotReady(volume) {
		virtualBucket := kosv1alpha1.VirtualBucket{}
		err = c.client.Get(ctx, client.ObjectKey{Name: fmt.Sprintf("%s-%s", volume.Namespace, volume.Name)}, &virtualBucket)
		if err != nil {
			if apierrors.IsNotFound(err) {
				err = c.createBucket(ctx, volume)
				if err != nil {
					volume.Status.Reason = "VolumeBucketCreateFailed"
					volume.Status.Message = err.Error()
					volume.Status.Conditions.UpsertCondition(metav1.Condition{
						Type:               v1alpha1.JuiceFSVolumeConditionBucketReady,
						Status:             metav1.ConditionFalse,
						LastTransitionTime: metav1.Now(),
						Reason:             "VolumeBucketCreateFailed",
						Message:            err.Error(),
					})
					return ctrl.Result{}, c.client.Status().Update(ctx, volume)
				}
			}
			return ctrl.Result{}, err
		}
		if virtualBucket.Status.State != kosv1alpha1.VirtualBucketReady {
			volume.Status.Reason = "VolumeBucketCreating"
			volume.Status.Message = "KOS VirtualBucket is creating"
			volume.Status.LastTransitionTime = metav1.Now()
			return ctrl.Result{}, c.client.Status().Update(ctx, volume)
		} else {
			volume.Status.Conditions.UpsertCondition(metav1.Condition{
				Type:               v1alpha1.JuiceFSVolumeConditionBucketReady,
				Status:             metav1.ConditionTrue,
				LastTransitionTime: metav1.Now(),
				Reason:             "VolumeBucketCreateSuccess",
				Message:            "",
			})
			return ctrl.Result{}, c.client.Status().Update(ctx, volume)
		}
	}
	if volume.Status.State == v1alpha1.VolumeStateRunning && c.VolumeFsNotReady(volume) {
		virtualBucket := &kosv1alpha1.VirtualBucket{}
		err = c.client.Get(ctx, client.ObjectKey{Name: fmt.Sprintf("%s-%s", volume.Namespace, volume.Name)}, virtualBucket)
		if err != nil {
			return ctrl.Result{}, err
		}
		//桶和元数据引擎全部创建成功后，需要初始化JuiceFS的文件系统
		err = c.juiceFSFormat(ctx, volume, virtualBucket)
		if err != nil {
			klog.Errorf("juiceFS format failed: %v", err)
			volume.Status.Reason = "JuiceFSFormatFailed"
			volume.Status.Message = err.Error()
			volume.Status.LastTransitionTime = metav1.Now()
			return ctrl.Result{}, c.client.Status().Update(ctx, volume)
		}
	}
	if volume.Status.State != v1alpha1.VolumeStateRunning && c.VolumeSyncClusterNotReady(volume) {
		if volume.Status.SyncStatus.State == "" {
			if len(volume.Spec.SyncClusters) > 0 {
				volume.Status.SyncStatus = v1alpha1.SyncStatus{
					State:                v1alpha1.VolumeSyncClusterStateSyncing,
					LastTransitionTime:   metav1.Now(),
					Message:              "Syncing",
					Reason:               "Syncing",
					ApplicationNamespace: volume.Namespace,
					ApplicationName:      fmt.Sprintf("%s-%s", "juicefs", volume.Name),
				}

			} else {
				volume.Status.SyncStatus = v1alpha1.SyncStatus{
					State:                v1alpha1.VolumeSyncClusterStateCompleted,
					LastTransitionTime:   metav1.Now(),
					Message:              "SyncIgnored",
					Reason:               "SyncIgnored",
					ApplicationNamespace: volume.Namespace,
					ApplicationName:      fmt.Sprintf("%s-%s", "juicefs", volume.Name),
					//Conditions: []metav1.Condition{
					//	{
					//		Type:               v1alpha1.JuiceFSVolumeConditionSyncPVReady,
					//		Status:             metav1.ConditionFalse,
					//		Reason:             "SyncIgnored",
					//		Message:            "SyncIgnored",
					//		LastTransitionTime: metav1.Now(),
					//	},
					//	{
					//		Type:               v1alpha1.JuiceFSVolumeConditionSyncPVCReady,
					//		Status:             metav1.ConditionFalse,
					//		Reason:             "SyncIgnored",
					//		Message:            "SyncIgnored",
					//		LastTransitionTime: metav1.Now(),
					//	},
					//},
				}
			}
			return ctrl.Result{}, c.client.Status().Update(ctx, volume)
		}
		if volume.Status.SyncStatus.State == v1alpha1.VolumeSyncClusterStateSyncing {
			if condition := volume.Status.Conditions.FindCondition(v1alpha1.JuiceFSVolumeConditionSyncPVReady); condition != nil && condition.Status == metav1.ConditionFalse {
				err = c.syncPV(ctx, volume)
				if err != nil {
					klog.Errorf("sync pv failed: %v", err)
					volume.Status.Conditions.UpsertCondition(metav1.Condition{
						Type:               v1alpha1.JuiceFSVolumeConditionSyncPVReady,
						Status:             metav1.ConditionFalse,
						Reason:             "SyncFailed",
						Message:            err.Error(),
						LastTransitionTime: metav1.Now(),
					})
					return ctrl.Result{}, c.client.Status().Update(ctx, volume)
				}
				volume.Status.Conditions.UpsertCondition(metav1.Condition{
					Type:               v1alpha1.JuiceFSVolumeConditionSyncPVReady,
					Status:             metav1.ConditionTrue,
					Reason:             "SyncSuccess",
					Message:            "SyncSuccess",
					LastTransitionTime: metav1.Now(),
				})
				return ctrl.Result{}, c.client.Status().Update(ctx, volume)
			}
			if condition := volume.Status.Conditions.FindCondition(v1alpha1.JuiceFSVolumeConditionSyncPVCReady); condition != nil && condition.Status == metav1.ConditionFalse {
				err = c.syncPVC(ctx, volume)
				if err != nil {
					klog.Errorf("sync pvc failed: %v", err)
					volume.Status.Conditions.UpsertCondition(metav1.Condition{
						Type:               v1alpha1.JuiceFSVolumeConditionSyncPVCReady,
						Status:             metav1.ConditionFalse,
						Reason:             "SyncFailed",
						Message:            err.Error(),
						LastTransitionTime: metav1.Now(),
					})
					return ctrl.Result{}, c.client.Status().Update(ctx, volume)
				}
				volume.Status.Conditions.UpsertCondition(metav1.Condition{
					Type:               v1alpha1.JuiceFSVolumeConditionSyncPVCReady,
					Status:             metav1.ConditionTrue,
					Reason:             "SyncSuccess",
					Message:            "SyncSuccess",
					LastTransitionTime: metav1.Now(),
				})
				return ctrl.Result{}, c.client.Status().Update(ctx, volume)
			}
			if IsJuiceFSVolumeConditionSyncPVReady(volume) && IsJuiceFSVolumeConditionSyncPVCReady(volume) {
				volume.Status.SyncStatus.State = v1alpha1.VolumeSyncClusterStateCompleted
				volume.Status.SyncStatus.LastTransitionTime = metav1.Now()
				volume.Status.SyncStatus.Message = "SyncSuccess"
				volume.Status.SyncStatus.Reason = "SyncSuccess"
				return ctrl.Result{}, c.client.Status().Update(ctx, volume)
			}
		}
		if IsJuiceFSVolumeConditionClusterSyncReady(volume) {
			volume.Status.SyncStatus.State = v1alpha1.VolumeSyncClusterStateCompleted
			volume.Status.SyncStatus.LastTransitionTime = metav1.Now()
			volume.Status.SyncStatus.Message = "SyncSuccess"
			volume.Status.SyncStatus.Reason = "SyncSuccess"
			return ctrl.Result{}, c.client.Status().Update(ctx, volume)
		}

		return ctrl.Result{}, c.client.Status().Update(ctx, volume)
	}
	return ctrl.Result{}, nil
}

func (c *VolumeController) syncPV(ctx context.Context, volume *v1alpha1.JuiceFSVolume) error {
	metaCluster := multicluster.Instance().GetMetaCluster()
	metaClusterClient, err := metaCluster.GetClient()
	if err != nil {
		return err
	}
	volSecret := &corev1.Secret{}
	err = metaClusterClient.Direct().Get(ctx, types.NamespacedName{Name: volume.Status.SecretRef, Namespace: volume.Namespace}, volSecret)
	if err != nil {
		if apierrors.IsNotFound(err) {
			return fmt.Errorf("secret %s not found", volume.Status.SecretRef)
		}
		return err
	}
	pv := &corev1.PersistentVolume{
		ObjectMeta: metav1.ObjectMeta{
			Name: fmt.Sprintf("%s-%s", volume.Name, "cfs-vol"),
			Labels: map[string]string{
				"cloudfs.kcs.io/volume-name": volume.Name,
				"cloudfs.kcs.io/workspace":   volume.Namespace,
			},
		},
		Spec: corev1.PersistentVolumeSpec{
			Capacity: corev1.ResourceList{
				corev1.ResourceStorage: resource.MustParse(fmt.Sprintf("%dGi", volume.Spec.Limit.QuotaLimit.Capacity)),
			},
			AccessModes: []corev1.PersistentVolumeAccessMode{
				corev1.ReadWriteMany,
			},
			StorageClassName:              DefaultCSIDriverName,
			PersistentVolumeReclaimPolicy: corev1.PersistentVolumeReclaimRetain,
			PersistentVolumeSource: corev1.PersistentVolumeSource{
				CSI: &corev1.CSIPersistentVolumeSource{
					Driver:           DefaultCSIDriverName,
					VolumeAttributes: map[string]string{},
					VolumeHandle:     volume.Name,
				},
			},
		},
	}
	application := &kubevelav1beta1.Application{
		ObjectMeta: metav1.ObjectMeta{
			Name:      fmt.Sprintf("%s-%s", volume.Name, "cfs-vol"),
			Namespace: volume.Namespace,
		},
		Spec: kubevelav1beta1.ApplicationSpec{
			Components: []common.ApplicationComponent{
				{
					Name:       fmt.Sprintf("%s-%s", volume.Name, "cfs-vol"),
					Type:       "k8s-objects",
					Properties: kubevelautil.Object2RawExtension(pv),
				},
			},
			Policies: []kubevelav1beta1.AppPolicy{
				{
					Name: "create-pv-topology",
					Type: "topology",
					Properties: kubevelautil.Object2RawExtension(map[string]interface{}{
						"clusters":  volume.Spec.SyncClusters,
						"namespace": volume.Namespace,
					}),
				},
			},
			Workflow: &kubevelav1beta1.Workflow{
				Steps: []workflowv1alpha1.WorkflowStep{
					{
						WorkflowStepBase: workflowv1alpha1.WorkflowStepBase{
							Name:       "create-pv",
							Type:       "deploy",
							Properties: kubevelautil.Object2RawExtension([]string{"create-pv-topology"}),
						},
					},
				},
			},
		},
	}
	result, err := controllerutil.CreateOrUpdate(ctx, metaClusterClient.Direct(), application, func() error {
		return nil
	})
	if err != nil {
		return err
	}
	klog.Infof("sync pv success, vol:%s, operation:%s", pv.Name, result)
	return nil
}

func (c *VolumeController) syncPVC(ctx context.Context, volume *v1alpha1.JuiceFSVolume) error {
	metaCluster := multicluster.Instance().GetMetaCluster()
	metaClusterClient, err := metaCluster.GetClient()
	if err != nil {
		return err
	}
	//直接去目标集群去创建对应的PVC，不依赖分发组件了。
	pvc := corev1.PersistentVolumeClaim{
		ObjectMeta: metav1.ObjectMeta{
			Name: volume.Name,
		},
		Spec: corev1.PersistentVolumeClaimSpec{
			AccessModes: []corev1.PersistentVolumeAccessMode{
				corev1.ReadWriteMany,
			},
			Selector: &metav1.LabelSelector{
				MatchLabels: map[string]string{
					"cloudfs.kcs.io/volume-name": volume.Name,
					"cloudfs.kcs.io/workspace":   volume.Namespace,
				},
			},
			Resources: corev1.ResourceRequirements{
				Requests: corev1.ResourceList{
					corev1.ResourceStorage: resource.MustParse(fmt.Sprintf("%dGi", volume.Spec.Limit.QuotaLimit.Capacity)),
				},
			},
			VolumeName: fmt.Sprintf("%s-%s", volume.Name, "cfs-vol"),
		},
	}
	application := &kubevelav1beta1.Application{
		ObjectMeta: metav1.ObjectMeta{
			Name:      fmt.Sprintf("%s-%s", volume.Name, "cfs-vol"),
			Namespace: volume.Namespace,
		},
		Spec: kubevelav1beta1.ApplicationSpec{
			Components: []common.ApplicationComponent{
				{
					Name:       fmt.Sprintf("%s-%s", volume.Name, "cfs-vol"),
					Type:       "k8s-objects",
					Properties: kubevelautil.Object2RawExtension(pvc),
				},
			},
			Policies: []kubevelav1beta1.AppPolicy{
				{
					Name: "create-pv-topology",
					Type: "topology",
					Properties: kubevelautil.Object2RawExtension(map[string]interface{}{
						"clusters":  volume.Spec.SyncClusters,
						"namespace": volume.Namespace,
					}),
				},
			},
			Workflow: &kubevelav1beta1.Workflow{
				Steps: []workflowv1alpha1.WorkflowStep{
					{
						WorkflowStepBase: workflowv1alpha1.WorkflowStepBase{
							Name:       "create-pv",
							Type:       "deploy",
							Properties: kubevelautil.Object2RawExtension([]string{"create-pv-topology"}),
						},
					},
				},
			},
		},
	}
	result, err := controllerutil.CreateOrUpdate(ctx, metaClusterClient.Direct(), application, func() error {
		return nil
	})
	if err != nil {
		return err
	}
	klog.Infof("sync pv success, vol:%s, operation:%s", pvc.Name, result)
	return nil
}

func (c *VolumeController) initVolume(ctx context.Context, volume *v1alpha1.JuiceFSVolume) (ctrl.Result, error) {
	secret := corev1.Secret{
		ObjectMeta: metav1.ObjectMeta{
			Name:      c.secretName(volume),
			Namespace: volume.Namespace,
			OwnerReferences: []metav1.OwnerReference{
				{
					APIVersion: volume.APIVersion,
					Kind:       volume.Kind,
					UID:        volume.UID,
					Name:       volume.Name,
				},
			},
		},
	}
	//创建秘钥token
	result, err := controllerutil.CreateOrUpdate(ctx, c.client, &secret, func() error {
		return nil
	})
	if err != nil {
		return ctrl.Result{Requeue: true}, err
	}
	klog.Info("create secret[%s:%s] result:", secret.Namespace, secret.Name, result)
	volume.Status.State = v1alpha1.VolumeStatePending
	volume.Status.Conditions.UpsertCondition(metav1.Condition{
		Type:               v1alpha1.JuiceFSVolumeConditionMetaReady,
		Status:             metav1.ConditionFalse,
		LastTransitionTime: metav1.Now(),
		Reason:             "Initializing",
		Message:            "元数据引擎初始化中",
	})
	volume.Status.Conditions.UpsertCondition(metav1.Condition{
		Type:               v1alpha1.JuiceFSVolumeConditionBucketReady,
		Status:             metav1.ConditionFalse,
		LastTransitionTime: metav1.Now(),
		Reason:             "Initializing",
		Message:            "Bucket初始化中",
	})
	volume.Status.Conditions.UpsertCondition(metav1.Condition{
		Type:               v1alpha1.JuiceFSVolumeConditionFormatReady,
		Status:             metav1.ConditionFalse,
		LastTransitionTime: metav1.Now(),
		Reason:             "Initializing",
		Message:            "文件系统初始化中",
	})
	volume.Status.Reason = "Initializing"
	volume.Status.Message = "Volume初始化中"
	volume.Status.LastTransitionTime = metav1.Now()
	volume.Status.SecretRef = secret.Name
	err = c.client.Status().Update(ctx, volume)
	if err != nil {
		return ctrl.Result{}, err
	}
	return ctrl.Result{}, nil
}

func (c *VolumeController) createVolumeMeta(ctx context.Context, volume *v1alpha1.JuiceFSVolume) error {
	metaCluster := &v1alpha1.JuiceFSMetaCluster{}
	if err := c.client.Get(ctx, client.ObjectKey{Name: volume.Spec.MetaClusterRef}, metaCluster); err != nil {
		if apierrors.IsNotFound(err) {
			volume.Status.State = v1alpha1.VolumeStateFailed
			volume.Status.Conditions.UpsertCondition(metav1.Condition{
				Type:               v1alpha1.JuiceFSVolumeConditionMetaReady,
				Status:             metav1.ConditionFalse,
				LastTransitionTime: metav1.Now(),
				Reason:             fmt.Sprintf("MetaCluster[%s]NotFound", volume.Spec.MetaClusterRef),
				Message:            fmt.Sprintf("元数据引擎集群[%s]不存在", volume.Spec.MetaClusterRef),
			})
			volume.Status.LastTransitionTime = metav1.Now()
		}
	}
	metaEngine, err := c.metaManager.GetEngine(volume.Spec.Region, volume.Spec.MetaEngine)
	if err != nil {
		return err
	}
	err = metaEngine.CreateVolumeMeta(ctx, volume.Namespace, volume.Name)
	if err != nil {
		return err
	}
	username := util.GenerateKey(16)
	password := util.GenerateKey(32)
	err = metaEngine.GrantPrivileges(ctx, username, password, volume.Namespace, volume.Name, volume.Spec.Region)
	if err != nil {
		return err
	}
	var secret = &corev1.Secret{
		ObjectMeta: metav1.ObjectMeta{
			Name:      c.secretName(volume),
			Namespace: volume.Namespace,
		},
	}
	err = c.client.Get(ctx, client.ObjectKey{Namespace: volume.Namespace, Name: c.secretName(volume)}, secret)
	if err != nil {
		return err
	}
	metaUrl := metaEngine.GenerateMetaURL(ctx, username, password, volume.Namespace, volume.Name)
	secret.StringData["metaurl"] = metaUrl
	secret.StringData["name"] = fmt.Sprintf("%s-%s", volume.Namespace, volume.Name)
	return c.client.Update(ctx, secret)
}

func (c *VolumeController) createBucket(ctx context.Context, volume *v1alpha1.JuiceFSVolume) error {
	var cloudBucket string
	if volume.Spec.BucketConfig.CloudBucket == "" {
		cloudBucket = c.defaultCloudBucket
	} else {
		cloudBucket = volume.Spec.BucketConfig.CloudBucket
	}
	//先创建ReplicationTracker资源
	replicationTracker := kosv1alpha1.ReplicationTracker{
		ObjectMeta: metav1.ObjectMeta{
			Name: fmt.Sprintf("%s-%s", volume.Namespace, volume.Name),
		},
		Spec: kosv1alpha1.ReplicationTrackerSpec{
			CloudBucketBindingName: cloudBucket,
			Dir:                    fmt.Sprintf("/%s/%s", volume.Namespace, volume.Name),
		},
	}
	result, err := controllerutil.CreateOrUpdate(ctx, c.client, &replicationTracker, func() error {
		return nil
	})
	if err != nil {
		return err
	}
	klog.Infof("create ReplicationTracker[%s:%s] result: %v", replicationTracker.Namespace, replicationTracker.Name, result)
	//创建bucket, 直接创建KOS的VirtualBucket即可
	virtualBucket := kosv1alpha1.VirtualBucket{
		ObjectMeta: metav1.ObjectMeta{
			Name: fmt.Sprintf("%s-%s", volume.Namespace, volume.Name),
			OwnerReferences: []metav1.OwnerReference{
				{
					APIVersion: volume.APIVersion,
					Kind:       volume.Kind,
					UID:        volume.UID,
					Name:       volume.Name,
				},
			},
		},
		Spec: kosv1alpha1.VirtualBucketSpec{
			Description: fmt.Sprintf("JuiceFS Volume %s", volume.Name),
			BackendType: func(i kosv1alpha1.BackendType) *kosv1alpha1.BackendType { return &i }(kosv1alpha1.BackendDirect),
			Backend: &kosv1alpha1.Backend{
				Direct: &kosv1alpha1.DirectBackend{
					ReplicationName: replicationTracker.Name,
				},
			},
			CacheAcceleration:   volume.Spec.BucketConfig.DistributionCache,
			MimeTransferEnabled: volume.Spec.BucketConfig.MimeTransferEnabled,
		},
	}
	result, err = controllerutil.CreateOrUpdate(ctx, c.client, &virtualBucket, func() error {
		return nil
	})
	if err != nil {
		return err
	}
	klog.Infof("create VirtualBucket[%s:%s] result: %v", virtualBucket.Namespace, virtualBucket.Name, result)
	var secret = &corev1.Secret{}
	err = c.client.Get(ctx, client.ObjectKey{Namespace: volume.Namespace, Name: c.secretName(volume)}, secret)
	if err != nil {
		return err
	}
	//设置bucketURL, 是否需要根据场景区别一下代理的域名
	//对于kos-proxy，需要遵循一定的规则, 例如: kos://wq.ap-beijing.kos.ke.com, kos://tz.ap-beijing.kos.ke.com
	//默认规则: kos://<zone>.ap-<region>.kos.ke.com
	bucketURL := fmt.Sprintf("kos://%s", fmt.Sprintf("%s.ap-%s.kos.ke.com", volume.Spec.Zone, volume.Spec.Region))
	secret.StringData["bucket"] = bucketURL
	secret.StringData["access-key"] = virtualBucket.Status.AccessKey
	secret.StringData["secret-key"] = virtualBucket.Status.SecretKey
	return c.client.Update(ctx, secret)
}

func (c *VolumeController) secretName(volume *v1alpha1.JuiceFSVolume) string {
	return fmt.Sprintf("%s-%s", volume.Name, "-volume-secret")
}

func (c *VolumeController) juiceFSFormat(ctx context.Context, volume *v1alpha1.JuiceFSVolume, virtualBucket *kosv1alpha1.VirtualBucket) error {
	var secret = &corev1.Secret{}
	err := c.client.Get(ctx, client.ObjectKey{Namespace: volume.Namespace, Name: c.secretName(volume)}, secret)
	if err != nil {
		return err
	}
	mysqlUser := secret.Data[OceanBaseUserKey]
	mysqlPassword := secret.Data[OceanBasePasswordKey]
	metaEngine, err := c.metaManager.GetEngine(volume.Spec.Region, volume.Spec.MetaEngine)
	if err != nil {
		return err
	}
	metaUrl := metaEngine.GenerateMetaURL(ctx, string(mysqlUser), string(mysqlPassword), volume.Namespace, volume.Name)
	metaClient := juicefsmeta.NewClient(metaUrl, nil)
	var create bool
	format, err := metaClient.Load(false)
	if err == nil {
		klog.Infof("format[%s:%s] already exists, update it", volume.Namespace, volume.Name)
	} else if strings.HasPrefix(err.Error(), "database is not formatted") {
		format = &juicefsmeta.Format{}
		create = true
	} else {
		return fmt.Errorf("load format failed: %v", err)
	}
	format.Inodes = volume.Spec.Limit.QuotaLimit.Inodes
	format.TrashDays = int(volume.Spec.Limit.TrashDays)
	format.Capacity = volume.Spec.Limit.QuotaLimit.Capacity << 30
	format.AccessKey = virtualBucket.Status.AccessKey
	format.SecretKey = virtualBucket.Status.SecretKey
	format.Bucket = virtualBucket.Name
	format.Shards = 0
	format.Storage = "s3"
	format.Compression = volume.Spec.Compress
	format.HashPrefix = true
	format.Name = fmt.Sprintf("%s-%s", volume.Namespace, volume.Name)
	blob, err := createStorage(*format)
	if err != nil {
		return err
	}
	klog.Infof("format[%s:%s] create storage success, Data use %s", volume.Namespace, volume.Name, blob)
	if os.Getenv("JFS_NO_CHECK_OBJECT_STORAGE") == "" {
		if err := test(blob); err != nil {
			return err
		}
		if create {
			if objs, err := osync.ListAll(blob, "", "", "", true); err == nil {
				for o := range objs {
					if o == nil {
						klog.Warningf("List Storage %s failed", blob)
						break
					} else if o.IsDir() || o.Size() == 0 {
						continue
					} else if o.Key() != "testing" && !strings.HasPrefix(o.Key(), "testing/") {
						return fmt.Errorf("storage %s is not empty; please clean it up or pick another volume name", blob)
					}
				}
			} else {
				klog.Warningf("List Storage %s failed", blob)
			}
			if err = blob.Put("juicefs_uuid", strings.NewReader(format.UUID)); err != nil {
				klog.Warningf("Put uuid object: %s", err)
			}
		}
	}
	if create {
		if err = format.Encrypt(); err != nil {
			return fmt.Errorf("format encrypt: %v", err)
		}
	}
	if err = metaClient.Init(format, false); err != nil {
		if create {
			_ = blob.Delete("juicefs_uuid")
		}
		return fmt.Errorf("init format failed: %v", err)
	}
	klog.Infof("format[%s:%s] init success", volume.Namespace, volume.Name)
	return nil
}

func createStorage(format juicefsmeta.Format) (object.ObjectStorage, error) {
	if err := format.Decrypt(); err != nil {
		return nil, fmt.Errorf("format decrypt: %s", err)
	}
	object.UserAgent = "JuiceFS-" + version.Version()
	var blob object.ObjectStorage
	var err error
	if u, err := url.Parse(format.Bucket); err == nil {
		values := u.Query()
		if values.Get("tls-insecure-skip-verify") != "" {
			var tlsSkipVerify bool
			if tlsSkipVerify, err = strconv.ParseBool(values.Get("tls-insecure-skip-verify")); err != nil {
				return nil, err
			}
			object.GetHttpClient().Transport.(*http.Transport).TLSClientConfig.InsecureSkipVerify = tlsSkipVerify
			values.Del("tls-insecure-skip-verify")
			u.RawQuery = values.Encode()
			format.Bucket = u.String()
		}
	}

	if format.Shards > 1 {
		blob, err = object.NewSharded(strings.ToLower(format.Storage), format.Bucket, format.AccessKey, format.SecretKey, format.SessionToken, format.Shards)
	} else {
		blob, err = object.CreateStorage(strings.ToLower(format.Storage), format.Bucket, format.AccessKey, format.SecretKey, format.SessionToken)
	}
	if err != nil {
		return nil, err
	}
	blob = object.WithPrefix(blob, format.Name+"/")

	if format.EncryptKey != "" {
		passphrase := os.Getenv("JFS_RSA_PASSPHRASE")
		if passphrase == "" {
			block, _ := pem.Decode([]byte(format.EncryptKey))
			// nolint:staticcheck
			if block != nil && strings.Contains(block.Headers["Proc-Type"], "ENCRYPTED") && x509.IsEncryptedPEMBlock(block) {
				return nil, fmt.Errorf("passphrase is required to private key, please try again after setting the 'JFS_RSA_PASSPHRASE' environment variable")
			}
		}

		privKey, err := object.ParseRsaPrivateKeyFromPem([]byte(format.EncryptKey), []byte(passphrase))
		if err != nil {
			return nil, fmt.Errorf("parse rsa: %s", err)
		}
		encryptor, err := object.NewDataEncryptor(object.NewRSAEncryptor(privKey), format.EncryptAlgo)
		if err != nil {
			return nil, err
		}
		blob = object.NewEncrypted(blob, encryptor)
	}
	return blob, nil
}

func doTesting(store object.ObjectStorage, key string, data []byte) error {
	if err := store.Put(key, bytes.NewReader(data)); err != nil {
		if strings.Contains(err.Error(), "Access Denied") {
			return fmt.Errorf("failed to put: %s", err)
		}
		if err2 := store.Create(); err2 != nil {
			if strings.Contains(err.Error(), "NoSuchBucket") {
				return fmt.Errorf("Failed to create bucket %s: %s, previous error: %s\nPlease create bucket %s manually, then format again.",
					store, err2, err, store)
			} else {
				return fmt.Errorf("failed to create bucket %s: %s, previous error: %s",
					store, err2, err)
			}
		}
		if err := store.Put(key, bytes.NewReader(data)); err != nil {
			return fmt.Errorf("failed to put: %s", err)
		}
	}
	p, err := store.Get(key, 0, -1)
	if err != nil {
		return fmt.Errorf("failed to get: %s", err)
	}
	data2, err := io.ReadAll(p)
	_ = p.Close()
	if err != nil {
		return err
	}
	if !bytes.Equal(data, data2) {
		return fmt.Errorf("read wrong data")
	}
	err = store.Delete(key)
	if err != nil {
		// it's OK to don't have delete permission
		fmt.Printf("Failed to delete: %s", err)
	}
	return nil
}

var letters = []rune("abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789")

func randSeq(n int) string {
	b := make([]rune, n)
	r := rand.New(rand.NewSource(time.Now().UnixNano()))
	for i := range b {
		b[i] = letters[r.Intn(len(letters))]
	}
	return string(b)
}

func randRead(buf []byte) {
	if _, err := rand.Read(buf); err != nil {
		klog.Fatalf("Generate random content: %s", err)
	}
}

func test(store object.ObjectStorage) error {
	key := "testing/" + randSeq(10)
	data := make([]byte, 100)
	randRead(data)
	nRetry := 3
	var err error
	for i := 0; i < nRetry; i++ {
		err = doTesting(store, key, data)
		if err == nil {
			break
		}
		time.Sleep(time.Second * time.Duration(i*3+1))
	}
	if err == nil {
		_ = store.Delete("testing/")
	}
	return err
}

func SetupManager(ctx context.Context, manager manager.Manager, properties property.EnvironmentProperty) error {
	recorder := manager.GetEventRecorderFor("juicefs-controller")
	volumeController := &VolumeController{
		client:   manager.GetClient(),
		recorder: recorder,
	}
	return ctrl.NewControllerManagedBy(manager).For(&v1alpha1.JuiceFSVolume{}).
		Watches(&source.Kind{Type: &kosv1alpha1.VirtualBucket{}}, enqueueForVirtualBucket(), builder.WithPredicates(predicate.NewPredicateFuncs(volumeController.hasMatchedVirtualBucket()))).
		Complete(volumeController)
}

func (c *VolumeController) handleFinalizer(ctx context.Context, volume *v1alpha1.JuiceFSVolume) error {
	//todo 处理卷删除
	return nil
}

func (c *VolumeController) addFinalizer(ctx context.Context, volume *v1alpha1.JuiceFSVolume) error {
	volume.AddFinalizer(constant.AppFinalizer)
	return c.client.Update(ctx, volume)
}

func (c *VolumeController) VolumeMetaNotReady(volume *v1alpha1.JuiceFSVolume) bool {
	return volume.Status.Conditions.FindCondition(v1alpha1.JuiceFSVolumeConditionMetaReady).Status == metav1.ConditionFalse
}

func (c *VolumeController) VolumeBucketReady(volume *v1alpha1.JuiceFSVolume) bool {
	return volume.Status.Conditions.FindCondition(v1alpha1.JuiceFSVolumeConditionBucketReady).Status == metav1.ConditionTrue
}

func (c *VolumeController) VolumeSyncClusterReady(volume *v1alpha1.JuiceFSVolume) bool {
	return volume.Status.Conditions.FindCondition(v1alpha1.JuiceFSVolumeConditionClusterSyncReady).Status == metav1.ConditionTrue
}

func (c *VolumeController) VolumeSyncClusterNotReady(volume *v1alpha1.JuiceFSVolume) bool {
	return volume.Status.Conditions.FindCondition(v1alpha1.JuiceFSVolumeConditionClusterSyncReady).Status == metav1.ConditionFalse
}

func (c *VolumeController) VolumeFormatReady(volume *v1alpha1.JuiceFSVolume) bool {
	return volume.Status.Conditions.FindCondition(v1alpha1.JuiceFSVolumeConditionFormatReady).Status == metav1.ConditionTrue
}

func (c *VolumeController) VolumeFsNotReady(volume *v1alpha1.JuiceFSVolume) bool {
	return volume.Status.Conditions.FindCondition(v1alpha1.JuiceFSVolumeConditionFormatReady).Status == metav1.ConditionFalse
}

func (c *VolumeController) VolumeBucketNotReady(volume *v1alpha1.JuiceFSVolume) bool {
	return volume.Status.Conditions.FindCondition(v1alpha1.JuiceFSVolumeConditionBucketReady).Status == metav1.ConditionFalse
}

func enqueueForVirtualBucket() handler.EventHandler {
	return handler.EnqueueRequestsFromMapFunc(func(a client.Object) []reconcile.Request {
		var requests []reconcile.Request
		virtualBucket, ok := a.(*kosv1alpha1.VirtualBucket)
		if ok {
			for _, ref := range virtualBucket.OwnerReferences {
				juicefsVolume := v1alpha1.JuiceFSVolume{}
				if ref.Kind == juicefsVolume.Kind && ref.APIVersion == juicefsVolume.APIVersion {
					parts := strings.Split(ref.Name, "-")
					requests = append(requests, reconcile.Request{
						NamespacedName: types.NamespacedName{
							Namespace: parts[0],
							Name:      parts[1],
						},
					})
				}
			}
		}
		return requests
	})
}

func (c *VolumeController) hasMatchedVirtualBucket() func(obj client.Object) bool {
	return func(obj client.Object) bool {
		virtualBucket, ok := obj.(*kosv1alpha1.VirtualBucket)
		if ok {
			if len(virtualBucket.OwnerReferences) > 0 {
				juicefsVolume := v1alpha1.JuiceFSVolume{}
				for _, ref := range virtualBucket.OwnerReferences {
					if ref.Kind == juicefsVolume.Kind && ref.APIVersion == juicefsVolume.APIVersion {
						return true
					}
				}
			}
		}
		return false
	}
}

func IsJuiceFSVolumeConditionSyncPVCReady(volume *v1alpha1.JuiceFSVolume) bool {
	condition := volume.Status.Conditions.FindCondition(v1alpha1.JuiceFSVolumeConditionSyncPVCReady)
	return condition.Status == metav1.ConditionTrue
}

func IsJuiceFSVolumeConditionSyncPVReady(volume *v1alpha1.JuiceFSVolume) bool {
	condition := volume.Status.Conditions.FindCondition(v1alpha1.JuiceFSVolumeConditionSyncPVReady)
	return condition.Status == metav1.ConditionTrue
}

func IsJuiceFSVolumeConditionClusterSyncReady(volume *v1alpha1.JuiceFSVolume) bool {
	condition := volume.Status.Conditions.FindCondition(v1alpha1.JuiceFSVolumeConditionClusterSyncReady)
	return condition.Status == metav1.ConditionTrue
}

func IsJuiceFSVolumeConditionPVCSyncIgnored(volume *v1alpha1.CubeFSVolume) bool {
	condition := volume.Status.Conditions.FindCondition(v1alpha1.JuiceFSVolumeConditionSyncPVCReady)
	return condition.Status == metav1.ConditionTrue && condition.Reason == "SyncIgnored"
}

func IsJuiceFSVolumeConditionPVSyncIgnored(volume *v1alpha1.CubeFSVolume) bool {
	condition := volume.Status.Conditions.FindCondition(v1alpha1.JuiceFSVolumeConditionSyncPVReady)
	return condition.Status == metav1.ConditionTrue && condition.Reason == "SyncIgnored"
}
