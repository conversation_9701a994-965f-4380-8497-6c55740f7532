# This is a gateway config.
name: helloworld
hosts:
  - localhost
  - 127.0.0.1
middlewares:
  - name: cors
    options:
      '@type': type.googleapis.com/gateway.middleware.cors.v1.Cors
      allowCredentials: true
      allowOrigins:
        - .google.com
      allowMethods:
        - GET
        - POST
        - OPTIONS
  - name: tracing
    options:
      '@type': type.googleapis.com/gateway.middleware.tracing.v1.Tracing
      httpEndpoint: 'localhost:4318' # default opentelemetry collector port
endpoints:
  - path: /helloworld/*
    protocol: HTTP
    timeout: 1s
    backends:
      - target: '127.0.0.1:8000'
  - path: /helloworld.Greeter/*
    method: POST
    protocol: GRPC
    timeout: 1s
    backends:
      - target: '127.0.0.1:9000'
    retry:
      attempts: 3
      perTryTimeout: 0.5s
      conditions:
        - byStatusCode: '502-504'
        - byHeader:
            name: 'Grpc-Status'
            value: '14'
