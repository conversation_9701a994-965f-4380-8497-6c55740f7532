package apis

import (
	"net/http"
	"net/url"
	"sync"
)

var instance *httpClientManager

func init() {
	instance = newHttpClientManager()
}

func GetClientWithProxy(proxyUrl *url.URL) *http.Client {
	return instance.GetClientWithProxy(proxyUrl)
}

func GetDefaultClient() *http.Client {
	return http.DefaultClient
}

type httpClientManager struct {
	clients map[*url.URL]*http.Client
	lock    sync.Mutex
}

func newHttpClientManager() *httpClientManager {
	return &httpClientManager{
		clients: make(map[*url.URL]*http.Client),
	}
}

func (h *httpClientManager) GetClientWithProxy(proxyUrl *url.URL) *http.Client {
	h.lock.Lock()
	defer h.lock.Unlock()
	if client, ok := h.clients[proxyUrl]; ok {
		return client
	}
	client := &http.Client{
		Transport: &http.Transport{
			Proxy: http.ProxyURL(proxyUrl),
		},
	}
	h.clients[proxyUrl] = client
	return client
}
