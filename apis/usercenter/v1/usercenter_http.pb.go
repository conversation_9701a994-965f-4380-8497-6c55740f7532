// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.3
// - protoc             v5.29.3
// source: usercenter/v1/usercenter.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationUserServiceCheckSSHKey = "/apis.usercenter.v1.UserService/CheckSSHKey"
const OperationUserServiceCreateOrUpdateUser = "/apis.usercenter.v1.UserService/CreateOrUpdateUser"
const OperationUserServiceCreateSSHKey = "/apis.usercenter.v1.UserService/CreateSSHKey"
const OperationUserServiceDeleteAccessToken = "/apis.usercenter.v1.UserService/DeleteAccessToken"
const OperationUserServiceDeleteSSHKey = "/apis.usercenter.v1.UserService/DeleteSSHKey"
const OperationUserServiceDeleteUser = "/apis.usercenter.v1.UserService/DeleteUser"
const OperationUserServiceGenerateAccessToken = "/apis.usercenter.v1.UserService/GenerateAccessToken"
const OperationUserServiceGetAccessToken = "/apis.usercenter.v1.UserService/GetAccessToken"
const OperationUserServiceGetHadoopUsers = "/apis.usercenter.v1.UserService/GetHadoopUsers"
const OperationUserServiceGetSSHKeysForUser = "/apis.usercenter.v1.UserService/GetSSHKeysForUser"
const OperationUserServiceGetUser = "/apis.usercenter.v1.UserService/GetUser"
const OperationUserServiceListUsers = "/apis.usercenter.v1.UserService/ListUsers"
const OperationUserServiceSearchUser = "/apis.usercenter.v1.UserService/SearchUser"
const OperationUserServiceUpdateSSHKey = "/apis.usercenter.v1.UserService/UpdateSSHKey"
const OperationUserServiceWhoami = "/apis.usercenter.v1.UserService/Whoami"

type UserServiceHTTPServer interface {
	// CheckSSHKey校验ssh-key
	CheckSSHKey(context.Context, *CheckSSHKeyRequest) (*CheckSSHKeyResponse, error)
	// CreateOrUpdateUser 创建或更新用户
	CreateOrUpdateUser(context.Context, *CreateOrUpdateUserRequest) (*UserDetail, error)
	// CreateSSHKey创建ssh-key
	CreateSSHKey(context.Context, *CreateSSHKeyRequest) (*CreateSSHKeyResponse, error)
	// DeleteAccessToken 删除用户的 accessToken
	DeleteAccessToken(context.Context, *DeleteAccessTokenRequest) (*emptypb.Empty, error)
	// DeleteSSHKey删除ssh-key
	DeleteSSHKey(context.Context, *DeleteSSHKeyRequest) (*DeleteSSHKeyResponse, error)
	// DeleteUser 删除用户
	DeleteUser(context.Context, *DeleteUserRequest) (*emptypb.Empty, error)
	// GenerateAccessToken 生成用户的 accessToken，用户可以生成自己的 accessToken，用户接口调用
	GenerateAccessToken(context.Context, *GenerateAccessTokenRequest) (*emptypb.Empty, error)
	// GetAccessToken 获取用户的 accessToken
	GetAccessToken(context.Context, *GetAccessTokenRequest) (*GetAccessTokenResponse, error)
	// GetHadoopUsers获取hadoop账号
	GetHadoopUsers(context.Context, *GetHadoopUsersRequest) (*GetHadoopUsersResponse, error)
	// GetSSHKeysForUser 获取用户的ssh key
	GetSSHKeysForUser(context.Context, *GetSSHKeysForUserRequest) (*GetSSHKeyResponse, error)
	// GetUser 获取用户
	GetUser(context.Context, *GetUserDetailRequest) (*UserDetail, error)
	// ListUsers 展示用户列表
	ListUsers(context.Context, *ListOptions) (*ListUsersResult, error)
	// SearchUser 搜索用户
	SearchUser(context.Context, *SearchUserRequest) (*SearchUserResult, error)
	// UpdateSSHKey 更新ssh-key，只能更新私钥
	UpdateSSHKey(context.Context, *UpdateSSHKeyRequest) (*UpdateSSHKeyResponse, error)
	// Whoami获取当前用户
	Whoami(context.Context, *WhoamiRequest) (*WhoamiResponse, error)
}

func RegisterUserServiceHTTPServer(s *http.Server, srv UserServiceHTTPServer) {
	r := s.Route("/")
	r.PUT("/apis/v1/user/{account}", _UserService_CreateOrUpdateUser0_HTTP_Handler(srv))
	r.POST("/apis/v1/user", _UserService_CreateOrUpdateUser1_HTTP_Handler(srv))
	r.DELETE("/apis/v1/user/{account}", _UserService_DeleteUser0_HTTP_Handler(srv))
	r.GET("/apis/v1/user/{account}", _UserService_GetUser0_HTTP_Handler(srv))
	r.GET("/apis/v1/users", _UserService_ListUsers0_HTTP_Handler(srv))
	r.GET("/apis/v1/user_search", _UserService_SearchUser0_HTTP_Handler(srv))
	r.GET("/apis/v1/user/{account}/ssh-keys", _UserService_GetSSHKeysForUser0_HTTP_Handler(srv))
	r.POST("/apis/v1/user/{account}/ssh-key", _UserService_CreateSSHKey0_HTTP_Handler(srv))
	r.PUT("/apis/v1/user/{account}/ssh-key/{name}", _UserService_UpdateSSHKey0_HTTP_Handler(srv))
	r.DELETE("/apis/v1/user/{account}/ssh-key/{name}", _UserService_DeleteSSHKey0_HTTP_Handler(srv))
	r.GET("/apis/v1/user/{account}/ssh-key/check", _UserService_CheckSSHKey0_HTTP_Handler(srv))
	r.POST("/apis/v1/user/{account}/access_token", _UserService_GenerateAccessToken0_HTTP_Handler(srv))
	r.GET("/apis/v1/user/{account}/access_token", _UserService_GetAccessToken0_HTTP_Handler(srv))
	r.DELETE("/apis/v1/user/{account}/access_token/{id}", _UserService_DeleteAccessToken0_HTTP_Handler(srv))
	r.GET("/apis/v1/user_whoami", _UserService_Whoami0_HTTP_Handler(srv))
	r.GET("/apis/v1/user/{account}/hadoopusers", _UserService_GetHadoopUsers0_HTTP_Handler(srv))
}

func _UserService_CreateOrUpdateUser0_HTTP_Handler(srv UserServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateOrUpdateUserRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserServiceCreateOrUpdateUser)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateOrUpdateUser(ctx, req.(*CreateOrUpdateUserRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*UserDetail)
		return ctx.Result(200, reply)
	}
}

func _UserService_CreateOrUpdateUser1_HTTP_Handler(srv UserServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateOrUpdateUserRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserServiceCreateOrUpdateUser)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateOrUpdateUser(ctx, req.(*CreateOrUpdateUserRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*UserDetail)
		return ctx.Result(200, reply)
	}
}

func _UserService_DeleteUser0_HTTP_Handler(srv UserServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteUserRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserServiceDeleteUser)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteUser(ctx, req.(*DeleteUserRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _UserService_GetUser0_HTTP_Handler(srv UserServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetUserDetailRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserServiceGetUser)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetUser(ctx, req.(*GetUserDetailRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*UserDetail)
		return ctx.Result(200, reply)
	}
}

func _UserService_ListUsers0_HTTP_Handler(srv UserServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListOptions
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserServiceListUsers)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListUsers(ctx, req.(*ListOptions))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListUsersResult)
		return ctx.Result(200, reply)
	}
}

func _UserService_SearchUser0_HTTP_Handler(srv UserServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in SearchUserRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserServiceSearchUser)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SearchUser(ctx, req.(*SearchUserRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*SearchUserResult)
		return ctx.Result(200, reply)
	}
}

func _UserService_GetSSHKeysForUser0_HTTP_Handler(srv UserServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetSSHKeysForUserRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserServiceGetSSHKeysForUser)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetSSHKeysForUser(ctx, req.(*GetSSHKeysForUserRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetSSHKeyResponse)
		return ctx.Result(200, reply)
	}
}

func _UserService_CreateSSHKey0_HTTP_Handler(srv UserServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateSSHKeyRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserServiceCreateSSHKey)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateSSHKey(ctx, req.(*CreateSSHKeyRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CreateSSHKeyResponse)
		return ctx.Result(200, reply)
	}
}

func _UserService_UpdateSSHKey0_HTTP_Handler(srv UserServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UpdateSSHKeyRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserServiceUpdateSSHKey)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateSSHKey(ctx, req.(*UpdateSSHKeyRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*UpdateSSHKeyResponse)
		return ctx.Result(200, reply)
	}
}

func _UserService_DeleteSSHKey0_HTTP_Handler(srv UserServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteSSHKeyRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserServiceDeleteSSHKey)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteSSHKey(ctx, req.(*DeleteSSHKeyRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DeleteSSHKeyResponse)
		return ctx.Result(200, reply)
	}
}

func _UserService_CheckSSHKey0_HTTP_Handler(srv UserServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CheckSSHKeyRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserServiceCheckSSHKey)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CheckSSHKey(ctx, req.(*CheckSSHKeyRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CheckSSHKeyResponse)
		return ctx.Result(200, reply)
	}
}

func _UserService_GenerateAccessToken0_HTTP_Handler(srv UserServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GenerateAccessTokenRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserServiceGenerateAccessToken)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GenerateAccessToken(ctx, req.(*GenerateAccessTokenRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _UserService_GetAccessToken0_HTTP_Handler(srv UserServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetAccessTokenRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserServiceGetAccessToken)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetAccessToken(ctx, req.(*GetAccessTokenRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetAccessTokenResponse)
		return ctx.Result(200, reply)
	}
}

func _UserService_DeleteAccessToken0_HTTP_Handler(srv UserServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteAccessTokenRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserServiceDeleteAccessToken)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteAccessToken(ctx, req.(*DeleteAccessTokenRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _UserService_Whoami0_HTTP_Handler(srv UserServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in WhoamiRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserServiceWhoami)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Whoami(ctx, req.(*WhoamiRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*WhoamiResponse)
		return ctx.Result(200, reply)
	}
}

func _UserService_GetHadoopUsers0_HTTP_Handler(srv UserServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetHadoopUsersRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserServiceGetHadoopUsers)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetHadoopUsers(ctx, req.(*GetHadoopUsersRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetHadoopUsersResponse)
		return ctx.Result(200, reply)
	}
}

type UserServiceHTTPClient interface {
	CheckSSHKey(ctx context.Context, req *CheckSSHKeyRequest, opts ...http.CallOption) (rsp *CheckSSHKeyResponse, err error)
	CreateOrUpdateUser(ctx context.Context, req *CreateOrUpdateUserRequest, opts ...http.CallOption) (rsp *UserDetail, err error)
	CreateSSHKey(ctx context.Context, req *CreateSSHKeyRequest, opts ...http.CallOption) (rsp *CreateSSHKeyResponse, err error)
	DeleteAccessToken(ctx context.Context, req *DeleteAccessTokenRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	DeleteSSHKey(ctx context.Context, req *DeleteSSHKeyRequest, opts ...http.CallOption) (rsp *DeleteSSHKeyResponse, err error)
	DeleteUser(ctx context.Context, req *DeleteUserRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	GenerateAccessToken(ctx context.Context, req *GenerateAccessTokenRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	GetAccessToken(ctx context.Context, req *GetAccessTokenRequest, opts ...http.CallOption) (rsp *GetAccessTokenResponse, err error)
	GetHadoopUsers(ctx context.Context, req *GetHadoopUsersRequest, opts ...http.CallOption) (rsp *GetHadoopUsersResponse, err error)
	GetSSHKeysForUser(ctx context.Context, req *GetSSHKeysForUserRequest, opts ...http.CallOption) (rsp *GetSSHKeyResponse, err error)
	GetUser(ctx context.Context, req *GetUserDetailRequest, opts ...http.CallOption) (rsp *UserDetail, err error)
	ListUsers(ctx context.Context, req *ListOptions, opts ...http.CallOption) (rsp *ListUsersResult, err error)
	SearchUser(ctx context.Context, req *SearchUserRequest, opts ...http.CallOption) (rsp *SearchUserResult, err error)
	UpdateSSHKey(ctx context.Context, req *UpdateSSHKeyRequest, opts ...http.CallOption) (rsp *UpdateSSHKeyResponse, err error)
	Whoami(ctx context.Context, req *WhoamiRequest, opts ...http.CallOption) (rsp *WhoamiResponse, err error)
}

type UserServiceHTTPClientImpl struct {
	cc *http.Client
}

func NewUserServiceHTTPClient(client *http.Client) UserServiceHTTPClient {
	return &UserServiceHTTPClientImpl{client}
}

func (c *UserServiceHTTPClientImpl) CheckSSHKey(ctx context.Context, in *CheckSSHKeyRequest, opts ...http.CallOption) (*CheckSSHKeyResponse, error) {
	var out CheckSSHKeyResponse
	pattern := "/apis/v1/user/{account}/ssh-key/check"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationUserServiceCheckSSHKey))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *UserServiceHTTPClientImpl) CreateOrUpdateUser(ctx context.Context, in *CreateOrUpdateUserRequest, opts ...http.CallOption) (*UserDetail, error) {
	var out UserDetail
	pattern := "/apis/v1/user"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationUserServiceCreateOrUpdateUser))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *UserServiceHTTPClientImpl) CreateSSHKey(ctx context.Context, in *CreateSSHKeyRequest, opts ...http.CallOption) (*CreateSSHKeyResponse, error) {
	var out CreateSSHKeyResponse
	pattern := "/apis/v1/user/{account}/ssh-key"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationUserServiceCreateSSHKey))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *UserServiceHTTPClientImpl) DeleteAccessToken(ctx context.Context, in *DeleteAccessTokenRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/apis/v1/user/{account}/access_token/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationUserServiceDeleteAccessToken))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *UserServiceHTTPClientImpl) DeleteSSHKey(ctx context.Context, in *DeleteSSHKeyRequest, opts ...http.CallOption) (*DeleteSSHKeyResponse, error) {
	var out DeleteSSHKeyResponse
	pattern := "/apis/v1/user/{account}/ssh-key/{name}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationUserServiceDeleteSSHKey))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *UserServiceHTTPClientImpl) DeleteUser(ctx context.Context, in *DeleteUserRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/apis/v1/user/{account}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationUserServiceDeleteUser))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *UserServiceHTTPClientImpl) GenerateAccessToken(ctx context.Context, in *GenerateAccessTokenRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/apis/v1/user/{account}/access_token"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationUserServiceGenerateAccessToken))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *UserServiceHTTPClientImpl) GetAccessToken(ctx context.Context, in *GetAccessTokenRequest, opts ...http.CallOption) (*GetAccessTokenResponse, error) {
	var out GetAccessTokenResponse
	pattern := "/apis/v1/user/{account}/access_token"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationUserServiceGetAccessToken))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *UserServiceHTTPClientImpl) GetHadoopUsers(ctx context.Context, in *GetHadoopUsersRequest, opts ...http.CallOption) (*GetHadoopUsersResponse, error) {
	var out GetHadoopUsersResponse
	pattern := "/apis/v1/user/{account}/hadoopusers"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationUserServiceGetHadoopUsers))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *UserServiceHTTPClientImpl) GetSSHKeysForUser(ctx context.Context, in *GetSSHKeysForUserRequest, opts ...http.CallOption) (*GetSSHKeyResponse, error) {
	var out GetSSHKeyResponse
	pattern := "/apis/v1/user/{account}/ssh-keys"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationUserServiceGetSSHKeysForUser))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *UserServiceHTTPClientImpl) GetUser(ctx context.Context, in *GetUserDetailRequest, opts ...http.CallOption) (*UserDetail, error) {
	var out UserDetail
	pattern := "/apis/v1/user/{account}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationUserServiceGetUser))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *UserServiceHTTPClientImpl) ListUsers(ctx context.Context, in *ListOptions, opts ...http.CallOption) (*ListUsersResult, error) {
	var out ListUsersResult
	pattern := "/apis/v1/users"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationUserServiceListUsers))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *UserServiceHTTPClientImpl) SearchUser(ctx context.Context, in *SearchUserRequest, opts ...http.CallOption) (*SearchUserResult, error) {
	var out SearchUserResult
	pattern := "/apis/v1/user_search"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationUserServiceSearchUser))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *UserServiceHTTPClientImpl) UpdateSSHKey(ctx context.Context, in *UpdateSSHKeyRequest, opts ...http.CallOption) (*UpdateSSHKeyResponse, error) {
	var out UpdateSSHKeyResponse
	pattern := "/apis/v1/user/{account}/ssh-key/{name}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationUserServiceUpdateSSHKey))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *UserServiceHTTPClientImpl) Whoami(ctx context.Context, in *WhoamiRequest, opts ...http.CallOption) (*WhoamiResponse, error) {
	var out WhoamiResponse
	pattern := "/apis/v1/user_whoami"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationUserServiceWhoami))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
