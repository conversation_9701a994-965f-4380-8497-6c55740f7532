// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v5.29.3
// source: usercenter/v1/usercenter.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	UserService_CreateOrUpdateUser_FullMethodName  = "/apis.usercenter.v1.UserService/CreateOrUpdateUser"
	UserService_DeleteUser_FullMethodName          = "/apis.usercenter.v1.UserService/DeleteUser"
	UserService_GetUser_FullMethodName             = "/apis.usercenter.v1.UserService/GetUser"
	UserService_ListUsers_FullMethodName           = "/apis.usercenter.v1.UserService/ListUsers"
	UserService_SearchUser_FullMethodName          = "/apis.usercenter.v1.UserService/SearchUser"
	UserService_GetSSHKeysForUser_FullMethodName   = "/apis.usercenter.v1.UserService/GetSSHKeysForUser"
	UserService_CreateSSHKey_FullMethodName        = "/apis.usercenter.v1.UserService/CreateSSHKey"
	UserService_UpdateSSHKey_FullMethodName        = "/apis.usercenter.v1.UserService/UpdateSSHKey"
	UserService_DeleteSSHKey_FullMethodName        = "/apis.usercenter.v1.UserService/DeleteSSHKey"
	UserService_CheckSSHKey_FullMethodName         = "/apis.usercenter.v1.UserService/CheckSSHKey"
	UserService_GenerateAccessToken_FullMethodName = "/apis.usercenter.v1.UserService/GenerateAccessToken"
	UserService_GetAccessToken_FullMethodName      = "/apis.usercenter.v1.UserService/GetAccessToken"
	UserService_DeleteAccessToken_FullMethodName   = "/apis.usercenter.v1.UserService/DeleteAccessToken"
	UserService_Whoami_FullMethodName              = "/apis.usercenter.v1.UserService/Whoami"
	UserService_GetHadoopUsers_FullMethodName      = "/apis.usercenter.v1.UserService/GetHadoopUsers"
)

// UserServiceClient is the client API for UserService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// The greeting service definition.
type UserServiceClient interface {
	// 创建或更新用户
	CreateOrUpdateUser(ctx context.Context, in *CreateOrUpdateUserRequest, opts ...grpc.CallOption) (*UserDetail, error)
	// 删除用户
	DeleteUser(ctx context.Context, in *DeleteUserRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 获取用户
	GetUser(ctx context.Context, in *GetUserDetailRequest, opts ...grpc.CallOption) (*UserDetail, error)
	// 展示用户列表
	ListUsers(ctx context.Context, in *ListOptions, opts ...grpc.CallOption) (*ListUsersResult, error)
	// 搜索用户
	SearchUser(ctx context.Context, in *SearchUserRequest, opts ...grpc.CallOption) (*SearchUserResult, error)
	// 获取用户的ssh key
	GetSSHKeysForUser(ctx context.Context, in *GetSSHKeysForUserRequest, opts ...grpc.CallOption) (*GetSSHKeyResponse, error)
	// 创建ssh-key
	CreateSSHKey(ctx context.Context, in *CreateSSHKeyRequest, opts ...grpc.CallOption) (*CreateSSHKeyResponse, error)
	// 更新ssh-key，只能更新私钥
	UpdateSSHKey(ctx context.Context, in *UpdateSSHKeyRequest, opts ...grpc.CallOption) (*UpdateSSHKeyResponse, error)
	// 删除ssh-key
	DeleteSSHKey(ctx context.Context, in *DeleteSSHKeyRequest, opts ...grpc.CallOption) (*DeleteSSHKeyResponse, error)
	// 校验ssh-key
	CheckSSHKey(ctx context.Context, in *CheckSSHKeyRequest, opts ...grpc.CallOption) (*CheckSSHKeyResponse, error)
	// 生成用户的 accessToken，用户可以生成自己的 accessToken，用户接口调用
	GenerateAccessToken(ctx context.Context, in *GenerateAccessTokenRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 获取用户的 accessToken
	GetAccessToken(ctx context.Context, in *GetAccessTokenRequest, opts ...grpc.CallOption) (*GetAccessTokenResponse, error)
	// 删除用户的 accessToken
	DeleteAccessToken(ctx context.Context, in *DeleteAccessTokenRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 获取当前用户
	Whoami(ctx context.Context, in *WhoamiRequest, opts ...grpc.CallOption) (*WhoamiResponse, error)
	// 获取hadoop账号
	GetHadoopUsers(ctx context.Context, in *GetHadoopUsersRequest, opts ...grpc.CallOption) (*GetHadoopUsersResponse, error)
}

type userServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewUserServiceClient(cc grpc.ClientConnInterface) UserServiceClient {
	return &userServiceClient{cc}
}

func (c *userServiceClient) CreateOrUpdateUser(ctx context.Context, in *CreateOrUpdateUserRequest, opts ...grpc.CallOption) (*UserDetail, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UserDetail)
	err := c.cc.Invoke(ctx, UserService_CreateOrUpdateUser_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) DeleteUser(ctx context.Context, in *DeleteUserRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, UserService_DeleteUser_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) GetUser(ctx context.Context, in *GetUserDetailRequest, opts ...grpc.CallOption) (*UserDetail, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UserDetail)
	err := c.cc.Invoke(ctx, UserService_GetUser_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) ListUsers(ctx context.Context, in *ListOptions, opts ...grpc.CallOption) (*ListUsersResult, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListUsersResult)
	err := c.cc.Invoke(ctx, UserService_ListUsers_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) SearchUser(ctx context.Context, in *SearchUserRequest, opts ...grpc.CallOption) (*SearchUserResult, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SearchUserResult)
	err := c.cc.Invoke(ctx, UserService_SearchUser_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) GetSSHKeysForUser(ctx context.Context, in *GetSSHKeysForUserRequest, opts ...grpc.CallOption) (*GetSSHKeyResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetSSHKeyResponse)
	err := c.cc.Invoke(ctx, UserService_GetSSHKeysForUser_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) CreateSSHKey(ctx context.Context, in *CreateSSHKeyRequest, opts ...grpc.CallOption) (*CreateSSHKeyResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateSSHKeyResponse)
	err := c.cc.Invoke(ctx, UserService_CreateSSHKey_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) UpdateSSHKey(ctx context.Context, in *UpdateSSHKeyRequest, opts ...grpc.CallOption) (*UpdateSSHKeyResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateSSHKeyResponse)
	err := c.cc.Invoke(ctx, UserService_UpdateSSHKey_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) DeleteSSHKey(ctx context.Context, in *DeleteSSHKeyRequest, opts ...grpc.CallOption) (*DeleteSSHKeyResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteSSHKeyResponse)
	err := c.cc.Invoke(ctx, UserService_DeleteSSHKey_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) CheckSSHKey(ctx context.Context, in *CheckSSHKeyRequest, opts ...grpc.CallOption) (*CheckSSHKeyResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CheckSSHKeyResponse)
	err := c.cc.Invoke(ctx, UserService_CheckSSHKey_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) GenerateAccessToken(ctx context.Context, in *GenerateAccessTokenRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, UserService_GenerateAccessToken_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) GetAccessToken(ctx context.Context, in *GetAccessTokenRequest, opts ...grpc.CallOption) (*GetAccessTokenResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetAccessTokenResponse)
	err := c.cc.Invoke(ctx, UserService_GetAccessToken_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) DeleteAccessToken(ctx context.Context, in *DeleteAccessTokenRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, UserService_DeleteAccessToken_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) Whoami(ctx context.Context, in *WhoamiRequest, opts ...grpc.CallOption) (*WhoamiResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(WhoamiResponse)
	err := c.cc.Invoke(ctx, UserService_Whoami_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) GetHadoopUsers(ctx context.Context, in *GetHadoopUsersRequest, opts ...grpc.CallOption) (*GetHadoopUsersResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetHadoopUsersResponse)
	err := c.cc.Invoke(ctx, UserService_GetHadoopUsers_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// UserServiceServer is the server API for UserService service.
// All implementations must embed UnimplementedUserServiceServer
// for forward compatibility
//
// The greeting service definition.
type UserServiceServer interface {
	// 创建或更新用户
	CreateOrUpdateUser(context.Context, *CreateOrUpdateUserRequest) (*UserDetail, error)
	// 删除用户
	DeleteUser(context.Context, *DeleteUserRequest) (*emptypb.Empty, error)
	// 获取用户
	GetUser(context.Context, *GetUserDetailRequest) (*UserDetail, error)
	// 展示用户列表
	ListUsers(context.Context, *ListOptions) (*ListUsersResult, error)
	// 搜索用户
	SearchUser(context.Context, *SearchUserRequest) (*SearchUserResult, error)
	// 获取用户的ssh key
	GetSSHKeysForUser(context.Context, *GetSSHKeysForUserRequest) (*GetSSHKeyResponse, error)
	// 创建ssh-key
	CreateSSHKey(context.Context, *CreateSSHKeyRequest) (*CreateSSHKeyResponse, error)
	// 更新ssh-key，只能更新私钥
	UpdateSSHKey(context.Context, *UpdateSSHKeyRequest) (*UpdateSSHKeyResponse, error)
	// 删除ssh-key
	DeleteSSHKey(context.Context, *DeleteSSHKeyRequest) (*DeleteSSHKeyResponse, error)
	// 校验ssh-key
	CheckSSHKey(context.Context, *CheckSSHKeyRequest) (*CheckSSHKeyResponse, error)
	// 生成用户的 accessToken，用户可以生成自己的 accessToken，用户接口调用
	GenerateAccessToken(context.Context, *GenerateAccessTokenRequest) (*emptypb.Empty, error)
	// 获取用户的 accessToken
	GetAccessToken(context.Context, *GetAccessTokenRequest) (*GetAccessTokenResponse, error)
	// 删除用户的 accessToken
	DeleteAccessToken(context.Context, *DeleteAccessTokenRequest) (*emptypb.Empty, error)
	// 获取当前用户
	Whoami(context.Context, *WhoamiRequest) (*WhoamiResponse, error)
	// 获取hadoop账号
	GetHadoopUsers(context.Context, *GetHadoopUsersRequest) (*GetHadoopUsersResponse, error)
	mustEmbedUnimplementedUserServiceServer()
}

// UnimplementedUserServiceServer must be embedded to have forward compatible implementations.
type UnimplementedUserServiceServer struct {
}

func (UnimplementedUserServiceServer) CreateOrUpdateUser(context.Context, *CreateOrUpdateUserRequest) (*UserDetail, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateOrUpdateUser not implemented")
}
func (UnimplementedUserServiceServer) DeleteUser(context.Context, *DeleteUserRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteUser not implemented")
}
func (UnimplementedUserServiceServer) GetUser(context.Context, *GetUserDetailRequest) (*UserDetail, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUser not implemented")
}
func (UnimplementedUserServiceServer) ListUsers(context.Context, *ListOptions) (*ListUsersResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListUsers not implemented")
}
func (UnimplementedUserServiceServer) SearchUser(context.Context, *SearchUserRequest) (*SearchUserResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchUser not implemented")
}
func (UnimplementedUserServiceServer) GetSSHKeysForUser(context.Context, *GetSSHKeysForUserRequest) (*GetSSHKeyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSSHKeysForUser not implemented")
}
func (UnimplementedUserServiceServer) CreateSSHKey(context.Context, *CreateSSHKeyRequest) (*CreateSSHKeyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateSSHKey not implemented")
}
func (UnimplementedUserServiceServer) UpdateSSHKey(context.Context, *UpdateSSHKeyRequest) (*UpdateSSHKeyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateSSHKey not implemented")
}
func (UnimplementedUserServiceServer) DeleteSSHKey(context.Context, *DeleteSSHKeyRequest) (*DeleteSSHKeyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteSSHKey not implemented")
}
func (UnimplementedUserServiceServer) CheckSSHKey(context.Context, *CheckSSHKeyRequest) (*CheckSSHKeyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckSSHKey not implemented")
}
func (UnimplementedUserServiceServer) GenerateAccessToken(context.Context, *GenerateAccessTokenRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GenerateAccessToken not implemented")
}
func (UnimplementedUserServiceServer) GetAccessToken(context.Context, *GetAccessTokenRequest) (*GetAccessTokenResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAccessToken not implemented")
}
func (UnimplementedUserServiceServer) DeleteAccessToken(context.Context, *DeleteAccessTokenRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteAccessToken not implemented")
}
func (UnimplementedUserServiceServer) Whoami(context.Context, *WhoamiRequest) (*WhoamiResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Whoami not implemented")
}
func (UnimplementedUserServiceServer) GetHadoopUsers(context.Context, *GetHadoopUsersRequest) (*GetHadoopUsersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetHadoopUsers not implemented")
}
func (UnimplementedUserServiceServer) mustEmbedUnimplementedUserServiceServer() {}

// UnsafeUserServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to UserServiceServer will
// result in compilation errors.
type UnsafeUserServiceServer interface {
	mustEmbedUnimplementedUserServiceServer()
}

func RegisterUserServiceServer(s grpc.ServiceRegistrar, srv UserServiceServer) {
	s.RegisterService(&UserService_ServiceDesc, srv)
}

func _UserService_CreateOrUpdateUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateOrUpdateUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).CreateOrUpdateUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_CreateOrUpdateUser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).CreateOrUpdateUser(ctx, req.(*CreateOrUpdateUserRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_DeleteUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).DeleteUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_DeleteUser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).DeleteUser(ctx, req.(*DeleteUserRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_GetUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).GetUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_GetUser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).GetUser(ctx, req.(*GetUserDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_ListUsers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListOptions)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).ListUsers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_ListUsers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).ListUsers(ctx, req.(*ListOptions))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_SearchUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).SearchUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_SearchUser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).SearchUser(ctx, req.(*SearchUserRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_GetSSHKeysForUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSSHKeysForUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).GetSSHKeysForUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_GetSSHKeysForUser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).GetSSHKeysForUser(ctx, req.(*GetSSHKeysForUserRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_CreateSSHKey_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateSSHKeyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).CreateSSHKey(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_CreateSSHKey_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).CreateSSHKey(ctx, req.(*CreateSSHKeyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_UpdateSSHKey_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateSSHKeyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).UpdateSSHKey(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_UpdateSSHKey_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).UpdateSSHKey(ctx, req.(*UpdateSSHKeyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_DeleteSSHKey_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteSSHKeyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).DeleteSSHKey(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_DeleteSSHKey_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).DeleteSSHKey(ctx, req.(*DeleteSSHKeyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_CheckSSHKey_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckSSHKeyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).CheckSSHKey(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_CheckSSHKey_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).CheckSSHKey(ctx, req.(*CheckSSHKeyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_GenerateAccessToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GenerateAccessTokenRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).GenerateAccessToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_GenerateAccessToken_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).GenerateAccessToken(ctx, req.(*GenerateAccessTokenRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_GetAccessToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAccessTokenRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).GetAccessToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_GetAccessToken_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).GetAccessToken(ctx, req.(*GetAccessTokenRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_DeleteAccessToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteAccessTokenRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).DeleteAccessToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_DeleteAccessToken_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).DeleteAccessToken(ctx, req.(*DeleteAccessTokenRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_Whoami_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WhoamiRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).Whoami(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_Whoami_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).Whoami(ctx, req.(*WhoamiRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_GetHadoopUsers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetHadoopUsersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).GetHadoopUsers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_GetHadoopUsers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).GetHadoopUsers(ctx, req.(*GetHadoopUsersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// UserService_ServiceDesc is the grpc.ServiceDesc for UserService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var UserService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "apis.usercenter.v1.UserService",
	HandlerType: (*UserServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateOrUpdateUser",
			Handler:    _UserService_CreateOrUpdateUser_Handler,
		},
		{
			MethodName: "DeleteUser",
			Handler:    _UserService_DeleteUser_Handler,
		},
		{
			MethodName: "GetUser",
			Handler:    _UserService_GetUser_Handler,
		},
		{
			MethodName: "ListUsers",
			Handler:    _UserService_ListUsers_Handler,
		},
		{
			MethodName: "SearchUser",
			Handler:    _UserService_SearchUser_Handler,
		},
		{
			MethodName: "GetSSHKeysForUser",
			Handler:    _UserService_GetSSHKeysForUser_Handler,
		},
		{
			MethodName: "CreateSSHKey",
			Handler:    _UserService_CreateSSHKey_Handler,
		},
		{
			MethodName: "UpdateSSHKey",
			Handler:    _UserService_UpdateSSHKey_Handler,
		},
		{
			MethodName: "DeleteSSHKey",
			Handler:    _UserService_DeleteSSHKey_Handler,
		},
		{
			MethodName: "CheckSSHKey",
			Handler:    _UserService_CheckSSHKey_Handler,
		},
		{
			MethodName: "GenerateAccessToken",
			Handler:    _UserService_GenerateAccessToken_Handler,
		},
		{
			MethodName: "GetAccessToken",
			Handler:    _UserService_GetAccessToken_Handler,
		},
		{
			MethodName: "DeleteAccessToken",
			Handler:    _UserService_DeleteAccessToken_Handler,
		},
		{
			MethodName: "Whoami",
			Handler:    _UserService_Whoami_Handler,
		},
		{
			MethodName: "GetHadoopUsers",
			Handler:    _UserService_GetHadoopUsers_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "usercenter/v1/usercenter.proto",
}
