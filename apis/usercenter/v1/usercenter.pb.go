// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v5.29.3
// source: usercenter/v1/usercenter.proto

package v1

import (
	common "git.lianjia.com/cloudnative/kic/kic-platform/apis/common"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type UserType int32

const (
	UserType_User  UserType = 0
	UserType_Robot UserType = 1
)

// Enum value maps for UserType.
var (
	UserType_name = map[int32]string{
		0: "User",
		1: "Robot",
	}
	UserType_value = map[string]int32{
		"User":  0,
		"Robot": 1,
	}
)

func (x UserType) Enum() *UserType {
	p := new(UserType)
	*p = x
	return p
}

func (x UserType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UserType) Descriptor() protoreflect.EnumDescriptor {
	return file_usercenter_v1_usercenter_proto_enumTypes[0].Descriptor()
}

func (UserType) Type() protoreflect.EnumType {
	return &file_usercenter_v1_usercenter_proto_enumTypes[0]
}

func (x UserType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UserType.Descriptor instead.
func (UserType) EnumDescriptor() ([]byte, []int) {
	return file_usercenter_v1_usercenter_proto_rawDescGZIP(), []int{0}
}

type GetHadoopUsersRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Account string `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`
}

func (x *GetHadoopUsersRequest) Reset() {
	*x = GetHadoopUsersRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_v1_usercenter_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetHadoopUsersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetHadoopUsersRequest) ProtoMessage() {}

func (x *GetHadoopUsersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_v1_usercenter_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetHadoopUsersRequest.ProtoReflect.Descriptor instead.
func (*GetHadoopUsersRequest) Descriptor() ([]byte, []int) {
	return file_usercenter_v1_usercenter_proto_rawDescGZIP(), []int{0}
}

func (x *GetHadoopUsersRequest) GetAccount() string {
	if x != nil {
		return x.Account
	}
	return ""
}

type GetHadoopUsersResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HadoopUsers []string `protobuf:"bytes,1,rep,name=hadoopUsers,proto3" json:"hadoopUsers,omitempty"`
}

func (x *GetHadoopUsersResponse) Reset() {
	*x = GetHadoopUsersResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_v1_usercenter_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetHadoopUsersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetHadoopUsersResponse) ProtoMessage() {}

func (x *GetHadoopUsersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_v1_usercenter_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetHadoopUsersResponse.ProtoReflect.Descriptor instead.
func (*GetHadoopUsersResponse) Descriptor() ([]byte, []int) {
	return file_usercenter_v1_usercenter_proto_rawDescGZIP(), []int{1}
}

func (x *GetHadoopUsersResponse) GetHadoopUsers() []string {
	if x != nil {
		return x.HadoopUsers
	}
	return nil
}

type UserDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Timestamp       *common.TimestampModel `protobuf:"bytes,1,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	LastLoginTime   *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=lastLoginTime,proto3" json:"lastLoginTime,omitempty"`
	RobotAttributes *RobotAttributes       `protobuf:"bytes,3,opt,name=robotAttributes,proto3" json:"robotAttributes,omitempty"` //机器人账号属性
	UserAttributes  *UserAttributes        `protobuf:"bytes,4,opt,name=userAttributes,proto3" json:"userAttributes,omitempty"`   //用户属性
	Account         string                 `protobuf:"bytes,5,opt,name=account,proto3" json:"account,omitempty"`
	UserType        UserType               `protobuf:"varint,6,opt,name=userType,proto3,enum=apis.usercenter.v1.UserType" json:"userType,omitempty"` //用户类型, 0: 用户, 1: 机器人
	PlatformRole    string                 `protobuf:"bytes,7,opt,name=platformRole,proto3" json:"platformRole,omitempty"`                           //平台角色
}

func (x *UserDetail) Reset() {
	*x = UserDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_v1_usercenter_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserDetail) ProtoMessage() {}

func (x *UserDetail) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_v1_usercenter_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserDetail.ProtoReflect.Descriptor instead.
func (*UserDetail) Descriptor() ([]byte, []int) {
	return file_usercenter_v1_usercenter_proto_rawDescGZIP(), []int{2}
}

func (x *UserDetail) GetTimestamp() *common.TimestampModel {
	if x != nil {
		return x.Timestamp
	}
	return nil
}

func (x *UserDetail) GetLastLoginTime() *timestamppb.Timestamp {
	if x != nil {
		return x.LastLoginTime
	}
	return nil
}

func (x *UserDetail) GetRobotAttributes() *RobotAttributes {
	if x != nil {
		return x.RobotAttributes
	}
	return nil
}

func (x *UserDetail) GetUserAttributes() *UserAttributes {
	if x != nil {
		return x.UserAttributes
	}
	return nil
}

func (x *UserDetail) GetAccount() string {
	if x != nil {
		return x.Account
	}
	return ""
}

func (x *UserDetail) GetUserType() UserType {
	if x != nil {
		return x.UserType
	}
	return UserType_User
}

func (x *UserDetail) GetPlatformRole() string {
	if x != nil {
		return x.PlatformRole
	}
	return ""
}

// 普通用户
type UserAttributes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Email          string `protobuf:"bytes,2,opt,name=email,proto3" json:"email,omitempty"`
	CnName         string `protobuf:"bytes,3,opt,name=cnName,proto3" json:"cnName,omitempty"`                  //用户中文名
	NickName       string `protobuf:"bytes,4,opt,name=nickName,proto3" json:"nickName,omitempty"`              //花名
	Account        string `protobuf:"bytes,5,opt,name=account,proto3" json:"account,omitempty"`                //用户账号
	Avatar         string `protobuf:"bytes,6,opt,name=avatar,proto3" json:"avatar,omitempty"`                  //用户头像
	PositionStatus int64  `protobuf:"varint,7,opt,name=positionStatus,proto3" json:"positionStatus,omitempty"` //1在职， 0 离职
	OrgName        string `protobuf:"bytes,8,opt,name=orgName,proto3" json:"orgName,omitempty"`                //组织名称
	OrgCode        string `protobuf:"bytes,9,opt,name=orgCode,proto3" json:"orgCode,omitempty"`                //组织Code
	UserCode       string `protobuf:"bytes,10,opt,name=userCode,proto3" json:"userCode,omitempty"`             //用户工号
}

func (x *UserAttributes) Reset() {
	*x = UserAttributes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_v1_usercenter_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserAttributes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserAttributes) ProtoMessage() {}

func (x *UserAttributes) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_v1_usercenter_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserAttributes.ProtoReflect.Descriptor instead.
func (*UserAttributes) Descriptor() ([]byte, []int) {
	return file_usercenter_v1_usercenter_proto_rawDescGZIP(), []int{3}
}

func (x *UserAttributes) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *UserAttributes) GetCnName() string {
	if x != nil {
		return x.CnName
	}
	return ""
}

func (x *UserAttributes) GetNickName() string {
	if x != nil {
		return x.NickName
	}
	return ""
}

func (x *UserAttributes) GetAccount() string {
	if x != nil {
		return x.Account
	}
	return ""
}

func (x *UserAttributes) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *UserAttributes) GetPositionStatus() int64 {
	if x != nil {
		return x.PositionStatus
	}
	return 0
}

func (x *UserAttributes) GetOrgName() string {
	if x != nil {
		return x.OrgName
	}
	return ""
}

func (x *UserAttributes) GetOrgCode() string {
	if x != nil {
		return x.OrgCode
	}
	return ""
}

func (x *UserAttributes) GetUserCode() string {
	if x != nil {
		return x.UserCode
	}
	return ""
}

// 机器人账号
type RobotAttributes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Account          string   `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`
	Password         string   `protobuf:"bytes,2,opt,name=password,proto3" json:"password,omitempty"`
	DisplayName      string   `protobuf:"bytes,3,opt,name=displayName,proto3" json:"displayName,omitempty"`
	Description      string   `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`           // 描述信息
	AccessToken      string   `protobuf:"bytes,5,opt,name=accessToken,proto3" json:"accessToken,omitempty"`           // 访问秘钥
	WeChatRobotToken string   `protobuf:"bytes,6,opt,name=weChatRobotToken,proto3" json:"weChatRobotToken,omitempty"` // 微信机器人token
	Workspaces       []string `protobuf:"bytes,7,rep,name=workspaces,proto3" json:"workspaces,omitempty"`             // 机器人所属的工作空间
	OriginWorkspace  string   `protobuf:"bytes,8,opt,name=originWorkspace,proto3" json:"originWorkspace,omitempty"`   // 机器人的原始工作空间
	IsDefault        string   `protobuf:"bytes,9,opt,name=isDefault,proto3" json:"isDefault,omitempty"`               // 是否是默认机器人
}

func (x *RobotAttributes) Reset() {
	*x = RobotAttributes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_v1_usercenter_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RobotAttributes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RobotAttributes) ProtoMessage() {}

func (x *RobotAttributes) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_v1_usercenter_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RobotAttributes.ProtoReflect.Descriptor instead.
func (*RobotAttributes) Descriptor() ([]byte, []int) {
	return file_usercenter_v1_usercenter_proto_rawDescGZIP(), []int{4}
}

func (x *RobotAttributes) GetAccount() string {
	if x != nil {
		return x.Account
	}
	return ""
}

func (x *RobotAttributes) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *RobotAttributes) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *RobotAttributes) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *RobotAttributes) GetAccessToken() string {
	if x != nil {
		return x.AccessToken
	}
	return ""
}

func (x *RobotAttributes) GetWeChatRobotToken() string {
	if x != nil {
		return x.WeChatRobotToken
	}
	return ""
}

func (x *RobotAttributes) GetWorkspaces() []string {
	if x != nil {
		return x.Workspaces
	}
	return nil
}

func (x *RobotAttributes) GetOriginWorkspace() string {
	if x != nil {
		return x.OriginWorkspace
	}
	return ""
}

func (x *RobotAttributes) GetIsDefault() string {
	if x != nil {
		return x.IsDefault
	}
	return ""
}

type CreateOrUpdateUserRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Account      string      `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`
	UserDetail   *UserDetail `protobuf:"bytes,2,opt,name=userDetail,proto3" json:"userDetail,omitempty"`
	Password     string      `protobuf:"bytes,3,opt,name=password,proto3" json:"password,omitempty"`
	UserType     string      `protobuf:"bytes,4,opt,name=userType,proto3" json:"userType,omitempty"`         //用户类型, 0: 用户, 1: 机器人
	Creator      string      `protobuf:"bytes,5,opt,name=creator,proto3" json:"creator,omitempty"`           //创建者
	Updater      string      `protobuf:"bytes,6,opt,name=updater,proto3" json:"updater,omitempty"`           //更新者
	PlatformRole string      `protobuf:"bytes,7,opt,name=platformRole,proto3" json:"platformRole,omitempty"` //平台角色
}

func (x *CreateOrUpdateUserRequest) Reset() {
	*x = CreateOrUpdateUserRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_v1_usercenter_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateOrUpdateUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateOrUpdateUserRequest) ProtoMessage() {}

func (x *CreateOrUpdateUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_v1_usercenter_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateOrUpdateUserRequest.ProtoReflect.Descriptor instead.
func (*CreateOrUpdateUserRequest) Descriptor() ([]byte, []int) {
	return file_usercenter_v1_usercenter_proto_rawDescGZIP(), []int{5}
}

func (x *CreateOrUpdateUserRequest) GetAccount() string {
	if x != nil {
		return x.Account
	}
	return ""
}

func (x *CreateOrUpdateUserRequest) GetUserDetail() *UserDetail {
	if x != nil {
		return x.UserDetail
	}
	return nil
}

func (x *CreateOrUpdateUserRequest) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *CreateOrUpdateUserRequest) GetUserType() string {
	if x != nil {
		return x.UserType
	}
	return ""
}

func (x *CreateOrUpdateUserRequest) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *CreateOrUpdateUserRequest) GetUpdater() string {
	if x != nil {
		return x.Updater
	}
	return ""
}

func (x *CreateOrUpdateUserRequest) GetPlatformRole() string {
	if x != nil {
		return x.PlatformRole
	}
	return ""
}

type DeleteUserRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Account string `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`
}

func (x *DeleteUserRequest) Reset() {
	*x = DeleteUserRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_v1_usercenter_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteUserRequest) ProtoMessage() {}

func (x *DeleteUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_v1_usercenter_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteUserRequest.ProtoReflect.Descriptor instead.
func (*DeleteUserRequest) Descriptor() ([]byte, []int) {
	return file_usercenter_v1_usercenter_proto_rawDescGZIP(), []int{6}
}

func (x *DeleteUserRequest) GetAccount() string {
	if x != nil {
		return x.Account
	}
	return ""
}

type GetUserDetailRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Account string `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`
}

func (x *GetUserDetailRequest) Reset() {
	*x = GetUserDetailRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_v1_usercenter_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserDetailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserDetailRequest) ProtoMessage() {}

func (x *GetUserDetailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_v1_usercenter_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserDetailRequest.ProtoReflect.Descriptor instead.
func (*GetUserDetailRequest) Descriptor() ([]byte, []int) {
	return file_usercenter_v1_usercenter_proto_rawDescGZIP(), []int{7}
}

func (x *GetUserDetailRequest) GetAccount() string {
	if x != nil {
		return x.Account
	}
	return ""
}

type ListOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PageNo        int32  `protobuf:"varint,1,opt,name=pageNo,proto3" json:"pageNo,omitempty"`
	PageSize      int32  `protobuf:"varint,2,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
	RoleName      string `protobuf:"bytes,3,opt,name=roleName,proto3" json:"roleName,omitempty"`
	UserType      string `protobuf:"bytes,4,opt,name=userType,proto3" json:"userType,omitempty"`
	Account       string `protobuf:"bytes,5,opt,name=account,proto3" json:"account,omitempty"` // 支持模糊搜索
	WorkspaceName string `protobuf:"bytes,6,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	IsDefault     string `protobuf:"bytes,7,opt,name=isDefault,proto3" json:"isDefault,omitempty"`
}

func (x *ListOptions) Reset() {
	*x = ListOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_v1_usercenter_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListOptions) ProtoMessage() {}

func (x *ListOptions) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_v1_usercenter_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListOptions.ProtoReflect.Descriptor instead.
func (*ListOptions) Descriptor() ([]byte, []int) {
	return file_usercenter_v1_usercenter_proto_rawDescGZIP(), []int{8}
}

func (x *ListOptions) GetPageNo() int32 {
	if x != nil {
		return x.PageNo
	}
	return 0
}

func (x *ListOptions) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListOptions) GetRoleName() string {
	if x != nil {
		return x.RoleName
	}
	return ""
}

func (x *ListOptions) GetUserType() string {
	if x != nil {
		return x.UserType
	}
	return ""
}

func (x *ListOptions) GetAccount() string {
	if x != nil {
		return x.Account
	}
	return ""
}

func (x *ListOptions) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *ListOptions) GetIsDefault() string {
	if x != nil {
		return x.IsDefault
	}
	return ""
}

type ListUsersResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total    int32         `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	PageNo   int32         `protobuf:"varint,2,opt,name=pageNo,proto3" json:"pageNo,omitempty"`
	PageSize int32         `protobuf:"varint,3,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
	Users    []*UserDetail `protobuf:"bytes,4,rep,name=users,proto3" json:"users,omitempty"`
}

func (x *ListUsersResult) Reset() {
	*x = ListUsersResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_v1_usercenter_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListUsersResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListUsersResult) ProtoMessage() {}

func (x *ListUsersResult) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_v1_usercenter_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListUsersResult.ProtoReflect.Descriptor instead.
func (*ListUsersResult) Descriptor() ([]byte, []int) {
	return file_usercenter_v1_usercenter_proto_rawDescGZIP(), []int{9}
}

func (x *ListUsersResult) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ListUsersResult) GetPageNo() int32 {
	if x != nil {
		return x.PageNo
	}
	return 0
}

func (x *ListUsersResult) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListUsersResult) GetUsers() []*UserDetail {
	if x != nil {
		return x.Users
	}
	return nil
}

type SearchUserRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Account       string `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`
	WorkspaceName string `protobuf:"bytes,2,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	IsDefault     string `protobuf:"bytes,3,opt,name=isDefault,proto3" json:"isDefault,omitempty"` // 用于机器人用户筛选，“true”包括空间默认机器人，“false”不包括空间默认机器人
}

func (x *SearchUserRequest) Reset() {
	*x = SearchUserRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_v1_usercenter_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchUserRequest) ProtoMessage() {}

func (x *SearchUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_v1_usercenter_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchUserRequest.ProtoReflect.Descriptor instead.
func (*SearchUserRequest) Descriptor() ([]byte, []int) {
	return file_usercenter_v1_usercenter_proto_rawDescGZIP(), []int{10}
}

func (x *SearchUserRequest) GetAccount() string {
	if x != nil {
		return x.Account
	}
	return ""
}

func (x *SearchUserRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *SearchUserRequest) GetIsDefault() string {
	if x != nil {
		return x.IsDefault
	}
	return ""
}

type SearchUserResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Users []*UserName `protobuf:"bytes,1,rep,name=users,proto3" json:"users,omitempty"`
}

func (x *SearchUserResult) Reset() {
	*x = SearchUserResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_v1_usercenter_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchUserResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchUserResult) ProtoMessage() {}

func (x *SearchUserResult) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_v1_usercenter_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchUserResult.ProtoReflect.Descriptor instead.
func (*SearchUserResult) Descriptor() ([]byte, []int) {
	return file_usercenter_v1_usercenter_proto_rawDescGZIP(), []int{11}
}

func (x *SearchUserResult) GetUsers() []*UserName {
	if x != nil {
		return x.Users
	}
	return nil
}

type UserName struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Account     string `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`         // 用户账号
	DisplayName string `protobuf:"bytes,2,opt,name=displayName,proto3" json:"displayName,omitempty"` // 显示名称
}

func (x *UserName) Reset() {
	*x = UserName{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_v1_usercenter_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserName) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserName) ProtoMessage() {}

func (x *UserName) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_v1_usercenter_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserName.ProtoReflect.Descriptor instead.
func (*UserName) Descriptor() ([]byte, []int) {
	return file_usercenter_v1_usercenter_proto_rawDescGZIP(), []int{12}
}

func (x *UserName) GetAccount() string {
	if x != nil {
		return x.Account
	}
	return ""
}

func (x *UserName) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

type GetSSHKeysForUserRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Account     string `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`
	Page        int32  `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	PerPage     int32  `protobuf:"varint,3,opt,name=perPage,proto3" json:"perPage,omitempty"`
	Region      string `protobuf:"bytes,4,opt,name=region,proto3" json:"region,omitempty"`
	HavePrivate bool   `protobuf:"varint,5,opt,name=havePrivate,proto3" json:"havePrivate,omitempty"`
}

func (x *GetSSHKeysForUserRequest) Reset() {
	*x = GetSSHKeysForUserRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_v1_usercenter_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSSHKeysForUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSSHKeysForUserRequest) ProtoMessage() {}

func (x *GetSSHKeysForUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_v1_usercenter_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSSHKeysForUserRequest.ProtoReflect.Descriptor instead.
func (*GetSSHKeysForUserRequest) Descriptor() ([]byte, []int) {
	return file_usercenter_v1_usercenter_proto_rawDescGZIP(), []int{13}
}

func (x *GetSSHKeysForUserRequest) GetAccount() string {
	if x != nil {
		return x.Account
	}
	return ""
}

func (x *GetSSHKeysForUserRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetSSHKeysForUserRequest) GetPerPage() int32 {
	if x != nil {
		return x.PerPage
	}
	return 0
}

func (x *GetSSHKeysForUserRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *GetSSHKeysForUserRequest) GetHavePrivate() bool {
	if x != nil {
		return x.HavePrivate
	}
	return false
}

type GetSSHKeyResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Msg   string    `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg,omitempty"`
	Items []*SSHKey `protobuf:"bytes,2,rep,name=items,proto3" json:"items,omitempty"`
	Count int32     `protobuf:"varint,3,opt,name=count,proto3" json:"count,omitempty"`
}

func (x *GetSSHKeyResponse) Reset() {
	*x = GetSSHKeyResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_v1_usercenter_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSSHKeyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSSHKeyResponse) ProtoMessage() {}

func (x *GetSSHKeyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_v1_usercenter_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSSHKeyResponse.ProtoReflect.Descriptor instead.
func (*GetSSHKeyResponse) Descriptor() ([]byte, []int) {
	return file_usercenter_v1_usercenter_proto_rawDescGZIP(), []int{14}
}

func (x *GetSSHKeyResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *GetSSHKeyResponse) GetItems() []*SSHKey {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *GetSSHKeyResponse) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

type AccessToken struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	AccessToken string `protobuf:"bytes,2,opt,name=accessToken,proto3" json:"accessToken,omitempty"`
	Deadline    string `protobuf:"bytes,3,opt,name=deadline,proto3" json:"deadline,omitempty"`
}

func (x *AccessToken) Reset() {
	*x = AccessToken{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_v1_usercenter_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AccessToken) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccessToken) ProtoMessage() {}

func (x *AccessToken) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_v1_usercenter_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccessToken.ProtoReflect.Descriptor instead.
func (*AccessToken) Descriptor() ([]byte, []int) {
	return file_usercenter_v1_usercenter_proto_rawDescGZIP(), []int{15}
}

func (x *AccessToken) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *AccessToken) GetAccessToken() string {
	if x != nil {
		return x.AccessToken
	}
	return ""
}

func (x *AccessToken) GetDeadline() string {
	if x != nil {
		return x.Deadline
	}
	return ""
}

type GenerateAccessTokenRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Account    string `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`
	ExpireTime int64  `protobuf:"varint,2,opt,name=expireTime,proto3" json:"expireTime,omitempty"`
}

func (x *GenerateAccessTokenRequest) Reset() {
	*x = GenerateAccessTokenRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_v1_usercenter_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GenerateAccessTokenRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenerateAccessTokenRequest) ProtoMessage() {}

func (x *GenerateAccessTokenRequest) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_v1_usercenter_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenerateAccessTokenRequest.ProtoReflect.Descriptor instead.
func (*GenerateAccessTokenRequest) Descriptor() ([]byte, []int) {
	return file_usercenter_v1_usercenter_proto_rawDescGZIP(), []int{16}
}

func (x *GenerateAccessTokenRequest) GetAccount() string {
	if x != nil {
		return x.Account
	}
	return ""
}

func (x *GenerateAccessTokenRequest) GetExpireTime() int64 {
	if x != nil {
		return x.ExpireTime
	}
	return 0
}

type GetAccessTokenRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Account string `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`
}

func (x *GetAccessTokenRequest) Reset() {
	*x = GetAccessTokenRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_v1_usercenter_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAccessTokenRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAccessTokenRequest) ProtoMessage() {}

func (x *GetAccessTokenRequest) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_v1_usercenter_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAccessTokenRequest.ProtoReflect.Descriptor instead.
func (*GetAccessTokenRequest) Descriptor() ([]byte, []int) {
	return file_usercenter_v1_usercenter_proto_rawDescGZIP(), []int{17}
}

func (x *GetAccessTokenRequest) GetAccount() string {
	if x != nil {
		return x.Account
	}
	return ""
}

type GetAccessTokenResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AccessTokens []*AccessToken `protobuf:"bytes,1,rep,name=accessTokens,proto3" json:"accessTokens,omitempty"`
}

func (x *GetAccessTokenResponse) Reset() {
	*x = GetAccessTokenResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_v1_usercenter_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAccessTokenResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAccessTokenResponse) ProtoMessage() {}

func (x *GetAccessTokenResponse) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_v1_usercenter_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAccessTokenResponse.ProtoReflect.Descriptor instead.
func (*GetAccessTokenResponse) Descriptor() ([]byte, []int) {
	return file_usercenter_v1_usercenter_proto_rawDescGZIP(), []int{18}
}

func (x *GetAccessTokenResponse) GetAccessTokens() []*AccessToken {
	if x != nil {
		return x.AccessTokens
	}
	return nil
}

type DeleteAccessTokenRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id      string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Account string `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty"`
}

func (x *DeleteAccessTokenRequest) Reset() {
	*x = DeleteAccessTokenRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_v1_usercenter_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteAccessTokenRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteAccessTokenRequest) ProtoMessage() {}

func (x *DeleteAccessTokenRequest) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_v1_usercenter_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteAccessTokenRequest.ProtoReflect.Descriptor instead.
func (*DeleteAccessTokenRequest) Descriptor() ([]byte, []int) {
	return file_usercenter_v1_usercenter_proto_rawDescGZIP(), []int{19}
}

func (x *DeleteAccessTokenRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *DeleteAccessTokenRequest) GetAccount() string {
	if x != nil {
		return x.Account
	}
	return ""
}

type CreateSSHKeyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name          string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Account       string `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty"`
	Content       string `protobuf:"bytes,3,opt,name=content,proto3" json:"content,omitempty"`
	Region        string `protobuf:"bytes,4,opt,name=region,proto3" json:"region,omitempty"`
	SshKeyPrivate string `protobuf:"bytes,5,opt,name=sshKeyPrivate,proto3" json:"sshKeyPrivate,omitempty"`
	CreateType    string `protobuf:"bytes,6,opt,name=createType,proto3" json:"createType,omitempty"`
}

func (x *CreateSSHKeyRequest) Reset() {
	*x = CreateSSHKeyRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_v1_usercenter_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateSSHKeyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateSSHKeyRequest) ProtoMessage() {}

func (x *CreateSSHKeyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_v1_usercenter_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateSSHKeyRequest.ProtoReflect.Descriptor instead.
func (*CreateSSHKeyRequest) Descriptor() ([]byte, []int) {
	return file_usercenter_v1_usercenter_proto_rawDescGZIP(), []int{20}
}

func (x *CreateSSHKeyRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateSSHKeyRequest) GetAccount() string {
	if x != nil {
		return x.Account
	}
	return ""
}

func (x *CreateSSHKeyRequest) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *CreateSSHKeyRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *CreateSSHKeyRequest) GetSshKeyPrivate() string {
	if x != nil {
		return x.SshKeyPrivate
	}
	return ""
}

func (x *CreateSSHKeyRequest) GetCreateType() string {
	if x != nil {
		return x.CreateType
	}
	return ""
}

type CreateSSHKeyResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status int32   `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	Msg    string  `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	SSHKey *SSHKey `protobuf:"bytes,3,opt,name=SSHKey,proto3" json:"SSHKey,omitempty"`
}

func (x *CreateSSHKeyResponse) Reset() {
	*x = CreateSSHKeyResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_v1_usercenter_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateSSHKeyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateSSHKeyResponse) ProtoMessage() {}

func (x *CreateSSHKeyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_v1_usercenter_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateSSHKeyResponse.ProtoReflect.Descriptor instead.
func (*CreateSSHKeyResponse) Descriptor() ([]byte, []int) {
	return file_usercenter_v1_usercenter_proto_rawDescGZIP(), []int{21}
}

func (x *CreateSSHKeyResponse) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *CreateSSHKeyResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *CreateSSHKeyResponse) GetSSHKey() *SSHKey {
	if x != nil {
		return x.SSHKey
	}
	return nil
}

type UpdateSSHKeyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name          string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Account       string `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty"`
	Content       string `protobuf:"bytes,3,opt,name=content,proto3" json:"content,omitempty"`
	Region        string `protobuf:"bytes,4,opt,name=region,proto3" json:"region,omitempty"`
	SshKeyPrivate string `protobuf:"bytes,5,opt,name=sshKeyPrivate,proto3" json:"sshKeyPrivate,omitempty"`
}

func (x *UpdateSSHKeyRequest) Reset() {
	*x = UpdateSSHKeyRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_v1_usercenter_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateSSHKeyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateSSHKeyRequest) ProtoMessage() {}

func (x *UpdateSSHKeyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_v1_usercenter_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateSSHKeyRequest.ProtoReflect.Descriptor instead.
func (*UpdateSSHKeyRequest) Descriptor() ([]byte, []int) {
	return file_usercenter_v1_usercenter_proto_rawDescGZIP(), []int{22}
}

func (x *UpdateSSHKeyRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UpdateSSHKeyRequest) GetAccount() string {
	if x != nil {
		return x.Account
	}
	return ""
}

func (x *UpdateSSHKeyRequest) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *UpdateSSHKeyRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *UpdateSSHKeyRequest) GetSshKeyPrivate() string {
	if x != nil {
		return x.SshKeyPrivate
	}
	return ""
}

type UpdateSSHKeyResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Msg    string `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg,omitempty"`
	Status int32  `protobuf:"varint,2,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *UpdateSSHKeyResponse) Reset() {
	*x = UpdateSSHKeyResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_v1_usercenter_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateSSHKeyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateSSHKeyResponse) ProtoMessage() {}

func (x *UpdateSSHKeyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_v1_usercenter_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateSSHKeyResponse.ProtoReflect.Descriptor instead.
func (*UpdateSSHKeyResponse) Descriptor() ([]byte, []int) {
	return file_usercenter_v1_usercenter_proto_rawDescGZIP(), []int{23}
}

func (x *UpdateSSHKeyResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *UpdateSSHKeyResponse) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

type DeleteSSHKeyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name    string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Account string `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty"`
	Region  string `protobuf:"bytes,3,opt,name=region,proto3" json:"region,omitempty"`
}

func (x *DeleteSSHKeyRequest) Reset() {
	*x = DeleteSSHKeyRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_v1_usercenter_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteSSHKeyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteSSHKeyRequest) ProtoMessage() {}

func (x *DeleteSSHKeyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_v1_usercenter_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteSSHKeyRequest.ProtoReflect.Descriptor instead.
func (*DeleteSSHKeyRequest) Descriptor() ([]byte, []int) {
	return file_usercenter_v1_usercenter_proto_rawDescGZIP(), []int{24}
}

func (x *DeleteSSHKeyRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DeleteSSHKeyRequest) GetAccount() string {
	if x != nil {
		return x.Account
	}
	return ""
}

func (x *DeleteSSHKeyRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

type DeleteSSHKeyResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Msg string `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg,omitempty"`
}

func (x *DeleteSSHKeyResponse) Reset() {
	*x = DeleteSSHKeyResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_v1_usercenter_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteSSHKeyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteSSHKeyResponse) ProtoMessage() {}

func (x *DeleteSSHKeyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_v1_usercenter_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteSSHKeyResponse.ProtoReflect.Descriptor instead.
func (*DeleteSSHKeyResponse) Descriptor() ([]byte, []int) {
	return file_usercenter_v1_usercenter_proto_rawDescGZIP(), []int{25}
}

func (x *DeleteSSHKeyResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type CheckSSHKeyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name    string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Account string `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty"`
	Region  string `protobuf:"bytes,3,opt,name=region,proto3" json:"region,omitempty"`
	Content string `protobuf:"bytes,4,opt,name=content,proto3" json:"content,omitempty"`
}

func (x *CheckSSHKeyRequest) Reset() {
	*x = CheckSSHKeyRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_v1_usercenter_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckSSHKeyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckSSHKeyRequest) ProtoMessage() {}

func (x *CheckSSHKeyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_v1_usercenter_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckSSHKeyRequest.ProtoReflect.Descriptor instead.
func (*CheckSSHKeyRequest) Descriptor() ([]byte, []int) {
	return file_usercenter_v1_usercenter_proto_rawDescGZIP(), []int{26}
}

func (x *CheckSSHKeyRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CheckSSHKeyRequest) GetAccount() string {
	if x != nil {
		return x.Account
	}
	return ""
}

func (x *CheckSSHKeyRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *CheckSSHKeyRequest) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

type CheckSSHKeyResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Errors string `protobuf:"bytes,1,opt,name=errors,proto3" json:"errors,omitempty"`
	Status int32  `protobuf:"varint,2,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *CheckSSHKeyResponse) Reset() {
	*x = CheckSSHKeyResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_v1_usercenter_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckSSHKeyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckSSHKeyResponse) ProtoMessage() {}

func (x *CheckSSHKeyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_v1_usercenter_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckSSHKeyResponse.ProtoReflect.Descriptor instead.
func (*CheckSSHKeyResponse) Descriptor() ([]byte, []int) {
	return file_usercenter_v1_usercenter_proto_rawDescGZIP(), []int{27}
}

func (x *CheckSSHKeyResponse) GetErrors() string {
	if x != nil {
		return x.Errors
	}
	return ""
}

func (x *CheckSSHKeyResponse) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

type SSHKey struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ID            int32  `protobuf:"varint,1,opt,name=ID,proto3" json:"ID,omitempty"`
	Name          string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Content       string `protobuf:"bytes,3,opt,name=content,proto3" json:"content,omitempty"`
	SshKeyPrivate string `protobuf:"bytes,4,opt,name=sshKeyPrivate,proto3" json:"sshKeyPrivate,omitempty"`
}

func (x *SSHKey) Reset() {
	*x = SSHKey{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_v1_usercenter_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SSHKey) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SSHKey) ProtoMessage() {}

func (x *SSHKey) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_v1_usercenter_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SSHKey.ProtoReflect.Descriptor instead.
func (*SSHKey) Descriptor() ([]byte, []int) {
	return file_usercenter_v1_usercenter_proto_rawDescGZIP(), []int{28}
}

func (x *SSHKey) GetID() int32 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *SSHKey) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SSHKey) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *SSHKey) GetSshKeyPrivate() string {
	if x != nil {
		return x.SshKeyPrivate
	}
	return ""
}

type WhoamiRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *WhoamiRequest) Reset() {
	*x = WhoamiRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_v1_usercenter_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WhoamiRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WhoamiRequest) ProtoMessage() {}

func (x *WhoamiRequest) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_v1_usercenter_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WhoamiRequest.ProtoReflect.Descriptor instead.
func (*WhoamiRequest) Descriptor() ([]byte, []int) {
	return file_usercenter_v1_usercenter_proto_rawDescGZIP(), []int{29}
}

type WhoamiResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Account string `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`
}

func (x *WhoamiResponse) Reset() {
	*x = WhoamiResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_v1_usercenter_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WhoamiResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WhoamiResponse) ProtoMessage() {}

func (x *WhoamiResponse) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_v1_usercenter_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WhoamiResponse.ProtoReflect.Descriptor instead.
func (*WhoamiResponse) Descriptor() ([]byte, []int) {
	return file_usercenter_v1_usercenter_proto_rawDescGZIP(), []int{30}
}

func (x *WhoamiResponse) GetAccount() string {
	if x != nil {
		return x.Account
	}
	return ""
}

var File_usercenter_v1_usercenter_proto protoreflect.FileDescriptor

var file_usercenter_v1_usercenter_proto_rawDesc = []byte{
	0x0a, 0x1e, 0x75, 0x73, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f,
	0x75, 0x73, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x12, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x13, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x31, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x48, 0x61, 0x64, 0x6f,
	0x6f, 0x70, 0x55, 0x73, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x18,
	0x0a, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x3a, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x48,
	0x61, 0x64, 0x6f, 0x6f, 0x70, 0x55, 0x73, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x68, 0x61, 0x64, 0x6f, 0x6f, 0x70, 0x55, 0x73, 0x65, 0x72,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x68, 0x61, 0x64, 0x6f, 0x6f, 0x70, 0x55,
	0x73, 0x65, 0x72, 0x73, 0x22, 0x9c, 0x03, 0x0a, 0x0a, 0x55, 0x73, 0x65, 0x72, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x12, 0x39, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x40,
	0x0a, 0x0d, 0x6c, 0x61, 0x73, 0x74, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x52, 0x0d, 0x6c, 0x61, 0x73, 0x74, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x4d, 0x0a, 0x0f, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75,
	0x74, 0x65, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x73,
	0x2e, 0x75, 0x73, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x52,
	0x6f, 0x62, 0x6f, 0x74, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x52, 0x0f,
	0x72, 0x6f, 0x62, 0x6f, 0x74, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x12,
	0x4a, 0x0a, 0x0e, 0x75, 0x73, 0x65, 0x72, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65,
	0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x75,
	0x73, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65,
	0x72, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x52, 0x0e, 0x75, 0x73, 0x65,
	0x72, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x38, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x54, 0x79, 0x70,
	0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x75,
	0x73, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65,
	0x72, 0x54, 0x79, 0x70, 0x65, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x22, 0x0a, 0x0c, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x52, 0x6f, 0x6c, 0x65, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x52,
	0x6f, 0x6c, 0x65, 0x22, 0x84, 0x02, 0x0a, 0x0e, 0x55, 0x73, 0x65, 0x72, 0x41, 0x74, 0x74, 0x72,
	0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x16, 0x0a, 0x06,
	0x63, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x6e,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x4e, 0x61, 0x6d, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x18, 0x0a, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x76,
	0x61, 0x74, 0x61, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x76, 0x61, 0x74,
	0x61, 0x72, 0x12, 0x26, 0x0a, 0x0e, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x70, 0x6f, 0x73, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x6f, 0x72,
	0x67, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x72, 0x67,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6f, 0x72, 0x67, 0x43, 0x6f, 0x64, 0x65, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x72, 0x67, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1a,
	0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x22, 0xc1, 0x02, 0x0a, 0x0f, 0x52,
	0x6f, 0x62, 0x6f, 0x74, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x12, 0x18,
	0x0a, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73,
	0x77, 0x6f, 0x72, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73,
	0x77, 0x6f, 0x72, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e,
	0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c,
	0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x20, 0x0a, 0x0b, 0x61, 0x63, 0x63, 0x65,
	0x73, 0x73, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61,
	0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x2a, 0x0a, 0x10, 0x77, 0x65,
	0x43, 0x68, 0x61, 0x74, 0x52, 0x6f, 0x62, 0x6f, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x77, 0x65, 0x43, 0x68, 0x61, 0x74, 0x52, 0x6f, 0x62, 0x6f,
	0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x1e, 0x0a, 0x0a, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x77, 0x6f, 0x72, 0x6b,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x73, 0x12, 0x28, 0x0a, 0x0f, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e,
	0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0f, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x12, 0x1c, 0x0a, 0x09, 0x69, 0x73, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x69, 0x73, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x22, 0x85,
	0x02, 0x0a, 0x19, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x18, 0x0a, 0x07,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x3e, 0x0a, 0x0a, 0x75, 0x73, 0x65, 0x72, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x61, 0x70, 0x69,
	0x73, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e,
	0x55, 0x73, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x0a, 0x75, 0x73, 0x65, 0x72,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f,
	0x72, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f,
	0x72, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x18,
	0x0a, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x18, 0x0a, 0x07, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x72, 0x12, 0x22, 0x0a, 0x0c, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x52, 0x6f,
	0x6c, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f,
	0x72, 0x6d, 0x52, 0x6f, 0x6c, 0x65, 0x22, 0x2d, 0x0a, 0x11, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x30, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x18, 0x0a,
	0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0xd7, 0x01, 0x0a, 0x0b, 0x4c, 0x69, 0x73, 0x74,
	0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x61, 0x67, 0x65, 0x4e,
	0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x70, 0x61, 0x67, 0x65, 0x4e, 0x6f, 0x12,
	0x1a, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x72,
	0x6f, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72,
	0x6f, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x54,
	0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x24, 0x0a,
	0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x69, 0x73, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x69, 0x73, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c,
	0x74, 0x22, 0x91, 0x01, 0x0a, 0x0f, 0x4c, 0x69, 0x73, 0x74, 0x55, 0x73, 0x65, 0x72, 0x73, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x70,
	0x61, 0x67, 0x65, 0x4e, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x70, 0x61, 0x67,
	0x65, 0x4e, 0x6f, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12,
	0x34, 0x0a, 0x05, 0x75, 0x73, 0x65, 0x72, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e,
	0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x05,
	0x75, 0x73, 0x65, 0x72, 0x73, 0x22, 0x71, 0x0a, 0x11, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x55,
	0x73, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72,
	0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x69, 0x73,
	0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x69,
	0x73, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x22, 0x46, 0x0a, 0x10, 0x53, 0x65, 0x61, 0x72,
	0x63, 0x68, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x32, 0x0a, 0x05,
	0x75, 0x73, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x61, 0x70,
	0x69, 0x73, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x2e, 0x55, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x05, 0x75, 0x73, 0x65, 0x72, 0x73,
	0x22, 0x46, 0x0a, 0x08, 0x55, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61,
	0x79, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x69, 0x73,
	0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x9c, 0x01, 0x0a, 0x18, 0x47, 0x65, 0x74,
	0x53, 0x53, 0x48, 0x4b, 0x65, 0x79, 0x73, 0x46, 0x6f, 0x72, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70,
	0x61, 0x67, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x65, 0x72, 0x50, 0x61, 0x67, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x70, 0x65, 0x72, 0x50, 0x61, 0x67, 0x65, 0x12, 0x16, 0x0a,
	0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72,
	0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x20, 0x0a, 0x0b, 0x68, 0x61, 0x76, 0x65, 0x50, 0x72, 0x69,
	0x76, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x68, 0x61, 0x76, 0x65,
	0x50, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x22, 0x6d, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x53, 0x53,
	0x48, 0x4b, 0x65, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x10, 0x0a, 0x03,
	0x6d, 0x73, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x30,
	0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x61, 0x70, 0x69, 0x73, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x53, 0x48, 0x4b, 0x65, 0x79, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73,
	0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x5b, 0x0a, 0x0b, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54,
	0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x63, 0x63, 0x65,
	0x73, 0x73, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x65, 0x61, 0x64, 0x6c,
	0x69, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x65, 0x61, 0x64, 0x6c,
	0x69, 0x6e, 0x65, 0x22, 0x56, 0x0a, 0x1a, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x41,
	0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x65,
	0x78, 0x70, 0x69, 0x72, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0a, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x31, 0x0a, 0x15, 0x47,
	0x65, 0x74, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x5d,
	0x0a, 0x16, 0x47, 0x65, 0x74, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x6f, 0x6b, 0x65, 0x6e,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x43, 0x0a, 0x0c, 0x61, 0x63, 0x63, 0x65,
	0x73, 0x73, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f,
	0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52,
	0x0c, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x73, 0x22, 0x44, 0x0a,
	0x18, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x6f, 0x6b,
	0x65, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x22, 0xbb, 0x01, 0x0a, 0x13, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x53,
	0x48, 0x4b, 0x65, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x18, 0x0a, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x24, 0x0a, 0x0d, 0x73,
	0x73, 0x68, 0x4b, 0x65, 0x79, 0x50, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x73, 0x73, 0x68, 0x4b, 0x65, 0x79, 0x50, 0x72, 0x69, 0x76, 0x61, 0x74,
	0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x22, 0x74, 0x0a, 0x14, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x53, 0x48, 0x4b, 0x65,
	0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6d, 0x73, 0x67, 0x12, 0x32, 0x0a, 0x06, 0x53, 0x53, 0x48, 0x4b, 0x65, 0x79, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x63,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x53, 0x48, 0x4b, 0x65, 0x79, 0x52,
	0x06, 0x53, 0x53, 0x48, 0x4b, 0x65, 0x79, 0x22, 0x9b, 0x01, 0x0a, 0x13, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x53, 0x53, 0x48, 0x4b, 0x65, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x18, 0x0a,
	0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f,
	0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12,
	0x24, 0x0a, 0x0d, 0x73, 0x73, 0x68, 0x4b, 0x65, 0x79, 0x50, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x73, 0x68, 0x4b, 0x65, 0x79, 0x50, 0x72,
	0x69, 0x76, 0x61, 0x74, 0x65, 0x22, 0x40, 0x0a, 0x14, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53,
	0x53, 0x48, 0x4b, 0x65, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x10, 0x0a,
	0x03, 0x6d, 0x73, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12,
	0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x5b, 0x0a, 0x13, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x53, 0x53, 0x48, 0x4b, 0x65, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x16, 0x0a, 0x06,
	0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65,
	0x67, 0x69, 0x6f, 0x6e, 0x22, 0x28, 0x0a, 0x14, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x53,
	0x48, 0x4b, 0x65, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x10, 0x0a, 0x03,
	0x6d, 0x73, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x22, 0x74,
	0x0a, 0x12, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x53, 0x53, 0x48, 0x4b, 0x65, 0x79, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x22, 0x45, 0x0a, 0x13, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x53, 0x53, 0x48,
	0x4b, 0x65, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x6c, 0x0a, 0x06, 0x53,
	0x53, 0x48, 0x4b, 0x65, 0x79, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x02, 0x49, 0x44, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x73, 0x73, 0x68, 0x4b, 0x65, 0x79, 0x50, 0x72, 0x69,
	0x76, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x73, 0x68, 0x4b,
	0x65, 0x79, 0x50, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x22, 0x0f, 0x0a, 0x0d, 0x57, 0x68, 0x6f,
	0x61, 0x6d, 0x69, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x2a, 0x0a, 0x0e, 0x57, 0x68,
	0x6f, 0x61, 0x6d, 0x69, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2a, 0x1f, 0x0a, 0x08, 0x55, 0x73, 0x65, 0x72, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x08, 0x0a, 0x04, 0x55, 0x73, 0x65, 0x72, 0x10, 0x00, 0x12, 0x09, 0x0a, 0x05,
	0x52, 0x6f, 0x62, 0x6f, 0x74, 0x10, 0x01, 0x32, 0x91, 0x10, 0x0a, 0x0b, 0x55, 0x73, 0x65, 0x72,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x9b, 0x01, 0x0a, 0x12, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x4f, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x12, 0x2d,
	0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e,
	0x61, 0x70, 0x69, 0x73, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x22, 0x36, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x30, 0x3a, 0x01, 0x2a, 0x5a, 0x1c, 0x3a, 0x01, 0x2a, 0x1a, 0x17, 0x2f,
	0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x2f, 0x7b, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x7d, 0x22, 0x0d, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31,
	0x2f, 0x75, 0x73, 0x65, 0x72, 0x12, 0x6c, 0x0a, 0x0a, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x55,
	0x73, 0x65, 0x72, 0x12, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x63,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x55,
	0x73, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70,
	0x74, 0x79, 0x22, 0x1f, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x19, 0x2a, 0x17, 0x2f, 0x61, 0x70, 0x69,
	0x73, 0x2f, 0x76, 0x31, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x2f, 0x7b, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x7d, 0x12, 0x74, 0x0a, 0x07, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x12, 0x28,
	0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e,
	0x75, 0x73, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73,
	0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x22, 0x1f, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x19,
	0x12, 0x17, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x2f,
	0x7b, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x7d, 0x12, 0x69, 0x0a, 0x09, 0x4c, 0x69, 0x73,
	0x74, 0x55, 0x73, 0x65, 0x72, 0x73, 0x12, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x75, 0x73,
	0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74,
	0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x1a, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x75,
	0x73, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x55, 0x73, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x16, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x10, 0x12, 0x0e, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x75,
	0x73, 0x65, 0x72, 0x73, 0x12, 0x77, 0x0a, 0x0a, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x55, 0x73,
	0x65, 0x72, 0x12, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x63, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x55, 0x73,
	0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x73,
	0x2e, 0x75, 0x73, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22,
	0x1c, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x16, 0x12, 0x14, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76,
	0x31, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x12, 0x92, 0x01,
	0x0a, 0x11, 0x47, 0x65, 0x74, 0x53, 0x53, 0x48, 0x4b, 0x65, 0x79, 0x73, 0x46, 0x6f, 0x72, 0x55,
	0x73, 0x65, 0x72, 0x12, 0x2c, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x63,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x53, 0x48, 0x4b,
	0x65, 0x79, 0x73, 0x46, 0x6f, 0x72, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x63, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x53, 0x48, 0x4b, 0x65, 0x79,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x28, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x22,
	0x12, 0x20, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x2f,
	0x7b, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x7d, 0x2f, 0x73, 0x73, 0x68, 0x2d, 0x6b, 0x65,
	0x79, 0x73, 0x12, 0x8d, 0x01, 0x0a, 0x0c, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x53, 0x48,
	0x4b, 0x65, 0x79, 0x12, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x63,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53,
	0x53, 0x48, 0x4b, 0x65, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x61,
	0x70, 0x69, 0x73, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x53, 0x48, 0x4b, 0x65, 0x79, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x2a, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x24, 0x3a, 0x01,
	0x2a, 0x22, 0x1f, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x75, 0x73, 0x65, 0x72,
	0x2f, 0x7b, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x7d, 0x2f, 0x73, 0x73, 0x68, 0x2d, 0x6b,
	0x65, 0x79, 0x12, 0x94, 0x01, 0x0a, 0x0c, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x53, 0x48,
	0x4b, 0x65, 0x79, 0x12, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x63,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53,
	0x53, 0x48, 0x4b, 0x65, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x61,
	0x70, 0x69, 0x73, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76,
	0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x53, 0x48, 0x4b, 0x65, 0x79, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x31, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2b, 0x3a, 0x01,
	0x2a, 0x1a, 0x26, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x75, 0x73, 0x65, 0x72,
	0x2f, 0x7b, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x7d, 0x2f, 0x73, 0x73, 0x68, 0x2d, 0x6b,
	0x65, 0x79, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x12, 0x91, 0x01, 0x0a, 0x0c, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x53, 0x53, 0x48, 0x4b, 0x65, 0x79, 0x12, 0x27, 0x2e, 0x61, 0x70, 0x69,
	0x73, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x53, 0x48, 0x4b, 0x65, 0x79, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x63,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53,
	0x53, 0x48, 0x4b, 0x65, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x2e, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x28, 0x2a, 0x26, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f,
	0x75, 0x73, 0x65, 0x72, 0x2f, 0x7b, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x7d, 0x2f, 0x73,
	0x73, 0x68, 0x2d, 0x6b, 0x65, 0x79, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x12, 0x8d, 0x01,
	0x0a, 0x0b, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x53, 0x53, 0x48, 0x4b, 0x65, 0x79, 0x12, 0x26, 0x2e,
	0x61, 0x70, 0x69, 0x73, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x53, 0x53, 0x48, 0x4b, 0x65, 0x79, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x75, 0x73, 0x65,
	0x72, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b,
	0x53, 0x53, 0x48, 0x4b, 0x65, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x2d,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x27, 0x12, 0x25, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31,
	0x2f, 0x75, 0x73, 0x65, 0x72, 0x2f, 0x7b, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x7d, 0x2f,
	0x73, 0x73, 0x68, 0x2d, 0x6b, 0x65, 0x79, 0x2f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x12, 0x8e, 0x01,
	0x0a, 0x13, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x75, 0x73, 0x65,
	0x72, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x6e, 0x65, 0x72,
	0x61, 0x74, 0x65, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x2f, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x29, 0x3a, 0x01, 0x2a, 0x22, 0x24, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f,
	0x76, 0x31, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x2f, 0x7b, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x7d, 0x2f, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x95,
	0x01, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x6f, 0x6b, 0x65,
	0x6e, 0x12, 0x29, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x63, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2a, 0x2e, 0x61,
	0x70, 0x69, 0x73, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x6f, 0x6b, 0x65, 0x6e,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x2c, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x26,
	0x12, 0x24, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x2f,
	0x7b, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x7d, 0x2f, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x8c, 0x01, 0x0a, 0x11, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x2c, 0x2e, 0x61,
	0x70, 0x69, 0x73, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76,
	0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x6f,
	0x6b, 0x65, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70,
	0x74, 0x79, 0x22, 0x31, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2b, 0x2a, 0x29, 0x2f, 0x61, 0x70, 0x69,
	0x73, 0x2f, 0x76, 0x31, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x2f, 0x7b, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x7d, 0x2f, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e,
	0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x12, 0x6d, 0x0a, 0x06, 0x57, 0x68, 0x6f, 0x61, 0x6d, 0x69, 0x12,
	0x21, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x68, 0x6f, 0x61, 0x6d, 0x69, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x63, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x68, 0x6f, 0x61, 0x6d, 0x69, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x1c, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x16, 0x12, 0x14,
	0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x77, 0x68,
	0x6f, 0x61, 0x6d, 0x69, 0x12, 0x94, 0x01, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x48, 0x61, 0x64, 0x6f,
	0x6f, 0x70, 0x55, 0x73, 0x65, 0x72, 0x73, 0x12, 0x29, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x75,
	0x73, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74,
	0x48, 0x61, 0x64, 0x6f, 0x6f, 0x70, 0x55, 0x73, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x2a, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x63, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x48, 0x61, 0x64, 0x6f, 0x6f,
	0x70, 0x55, 0x73, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x2b,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x25, 0x12, 0x23, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31,
	0x2f, 0x75, 0x73, 0x65, 0x72, 0x2f, 0x7b, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x7d, 0x2f,
	0x68, 0x61, 0x64, 0x6f, 0x6f, 0x70, 0x75, 0x73, 0x65, 0x72, 0x73, 0x42, 0x44, 0x5a, 0x42, 0x67,
	0x69, 0x74, 0x2e, 0x6c, 0x69, 0x61, 0x6e, 0x6a, 0x69, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x63,
	0x6c, 0x6f, 0x75, 0x64, 0x6e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x2f, 0x6b, 0x69, 0x63, 0x2f, 0x6b,
	0x69, 0x63, 0x2d, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x61, 0x70, 0x69, 0x73,
	0x2f, 0x75, 0x73, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x3b, 0x76,
	0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_usercenter_v1_usercenter_proto_rawDescOnce sync.Once
	file_usercenter_v1_usercenter_proto_rawDescData = file_usercenter_v1_usercenter_proto_rawDesc
)

func file_usercenter_v1_usercenter_proto_rawDescGZIP() []byte {
	file_usercenter_v1_usercenter_proto_rawDescOnce.Do(func() {
		file_usercenter_v1_usercenter_proto_rawDescData = protoimpl.X.CompressGZIP(file_usercenter_v1_usercenter_proto_rawDescData)
	})
	return file_usercenter_v1_usercenter_proto_rawDescData
}

var file_usercenter_v1_usercenter_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_usercenter_v1_usercenter_proto_msgTypes = make([]protoimpl.MessageInfo, 31)
var file_usercenter_v1_usercenter_proto_goTypes = []any{
	(UserType)(0),                      // 0: apis.usercenter.v1.UserType
	(*GetHadoopUsersRequest)(nil),      // 1: apis.usercenter.v1.GetHadoopUsersRequest
	(*GetHadoopUsersResponse)(nil),     // 2: apis.usercenter.v1.GetHadoopUsersResponse
	(*UserDetail)(nil),                 // 3: apis.usercenter.v1.UserDetail
	(*UserAttributes)(nil),             // 4: apis.usercenter.v1.UserAttributes
	(*RobotAttributes)(nil),            // 5: apis.usercenter.v1.RobotAttributes
	(*CreateOrUpdateUserRequest)(nil),  // 6: apis.usercenter.v1.CreateOrUpdateUserRequest
	(*DeleteUserRequest)(nil),          // 7: apis.usercenter.v1.DeleteUserRequest
	(*GetUserDetailRequest)(nil),       // 8: apis.usercenter.v1.GetUserDetailRequest
	(*ListOptions)(nil),                // 9: apis.usercenter.v1.ListOptions
	(*ListUsersResult)(nil),            // 10: apis.usercenter.v1.ListUsersResult
	(*SearchUserRequest)(nil),          // 11: apis.usercenter.v1.SearchUserRequest
	(*SearchUserResult)(nil),           // 12: apis.usercenter.v1.SearchUserResult
	(*UserName)(nil),                   // 13: apis.usercenter.v1.UserName
	(*GetSSHKeysForUserRequest)(nil),   // 14: apis.usercenter.v1.GetSSHKeysForUserRequest
	(*GetSSHKeyResponse)(nil),          // 15: apis.usercenter.v1.GetSSHKeyResponse
	(*AccessToken)(nil),                // 16: apis.usercenter.v1.AccessToken
	(*GenerateAccessTokenRequest)(nil), // 17: apis.usercenter.v1.GenerateAccessTokenRequest
	(*GetAccessTokenRequest)(nil),      // 18: apis.usercenter.v1.GetAccessTokenRequest
	(*GetAccessTokenResponse)(nil),     // 19: apis.usercenter.v1.GetAccessTokenResponse
	(*DeleteAccessTokenRequest)(nil),   // 20: apis.usercenter.v1.DeleteAccessTokenRequest
	(*CreateSSHKeyRequest)(nil),        // 21: apis.usercenter.v1.CreateSSHKeyRequest
	(*CreateSSHKeyResponse)(nil),       // 22: apis.usercenter.v1.CreateSSHKeyResponse
	(*UpdateSSHKeyRequest)(nil),        // 23: apis.usercenter.v1.UpdateSSHKeyRequest
	(*UpdateSSHKeyResponse)(nil),       // 24: apis.usercenter.v1.UpdateSSHKeyResponse
	(*DeleteSSHKeyRequest)(nil),        // 25: apis.usercenter.v1.DeleteSSHKeyRequest
	(*DeleteSSHKeyResponse)(nil),       // 26: apis.usercenter.v1.DeleteSSHKeyResponse
	(*CheckSSHKeyRequest)(nil),         // 27: apis.usercenter.v1.CheckSSHKeyRequest
	(*CheckSSHKeyResponse)(nil),        // 28: apis.usercenter.v1.CheckSSHKeyResponse
	(*SSHKey)(nil),                     // 29: apis.usercenter.v1.SSHKey
	(*WhoamiRequest)(nil),              // 30: apis.usercenter.v1.WhoamiRequest
	(*WhoamiResponse)(nil),             // 31: apis.usercenter.v1.WhoamiResponse
	(*common.TimestampModel)(nil),      // 32: apis.common.TimestampModel
	(*timestamppb.Timestamp)(nil),      // 33: google.protobuf.Timestamp
	(*emptypb.Empty)(nil),              // 34: google.protobuf.Empty
}
var file_usercenter_v1_usercenter_proto_depIdxs = []int32{
	32, // 0: apis.usercenter.v1.UserDetail.timestamp:type_name -> apis.common.TimestampModel
	33, // 1: apis.usercenter.v1.UserDetail.lastLoginTime:type_name -> google.protobuf.Timestamp
	5,  // 2: apis.usercenter.v1.UserDetail.robotAttributes:type_name -> apis.usercenter.v1.RobotAttributes
	4,  // 3: apis.usercenter.v1.UserDetail.userAttributes:type_name -> apis.usercenter.v1.UserAttributes
	0,  // 4: apis.usercenter.v1.UserDetail.userType:type_name -> apis.usercenter.v1.UserType
	3,  // 5: apis.usercenter.v1.CreateOrUpdateUserRequest.userDetail:type_name -> apis.usercenter.v1.UserDetail
	3,  // 6: apis.usercenter.v1.ListUsersResult.users:type_name -> apis.usercenter.v1.UserDetail
	13, // 7: apis.usercenter.v1.SearchUserResult.users:type_name -> apis.usercenter.v1.UserName
	29, // 8: apis.usercenter.v1.GetSSHKeyResponse.items:type_name -> apis.usercenter.v1.SSHKey
	16, // 9: apis.usercenter.v1.GetAccessTokenResponse.accessTokens:type_name -> apis.usercenter.v1.AccessToken
	29, // 10: apis.usercenter.v1.CreateSSHKeyResponse.SSHKey:type_name -> apis.usercenter.v1.SSHKey
	6,  // 11: apis.usercenter.v1.UserService.CreateOrUpdateUser:input_type -> apis.usercenter.v1.CreateOrUpdateUserRequest
	7,  // 12: apis.usercenter.v1.UserService.DeleteUser:input_type -> apis.usercenter.v1.DeleteUserRequest
	8,  // 13: apis.usercenter.v1.UserService.GetUser:input_type -> apis.usercenter.v1.GetUserDetailRequest
	9,  // 14: apis.usercenter.v1.UserService.ListUsers:input_type -> apis.usercenter.v1.ListOptions
	11, // 15: apis.usercenter.v1.UserService.SearchUser:input_type -> apis.usercenter.v1.SearchUserRequest
	14, // 16: apis.usercenter.v1.UserService.GetSSHKeysForUser:input_type -> apis.usercenter.v1.GetSSHKeysForUserRequest
	21, // 17: apis.usercenter.v1.UserService.CreateSSHKey:input_type -> apis.usercenter.v1.CreateSSHKeyRequest
	23, // 18: apis.usercenter.v1.UserService.UpdateSSHKey:input_type -> apis.usercenter.v1.UpdateSSHKeyRequest
	25, // 19: apis.usercenter.v1.UserService.DeleteSSHKey:input_type -> apis.usercenter.v1.DeleteSSHKeyRequest
	27, // 20: apis.usercenter.v1.UserService.CheckSSHKey:input_type -> apis.usercenter.v1.CheckSSHKeyRequest
	17, // 21: apis.usercenter.v1.UserService.GenerateAccessToken:input_type -> apis.usercenter.v1.GenerateAccessTokenRequest
	18, // 22: apis.usercenter.v1.UserService.GetAccessToken:input_type -> apis.usercenter.v1.GetAccessTokenRequest
	20, // 23: apis.usercenter.v1.UserService.DeleteAccessToken:input_type -> apis.usercenter.v1.DeleteAccessTokenRequest
	30, // 24: apis.usercenter.v1.UserService.Whoami:input_type -> apis.usercenter.v1.WhoamiRequest
	1,  // 25: apis.usercenter.v1.UserService.GetHadoopUsers:input_type -> apis.usercenter.v1.GetHadoopUsersRequest
	3,  // 26: apis.usercenter.v1.UserService.CreateOrUpdateUser:output_type -> apis.usercenter.v1.UserDetail
	34, // 27: apis.usercenter.v1.UserService.DeleteUser:output_type -> google.protobuf.Empty
	3,  // 28: apis.usercenter.v1.UserService.GetUser:output_type -> apis.usercenter.v1.UserDetail
	10, // 29: apis.usercenter.v1.UserService.ListUsers:output_type -> apis.usercenter.v1.ListUsersResult
	12, // 30: apis.usercenter.v1.UserService.SearchUser:output_type -> apis.usercenter.v1.SearchUserResult
	15, // 31: apis.usercenter.v1.UserService.GetSSHKeysForUser:output_type -> apis.usercenter.v1.GetSSHKeyResponse
	22, // 32: apis.usercenter.v1.UserService.CreateSSHKey:output_type -> apis.usercenter.v1.CreateSSHKeyResponse
	24, // 33: apis.usercenter.v1.UserService.UpdateSSHKey:output_type -> apis.usercenter.v1.UpdateSSHKeyResponse
	26, // 34: apis.usercenter.v1.UserService.DeleteSSHKey:output_type -> apis.usercenter.v1.DeleteSSHKeyResponse
	28, // 35: apis.usercenter.v1.UserService.CheckSSHKey:output_type -> apis.usercenter.v1.CheckSSHKeyResponse
	34, // 36: apis.usercenter.v1.UserService.GenerateAccessToken:output_type -> google.protobuf.Empty
	19, // 37: apis.usercenter.v1.UserService.GetAccessToken:output_type -> apis.usercenter.v1.GetAccessTokenResponse
	34, // 38: apis.usercenter.v1.UserService.DeleteAccessToken:output_type -> google.protobuf.Empty
	31, // 39: apis.usercenter.v1.UserService.Whoami:output_type -> apis.usercenter.v1.WhoamiResponse
	2,  // 40: apis.usercenter.v1.UserService.GetHadoopUsers:output_type -> apis.usercenter.v1.GetHadoopUsersResponse
	26, // [26:41] is the sub-list for method output_type
	11, // [11:26] is the sub-list for method input_type
	11, // [11:11] is the sub-list for extension type_name
	11, // [11:11] is the sub-list for extension extendee
	0,  // [0:11] is the sub-list for field type_name
}

func init() { file_usercenter_v1_usercenter_proto_init() }
func file_usercenter_v1_usercenter_proto_init() {
	if File_usercenter_v1_usercenter_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_usercenter_v1_usercenter_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*GetHadoopUsersRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_v1_usercenter_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*GetHadoopUsersResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_v1_usercenter_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*UserDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_v1_usercenter_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*UserAttributes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_v1_usercenter_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*RobotAttributes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_v1_usercenter_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*CreateOrUpdateUserRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_v1_usercenter_proto_msgTypes[6].Exporter = func(v any, i int) any {
			switch v := v.(*DeleteUserRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_v1_usercenter_proto_msgTypes[7].Exporter = func(v any, i int) any {
			switch v := v.(*GetUserDetailRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_v1_usercenter_proto_msgTypes[8].Exporter = func(v any, i int) any {
			switch v := v.(*ListOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_v1_usercenter_proto_msgTypes[9].Exporter = func(v any, i int) any {
			switch v := v.(*ListUsersResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_v1_usercenter_proto_msgTypes[10].Exporter = func(v any, i int) any {
			switch v := v.(*SearchUserRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_v1_usercenter_proto_msgTypes[11].Exporter = func(v any, i int) any {
			switch v := v.(*SearchUserResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_v1_usercenter_proto_msgTypes[12].Exporter = func(v any, i int) any {
			switch v := v.(*UserName); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_v1_usercenter_proto_msgTypes[13].Exporter = func(v any, i int) any {
			switch v := v.(*GetSSHKeysForUserRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_v1_usercenter_proto_msgTypes[14].Exporter = func(v any, i int) any {
			switch v := v.(*GetSSHKeyResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_v1_usercenter_proto_msgTypes[15].Exporter = func(v any, i int) any {
			switch v := v.(*AccessToken); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_v1_usercenter_proto_msgTypes[16].Exporter = func(v any, i int) any {
			switch v := v.(*GenerateAccessTokenRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_v1_usercenter_proto_msgTypes[17].Exporter = func(v any, i int) any {
			switch v := v.(*GetAccessTokenRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_v1_usercenter_proto_msgTypes[18].Exporter = func(v any, i int) any {
			switch v := v.(*GetAccessTokenResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_v1_usercenter_proto_msgTypes[19].Exporter = func(v any, i int) any {
			switch v := v.(*DeleteAccessTokenRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_v1_usercenter_proto_msgTypes[20].Exporter = func(v any, i int) any {
			switch v := v.(*CreateSSHKeyRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_v1_usercenter_proto_msgTypes[21].Exporter = func(v any, i int) any {
			switch v := v.(*CreateSSHKeyResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_v1_usercenter_proto_msgTypes[22].Exporter = func(v any, i int) any {
			switch v := v.(*UpdateSSHKeyRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_v1_usercenter_proto_msgTypes[23].Exporter = func(v any, i int) any {
			switch v := v.(*UpdateSSHKeyResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_v1_usercenter_proto_msgTypes[24].Exporter = func(v any, i int) any {
			switch v := v.(*DeleteSSHKeyRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_v1_usercenter_proto_msgTypes[25].Exporter = func(v any, i int) any {
			switch v := v.(*DeleteSSHKeyResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_v1_usercenter_proto_msgTypes[26].Exporter = func(v any, i int) any {
			switch v := v.(*CheckSSHKeyRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_v1_usercenter_proto_msgTypes[27].Exporter = func(v any, i int) any {
			switch v := v.(*CheckSSHKeyResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_v1_usercenter_proto_msgTypes[28].Exporter = func(v any, i int) any {
			switch v := v.(*SSHKey); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_v1_usercenter_proto_msgTypes[29].Exporter = func(v any, i int) any {
			switch v := v.(*WhoamiRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_v1_usercenter_proto_msgTypes[30].Exporter = func(v any, i int) any {
			switch v := v.(*WhoamiResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_usercenter_v1_usercenter_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   31,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_usercenter_v1_usercenter_proto_goTypes,
		DependencyIndexes: file_usercenter_v1_usercenter_proto_depIdxs,
		EnumInfos:         file_usercenter_v1_usercenter_proto_enumTypes,
		MessageInfos:      file_usercenter_v1_usercenter_proto_msgTypes,
	}.Build()
	File_usercenter_v1_usercenter_proto = out.File
	file_usercenter_v1_usercenter_proto_rawDesc = nil
	file_usercenter_v1_usercenter_proto_goTypes = nil
	file_usercenter_v1_usercenter_proto_depIdxs = nil
}
