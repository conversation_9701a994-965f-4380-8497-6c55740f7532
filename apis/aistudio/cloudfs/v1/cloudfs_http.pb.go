// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.3
// - protoc             v5.29.3
// source: aistudio/cloudfs/v1/cloudfs.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationCloudFSServiceCreateVolume = "/apis.aistudio.cloudFs.v1.CloudFSService/CreateVolume"
const OperationCloudFSServiceDeleteVolume = "/apis.aistudio.cloudFs.v1.CloudFSService/DeleteVolume"
const OperationCloudFSServiceGetVolume = "/apis.aistudio.cloudFs.v1.CloudFSService/GetVolume"
const OperationCloudFSServiceGetVolumeMeta = "/apis.aistudio.cloudFs.v1.CloudFSService/GetVolumeMeta"
const OperationCloudFSServiceListVolumes = "/apis.aistudio.cloudFs.v1.CloudFSService/ListVolumes"
const OperationCloudFSServiceUpdateVolume = "/apis.aistudio.cloudFs.v1.CloudFSService/UpdateVolume"

type CloudFSServiceHTTPServer interface {
	CreateVolume(context.Context, *CreateVolumeRequest) (*Volume, error)
	DeleteVolume(context.Context, *DeleteVolumeRequest) (*emptypb.Empty, error)
	GetVolume(context.Context, *GetVolumeRequest) (*Volume, error)
	GetVolumeMeta(context.Context, *GetVolumeMetaRequest) (*GetVolumeMetaResponse, error)
	ListVolumes(context.Context, *ListVolumeOptions) (*ListVolumeResult, error)
	UpdateVolume(context.Context, *UpdateVolumeRequest) (*Volume, error)
}

func RegisterCloudFSServiceHTTPServer(s *http.Server, srv CloudFSServiceHTTPServer) {
	r := s.Route("/")
	r.POST("/apis/v1/workspace/{workspaceName}/cloudfs/volume", _CloudFSService_CreateVolume0_HTTP_Handler(srv))
	r.PUT("/apis/v1/workspace/{workspaceName}/cloudfs/volume/{name}", _CloudFSService_UpdateVolume0_HTTP_Handler(srv))
	r.GET("/apis/v1/workspace/{workspaceName}/cloudfs/volume/{name}", _CloudFSService_GetVolume0_HTTP_Handler(srv))
	r.GET("/apis/v1/workspace/{workspaceName}/cloudfs/volume", _CloudFSService_ListVolumes0_HTTP_Handler(srv))
	r.DELETE("/apis/v1/workspace/{workspaceName}/cloudfs/volume/{name}", _CloudFSService_DeleteVolume0_HTTP_Handler(srv))
	r.GET("/apis/v1/workspace/{workspaceName}/cloudfs/volume/{volumeName}/meta", _CloudFSService_GetVolumeMeta0_HTTP_Handler(srv))
}

func _CloudFSService_CreateVolume0_HTTP_Handler(srv CloudFSServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateVolumeRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCloudFSServiceCreateVolume)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateVolume(ctx, req.(*CreateVolumeRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Volume)
		return ctx.Result(200, reply)
	}
}

func _CloudFSService_UpdateVolume0_HTTP_Handler(srv CloudFSServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UpdateVolumeRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCloudFSServiceUpdateVolume)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateVolume(ctx, req.(*UpdateVolumeRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Volume)
		return ctx.Result(200, reply)
	}
}

func _CloudFSService_GetVolume0_HTTP_Handler(srv CloudFSServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetVolumeRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCloudFSServiceGetVolume)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetVolume(ctx, req.(*GetVolumeRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Volume)
		return ctx.Result(200, reply)
	}
}

func _CloudFSService_ListVolumes0_HTTP_Handler(srv CloudFSServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListVolumeOptions
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCloudFSServiceListVolumes)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListVolumes(ctx, req.(*ListVolumeOptions))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListVolumeResult)
		return ctx.Result(200, reply)
	}
}

func _CloudFSService_DeleteVolume0_HTTP_Handler(srv CloudFSServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteVolumeRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCloudFSServiceDeleteVolume)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteVolume(ctx, req.(*DeleteVolumeRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _CloudFSService_GetVolumeMeta0_HTTP_Handler(srv CloudFSServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetVolumeMetaRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCloudFSServiceGetVolumeMeta)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetVolumeMeta(ctx, req.(*GetVolumeMetaRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetVolumeMetaResponse)
		return ctx.Result(200, reply)
	}
}

type CloudFSServiceHTTPClient interface {
	CreateVolume(ctx context.Context, req *CreateVolumeRequest, opts ...http.CallOption) (rsp *Volume, err error)
	DeleteVolume(ctx context.Context, req *DeleteVolumeRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	GetVolume(ctx context.Context, req *GetVolumeRequest, opts ...http.CallOption) (rsp *Volume, err error)
	GetVolumeMeta(ctx context.Context, req *GetVolumeMetaRequest, opts ...http.CallOption) (rsp *GetVolumeMetaResponse, err error)
	ListVolumes(ctx context.Context, req *ListVolumeOptions, opts ...http.CallOption) (rsp *ListVolumeResult, err error)
	UpdateVolume(ctx context.Context, req *UpdateVolumeRequest, opts ...http.CallOption) (rsp *Volume, err error)
}

type CloudFSServiceHTTPClientImpl struct {
	cc *http.Client
}

func NewCloudFSServiceHTTPClient(client *http.Client) CloudFSServiceHTTPClient {
	return &CloudFSServiceHTTPClientImpl{client}
}

func (c *CloudFSServiceHTTPClientImpl) CreateVolume(ctx context.Context, in *CreateVolumeRequest, opts ...http.CallOption) (*Volume, error) {
	var out Volume
	pattern := "/apis/v1/workspace/{workspaceName}/cloudfs/volume"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCloudFSServiceCreateVolume))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *CloudFSServiceHTTPClientImpl) DeleteVolume(ctx context.Context, in *DeleteVolumeRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/apis/v1/workspace/{workspaceName}/cloudfs/volume/{name}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationCloudFSServiceDeleteVolume))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *CloudFSServiceHTTPClientImpl) GetVolume(ctx context.Context, in *GetVolumeRequest, opts ...http.CallOption) (*Volume, error) {
	var out Volume
	pattern := "/apis/v1/workspace/{workspaceName}/cloudfs/volume/{name}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationCloudFSServiceGetVolume))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *CloudFSServiceHTTPClientImpl) GetVolumeMeta(ctx context.Context, in *GetVolumeMetaRequest, opts ...http.CallOption) (*GetVolumeMetaResponse, error) {
	var out GetVolumeMetaResponse
	pattern := "/apis/v1/workspace/{workspaceName}/cloudfs/volume/{volumeName}/meta"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationCloudFSServiceGetVolumeMeta))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *CloudFSServiceHTTPClientImpl) ListVolumes(ctx context.Context, in *ListVolumeOptions, opts ...http.CallOption) (*ListVolumeResult, error) {
	var out ListVolumeResult
	pattern := "/apis/v1/workspace/{workspaceName}/cloudfs/volume"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationCloudFSServiceListVolumes))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *CloudFSServiceHTTPClientImpl) UpdateVolume(ctx context.Context, in *UpdateVolumeRequest, opts ...http.CallOption) (*Volume, error) {
	var out Volume
	pattern := "/apis/v1/workspace/{workspaceName}/cloudfs/volume/{name}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCloudFSServiceUpdateVolume))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
