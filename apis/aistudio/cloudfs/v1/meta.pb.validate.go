// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: aistudio/cloudfs/v1/meta.proto

package v1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on MetaCluster with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *MetaCluster) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MetaCluster with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in MetaClusterMultiError, or
// nil if none found.
func (m *MetaCluster) ValidateAll() error {
	return m.validate(true)
}

func (m *MetaCluster) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return MetaClusterMultiError(errors)
	}

	return nil
}

// MetaClusterMultiError is an error wrapping multiple validation errors
// returned by MetaCluster.ValidateAll() if the designated constraints aren't met.
type MetaClusterMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MetaClusterMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MetaClusterMultiError) AllErrors() []error { return m }

// MetaClusterValidationError is the validation error returned by
// MetaCluster.Validate if the designated constraints aren't met.
type MetaClusterValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MetaClusterValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MetaClusterValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MetaClusterValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MetaClusterValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MetaClusterValidationError) ErrorName() string { return "MetaClusterValidationError" }

// Error satisfies the builtin error interface
func (e MetaClusterValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMetaCluster.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MetaClusterValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MetaClusterValidationError{}
