// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v5.29.3
// source: aistudio/cloudfs/v1/meta.proto

package v1

import (
	_ "git.lianjia.com/cloudnative/kic/kic-platform/apis/common"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	_ "google.golang.org/protobuf/types/known/durationpb"
	_ "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type MetaCluster struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *MetaCluster) Reset() {
	*x = MetaCluster{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_cloudfs_v1_meta_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MetaCluster) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MetaCluster) ProtoMessage() {}

func (x *MetaCluster) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_cloudfs_v1_meta_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MetaCluster.ProtoReflect.Descriptor instead.
func (*MetaCluster) Descriptor() ([]byte, []int) {
	return file_aistudio_cloudfs_v1_meta_proto_rawDescGZIP(), []int{0}
}

var File_aistudio_cloudfs_v1_meta_proto protoreflect.FileDescriptor

var file_aistudio_cloudfs_v1_meta_proto_rawDesc = []byte{
	0x0a, 0x1e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64,
	0x66, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x65, 0x74, 0x61, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x18, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e,
	0x63, 0x6c, 0x6f, 0x75, 0x64, 0x66, 0x73, 0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x13, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65,
	0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x0d, 0x0a, 0x0b, 0x4d, 0x65,
	0x74, 0x61, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x32, 0x0e, 0x0a, 0x0c, 0x4d, 0x65, 0x74,
	0x61, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x42, 0x4a, 0x5a, 0x48, 0x67, 0x69, 0x74,
	0x2e, 0x6c, 0x69, 0x61, 0x6e, 0x6a, 0x69, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x63, 0x6c, 0x6f,
	0x75, 0x64, 0x6e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x2f, 0x6b, 0x69, 0x63, 0x2f, 0x6b, 0x69, 0x63,
	0x2d, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x61,
	0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x66, 0x73, 0x2f,
	0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_aistudio_cloudfs_v1_meta_proto_rawDescOnce sync.Once
	file_aistudio_cloudfs_v1_meta_proto_rawDescData = file_aistudio_cloudfs_v1_meta_proto_rawDesc
)

func file_aistudio_cloudfs_v1_meta_proto_rawDescGZIP() []byte {
	file_aistudio_cloudfs_v1_meta_proto_rawDescOnce.Do(func() {
		file_aistudio_cloudfs_v1_meta_proto_rawDescData = protoimpl.X.CompressGZIP(file_aistudio_cloudfs_v1_meta_proto_rawDescData)
	})
	return file_aistudio_cloudfs_v1_meta_proto_rawDescData
}

var file_aistudio_cloudfs_v1_meta_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_aistudio_cloudfs_v1_meta_proto_goTypes = []any{
	(*MetaCluster)(nil), // 0: apis.aistudio.cloudfs.v1.MetaCluster
}
var file_aistudio_cloudfs_v1_meta_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_aistudio_cloudfs_v1_meta_proto_init() }
func file_aistudio_cloudfs_v1_meta_proto_init() {
	if File_aistudio_cloudfs_v1_meta_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_aistudio_cloudfs_v1_meta_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*MetaCluster); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_aistudio_cloudfs_v1_meta_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_aistudio_cloudfs_v1_meta_proto_goTypes,
		DependencyIndexes: file_aistudio_cloudfs_v1_meta_proto_depIdxs,
		MessageInfos:      file_aistudio_cloudfs_v1_meta_proto_msgTypes,
	}.Build()
	File_aistudio_cloudfs_v1_meta_proto = out.File
	file_aistudio_cloudfs_v1_meta_proto_rawDesc = nil
	file_aistudio_cloudfs_v1_meta_proto_goTypes = nil
	file_aistudio_cloudfs_v1_meta_proto_depIdxs = nil
}
