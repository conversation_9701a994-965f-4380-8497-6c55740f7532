// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v5.29.3
// source: aistudio/cloudfs/v1/meta.proto

package v1

import (
	grpc "google.golang.org/grpc"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

// MetaInstanceClient is the client API for MetaInstance service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type MetaInstanceClient interface {
}

type metaInstanceClient struct {
	cc grpc.ClientConnInterface
}

func NewMetaInstanceClient(cc grpc.ClientConnInterface) MetaInstanceClient {
	return &metaInstanceClient{cc}
}

// MetaInstanceServer is the server API for MetaInstance service.
// All implementations must embed UnimplementedMetaInstanceServer
// for forward compatibility
type MetaInstanceServer interface {
	mustEmbedUnimplementedMetaInstanceServer()
}

// UnimplementedMetaInstanceServer must be embedded to have forward compatible implementations.
type UnimplementedMetaInstanceServer struct {
}

func (UnimplementedMetaInstanceServer) mustEmbedUnimplementedMetaInstanceServer() {}

// UnsafeMetaInstanceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to MetaInstanceServer will
// result in compilation errors.
type UnsafeMetaInstanceServer interface {
	mustEmbedUnimplementedMetaInstanceServer()
}

func RegisterMetaInstanceServer(s grpc.ServiceRegistrar, srv MetaInstanceServer) {
	s.RegisterService(&MetaInstance_ServiceDesc, srv)
}

// MetaInstance_ServiceDesc is the grpc.ServiceDesc for MetaInstance service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var MetaInstance_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "apis.aistudio.cloudfs.v1.MetaInstance",
	HandlerType: (*MetaInstanceServer)(nil),
	Methods:     []grpc.MethodDesc{},
	Streams:     []grpc.StreamDesc{},
	Metadata:    "aistudio/cloudfs/v1/meta.proto",
}
