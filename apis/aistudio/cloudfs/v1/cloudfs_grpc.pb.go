// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v5.29.3
// source: aistudio/cloudfs/v1/cloudfs.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	CloudFSService_CreateVolume_FullMethodName  = "/apis.aistudio.cloudFs.v1.CloudFSService/CreateVolume"
	CloudFSService_UpdateVolume_FullMethodName  = "/apis.aistudio.cloudFs.v1.CloudFSService/UpdateVolume"
	CloudFSService_GetVolume_FullMethodName     = "/apis.aistudio.cloudFs.v1.CloudFSService/GetVolume"
	CloudFSService_ListVolumes_FullMethodName   = "/apis.aistudio.cloudFs.v1.CloudFSService/ListVolumes"
	CloudFSService_DeleteVolume_FullMethodName  = "/apis.aistudio.cloudFs.v1.CloudFSService/DeleteVolume"
	CloudFSService_GetVolumeMeta_FullMethodName = "/apis.aistudio.cloudFs.v1.CloudFSService/GetVolumeMeta"
)

// CloudFSServiceClient is the client API for CloudFSService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// AIStudio层, 底层创建fluid的总dataset
type CloudFSServiceClient interface {
	CreateVolume(ctx context.Context, in *CreateVolumeRequest, opts ...grpc.CallOption) (*Volume, error)
	UpdateVolume(ctx context.Context, in *UpdateVolumeRequest, opts ...grpc.CallOption) (*Volume, error)
	GetVolume(ctx context.Context, in *GetVolumeRequest, opts ...grpc.CallOption) (*Volume, error)
	ListVolumes(ctx context.Context, in *ListVolumeOptions, opts ...grpc.CallOption) (*ListVolumeResult, error)
	DeleteVolume(ctx context.Context, in *DeleteVolumeRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	GetVolumeMeta(ctx context.Context, in *GetVolumeMetaRequest, opts ...grpc.CallOption) (*GetVolumeMetaResponse, error)
}

type cloudFSServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewCloudFSServiceClient(cc grpc.ClientConnInterface) CloudFSServiceClient {
	return &cloudFSServiceClient{cc}
}

func (c *cloudFSServiceClient) CreateVolume(ctx context.Context, in *CreateVolumeRequest, opts ...grpc.CallOption) (*Volume, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(Volume)
	err := c.cc.Invoke(ctx, CloudFSService_CreateVolume_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cloudFSServiceClient) UpdateVolume(ctx context.Context, in *UpdateVolumeRequest, opts ...grpc.CallOption) (*Volume, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(Volume)
	err := c.cc.Invoke(ctx, CloudFSService_UpdateVolume_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cloudFSServiceClient) GetVolume(ctx context.Context, in *GetVolumeRequest, opts ...grpc.CallOption) (*Volume, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(Volume)
	err := c.cc.Invoke(ctx, CloudFSService_GetVolume_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cloudFSServiceClient) ListVolumes(ctx context.Context, in *ListVolumeOptions, opts ...grpc.CallOption) (*ListVolumeResult, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListVolumeResult)
	err := c.cc.Invoke(ctx, CloudFSService_ListVolumes_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cloudFSServiceClient) DeleteVolume(ctx context.Context, in *DeleteVolumeRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, CloudFSService_DeleteVolume_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cloudFSServiceClient) GetVolumeMeta(ctx context.Context, in *GetVolumeMetaRequest, opts ...grpc.CallOption) (*GetVolumeMetaResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetVolumeMetaResponse)
	err := c.cc.Invoke(ctx, CloudFSService_GetVolumeMeta_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CloudFSServiceServer is the server API for CloudFSService service.
// All implementations must embed UnimplementedCloudFSServiceServer
// for forward compatibility
//
// AIStudio层, 底层创建fluid的总dataset
type CloudFSServiceServer interface {
	CreateVolume(context.Context, *CreateVolumeRequest) (*Volume, error)
	UpdateVolume(context.Context, *UpdateVolumeRequest) (*Volume, error)
	GetVolume(context.Context, *GetVolumeRequest) (*Volume, error)
	ListVolumes(context.Context, *ListVolumeOptions) (*ListVolumeResult, error)
	DeleteVolume(context.Context, *DeleteVolumeRequest) (*emptypb.Empty, error)
	GetVolumeMeta(context.Context, *GetVolumeMetaRequest) (*GetVolumeMetaResponse, error)
	mustEmbedUnimplementedCloudFSServiceServer()
}

// UnimplementedCloudFSServiceServer must be embedded to have forward compatible implementations.
type UnimplementedCloudFSServiceServer struct {
}

func (UnimplementedCloudFSServiceServer) CreateVolume(context.Context, *CreateVolumeRequest) (*Volume, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateVolume not implemented")
}
func (UnimplementedCloudFSServiceServer) UpdateVolume(context.Context, *UpdateVolumeRequest) (*Volume, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateVolume not implemented")
}
func (UnimplementedCloudFSServiceServer) GetVolume(context.Context, *GetVolumeRequest) (*Volume, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetVolume not implemented")
}
func (UnimplementedCloudFSServiceServer) ListVolumes(context.Context, *ListVolumeOptions) (*ListVolumeResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListVolumes not implemented")
}
func (UnimplementedCloudFSServiceServer) DeleteVolume(context.Context, *DeleteVolumeRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteVolume not implemented")
}
func (UnimplementedCloudFSServiceServer) GetVolumeMeta(context.Context, *GetVolumeMetaRequest) (*GetVolumeMetaResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetVolumeMeta not implemented")
}
func (UnimplementedCloudFSServiceServer) mustEmbedUnimplementedCloudFSServiceServer() {}

// UnsafeCloudFSServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CloudFSServiceServer will
// result in compilation errors.
type UnsafeCloudFSServiceServer interface {
	mustEmbedUnimplementedCloudFSServiceServer()
}

func RegisterCloudFSServiceServer(s grpc.ServiceRegistrar, srv CloudFSServiceServer) {
	s.RegisterService(&CloudFSService_ServiceDesc, srv)
}

func _CloudFSService_CreateVolume_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateVolumeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CloudFSServiceServer).CreateVolume(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CloudFSService_CreateVolume_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CloudFSServiceServer).CreateVolume(ctx, req.(*CreateVolumeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CloudFSService_UpdateVolume_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateVolumeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CloudFSServiceServer).UpdateVolume(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CloudFSService_UpdateVolume_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CloudFSServiceServer).UpdateVolume(ctx, req.(*UpdateVolumeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CloudFSService_GetVolume_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetVolumeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CloudFSServiceServer).GetVolume(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CloudFSService_GetVolume_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CloudFSServiceServer).GetVolume(ctx, req.(*GetVolumeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CloudFSService_ListVolumes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListVolumeOptions)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CloudFSServiceServer).ListVolumes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CloudFSService_ListVolumes_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CloudFSServiceServer).ListVolumes(ctx, req.(*ListVolumeOptions))
	}
	return interceptor(ctx, in, info, handler)
}

func _CloudFSService_DeleteVolume_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteVolumeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CloudFSServiceServer).DeleteVolume(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CloudFSService_DeleteVolume_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CloudFSServiceServer).DeleteVolume(ctx, req.(*DeleteVolumeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CloudFSService_GetVolumeMeta_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetVolumeMetaRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CloudFSServiceServer).GetVolumeMeta(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CloudFSService_GetVolumeMeta_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CloudFSServiceServer).GetVolumeMeta(ctx, req.(*GetVolumeMetaRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// CloudFSService_ServiceDesc is the grpc.ServiceDesc for CloudFSService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var CloudFSService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "apis.aistudio.cloudFs.v1.CloudFSService",
	HandlerType: (*CloudFSServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateVolume",
			Handler:    _CloudFSService_CreateVolume_Handler,
		},
		{
			MethodName: "UpdateVolume",
			Handler:    _CloudFSService_UpdateVolume_Handler,
		},
		{
			MethodName: "GetVolume",
			Handler:    _CloudFSService_GetVolume_Handler,
		},
		{
			MethodName: "ListVolumes",
			Handler:    _CloudFSService_ListVolumes_Handler,
		},
		{
			MethodName: "DeleteVolume",
			Handler:    _CloudFSService_DeleteVolume_Handler,
		},
		{
			MethodName: "GetVolumeMeta",
			Handler:    _CloudFSService_GetVolumeMeta_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "aistudio/cloudfs/v1/cloudfs.proto",
}
