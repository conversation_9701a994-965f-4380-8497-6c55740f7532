// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v5.29.3
// source: aistudio/cloudfs/v1/cloudfs.proto

package v1

import (
	common "git.lianjia.com/cloudnative/kic/kic-platform/apis/common"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	_ "google.golang.org/protobuf/types/known/durationpb"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetVolumeMetaRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	VolumeName    string `protobuf:"bytes,1,opt,name=volumeName,proto3" json:"volumeName,omitempty"`
	WorkspaceName string `protobuf:"bytes,2,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
}

func (x *GetVolumeMetaRequest) Reset() {
	*x = GetVolumeMetaRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_cloudfs_v1_cloudfs_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetVolumeMetaRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetVolumeMetaRequest) ProtoMessage() {}

func (x *GetVolumeMetaRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_cloudfs_v1_cloudfs_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetVolumeMetaRequest.ProtoReflect.Descriptor instead.
func (*GetVolumeMetaRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_cloudfs_v1_cloudfs_proto_rawDescGZIP(), []int{0}
}

func (x *GetVolumeMetaRequest) GetVolumeName() string {
	if x != nil {
		return x.VolumeName
	}
	return ""
}

func (x *GetVolumeMetaRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

type GetVolumeMetaResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FormatName string `protobuf:"bytes,1,opt,name=formatName,proto3" json:"formatName,omitempty"`
	MetaUrl    string `protobuf:"bytes,2,opt,name=metaUrl,proto3" json:"metaUrl,omitempty"`
	Storage    string `protobuf:"bytes,3,opt,name=storage,proto3" json:"storage,omitempty"`
	Bucket     string `protobuf:"bytes,4,opt,name=bucket,proto3" json:"bucket,omitempty"`
	AccessKey  string `protobuf:"bytes,5,opt,name=accessKey,proto3" json:"accessKey,omitempty"`
	SecretKey  string `protobuf:"bytes,6,opt,name=secretKey,proto3" json:"secretKey,omitempty"`
}

func (x *GetVolumeMetaResponse) Reset() {
	*x = GetVolumeMetaResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_cloudfs_v1_cloudfs_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetVolumeMetaResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetVolumeMetaResponse) ProtoMessage() {}

func (x *GetVolumeMetaResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_cloudfs_v1_cloudfs_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetVolumeMetaResponse.ProtoReflect.Descriptor instead.
func (*GetVolumeMetaResponse) Descriptor() ([]byte, []int) {
	return file_aistudio_cloudfs_v1_cloudfs_proto_rawDescGZIP(), []int{1}
}

func (x *GetVolumeMetaResponse) GetFormatName() string {
	if x != nil {
		return x.FormatName
	}
	return ""
}

func (x *GetVolumeMetaResponse) GetMetaUrl() string {
	if x != nil {
		return x.MetaUrl
	}
	return ""
}

func (x *GetVolumeMetaResponse) GetStorage() string {
	if x != nil {
		return x.Storage
	}
	return ""
}

func (x *GetVolumeMetaResponse) GetBucket() string {
	if x != nil {
		return x.Bucket
	}
	return ""
}

func (x *GetVolumeMetaResponse) GetAccessKey() string {
	if x != nil {
		return x.AccessKey
	}
	return ""
}

func (x *GetVolumeMetaResponse) GetSecretKey() string {
	if x != nil {
		return x.SecretKey
	}
	return ""
}

type Volume struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName string                 `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Description   string                 `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	Region        string                 `protobuf:"bytes,4,opt,name=region,proto3" json:"region,omitempty"`           // 区域
	Zone          string                 `protobuf:"bytes,5,opt,name=zone,proto3" json:"zone,omitempty"`               //分区
	DisplayName   string                 `protobuf:"bytes,6,opt,name=displayName,proto3" json:"displayName,omitempty"` // 显示名称
	Timestamp     *common.TimestampModel `protobuf:"bytes,7,opt,name=timestamp,proto3" json:"timestamp,omitempty"`     //时间
	Id            string                 `protobuf:"bytes,8,opt,name=id,proto3" json:"id,omitempty"`
	Creator       string                 `protobuf:"bytes,9,opt,name=creator,proto3" json:"creator,omitempty"`
	State         string                 `protobuf:"bytes,10,opt,name=state,proto3" json:"state,omitempty"`
	VolumeLimit   *VolumeLimit           `protobuf:"bytes,11,opt,name=volumeLimit,proto3" json:"volumeLimit,omitempty"` //卷限制
}

func (x *Volume) Reset() {
	*x = Volume{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_cloudfs_v1_cloudfs_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Volume) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Volume) ProtoMessage() {}

func (x *Volume) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_cloudfs_v1_cloudfs_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Volume.ProtoReflect.Descriptor instead.
func (*Volume) Descriptor() ([]byte, []int) {
	return file_aistudio_cloudfs_v1_cloudfs_proto_rawDescGZIP(), []int{2}
}

func (x *Volume) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *Volume) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Volume) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Volume) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *Volume) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *Volume) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *Volume) GetTimestamp() *common.TimestampModel {
	if x != nil {
		return x.Timestamp
	}
	return nil
}

func (x *Volume) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Volume) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *Volume) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

func (x *Volume) GetVolumeLimit() *VolumeLimit {
	if x != nil {
		return x.VolumeLimit
	}
	return nil
}

type VolumeLimit struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UploadLimit   uint64                        `protobuf:"varint,1,opt,name=uploadLimit,proto3" json:"uploadLimit,omitempty"`     //上传限制,单位为MiB
	DownloadLimit uint64                        `protobuf:"varint,2,opt,name=downloadLimit,proto3" json:"downloadLimit,omitempty"` //下载限制,单位为MiB
	TrashDays     uint32                        `protobuf:"varint,3,opt,name=trashDays,proto3" json:"trashDays,omitempty"`         // TrashDays 回收站保留天数
	QuotaLimit    *VolumeLimit_VolumeQuotaLimit `protobuf:"bytes,4,opt,name=quotaLimit,proto3" json:"quotaLimit,omitempty"`        // 配额限制
}

func (x *VolumeLimit) Reset() {
	*x = VolumeLimit{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_cloudfs_v1_cloudfs_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VolumeLimit) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VolumeLimit) ProtoMessage() {}

func (x *VolumeLimit) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_cloudfs_v1_cloudfs_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VolumeLimit.ProtoReflect.Descriptor instead.
func (*VolumeLimit) Descriptor() ([]byte, []int) {
	return file_aistudio_cloudfs_v1_cloudfs_proto_rawDescGZIP(), []int{3}
}

func (x *VolumeLimit) GetUploadLimit() uint64 {
	if x != nil {
		return x.UploadLimit
	}
	return 0
}

func (x *VolumeLimit) GetDownloadLimit() uint64 {
	if x != nil {
		return x.DownloadLimit
	}
	return 0
}

func (x *VolumeLimit) GetTrashDays() uint32 {
	if x != nil {
		return x.TrashDays
	}
	return 0
}

func (x *VolumeLimit) GetQuotaLimit() *VolumeLimit_VolumeQuotaLimit {
	if x != nil {
		return x.QuotaLimit
	}
	return nil
}

type CreateVolumeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName string       `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Name          string       `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Description   string       `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	Zone          string       `protobuf:"bytes,4,opt,name=zone,proto3" json:"zone,omitempty"`               // 区域
	Region        string       `protobuf:"bytes,5,opt,name=region,proto3" json:"region,omitempty"`           // 地域
	DisplayName   string       `protobuf:"bytes,6,opt,name=displayName,proto3" json:"displayName,omitempty"` // 显示名称
	Creator       string       `protobuf:"bytes,7,opt,name=creator,proto3" json:"creator,omitempty"`
	ReadOnly      bool         `protobuf:"varint,8,opt,name=readOnly,proto3" json:"readOnly,omitempty"`
	VolumeLimit   *VolumeLimit `protobuf:"bytes,9,opt,name=volumeLimit,proto3" json:"volumeLimit,omitempty"` //卷限制
}

func (x *CreateVolumeRequest) Reset() {
	*x = CreateVolumeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_cloudfs_v1_cloudfs_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateVolumeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateVolumeRequest) ProtoMessage() {}

func (x *CreateVolumeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_cloudfs_v1_cloudfs_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateVolumeRequest.ProtoReflect.Descriptor instead.
func (*CreateVolumeRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_cloudfs_v1_cloudfs_proto_rawDescGZIP(), []int{4}
}

func (x *CreateVolumeRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *CreateVolumeRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateVolumeRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CreateVolumeRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *CreateVolumeRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *CreateVolumeRequest) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *CreateVolumeRequest) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *CreateVolumeRequest) GetReadOnly() bool {
	if x != nil {
		return x.ReadOnly
	}
	return false
}

func (x *CreateVolumeRequest) GetVolumeLimit() *VolumeLimit {
	if x != nil {
		return x.VolumeLimit
	}
	return nil
}

type UpdateVolumeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName string       `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Name          string       `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Description   string       `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	DisplayName   string       `protobuf:"bytes,4,opt,name=displayName,proto3" json:"displayName,omitempty"`
	ReadOnly      bool         `protobuf:"varint,5,opt,name=readOnly,proto3" json:"readOnly,omitempty"`
	Region        string       `protobuf:"bytes,6,opt,name=region,proto3" json:"region,omitempty"`
	Zone          string       `protobuf:"bytes,7,opt,name=zone,proto3" json:"zone,omitempty"`
	VolumeLimit   *VolumeLimit `protobuf:"bytes,8,opt,name=volumeLimit,proto3" json:"volumeLimit,omitempty"` //卷限制
}

func (x *UpdateVolumeRequest) Reset() {
	*x = UpdateVolumeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_cloudfs_v1_cloudfs_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateVolumeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateVolumeRequest) ProtoMessage() {}

func (x *UpdateVolumeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_cloudfs_v1_cloudfs_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateVolumeRequest.ProtoReflect.Descriptor instead.
func (*UpdateVolumeRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_cloudfs_v1_cloudfs_proto_rawDescGZIP(), []int{5}
}

func (x *UpdateVolumeRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *UpdateVolumeRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UpdateVolumeRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *UpdateVolumeRequest) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *UpdateVolumeRequest) GetReadOnly() bool {
	if x != nil {
		return x.ReadOnly
	}
	return false
}

func (x *UpdateVolumeRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *UpdateVolumeRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *UpdateVolumeRequest) GetVolumeLimit() *VolumeLimit {
	if x != nil {
		return x.VolumeLimit
	}
	return nil
}

type GetVolumeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName string `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Name          string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *GetVolumeRequest) Reset() {
	*x = GetVolumeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_cloudfs_v1_cloudfs_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetVolumeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetVolumeRequest) ProtoMessage() {}

func (x *GetVolumeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_cloudfs_v1_cloudfs_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetVolumeRequest.ProtoReflect.Descriptor instead.
func (*GetVolumeRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_cloudfs_v1_cloudfs_proto_rawDescGZIP(), []int{6}
}

func (x *GetVolumeRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *GetVolumeRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type ListVolumeOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Region        string `protobuf:"bytes,1,opt,name=region,proto3" json:"region,omitempty"`
	Zone          string `protobuf:"bytes,2,opt,name=zone,proto3" json:"zone,omitempty"`
	WorkspaceName string `protobuf:"bytes,3,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
}

func (x *ListVolumeOptions) Reset() {
	*x = ListVolumeOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_cloudfs_v1_cloudfs_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListVolumeOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListVolumeOptions) ProtoMessage() {}

func (x *ListVolumeOptions) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_cloudfs_v1_cloudfs_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListVolumeOptions.ProtoReflect.Descriptor instead.
func (*ListVolumeOptions) Descriptor() ([]byte, []int) {
	return file_aistudio_cloudfs_v1_cloudfs_proto_rawDescGZIP(), []int{7}
}

func (x *ListVolumeOptions) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *ListVolumeOptions) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *ListVolumeOptions) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

type ListVolumeResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items    []*Volume `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	PageSize int32     `protobuf:"varint,2,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
	PageNo   int32     `protobuf:"varint,3,opt,name=pageNo,proto3" json:"pageNo,omitempty"`
	Total    int32     `protobuf:"varint,4,opt,name=total,proto3" json:"total,omitempty"`
}

func (x *ListVolumeResult) Reset() {
	*x = ListVolumeResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_cloudfs_v1_cloudfs_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListVolumeResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListVolumeResult) ProtoMessage() {}

func (x *ListVolumeResult) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_cloudfs_v1_cloudfs_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListVolumeResult.ProtoReflect.Descriptor instead.
func (*ListVolumeResult) Descriptor() ([]byte, []int) {
	return file_aistudio_cloudfs_v1_cloudfs_proto_rawDescGZIP(), []int{8}
}

func (x *ListVolumeResult) GetItems() []*Volume {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *ListVolumeResult) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListVolumeResult) GetPageNo() int32 {
	if x != nil {
		return x.PageNo
	}
	return 0
}

func (x *ListVolumeResult) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

type DeleteVolumeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name          string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	WorkspaceName string `protobuf:"bytes,2,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
}

func (x *DeleteVolumeRequest) Reset() {
	*x = DeleteVolumeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_cloudfs_v1_cloudfs_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteVolumeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteVolumeRequest) ProtoMessage() {}

func (x *DeleteVolumeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_cloudfs_v1_cloudfs_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteVolumeRequest.ProtoReflect.Descriptor instead.
func (*DeleteVolumeRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_cloudfs_v1_cloudfs_proto_rawDescGZIP(), []int{9}
}

func (x *DeleteVolumeRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DeleteVolumeRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

type VolumeLimit_VolumeQuotaLimit struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Capacity uint64 `protobuf:"varint,1,opt,name=capacity,proto3" json:"capacity,omitempty"` //容量,单位为GiB
	Inodes   uint64 `protobuf:"varint,2,opt,name=inodes,proto3" json:"inodes,omitempty"`     //文件数量, 默认为1亿
}

func (x *VolumeLimit_VolumeQuotaLimit) Reset() {
	*x = VolumeLimit_VolumeQuotaLimit{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_cloudfs_v1_cloudfs_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VolumeLimit_VolumeQuotaLimit) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VolumeLimit_VolumeQuotaLimit) ProtoMessage() {}

func (x *VolumeLimit_VolumeQuotaLimit) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_cloudfs_v1_cloudfs_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VolumeLimit_VolumeQuotaLimit.ProtoReflect.Descriptor instead.
func (*VolumeLimit_VolumeQuotaLimit) Descriptor() ([]byte, []int) {
	return file_aistudio_cloudfs_v1_cloudfs_proto_rawDescGZIP(), []int{3, 0}
}

func (x *VolumeLimit_VolumeQuotaLimit) GetCapacity() uint64 {
	if x != nil {
		return x.Capacity
	}
	return 0
}

func (x *VolumeLimit_VolumeQuotaLimit) GetInodes() uint64 {
	if x != nil {
		return x.Inodes
	}
	return 0
}

var File_aistudio_cloudfs_v1_cloudfs_proto protoreflect.FileDescriptor

var file_aistudio_cloudfs_v1_cloudfs_proto_rawDesc = []byte{
	0x0a, 0x21, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64,
	0x66, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x66, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x18, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64,
	0x69, 0x6f, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x46, 0x73, 0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x64, 0x75, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x13, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x5c, 0x0a,
	0x14, 0x47, 0x65, 0x74, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x76, 0x6f, 0x6c, 0x75, 0x6d,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f,
	0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xbf, 0x01, 0x0a, 0x15,
	0x47, 0x65, 0x74, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x4e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x66, 0x6f, 0x72, 0x6d, 0x61,
	0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x74, 0x61, 0x55, 0x72, 0x6c,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x74, 0x61, 0x55, 0x72, 0x6c, 0x12,
	0x18, 0x0a, 0x07, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x62, 0x75, 0x63,
	0x6b, 0x65, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x62, 0x75, 0x63, 0x6b, 0x65,
	0x74, 0x12, 0x1c, 0x0a, 0x09, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x4b, 0x65, 0x79, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x4b, 0x65, 0x79, 0x12,
	0x1c, 0x0a, 0x09, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x4b, 0x65, 0x79, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x4b, 0x65, 0x79, 0x22, 0xf6, 0x02,
	0x0a, 0x06, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04,
	0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65,
	0x12, 0x20, 0x0a, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x39, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x18, 0x0a,
	0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x47, 0x0a,
	0x0b, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64,
	0x69, 0x6f, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x46, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x6f,
	0x6c, 0x75, 0x6d, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x52, 0x0b, 0x76, 0x6f, 0x6c, 0x75, 0x6d,
	0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x22, 0x93, 0x02, 0x0a, 0x0b, 0x56, 0x6f, 0x6c, 0x75, 0x6d,
	0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64,
	0x4c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0b, 0x75, 0x70, 0x6c,
	0x6f, 0x61, 0x64, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x64, 0x6f, 0x77, 0x6e,
	0x6c, 0x6f, 0x61, 0x64, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x0d, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x1c,
	0x0a, 0x09, 0x74, 0x72, 0x61, 0x73, 0x68, 0x44, 0x61, 0x79, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x09, 0x74, 0x72, 0x61, 0x73, 0x68, 0x44, 0x61, 0x79, 0x73, 0x12, 0x56, 0x0a, 0x0a,
	0x71, 0x75, 0x6f, 0x74, 0x61, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x36, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f,
	0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x46, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x6f, 0x6c, 0x75,
	0x6d, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x2e, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x51, 0x75,
	0x6f, 0x74, 0x61, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x52, 0x0a, 0x71, 0x75, 0x6f, 0x74, 0x61, 0x4c,
	0x69, 0x6d, 0x69, 0x74, 0x1a, 0x46, 0x0a, 0x10, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x51, 0x75,
	0x6f, 0x74, 0x61, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x61, 0x70, 0x61,
	0x63, 0x69, 0x74, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x63, 0x61, 0x70, 0x61,
	0x63, 0x69, 0x74, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x69, 0x6e, 0x6f, 0x64, 0x65, 0x73, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x69, 0x6e, 0x6f, 0x64, 0x65, 0x73, 0x22, 0xbe, 0x02, 0x0a,
	0x13, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72,
	0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20,
	0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x20, 0x0a, 0x0b,
	0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18,
	0x0a, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x61, 0x64,
	0x4f, 0x6e, 0x6c, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x72, 0x65, 0x61, 0x64,
	0x4f, 0x6e, 0x6c, 0x79, 0x12, 0x47, 0x0a, 0x0b, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x4c, 0x69,
	0x6d, 0x69, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x73,
	0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x46,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74,
	0x52, 0x0b, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x22, 0xa4, 0x02,
	0x0a, 0x13, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f,
	0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x61, 0x64, 0x4f, 0x6e, 0x6c, 0x79, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x72, 0x65, 0x61, 0x64, 0x4f, 0x6e, 0x6c, 0x79, 0x12,
	0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x47, 0x0a, 0x0b, 0x76,
	0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f,
	0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x46, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x6f, 0x6c, 0x75,
	0x6d, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x52, 0x0b, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x4c,
	0x69, 0x6d, 0x69, 0x74, 0x22, 0x4c, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x56, 0x6f, 0x6c, 0x75, 0x6d,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x22, 0x65, 0x0a, 0x11, 0x4c, 0x69, 0x73, 0x74, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65,
	0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f,
	0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12,
	0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x7a,
	0x6f, 0x6e, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x94, 0x01, 0x0a, 0x10, 0x4c, 0x69,
	0x73, 0x74, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x36,
	0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e,
	0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x63, 0x6c,
	0x6f, 0x75, 0x64, 0x46, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x52,
	0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69,
	0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69,
	0x7a, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x61, 0x67, 0x65, 0x4e, 0x6f, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x06, 0x70, 0x61, 0x67, 0x65, 0x4e, 0x6f, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x22, 0x4f, 0x0a, 0x13, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x77,
	0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x32, 0xf3, 0x07, 0x0a, 0x0e, 0x43, 0x6c, 0x6f, 0x75, 0x64, 0x46, 0x53, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x12, 0x9d, 0x01, 0x0a, 0x0c, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x56,
	0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x12, 0x2d, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73,
	0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x46, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74,
	0x75, 0x64, 0x69, 0x6f, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x46, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x22, 0x3c, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x36, 0x3a, 0x01,
	0x2a, 0x22, 0x31, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x6f, 0x72, 0x6b,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x2f, 0x7b, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x66, 0x73, 0x2f, 0x76, 0x6f,
	0x6c, 0x75, 0x6d, 0x65, 0x12, 0xa4, 0x01, 0x0a, 0x0c, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x56,
	0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x12, 0x2d, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73,
	0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x46, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74,
	0x75, 0x64, 0x69, 0x6f, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x46, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x22, 0x43, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x3d, 0x3a, 0x01,
	0x2a, 0x1a, 0x38, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x6f, 0x72, 0x6b,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x2f, 0x7b, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x66, 0x73, 0x2f, 0x76, 0x6f,
	0x6c, 0x75, 0x6d, 0x65, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x12, 0x9b, 0x01, 0x0a, 0x09,
	0x47, 0x65, 0x74, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x12, 0x2a, 0x2e, 0x61, 0x70, 0x69, 0x73,
	0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x46,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73,
	0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x46, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x22, 0x40, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x3a, 0x12,
	0x38, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x2f, 0x7b, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x7d, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x66, 0x73, 0x2f, 0x76, 0x6f, 0x6c, 0x75,
	0x6d, 0x65, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x12, 0xa1, 0x01, 0x0a, 0x0b, 0x4c, 0x69,
	0x73, 0x74, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x73, 0x12, 0x2b, 0x2e, 0x61, 0x70, 0x69, 0x73,
	0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x46,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x4f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x1a, 0x2a, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69,
	0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x46, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x22, 0x39, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x33, 0x12, 0x31, 0x2f, 0x61, 0x70, 0x69,
	0x73, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2f, 0x7b,
	0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x63,
	0x6c, 0x6f, 0x75, 0x64, 0x66, 0x73, 0x2f, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x12, 0x97, 0x01,
	0x0a, 0x0c, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x12, 0x2d,
	0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x63,
	0x6c, 0x6f, 0x75, 0x64, 0x46, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x40, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x3a, 0x2a, 0x38, 0x2f,
	0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x2f, 0x7b, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x7d, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x66, 0x73, 0x2f, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65,
	0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x12, 0xbd, 0x01, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x56,
	0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x73,
	0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x46,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x4d, 0x65,
	0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2f, 0x2e, 0x61, 0x70, 0x69, 0x73,
	0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x46,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x4d, 0x65,
	0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x4b, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x45, 0x12, 0x43, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x6f, 0x72,
	0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2f, 0x7b, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x66, 0x73, 0x2f, 0x76,
	0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x2f, 0x7b, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x7d, 0x2f, 0x6d, 0x65, 0x74, 0x61, 0x42, 0x4a, 0x5a, 0x48, 0x67, 0x69, 0x74, 0x2e, 0x6c,
	0x69, 0x61, 0x6e, 0x6a, 0x69, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64,
	0x6e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x2f, 0x6b, 0x69, 0x63, 0x2f, 0x6b, 0x69, 0x63, 0x2d, 0x70,
	0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x61, 0x69, 0x73,
	0x74, 0x75, 0x64, 0x69, 0x6f, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x66, 0x73, 0x2f, 0x76, 0x31,
	0x3b, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_aistudio_cloudfs_v1_cloudfs_proto_rawDescOnce sync.Once
	file_aistudio_cloudfs_v1_cloudfs_proto_rawDescData = file_aistudio_cloudfs_v1_cloudfs_proto_rawDesc
)

func file_aistudio_cloudfs_v1_cloudfs_proto_rawDescGZIP() []byte {
	file_aistudio_cloudfs_v1_cloudfs_proto_rawDescOnce.Do(func() {
		file_aistudio_cloudfs_v1_cloudfs_proto_rawDescData = protoimpl.X.CompressGZIP(file_aistudio_cloudfs_v1_cloudfs_proto_rawDescData)
	})
	return file_aistudio_cloudfs_v1_cloudfs_proto_rawDescData
}

var file_aistudio_cloudfs_v1_cloudfs_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_aistudio_cloudfs_v1_cloudfs_proto_goTypes = []any{
	(*GetVolumeMetaRequest)(nil),         // 0: apis.aistudio.cloudFs.v1.GetVolumeMetaRequest
	(*GetVolumeMetaResponse)(nil),        // 1: apis.aistudio.cloudFs.v1.GetVolumeMetaResponse
	(*Volume)(nil),                       // 2: apis.aistudio.cloudFs.v1.Volume
	(*VolumeLimit)(nil),                  // 3: apis.aistudio.cloudFs.v1.VolumeLimit
	(*CreateVolumeRequest)(nil),          // 4: apis.aistudio.cloudFs.v1.CreateVolumeRequest
	(*UpdateVolumeRequest)(nil),          // 5: apis.aistudio.cloudFs.v1.UpdateVolumeRequest
	(*GetVolumeRequest)(nil),             // 6: apis.aistudio.cloudFs.v1.GetVolumeRequest
	(*ListVolumeOptions)(nil),            // 7: apis.aistudio.cloudFs.v1.ListVolumeOptions
	(*ListVolumeResult)(nil),             // 8: apis.aistudio.cloudFs.v1.ListVolumeResult
	(*DeleteVolumeRequest)(nil),          // 9: apis.aistudio.cloudFs.v1.DeleteVolumeRequest
	(*VolumeLimit_VolumeQuotaLimit)(nil), // 10: apis.aistudio.cloudFs.v1.VolumeLimit.VolumeQuotaLimit
	(*common.TimestampModel)(nil),        // 11: apis.common.TimestampModel
	(*emptypb.Empty)(nil),                // 12: google.protobuf.Empty
}
var file_aistudio_cloudfs_v1_cloudfs_proto_depIdxs = []int32{
	11, // 0: apis.aistudio.cloudFs.v1.Volume.timestamp:type_name -> apis.common.TimestampModel
	3,  // 1: apis.aistudio.cloudFs.v1.Volume.volumeLimit:type_name -> apis.aistudio.cloudFs.v1.VolumeLimit
	10, // 2: apis.aistudio.cloudFs.v1.VolumeLimit.quotaLimit:type_name -> apis.aistudio.cloudFs.v1.VolumeLimit.VolumeQuotaLimit
	3,  // 3: apis.aistudio.cloudFs.v1.CreateVolumeRequest.volumeLimit:type_name -> apis.aistudio.cloudFs.v1.VolumeLimit
	3,  // 4: apis.aistudio.cloudFs.v1.UpdateVolumeRequest.volumeLimit:type_name -> apis.aistudio.cloudFs.v1.VolumeLimit
	2,  // 5: apis.aistudio.cloudFs.v1.ListVolumeResult.items:type_name -> apis.aistudio.cloudFs.v1.Volume
	4,  // 6: apis.aistudio.cloudFs.v1.CloudFSService.CreateVolume:input_type -> apis.aistudio.cloudFs.v1.CreateVolumeRequest
	5,  // 7: apis.aistudio.cloudFs.v1.CloudFSService.UpdateVolume:input_type -> apis.aistudio.cloudFs.v1.UpdateVolumeRequest
	6,  // 8: apis.aistudio.cloudFs.v1.CloudFSService.GetVolume:input_type -> apis.aistudio.cloudFs.v1.GetVolumeRequest
	7,  // 9: apis.aistudio.cloudFs.v1.CloudFSService.ListVolumes:input_type -> apis.aistudio.cloudFs.v1.ListVolumeOptions
	9,  // 10: apis.aistudio.cloudFs.v1.CloudFSService.DeleteVolume:input_type -> apis.aistudio.cloudFs.v1.DeleteVolumeRequest
	0,  // 11: apis.aistudio.cloudFs.v1.CloudFSService.GetVolumeMeta:input_type -> apis.aistudio.cloudFs.v1.GetVolumeMetaRequest
	2,  // 12: apis.aistudio.cloudFs.v1.CloudFSService.CreateVolume:output_type -> apis.aistudio.cloudFs.v1.Volume
	2,  // 13: apis.aistudio.cloudFs.v1.CloudFSService.UpdateVolume:output_type -> apis.aistudio.cloudFs.v1.Volume
	2,  // 14: apis.aistudio.cloudFs.v1.CloudFSService.GetVolume:output_type -> apis.aistudio.cloudFs.v1.Volume
	8,  // 15: apis.aistudio.cloudFs.v1.CloudFSService.ListVolumes:output_type -> apis.aistudio.cloudFs.v1.ListVolumeResult
	12, // 16: apis.aistudio.cloudFs.v1.CloudFSService.DeleteVolume:output_type -> google.protobuf.Empty
	1,  // 17: apis.aistudio.cloudFs.v1.CloudFSService.GetVolumeMeta:output_type -> apis.aistudio.cloudFs.v1.GetVolumeMetaResponse
	12, // [12:18] is the sub-list for method output_type
	6,  // [6:12] is the sub-list for method input_type
	6,  // [6:6] is the sub-list for extension type_name
	6,  // [6:6] is the sub-list for extension extendee
	0,  // [0:6] is the sub-list for field type_name
}

func init() { file_aistudio_cloudfs_v1_cloudfs_proto_init() }
func file_aistudio_cloudfs_v1_cloudfs_proto_init() {
	if File_aistudio_cloudfs_v1_cloudfs_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_aistudio_cloudfs_v1_cloudfs_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*GetVolumeMetaRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_cloudfs_v1_cloudfs_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*GetVolumeMetaResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_cloudfs_v1_cloudfs_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*Volume); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_cloudfs_v1_cloudfs_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*VolumeLimit); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_cloudfs_v1_cloudfs_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*CreateVolumeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_cloudfs_v1_cloudfs_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*UpdateVolumeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_cloudfs_v1_cloudfs_proto_msgTypes[6].Exporter = func(v any, i int) any {
			switch v := v.(*GetVolumeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_cloudfs_v1_cloudfs_proto_msgTypes[7].Exporter = func(v any, i int) any {
			switch v := v.(*ListVolumeOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_cloudfs_v1_cloudfs_proto_msgTypes[8].Exporter = func(v any, i int) any {
			switch v := v.(*ListVolumeResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_cloudfs_v1_cloudfs_proto_msgTypes[9].Exporter = func(v any, i int) any {
			switch v := v.(*DeleteVolumeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_cloudfs_v1_cloudfs_proto_msgTypes[10].Exporter = func(v any, i int) any {
			switch v := v.(*VolumeLimit_VolumeQuotaLimit); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_aistudio_cloudfs_v1_cloudfs_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_aistudio_cloudfs_v1_cloudfs_proto_goTypes,
		DependencyIndexes: file_aistudio_cloudfs_v1_cloudfs_proto_depIdxs,
		MessageInfos:      file_aistudio_cloudfs_v1_cloudfs_proto_msgTypes,
	}.Build()
	File_aistudio_cloudfs_v1_cloudfs_proto = out.File
	file_aistudio_cloudfs_v1_cloudfs_proto_rawDesc = nil
	file_aistudio_cloudfs_v1_cloudfs_proto_goTypes = nil
	file_aistudio_cloudfs_v1_cloudfs_proto_depIdxs = nil
}
