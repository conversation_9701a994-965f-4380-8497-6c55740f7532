// Code generated by protoc-gen-deepcopy. DO NOT EDIT.
package v1

import (
	proto "google.golang.org/protobuf/proto"
)

// DeepCopyInto supports using LoginRequest within kubernetes types, where deepcopy-gen is used.
func (in *LoginRequest) DeepCopyInto(out *LoginRequest) {
	p := proto.Clone(in).(*LoginRequest)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new LoginRequest. Required by controller-gen.
func (in *LoginRequest) DeepCopy() *LoginRequest {
	if in == nil {
		return nil
	}
	out := new(LoginRequest)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new LoginRequest. Required by controller-gen.
func (in *LoginRequest) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using LoginResponse within kubernetes types, where deepcopy-gen is used.
func (in *LoginResponse) DeepCopyInto(out *LoginResponse) {
	p := proto.Clone(in).(*LoginResponse)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new LoginResponse. Required by controller-gen.
func (in *LoginResponse) DeepCopy() *LoginResponse {
	if in == nil {
		return nil
	}
	out := new(LoginResponse)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new LoginResponse. Required by controller-gen.
func (in *LoginResponse) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using RefreshTokenRequest within kubernetes types, where deepcopy-gen is used.
func (in *RefreshTokenRequest) DeepCopyInto(out *RefreshTokenRequest) {
	p := proto.Clone(in).(*RefreshTokenRequest)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new RefreshTokenRequest. Required by controller-gen.
func (in *RefreshTokenRequest) DeepCopy() *RefreshTokenRequest {
	if in == nil {
		return nil
	}
	out := new(RefreshTokenRequest)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new RefreshTokenRequest. Required by controller-gen.
func (in *RefreshTokenRequest) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using RefreshTokenResponse within kubernetes types, where deepcopy-gen is used.
func (in *RefreshTokenResponse) DeepCopyInto(out *RefreshTokenResponse) {
	p := proto.Clone(in).(*RefreshTokenResponse)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new RefreshTokenResponse. Required by controller-gen.
func (in *RefreshTokenResponse) DeepCopy() *RefreshTokenResponse {
	if in == nil {
		return nil
	}
	out := new(RefreshTokenResponse)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new RefreshTokenResponse. Required by controller-gen.
func (in *RefreshTokenResponse) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using LogoutRequest within kubernetes types, where deepcopy-gen is used.
func (in *LogoutRequest) DeepCopyInto(out *LogoutRequest) {
	p := proto.Clone(in).(*LogoutRequest)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new LogoutRequest. Required by controller-gen.
func (in *LogoutRequest) DeepCopy() *LogoutRequest {
	if in == nil {
		return nil
	}
	out := new(LogoutRequest)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new LogoutRequest. Required by controller-gen.
func (in *LogoutRequest) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using ValidateRequest within kubernetes types, where deepcopy-gen is used.
func (in *ValidateRequest) DeepCopyInto(out *ValidateRequest) {
	p := proto.Clone(in).(*ValidateRequest)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ValidateRequest. Required by controller-gen.
func (in *ValidateRequest) DeepCopy() *ValidateRequest {
	if in == nil {
		return nil
	}
	out := new(ValidateRequest)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new ValidateRequest. Required by controller-gen.
func (in *ValidateRequest) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using ValidateResult within kubernetes types, where deepcopy-gen is used.
func (in *ValidateResult) DeepCopyInto(out *ValidateResult) {
	p := proto.Clone(in).(*ValidateResult)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ValidateResult. Required by controller-gen.
func (in *ValidateResult) DeepCopy() *ValidateResult {
	if in == nil {
		return nil
	}
	out := new(ValidateResult)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new ValidateResult. Required by controller-gen.
func (in *ValidateResult) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}
