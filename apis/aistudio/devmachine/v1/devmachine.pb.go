// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v5.29.3
// source: aistudio/devmachine/v1/devmachine.proto

package v1

import (
	v1 "git.lianjia.com/cloudnative/kic/kic-platform/apis/aistudio/config/v1"
	common "git.lianjia.com/cloudnative/kic/kic-platform/apis/common"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ProtocolType int32

const (
	ProtocolType_TCP  ProtocolType = 0
	ProtocolType_UDP  ProtocolType = 1
	ProtocolType_SCTP ProtocolType = 2
)

// Enum value maps for ProtocolType.
var (
	ProtocolType_name = map[int32]string{
		0: "TCP",
		1: "UDP",
		2: "SCTP",
	}
	ProtocolType_value = map[string]int32{
		"TCP":  0,
		"UDP":  1,
		"SCTP": 2,
	}
)

func (x ProtocolType) Enum() *ProtocolType {
	p := new(ProtocolType)
	*p = x
	return p
}

func (x ProtocolType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ProtocolType) Descriptor() protoreflect.EnumDescriptor {
	return file_aistudio_devmachine_v1_devmachine_proto_enumTypes[0].Descriptor()
}

func (ProtocolType) Type() protoreflect.EnumType {
	return &file_aistudio_devmachine_v1_devmachine_proto_enumTypes[0]
}

func (x ProtocolType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ProtocolType.Descriptor instead.
func (ProtocolType) EnumDescriptor() ([]byte, []int) {
	return file_aistudio_devmachine_v1_devmachine_proto_rawDescGZIP(), []int{0}
}

type CreateDevMachineRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name          string            `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	DisplayName   string            `protobuf:"bytes,2,opt,name=displayName,proto3" json:"displayName,omitempty"`
	Description   string            `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	WorkspaceName string            `protobuf:"bytes,4,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Region        string            `protobuf:"bytes,5,opt,name=region,proto3" json:"region,omitempty"`
	Labels        map[string]string `protobuf:"bytes,6,rep,name=labels,proto3" json:"labels,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Members       []string          `protobuf:"bytes,7,rep,name=members,proto3" json:"members,omitempty"`
	Managers      []string          `protobuf:"bytes,17,rep,name=managers,proto3" json:"managers,omitempty"` //DevMachine的管理员
	// 环境配置
	Image              string           `protobuf:"bytes,8,opt,name=image,proto3" json:"image,omitempty"`                             // 基础镜像,一般来说必须是Ubuntu或者Centos作为基础镜像的Image才行
	QueueName          string           `protobuf:"bytes,9,opt,name=queueName,proto3" json:"queueName,omitempty"`                     // 运行队列
	TensorboardEnabled bool             `protobuf:"varint,16,opt,name=tensorboardEnabled,proto3" json:"tensorboardEnabled,omitempty"` // 是否开启tensorboard
	EnvVars            []*common.EnvVar `protobuf:"bytes,10,rep,name=envVars,proto3" json:"envVars,omitempty"`                        //环境变量
	// 规则配置，支持随机, 指定节点,节点规格
	NodeSpecificationName string                `protobuf:"bytes,13,opt,name=nodeSpecificationName,proto3" json:"nodeSpecificationName,omitempty"` //节点规格
	NodeIP                string                `protobuf:"bytes,12,opt,name=nodeIP,proto3" json:"nodeIP,omitempty"`
	Specification         *common.Specification `protobuf:"bytes,11,opt,name=specification,proto3" json:"specification,omitempty"` //规格
	// 存储配置
	VolumeSpecs []*common.VolumeSpec `protobuf:"bytes,14,rep,name=volumeSpecs,proto3" json:"volumeSpecs,omitempty"` //卷, 可支持直接挂卷,可以写训练的模型
	ConfigSpecs []*v1.ConfigSpec     `protobuf:"bytes,25,rep,name=configSpecs,proto3" json:"configSpecs,omitempty"` //文件配置
	// 网络设置
	HostNetworkEnabled bool `protobuf:"varint,18,opt,name=hostNetworkEnabled,proto3" json:"hostNetworkEnabled,omitempty"` // 是否使用宿主机网络
	HostIPCEnabled     bool `protobuf:"varint,22,opt,name=hostIPCEnabled,proto3" json:"hostIPCEnabled,omitempty"`         // 是否使用宿主机IPC
	// 特权模式
	PrivilegedModeEnabled bool `protobuf:"varint,23,opt,name=privilegedModeEnabled,proto3" json:"privilegedModeEnabled,omitempty"` // 是否开启特权模式
	// 访问配置
	// ssh访问配置
	SshConfig         *SSHConfig          `protobuf:"bytes,15,opt,name=sshConfig,proto3" json:"sshConfig,omitempty"`
	ExposePortConfigs []*ExposePortConfig `protobuf:"bytes,19,rep,name=exposePortConfigs,proto3" json:"exposePortConfigs,omitempty"`
	HadoopEnabled     bool                `protobuf:"varint,20,opt,name=hadoopEnabled,proto3" json:"hadoopEnabled,omitempty"`
	// hadoop用户
	HadoopUsers     []string `protobuf:"bytes,21,rep,name=hadoopUsers,proto3" json:"hadoopUsers,omitempty"`
	GpuShareEnabled bool     `protobuf:"varint,24,opt,name=gpuShareEnabled,proto3" json:"gpuShareEnabled,omitempty"` // 是否开启GPU共享
}

func (x *CreateDevMachineRequest) Reset() {
	*x = CreateDevMachineRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_devmachine_v1_devmachine_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateDevMachineRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateDevMachineRequest) ProtoMessage() {}

func (x *CreateDevMachineRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_devmachine_v1_devmachine_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateDevMachineRequest.ProtoReflect.Descriptor instead.
func (*CreateDevMachineRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_devmachine_v1_devmachine_proto_rawDescGZIP(), []int{0}
}

func (x *CreateDevMachineRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateDevMachineRequest) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *CreateDevMachineRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CreateDevMachineRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *CreateDevMachineRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *CreateDevMachineRequest) GetLabels() map[string]string {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *CreateDevMachineRequest) GetMembers() []string {
	if x != nil {
		return x.Members
	}
	return nil
}

func (x *CreateDevMachineRequest) GetManagers() []string {
	if x != nil {
		return x.Managers
	}
	return nil
}

func (x *CreateDevMachineRequest) GetImage() string {
	if x != nil {
		return x.Image
	}
	return ""
}

func (x *CreateDevMachineRequest) GetQueueName() string {
	if x != nil {
		return x.QueueName
	}
	return ""
}

func (x *CreateDevMachineRequest) GetTensorboardEnabled() bool {
	if x != nil {
		return x.TensorboardEnabled
	}
	return false
}

func (x *CreateDevMachineRequest) GetEnvVars() []*common.EnvVar {
	if x != nil {
		return x.EnvVars
	}
	return nil
}

func (x *CreateDevMachineRequest) GetNodeSpecificationName() string {
	if x != nil {
		return x.NodeSpecificationName
	}
	return ""
}

func (x *CreateDevMachineRequest) GetNodeIP() string {
	if x != nil {
		return x.NodeIP
	}
	return ""
}

func (x *CreateDevMachineRequest) GetSpecification() *common.Specification {
	if x != nil {
		return x.Specification
	}
	return nil
}

func (x *CreateDevMachineRequest) GetVolumeSpecs() []*common.VolumeSpec {
	if x != nil {
		return x.VolumeSpecs
	}
	return nil
}

func (x *CreateDevMachineRequest) GetConfigSpecs() []*v1.ConfigSpec {
	if x != nil {
		return x.ConfigSpecs
	}
	return nil
}

func (x *CreateDevMachineRequest) GetHostNetworkEnabled() bool {
	if x != nil {
		return x.HostNetworkEnabled
	}
	return false
}

func (x *CreateDevMachineRequest) GetHostIPCEnabled() bool {
	if x != nil {
		return x.HostIPCEnabled
	}
	return false
}

func (x *CreateDevMachineRequest) GetPrivilegedModeEnabled() bool {
	if x != nil {
		return x.PrivilegedModeEnabled
	}
	return false
}

func (x *CreateDevMachineRequest) GetSshConfig() *SSHConfig {
	if x != nil {
		return x.SshConfig
	}
	return nil
}

func (x *CreateDevMachineRequest) GetExposePortConfigs() []*ExposePortConfig {
	if x != nil {
		return x.ExposePortConfigs
	}
	return nil
}

func (x *CreateDevMachineRequest) GetHadoopEnabled() bool {
	if x != nil {
		return x.HadoopEnabled
	}
	return false
}

func (x *CreateDevMachineRequest) GetHadoopUsers() []string {
	if x != nil {
		return x.HadoopUsers
	}
	return nil
}

func (x *CreateDevMachineRequest) GetGpuShareEnabled() bool {
	if x != nil {
		return x.GpuShareEnabled
	}
	return false
}

type ExposePortConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Protocol   ProtocolType `protobuf:"varint,2,opt,name=protocol,proto3,enum=apis.aistudio.devmachine.v1.ProtocolType" json:"protocol,omitempty"`
	TargetPort int32        `protobuf:"varint,5,opt,name=targetPort,proto3" json:"targetPort,omitempty"` // 需要开启的端口
	NodePort   int32        `protobuf:"varint,3,opt,name=nodePort,proto3" json:"nodePort,omitempty"`     // 指定端口
}

func (x *ExposePortConfig) Reset() {
	*x = ExposePortConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_devmachine_v1_devmachine_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExposePortConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExposePortConfig) ProtoMessage() {}

func (x *ExposePortConfig) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_devmachine_v1_devmachine_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExposePortConfig.ProtoReflect.Descriptor instead.
func (*ExposePortConfig) Descriptor() ([]byte, []int) {
	return file_aistudio_devmachine_v1_devmachine_proto_rawDescGZIP(), []int{1}
}

func (x *ExposePortConfig) GetProtocol() ProtocolType {
	if x != nil {
		return x.Protocol
	}
	return ProtocolType_TCP
}

func (x *ExposePortConfig) GetTargetPort() int32 {
	if x != nil {
		return x.TargetPort
	}
	return 0
}

func (x *ExposePortConfig) GetNodePort() int32 {
	if x != nil {
		return x.NodePort
	}
	return 0
}

type ListExposePortConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name         string       `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Protocol     ProtocolType `protobuf:"varint,2,opt,name=protocol,proto3,enum=apis.aistudio.devmachine.v1.ProtocolType" json:"protocol,omitempty"`
	TargetPort   int32        `protobuf:"varint,5,opt,name=targetPort,proto3" json:"targetPort,omitempty"`     // 需要开启的端口
	NodePort     int32        `protobuf:"varint,3,opt,name=nodePort,proto3" json:"nodePort,omitempty"`         // 指定端口
	IsSystemPort bool         `protobuf:"varint,4,opt,name=isSystemPort,proto3" json:"isSystemPort,omitempty"` // 是否是系统端口
	NodeIP       string       `protobuf:"bytes,6,opt,name=nodeIP,proto3" json:"nodeIP,omitempty"`              // 节点IP
}

func (x *ListExposePortConfig) Reset() {
	*x = ListExposePortConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_devmachine_v1_devmachine_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListExposePortConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListExposePortConfig) ProtoMessage() {}

func (x *ListExposePortConfig) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_devmachine_v1_devmachine_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListExposePortConfig.ProtoReflect.Descriptor instead.
func (*ListExposePortConfig) Descriptor() ([]byte, []int) {
	return file_aistudio_devmachine_v1_devmachine_proto_rawDescGZIP(), []int{2}
}

func (x *ListExposePortConfig) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ListExposePortConfig) GetProtocol() ProtocolType {
	if x != nil {
		return x.Protocol
	}
	return ProtocolType_TCP
}

func (x *ListExposePortConfig) GetTargetPort() int32 {
	if x != nil {
		return x.TargetPort
	}
	return 0
}

func (x *ListExposePortConfig) GetNodePort() int32 {
	if x != nil {
		return x.NodePort
	}
	return 0
}

func (x *ListExposePortConfig) GetIsSystemPort() bool {
	if x != nil {
		return x.IsSystemPort
	}
	return false
}

func (x *ListExposePortConfig) GetNodeIP() string {
	if x != nil {
		return x.NodeIP
	}
	return ""
}

type SSHConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EnableSSH   bool     `protobuf:"varint,1,opt,name=enableSSH,proto3" json:"enableSSH,omitempty"`
	SshUser     string   `protobuf:"bytes,2,opt,name=sshUser,proto3" json:"sshUser,omitempty"` // 不设置时，默认为 root
	SshPassword string   `protobuf:"bytes,3,opt,name=sshPassword,proto3" json:"sshPassword,omitempty"`
	SshKeys     []string `protobuf:"bytes,4,rep,name=sshKeys,proto3" json:"sshKeys,omitempty"`
}

func (x *SSHConfig) Reset() {
	*x = SSHConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_devmachine_v1_devmachine_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SSHConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SSHConfig) ProtoMessage() {}

func (x *SSHConfig) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_devmachine_v1_devmachine_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SSHConfig.ProtoReflect.Descriptor instead.
func (*SSHConfig) Descriptor() ([]byte, []int) {
	return file_aistudio_devmachine_v1_devmachine_proto_rawDescGZIP(), []int{3}
}

func (x *SSHConfig) GetEnableSSH() bool {
	if x != nil {
		return x.EnableSSH
	}
	return false
}

func (x *SSHConfig) GetSshUser() string {
	if x != nil {
		return x.SshUser
	}
	return ""
}

func (x *SSHConfig) GetSshPassword() string {
	if x != nil {
		return x.SshPassword
	}
	return ""
}

func (x *SSHConfig) GetSshKeys() []string {
	if x != nil {
		return x.SshKeys
	}
	return nil
}

type DevMachine struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id            string            `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`                                                                                                 //DevMachine的ID
	Name          string            `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`                                                                                             //DevMachine的名字
	DisplayName   string            `protobuf:"bytes,3,opt,name=displayName,proto3" json:"displayName,omitempty"`                                                                               //DevMachine的显示名字
	Description   string            `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`                                                                               //DevMachine的描述
	WorkspaceName string            `protobuf:"bytes,5,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`                                                                           //工作空间的名字
	Region        string            `protobuf:"bytes,6,opt,name=region,proto3" json:"region,omitempty"`                                                                                         //DevMachine所在的区域
	Labels        map[string]string `protobuf:"bytes,7,rep,name=labels,proto3" json:"labels,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` //DevMachine的标签
	Members       []string          `protobuf:"bytes,8,rep,name=members,proto3" json:"members,omitempty"`                                                                                       //DevMachine的成员
	Creator       string            `protobuf:"bytes,23,opt,name=creator,proto3" json:"creator,omitempty"`                                                                                      //创建者
	// 环境配置
	Image                 string                `protobuf:"bytes,9,opt,name=image,proto3" json:"image,omitempty"`                                  // 基础镜像,一般来说必须是Ubuntu或者Centos作为基础镜像的Image才行
	QueueName             string                `protobuf:"bytes,10,opt,name=queueName,proto3" json:"queueName,omitempty"`                         // 运行队列
	EnvVars               []*common.EnvVar      `protobuf:"bytes,11,rep,name=envVars,proto3" json:"envVars,omitempty"`                             //环境变量
	Specification         *common.Specification `protobuf:"bytes,12,opt,name=specification,proto3" json:"specification,omitempty"`                 //规格
	NodeSpecificationName string                `protobuf:"bytes,13,opt,name=nodeSpecificationName,proto3" json:"nodeSpecificationName,omitempty"` //节点规格
	NodeIP                string                `protobuf:"bytes,14,opt,name=nodeIP,proto3" json:"nodeIP,omitempty"`
	TensorboardEnabled    bool                  `protobuf:"varint,21,opt,name=tensorboardEnabled,proto3" json:"tensorboardEnabled,omitempty"` // 是否开启tensorboard
	// 存储配置
	VolumeSpecs []*common.VolumeSpec `protobuf:"bytes,15,rep,name=volumeSpecs,proto3" json:"volumeSpecs,omitempty"` //卷, 可支持直接挂卷,可以写训练的模型
	CreateTime  string               `protobuf:"bytes,20,opt,name=createTime,proto3" json:"createTime,omitempty"`
	UpdateTime  string               `protobuf:"bytes,18,opt,name=updateTime,proto3" json:"updateTime,omitempty"`
	IsDeleted   bool                 `protobuf:"varint,22,opt,name=isDeleted,proto3" json:"isDeleted,omitempty"`
	Managers    []string             `protobuf:"bytes,24,rep,name=managers,proto3" json:"managers,omitempty"` //DevMachine的管理员
	// 网络设置
	HostNetworkEnabled bool `protobuf:"varint,19,opt,name=hostNetworkEnabled,proto3" json:"hostNetworkEnabled,omitempty"` // 是否使用宿主机网络
	HostIPCEnabled     bool `protobuf:"varint,27,opt,name=hostIPCEnabled,proto3" json:"hostIPCEnabled,omitempty"`         // 是否使用宿主机IPC
	// 特权模式
	PrivilegedModeEnabled bool `protobuf:"varint,28,opt,name=privilegedModeEnabled,proto3" json:"privilegedModeEnabled,omitempty"` // 是否开启特权模式
	// ssh访问配置
	SshConfig         *SSHConfig          `protobuf:"bytes,16,opt,name=sshConfig,proto3" json:"sshConfig,omitempty"`
	ExposePortConfigs []*ExposePortConfig `protobuf:"bytes,25,rep,name=exposePortConfigs,proto3" json:"exposePortConfigs,omitempty"`
	DmStatus          *DevMachineStatus   `protobuf:"bytes,17,opt,name=dmStatus,proto3" json:"dmStatus,omitempty"`
	GpuShareEnabled   bool                `protobuf:"varint,26,opt,name=gpuShareEnabled,proto3" json:"gpuShareEnabled,omitempty"` // 是否开启GPU共享
	IsBuildingImage   bool                `protobuf:"varint,29,opt,name=isBuildingImage,proto3" json:"isBuildingImage,omitempty"` // 是否正在构建
	ConfigSpecs       []*v1.ConfigSpec    `protobuf:"bytes,30,rep,name=configSpecs,proto3" json:"configSpecs,omitempty"`          //文件配置
	HadoopEnabled     bool                `protobuf:"varint,31,opt,name=hadoopEnabled,proto3" json:"hadoopEnabled,omitempty"`     // 是否开启hadoop
	HadoopUsers       []string            `protobuf:"bytes,32,rep,name=hadoopUsers,proto3" json:"hadoopUsers,omitempty"`          // hadoop用户
}

func (x *DevMachine) Reset() {
	*x = DevMachine{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_devmachine_v1_devmachine_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DevMachine) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DevMachine) ProtoMessage() {}

func (x *DevMachine) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_devmachine_v1_devmachine_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DevMachine.ProtoReflect.Descriptor instead.
func (*DevMachine) Descriptor() ([]byte, []int) {
	return file_aistudio_devmachine_v1_devmachine_proto_rawDescGZIP(), []int{4}
}

func (x *DevMachine) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *DevMachine) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DevMachine) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *DevMachine) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *DevMachine) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *DevMachine) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *DevMachine) GetLabels() map[string]string {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *DevMachine) GetMembers() []string {
	if x != nil {
		return x.Members
	}
	return nil
}

func (x *DevMachine) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *DevMachine) GetImage() string {
	if x != nil {
		return x.Image
	}
	return ""
}

func (x *DevMachine) GetQueueName() string {
	if x != nil {
		return x.QueueName
	}
	return ""
}

func (x *DevMachine) GetEnvVars() []*common.EnvVar {
	if x != nil {
		return x.EnvVars
	}
	return nil
}

func (x *DevMachine) GetSpecification() *common.Specification {
	if x != nil {
		return x.Specification
	}
	return nil
}

func (x *DevMachine) GetNodeSpecificationName() string {
	if x != nil {
		return x.NodeSpecificationName
	}
	return ""
}

func (x *DevMachine) GetNodeIP() string {
	if x != nil {
		return x.NodeIP
	}
	return ""
}

func (x *DevMachine) GetTensorboardEnabled() bool {
	if x != nil {
		return x.TensorboardEnabled
	}
	return false
}

func (x *DevMachine) GetVolumeSpecs() []*common.VolumeSpec {
	if x != nil {
		return x.VolumeSpecs
	}
	return nil
}

func (x *DevMachine) GetCreateTime() string {
	if x != nil {
		return x.CreateTime
	}
	return ""
}

func (x *DevMachine) GetUpdateTime() string {
	if x != nil {
		return x.UpdateTime
	}
	return ""
}

func (x *DevMachine) GetIsDeleted() bool {
	if x != nil {
		return x.IsDeleted
	}
	return false
}

func (x *DevMachine) GetManagers() []string {
	if x != nil {
		return x.Managers
	}
	return nil
}

func (x *DevMachine) GetHostNetworkEnabled() bool {
	if x != nil {
		return x.HostNetworkEnabled
	}
	return false
}

func (x *DevMachine) GetHostIPCEnabled() bool {
	if x != nil {
		return x.HostIPCEnabled
	}
	return false
}

func (x *DevMachine) GetPrivilegedModeEnabled() bool {
	if x != nil {
		return x.PrivilegedModeEnabled
	}
	return false
}

func (x *DevMachine) GetSshConfig() *SSHConfig {
	if x != nil {
		return x.SshConfig
	}
	return nil
}

func (x *DevMachine) GetExposePortConfigs() []*ExposePortConfig {
	if x != nil {
		return x.ExposePortConfigs
	}
	return nil
}

func (x *DevMachine) GetDmStatus() *DevMachineStatus {
	if x != nil {
		return x.DmStatus
	}
	return nil
}

func (x *DevMachine) GetGpuShareEnabled() bool {
	if x != nil {
		return x.GpuShareEnabled
	}
	return false
}

func (x *DevMachine) GetIsBuildingImage() bool {
	if x != nil {
		return x.IsBuildingImage
	}
	return false
}

func (x *DevMachine) GetConfigSpecs() []*v1.ConfigSpec {
	if x != nil {
		return x.ConfigSpecs
	}
	return nil
}

func (x *DevMachine) GetHadoopEnabled() bool {
	if x != nil {
		return x.HadoopEnabled
	}
	return false
}

func (x *DevMachine) GetHadoopUsers() []string {
	if x != nil {
		return x.HadoopUsers
	}
	return nil
}

type DevMachineStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	State                 string `protobuf:"bytes,1,opt,name=state,proto3" json:"state,omitempty"`                                  //状态
	Message               string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`                              //状态信息
	Reason                string `protobuf:"bytes,3,opt,name=reason,proto3" json:"reason,omitempty"`                                //状态原因
	LastTransitionTime    string `protobuf:"bytes,4,opt,name=lastTransitionTime,proto3" json:"lastTransitionTime,omitempty"`        //最后一次状态变更时间
	CreateTimestamp       string `protobuf:"bytes,5,opt,name=createTimestamp,proto3" json:"createTimestamp,omitempty"`              //创建时间
	RunningDuration       string `protobuf:"bytes,7,opt,name=runningDuration,proto3" json:"runningDuration,omitempty"`              //运行时长
	ClusterName           string `protobuf:"bytes,8,opt,name=clusterName,proto3" json:"clusterName,omitempty"`                      //集群名称
	NodeName              string `protobuf:"bytes,9,opt,name=nodeName,proto3" json:"nodeName,omitempty"`                            //节点名称
	NodeIP                string `protobuf:"bytes,10,opt,name=nodeIP,proto3" json:"nodeIP,omitempty"`                               //节点IP
	NodeSpecificationName string `protobuf:"bytes,11,opt,name=nodeSpecificationName,proto3" json:"nodeSpecificationName,omitempty"` //节点规格
	VscodeReady           bool   `protobuf:"varint,12,opt,name=vscodeReady,proto3" json:"vscodeReady,omitempty"`                    //vscode是否准备就绪
	JupyterReady          bool   `protobuf:"varint,13,opt,name=jupyterReady,proto3" json:"jupyterReady,omitempty"`                  //jupyter是否准备就绪
	SshEnabled            bool   `protobuf:"varint,14,opt,name=sshEnabled,proto3" json:"sshEnabled,omitempty"`                      //ssh是否开启
	SshPort               int32  `protobuf:"varint,15,opt,name=sshPort,proto3" json:"sshPort,omitempty"`                            //ssh端口
	PodName               string `protobuf:"bytes,16,opt,name=podName,proto3" json:"podName,omitempty"`                             //podName
	Namespace             string `protobuf:"bytes,17,opt,name=namespace,proto3" json:"namespace,omitempty"`                         //podNamespace
}

func (x *DevMachineStatus) Reset() {
	*x = DevMachineStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_devmachine_v1_devmachine_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DevMachineStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DevMachineStatus) ProtoMessage() {}

func (x *DevMachineStatus) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_devmachine_v1_devmachine_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DevMachineStatus.ProtoReflect.Descriptor instead.
func (*DevMachineStatus) Descriptor() ([]byte, []int) {
	return file_aistudio_devmachine_v1_devmachine_proto_rawDescGZIP(), []int{5}
}

func (x *DevMachineStatus) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

func (x *DevMachineStatus) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *DevMachineStatus) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *DevMachineStatus) GetLastTransitionTime() string {
	if x != nil {
		return x.LastTransitionTime
	}
	return ""
}

func (x *DevMachineStatus) GetCreateTimestamp() string {
	if x != nil {
		return x.CreateTimestamp
	}
	return ""
}

func (x *DevMachineStatus) GetRunningDuration() string {
	if x != nil {
		return x.RunningDuration
	}
	return ""
}

func (x *DevMachineStatus) GetClusterName() string {
	if x != nil {
		return x.ClusterName
	}
	return ""
}

func (x *DevMachineStatus) GetNodeName() string {
	if x != nil {
		return x.NodeName
	}
	return ""
}

func (x *DevMachineStatus) GetNodeIP() string {
	if x != nil {
		return x.NodeIP
	}
	return ""
}

func (x *DevMachineStatus) GetNodeSpecificationName() string {
	if x != nil {
		return x.NodeSpecificationName
	}
	return ""
}

func (x *DevMachineStatus) GetVscodeReady() bool {
	if x != nil {
		return x.VscodeReady
	}
	return false
}

func (x *DevMachineStatus) GetJupyterReady() bool {
	if x != nil {
		return x.JupyterReady
	}
	return false
}

func (x *DevMachineStatus) GetSshEnabled() bool {
	if x != nil {
		return x.SshEnabled
	}
	return false
}

func (x *DevMachineStatus) GetSshPort() int32 {
	if x != nil {
		return x.SshPort
	}
	return 0
}

func (x *DevMachineStatus) GetPodName() string {
	if x != nil {
		return x.PodName
	}
	return ""
}

func (x *DevMachineStatus) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

type UpdateDevMachineRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DevMachineName string            `protobuf:"bytes,1,opt,name=devMachineName,proto3" json:"devMachineName,omitempty"`
	DisplayName    string            `protobuf:"bytes,2,opt,name=displayName,proto3" json:"displayName,omitempty"`
	Description    string            `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	WorkspaceName  string            `protobuf:"bytes,4,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Region         string            `protobuf:"bytes,5,opt,name=region,proto3" json:"region,omitempty"`
	Labels         map[string]string `protobuf:"bytes,6,rep,name=labels,proto3" json:"labels,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Members        []string          `protobuf:"bytes,7,rep,name=members,proto3" json:"members,omitempty"`
	Managers       []string          `protobuf:"bytes,17,rep,name=managers,proto3" json:"managers,omitempty"` //DevMachine的管理员
	// 环境配置
	Image                 string                `protobuf:"bytes,8,opt,name=image,proto3" json:"image,omitempty"`                                  // 基础镜像,一般来说必须是Ubuntu或者Centos作为基础镜像的Image才行
	QueueName             string                `protobuf:"bytes,9,opt,name=queueName,proto3" json:"queueName,omitempty"`                          // 运行队列
	TensorboardEnabled    bool                  `protobuf:"varint,16,opt,name=tensorboardEnabled,proto3" json:"tensorboardEnabled,omitempty"`      // 是否开启tensorboard
	EnvVars               []*common.EnvVar      `protobuf:"bytes,10,rep,name=envVars,proto3" json:"envVars,omitempty"`                             //环境变量
	Specification         *common.Specification `protobuf:"bytes,11,opt,name=specification,proto3" json:"specification,omitempty"`                 //规格
	NodeSpecificationName string                `protobuf:"bytes,12,opt,name=nodeSpecificationName,proto3" json:"nodeSpecificationName,omitempty"` //节点规格
	NodeIP                string                `protobuf:"bytes,13,opt,name=nodeIP,proto3" json:"nodeIP,omitempty"`                               // 指定节点
	// 存储配置
	VolumeSpecs []*common.VolumeSpec `protobuf:"bytes,14,rep,name=volumeSpecs,proto3" json:"volumeSpecs,omitempty"` //卷, 可支持直接挂卷,可以写训练的模型
	// 网络设置
	HostNetworkEnabled bool `protobuf:"varint,18,opt,name=hostNetworkEnabled,proto3" json:"hostNetworkEnabled,omitempty"` // 是否使用宿主机网络
	HostIPCEnabled     bool `protobuf:"varint,28,opt,name=hostIPCEnabled,proto3" json:"hostIPCEnabled,omitempty"`         // 是否使用宿主机IPC
	// 特权模式
	PrivilegedModeEnabled bool `protobuf:"varint,29,opt,name=privilegedModeEnabled,proto3" json:"privilegedModeEnabled,omitempty"` // 是否使用特权模式
	// ssh访问配置
	SshConfig *SSHConfig `protobuf:"bytes,15,opt,name=sshConfig,proto3" json:"sshConfig,omitempty"`
	// 是否开启hadoop
	HadoopEnabled bool `protobuf:"varint,26,opt,name=hadoopEnabled,proto3" json:"hadoopEnabled,omitempty"`
	// hadoop用户
	HadoopUsers       []string            `protobuf:"bytes,27,rep,name=hadoopUsers,proto3" json:"hadoopUsers,omitempty"`
	ExposePortConfigs []*ExposePortConfig `protobuf:"bytes,25,rep,name=exposePortConfigs,proto3" json:"exposePortConfigs,omitempty"`
	GpuShareEnabled   bool                `protobuf:"varint,30,opt,name=gpuShareEnabled,proto3" json:"gpuShareEnabled,omitempty"` // 是否开启GPU共享
	ConfigSpecs       []*v1.ConfigSpec    `protobuf:"bytes,31,rep,name=configSpecs,proto3" json:"configSpecs,omitempty"`          //文件配置
}

func (x *UpdateDevMachineRequest) Reset() {
	*x = UpdateDevMachineRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_devmachine_v1_devmachine_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateDevMachineRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateDevMachineRequest) ProtoMessage() {}

func (x *UpdateDevMachineRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_devmachine_v1_devmachine_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateDevMachineRequest.ProtoReflect.Descriptor instead.
func (*UpdateDevMachineRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_devmachine_v1_devmachine_proto_rawDescGZIP(), []int{6}
}

func (x *UpdateDevMachineRequest) GetDevMachineName() string {
	if x != nil {
		return x.DevMachineName
	}
	return ""
}

func (x *UpdateDevMachineRequest) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *UpdateDevMachineRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *UpdateDevMachineRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *UpdateDevMachineRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *UpdateDevMachineRequest) GetLabels() map[string]string {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *UpdateDevMachineRequest) GetMembers() []string {
	if x != nil {
		return x.Members
	}
	return nil
}

func (x *UpdateDevMachineRequest) GetManagers() []string {
	if x != nil {
		return x.Managers
	}
	return nil
}

func (x *UpdateDevMachineRequest) GetImage() string {
	if x != nil {
		return x.Image
	}
	return ""
}

func (x *UpdateDevMachineRequest) GetQueueName() string {
	if x != nil {
		return x.QueueName
	}
	return ""
}

func (x *UpdateDevMachineRequest) GetTensorboardEnabled() bool {
	if x != nil {
		return x.TensorboardEnabled
	}
	return false
}

func (x *UpdateDevMachineRequest) GetEnvVars() []*common.EnvVar {
	if x != nil {
		return x.EnvVars
	}
	return nil
}

func (x *UpdateDevMachineRequest) GetSpecification() *common.Specification {
	if x != nil {
		return x.Specification
	}
	return nil
}

func (x *UpdateDevMachineRequest) GetNodeSpecificationName() string {
	if x != nil {
		return x.NodeSpecificationName
	}
	return ""
}

func (x *UpdateDevMachineRequest) GetNodeIP() string {
	if x != nil {
		return x.NodeIP
	}
	return ""
}

func (x *UpdateDevMachineRequest) GetVolumeSpecs() []*common.VolumeSpec {
	if x != nil {
		return x.VolumeSpecs
	}
	return nil
}

func (x *UpdateDevMachineRequest) GetHostNetworkEnabled() bool {
	if x != nil {
		return x.HostNetworkEnabled
	}
	return false
}

func (x *UpdateDevMachineRequest) GetHostIPCEnabled() bool {
	if x != nil {
		return x.HostIPCEnabled
	}
	return false
}

func (x *UpdateDevMachineRequest) GetPrivilegedModeEnabled() bool {
	if x != nil {
		return x.PrivilegedModeEnabled
	}
	return false
}

func (x *UpdateDevMachineRequest) GetSshConfig() *SSHConfig {
	if x != nil {
		return x.SshConfig
	}
	return nil
}

func (x *UpdateDevMachineRequest) GetHadoopEnabled() bool {
	if x != nil {
		return x.HadoopEnabled
	}
	return false
}

func (x *UpdateDevMachineRequest) GetHadoopUsers() []string {
	if x != nil {
		return x.HadoopUsers
	}
	return nil
}

func (x *UpdateDevMachineRequest) GetExposePortConfigs() []*ExposePortConfig {
	if x != nil {
		return x.ExposePortConfigs
	}
	return nil
}

func (x *UpdateDevMachineRequest) GetGpuShareEnabled() bool {
	if x != nil {
		return x.GpuShareEnabled
	}
	return false
}

func (x *UpdateDevMachineRequest) GetConfigSpecs() []*v1.ConfigSpec {
	if x != nil {
		return x.ConfigSpecs
	}
	return nil
}

type CreateTensorboardRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName  string `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	DevMachineName string `protobuf:"bytes,2,opt,name=devMachineName,proto3" json:"devMachineName,omitempty"`
}

func (x *CreateTensorboardRequest) Reset() {
	*x = CreateTensorboardRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_devmachine_v1_devmachine_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateTensorboardRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateTensorboardRequest) ProtoMessage() {}

func (x *CreateTensorboardRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_devmachine_v1_devmachine_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateTensorboardRequest.ProtoReflect.Descriptor instead.
func (*CreateTensorboardRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_devmachine_v1_devmachine_proto_rawDescGZIP(), []int{7}
}

func (x *CreateTensorboardRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *CreateTensorboardRequest) GetDevMachineName() string {
	if x != nil {
		return x.DevMachineName
	}
	return ""
}

type GetTensorboardRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName  string `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	DevMachineName string `protobuf:"bytes,2,opt,name=devMachineName,proto3" json:"devMachineName,omitempty"`
}

func (x *GetTensorboardRequest) Reset() {
	*x = GetTensorboardRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_devmachine_v1_devmachine_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTensorboardRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTensorboardRequest) ProtoMessage() {}

func (x *GetTensorboardRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_devmachine_v1_devmachine_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTensorboardRequest.ProtoReflect.Descriptor instead.
func (*GetTensorboardRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_devmachine_v1_devmachine_proto_rawDescGZIP(), []int{8}
}

func (x *GetTensorboardRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *GetTensorboardRequest) GetDevMachineName() string {
	if x != nil {
		return x.DevMachineName
	}
	return ""
}

type GetTensorboardResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url   string `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	State string `protobuf:"bytes,2,opt,name=state,proto3" json:"state,omitempty"`
}

func (x *GetTensorboardResponse) Reset() {
	*x = GetTensorboardResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_devmachine_v1_devmachine_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTensorboardResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTensorboardResponse) ProtoMessage() {}

func (x *GetTensorboardResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_devmachine_v1_devmachine_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTensorboardResponse.ProtoReflect.Descriptor instead.
func (*GetTensorboardResponse) Descriptor() ([]byte, []int) {
	return file_aistudio_devmachine_v1_devmachine_proto_rawDescGZIP(), []int{9}
}

func (x *GetTensorboardResponse) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *GetTensorboardResponse) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

type ListVolumes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Volumes []*Volume `protobuf:"bytes,1,rep,name=volumes,proto3" json:"volumes,omitempty"`
}

func (x *ListVolumes) Reset() {
	*x = ListVolumes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_devmachine_v1_devmachine_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListVolumes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListVolumes) ProtoMessage() {}

func (x *ListVolumes) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_devmachine_v1_devmachine_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListVolumes.ProtoReflect.Descriptor instead.
func (*ListVolumes) Descriptor() ([]byte, []int) {
	return file_aistudio_devmachine_v1_devmachine_proto_rawDescGZIP(), []int{10}
}

func (x *ListVolumes) GetVolumes() []*Volume {
	if x != nil {
		return x.Volumes
	}
	return nil
}

type GetDevMachineExposePortsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName  string `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	DevMachineName string `protobuf:"bytes,2,opt,name=devMachineName,proto3" json:"devMachineName,omitempty"`
	Region         string `protobuf:"bytes,3,opt,name=region,proto3" json:"region,omitempty"`
}

func (x *GetDevMachineExposePortsRequest) Reset() {
	*x = GetDevMachineExposePortsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_devmachine_v1_devmachine_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDevMachineExposePortsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDevMachineExposePortsRequest) ProtoMessage() {}

func (x *GetDevMachineExposePortsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_devmachine_v1_devmachine_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDevMachineExposePortsRequest.ProtoReflect.Descriptor instead.
func (*GetDevMachineExposePortsRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_devmachine_v1_devmachine_proto_rawDescGZIP(), []int{11}
}

func (x *GetDevMachineExposePortsRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *GetDevMachineExposePortsRequest) GetDevMachineName() string {
	if x != nil {
		return x.DevMachineName
	}
	return ""
}

func (x *GetDevMachineExposePortsRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

type ListExposePortConfigs struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExposePortConfigs []*ListExposePortConfig `protobuf:"bytes,1,rep,name=exposePortConfigs,proto3" json:"exposePortConfigs,omitempty"`
}

func (x *ListExposePortConfigs) Reset() {
	*x = ListExposePortConfigs{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_devmachine_v1_devmachine_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListExposePortConfigs) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListExposePortConfigs) ProtoMessage() {}

func (x *ListExposePortConfigs) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_devmachine_v1_devmachine_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListExposePortConfigs.ProtoReflect.Descriptor instead.
func (*ListExposePortConfigs) Descriptor() ([]byte, []int) {
	return file_aistudio_devmachine_v1_devmachine_proto_rawDescGZIP(), []int{12}
}

func (x *ListExposePortConfigs) GetExposePortConfigs() []*ListExposePortConfig {
	if x != nil {
		return x.ExposePortConfigs
	}
	return nil
}

type Volume struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name       string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	MountPoint string `protobuf:"bytes,2,opt,name=mountPoint,proto3" json:"mountPoint,omitempty"`
	VolumeType string `protobuf:"bytes,3,opt,name=volumeType,proto3" json:"volumeType,omitempty"`
	SubPath    string `protobuf:"bytes,4,opt,name=subPath,proto3" json:"subPath,omitempty"`
}

func (x *Volume) Reset() {
	*x = Volume{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_devmachine_v1_devmachine_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Volume) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Volume) ProtoMessage() {}

func (x *Volume) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_devmachine_v1_devmachine_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Volume.ProtoReflect.Descriptor instead.
func (*Volume) Descriptor() ([]byte, []int) {
	return file_aistudio_devmachine_v1_devmachine_proto_rawDescGZIP(), []int{13}
}

func (x *Volume) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Volume) GetMountPoint() string {
	if x != nil {
		return x.MountPoint
	}
	return ""
}

func (x *Volume) GetVolumeType() string {
	if x != nil {
		return x.VolumeType
	}
	return ""
}

func (x *Volume) GetSubPath() string {
	if x != nil {
		return x.SubPath
	}
	return ""
}

type GetDevMachineRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName  string `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	DevMachineName string `protobuf:"bytes,2,opt,name=devMachineName,proto3" json:"devMachineName,omitempty"`
	Region         string `protobuf:"bytes,3,opt,name=region,proto3" json:"region,omitempty"`
}

func (x *GetDevMachineRequest) Reset() {
	*x = GetDevMachineRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_devmachine_v1_devmachine_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDevMachineRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDevMachineRequest) ProtoMessage() {}

func (x *GetDevMachineRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_devmachine_v1_devmachine_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDevMachineRequest.ProtoReflect.Descriptor instead.
func (*GetDevMachineRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_devmachine_v1_devmachine_proto_rawDescGZIP(), []int{14}
}

func (x *GetDevMachineRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *GetDevMachineRequest) GetDevMachineName() string {
	if x != nil {
		return x.DevMachineName
	}
	return ""
}

func (x *GetDevMachineRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

type GetDevMachineVolumesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName  string `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	DevMachineName string `protobuf:"bytes,2,opt,name=devMachineName,proto3" json:"devMachineName,omitempty"`
	Region         string `protobuf:"bytes,3,opt,name=region,proto3" json:"region,omitempty"`
}

func (x *GetDevMachineVolumesRequest) Reset() {
	*x = GetDevMachineVolumesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_devmachine_v1_devmachine_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDevMachineVolumesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDevMachineVolumesRequest) ProtoMessage() {}

func (x *GetDevMachineVolumesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_devmachine_v1_devmachine_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDevMachineVolumesRequest.ProtoReflect.Descriptor instead.
func (*GetDevMachineVolumesRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_devmachine_v1_devmachine_proto_rawDescGZIP(), []int{15}
}

func (x *GetDevMachineVolumesRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *GetDevMachineVolumesRequest) GetDevMachineName() string {
	if x != nil {
		return x.DevMachineName
	}
	return ""
}

func (x *GetDevMachineVolumesRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

type StartDevMachineRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName  string `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	DevMachineName string `protobuf:"bytes,2,opt,name=devMachineName,proto3" json:"devMachineName,omitempty"`
	Region         string `protobuf:"bytes,3,opt,name=region,proto3" json:"region,omitempty"`
}

func (x *StartDevMachineRequest) Reset() {
	*x = StartDevMachineRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_devmachine_v1_devmachine_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StartDevMachineRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartDevMachineRequest) ProtoMessage() {}

func (x *StartDevMachineRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_devmachine_v1_devmachine_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartDevMachineRequest.ProtoReflect.Descriptor instead.
func (*StartDevMachineRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_devmachine_v1_devmachine_proto_rawDescGZIP(), []int{16}
}

func (x *StartDevMachineRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *StartDevMachineRequest) GetDevMachineName() string {
	if x != nil {
		return x.DevMachineName
	}
	return ""
}

func (x *StartDevMachineRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

type StopDevMachineRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName  string `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	DevMachineName string `protobuf:"bytes,2,opt,name=devMachineName,proto3" json:"devMachineName,omitempty"`
	Region         string `protobuf:"bytes,3,opt,name=region,proto3" json:"region,omitempty"`
}

func (x *StopDevMachineRequest) Reset() {
	*x = StopDevMachineRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_devmachine_v1_devmachine_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StopDevMachineRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StopDevMachineRequest) ProtoMessage() {}

func (x *StopDevMachineRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_devmachine_v1_devmachine_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StopDevMachineRequest.ProtoReflect.Descriptor instead.
func (*StopDevMachineRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_devmachine_v1_devmachine_proto_rawDescGZIP(), []int{17}
}

func (x *StopDevMachineRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *StopDevMachineRequest) GetDevMachineName() string {
	if x != nil {
		return x.DevMachineName
	}
	return ""
}

func (x *StopDevMachineRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

type RestartDevMachineRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName  string `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	DevMachineName string `protobuf:"bytes,2,opt,name=devMachineName,proto3" json:"devMachineName,omitempty"`
	Region         string `protobuf:"bytes,3,opt,name=region,proto3" json:"region,omitempty"`
}

func (x *RestartDevMachineRequest) Reset() {
	*x = RestartDevMachineRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_devmachine_v1_devmachine_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RestartDevMachineRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RestartDevMachineRequest) ProtoMessage() {}

func (x *RestartDevMachineRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_devmachine_v1_devmachine_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RestartDevMachineRequest.ProtoReflect.Descriptor instead.
func (*RestartDevMachineRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_devmachine_v1_devmachine_proto_rawDescGZIP(), []int{18}
}

func (x *RestartDevMachineRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *RestartDevMachineRequest) GetDevMachineName() string {
	if x != nil {
		return x.DevMachineName
	}
	return ""
}

func (x *RestartDevMachineRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

type RemoveDevMachineRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName  string `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	DevMachineName string `protobuf:"bytes,2,opt,name=devMachineName,proto3" json:"devMachineName,omitempty"`
	Region         string `protobuf:"bytes,3,opt,name=region,proto3" json:"region,omitempty"`
}

func (x *RemoveDevMachineRequest) Reset() {
	*x = RemoveDevMachineRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_devmachine_v1_devmachine_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RemoveDevMachineRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoveDevMachineRequest) ProtoMessage() {}

func (x *RemoveDevMachineRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_devmachine_v1_devmachine_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoveDevMachineRequest.ProtoReflect.Descriptor instead.
func (*RemoveDevMachineRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_devmachine_v1_devmachine_proto_rawDescGZIP(), []int{19}
}

func (x *RemoveDevMachineRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *RemoveDevMachineRequest) GetDevMachineName() string {
	if x != nil {
		return x.DevMachineName
	}
	return ""
}

func (x *RemoveDevMachineRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

type ListDevMachinesOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName         string `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"` //工作空间的名字
	Creator               string `protobuf:"bytes,2,opt,name=creator,proto3" json:"creator,omitempty"`             //创建者
	State                 string `protobuf:"bytes,3,opt,name=state,proto3" json:"state,omitempty"`                 //状态
	Name                  string `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	QueueName             string `protobuf:"bytes,5,opt,name=queueName,proto3" json:"queueName,omitempty"`
	Region                string `protobuf:"bytes,6,opt,name=region,proto3" json:"region,omitempty"`
	Member                string `protobuf:"bytes,7,opt,name=member,proto3" json:"member,omitempty"`
	DisplayName           string `protobuf:"bytes,8,opt,name=displayName,proto3" json:"displayName,omitempty"`
	NodeIP                string `protobuf:"bytes,9,opt,name=nodeIP,proto3" json:"nodeIP,omitempty"`
	NodeSpecificationName string `protobuf:"bytes,12,opt,name=nodeSpecificationName,proto3" json:"nodeSpecificationName,omitempty"`
	Page                  int32  `protobuf:"varint,10,opt,name=page,proto3" json:"page,omitempty"`
	PageSize              int32  `protobuf:"varint,11,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
	Manager               string `protobuf:"bytes,13,opt,name=manager,proto3" json:"manager,omitempty"`
}

func (x *ListDevMachinesOptions) Reset() {
	*x = ListDevMachinesOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_devmachine_v1_devmachine_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListDevMachinesOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDevMachinesOptions) ProtoMessage() {}

func (x *ListDevMachinesOptions) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_devmachine_v1_devmachine_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDevMachinesOptions.ProtoReflect.Descriptor instead.
func (*ListDevMachinesOptions) Descriptor() ([]byte, []int) {
	return file_aistudio_devmachine_v1_devmachine_proto_rawDescGZIP(), []int{20}
}

func (x *ListDevMachinesOptions) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *ListDevMachinesOptions) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *ListDevMachinesOptions) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

func (x *ListDevMachinesOptions) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ListDevMachinesOptions) GetQueueName() string {
	if x != nil {
		return x.QueueName
	}
	return ""
}

func (x *ListDevMachinesOptions) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *ListDevMachinesOptions) GetMember() string {
	if x != nil {
		return x.Member
	}
	return ""
}

func (x *ListDevMachinesOptions) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *ListDevMachinesOptions) GetNodeIP() string {
	if x != nil {
		return x.NodeIP
	}
	return ""
}

func (x *ListDevMachinesOptions) GetNodeSpecificationName() string {
	if x != nil {
		return x.NodeSpecificationName
	}
	return ""
}

func (x *ListDevMachinesOptions) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListDevMachinesOptions) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListDevMachinesOptions) GetManager() string {
	if x != nil {
		return x.Manager
	}
	return ""
}

type ListDevMachinesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DevMachines []*DevMachine `protobuf:"bytes,1,rep,name=devMachines,proto3" json:"devMachines,omitempty"`
	Total       int64         `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
}

func (x *ListDevMachinesResponse) Reset() {
	*x = ListDevMachinesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_devmachine_v1_devmachine_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListDevMachinesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDevMachinesResponse) ProtoMessage() {}

func (x *ListDevMachinesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_devmachine_v1_devmachine_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDevMachinesResponse.ProtoReflect.Descriptor instead.
func (*ListDevMachinesResponse) Descriptor() ([]byte, []int) {
	return file_aistudio_devmachine_v1_devmachine_proto_rawDescGZIP(), []int{21}
}

func (x *ListDevMachinesResponse) GetDevMachines() []*DevMachine {
	if x != nil {
		return x.DevMachines
	}
	return nil
}

func (x *ListDevMachinesResponse) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

type CreateDevMachineResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DevMachineId   string `protobuf:"bytes,1,opt,name=devMachineId,proto3" json:"devMachineId,omitempty"`     //返回的DevMachine的ID
	DevMachineName string `protobuf:"bytes,2,opt,name=devMachineName,proto3" json:"devMachineName,omitempty"` //DevMachine的名字
	DisplayName    string `protobuf:"bytes,3,opt,name=displayName,proto3" json:"displayName,omitempty"`
}

func (x *CreateDevMachineResponse) Reset() {
	*x = CreateDevMachineResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_devmachine_v1_devmachine_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateDevMachineResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateDevMachineResponse) ProtoMessage() {}

func (x *CreateDevMachineResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_devmachine_v1_devmachine_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateDevMachineResponse.ProtoReflect.Descriptor instead.
func (*CreateDevMachineResponse) Descriptor() ([]byte, []int) {
	return file_aistudio_devmachine_v1_devmachine_proto_rawDescGZIP(), []int{22}
}

func (x *CreateDevMachineResponse) GetDevMachineId() string {
	if x != nil {
		return x.DevMachineId
	}
	return ""
}

func (x *CreateDevMachineResponse) GetDevMachineName() string {
	if x != nil {
		return x.DevMachineName
	}
	return ""
}

func (x *CreateDevMachineResponse) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

type UpdateDevMachineMembersRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DevMachineName string   `protobuf:"bytes,1,opt,name=devMachineName,proto3" json:"devMachineName,omitempty"`
	WorkspaceName  string   `protobuf:"bytes,2,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Members        []string `protobuf:"bytes,3,rep,name=members,proto3" json:"members,omitempty"`
	Managers       []string `protobuf:"bytes,4,rep,name=managers,proto3" json:"managers,omitempty"`
}

func (x *UpdateDevMachineMembersRequest) Reset() {
	*x = UpdateDevMachineMembersRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_devmachine_v1_devmachine_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateDevMachineMembersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateDevMachineMembersRequest) ProtoMessage() {}

func (x *UpdateDevMachineMembersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_devmachine_v1_devmachine_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateDevMachineMembersRequest.ProtoReflect.Descriptor instead.
func (*UpdateDevMachineMembersRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_devmachine_v1_devmachine_proto_rawDescGZIP(), []int{23}
}

func (x *UpdateDevMachineMembersRequest) GetDevMachineName() string {
	if x != nil {
		return x.DevMachineName
	}
	return ""
}

func (x *UpdateDevMachineMembersRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *UpdateDevMachineMembersRequest) GetMembers() []string {
	if x != nil {
		return x.Members
	}
	return nil
}

func (x *UpdateDevMachineMembersRequest) GetManagers() []string {
	if x != nil {
		return x.Managers
	}
	return nil
}

var File_aistudio_devmachine_v1_devmachine_proto protoreflect.FileDescriptor

var file_aistudio_devmachine_v1_devmachine_proto_rawDesc = []byte{
	0x0a, 0x27, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2f, 0x64, 0x65, 0x76, 0x6d, 0x61,
	0x63, 0x68, 0x69, 0x6e, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x65, 0x76, 0x6d, 0x61, 0x63, 0x68,
	0x69, 0x6e, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1b, 0x61, 0x70, 0x69, 0x73, 0x2e,
	0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x64, 0x65, 0x76, 0x6d, 0x61, 0x63, 0x68,
	0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x1a, 0x13, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70,
	0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65,
	0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x1f, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0xff, 0x09, 0x0a, 0x17, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x65, 0x76, 0x4d, 0x61,
	0x63, 0x68, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x20, 0x0a, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x29, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x64,
	0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x4d, 0x0a,
	0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x27, 0xfa, 0x42, 0x24, 0x72, 0x22, 0x10, 0x01, 0x18, 0x1f, 0x32,
	0x1c, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x5d, 0x28, 0x5b, 0x2d, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39,
	0x5d, 0x2a, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x29, 0x3f, 0x24, 0x52, 0x0d, 0x77,
	0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06,
	0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65,
	0x67, 0x69, 0x6f, 0x6e, 0x12, 0x58, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x06,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x40, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74,
	0x75, 0x64, 0x69, 0x6f, 0x2e, 0x64, 0x65, 0x76, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x65, 0x76, 0x4d, 0x61, 0x63, 0x68,
	0x69, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c,
	0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x12, 0x18,
	0x0a, 0x07, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x07, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x6d, 0x61, 0x6e, 0x61,
	0x67, 0x65, 0x72, 0x73, 0x18, 0x11, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x6d, 0x61, 0x6e, 0x61,
	0x67, 0x65, 0x72, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x27, 0x0a, 0x09, 0x71, 0x75,
	0x65, 0x75, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa,
	0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x1f, 0x52, 0x09, 0x71, 0x75, 0x65, 0x75, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x2e, 0x0a, 0x12, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x62, 0x6f, 0x61,
	0x72, 0x64, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x10, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x12, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x45, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x64, 0x12, 0x2d, 0x0a, 0x07, 0x65, 0x6e, 0x76, 0x56, 0x61, 0x72, 0x73, 0x18, 0x0a,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x45, 0x6e, 0x76, 0x56, 0x61, 0x72, 0x52, 0x07, 0x65, 0x6e, 0x76, 0x56, 0x61,
	0x72, 0x73, 0x12, 0x34, 0x0a, 0x15, 0x6e, 0x6f, 0x64, 0x65, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x15, 0x6e, 0x6f, 0x64, 0x65, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6e, 0x6f, 0x64, 0x65,
	0x49, 0x50, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6e, 0x6f, 0x64, 0x65, 0x49, 0x50,
	0x12, 0x40, 0x0a, 0x0d, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x0d, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x39, 0x0a, 0x0b, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x53, 0x70, 0x65, 0x63,
	0x73, 0x18, 0x0e, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x53, 0x70, 0x65, 0x63,
	0x52, 0x0b, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x53, 0x70, 0x65, 0x63, 0x73, 0x12, 0x45, 0x0a,
	0x0b, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x53, 0x70, 0x65, 0x63, 0x73, 0x18, 0x19, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64,
	0x69, 0x6f, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x53, 0x70, 0x65, 0x63, 0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x53,
	0x70, 0x65, 0x63, 0x73, 0x12, 0x2e, 0x0a, 0x12, 0x68, 0x6f, 0x73, 0x74, 0x4e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x12, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x12, 0x68, 0x6f, 0x73, 0x74, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x45, 0x6e, 0x61,
	0x62, 0x6c, 0x65, 0x64, 0x12, 0x26, 0x0a, 0x0e, 0x68, 0x6f, 0x73, 0x74, 0x49, 0x50, 0x43, 0x45,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x16, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x68, 0x6f,
	0x73, 0x74, 0x49, 0x50, 0x43, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x34, 0x0a, 0x15,
	0x70, 0x72, 0x69, 0x76, 0x69, 0x6c, 0x65, 0x67, 0x65, 0x64, 0x4d, 0x6f, 0x64, 0x65, 0x45, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x17, 0x20, 0x01, 0x28, 0x08, 0x52, 0x15, 0x70, 0x72, 0x69,
	0x76, 0x69, 0x6c, 0x65, 0x67, 0x65, 0x64, 0x4d, 0x6f, 0x64, 0x65, 0x45, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x64, 0x12, 0x44, 0x0a, 0x09, 0x73, 0x73, 0x68, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18,
	0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73,
	0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x64, 0x65, 0x76, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x53, 0x48, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x09, 0x73,
	0x73, 0x68, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x5b, 0x0a, 0x11, 0x65, 0x78, 0x70, 0x6f,
	0x73, 0x65, 0x50, 0x6f, 0x72, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x18, 0x13, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75,
	0x64, 0x69, 0x6f, 0x2e, 0x64, 0x65, 0x76, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x73, 0x65, 0x50, 0x6f, 0x72, 0x74, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x52, 0x11, 0x65, 0x78, 0x70, 0x6f, 0x73, 0x65, 0x50, 0x6f, 0x72, 0x74, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x73, 0x12, 0x24, 0x0a, 0x0d, 0x68, 0x61, 0x64, 0x6f, 0x6f, 0x70, 0x45,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x14, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x68, 0x61,
	0x64, 0x6f, 0x6f, 0x70, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x68,
	0x61, 0x64, 0x6f, 0x6f, 0x70, 0x55, 0x73, 0x65, 0x72, 0x73, 0x18, 0x15, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x0b, 0x68, 0x61, 0x64, 0x6f, 0x6f, 0x70, 0x55, 0x73, 0x65, 0x72, 0x73, 0x12, 0x28, 0x0a,
	0x0f, 0x67, 0x70, 0x75, 0x53, 0x68, 0x61, 0x72, 0x65, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64,
	0x18, 0x18, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x67, 0x70, 0x75, 0x53, 0x68, 0x61, 0x72, 0x65,
	0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x1a, 0x39, 0x0a, 0x0b, 0x4c, 0x61, 0x62, 0x65, 0x6c,
	0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02,
	0x38, 0x01, 0x22, 0x95, 0x01, 0x0a, 0x10, 0x45, 0x78, 0x70, 0x6f, 0x73, 0x65, 0x50, 0x6f, 0x72,
	0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x45, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x63, 0x6f, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x61, 0x70, 0x69, 0x73,
	0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x64, 0x65, 0x76, 0x6d, 0x61, 0x63,
	0x68, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x12, 0x1e,
	0x0a, 0x0a, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x50, 0x6f, 0x72, 0x74, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0a, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x50, 0x6f, 0x72, 0x74, 0x12, 0x1a,
	0x0a, 0x08, 0x6e, 0x6f, 0x64, 0x65, 0x50, 0x6f, 0x72, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x08, 0x6e, 0x6f, 0x64, 0x65, 0x50, 0x6f, 0x72, 0x74, 0x22, 0xe9, 0x01, 0x0a, 0x14, 0x4c,
	0x69, 0x73, 0x74, 0x45, 0x78, 0x70, 0x6f, 0x73, 0x65, 0x50, 0x6f, 0x72, 0x74, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x45, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x63, 0x6f, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x61, 0x70, 0x69, 0x73,
	0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x64, 0x65, 0x76, 0x6d, 0x61, 0x63,
	0x68, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x12, 0x1e,
	0x0a, 0x0a, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x50, 0x6f, 0x72, 0x74, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0a, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x50, 0x6f, 0x72, 0x74, 0x12, 0x1a,
	0x0a, 0x08, 0x6e, 0x6f, 0x64, 0x65, 0x50, 0x6f, 0x72, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x08, 0x6e, 0x6f, 0x64, 0x65, 0x50, 0x6f, 0x72, 0x74, 0x12, 0x22, 0x0a, 0x0c, 0x69, 0x73,
	0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x50, 0x6f, 0x72, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x0c, 0x69, 0x73, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x50, 0x6f, 0x72, 0x74, 0x12, 0x16,
	0x0a, 0x06, 0x6e, 0x6f, 0x64, 0x65, 0x49, 0x50, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x6e, 0x6f, 0x64, 0x65, 0x49, 0x50, 0x22, 0x7f, 0x0a, 0x09, 0x53, 0x53, 0x48, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x12, 0x1c, 0x0a, 0x09, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x53, 0x48,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x53,
	0x48, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x73, 0x68, 0x55, 0x73, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x73, 0x73, 0x68, 0x55, 0x73, 0x65, 0x72, 0x12, 0x20, 0x0a, 0x0b, 0x73,
	0x73, 0x68, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x73, 0x73, 0x68, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x18, 0x0a,
	0x07, 0x73, 0x73, 0x68, 0x4b, 0x65, 0x79, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07,
	0x73, 0x73, 0x68, 0x4b, 0x65, 0x79, 0x73, 0x22, 0xa5, 0x0b, 0x0a, 0x0a, 0x44, 0x65, 0x76, 0x4d,
	0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x69,
	0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b,
	0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x24,
	0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x4b, 0x0a, 0x06,
	0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x61,
	0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x64, 0x65, 0x76,
	0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x76, 0x4d, 0x61,
	0x63, 0x68, 0x69, 0x6e, 0x65, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x6d,
	0x62, 0x65, 0x72, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x6d, 0x62,
	0x65, 0x72, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x17,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x14, 0x0a,
	0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x69, 0x6d,
	0x61, 0x67, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x71, 0x75, 0x65, 0x75, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x71, 0x75, 0x65, 0x75, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x2d, 0x0a, 0x07, 0x65, 0x6e, 0x76, 0x56, 0x61, 0x72, 0x73, 0x18, 0x0b, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x13, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x45, 0x6e, 0x76, 0x56, 0x61, 0x72, 0x52, 0x07, 0x65, 0x6e, 0x76, 0x56, 0x61, 0x72, 0x73,
	0x12, 0x40, 0x0a, 0x0d, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x0d, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x34, 0x0a, 0x15, 0x6e, 0x6f, 0x64, 0x65, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x15, 0x6e, 0x6f, 0x64, 0x65, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6e, 0x6f, 0x64, 0x65,
	0x49, 0x50, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6e, 0x6f, 0x64, 0x65, 0x49, 0x50,
	0x12, 0x2e, 0x0a, 0x12, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x45,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x15, 0x20, 0x01, 0x28, 0x08, 0x52, 0x12, 0x74, 0x65,
	0x6e, 0x73, 0x6f, 0x72, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64,
	0x12, 0x39, 0x0a, 0x0b, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x53, 0x70, 0x65, 0x63, 0x73, 0x18,
	0x0f, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x53, 0x70, 0x65, 0x63, 0x52, 0x0b,
	0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x53, 0x70, 0x65, 0x63, 0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x69,
	0x73, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x18, 0x16, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09,
	0x69, 0x73, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x6d, 0x61, 0x6e,
	0x61, 0x67, 0x65, 0x72, 0x73, 0x18, 0x18, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x6d, 0x61, 0x6e,
	0x61, 0x67, 0x65, 0x72, 0x73, 0x12, 0x2e, 0x0a, 0x12, 0x68, 0x6f, 0x73, 0x74, 0x4e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x13, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x12, 0x68, 0x6f, 0x73, 0x74, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x45, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x26, 0x0a, 0x0e, 0x68, 0x6f, 0x73, 0x74, 0x49, 0x50, 0x43,
	0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x68,
	0x6f, 0x73, 0x74, 0x49, 0x50, 0x43, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x34, 0x0a,
	0x15, 0x70, 0x72, 0x69, 0x76, 0x69, 0x6c, 0x65, 0x67, 0x65, 0x64, 0x4d, 0x6f, 0x64, 0x65, 0x45,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x08, 0x52, 0x15, 0x70, 0x72,
	0x69, 0x76, 0x69, 0x6c, 0x65, 0x67, 0x65, 0x64, 0x4d, 0x6f, 0x64, 0x65, 0x45, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x64, 0x12, 0x44, 0x0a, 0x09, 0x73, 0x73, 0x68, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x18, 0x10, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69,
	0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x64, 0x65, 0x76, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x53, 0x48, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x09,
	0x73, 0x73, 0x68, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x5b, 0x0a, 0x11, 0x65, 0x78, 0x70,
	0x6f, 0x73, 0x65, 0x50, 0x6f, 0x72, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x18, 0x19,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74,
	0x75, 0x64, 0x69, 0x6f, 0x2e, 0x64, 0x65, 0x76, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x2e,
	0x76, 0x31, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x73, 0x65, 0x50, 0x6f, 0x72, 0x74, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x52, 0x11, 0x65, 0x78, 0x70, 0x6f, 0x73, 0x65, 0x50, 0x6f, 0x72, 0x74, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x12, 0x49, 0x0a, 0x08, 0x64, 0x6d, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e,
	0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x64, 0x65, 0x76, 0x6d, 0x61, 0x63, 0x68,
	0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x76, 0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e,
	0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x08, 0x64, 0x6d, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x28, 0x0a, 0x0f, 0x67, 0x70, 0x75, 0x53, 0x68, 0x61, 0x72, 0x65, 0x45, 0x6e, 0x61,
	0x62, 0x6c, 0x65, 0x64, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x67, 0x70, 0x75, 0x53,
	0x68, 0x61, 0x72, 0x65, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x28, 0x0a, 0x0f, 0x69,
	0x73, 0x42, 0x75, 0x69, 0x6c, 0x64, 0x69, 0x6e, 0x67, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x1d,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x69, 0x73, 0x42, 0x75, 0x69, 0x6c, 0x64, 0x69, 0x6e, 0x67,
	0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x45, 0x0a, 0x0b, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x53,
	0x70, 0x65, 0x63, 0x73, 0x18, 0x1e, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x61, 0x70, 0x69,
	0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x53, 0x70, 0x65, 0x63, 0x52,
	0x0b, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x53, 0x70, 0x65, 0x63, 0x73, 0x12, 0x24, 0x0a, 0x0d,
	0x68, 0x61, 0x64, 0x6f, 0x6f, 0x70, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x1f, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0d, 0x68, 0x61, 0x64, 0x6f, 0x6f, 0x70, 0x45, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x68, 0x61, 0x64, 0x6f, 0x6f, 0x70, 0x55, 0x73, 0x65, 0x72,
	0x73, 0x18, 0x20, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x68, 0x61, 0x64, 0x6f, 0x6f, 0x70, 0x55,
	0x73, 0x65, 0x72, 0x73, 0x1a, 0x39, 0x0a, 0x0b, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22,
	0xa2, 0x04, 0x0a, 0x10, 0x44, 0x65, 0x76, 0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x2e, 0x0a, 0x12,
	0x6c, 0x61, 0x73, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x69,
	0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x6c, 0x61, 0x73, 0x74, 0x54, 0x72,
	0x61, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x28, 0x0a, 0x0f,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x28, 0x0a, 0x0f, 0x72, 0x75, 0x6e, 0x6e, 0x69, 0x6e,
	0x67, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0f, 0x72, 0x75, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x20, 0x0a, 0x0b, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6e, 0x6f, 0x64, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x6f, 0x64, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16,
	0x0a, 0x06, 0x6e, 0x6f, 0x64, 0x65, 0x49, 0x50, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x6e, 0x6f, 0x64, 0x65, 0x49, 0x50, 0x12, 0x34, 0x0a, 0x15, 0x6e, 0x6f, 0x64, 0x65, 0x53, 0x70,
	0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x6e, 0x6f, 0x64, 0x65, 0x53, 0x70, 0x65, 0x63, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b,
	0x76, 0x73, 0x63, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x61, 0x64, 0x79, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x0b, 0x76, 0x73, 0x63, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x61, 0x64, 0x79, 0x12, 0x22,
	0x0a, 0x0c, 0x6a, 0x75, 0x70, 0x79, 0x74, 0x65, 0x72, 0x52, 0x65, 0x61, 0x64, 0x79, 0x18, 0x0d,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x6a, 0x75, 0x70, 0x79, 0x74, 0x65, 0x72, 0x52, 0x65, 0x61,
	0x64, 0x79, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x73, 0x68, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64,
	0x18, 0x0e, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x73, 0x73, 0x68, 0x45, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x73, 0x68, 0x50, 0x6f, 0x72, 0x74, 0x18, 0x0f, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x07, 0x73, 0x73, 0x68, 0x50, 0x6f, 0x72, 0x74, 0x12, 0x18, 0x0a, 0x07,
	0x70, 0x6f, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70,
	0x6f, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x22, 0x93, 0x0a, 0x0a, 0x17, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44,
	0x65, 0x76, 0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x26, 0x0a, 0x0e, 0x64, 0x65, 0x76, 0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x64, 0x65, 0x76, 0x4d, 0x61, 0x63,
	0x68, 0x69, 0x6e, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x69, 0x73, 0x70,
	0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64,
	0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x29, 0x0a, 0x0b, 0x64, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x64, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x4d, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x27, 0xfa, 0x42,
	0x24, 0x72, 0x22, 0x10, 0x01, 0x18, 0x1f, 0x32, 0x1c, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x5d, 0x28,
	0x5b, 0x2d, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x2a, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d,
	0x39, 0x5d, 0x29, 0x3f, 0x24, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x58, 0x0a, 0x06,
	0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x40, 0x2e, 0x61,
	0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x64, 0x65, 0x76,
	0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x44, 0x65, 0x76, 0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06,
	0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72,
	0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73,
	0x12, 0x1a, 0x0a, 0x08, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x73, 0x18, 0x11, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x08, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x73, 0x12, 0x14, 0x0a, 0x05,
	0x69, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x69, 0x6d, 0x61,
	0x67, 0x65, 0x12, 0x27, 0x0a, 0x09, 0x71, 0x75, 0x65, 0x75, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x1f,
	0x52, 0x09, 0x71, 0x75, 0x65, 0x75, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2e, 0x0a, 0x12, 0x74,
	0x65, 0x6e, 0x73, 0x6f, 0x72, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65,
	0x64, 0x18, 0x10, 0x20, 0x01, 0x28, 0x08, 0x52, 0x12, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x62,
	0x6f, 0x61, 0x72, 0x64, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x2d, 0x0a, 0x07, 0x65,
	0x6e, 0x76, 0x56, 0x61, 0x72, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x61,
	0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x6e, 0x76, 0x56, 0x61,
	0x72, 0x52, 0x07, 0x65, 0x6e, 0x76, 0x56, 0x61, 0x72, 0x73, 0x12, 0x40, 0x0a, 0x0d, 0x73, 0x70,
	0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0d, 0x73,
	0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x34, 0x0a, 0x15,
	0x6e, 0x6f, 0x64, 0x65, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x6e, 0x6f, 0x64,
	0x65, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6e, 0x6f, 0x64, 0x65, 0x49, 0x50, 0x18, 0x0d, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x6e, 0x6f, 0x64, 0x65, 0x49, 0x50, 0x12, 0x39, 0x0a, 0x0b, 0x76, 0x6f,
	0x6c, 0x75, 0x6d, 0x65, 0x53, 0x70, 0x65, 0x63, 0x73, 0x18, 0x0e, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x17, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x6f,
	0x6c, 0x75, 0x6d, 0x65, 0x53, 0x70, 0x65, 0x63, 0x52, 0x0b, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65,
	0x53, 0x70, 0x65, 0x63, 0x73, 0x12, 0x2e, 0x0a, 0x12, 0x68, 0x6f, 0x73, 0x74, 0x4e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x12, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x12, 0x68, 0x6f, 0x73, 0x74, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x45, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x26, 0x0a, 0x0e, 0x68, 0x6f, 0x73, 0x74, 0x49, 0x50, 0x43,
	0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x68,
	0x6f, 0x73, 0x74, 0x49, 0x50, 0x43, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x34, 0x0a,
	0x15, 0x70, 0x72, 0x69, 0x76, 0x69, 0x6c, 0x65, 0x67, 0x65, 0x64, 0x4d, 0x6f, 0x64, 0x65, 0x45,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x08, 0x52, 0x15, 0x70, 0x72,
	0x69, 0x76, 0x69, 0x6c, 0x65, 0x67, 0x65, 0x64, 0x4d, 0x6f, 0x64, 0x65, 0x45, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x64, 0x12, 0x44, 0x0a, 0x09, 0x73, 0x73, 0x68, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69,
	0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x64, 0x65, 0x76, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x53, 0x48, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x09,
	0x73, 0x73, 0x68, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x24, 0x0a, 0x0d, 0x68, 0x61, 0x64,
	0x6f, 0x6f, 0x70, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x0d, 0x68, 0x61, 0x64, 0x6f, 0x6f, 0x70, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12,
	0x20, 0x0a, 0x0b, 0x68, 0x61, 0x64, 0x6f, 0x6f, 0x70, 0x55, 0x73, 0x65, 0x72, 0x73, 0x18, 0x1b,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x68, 0x61, 0x64, 0x6f, 0x6f, 0x70, 0x55, 0x73, 0x65, 0x72,
	0x73, 0x12, 0x5b, 0x0a, 0x11, 0x65, 0x78, 0x70, 0x6f, 0x73, 0x65, 0x50, 0x6f, 0x72, 0x74, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x18, 0x19, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x61,
	0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x64, 0x65, 0x76,
	0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x73,
	0x65, 0x50, 0x6f, 0x72, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x11, 0x65, 0x78, 0x70,
	0x6f, 0x73, 0x65, 0x50, 0x6f, 0x72, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x12, 0x28,
	0x0a, 0x0f, 0x67, 0x70, 0x75, 0x53, 0x68, 0x61, 0x72, 0x65, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65,
	0x64, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x67, 0x70, 0x75, 0x53, 0x68, 0x61, 0x72,
	0x65, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x45, 0x0a, 0x0b, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x53, 0x70, 0x65, 0x63, 0x73, 0x18, 0x1f, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e,
	0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x53, 0x70,
	0x65, 0x63, 0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x53, 0x70, 0x65, 0x63, 0x73, 0x1a,
	0x39, 0x0a, 0x0b, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x68, 0x0a, 0x18, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77,
	0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x26, 0x0a, 0x0e,
	0x64, 0x65, 0x76, 0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x64, 0x65, 0x76, 0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x22, 0x65, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x54, 0x65, 0x6e, 0x73, 0x6f,
	0x72, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a,
	0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x64, 0x65, 0x76, 0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x64, 0x65, 0x76,
	0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x40, 0x0a, 0x16, 0x47,
	0x65, 0x74, 0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x22, 0x4c, 0x0a,
	0x0b, 0x4c, 0x69, 0x73, 0x74, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x73, 0x12, 0x3d, 0x0a, 0x07,
	0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e,
	0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x64, 0x65,
	0x76, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x6f, 0x6c, 0x75,
	0x6d, 0x65, 0x52, 0x07, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x73, 0x22, 0x87, 0x01, 0x0a, 0x1f,
	0x47, 0x65, 0x74, 0x44, 0x65, 0x76, 0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x45, 0x78, 0x70,
	0x6f, 0x73, 0x65, 0x50, 0x6f, 0x72, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x64, 0x65, 0x76, 0x4d, 0x61, 0x63, 0x68,
	0x69, 0x6e, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x64,
	0x65, 0x76, 0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a,
	0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72,
	0x65, 0x67, 0x69, 0x6f, 0x6e, 0x22, 0x78, 0x0a, 0x15, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x78, 0x70,
	0x6f, 0x73, 0x65, 0x50, 0x6f, 0x72, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x12, 0x5f,
	0x0a, 0x11, 0x65, 0x78, 0x70, 0x6f, 0x73, 0x65, 0x50, 0x6f, 0x72, 0x74, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x61, 0x70, 0x69, 0x73,
	0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x64, 0x65, 0x76, 0x6d, 0x61, 0x63,
	0x68, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x78, 0x70, 0x6f,
	0x73, 0x65, 0x50, 0x6f, 0x72, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x11, 0x65, 0x78,
	0x70, 0x6f, 0x73, 0x65, 0x50, 0x6f, 0x72, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x22,
	0x76, 0x0a, 0x06, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1e, 0x0a,
	0x0a, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x12, 0x1e, 0x0a,
	0x0a, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a,
	0x07, 0x73, 0x75, 0x62, 0x50, 0x61, 0x74, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x73, 0x75, 0x62, 0x50, 0x61, 0x74, 0x68, 0x22, 0x7c, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x44, 0x65,
	0x76, 0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x64, 0x65, 0x76, 0x4d, 0x61, 0x63, 0x68,
	0x69, 0x6e, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x64,
	0x65, 0x76, 0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a,
	0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72,
	0x65, 0x67, 0x69, 0x6f, 0x6e, 0x22, 0x83, 0x01, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x44, 0x65, 0x76,
	0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f,
	0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x64,
	0x65, 0x76, 0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0e, 0x64, 0x65, 0x76, 0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x22, 0x7e, 0x0a, 0x16, 0x53,
	0x74, 0x61, 0x72, 0x74, 0x44, 0x65, 0x76, 0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f,
	0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x64,
	0x65, 0x76, 0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0e, 0x64, 0x65, 0x76, 0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x22, 0x7d, 0x0a, 0x15, 0x53,
	0x74, 0x6f, 0x70, 0x44, 0x65, 0x76, 0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72,
	0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x64, 0x65,
	0x76, 0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0e, 0x64, 0x65, 0x76, 0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x22, 0x80, 0x01, 0x0a, 0x18, 0x52,
	0x65, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44, 0x65, 0x76, 0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x26, 0x0a,
	0x0e, 0x64, 0x65, 0x76, 0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x64, 0x65, 0x76, 0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x22, 0x7f, 0x0a,
	0x17, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x44, 0x65, 0x76, 0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x26,
	0x0a, 0x0e, 0x64, 0x65, 0x76, 0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x64, 0x65, 0x76, 0x4d, 0x61, 0x63, 0x68, 0x69,
	0x6e, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x22, 0x8a,
	0x03, 0x0a, 0x16, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x65, 0x76, 0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e,
	0x65, 0x73, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72,
	0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x18, 0x0a, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61,
	0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x71, 0x75, 0x65, 0x75, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x71, 0x75, 0x65, 0x75, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x65, 0x6d,
	0x62, 0x65, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x65, 0x6d, 0x62, 0x65,
	0x72, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6e, 0x6f, 0x64, 0x65, 0x49, 0x50, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x6e, 0x6f, 0x64, 0x65, 0x49, 0x50, 0x12, 0x34, 0x0a, 0x15, 0x6e,
	0x6f, 0x64, 0x65, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x6e, 0x6f, 0x64, 0x65,
	0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a,
	0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a,
	0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x18, 0x0d, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x22, 0x7a, 0x0a, 0x17, 0x4c,
	0x69, 0x73, 0x74, 0x44, 0x65, 0x76, 0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x49, 0x0a, 0x0b, 0x64, 0x65, 0x76, 0x4d, 0x61, 0x63,
	0x68, 0x69, 0x6e, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x61, 0x70,
	0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x64, 0x65, 0x76, 0x6d,
	0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x76, 0x4d, 0x61, 0x63,
	0x68, 0x69, 0x6e, 0x65, 0x52, 0x0b, 0x64, 0x65, 0x76, 0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65,
	0x73, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x22, 0x88, 0x01, 0x0a, 0x18, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x44, 0x65, 0x76, 0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x64, 0x65, 0x76, 0x4d, 0x61, 0x63, 0x68, 0x69,
	0x6e, 0x65, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x64, 0x65, 0x76, 0x4d,
	0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0e, 0x64, 0x65, 0x76, 0x4d,
	0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0e, 0x64, 0x65, 0x76, 0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x20, 0x0a, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61,
	0x6d, 0x65, 0x22, 0xa4, 0x01, 0x0a, 0x1e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x65, 0x76,
	0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0e, 0x64, 0x65, 0x76, 0x4d, 0x61, 0x63, 0x68,
	0x69, 0x6e, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x64,
	0x65, 0x76, 0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x24, 0x0a,
	0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x12, 0x1a, 0x0a,
	0x08, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x08, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x73, 0x2a, 0x2a, 0x0a, 0x0c, 0x50, 0x72, 0x6f,
	0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x07, 0x0a, 0x03, 0x54, 0x43, 0x50,
	0x10, 0x00, 0x12, 0x07, 0x0a, 0x03, 0x55, 0x44, 0x50, 0x10, 0x01, 0x12, 0x08, 0x0a, 0x04, 0x53,
	0x43, 0x54, 0x50, 0x10, 0x02, 0x32, 0x9e, 0x12, 0x0a, 0x11, 0x44, 0x65, 0x76, 0x4d, 0x61, 0x63,
	0x68, 0x69, 0x6e, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0xc2, 0x01, 0x0a, 0x18,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x65, 0x76, 0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65,
	0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x34, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e,
	0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x64, 0x65, 0x76, 0x6d, 0x61, 0x63, 0x68,
	0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x65, 0x76,
	0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x35,
	0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x64,
	0x65, 0x76, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x44, 0x65, 0x76, 0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x39, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x33, 0x3a, 0x01, 0x2a,
	0x22, 0x2e, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x2f, 0x7b, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x64, 0x65, 0x76, 0x2d, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65,
	0x12, 0xc1, 0x01, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x44, 0x65, 0x76, 0x4d, 0x61, 0x63, 0x68, 0x69,
	0x6e, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x31, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e,
	0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x64, 0x65, 0x76, 0x6d, 0x61, 0x63, 0x68,
	0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x65, 0x76, 0x4d, 0x61, 0x63,
	0x68, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x61, 0x70,
	0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x64, 0x65, 0x76, 0x6d,
	0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x76, 0x4d, 0x61, 0x63,
	0x68, 0x69, 0x6e, 0x65, 0x22, 0x4e, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x48, 0x12, 0x46, 0x2f, 0x61,
	0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x2f, 0x7b, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d,
	0x2f, 0x64, 0x65, 0x76, 0x2d, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x2f, 0x7b, 0x64, 0x65,
	0x76, 0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x64, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x12, 0xb5, 0x01, 0x0a, 0x0f, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x65, 0x76,
	0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x73, 0x12, 0x33, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e,
	0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x64, 0x65, 0x76, 0x6d, 0x61, 0x63, 0x68,
	0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x65, 0x76, 0x4d, 0x61,
	0x63, 0x68, 0x69, 0x6e, 0x65, 0x73, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x1a, 0x34, 0x2e,
	0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x64, 0x65,
	0x76, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74,
	0x44, 0x65, 0x76, 0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x37, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x31, 0x12, 0x2f, 0x2f, 0x61, 0x70,
	0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2f,
	0x7b, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f,
	0x64, 0x65, 0x76, 0x2d, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x73, 0x12, 0xac, 0x01, 0x0a,
	0x10, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x65, 0x76, 0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e,
	0x65, 0x12, 0x34, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69,
	0x6f, 0x2e, 0x64, 0x65, 0x76, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x65, 0x76, 0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22,
	0x4a, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x44, 0x3a, 0x01, 0x2a, 0x1a, 0x3f, 0x2f, 0x61, 0x70, 0x69,
	0x73, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2f, 0x7b,
	0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x64,
	0x65, 0x76, 0x2d, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x2f, 0x7b, 0x64, 0x65, 0x76, 0x4d,
	0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x12, 0xb6, 0x01, 0x0a, 0x11,
	0x52, 0x65, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44, 0x65, 0x76, 0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e,
	0x65, 0x12, 0x35, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69,
	0x6f, 0x2e, 0x64, 0x65, 0x76, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e,
	0x52, 0x65, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44, 0x65, 0x76, 0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79,
	0x22, 0x52, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x4c, 0x3a, 0x01, 0x2a, 0x22, 0x47, 0x2f, 0x61, 0x70,
	0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2f,
	0x7b, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f,
	0x64, 0x65, 0x76, 0x2d, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x2f, 0x7b, 0x64, 0x65, 0x76,
	0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x12, 0xad, 0x01, 0x0a, 0x0e, 0x53, 0x74, 0x6f, 0x70, 0x44, 0x65, 0x76,
	0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x12, 0x32, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61,
	0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x64, 0x65, 0x76, 0x6d, 0x61, 0x63, 0x68, 0x69,
	0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x6f, 0x70, 0x44, 0x65, 0x76, 0x4d, 0x61, 0x63,
	0x68, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x22, 0x4f, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x49, 0x3a, 0x01, 0x2a, 0x22, 0x44,
	0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x2f, 0x7b, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x7d, 0x2f, 0x64, 0x65, 0x76, 0x2d, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x2f, 0x7b,
	0x64, 0x65, 0x76, 0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f,
	0x73, 0x74, 0x6f, 0x70, 0x12, 0xa9, 0x01, 0x0a, 0x10, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x44,
	0x65, 0x76, 0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x12, 0x34, 0x2e, 0x61, 0x70, 0x69, 0x73,
	0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x64, 0x65, 0x76, 0x6d, 0x61, 0x63,
	0x68, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x44, 0x65,
	0x76, 0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x47, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x41, 0x2a,
	0x3f, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x2f, 0x7b, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x7d, 0x2f, 0x64, 0x65, 0x76, 0x2d, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x2f,
	0x7b, 0x64, 0x65, 0x76, 0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d,
	0x12, 0xcb, 0x01, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x44, 0x65, 0x76, 0x4d, 0x61, 0x63, 0x68, 0x69,
	0x6e, 0x65, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x73, 0x12, 0x38, 0x2e, 0x61, 0x70, 0x69, 0x73,
	0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x64, 0x65, 0x76, 0x6d, 0x61, 0x63,
	0x68, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x65, 0x76, 0x4d, 0x61,
	0x63, 0x68, 0x69, 0x6e, 0x65, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75,
	0x64, 0x69, 0x6f, 0x2e, 0x64, 0x65, 0x76, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x73, 0x22, 0x4f, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x49, 0x12, 0x47, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f,
	0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2f, 0x7b, 0x77, 0x6f, 0x72, 0x6b, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x64, 0x65, 0x76, 0x2d, 0x6d, 0x61,
	0x63, 0x68, 0x69, 0x6e, 0x65, 0x2f, 0x7b, 0x64, 0x65, 0x76, 0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x73, 0x12, 0xe2,
	0x01, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x44, 0x65, 0x76, 0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65,
	0x45, 0x78, 0x70, 0x6f, 0x73, 0x65, 0x50, 0x6f, 0x72, 0x74, 0x73, 0x12, 0x3c, 0x2e, 0x61, 0x70,
	0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x64, 0x65, 0x76, 0x6d,
	0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x65, 0x76,
	0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x45, 0x78, 0x70, 0x6f, 0x73, 0x65, 0x50, 0x6f, 0x72,
	0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x32, 0x2e, 0x61, 0x70, 0x69, 0x73,
	0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x64, 0x65, 0x76, 0x6d, 0x61, 0x63,
	0x68, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x78, 0x70, 0x6f,
	0x73, 0x65, 0x50, 0x6f, 0x72, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x22, 0x54, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x4e, 0x12, 0x4c, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f,
	0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2f, 0x7b, 0x77, 0x6f, 0x72, 0x6b, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x64, 0x65, 0x76, 0x2d, 0x6d, 0x61,
	0x63, 0x68, 0x69, 0x6e, 0x65, 0x2f, 0x7b, 0x64, 0x65, 0x76, 0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x65, 0x78, 0x70, 0x6f, 0x73, 0x65, 0x2d, 0x70, 0x6f,
	0x72, 0x74, 0x73, 0x12, 0xba, 0x01, 0x0a, 0x11, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x65,
	0x6e, 0x73, 0x6f, 0x72, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x12, 0x35, 0x2e, 0x61, 0x70, 0x69, 0x73,
	0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x64, 0x65, 0x76, 0x6d, 0x61, 0x63,
	0x68, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x65,
	0x6e, 0x73, 0x6f, 0x72, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x56, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x50,
	0x3a, 0x01, 0x2a, 0x22, 0x4b, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x6f,
	0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2f, 0x7b, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x64, 0x65, 0x76, 0x2d, 0x6d, 0x61, 0x63, 0x68,
	0x69, 0x6e, 0x65, 0x2f, 0x7b, 0x64, 0x65, 0x76, 0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x62, 0x6f, 0x61, 0x72, 0x64,
	0x12, 0xce, 0x01, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x62, 0x6f,
	0x61, 0x72, 0x64, 0x12, 0x32, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75,
	0x64, 0x69, 0x6f, 0x2e, 0x64, 0x65, 0x76, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x62, 0x6f, 0x61, 0x72, 0x64,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x33, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61,
	0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x64, 0x65, 0x76, 0x6d, 0x61, 0x63, 0x68, 0x69,
	0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x62,
	0x6f, 0x61, 0x72, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x53, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x4d, 0x12, 0x4b, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x77,
	0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2f, 0x7b, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x64, 0x65, 0x76, 0x2d, 0x6d, 0x61, 0x63,
	0x68, 0x69, 0x6e, 0x65, 0x2f, 0x7b, 0x64, 0x65, 0x76, 0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x62, 0x6f, 0x61, 0x72,
	0x64, 0x12, 0xc2, 0x01, 0x0a, 0x17, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x65, 0x76, 0x4d,
	0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x12, 0x3b, 0x2e,
	0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x64, 0x65,
	0x76, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x44, 0x65, 0x76, 0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x4d, 0x65, 0x6d, 0x62,
	0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70,
	0x74, 0x79, 0x22, 0x52, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x4c, 0x3a, 0x01, 0x2a, 0x1a, 0x47, 0x2f,
	0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x2f, 0x7b, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x7d, 0x2f, 0x64, 0x65, 0x76, 0x2d, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x2f, 0x7b, 0x64,
	0x65, 0x76, 0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x6d,
	0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x42, 0x4d, 0x5a, 0x4b, 0x67, 0x69, 0x74, 0x2e, 0x6c, 0x69,
	0x61, 0x6e, 0x6a, 0x69, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6e,
	0x61, 0x74, 0x69, 0x76, 0x65, 0x2f, 0x6b, 0x69, 0x63, 0x2f, 0x6b, 0x69, 0x63, 0x2d, 0x70, 0x6c,
	0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x61, 0x69, 0x73, 0x74,
	0x75, 0x64, 0x69, 0x6f, 0x2f, 0x64, 0x65, 0x76, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x2f,
	0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_aistudio_devmachine_v1_devmachine_proto_rawDescOnce sync.Once
	file_aistudio_devmachine_v1_devmachine_proto_rawDescData = file_aistudio_devmachine_v1_devmachine_proto_rawDesc
)

func file_aistudio_devmachine_v1_devmachine_proto_rawDescGZIP() []byte {
	file_aistudio_devmachine_v1_devmachine_proto_rawDescOnce.Do(func() {
		file_aistudio_devmachine_v1_devmachine_proto_rawDescData = protoimpl.X.CompressGZIP(file_aistudio_devmachine_v1_devmachine_proto_rawDescData)
	})
	return file_aistudio_devmachine_v1_devmachine_proto_rawDescData
}

var file_aistudio_devmachine_v1_devmachine_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_aistudio_devmachine_v1_devmachine_proto_msgTypes = make([]protoimpl.MessageInfo, 27)
var file_aistudio_devmachine_v1_devmachine_proto_goTypes = []any{
	(ProtocolType)(0),                       // 0: apis.aistudio.devmachine.v1.ProtocolType
	(*CreateDevMachineRequest)(nil),         // 1: apis.aistudio.devmachine.v1.CreateDevMachineRequest
	(*ExposePortConfig)(nil),                // 2: apis.aistudio.devmachine.v1.ExposePortConfig
	(*ListExposePortConfig)(nil),            // 3: apis.aistudio.devmachine.v1.ListExposePortConfig
	(*SSHConfig)(nil),                       // 4: apis.aistudio.devmachine.v1.SSHConfig
	(*DevMachine)(nil),                      // 5: apis.aistudio.devmachine.v1.DevMachine
	(*DevMachineStatus)(nil),                // 6: apis.aistudio.devmachine.v1.DevMachineStatus
	(*UpdateDevMachineRequest)(nil),         // 7: apis.aistudio.devmachine.v1.UpdateDevMachineRequest
	(*CreateTensorboardRequest)(nil),        // 8: apis.aistudio.devmachine.v1.CreateTensorboardRequest
	(*GetTensorboardRequest)(nil),           // 9: apis.aistudio.devmachine.v1.GetTensorboardRequest
	(*GetTensorboardResponse)(nil),          // 10: apis.aistudio.devmachine.v1.GetTensorboardResponse
	(*ListVolumes)(nil),                     // 11: apis.aistudio.devmachine.v1.ListVolumes
	(*GetDevMachineExposePortsRequest)(nil), // 12: apis.aistudio.devmachine.v1.GetDevMachineExposePortsRequest
	(*ListExposePortConfigs)(nil),           // 13: apis.aistudio.devmachine.v1.ListExposePortConfigs
	(*Volume)(nil),                          // 14: apis.aistudio.devmachine.v1.Volume
	(*GetDevMachineRequest)(nil),            // 15: apis.aistudio.devmachine.v1.GetDevMachineRequest
	(*GetDevMachineVolumesRequest)(nil),     // 16: apis.aistudio.devmachine.v1.GetDevMachineVolumesRequest
	(*StartDevMachineRequest)(nil),          // 17: apis.aistudio.devmachine.v1.StartDevMachineRequest
	(*StopDevMachineRequest)(nil),           // 18: apis.aistudio.devmachine.v1.StopDevMachineRequest
	(*RestartDevMachineRequest)(nil),        // 19: apis.aistudio.devmachine.v1.RestartDevMachineRequest
	(*RemoveDevMachineRequest)(nil),         // 20: apis.aistudio.devmachine.v1.RemoveDevMachineRequest
	(*ListDevMachinesOptions)(nil),          // 21: apis.aistudio.devmachine.v1.ListDevMachinesOptions
	(*ListDevMachinesResponse)(nil),         // 22: apis.aistudio.devmachine.v1.ListDevMachinesResponse
	(*CreateDevMachineResponse)(nil),        // 23: apis.aistudio.devmachine.v1.CreateDevMachineResponse
	(*UpdateDevMachineMembersRequest)(nil),  // 24: apis.aistudio.devmachine.v1.UpdateDevMachineMembersRequest
	nil,                                     // 25: apis.aistudio.devmachine.v1.CreateDevMachineRequest.LabelsEntry
	nil,                                     // 26: apis.aistudio.devmachine.v1.DevMachine.LabelsEntry
	nil,                                     // 27: apis.aistudio.devmachine.v1.UpdateDevMachineRequest.LabelsEntry
	(*common.EnvVar)(nil),                   // 28: apis.common.EnvVar
	(*common.Specification)(nil),            // 29: apis.common.Specification
	(*common.VolumeSpec)(nil),               // 30: apis.common.VolumeSpec
	(*v1.ConfigSpec)(nil),                   // 31: apis.aistudio.config.v1.ConfigSpec
	(*emptypb.Empty)(nil),                   // 32: google.protobuf.Empty
}
var file_aistudio_devmachine_v1_devmachine_proto_depIdxs = []int32{
	25, // 0: apis.aistudio.devmachine.v1.CreateDevMachineRequest.labels:type_name -> apis.aistudio.devmachine.v1.CreateDevMachineRequest.LabelsEntry
	28, // 1: apis.aistudio.devmachine.v1.CreateDevMachineRequest.envVars:type_name -> apis.common.EnvVar
	29, // 2: apis.aistudio.devmachine.v1.CreateDevMachineRequest.specification:type_name -> apis.common.Specification
	30, // 3: apis.aistudio.devmachine.v1.CreateDevMachineRequest.volumeSpecs:type_name -> apis.common.VolumeSpec
	31, // 4: apis.aistudio.devmachine.v1.CreateDevMachineRequest.configSpecs:type_name -> apis.aistudio.config.v1.ConfigSpec
	4,  // 5: apis.aistudio.devmachine.v1.CreateDevMachineRequest.sshConfig:type_name -> apis.aistudio.devmachine.v1.SSHConfig
	2,  // 6: apis.aistudio.devmachine.v1.CreateDevMachineRequest.exposePortConfigs:type_name -> apis.aistudio.devmachine.v1.ExposePortConfig
	0,  // 7: apis.aistudio.devmachine.v1.ExposePortConfig.protocol:type_name -> apis.aistudio.devmachine.v1.ProtocolType
	0,  // 8: apis.aistudio.devmachine.v1.ListExposePortConfig.protocol:type_name -> apis.aistudio.devmachine.v1.ProtocolType
	26, // 9: apis.aistudio.devmachine.v1.DevMachine.labels:type_name -> apis.aistudio.devmachine.v1.DevMachine.LabelsEntry
	28, // 10: apis.aistudio.devmachine.v1.DevMachine.envVars:type_name -> apis.common.EnvVar
	29, // 11: apis.aistudio.devmachine.v1.DevMachine.specification:type_name -> apis.common.Specification
	30, // 12: apis.aistudio.devmachine.v1.DevMachine.volumeSpecs:type_name -> apis.common.VolumeSpec
	4,  // 13: apis.aistudio.devmachine.v1.DevMachine.sshConfig:type_name -> apis.aistudio.devmachine.v1.SSHConfig
	2,  // 14: apis.aistudio.devmachine.v1.DevMachine.exposePortConfigs:type_name -> apis.aistudio.devmachine.v1.ExposePortConfig
	6,  // 15: apis.aistudio.devmachine.v1.DevMachine.dmStatus:type_name -> apis.aistudio.devmachine.v1.DevMachineStatus
	31, // 16: apis.aistudio.devmachine.v1.DevMachine.configSpecs:type_name -> apis.aistudio.config.v1.ConfigSpec
	27, // 17: apis.aistudio.devmachine.v1.UpdateDevMachineRequest.labels:type_name -> apis.aistudio.devmachine.v1.UpdateDevMachineRequest.LabelsEntry
	28, // 18: apis.aistudio.devmachine.v1.UpdateDevMachineRequest.envVars:type_name -> apis.common.EnvVar
	29, // 19: apis.aistudio.devmachine.v1.UpdateDevMachineRequest.specification:type_name -> apis.common.Specification
	30, // 20: apis.aistudio.devmachine.v1.UpdateDevMachineRequest.volumeSpecs:type_name -> apis.common.VolumeSpec
	4,  // 21: apis.aistudio.devmachine.v1.UpdateDevMachineRequest.sshConfig:type_name -> apis.aistudio.devmachine.v1.SSHConfig
	2,  // 22: apis.aistudio.devmachine.v1.UpdateDevMachineRequest.exposePortConfigs:type_name -> apis.aistudio.devmachine.v1.ExposePortConfig
	31, // 23: apis.aistudio.devmachine.v1.UpdateDevMachineRequest.configSpecs:type_name -> apis.aistudio.config.v1.ConfigSpec
	14, // 24: apis.aistudio.devmachine.v1.ListVolumes.volumes:type_name -> apis.aistudio.devmachine.v1.Volume
	3,  // 25: apis.aistudio.devmachine.v1.ListExposePortConfigs.exposePortConfigs:type_name -> apis.aistudio.devmachine.v1.ListExposePortConfig
	5,  // 26: apis.aistudio.devmachine.v1.ListDevMachinesResponse.devMachines:type_name -> apis.aistudio.devmachine.v1.DevMachine
	1,  // 27: apis.aistudio.devmachine.v1.DevMachineService.CreateDevMachineInstance:input_type -> apis.aistudio.devmachine.v1.CreateDevMachineRequest
	15, // 28: apis.aistudio.devmachine.v1.DevMachineService.GetDevMachineDetail:input_type -> apis.aistudio.devmachine.v1.GetDevMachineRequest
	21, // 29: apis.aistudio.devmachine.v1.DevMachineService.ListDevMachines:input_type -> apis.aistudio.devmachine.v1.ListDevMachinesOptions
	7,  // 30: apis.aistudio.devmachine.v1.DevMachineService.UpdateDevMachine:input_type -> apis.aistudio.devmachine.v1.UpdateDevMachineRequest
	19, // 31: apis.aistudio.devmachine.v1.DevMachineService.RestartDevMachine:input_type -> apis.aistudio.devmachine.v1.RestartDevMachineRequest
	18, // 32: apis.aistudio.devmachine.v1.DevMachineService.StopDevMachine:input_type -> apis.aistudio.devmachine.v1.StopDevMachineRequest
	20, // 33: apis.aistudio.devmachine.v1.DevMachineService.RemoveDevMachine:input_type -> apis.aistudio.devmachine.v1.RemoveDevMachineRequest
	16, // 34: apis.aistudio.devmachine.v1.DevMachineService.GetDevMachineVolumes:input_type -> apis.aistudio.devmachine.v1.GetDevMachineVolumesRequest
	12, // 35: apis.aistudio.devmachine.v1.DevMachineService.GetDevMachineExposePorts:input_type -> apis.aistudio.devmachine.v1.GetDevMachineExposePortsRequest
	8,  // 36: apis.aistudio.devmachine.v1.DevMachineService.CreateTensorboard:input_type -> apis.aistudio.devmachine.v1.CreateTensorboardRequest
	9,  // 37: apis.aistudio.devmachine.v1.DevMachineService.GetTensorboard:input_type -> apis.aistudio.devmachine.v1.GetTensorboardRequest
	24, // 38: apis.aistudio.devmachine.v1.DevMachineService.UpdateDevMachineMembers:input_type -> apis.aistudio.devmachine.v1.UpdateDevMachineMembersRequest
	23, // 39: apis.aistudio.devmachine.v1.DevMachineService.CreateDevMachineInstance:output_type -> apis.aistudio.devmachine.v1.CreateDevMachineResponse
	5,  // 40: apis.aistudio.devmachine.v1.DevMachineService.GetDevMachineDetail:output_type -> apis.aistudio.devmachine.v1.DevMachine
	22, // 41: apis.aistudio.devmachine.v1.DevMachineService.ListDevMachines:output_type -> apis.aistudio.devmachine.v1.ListDevMachinesResponse
	32, // 42: apis.aistudio.devmachine.v1.DevMachineService.UpdateDevMachine:output_type -> google.protobuf.Empty
	32, // 43: apis.aistudio.devmachine.v1.DevMachineService.RestartDevMachine:output_type -> google.protobuf.Empty
	32, // 44: apis.aistudio.devmachine.v1.DevMachineService.StopDevMachine:output_type -> google.protobuf.Empty
	32, // 45: apis.aistudio.devmachine.v1.DevMachineService.RemoveDevMachine:output_type -> google.protobuf.Empty
	11, // 46: apis.aistudio.devmachine.v1.DevMachineService.GetDevMachineVolumes:output_type -> apis.aistudio.devmachine.v1.ListVolumes
	13, // 47: apis.aistudio.devmachine.v1.DevMachineService.GetDevMachineExposePorts:output_type -> apis.aistudio.devmachine.v1.ListExposePortConfigs
	32, // 48: apis.aistudio.devmachine.v1.DevMachineService.CreateTensorboard:output_type -> google.protobuf.Empty
	10, // 49: apis.aistudio.devmachine.v1.DevMachineService.GetTensorboard:output_type -> apis.aistudio.devmachine.v1.GetTensorboardResponse
	32, // 50: apis.aistudio.devmachine.v1.DevMachineService.UpdateDevMachineMembers:output_type -> google.protobuf.Empty
	39, // [39:51] is the sub-list for method output_type
	27, // [27:39] is the sub-list for method input_type
	27, // [27:27] is the sub-list for extension type_name
	27, // [27:27] is the sub-list for extension extendee
	0,  // [0:27] is the sub-list for field type_name
}

func init() { file_aistudio_devmachine_v1_devmachine_proto_init() }
func file_aistudio_devmachine_v1_devmachine_proto_init() {
	if File_aistudio_devmachine_v1_devmachine_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_aistudio_devmachine_v1_devmachine_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*CreateDevMachineRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_devmachine_v1_devmachine_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*ExposePortConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_devmachine_v1_devmachine_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*ListExposePortConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_devmachine_v1_devmachine_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*SSHConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_devmachine_v1_devmachine_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*DevMachine); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_devmachine_v1_devmachine_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*DevMachineStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_devmachine_v1_devmachine_proto_msgTypes[6].Exporter = func(v any, i int) any {
			switch v := v.(*UpdateDevMachineRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_devmachine_v1_devmachine_proto_msgTypes[7].Exporter = func(v any, i int) any {
			switch v := v.(*CreateTensorboardRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_devmachine_v1_devmachine_proto_msgTypes[8].Exporter = func(v any, i int) any {
			switch v := v.(*GetTensorboardRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_devmachine_v1_devmachine_proto_msgTypes[9].Exporter = func(v any, i int) any {
			switch v := v.(*GetTensorboardResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_devmachine_v1_devmachine_proto_msgTypes[10].Exporter = func(v any, i int) any {
			switch v := v.(*ListVolumes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_devmachine_v1_devmachine_proto_msgTypes[11].Exporter = func(v any, i int) any {
			switch v := v.(*GetDevMachineExposePortsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_devmachine_v1_devmachine_proto_msgTypes[12].Exporter = func(v any, i int) any {
			switch v := v.(*ListExposePortConfigs); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_devmachine_v1_devmachine_proto_msgTypes[13].Exporter = func(v any, i int) any {
			switch v := v.(*Volume); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_devmachine_v1_devmachine_proto_msgTypes[14].Exporter = func(v any, i int) any {
			switch v := v.(*GetDevMachineRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_devmachine_v1_devmachine_proto_msgTypes[15].Exporter = func(v any, i int) any {
			switch v := v.(*GetDevMachineVolumesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_devmachine_v1_devmachine_proto_msgTypes[16].Exporter = func(v any, i int) any {
			switch v := v.(*StartDevMachineRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_devmachine_v1_devmachine_proto_msgTypes[17].Exporter = func(v any, i int) any {
			switch v := v.(*StopDevMachineRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_devmachine_v1_devmachine_proto_msgTypes[18].Exporter = func(v any, i int) any {
			switch v := v.(*RestartDevMachineRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_devmachine_v1_devmachine_proto_msgTypes[19].Exporter = func(v any, i int) any {
			switch v := v.(*RemoveDevMachineRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_devmachine_v1_devmachine_proto_msgTypes[20].Exporter = func(v any, i int) any {
			switch v := v.(*ListDevMachinesOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_devmachine_v1_devmachine_proto_msgTypes[21].Exporter = func(v any, i int) any {
			switch v := v.(*ListDevMachinesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_devmachine_v1_devmachine_proto_msgTypes[22].Exporter = func(v any, i int) any {
			switch v := v.(*CreateDevMachineResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_devmachine_v1_devmachine_proto_msgTypes[23].Exporter = func(v any, i int) any {
			switch v := v.(*UpdateDevMachineMembersRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_aistudio_devmachine_v1_devmachine_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   27,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_aistudio_devmachine_v1_devmachine_proto_goTypes,
		DependencyIndexes: file_aistudio_devmachine_v1_devmachine_proto_depIdxs,
		EnumInfos:         file_aistudio_devmachine_v1_devmachine_proto_enumTypes,
		MessageInfos:      file_aistudio_devmachine_v1_devmachine_proto_msgTypes,
	}.Build()
	File_aistudio_devmachine_v1_devmachine_proto = out.File
	file_aistudio_devmachine_v1_devmachine_proto_rawDesc = nil
	file_aistudio_devmachine_v1_devmachine_proto_goTypes = nil
	file_aistudio_devmachine_v1_devmachine_proto_depIdxs = nil
}
