// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.3
// - protoc             v5.29.3
// source: aistudio/devmachine/v1/devmachine.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationDevMachineServiceCreateDevMachineInstance = "/apis.aistudio.devmachine.v1.DevMachineService/CreateDevMachineInstance"
const OperationDevMachineServiceCreateTensorboard = "/apis.aistudio.devmachine.v1.DevMachineService/CreateTensorboard"
const OperationDevMachineServiceGetDevMachineDetail = "/apis.aistudio.devmachine.v1.DevMachineService/GetDevMachineDetail"
const OperationDevMachineServiceGetDevMachineExposePorts = "/apis.aistudio.devmachine.v1.DevMachineService/GetDevMachineExposePorts"
const OperationDevMachineServiceGetDevMachineVolumes = "/apis.aistudio.devmachine.v1.DevMachineService/GetDevMachineVolumes"
const OperationDevMachineServiceGetTensorboard = "/apis.aistudio.devmachine.v1.DevMachineService/GetTensorboard"
const OperationDevMachineServiceListDevMachines = "/apis.aistudio.devmachine.v1.DevMachineService/ListDevMachines"
const OperationDevMachineServiceRemoveDevMachine = "/apis.aistudio.devmachine.v1.DevMachineService/RemoveDevMachine"
const OperationDevMachineServiceRestartDevMachine = "/apis.aistudio.devmachine.v1.DevMachineService/RestartDevMachine"
const OperationDevMachineServiceStopDevMachine = "/apis.aistudio.devmachine.v1.DevMachineService/StopDevMachine"
const OperationDevMachineServiceUpdateDevMachine = "/apis.aistudio.devmachine.v1.DevMachineService/UpdateDevMachine"
const OperationDevMachineServiceUpdateDevMachineMembers = "/apis.aistudio.devmachine.v1.DevMachineService/UpdateDevMachineMembers"

type DevMachineServiceHTTPServer interface {
	CreateDevMachineInstance(context.Context, *CreateDevMachineRequest) (*CreateDevMachineResponse, error)
	CreateTensorboard(context.Context, *CreateTensorboardRequest) (*emptypb.Empty, error)
	GetDevMachineDetail(context.Context, *GetDevMachineRequest) (*DevMachine, error)
	GetDevMachineExposePorts(context.Context, *GetDevMachineExposePortsRequest) (*ListExposePortConfigs, error)
	GetDevMachineVolumes(context.Context, *GetDevMachineVolumesRequest) (*ListVolumes, error)
	GetTensorboard(context.Context, *GetTensorboardRequest) (*GetTensorboardResponse, error)
	ListDevMachines(context.Context, *ListDevMachinesOptions) (*ListDevMachinesResponse, error)
	// RemoveDevMachine 删除
	RemoveDevMachine(context.Context, *RemoveDevMachineRequest) (*emptypb.Empty, error)
	// RestartDevMachine 开机
	RestartDevMachine(context.Context, *RestartDevMachineRequest) (*emptypb.Empty, error)
	// StopDevMachine关机
	StopDevMachine(context.Context, *StopDevMachineRequest) (*emptypb.Empty, error)
	UpdateDevMachine(context.Context, *UpdateDevMachineRequest) (*emptypb.Empty, error)
	UpdateDevMachineMembers(context.Context, *UpdateDevMachineMembersRequest) (*emptypb.Empty, error)
}

func RegisterDevMachineServiceHTTPServer(s *http.Server, srv DevMachineServiceHTTPServer) {
	r := s.Route("/")
	r.POST("/apis/v1/workspace/{workspaceName}/dev-machine", _DevMachineService_CreateDevMachineInstance0_HTTP_Handler(srv))
	r.GET("/apis/v1/workspace/{workspaceName}/dev-machine/{devMachineName}/detail", _DevMachineService_GetDevMachineDetail0_HTTP_Handler(srv))
	r.GET("/apis/v1/workspace/{workspaceName}/dev-machines", _DevMachineService_ListDevMachines0_HTTP_Handler(srv))
	r.PUT("/apis/v1/workspace/{workspaceName}/dev-machine/{devMachineName}", _DevMachineService_UpdateDevMachine0_HTTP_Handler(srv))
	r.POST("/apis/v1/workspace/{workspaceName}/dev-machine/{devMachineName}/restart", _DevMachineService_RestartDevMachine0_HTTP_Handler(srv))
	r.POST("/apis/v1/workspace/{workspaceName}/dev-machine/{devMachineName}/stop", _DevMachineService_StopDevMachine0_HTTP_Handler(srv))
	r.DELETE("/apis/v1/workspace/{workspaceName}/dev-machine/{devMachineName}", _DevMachineService_RemoveDevMachine0_HTTP_Handler(srv))
	r.GET("/apis/v1/workspace/{workspaceName}/dev-machine/{devMachineName}/volumes", _DevMachineService_GetDevMachineVolumes0_HTTP_Handler(srv))
	r.GET("/apis/v1/workspace/{workspaceName}/dev-machine/{devMachineName}/expose-ports", _DevMachineService_GetDevMachineExposePorts0_HTTP_Handler(srv))
	r.POST("/apis/v1/workspace/{workspaceName}/dev-machine/{devMachineName}/tensorboard", _DevMachineService_CreateTensorboard1_HTTP_Handler(srv))
	r.GET("/apis/v1/workspace/{workspaceName}/dev-machine/{devMachineName}/tensorboard", _DevMachineService_GetTensorboard1_HTTP_Handler(srv))
	r.PUT("/apis/v1/workspace/{workspaceName}/dev-machine/{devMachineName}/members", _DevMachineService_UpdateDevMachineMembers0_HTTP_Handler(srv))
}

func _DevMachineService_CreateDevMachineInstance0_HTTP_Handler(srv DevMachineServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateDevMachineRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationDevMachineServiceCreateDevMachineInstance)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateDevMachineInstance(ctx, req.(*CreateDevMachineRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CreateDevMachineResponse)
		return ctx.Result(200, reply)
	}
}

func _DevMachineService_GetDevMachineDetail0_HTTP_Handler(srv DevMachineServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetDevMachineRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationDevMachineServiceGetDevMachineDetail)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetDevMachineDetail(ctx, req.(*GetDevMachineRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DevMachine)
		return ctx.Result(200, reply)
	}
}

func _DevMachineService_ListDevMachines0_HTTP_Handler(srv DevMachineServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListDevMachinesOptions
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationDevMachineServiceListDevMachines)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListDevMachines(ctx, req.(*ListDevMachinesOptions))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListDevMachinesResponse)
		return ctx.Result(200, reply)
	}
}

func _DevMachineService_UpdateDevMachine0_HTTP_Handler(srv DevMachineServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UpdateDevMachineRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationDevMachineServiceUpdateDevMachine)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateDevMachine(ctx, req.(*UpdateDevMachineRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _DevMachineService_RestartDevMachine0_HTTP_Handler(srv DevMachineServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in RestartDevMachineRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationDevMachineServiceRestartDevMachine)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.RestartDevMachine(ctx, req.(*RestartDevMachineRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _DevMachineService_StopDevMachine0_HTTP_Handler(srv DevMachineServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in StopDevMachineRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationDevMachineServiceStopDevMachine)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.StopDevMachine(ctx, req.(*StopDevMachineRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _DevMachineService_RemoveDevMachine0_HTTP_Handler(srv DevMachineServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in RemoveDevMachineRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationDevMachineServiceRemoveDevMachine)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.RemoveDevMachine(ctx, req.(*RemoveDevMachineRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _DevMachineService_GetDevMachineVolumes0_HTTP_Handler(srv DevMachineServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetDevMachineVolumesRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationDevMachineServiceGetDevMachineVolumes)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetDevMachineVolumes(ctx, req.(*GetDevMachineVolumesRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListVolumes)
		return ctx.Result(200, reply)
	}
}

func _DevMachineService_GetDevMachineExposePorts0_HTTP_Handler(srv DevMachineServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetDevMachineExposePortsRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationDevMachineServiceGetDevMachineExposePorts)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetDevMachineExposePorts(ctx, req.(*GetDevMachineExposePortsRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListExposePortConfigs)
		return ctx.Result(200, reply)
	}
}

func _DevMachineService_CreateTensorboard1_HTTP_Handler(srv DevMachineServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateTensorboardRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationDevMachineServiceCreateTensorboard)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateTensorboard(ctx, req.(*CreateTensorboardRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _DevMachineService_GetTensorboard1_HTTP_Handler(srv DevMachineServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetTensorboardRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationDevMachineServiceGetTensorboard)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetTensorboard(ctx, req.(*GetTensorboardRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetTensorboardResponse)
		return ctx.Result(200, reply)
	}
}

func _DevMachineService_UpdateDevMachineMembers0_HTTP_Handler(srv DevMachineServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UpdateDevMachineMembersRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationDevMachineServiceUpdateDevMachineMembers)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateDevMachineMembers(ctx, req.(*UpdateDevMachineMembersRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

type DevMachineServiceHTTPClient interface {
	CreateDevMachineInstance(ctx context.Context, req *CreateDevMachineRequest, opts ...http.CallOption) (rsp *CreateDevMachineResponse, err error)
	CreateTensorboard(ctx context.Context, req *CreateTensorboardRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	GetDevMachineDetail(ctx context.Context, req *GetDevMachineRequest, opts ...http.CallOption) (rsp *DevMachine, err error)
	GetDevMachineExposePorts(ctx context.Context, req *GetDevMachineExposePortsRequest, opts ...http.CallOption) (rsp *ListExposePortConfigs, err error)
	GetDevMachineVolumes(ctx context.Context, req *GetDevMachineVolumesRequest, opts ...http.CallOption) (rsp *ListVolumes, err error)
	GetTensorboard(ctx context.Context, req *GetTensorboardRequest, opts ...http.CallOption) (rsp *GetTensorboardResponse, err error)
	ListDevMachines(ctx context.Context, req *ListDevMachinesOptions, opts ...http.CallOption) (rsp *ListDevMachinesResponse, err error)
	RemoveDevMachine(ctx context.Context, req *RemoveDevMachineRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	RestartDevMachine(ctx context.Context, req *RestartDevMachineRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	StopDevMachine(ctx context.Context, req *StopDevMachineRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	UpdateDevMachine(ctx context.Context, req *UpdateDevMachineRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	UpdateDevMachineMembers(ctx context.Context, req *UpdateDevMachineMembersRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
}

type DevMachineServiceHTTPClientImpl struct {
	cc *http.Client
}

func NewDevMachineServiceHTTPClient(client *http.Client) DevMachineServiceHTTPClient {
	return &DevMachineServiceHTTPClientImpl{client}
}

func (c *DevMachineServiceHTTPClientImpl) CreateDevMachineInstance(ctx context.Context, in *CreateDevMachineRequest, opts ...http.CallOption) (*CreateDevMachineResponse, error) {
	var out CreateDevMachineResponse
	pattern := "/apis/v1/workspace/{workspaceName}/dev-machine"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationDevMachineServiceCreateDevMachineInstance))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *DevMachineServiceHTTPClientImpl) CreateTensorboard(ctx context.Context, in *CreateTensorboardRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/apis/v1/workspace/{workspaceName}/dev-machine/{devMachineName}/tensorboard"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationDevMachineServiceCreateTensorboard))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *DevMachineServiceHTTPClientImpl) GetDevMachineDetail(ctx context.Context, in *GetDevMachineRequest, opts ...http.CallOption) (*DevMachine, error) {
	var out DevMachine
	pattern := "/apis/v1/workspace/{workspaceName}/dev-machine/{devMachineName}/detail"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationDevMachineServiceGetDevMachineDetail))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *DevMachineServiceHTTPClientImpl) GetDevMachineExposePorts(ctx context.Context, in *GetDevMachineExposePortsRequest, opts ...http.CallOption) (*ListExposePortConfigs, error) {
	var out ListExposePortConfigs
	pattern := "/apis/v1/workspace/{workspaceName}/dev-machine/{devMachineName}/expose-ports"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationDevMachineServiceGetDevMachineExposePorts))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *DevMachineServiceHTTPClientImpl) GetDevMachineVolumes(ctx context.Context, in *GetDevMachineVolumesRequest, opts ...http.CallOption) (*ListVolumes, error) {
	var out ListVolumes
	pattern := "/apis/v1/workspace/{workspaceName}/dev-machine/{devMachineName}/volumes"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationDevMachineServiceGetDevMachineVolumes))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *DevMachineServiceHTTPClientImpl) GetTensorboard(ctx context.Context, in *GetTensorboardRequest, opts ...http.CallOption) (*GetTensorboardResponse, error) {
	var out GetTensorboardResponse
	pattern := "/apis/v1/workspace/{workspaceName}/dev-machine/{devMachineName}/tensorboard"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationDevMachineServiceGetTensorboard))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *DevMachineServiceHTTPClientImpl) ListDevMachines(ctx context.Context, in *ListDevMachinesOptions, opts ...http.CallOption) (*ListDevMachinesResponse, error) {
	var out ListDevMachinesResponse
	pattern := "/apis/v1/workspace/{workspaceName}/dev-machines"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationDevMachineServiceListDevMachines))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *DevMachineServiceHTTPClientImpl) RemoveDevMachine(ctx context.Context, in *RemoveDevMachineRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/apis/v1/workspace/{workspaceName}/dev-machine/{devMachineName}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationDevMachineServiceRemoveDevMachine))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *DevMachineServiceHTTPClientImpl) RestartDevMachine(ctx context.Context, in *RestartDevMachineRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/apis/v1/workspace/{workspaceName}/dev-machine/{devMachineName}/restart"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationDevMachineServiceRestartDevMachine))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *DevMachineServiceHTTPClientImpl) StopDevMachine(ctx context.Context, in *StopDevMachineRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/apis/v1/workspace/{workspaceName}/dev-machine/{devMachineName}/stop"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationDevMachineServiceStopDevMachine))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *DevMachineServiceHTTPClientImpl) UpdateDevMachine(ctx context.Context, in *UpdateDevMachineRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/apis/v1/workspace/{workspaceName}/dev-machine/{devMachineName}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationDevMachineServiceUpdateDevMachine))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *DevMachineServiceHTTPClientImpl) UpdateDevMachineMembers(ctx context.Context, in *UpdateDevMachineMembersRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/apis/v1/workspace/{workspaceName}/dev-machine/{devMachineName}/members"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationDevMachineServiceUpdateDevMachineMembers))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
