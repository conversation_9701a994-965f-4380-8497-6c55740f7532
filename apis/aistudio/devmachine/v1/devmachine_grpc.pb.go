// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v5.29.3
// source: aistudio/devmachine/v1/devmachine.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	DevMachineService_CreateDevMachineInstance_FullMethodName = "/apis.aistudio.devmachine.v1.DevMachineService/CreateDevMachineInstance"
	DevMachineService_GetDevMachineDetail_FullMethodName      = "/apis.aistudio.devmachine.v1.DevMachineService/GetDevMachineDetail"
	DevMachineService_ListDevMachines_FullMethodName          = "/apis.aistudio.devmachine.v1.DevMachineService/ListDevMachines"
	DevMachineService_UpdateDevMachine_FullMethodName         = "/apis.aistudio.devmachine.v1.DevMachineService/UpdateDevMachine"
	DevMachineService_RestartDevMachine_FullMethodName        = "/apis.aistudio.devmachine.v1.DevMachineService/RestartDevMachine"
	DevMachineService_StopDevMachine_FullMethodName           = "/apis.aistudio.devmachine.v1.DevMachineService/StopDevMachine"
	DevMachineService_RemoveDevMachine_FullMethodName         = "/apis.aistudio.devmachine.v1.DevMachineService/RemoveDevMachine"
	DevMachineService_GetDevMachineVolumes_FullMethodName     = "/apis.aistudio.devmachine.v1.DevMachineService/GetDevMachineVolumes"
	DevMachineService_GetDevMachineExposePorts_FullMethodName = "/apis.aistudio.devmachine.v1.DevMachineService/GetDevMachineExposePorts"
	DevMachineService_CreateTensorboard_FullMethodName        = "/apis.aistudio.devmachine.v1.DevMachineService/CreateTensorboard"
	DevMachineService_GetTensorboard_FullMethodName           = "/apis.aistudio.devmachine.v1.DevMachineService/GetTensorboard"
	DevMachineService_UpdateDevMachineMembers_FullMethodName  = "/apis.aistudio.devmachine.v1.DevMachineService/UpdateDevMachineMembers"
)

// DevMachineServiceClient is the client API for DevMachineService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type DevMachineServiceClient interface {
	CreateDevMachineInstance(ctx context.Context, in *CreateDevMachineRequest, opts ...grpc.CallOption) (*CreateDevMachineResponse, error)
	GetDevMachineDetail(ctx context.Context, in *GetDevMachineRequest, opts ...grpc.CallOption) (*DevMachine, error)
	ListDevMachines(ctx context.Context, in *ListDevMachinesOptions, opts ...grpc.CallOption) (*ListDevMachinesResponse, error)
	UpdateDevMachine(ctx context.Context, in *UpdateDevMachineRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 开机
	RestartDevMachine(ctx context.Context, in *RestartDevMachineRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 关机
	StopDevMachine(ctx context.Context, in *StopDevMachineRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 删除
	RemoveDevMachine(ctx context.Context, in *RemoveDevMachineRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	GetDevMachineVolumes(ctx context.Context, in *GetDevMachineVolumesRequest, opts ...grpc.CallOption) (*ListVolumes, error)
	GetDevMachineExposePorts(ctx context.Context, in *GetDevMachineExposePortsRequest, opts ...grpc.CallOption) (*ListExposePortConfigs, error)
	CreateTensorboard(ctx context.Context, in *CreateTensorboardRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	GetTensorboard(ctx context.Context, in *GetTensorboardRequest, opts ...grpc.CallOption) (*GetTensorboardResponse, error)
	UpdateDevMachineMembers(ctx context.Context, in *UpdateDevMachineMembersRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
}

type devMachineServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewDevMachineServiceClient(cc grpc.ClientConnInterface) DevMachineServiceClient {
	return &devMachineServiceClient{cc}
}

func (c *devMachineServiceClient) CreateDevMachineInstance(ctx context.Context, in *CreateDevMachineRequest, opts ...grpc.CallOption) (*CreateDevMachineResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateDevMachineResponse)
	err := c.cc.Invoke(ctx, DevMachineService_CreateDevMachineInstance_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *devMachineServiceClient) GetDevMachineDetail(ctx context.Context, in *GetDevMachineRequest, opts ...grpc.CallOption) (*DevMachine, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DevMachine)
	err := c.cc.Invoke(ctx, DevMachineService_GetDevMachineDetail_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *devMachineServiceClient) ListDevMachines(ctx context.Context, in *ListDevMachinesOptions, opts ...grpc.CallOption) (*ListDevMachinesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListDevMachinesResponse)
	err := c.cc.Invoke(ctx, DevMachineService_ListDevMachines_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *devMachineServiceClient) UpdateDevMachine(ctx context.Context, in *UpdateDevMachineRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, DevMachineService_UpdateDevMachine_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *devMachineServiceClient) RestartDevMachine(ctx context.Context, in *RestartDevMachineRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, DevMachineService_RestartDevMachine_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *devMachineServiceClient) StopDevMachine(ctx context.Context, in *StopDevMachineRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, DevMachineService_StopDevMachine_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *devMachineServiceClient) RemoveDevMachine(ctx context.Context, in *RemoveDevMachineRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, DevMachineService_RemoveDevMachine_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *devMachineServiceClient) GetDevMachineVolumes(ctx context.Context, in *GetDevMachineVolumesRequest, opts ...grpc.CallOption) (*ListVolumes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListVolumes)
	err := c.cc.Invoke(ctx, DevMachineService_GetDevMachineVolumes_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *devMachineServiceClient) GetDevMachineExposePorts(ctx context.Context, in *GetDevMachineExposePortsRequest, opts ...grpc.CallOption) (*ListExposePortConfigs, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListExposePortConfigs)
	err := c.cc.Invoke(ctx, DevMachineService_GetDevMachineExposePorts_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *devMachineServiceClient) CreateTensorboard(ctx context.Context, in *CreateTensorboardRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, DevMachineService_CreateTensorboard_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *devMachineServiceClient) GetTensorboard(ctx context.Context, in *GetTensorboardRequest, opts ...grpc.CallOption) (*GetTensorboardResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetTensorboardResponse)
	err := c.cc.Invoke(ctx, DevMachineService_GetTensorboard_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *devMachineServiceClient) UpdateDevMachineMembers(ctx context.Context, in *UpdateDevMachineMembersRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, DevMachineService_UpdateDevMachineMembers_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// DevMachineServiceServer is the server API for DevMachineService service.
// All implementations must embed UnimplementedDevMachineServiceServer
// for forward compatibility
type DevMachineServiceServer interface {
	CreateDevMachineInstance(context.Context, *CreateDevMachineRequest) (*CreateDevMachineResponse, error)
	GetDevMachineDetail(context.Context, *GetDevMachineRequest) (*DevMachine, error)
	ListDevMachines(context.Context, *ListDevMachinesOptions) (*ListDevMachinesResponse, error)
	UpdateDevMachine(context.Context, *UpdateDevMachineRequest) (*emptypb.Empty, error)
	// 开机
	RestartDevMachine(context.Context, *RestartDevMachineRequest) (*emptypb.Empty, error)
	// 关机
	StopDevMachine(context.Context, *StopDevMachineRequest) (*emptypb.Empty, error)
	// 删除
	RemoveDevMachine(context.Context, *RemoveDevMachineRequest) (*emptypb.Empty, error)
	GetDevMachineVolumes(context.Context, *GetDevMachineVolumesRequest) (*ListVolumes, error)
	GetDevMachineExposePorts(context.Context, *GetDevMachineExposePortsRequest) (*ListExposePortConfigs, error)
	CreateTensorboard(context.Context, *CreateTensorboardRequest) (*emptypb.Empty, error)
	GetTensorboard(context.Context, *GetTensorboardRequest) (*GetTensorboardResponse, error)
	UpdateDevMachineMembers(context.Context, *UpdateDevMachineMembersRequest) (*emptypb.Empty, error)
	mustEmbedUnimplementedDevMachineServiceServer()
}

// UnimplementedDevMachineServiceServer must be embedded to have forward compatible implementations.
type UnimplementedDevMachineServiceServer struct {
}

func (UnimplementedDevMachineServiceServer) CreateDevMachineInstance(context.Context, *CreateDevMachineRequest) (*CreateDevMachineResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateDevMachineInstance not implemented")
}
func (UnimplementedDevMachineServiceServer) GetDevMachineDetail(context.Context, *GetDevMachineRequest) (*DevMachine, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDevMachineDetail not implemented")
}
func (UnimplementedDevMachineServiceServer) ListDevMachines(context.Context, *ListDevMachinesOptions) (*ListDevMachinesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListDevMachines not implemented")
}
func (UnimplementedDevMachineServiceServer) UpdateDevMachine(context.Context, *UpdateDevMachineRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateDevMachine not implemented")
}
func (UnimplementedDevMachineServiceServer) RestartDevMachine(context.Context, *RestartDevMachineRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RestartDevMachine not implemented")
}
func (UnimplementedDevMachineServiceServer) StopDevMachine(context.Context, *StopDevMachineRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StopDevMachine not implemented")
}
func (UnimplementedDevMachineServiceServer) RemoveDevMachine(context.Context, *RemoveDevMachineRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemoveDevMachine not implemented")
}
func (UnimplementedDevMachineServiceServer) GetDevMachineVolumes(context.Context, *GetDevMachineVolumesRequest) (*ListVolumes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDevMachineVolumes not implemented")
}
func (UnimplementedDevMachineServiceServer) GetDevMachineExposePorts(context.Context, *GetDevMachineExposePortsRequest) (*ListExposePortConfigs, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDevMachineExposePorts not implemented")
}
func (UnimplementedDevMachineServiceServer) CreateTensorboard(context.Context, *CreateTensorboardRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateTensorboard not implemented")
}
func (UnimplementedDevMachineServiceServer) GetTensorboard(context.Context, *GetTensorboardRequest) (*GetTensorboardResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTensorboard not implemented")
}
func (UnimplementedDevMachineServiceServer) UpdateDevMachineMembers(context.Context, *UpdateDevMachineMembersRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateDevMachineMembers not implemented")
}
func (UnimplementedDevMachineServiceServer) mustEmbedUnimplementedDevMachineServiceServer() {}

// UnsafeDevMachineServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to DevMachineServiceServer will
// result in compilation errors.
type UnsafeDevMachineServiceServer interface {
	mustEmbedUnimplementedDevMachineServiceServer()
}

func RegisterDevMachineServiceServer(s grpc.ServiceRegistrar, srv DevMachineServiceServer) {
	s.RegisterService(&DevMachineService_ServiceDesc, srv)
}

func _DevMachineService_CreateDevMachineInstance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateDevMachineRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DevMachineServiceServer).CreateDevMachineInstance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DevMachineService_CreateDevMachineInstance_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DevMachineServiceServer).CreateDevMachineInstance(ctx, req.(*CreateDevMachineRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DevMachineService_GetDevMachineDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDevMachineRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DevMachineServiceServer).GetDevMachineDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DevMachineService_GetDevMachineDetail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DevMachineServiceServer).GetDevMachineDetail(ctx, req.(*GetDevMachineRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DevMachineService_ListDevMachines_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListDevMachinesOptions)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DevMachineServiceServer).ListDevMachines(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DevMachineService_ListDevMachines_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DevMachineServiceServer).ListDevMachines(ctx, req.(*ListDevMachinesOptions))
	}
	return interceptor(ctx, in, info, handler)
}

func _DevMachineService_UpdateDevMachine_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateDevMachineRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DevMachineServiceServer).UpdateDevMachine(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DevMachineService_UpdateDevMachine_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DevMachineServiceServer).UpdateDevMachine(ctx, req.(*UpdateDevMachineRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DevMachineService_RestartDevMachine_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RestartDevMachineRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DevMachineServiceServer).RestartDevMachine(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DevMachineService_RestartDevMachine_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DevMachineServiceServer).RestartDevMachine(ctx, req.(*RestartDevMachineRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DevMachineService_StopDevMachine_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StopDevMachineRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DevMachineServiceServer).StopDevMachine(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DevMachineService_StopDevMachine_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DevMachineServiceServer).StopDevMachine(ctx, req.(*StopDevMachineRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DevMachineService_RemoveDevMachine_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveDevMachineRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DevMachineServiceServer).RemoveDevMachine(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DevMachineService_RemoveDevMachine_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DevMachineServiceServer).RemoveDevMachine(ctx, req.(*RemoveDevMachineRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DevMachineService_GetDevMachineVolumes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDevMachineVolumesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DevMachineServiceServer).GetDevMachineVolumes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DevMachineService_GetDevMachineVolumes_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DevMachineServiceServer).GetDevMachineVolumes(ctx, req.(*GetDevMachineVolumesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DevMachineService_GetDevMachineExposePorts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDevMachineExposePortsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DevMachineServiceServer).GetDevMachineExposePorts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DevMachineService_GetDevMachineExposePorts_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DevMachineServiceServer).GetDevMachineExposePorts(ctx, req.(*GetDevMachineExposePortsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DevMachineService_CreateTensorboard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateTensorboardRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DevMachineServiceServer).CreateTensorboard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DevMachineService_CreateTensorboard_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DevMachineServiceServer).CreateTensorboard(ctx, req.(*CreateTensorboardRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DevMachineService_GetTensorboard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTensorboardRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DevMachineServiceServer).GetTensorboard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DevMachineService_GetTensorboard_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DevMachineServiceServer).GetTensorboard(ctx, req.(*GetTensorboardRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DevMachineService_UpdateDevMachineMembers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateDevMachineMembersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DevMachineServiceServer).UpdateDevMachineMembers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DevMachineService_UpdateDevMachineMembers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DevMachineServiceServer).UpdateDevMachineMembers(ctx, req.(*UpdateDevMachineMembersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// DevMachineService_ServiceDesc is the grpc.ServiceDesc for DevMachineService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var DevMachineService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "apis.aistudio.devmachine.v1.DevMachineService",
	HandlerType: (*DevMachineServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateDevMachineInstance",
			Handler:    _DevMachineService_CreateDevMachineInstance_Handler,
		},
		{
			MethodName: "GetDevMachineDetail",
			Handler:    _DevMachineService_GetDevMachineDetail_Handler,
		},
		{
			MethodName: "ListDevMachines",
			Handler:    _DevMachineService_ListDevMachines_Handler,
		},
		{
			MethodName: "UpdateDevMachine",
			Handler:    _DevMachineService_UpdateDevMachine_Handler,
		},
		{
			MethodName: "RestartDevMachine",
			Handler:    _DevMachineService_RestartDevMachine_Handler,
		},
		{
			MethodName: "StopDevMachine",
			Handler:    _DevMachineService_StopDevMachine_Handler,
		},
		{
			MethodName: "RemoveDevMachine",
			Handler:    _DevMachineService_RemoveDevMachine_Handler,
		},
		{
			MethodName: "GetDevMachineVolumes",
			Handler:    _DevMachineService_GetDevMachineVolumes_Handler,
		},
		{
			MethodName: "GetDevMachineExposePorts",
			Handler:    _DevMachineService_GetDevMachineExposePorts_Handler,
		},
		{
			MethodName: "CreateTensorboard",
			Handler:    _DevMachineService_CreateTensorboard_Handler,
		},
		{
			MethodName: "GetTensorboard",
			Handler:    _DevMachineService_GetTensorboard_Handler,
		},
		{
			MethodName: "UpdateDevMachineMembers",
			Handler:    _DevMachineService_UpdateDevMachineMembers_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "aistudio/devmachine/v1/devmachine.proto",
}
