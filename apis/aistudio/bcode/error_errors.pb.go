// Code generated by protoc-gen-go-errors. DO NOT EDIT.

package bcode

import (
	fmt "fmt"
	errors "github.com/go-kratos/kratos/v2/errors"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
const _ = errors.SupportPackageIsVersion1

func IsUserNotFound(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_UserNotFound.String() && e.Code == 404
}

func ErrorUserNotFound(format string, args ...interface{}) *errors.Error {
	return errors.New(404, ErrorReason_UserNotFound.String(), fmt.Sprintf(format, args...))
}

func IsUserInconsistentPassword(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_UserInconsistentPassword.String() && e.Code == 403
}

func ErrorUserInconsistentPassword(format string, args ...interface{}) *errors.Error {
	return errors.New(403, ErrorReason_UserInconsistentPassword.String(), fmt.Sprintf(format, args...))
}

func IsUserInvalidPassword(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_UserInvalidPassword.String() && e.Code == 400
}

func ErrorUserInvalidPassword(format string, args ...interface{}) *errors.Error {
	return errors.New(400, ErrorReason_UserInvalidPassword.String(), fmt.Sprintf(format, args...))
}

func IsServerInternalError(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_ServerInternalError.String() && e.Code == 500
}

func ErrorServerInternalError(format string, args ...interface{}) *errors.Error {
	return errors.New(500, ErrorReason_ServerInternalError.String(), fmt.Sprintf(format, args...))
}

func IsPermissionNotAllowed(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_PermissionNotAllowed.String() && e.Code == 403
}

func ErrorPermissionNotAllowed(format string, args ...interface{}) *errors.Error {
	return errors.New(403, ErrorReason_PermissionNotAllowed.String(), fmt.Sprintf(format, args...))
}

func IsPermissionNotFound(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_PermissionNotFound.String() && e.Code == 400
}

func ErrorPermissionNotFound(format string, args ...interface{}) *errors.Error {
	return errors.New(400, ErrorReason_PermissionNotFound.String(), fmt.Sprintf(format, args...))
}

func IsRoleNotFound(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_RoleNotFound.String() && e.Code == 400
}

func ErrorRoleNotFound(format string, args ...interface{}) *errors.Error {
	return errors.New(400, ErrorReason_RoleNotFound.String(), fmt.Sprintf(format, args...))
}

func IsRoleBindingNotFound(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_RoleBindingNotFound.String() && e.Code == 400
}

func ErrorRoleBindingNotFound(format string, args ...interface{}) *errors.Error {
	return errors.New(400, ErrorReason_RoleBindingNotFound.String(), fmt.Sprintf(format, args...))
}

func IsUnauthorized(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_Unauthorized.String() && e.Code == 401
}

func ErrorUnauthorized(format string, args ...interface{}) *errors.Error {
	return errors.New(401, ErrorReason_Unauthorized.String(), fmt.Sprintf(format, args...))
}

func IsInvalidArgument(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_InvalidArgument.String() && e.Code == 400
}

func ErrorInvalidArgument(format string, args ...interface{}) *errors.Error {
	return errors.New(400, ErrorReason_InvalidArgument.String(), fmt.Sprintf(format, args...))
}

func IsKeycloakError(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_KeycloakError.String() && e.Code == 500
}

func ErrorKeycloakError(format string, args ...interface{}) *errors.Error {
	return errors.New(500, ErrorReason_KeycloakError.String(), fmt.Sprintf(format, args...))
}

func IsWorkspaceAlreadyExist(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_WorkspaceAlreadyExist.String() && e.Code == 400
}

func ErrorWorkspaceAlreadyExist(format string, args ...interface{}) *errors.Error {
	return errors.New(400, ErrorReason_WorkspaceAlreadyExist.String(), fmt.Sprintf(format, args...))
}

func IsNodeNotFound(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_NodeNotFound.String() && e.Code == 404
}

func ErrorNodeNotFound(format string, args ...interface{}) *errors.Error {
	return errors.New(404, ErrorReason_NodeNotFound.String(), fmt.Sprintf(format, args...))
}

func IsKubernetesClusterNotFound(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_KubernetesClusterNotFound.String() && e.Code == 404
}

func ErrorKubernetesClusterNotFound(format string, args ...interface{}) *errors.Error {
	return errors.New(404, ErrorReason_KubernetesClusterNotFound.String(), fmt.Sprintf(format, args...))
}

func IsNodeAlreadyAssigned(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_NodeAlreadyAssigned.String() && e.Code == 409
}

func ErrorNodeAlreadyAssigned(format string, args ...interface{}) *errors.Error {
	return errors.New(409, ErrorReason_NodeAlreadyAssigned.String(), fmt.Sprintf(format, args...))
}

func IsQueueAlreadyExists(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_QueueAlreadyExists.String() && e.Code == 409
}

func ErrorQueueAlreadyExists(format string, args ...interface{}) *errors.Error {
	return errors.New(409, ErrorReason_QueueAlreadyExists.String(), fmt.Sprintf(format, args...))
}

func IsQueueNotFound(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_QueueNotFound.String() && e.Code == 400
}

func ErrorQueueNotFound(format string, args ...interface{}) *errors.Error {
	return errors.New(400, ErrorReason_QueueNotFound.String(), fmt.Sprintf(format, args...))
}

func IsWorkflowNotFound(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_WorkflowNotFound.String() && e.Code == 400
}

func ErrorWorkflowNotFound(format string, args ...interface{}) *errors.Error {
	return errors.New(400, ErrorReason_WorkflowNotFound.String(), fmt.Sprintf(format, args...))
}

func IsK8sClusterInitError(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_K8sClusterInitError.String() && e.Code == 500
}

func ErrorK8sClusterInitError(format string, args ...interface{}) *errors.Error {
	return errors.New(500, ErrorReason_K8sClusterInitError.String(), fmt.Sprintf(format, args...))
}

func IsVelaProjectAlreadyExist(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_VelaProjectAlreadyExist.String() && e.Code == 400
}

func ErrorVelaProjectAlreadyExist(format string, args ...interface{}) *errors.Error {
	return errors.New(400, ErrorReason_VelaProjectAlreadyExist.String(), fmt.Sprintf(format, args...))
}

func IsVelaEnvironmentAlreadyExist(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_VelaEnvironmentAlreadyExist.String() && e.Code == 400
}

func ErrorVelaEnvironmentAlreadyExist(format string, args ...interface{}) *errors.Error {
	return errors.New(400, ErrorReason_VelaEnvironmentAlreadyExist.String(), fmt.Sprintf(format, args...))
}

func IsVelaInternalError(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_VelaInternalError.String() && e.Code == 500
}

func ErrorVelaInternalError(format string, args ...interface{}) *errors.Error {
	return errors.New(500, ErrorReason_VelaInternalError.String(), fmt.Sprintf(format, args...))
}

func IsCubeFSVolumeAlReadyExists(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_CubeFSVolumeAlReadyExists.String() && e.Code == 409
}

func ErrorCubeFSVolumeAlReadyExists(format string, args ...interface{}) *errors.Error {
	return errors.New(409, ErrorReason_CubeFSVolumeAlReadyExists.String(), fmt.Sprintf(format, args...))
}

func IsCubeFSVolumeNotFound(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_CubeFSVolumeNotFound.String() && e.Code == 404
}

func ErrorCubeFSVolumeNotFound(format string, args ...interface{}) *errors.Error {
	return errors.New(404, ErrorReason_CubeFSVolumeNotFound.String(), fmt.Sprintf(format, args...))
}

func IsCubeFSUserNotFount(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_CubeFSUserNotFount.String() && e.Code == 404
}

func ErrorCubeFSUserNotFount(format string, args ...interface{}) *errors.Error {
	return errors.New(404, ErrorReason_CubeFSUserNotFount.String(), fmt.Sprintf(format, args...))
}

func IsCubeFSUnauthorized(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_CubeFSUnauthorized.String() && e.Code == 401
}

func ErrorCubeFSUnauthorized(format string, args ...interface{}) *errors.Error {
	return errors.New(401, ErrorReason_CubeFSUnauthorized.String(), fmt.Sprintf(format, args...))
}

func IsJobNotFound(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_JobNotFound.String() && e.Code == 404
}

func ErrorJobNotFound(format string, args ...interface{}) *errors.Error {
	return errors.New(404, ErrorReason_JobNotFound.String(), fmt.Sprintf(format, args...))
}

func IsCloudFSVolumeNotFound(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_CloudFSVolumeNotFound.String() && e.Code == 404
}

func ErrorCloudFSVolumeNotFound(format string, args ...interface{}) *errors.Error {
	return errors.New(404, ErrorReason_CloudFSVolumeNotFound.String(), fmt.Sprintf(format, args...))
}

func IsCloudFSVolumeAlreadyExists(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_CloudFSVolumeAlreadyExists.String() && e.Code == 409
}

func ErrorCloudFSVolumeAlreadyExists(format string, args ...interface{}) *errors.Error {
	return errors.New(409, ErrorReason_CloudFSVolumeAlreadyExists.String(), fmt.Sprintf(format, args...))
}

func IsJobStatusError(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_JobStatusError.String() && e.Code == 500
}

func ErrorJobStatusError(format string, args ...interface{}) *errors.Error {
	return errors.New(500, ErrorReason_JobStatusError.String(), fmt.Sprintf(format, args...))
}

func IsQueueHasRunningJobs(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_QueueHasRunningJobs.String() && e.Code == 400
}

func ErrorQueueHasRunningJobs(format string, args ...interface{}) *errors.Error {
	return errors.New(400, ErrorReason_QueueHasRunningJobs.String(), fmt.Sprintf(format, args...))
}

func IsResourceInsufficient(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_ResourceInsufficient.String() && e.Code == 400
}

func ErrorResourceInsufficient(format string, args ...interface{}) *errors.Error {
	return errors.New(400, ErrorReason_ResourceInsufficient.String(), fmt.Sprintf(format, args...))
}

func IsNotFoundGPUProduct(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_NotFoundGPUProduct.String() && e.Code == 400
}

func ErrorNotFoundGPUProduct(format string, args ...interface{}) *errors.Error {
	return errors.New(400, ErrorReason_NotFoundGPUProduct.String(), fmt.Sprintf(format, args...))
}

func IsJobAlReadyExists(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_JobAlReadyExists.String() && e.Code == 400
}

func ErrorJobAlReadyExists(format string, args ...interface{}) *errors.Error {
	return errors.New(400, ErrorReason_JobAlReadyExists.String(), fmt.Sprintf(format, args...))
}

func IsQueueNotRunning(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_QueueNotRunning.String() && e.Code == 400
}

func ErrorQueueNotRunning(format string, args ...interface{}) *errors.Error {
	return errors.New(400, ErrorReason_QueueNotRunning.String(), fmt.Sprintf(format, args...))
}

func IsWorkspaceNotFound(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_WorkspaceNotFound.String() && e.Code == 404
}

func ErrorWorkspaceNotFound(format string, args ...interface{}) *errors.Error {
	return errors.New(404, ErrorReason_WorkspaceNotFound.String(), fmt.Sprintf(format, args...))
}

func IsOperationNotAllowed(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_OperationNotAllowed.String() && e.Code == 400
}

func ErrorOperationNotAllowed(format string, args ...interface{}) *errors.Error {
	return errors.New(400, ErrorReason_OperationNotAllowed.String(), fmt.Sprintf(format, args...))
}

func IsNotFoundCubeFSWorkedCluster(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_NotFoundCubeFSWorkedCluster.String() && e.Code == 400
}

func ErrorNotFoundCubeFSWorkedCluster(format string, args ...interface{}) *errors.Error {
	return errors.New(400, ErrorReason_NotFoundCubeFSWorkedCluster.String(), fmt.Sprintf(format, args...))
}

func IsTensorboardNotFound(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_TensorboardNotFound.String() && e.Code == 404
}

func ErrorTensorboardNotFound(format string, args ...interface{}) *errors.Error {
	return errors.New(404, ErrorReason_TensorboardNotFound.String(), fmt.Sprintf(format, args...))
}

func IsTensorboardFeatureNotEnabled(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_TensorboardFeatureNotEnabled.String() && e.Code == 400
}

func ErrorTensorboardFeatureNotEnabled(format string, args ...interface{}) *errors.Error {
	return errors.New(400, ErrorReason_TensorboardFeatureNotEnabled.String(), fmt.Sprintf(format, args...))
}

func IsTcrSystemError(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_TcrSystemError.String() && e.Code == 500
}

func ErrorTcrSystemError(format string, args ...interface{}) *errors.Error {
	return errors.New(500, ErrorReason_TcrSystemError.String(), fmt.Sprintf(format, args...))
}

func IsDevMachineAlReadyExists(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_DevMachineAlReadyExists.String() && e.Code == 400
}

func ErrorDevMachineAlReadyExists(format string, args ...interface{}) *errors.Error {
	return errors.New(400, ErrorReason_DevMachineAlReadyExists.String(), fmt.Sprintf(format, args...))
}

func IsDevMachineNotFound(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_DevMachineNotFound.String() && e.Code == 404
}

func ErrorDevMachineNotFound(format string, args ...interface{}) *errors.Error {
	return errors.New(404, ErrorReason_DevMachineNotFound.String(), fmt.Sprintf(format, args...))
}

func IsModelNotFound(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_ModelNotFound.String() && e.Code == 404
}

func ErrorModelNotFound(format string, args ...interface{}) *errors.Error {
	return errors.New(404, ErrorReason_ModelNotFound.String(), fmt.Sprintf(format, args...))
}

func IsRelatedResourcesNotCleared(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_RelatedResourcesNotCleared.String() && e.Code == 403
}

func ErrorRelatedResourcesNotCleared(format string, args ...interface{}) *errors.Error {
	return errors.New(403, ErrorReason_RelatedResourcesNotCleared.String(), fmt.Sprintf(format, args...))
}

// application error
func IsApplicationExist(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_ApplicationExist.String() && e.Code == 400
}

// application error
func ErrorApplicationExist(format string, args ...interface{}) *errors.Error {
	return errors.New(400, ErrorReason_ApplicationExist.String(), fmt.Sprintf(format, args...))
}

func IsApplicationNotFound(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_ApplicationNotFound.String() && e.Code == 404
}

func ErrorApplicationNotFound(format string, args ...interface{}) *errors.Error {
	return errors.New(404, ErrorReason_ApplicationNotFound.String(), fmt.Sprintf(format, args...))
}

func IsWorkspaceNotReady(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_WorkspaceNotReady.String() && e.Code == 400
}

func ErrorWorkspaceNotReady(format string, args ...interface{}) *errors.Error {
	return errors.New(400, ErrorReason_WorkspaceNotReady.String(), fmt.Sprintf(format, args...))
}

func IsDeploymentGroupExistsAlready(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_DeploymentGroupExistsAlready.String() && e.Code == 400
}

func ErrorDeploymentGroupExistsAlready(format string, args ...interface{}) *errors.Error {
	return errors.New(400, ErrorReason_DeploymentGroupExistsAlready.String(), fmt.Sprintf(format, args...))
}

func IsDeploymentGroupNotFound(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_DeploymentGroupNotFound.String() && e.Code == 404
}

func ErrorDeploymentGroupNotFound(format string, args ...interface{}) *errors.Error {
	return errors.New(404, ErrorReason_DeploymentGroupNotFound.String(), fmt.Sprintf(format, args...))
}

func IsHTTPRouteNotFound(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_HTTPRouteNotFound.String() && e.Code == 404
}

func ErrorHTTPRouteNotFound(format string, args ...interface{}) *errors.Error {
	return errors.New(404, ErrorReason_HTTPRouteNotFound.String(), fmt.Sprintf(format, args...))
}

func IsGatewayAlReadyExists(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_GatewayAlReadyExists.String() && e.Code == 400
}

func ErrorGatewayAlReadyExists(format string, args ...interface{}) *errors.Error {
	return errors.New(400, ErrorReason_GatewayAlReadyExists.String(), fmt.Sprintf(format, args...))
}

func IsGatewayNotFound(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_GatewayNotFound.String() && e.Code == 404
}

func ErrorGatewayNotFound(format string, args ...interface{}) *errors.Error {
	return errors.New(404, ErrorReason_GatewayNotFound.String(), fmt.Sprintf(format, args...))
}

func IsDomainAlReadyExists(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_DomainAlReadyExists.String() && e.Code == 400
}

func ErrorDomainAlReadyExists(format string, args ...interface{}) *errors.Error {
	return errors.New(400, ErrorReason_DomainAlReadyExists.String(), fmt.Sprintf(format, args...))
}

func IsDomainNotFound(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_DomainNotFound.String() && e.Code == 404
}

func ErrorDomainNotFound(format string, args ...interface{}) *errors.Error {
	return errors.New(404, ErrorReason_DomainNotFound.String(), fmt.Sprintf(format, args...))
}

func IsDestinationNotFound(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_DestinationNotFound.String() && e.Code == 404
}

func ErrorDestinationNotFound(format string, args ...interface{}) *errors.Error {
	return errors.New(404, ErrorReason_DestinationNotFound.String(), fmt.Sprintf(format, args...))
}

func IsDestinationAlReadyExists(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_DestinationAlReadyExists.String() && e.Code == 400
}

func ErrorDestinationAlReadyExists(format string, args ...interface{}) *errors.Error {
	return errors.New(400, ErrorReason_DestinationAlReadyExists.String(), fmt.Sprintf(format, args...))
}

func IsServiceEntryAlReadyExists(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_ServiceEntryAlReadyExists.String() && e.Code == 400
}

func ErrorServiceEntryAlReadyExists(format string, args ...interface{}) *errors.Error {
	return errors.New(400, ErrorReason_ServiceEntryAlReadyExists.String(), fmt.Sprintf(format, args...))
}

func IsServiceEntryNotFound(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_ServiceEntryNotFound.String() && e.Code == 404
}

func ErrorServiceEntryNotFound(format string, args ...interface{}) *errors.Error {
	return errors.New(404, ErrorReason_ServiceEntryNotFound.String(), fmt.Sprintf(format, args...))
}

func IsGatewayServiceBindingNotFound(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_GatewayServiceBindingNotFound.String() && e.Code == 404
}

func ErrorGatewayServiceBindingNotFound(format string, args ...interface{}) *errors.Error {
	return errors.New(404, ErrorReason_GatewayServiceBindingNotFound.String(), fmt.Sprintf(format, args...))
}

func IsGatewayServiceBindingAlreadyExists(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_GatewayServiceBindingAlreadyExists.String() && e.Code == 419
}

func ErrorGatewayServiceBindingAlreadyExists(format string, args ...interface{}) *errors.Error {
	return errors.New(419, ErrorReason_GatewayServiceBindingAlreadyExists.String(), fmt.Sprintf(format, args...))
}

func IsProductSpecificationAlreadyExists(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_ProductSpecificationAlreadyExists.String() && e.Code == 400
}

func ErrorProductSpecificationAlreadyExists(format string, args ...interface{}) *errors.Error {
	return errors.New(400, ErrorReason_ProductSpecificationAlreadyExists.String(), fmt.Sprintf(format, args...))
}

func IsProductSpecificationNotFound(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_ProductSpecificationNotFound.String() && e.Code == 404
}

func ErrorProductSpecificationNotFound(format string, args ...interface{}) *errors.Error {
	return errors.New(404, ErrorReason_ProductSpecificationNotFound.String(), fmt.Sprintf(format, args...))
}

func IsHadoopSecretNotFound(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_HadoopSecretNotFound.String() && e.Code == 404
}

func ErrorHadoopSecretNotFound(format string, args ...interface{}) *errors.Error {
	return errors.New(404, ErrorReason_HadoopSecretNotFound.String(), fmt.Sprintf(format, args...))
}

func IsNotEnoughResource(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_NotEnoughResource.String() && e.Code == 400
}

func ErrorNotEnoughResource(format string, args ...interface{}) *errors.Error {
	return errors.New(400, ErrorReason_NotEnoughResource.String(), fmt.Sprintf(format, args...))
}

func IsForbiddenOperation(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_ForbiddenOperation.String() && e.Code == 403
}

func ErrorForbiddenOperation(format string, args ...interface{}) *errors.Error {
	return errors.New(403, ErrorReason_ForbiddenOperation.String(), fmt.Sprintf(format, args...))
}

func IsJobTemplateAlreadyExists(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_JobTemplateAlreadyExists.String() && e.Code == 400
}

func ErrorJobTemplateAlreadyExists(format string, args ...interface{}) *errors.Error {
	return errors.New(400, ErrorReason_JobTemplateAlreadyExists.String(), fmt.Sprintf(format, args...))
}

func IsTemporalScheduleNotExists(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_TemporalScheduleNotExists.String() && e.Code == 404
}

func ErrorTemporalScheduleNotExists(format string, args ...interface{}) *errors.Error {
	return errors.New(404, ErrorReason_TemporalScheduleNotExists.String(), fmt.Sprintf(format, args...))
}
