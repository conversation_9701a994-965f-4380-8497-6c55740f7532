// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v5.29.3
// source: aistudio/bcode/error.proto

package bcode

import (
	_ "github.com/go-kratos/kratos/v2/errors"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ErrorReason int32

const (
	ErrorReason_UserNotFound                 ErrorReason = 0
	ErrorReason_UserInconsistentPassword     ErrorReason = 1
	ErrorReason_UserInvalidPassword          ErrorReason = 2
	ErrorReason_ServerInternalError          ErrorReason = 3
	ErrorReason_PermissionNotAllowed         ErrorReason = 4
	ErrorReason_PermissionNotFound           ErrorReason = 5
	ErrorReason_RoleNotFound                 ErrorReason = 6
	ErrorReason_RoleBindingNotFound          ErrorReason = 7
	ErrorReason_Unauthorized                 ErrorReason = 8
	ErrorReason_InvalidArgument              ErrorReason = 9
	ErrorReason_KeycloakError                ErrorReason = 10
	ErrorReason_WorkspaceAlreadyExist        ErrorReason = 11
	ErrorReason_NodeNotFound                 ErrorReason = 12
	ErrorReason_KubernetesClusterNotFound    ErrorReason = 13
	ErrorReason_NodeAlreadyAssigned          ErrorReason = 14
	ErrorReason_QueueAlreadyExists           ErrorReason = 15
	ErrorReason_QueueNotFound                ErrorReason = 16
	ErrorReason_WorkflowNotFound             ErrorReason = 17
	ErrorReason_K8sClusterInitError          ErrorReason = 18
	ErrorReason_VelaProjectAlreadyExist      ErrorReason = 19
	ErrorReason_VelaEnvironmentAlreadyExist  ErrorReason = 20
	ErrorReason_VelaInternalError            ErrorReason = 21
	ErrorReason_CubeFSVolumeAlReadyExists    ErrorReason = 22
	ErrorReason_CubeFSVolumeNotFound         ErrorReason = 23
	ErrorReason_CubeFSUserNotFount           ErrorReason = 41
	ErrorReason_CubeFSUnauthorized           ErrorReason = 42
	ErrorReason_JobNotFound                  ErrorReason = 24
	ErrorReason_CloudFSVolumeNotFound        ErrorReason = 25
	ErrorReason_CloudFSVolumeAlreadyExists   ErrorReason = 26
	ErrorReason_JobStatusError               ErrorReason = 27
	ErrorReason_QueueHasRunningJobs          ErrorReason = 28
	ErrorReason_ResourceInsufficient         ErrorReason = 29
	ErrorReason_NotFoundGPUProduct           ErrorReason = 30
	ErrorReason_JobAlReadyExists             ErrorReason = 31
	ErrorReason_QueueNotRunning              ErrorReason = 32
	ErrorReason_WorkspaceNotFound            ErrorReason = 33
	ErrorReason_OperationNotAllowed          ErrorReason = 34
	ErrorReason_NotFoundCubeFSWorkedCluster  ErrorReason = 35
	ErrorReason_TensorboardNotFound          ErrorReason = 36
	ErrorReason_TensorboardFeatureNotEnabled ErrorReason = 37
	ErrorReason_TcrSystemError               ErrorReason = 38
	ErrorReason_DevMachineAlReadyExists      ErrorReason = 39
	ErrorReason_DevMachineNotFound           ErrorReason = 40
	ErrorReason_ModelNotFound                ErrorReason = 43
	ErrorReason_RelatedResourcesNotCleared   ErrorReason = 44
	// application error
	ErrorReason_ApplicationExist                   ErrorReason = 45
	ErrorReason_ApplicationNotFound                ErrorReason = 46
	ErrorReason_WorkspaceNotReady                  ErrorReason = 47
	ErrorReason_DeploymentGroupExistsAlready       ErrorReason = 48
	ErrorReason_DeploymentGroupNotFound            ErrorReason = 49
	ErrorReason_HTTPRouteNotFound                  ErrorReason = 50
	ErrorReason_GatewayAlReadyExists               ErrorReason = 51
	ErrorReason_GatewayNotFound                    ErrorReason = 52
	ErrorReason_DomainAlReadyExists                ErrorReason = 53
	ErrorReason_DomainNotFound                     ErrorReason = 54
	ErrorReason_DestinationNotFound                ErrorReason = 55
	ErrorReason_DestinationAlReadyExists           ErrorReason = 56
	ErrorReason_ServiceEntryAlReadyExists          ErrorReason = 57
	ErrorReason_ServiceEntryNotFound               ErrorReason = 58
	ErrorReason_GatewayServiceBindingNotFound      ErrorReason = 59
	ErrorReason_GatewayServiceBindingAlreadyExists ErrorReason = 60
	ErrorReason_ProductSpecificationAlreadyExists  ErrorReason = 61
	ErrorReason_ProductSpecificationNotFound       ErrorReason = 62
	ErrorReason_HadoopSecretNotFound               ErrorReason = 63
	ErrorReason_NotEnoughResource                  ErrorReason = 64
	ErrorReason_ForbiddenOperation                 ErrorReason = 65
	ErrorReason_JobTemplateAlreadyExists           ErrorReason = 66
	ErrorReason_TemporalScheduleNotExists          ErrorReason = 67
)

// Enum value maps for ErrorReason.
var (
	ErrorReason_name = map[int32]string{
		0:  "UserNotFound",
		1:  "UserInconsistentPassword",
		2:  "UserInvalidPassword",
		3:  "ServerInternalError",
		4:  "PermissionNotAllowed",
		5:  "PermissionNotFound",
		6:  "RoleNotFound",
		7:  "RoleBindingNotFound",
		8:  "Unauthorized",
		9:  "InvalidArgument",
		10: "KeycloakError",
		11: "WorkspaceAlreadyExist",
		12: "NodeNotFound",
		13: "KubernetesClusterNotFound",
		14: "NodeAlreadyAssigned",
		15: "QueueAlreadyExists",
		16: "QueueNotFound",
		17: "WorkflowNotFound",
		18: "K8sClusterInitError",
		19: "VelaProjectAlreadyExist",
		20: "VelaEnvironmentAlreadyExist",
		21: "VelaInternalError",
		22: "CubeFSVolumeAlReadyExists",
		23: "CubeFSVolumeNotFound",
		41: "CubeFSUserNotFount",
		42: "CubeFSUnauthorized",
		24: "JobNotFound",
		25: "CloudFSVolumeNotFound",
		26: "CloudFSVolumeAlreadyExists",
		27: "JobStatusError",
		28: "QueueHasRunningJobs",
		29: "ResourceInsufficient",
		30: "NotFoundGPUProduct",
		31: "JobAlReadyExists",
		32: "QueueNotRunning",
		33: "WorkspaceNotFound",
		34: "OperationNotAllowed",
		35: "NotFoundCubeFSWorkedCluster",
		36: "TensorboardNotFound",
		37: "TensorboardFeatureNotEnabled",
		38: "TcrSystemError",
		39: "DevMachineAlReadyExists",
		40: "DevMachineNotFound",
		43: "ModelNotFound",
		44: "RelatedResourcesNotCleared",
		45: "ApplicationExist",
		46: "ApplicationNotFound",
		47: "WorkspaceNotReady",
		48: "DeploymentGroupExistsAlready",
		49: "DeploymentGroupNotFound",
		50: "HTTPRouteNotFound",
		51: "GatewayAlReadyExists",
		52: "GatewayNotFound",
		53: "DomainAlReadyExists",
		54: "DomainNotFound",
		55: "DestinationNotFound",
		56: "DestinationAlReadyExists",
		57: "ServiceEntryAlReadyExists",
		58: "ServiceEntryNotFound",
		59: "GatewayServiceBindingNotFound",
		60: "GatewayServiceBindingAlreadyExists",
		61: "ProductSpecificationAlreadyExists",
		62: "ProductSpecificationNotFound",
		63: "HadoopSecretNotFound",
		64: "NotEnoughResource",
		65: "ForbiddenOperation",
		66: "JobTemplateAlreadyExists",
		67: "TemporalScheduleNotExists",
	}
	ErrorReason_value = map[string]int32{
		"UserNotFound":                       0,
		"UserInconsistentPassword":           1,
		"UserInvalidPassword":                2,
		"ServerInternalError":                3,
		"PermissionNotAllowed":               4,
		"PermissionNotFound":                 5,
		"RoleNotFound":                       6,
		"RoleBindingNotFound":                7,
		"Unauthorized":                       8,
		"InvalidArgument":                    9,
		"KeycloakError":                      10,
		"WorkspaceAlreadyExist":              11,
		"NodeNotFound":                       12,
		"KubernetesClusterNotFound":          13,
		"NodeAlreadyAssigned":                14,
		"QueueAlreadyExists":                 15,
		"QueueNotFound":                      16,
		"WorkflowNotFound":                   17,
		"K8sClusterInitError":                18,
		"VelaProjectAlreadyExist":            19,
		"VelaEnvironmentAlreadyExist":        20,
		"VelaInternalError":                  21,
		"CubeFSVolumeAlReadyExists":          22,
		"CubeFSVolumeNotFound":               23,
		"CubeFSUserNotFount":                 41,
		"CubeFSUnauthorized":                 42,
		"JobNotFound":                        24,
		"CloudFSVolumeNotFound":              25,
		"CloudFSVolumeAlreadyExists":         26,
		"JobStatusError":                     27,
		"QueueHasRunningJobs":                28,
		"ResourceInsufficient":               29,
		"NotFoundGPUProduct":                 30,
		"JobAlReadyExists":                   31,
		"QueueNotRunning":                    32,
		"WorkspaceNotFound":                  33,
		"OperationNotAllowed":                34,
		"NotFoundCubeFSWorkedCluster":        35,
		"TensorboardNotFound":                36,
		"TensorboardFeatureNotEnabled":       37,
		"TcrSystemError":                     38,
		"DevMachineAlReadyExists":            39,
		"DevMachineNotFound":                 40,
		"ModelNotFound":                      43,
		"RelatedResourcesNotCleared":         44,
		"ApplicationExist":                   45,
		"ApplicationNotFound":                46,
		"WorkspaceNotReady":                  47,
		"DeploymentGroupExistsAlready":       48,
		"DeploymentGroupNotFound":            49,
		"HTTPRouteNotFound":                  50,
		"GatewayAlReadyExists":               51,
		"GatewayNotFound":                    52,
		"DomainAlReadyExists":                53,
		"DomainNotFound":                     54,
		"DestinationNotFound":                55,
		"DestinationAlReadyExists":           56,
		"ServiceEntryAlReadyExists":          57,
		"ServiceEntryNotFound":               58,
		"GatewayServiceBindingNotFound":      59,
		"GatewayServiceBindingAlreadyExists": 60,
		"ProductSpecificationAlreadyExists":  61,
		"ProductSpecificationNotFound":       62,
		"HadoopSecretNotFound":               63,
		"NotEnoughResource":                  64,
		"ForbiddenOperation":                 65,
		"JobTemplateAlreadyExists":           66,
		"TemporalScheduleNotExists":          67,
	}
)

func (x ErrorReason) Enum() *ErrorReason {
	p := new(ErrorReason)
	*p = x
	return p
}

func (x ErrorReason) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ErrorReason) Descriptor() protoreflect.EnumDescriptor {
	return file_aistudio_bcode_error_proto_enumTypes[0].Descriptor()
}

func (ErrorReason) Type() protoreflect.EnumType {
	return &file_aistudio_bcode_error_proto_enumTypes[0]
}

func (x ErrorReason) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ErrorReason.Descriptor instead.
func (ErrorReason) EnumDescriptor() ([]byte, []int) {
	return file_aistudio_bcode_error_proto_rawDescGZIP(), []int{0}
}

var File_aistudio_bcode_error_proto protoreflect.FileDescriptor

var file_aistudio_bcode_error_proto_rawDesc = []byte{
	0x0a, 0x1a, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2f, 0x62, 0x63, 0x6f, 0x64, 0x65,
	0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x13, 0x61, 0x70,
	0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x62, 0x63, 0x6f, 0x64,
	0x65, 0x1a, 0x13, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2a, 0x81, 0x11, 0x0a, 0x0b, 0x45, 0x72, 0x72, 0x6f, 0x72,
	0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x0c, 0x55, 0x73, 0x65, 0x72, 0x4e, 0x6f,
	0x74, 0x46, 0x6f, 0x75, 0x6e, 0x64, 0x10, 0x00, 0x1a, 0x04, 0xa8, 0x45, 0x94, 0x03, 0x12, 0x22,
	0x0a, 0x18, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x63, 0x6f, 0x6e, 0x73, 0x69, 0x73, 0x74, 0x65,
	0x6e, 0x74, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x10, 0x01, 0x1a, 0x04, 0xa8, 0x45,
	0x93, 0x03, 0x12, 0x1d, 0x0a, 0x13, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x10, 0x02, 0x1a, 0x04, 0xa8, 0x45, 0x90,
	0x03, 0x12, 0x1d, 0x0a, 0x13, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x49, 0x6e, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x6c, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x10, 0x03, 0x1a, 0x04, 0xa8, 0x45, 0xf4, 0x03,
	0x12, 0x1e, 0x0a, 0x14, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x4e, 0x6f,
	0x74, 0x41, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x10, 0x04, 0x1a, 0x04, 0xa8, 0x45, 0x93, 0x03,
	0x12, 0x1c, 0x0a, 0x12, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x4e, 0x6f,
	0x74, 0x46, 0x6f, 0x75, 0x6e, 0x64, 0x10, 0x05, 0x1a, 0x04, 0xa8, 0x45, 0x90, 0x03, 0x12, 0x16,
	0x0a, 0x0c, 0x52, 0x6f, 0x6c, 0x65, 0x4e, 0x6f, 0x74, 0x46, 0x6f, 0x75, 0x6e, 0x64, 0x10, 0x06,
	0x1a, 0x04, 0xa8, 0x45, 0x90, 0x03, 0x12, 0x1d, 0x0a, 0x13, 0x52, 0x6f, 0x6c, 0x65, 0x42, 0x69,
	0x6e, 0x64, 0x69, 0x6e, 0x67, 0x4e, 0x6f, 0x74, 0x46, 0x6f, 0x75, 0x6e, 0x64, 0x10, 0x07, 0x1a,
	0x04, 0xa8, 0x45, 0x90, 0x03, 0x12, 0x16, 0x0a, 0x0c, 0x55, 0x6e, 0x61, 0x75, 0x74, 0x68, 0x6f,
	0x72, 0x69, 0x7a, 0x65, 0x64, 0x10, 0x08, 0x1a, 0x04, 0xa8, 0x45, 0x91, 0x03, 0x12, 0x19, 0x0a,
	0x0f, 0x49, 0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x41, 0x72, 0x67, 0x75, 0x6d, 0x65, 0x6e, 0x74,
	0x10, 0x09, 0x1a, 0x04, 0xa8, 0x45, 0x90, 0x03, 0x12, 0x17, 0x0a, 0x0d, 0x4b, 0x65, 0x79, 0x63,
	0x6c, 0x6f, 0x61, 0x6b, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x10, 0x0a, 0x1a, 0x04, 0xa8, 0x45, 0xf4,
	0x03, 0x12, 0x1f, 0x0a, 0x15, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x41, 0x6c,
	0x72, 0x65, 0x61, 0x64, 0x79, 0x45, 0x78, 0x69, 0x73, 0x74, 0x10, 0x0b, 0x1a, 0x04, 0xa8, 0x45,
	0x90, 0x03, 0x12, 0x16, 0x0a, 0x0c, 0x4e, 0x6f, 0x64, 0x65, 0x4e, 0x6f, 0x74, 0x46, 0x6f, 0x75,
	0x6e, 0x64, 0x10, 0x0c, 0x1a, 0x04, 0xa8, 0x45, 0x94, 0x03, 0x12, 0x23, 0x0a, 0x19, 0x4b, 0x75,
	0x62, 0x65, 0x72, 0x6e, 0x65, 0x74, 0x65, 0x73, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x4e,
	0x6f, 0x74, 0x46, 0x6f, 0x75, 0x6e, 0x64, 0x10, 0x0d, 0x1a, 0x04, 0xa8, 0x45, 0x94, 0x03, 0x12,
	0x1d, 0x0a, 0x13, 0x4e, 0x6f, 0x64, 0x65, 0x41, 0x6c, 0x72, 0x65, 0x61, 0x64, 0x79, 0x41, 0x73,
	0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x10, 0x0e, 0x1a, 0x04, 0xa8, 0x45, 0x99, 0x03, 0x12, 0x1c,
	0x0a, 0x12, 0x51, 0x75, 0x65, 0x75, 0x65, 0x41, 0x6c, 0x72, 0x65, 0x61, 0x64, 0x79, 0x45, 0x78,
	0x69, 0x73, 0x74, 0x73, 0x10, 0x0f, 0x1a, 0x04, 0xa8, 0x45, 0x99, 0x03, 0x12, 0x17, 0x0a, 0x0d,
	0x51, 0x75, 0x65, 0x75, 0x65, 0x4e, 0x6f, 0x74, 0x46, 0x6f, 0x75, 0x6e, 0x64, 0x10, 0x10, 0x1a,
	0x04, 0xa8, 0x45, 0x90, 0x03, 0x12, 0x1a, 0x0a, 0x10, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f,
	0x77, 0x4e, 0x6f, 0x74, 0x46, 0x6f, 0x75, 0x6e, 0x64, 0x10, 0x11, 0x1a, 0x04, 0xa8, 0x45, 0x90,
	0x03, 0x12, 0x1d, 0x0a, 0x13, 0x4b, 0x38, 0x73, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x49,
	0x6e, 0x69, 0x74, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x10, 0x12, 0x1a, 0x04, 0xa8, 0x45, 0xf4, 0x03,
	0x12, 0x21, 0x0a, 0x17, 0x56, 0x65, 0x6c, 0x61, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x41,
	0x6c, 0x72, 0x65, 0x61, 0x64, 0x79, 0x45, 0x78, 0x69, 0x73, 0x74, 0x10, 0x13, 0x1a, 0x04, 0xa8,
	0x45, 0x90, 0x03, 0x12, 0x25, 0x0a, 0x1b, 0x56, 0x65, 0x6c, 0x61, 0x45, 0x6e, 0x76, 0x69, 0x72,
	0x6f, 0x6e, 0x6d, 0x65, 0x6e, 0x74, 0x41, 0x6c, 0x72, 0x65, 0x61, 0x64, 0x79, 0x45, 0x78, 0x69,
	0x73, 0x74, 0x10, 0x14, 0x1a, 0x04, 0xa8, 0x45, 0x90, 0x03, 0x12, 0x1b, 0x0a, 0x11, 0x56, 0x65,
	0x6c, 0x61, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x10,
	0x15, 0x1a, 0x04, 0xa8, 0x45, 0xf4, 0x03, 0x12, 0x23, 0x0a, 0x19, 0x43, 0x75, 0x62, 0x65, 0x46,
	0x53, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x41, 0x6c, 0x52, 0x65, 0x61, 0x64, 0x79, 0x45, 0x78,
	0x69, 0x73, 0x74, 0x73, 0x10, 0x16, 0x1a, 0x04, 0xa8, 0x45, 0x99, 0x03, 0x12, 0x1e, 0x0a, 0x14,
	0x43, 0x75, 0x62, 0x65, 0x46, 0x53, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x4e, 0x6f, 0x74, 0x46,
	0x6f, 0x75, 0x6e, 0x64, 0x10, 0x17, 0x1a, 0x04, 0xa8, 0x45, 0x94, 0x03, 0x12, 0x1c, 0x0a, 0x12,
	0x43, 0x75, 0x62, 0x65, 0x46, 0x53, 0x55, 0x73, 0x65, 0x72, 0x4e, 0x6f, 0x74, 0x46, 0x6f, 0x75,
	0x6e, 0x74, 0x10, 0x29, 0x1a, 0x04, 0xa8, 0x45, 0x94, 0x03, 0x12, 0x1c, 0x0a, 0x12, 0x43, 0x75,
	0x62, 0x65, 0x46, 0x53, 0x55, 0x6e, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x65, 0x64,
	0x10, 0x2a, 0x1a, 0x04, 0xa8, 0x45, 0x91, 0x03, 0x12, 0x15, 0x0a, 0x0b, 0x4a, 0x6f, 0x62, 0x4e,
	0x6f, 0x74, 0x46, 0x6f, 0x75, 0x6e, 0x64, 0x10, 0x18, 0x1a, 0x04, 0xa8, 0x45, 0x94, 0x03, 0x12,
	0x1f, 0x0a, 0x15, 0x43, 0x6c, 0x6f, 0x75, 0x64, 0x46, 0x53, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65,
	0x4e, 0x6f, 0x74, 0x46, 0x6f, 0x75, 0x6e, 0x64, 0x10, 0x19, 0x1a, 0x04, 0xa8, 0x45, 0x94, 0x03,
	0x12, 0x24, 0x0a, 0x1a, 0x43, 0x6c, 0x6f, 0x75, 0x64, 0x46, 0x53, 0x56, 0x6f, 0x6c, 0x75, 0x6d,
	0x65, 0x41, 0x6c, 0x72, 0x65, 0x61, 0x64, 0x79, 0x45, 0x78, 0x69, 0x73, 0x74, 0x73, 0x10, 0x1a,
	0x1a, 0x04, 0xa8, 0x45, 0x99, 0x03, 0x12, 0x18, 0x0a, 0x0e, 0x4a, 0x6f, 0x62, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x10, 0x1b, 0x1a, 0x04, 0xa8, 0x45, 0xf4, 0x03,
	0x12, 0x1d, 0x0a, 0x13, 0x51, 0x75, 0x65, 0x75, 0x65, 0x48, 0x61, 0x73, 0x52, 0x75, 0x6e, 0x6e,
	0x69, 0x6e, 0x67, 0x4a, 0x6f, 0x62, 0x73, 0x10, 0x1c, 0x1a, 0x04, 0xa8, 0x45, 0x90, 0x03, 0x12,
	0x1e, 0x0a, 0x14, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x6e, 0x73, 0x75, 0x66,
	0x66, 0x69, 0x63, 0x69, 0x65, 0x6e, 0x74, 0x10, 0x1d, 0x1a, 0x04, 0xa8, 0x45, 0x90, 0x03, 0x12,
	0x1c, 0x0a, 0x12, 0x4e, 0x6f, 0x74, 0x46, 0x6f, 0x75, 0x6e, 0x64, 0x47, 0x50, 0x55, 0x50, 0x72,
	0x6f, 0x64, 0x75, 0x63, 0x74, 0x10, 0x1e, 0x1a, 0x04, 0xa8, 0x45, 0x90, 0x03, 0x12, 0x1a, 0x0a,
	0x10, 0x4a, 0x6f, 0x62, 0x41, 0x6c, 0x52, 0x65, 0x61, 0x64, 0x79, 0x45, 0x78, 0x69, 0x73, 0x74,
	0x73, 0x10, 0x1f, 0x1a, 0x04, 0xa8, 0x45, 0x90, 0x03, 0x12, 0x19, 0x0a, 0x0f, 0x51, 0x75, 0x65,
	0x75, 0x65, 0x4e, 0x6f, 0x74, 0x52, 0x75, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x10, 0x20, 0x1a, 0x04,
	0xa8, 0x45, 0x90, 0x03, 0x12, 0x1b, 0x0a, 0x11, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x4e, 0x6f, 0x74, 0x46, 0x6f, 0x75, 0x6e, 0x64, 0x10, 0x21, 0x1a, 0x04, 0xa8, 0x45, 0x94,
	0x03, 0x12, 0x1d, 0x0a, 0x13, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x6f,
	0x74, 0x41, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x10, 0x22, 0x1a, 0x04, 0xa8, 0x45, 0x90, 0x03,
	0x12, 0x25, 0x0a, 0x1b, 0x4e, 0x6f, 0x74, 0x46, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x75, 0x62, 0x65,
	0x46, 0x53, 0x57, 0x6f, 0x72, 0x6b, 0x65, 0x64, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x10,
	0x23, 0x1a, 0x04, 0xa8, 0x45, 0x90, 0x03, 0x12, 0x1d, 0x0a, 0x13, 0x54, 0x65, 0x6e, 0x73, 0x6f,
	0x72, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x4e, 0x6f, 0x74, 0x46, 0x6f, 0x75, 0x6e, 0x64, 0x10, 0x24,
	0x1a, 0x04, 0xa8, 0x45, 0x94, 0x03, 0x12, 0x26, 0x0a, 0x1c, 0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72,
	0x62, 0x6f, 0x61, 0x72, 0x64, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x4e, 0x6f, 0x74, 0x45,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x10, 0x25, 0x1a, 0x04, 0xa8, 0x45, 0x90, 0x03, 0x12, 0x18,
	0x0a, 0x0e, 0x54, 0x63, 0x72, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x45, 0x72, 0x72, 0x6f, 0x72,
	0x10, 0x26, 0x1a, 0x04, 0xa8, 0x45, 0xf4, 0x03, 0x12, 0x21, 0x0a, 0x17, 0x44, 0x65, 0x76, 0x4d,
	0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x41, 0x6c, 0x52, 0x65, 0x61, 0x64, 0x79, 0x45, 0x78, 0x69,
	0x73, 0x74, 0x73, 0x10, 0x27, 0x1a, 0x04, 0xa8, 0x45, 0x90, 0x03, 0x12, 0x1c, 0x0a, 0x12, 0x44,
	0x65, 0x76, 0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x4e, 0x6f, 0x74, 0x46, 0x6f, 0x75, 0x6e,
	0x64, 0x10, 0x28, 0x1a, 0x04, 0xa8, 0x45, 0x94, 0x03, 0x12, 0x17, 0x0a, 0x0d, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x4e, 0x6f, 0x74, 0x46, 0x6f, 0x75, 0x6e, 0x64, 0x10, 0x2b, 0x1a, 0x04, 0xa8, 0x45,
	0x94, 0x03, 0x12, 0x24, 0x0a, 0x1a, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x52, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x4e, 0x6f, 0x74, 0x43, 0x6c, 0x65, 0x61, 0x72, 0x65, 0x64,
	0x10, 0x2c, 0x1a, 0x04, 0xa8, 0x45, 0x93, 0x03, 0x12, 0x1a, 0x0a, 0x10, 0x41, 0x70, 0x70, 0x6c,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x78, 0x69, 0x73, 0x74, 0x10, 0x2d, 0x1a, 0x04,
	0xa8, 0x45, 0x90, 0x03, 0x12, 0x1d, 0x0a, 0x13, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x4e, 0x6f, 0x74, 0x46, 0x6f, 0x75, 0x6e, 0x64, 0x10, 0x2e, 0x1a, 0x04, 0xa8,
	0x45, 0x94, 0x03, 0x12, 0x1b, 0x0a, 0x11, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x4e, 0x6f, 0x74, 0x52, 0x65, 0x61, 0x64, 0x79, 0x10, 0x2f, 0x1a, 0x04, 0xa8, 0x45, 0x90, 0x03,
	0x12, 0x26, 0x0a, 0x1c, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x45, 0x78, 0x69, 0x73, 0x74, 0x73, 0x41, 0x6c, 0x72, 0x65, 0x61, 0x64, 0x79,
	0x10, 0x30, 0x1a, 0x04, 0xa8, 0x45, 0x90, 0x03, 0x12, 0x21, 0x0a, 0x17, 0x44, 0x65, 0x70, 0x6c,
	0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x6f, 0x74, 0x46, 0x6f,
	0x75, 0x6e, 0x64, 0x10, 0x31, 0x1a, 0x04, 0xa8, 0x45, 0x94, 0x03, 0x12, 0x1b, 0x0a, 0x11, 0x48,
	0x54, 0x54, 0x50, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x4e, 0x6f, 0x74, 0x46, 0x6f, 0x75, 0x6e, 0x64,
	0x10, 0x32, 0x1a, 0x04, 0xa8, 0x45, 0x94, 0x03, 0x12, 0x1e, 0x0a, 0x14, 0x47, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x41, 0x6c, 0x52, 0x65, 0x61, 0x64, 0x79, 0x45, 0x78, 0x69, 0x73, 0x74, 0x73,
	0x10, 0x33, 0x1a, 0x04, 0xa8, 0x45, 0x90, 0x03, 0x12, 0x19, 0x0a, 0x0f, 0x47, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x4e, 0x6f, 0x74, 0x46, 0x6f, 0x75, 0x6e, 0x64, 0x10, 0x34, 0x1a, 0x04, 0xa8,
	0x45, 0x94, 0x03, 0x12, 0x1d, 0x0a, 0x13, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x41, 0x6c, 0x52,
	0x65, 0x61, 0x64, 0x79, 0x45, 0x78, 0x69, 0x73, 0x74, 0x73, 0x10, 0x35, 0x1a, 0x04, 0xa8, 0x45,
	0x90, 0x03, 0x12, 0x18, 0x0a, 0x0e, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x4e, 0x6f, 0x74, 0x46,
	0x6f, 0x75, 0x6e, 0x64, 0x10, 0x36, 0x1a, 0x04, 0xa8, 0x45, 0x94, 0x03, 0x12, 0x1d, 0x0a, 0x13,
	0x44, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x6f, 0x74, 0x46, 0x6f,
	0x75, 0x6e, 0x64, 0x10, 0x37, 0x1a, 0x04, 0xa8, 0x45, 0x94, 0x03, 0x12, 0x22, 0x0a, 0x18, 0x44,
	0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x6c, 0x52, 0x65, 0x61, 0x64,
	0x79, 0x45, 0x78, 0x69, 0x73, 0x74, 0x73, 0x10, 0x38, 0x1a, 0x04, 0xa8, 0x45, 0x90, 0x03, 0x12,
	0x23, 0x0a, 0x19, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x41,
	0x6c, 0x52, 0x65, 0x61, 0x64, 0x79, 0x45, 0x78, 0x69, 0x73, 0x74, 0x73, 0x10, 0x39, 0x1a, 0x04,
	0xa8, 0x45, 0x90, 0x03, 0x12, 0x1e, 0x0a, 0x14, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x4e, 0x6f, 0x74, 0x46, 0x6f, 0x75, 0x6e, 0x64, 0x10, 0x3a, 0x1a, 0x04,
	0xa8, 0x45, 0x94, 0x03, 0x12, 0x27, 0x0a, 0x1d, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x4e, 0x6f, 0x74,
	0x46, 0x6f, 0x75, 0x6e, 0x64, 0x10, 0x3b, 0x1a, 0x04, 0xa8, 0x45, 0x94, 0x03, 0x12, 0x2c, 0x0a,
	0x22, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x42,
	0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x41, 0x6c, 0x72, 0x65, 0x61, 0x64, 0x79, 0x45, 0x78, 0x69,
	0x73, 0x74, 0x73, 0x10, 0x3c, 0x1a, 0x04, 0xa8, 0x45, 0xa3, 0x03, 0x12, 0x2b, 0x0a, 0x21, 0x50,
	0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x41, 0x6c, 0x72, 0x65, 0x61, 0x64, 0x79, 0x45, 0x78, 0x69, 0x73, 0x74, 0x73,
	0x10, 0x3d, 0x1a, 0x04, 0xa8, 0x45, 0x90, 0x03, 0x12, 0x26, 0x0a, 0x1c, 0x50, 0x72, 0x6f, 0x64,
	0x75, 0x63, 0x74, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x4e, 0x6f, 0x74, 0x46, 0x6f, 0x75, 0x6e, 0x64, 0x10, 0x3e, 0x1a, 0x04, 0xa8, 0x45, 0x94, 0x03,
	0x12, 0x1e, 0x0a, 0x14, 0x48, 0x61, 0x64, 0x6f, 0x6f, 0x70, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74,
	0x4e, 0x6f, 0x74, 0x46, 0x6f, 0x75, 0x6e, 0x64, 0x10, 0x3f, 0x1a, 0x04, 0xa8, 0x45, 0x94, 0x03,
	0x12, 0x1b, 0x0a, 0x11, 0x4e, 0x6f, 0x74, 0x45, 0x6e, 0x6f, 0x75, 0x67, 0x68, 0x52, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x10, 0x40, 0x1a, 0x04, 0xa8, 0x45, 0x90, 0x03, 0x12, 0x1c, 0x0a,
	0x12, 0x46, 0x6f, 0x72, 0x62, 0x69, 0x64, 0x64, 0x65, 0x6e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x10, 0x41, 0x1a, 0x04, 0xa8, 0x45, 0x93, 0x03, 0x12, 0x22, 0x0a, 0x18, 0x4a,
	0x6f, 0x62, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x41, 0x6c, 0x72, 0x65, 0x61, 0x64,
	0x79, 0x45, 0x78, 0x69, 0x73, 0x74, 0x73, 0x10, 0x42, 0x1a, 0x04, 0xa8, 0x45, 0x90, 0x03, 0x12,
	0x23, 0x0a, 0x19, 0x54, 0x65, 0x6d, 0x70, 0x6f, 0x72, 0x61, 0x6c, 0x53, 0x63, 0x68, 0x65, 0x64,
	0x75, 0x6c, 0x65, 0x4e, 0x6f, 0x74, 0x45, 0x78, 0x69, 0x73, 0x74, 0x73, 0x10, 0x43, 0x1a, 0x04,
	0xa8, 0x45, 0x94, 0x03, 0x1a, 0x04, 0xa0, 0x45, 0xf4, 0x03, 0x42, 0x42, 0x5a, 0x40, 0x67, 0x69,
	0x74, 0x2e, 0x6c, 0x69, 0x61, 0x6e, 0x6a, 0x69, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x63, 0x6c,
	0x6f, 0x75, 0x64, 0x6e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x2f, 0x6b, 0x69, 0x63, 0x2f, 0x6b, 0x69,
	0x63, 0x2d, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f,
	0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2f, 0x62, 0x63, 0x6f, 0x64, 0x65, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_aistudio_bcode_error_proto_rawDescOnce sync.Once
	file_aistudio_bcode_error_proto_rawDescData = file_aistudio_bcode_error_proto_rawDesc
)

func file_aistudio_bcode_error_proto_rawDescGZIP() []byte {
	file_aistudio_bcode_error_proto_rawDescOnce.Do(func() {
		file_aistudio_bcode_error_proto_rawDescData = protoimpl.X.CompressGZIP(file_aistudio_bcode_error_proto_rawDescData)
	})
	return file_aistudio_bcode_error_proto_rawDescData
}

var file_aistudio_bcode_error_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_aistudio_bcode_error_proto_goTypes = []any{
	(ErrorReason)(0), // 0: apis.aistudio.bcode.ErrorReason
}
var file_aistudio_bcode_error_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_aistudio_bcode_error_proto_init() }
func file_aistudio_bcode_error_proto_init() {
	if File_aistudio_bcode_error_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_aistudio_bcode_error_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_aistudio_bcode_error_proto_goTypes,
		DependencyIndexes: file_aistudio_bcode_error_proto_depIdxs,
		EnumInfos:         file_aistudio_bcode_error_proto_enumTypes,
	}.Build()
	File_aistudio_bcode_error_proto = out.File
	file_aistudio_bcode_error_proto_rawDesc = nil
	file_aistudio_bcode_error_proto_goTypes = nil
	file_aistudio_bcode_error_proto_depIdxs = nil
}
