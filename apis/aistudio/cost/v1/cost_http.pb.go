// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.3
// - protoc             v5.29.3
// source: aistudio/cost/v1/cost.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationCostServiceCreateProductSpecification = "/apis.aistudio.cost.v1.CostService/CreateProductSpecification"
const OperationCostServiceDeleteProductSpecification = "/apis.aistudio.cost.v1.CostService/DeleteProductSpecification"
const OperationCostServiceGetProductSpecification = "/apis.aistudio.cost.v1.CostService/GetProductSpecification"
const OperationCostServiceGetProductSpecificationsTotalCost = "/apis.aistudio.cost.v1.CostService/GetProductSpecificationsTotalCost"
const OperationCostServiceListProductSpecifications = "/apis.aistudio.cost.v1.CostService/ListProductSpecifications"
const OperationCostServiceListProductSpecificationsCostDetail = "/apis.aistudio.cost.v1.CostService/ListProductSpecificationsCostDetail"
const OperationCostServiceListProductSpecificationsUsagesForKcsCost = "/apis.aistudio.cost.v1.CostService/ListProductSpecificationsUsagesForKcsCost"
const OperationCostServiceListResourceStatisticsUsages = "/apis.aistudio.cost.v1.CostService/ListResourceStatisticsUsages"
const OperationCostServiceUpdateProductSpecification = "/apis.aistudio.cost.v1.CostService/UpdateProductSpecification"

type CostServiceHTTPServer interface {
	// CreateProductSpecificationCreateProductSpecification 创建产品规格
	CreateProductSpecification(context.Context, *CreateOrUpdateProductSpecificationRequest) (*emptypb.Empty, error)
	// DeleteProductSpecificationDeleteProductSpecification 删除产品规格
	DeleteProductSpecification(context.Context, *DeleteProductSpecificationRequest) (*emptypb.Empty, error)
	// GetProductSpecificationGetProductSpecification 获取产品规格
	GetProductSpecification(context.Context, *GetProductSpecificationRequest) (*ProductSpecification, error)
	// GetProductSpecificationsTotalCostGetProductSpecificationsTotalCost 获取产品规格总成本
	GetProductSpecificationsTotalCost(context.Context, *ListProductSpecificationsCostDetailOption) (*GetProductSpecificationsTotalCostResult, error)
	// ListProductSpecificationsListProductSpecifications 获取产品规格列表
	ListProductSpecifications(context.Context, *ListProductSpecificationsOption) (*ListProductSpecificationsResult, error)
	// ListProductSpecificationsCostDetailListProductSpecificationsCostDetail 获取产品规格成本明细
	ListProductSpecificationsCostDetail(context.Context, *ListProductSpecificationsCostDetailOption) (*ListProductSpecificationsCostDetailResult, error)
	// ListProductSpecificationsUsagesForKcsCostListProductSpecificationsUsagesForKcsCost KcsCost服务专用获取产品规格用量数据
	ListProductSpecificationsUsagesForKcsCost(context.Context, *ListProductSpecificationsUsagesForKcsCostOption) (*ListProductSpecificationsUsagesForKcsCostResult, error)
	// ListResourceStatisticsUsagesListResourceStatisticsUsages 获取资源用量
	ListResourceStatisticsUsages(context.Context, *ListResourceStatisticsUsagesOption) (*ListResourceStatisticsUsagesResult, error)
	// UpdateProductSpecificationUpdateProductSpecification 更新产品规格
	UpdateProductSpecification(context.Context, *CreateOrUpdateProductSpecificationRequest) (*emptypb.Empty, error)
}

func RegisterCostServiceHTTPServer(s *http.Server, srv CostServiceHTTPServer) {
	r := s.Route("/")
	r.POST("/apis/v1/product_specification", _CostService_CreateProductSpecification0_HTTP_Handler(srv))
	r.PUT("/apis/v1/product_specification", _CostService_UpdateProductSpecification0_HTTP_Handler(srv))
	r.DELETE("/apis/v1/product_specification", _CostService_DeleteProductSpecification0_HTTP_Handler(srv))
	r.GET("/apis/v1/product_specification", _CostService_GetProductSpecification0_HTTP_Handler(srv))
	r.GET("/apis/v1/product_specifications", _CostService_ListProductSpecifications0_HTTP_Handler(srv))
	r.GET("/apis/v1/product_specifications/cost_detail", _CostService_ListProductSpecificationsCostDetail0_HTTP_Handler(srv))
	r.GET("/apis/v1/product-specifications/usages-for-kcs-cost", _CostService_ListProductSpecificationsUsagesForKcsCost0_HTTP_Handler(srv))
	r.GET("/apis/v1/product_specifications/total_cost", _CostService_GetProductSpecificationsTotalCost0_HTTP_Handler(srv))
	r.GET("/apis/v1/resource_statistics/usages", _CostService_ListResourceStatisticsUsages0_HTTP_Handler(srv))
}

func _CostService_CreateProductSpecification0_HTTP_Handler(srv CostServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateOrUpdateProductSpecificationRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCostServiceCreateProductSpecification)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateProductSpecification(ctx, req.(*CreateOrUpdateProductSpecificationRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _CostService_UpdateProductSpecification0_HTTP_Handler(srv CostServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateOrUpdateProductSpecificationRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCostServiceUpdateProductSpecification)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateProductSpecification(ctx, req.(*CreateOrUpdateProductSpecificationRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _CostService_DeleteProductSpecification0_HTTP_Handler(srv CostServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteProductSpecificationRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCostServiceDeleteProductSpecification)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteProductSpecification(ctx, req.(*DeleteProductSpecificationRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _CostService_GetProductSpecification0_HTTP_Handler(srv CostServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetProductSpecificationRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCostServiceGetProductSpecification)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetProductSpecification(ctx, req.(*GetProductSpecificationRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ProductSpecification)
		return ctx.Result(200, reply)
	}
}

func _CostService_ListProductSpecifications0_HTTP_Handler(srv CostServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListProductSpecificationsOption
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCostServiceListProductSpecifications)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListProductSpecifications(ctx, req.(*ListProductSpecificationsOption))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListProductSpecificationsResult)
		return ctx.Result(200, reply)
	}
}

func _CostService_ListProductSpecificationsCostDetail0_HTTP_Handler(srv CostServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListProductSpecificationsCostDetailOption
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCostServiceListProductSpecificationsCostDetail)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListProductSpecificationsCostDetail(ctx, req.(*ListProductSpecificationsCostDetailOption))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListProductSpecificationsCostDetailResult)
		return ctx.Result(200, reply)
	}
}

func _CostService_ListProductSpecificationsUsagesForKcsCost0_HTTP_Handler(srv CostServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListProductSpecificationsUsagesForKcsCostOption
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCostServiceListProductSpecificationsUsagesForKcsCost)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListProductSpecificationsUsagesForKcsCost(ctx, req.(*ListProductSpecificationsUsagesForKcsCostOption))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListProductSpecificationsUsagesForKcsCostResult)
		return ctx.Result(200, reply)
	}
}

func _CostService_GetProductSpecificationsTotalCost0_HTTP_Handler(srv CostServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListProductSpecificationsCostDetailOption
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCostServiceGetProductSpecificationsTotalCost)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetProductSpecificationsTotalCost(ctx, req.(*ListProductSpecificationsCostDetailOption))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetProductSpecificationsTotalCostResult)
		return ctx.Result(200, reply)
	}
}

func _CostService_ListResourceStatisticsUsages0_HTTP_Handler(srv CostServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListResourceStatisticsUsagesOption
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCostServiceListResourceStatisticsUsages)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListResourceStatisticsUsages(ctx, req.(*ListResourceStatisticsUsagesOption))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListResourceStatisticsUsagesResult)
		return ctx.Result(200, reply)
	}
}

type CostServiceHTTPClient interface {
	CreateProductSpecification(ctx context.Context, req *CreateOrUpdateProductSpecificationRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	DeleteProductSpecification(ctx context.Context, req *DeleteProductSpecificationRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	GetProductSpecification(ctx context.Context, req *GetProductSpecificationRequest, opts ...http.CallOption) (rsp *ProductSpecification, err error)
	GetProductSpecificationsTotalCost(ctx context.Context, req *ListProductSpecificationsCostDetailOption, opts ...http.CallOption) (rsp *GetProductSpecificationsTotalCostResult, err error)
	ListProductSpecifications(ctx context.Context, req *ListProductSpecificationsOption, opts ...http.CallOption) (rsp *ListProductSpecificationsResult, err error)
	ListProductSpecificationsCostDetail(ctx context.Context, req *ListProductSpecificationsCostDetailOption, opts ...http.CallOption) (rsp *ListProductSpecificationsCostDetailResult, err error)
	ListProductSpecificationsUsagesForKcsCost(ctx context.Context, req *ListProductSpecificationsUsagesForKcsCostOption, opts ...http.CallOption) (rsp *ListProductSpecificationsUsagesForKcsCostResult, err error)
	ListResourceStatisticsUsages(ctx context.Context, req *ListResourceStatisticsUsagesOption, opts ...http.CallOption) (rsp *ListResourceStatisticsUsagesResult, err error)
	UpdateProductSpecification(ctx context.Context, req *CreateOrUpdateProductSpecificationRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
}

type CostServiceHTTPClientImpl struct {
	cc *http.Client
}

func NewCostServiceHTTPClient(client *http.Client) CostServiceHTTPClient {
	return &CostServiceHTTPClientImpl{client}
}

func (c *CostServiceHTTPClientImpl) CreateProductSpecification(ctx context.Context, in *CreateOrUpdateProductSpecificationRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/apis/v1/product_specification"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCostServiceCreateProductSpecification))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *CostServiceHTTPClientImpl) DeleteProductSpecification(ctx context.Context, in *DeleteProductSpecificationRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/apis/v1/product_specification"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationCostServiceDeleteProductSpecification))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *CostServiceHTTPClientImpl) GetProductSpecification(ctx context.Context, in *GetProductSpecificationRequest, opts ...http.CallOption) (*ProductSpecification, error) {
	var out ProductSpecification
	pattern := "/apis/v1/product_specification"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationCostServiceGetProductSpecification))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *CostServiceHTTPClientImpl) GetProductSpecificationsTotalCost(ctx context.Context, in *ListProductSpecificationsCostDetailOption, opts ...http.CallOption) (*GetProductSpecificationsTotalCostResult, error) {
	var out GetProductSpecificationsTotalCostResult
	pattern := "/apis/v1/product_specifications/total_cost"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationCostServiceGetProductSpecificationsTotalCost))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *CostServiceHTTPClientImpl) ListProductSpecifications(ctx context.Context, in *ListProductSpecificationsOption, opts ...http.CallOption) (*ListProductSpecificationsResult, error) {
	var out ListProductSpecificationsResult
	pattern := "/apis/v1/product_specifications"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationCostServiceListProductSpecifications))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *CostServiceHTTPClientImpl) ListProductSpecificationsCostDetail(ctx context.Context, in *ListProductSpecificationsCostDetailOption, opts ...http.CallOption) (*ListProductSpecificationsCostDetailResult, error) {
	var out ListProductSpecificationsCostDetailResult
	pattern := "/apis/v1/product_specifications/cost_detail"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationCostServiceListProductSpecificationsCostDetail))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *CostServiceHTTPClientImpl) ListProductSpecificationsUsagesForKcsCost(ctx context.Context, in *ListProductSpecificationsUsagesForKcsCostOption, opts ...http.CallOption) (*ListProductSpecificationsUsagesForKcsCostResult, error) {
	var out ListProductSpecificationsUsagesForKcsCostResult
	pattern := "/apis/v1/product-specifications/usages-for-kcs-cost"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationCostServiceListProductSpecificationsUsagesForKcsCost))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *CostServiceHTTPClientImpl) ListResourceStatisticsUsages(ctx context.Context, in *ListResourceStatisticsUsagesOption, opts ...http.CallOption) (*ListResourceStatisticsUsagesResult, error) {
	var out ListResourceStatisticsUsagesResult
	pattern := "/apis/v1/resource_statistics/usages"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationCostServiceListResourceStatisticsUsages))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *CostServiceHTTPClientImpl) UpdateProductSpecification(ctx context.Context, in *CreateOrUpdateProductSpecificationRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/apis/v1/product_specification"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCostServiceUpdateProductSpecification))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
