// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v5.29.3
// source: aistudio/cost/v1/cost.proto

package v1

import (
	_ "git.lianjia.com/cloudnative/kic/kic-platform/apis/common"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PriceType int32

const (
	PriceType_FixedUnitPrice PriceType = 0
)

// Enum value maps for PriceType.
var (
	PriceType_name = map[int32]string{
		0: "FixedUnitPrice",
	}
	PriceType_value = map[string]int32{
		"FixedUnitPrice": 0,
	}
)

func (x PriceType) Enum() *PriceType {
	p := new(PriceType)
	*p = x
	return p
}

func (x PriceType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PriceType) Descriptor() protoreflect.EnumDescriptor {
	return file_aistudio_cost_v1_cost_proto_enumTypes[0].Descriptor()
}

func (PriceType) Type() protoreflect.EnumType {
	return &file_aistudio_cost_v1_cost_proto_enumTypes[0]
}

func (x PriceType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PriceType.Descriptor instead.
func (PriceType) EnumDescriptor() ([]byte, []int) {
	return file_aistudio_cost_v1_cost_proto_rawDescGZIP(), []int{0}
}

type UsageType int32

const (
	UsageType_Cumulative UsageType = 0
	UsageType_Count      UsageType = 1
)

// Enum value maps for UsageType.
var (
	UsageType_name = map[int32]string{
		0: "Cumulative",
		1: "Count",
	}
	UsageType_value = map[string]int32{
		"Cumulative": 0,
		"Count":      1,
	}
)

func (x UsageType) Enum() *UsageType {
	p := new(UsageType)
	*p = x
	return p
}

func (x UsageType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UsageType) Descriptor() protoreflect.EnumDescriptor {
	return file_aistudio_cost_v1_cost_proto_enumTypes[1].Descriptor()
}

func (UsageType) Type() protoreflect.EnumType {
	return &file_aistudio_cost_v1_cost_proto_enumTypes[1]
}

func (x UsageType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UsageType.Descriptor instead.
func (UsageType) EnumDescriptor() ([]byte, []int) {
	return file_aistudio_cost_v1_cost_proto_rawDescGZIP(), []int{1}
}

type BillingMode int32

const (
	BillingMode_UsageBilling BillingMode = 0
)

// Enum value maps for BillingMode.
var (
	BillingMode_name = map[int32]string{
		0: "UsageBilling",
	}
	BillingMode_value = map[string]int32{
		"UsageBilling": 0,
	}
)

func (x BillingMode) Enum() *BillingMode {
	p := new(BillingMode)
	*p = x
	return p
}

func (x BillingMode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BillingMode) Descriptor() protoreflect.EnumDescriptor {
	return file_aistudio_cost_v1_cost_proto_enumTypes[2].Descriptor()
}

func (BillingMode) Type() protoreflect.EnumType {
	return &file_aistudio_cost_v1_cost_proto_enumTypes[2]
}

func (x BillingMode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BillingMode.Descriptor instead.
func (BillingMode) EnumDescriptor() ([]byte, []int) {
	return file_aistudio_cost_v1_cost_proto_rawDescGZIP(), []int{2}
}

type BillingCycle int32

const (
	BillingCycle_Hourly BillingCycle = 0
)

// Enum value maps for BillingCycle.
var (
	BillingCycle_name = map[int32]string{
		0: "Hourly",
	}
	BillingCycle_value = map[string]int32{
		"Hourly": 0,
	}
)

func (x BillingCycle) Enum() *BillingCycle {
	p := new(BillingCycle)
	*p = x
	return p
}

func (x BillingCycle) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BillingCycle) Descriptor() protoreflect.EnumDescriptor {
	return file_aistudio_cost_v1_cost_proto_enumTypes[3].Descriptor()
}

func (BillingCycle) Type() protoreflect.EnumType {
	return &file_aistudio_cost_v1_cost_proto_enumTypes[3]
}

func (x BillingCycle) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BillingCycle.Descriptor instead.
func (BillingCycle) EnumDescriptor() ([]byte, []int) {
	return file_aistudio_cost_v1_cost_proto_rawDescGZIP(), []int{3}
}

type WindowType int32

const (
	WindowType_Day   WindowType = 0
	WindowType_Month WindowType = 1
)

// Enum value maps for WindowType.
var (
	WindowType_name = map[int32]string{
		0: "Day",
		1: "Month",
	}
	WindowType_value = map[string]int32{
		"Day":   0,
		"Month": 1,
	}
)

func (x WindowType) Enum() *WindowType {
	p := new(WindowType)
	*p = x
	return p
}

func (x WindowType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (WindowType) Descriptor() protoreflect.EnumDescriptor {
	return file_aistudio_cost_v1_cost_proto_enumTypes[4].Descriptor()
}

func (WindowType) Type() protoreflect.EnumType {
	return &file_aistudio_cost_v1_cost_proto_enumTypes[4]
}

func (x WindowType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use WindowType.Descriptor instead.
func (WindowType) EnumDescriptor() ([]byte, []int) {
	return file_aistudio_cost_v1_cost_proto_rawDescGZIP(), []int{4}
}

type ListProductSpecificationsUsagesForKcsCostOption struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TimePeriod               string `protobuf:"bytes,1,opt,name=timePeriod,proto3" json:"timePeriod,omitempty"` // 时间周期，支持today, yesterday, lastmonth...
	ServiceSpecificationName string `protobuf:"bytes,2,opt,name=serviceSpecificationName,proto3" json:"serviceSpecificationName,omitempty"`
	CurrentPage              int32  `protobuf:"varint,3,opt,name=currentPage,json=current_page,proto3" json:"currentPage,omitempty"`
	PageSize                 int32  `protobuf:"varint,4,opt,name=pageSize,json=page_size,proto3" json:"pageSize,omitempty"`
}

func (x *ListProductSpecificationsUsagesForKcsCostOption) Reset() {
	*x = ListProductSpecificationsUsagesForKcsCostOption{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_cost_v1_cost_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListProductSpecificationsUsagesForKcsCostOption) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListProductSpecificationsUsagesForKcsCostOption) ProtoMessage() {}

func (x *ListProductSpecificationsUsagesForKcsCostOption) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_cost_v1_cost_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListProductSpecificationsUsagesForKcsCostOption.ProtoReflect.Descriptor instead.
func (*ListProductSpecificationsUsagesForKcsCostOption) Descriptor() ([]byte, []int) {
	return file_aistudio_cost_v1_cost_proto_rawDescGZIP(), []int{0}
}

func (x *ListProductSpecificationsUsagesForKcsCostOption) GetTimePeriod() string {
	if x != nil {
		return x.TimePeriod
	}
	return ""
}

func (x *ListProductSpecificationsUsagesForKcsCostOption) GetServiceSpecificationName() string {
	if x != nil {
		return x.ServiceSpecificationName
	}
	return ""
}

func (x *ListProductSpecificationsUsagesForKcsCostOption) GetCurrentPage() int32 {
	if x != nil {
		return x.CurrentPage
	}
	return 0
}

func (x *ListProductSpecificationsUsagesForKcsCostOption) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

type ListProductSpecificationsUsagesForKcsCostResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    int32                    `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message string                   `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data    *KcsCostServiceUsageData `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *ListProductSpecificationsUsagesForKcsCostResult) Reset() {
	*x = ListProductSpecificationsUsagesForKcsCostResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_cost_v1_cost_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListProductSpecificationsUsagesForKcsCostResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListProductSpecificationsUsagesForKcsCostResult) ProtoMessage() {}

func (x *ListProductSpecificationsUsagesForKcsCostResult) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_cost_v1_cost_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListProductSpecificationsUsagesForKcsCostResult.ProtoReflect.Descriptor instead.
func (*ListProductSpecificationsUsagesForKcsCostResult) Descriptor() ([]byte, []int) {
	return file_aistudio_cost_v1_cost_proto_rawDescGZIP(), []int{1}
}

func (x *ListProductSpecificationsUsagesForKcsCostResult) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *ListProductSpecificationsUsagesForKcsCostResult) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ListProductSpecificationsUsagesForKcsCostResult) GetData() *KcsCostServiceUsageData {
	if x != nil {
		return x.Data
	}
	return nil
}

type KcsCostServiceUsageData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CurrentPage int32                  `protobuf:"varint,3,opt,name=currentPage,json=current_page,proto3" json:"currentPage,omitempty"`
	PageSize    int32                  `protobuf:"varint,4,opt,name=pageSize,json=page_size,proto3" json:"pageSize,omitempty"`
	TotalCount  int32                  `protobuf:"varint,5,opt,name=totalCount,json=total_count,proto3" json:"totalCount,omitempty"`
	List        []*KcsCostServiceUsage `protobuf:"bytes,6,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *KcsCostServiceUsageData) Reset() {
	*x = KcsCostServiceUsageData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_cost_v1_cost_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *KcsCostServiceUsageData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KcsCostServiceUsageData) ProtoMessage() {}

func (x *KcsCostServiceUsageData) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_cost_v1_cost_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KcsCostServiceUsageData.ProtoReflect.Descriptor instead.
func (*KcsCostServiceUsageData) Descriptor() ([]byte, []int) {
	return file_aistudio_cost_v1_cost_proto_rawDescGZIP(), []int{2}
}

func (x *KcsCostServiceUsageData) GetCurrentPage() int32 {
	if x != nil {
		return x.CurrentPage
	}
	return 0
}

func (x *KcsCostServiceUsageData) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *KcsCostServiceUsageData) GetTotalCount() int32 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

func (x *KcsCostServiceUsageData) GetList() []*KcsCostServiceUsage {
	if x != nil {
		return x.List
	}
	return nil
}

type KcsCostServiceUsage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ServiceUsageId            string  `protobuf:"bytes,1,opt,name=serviceUsageId,json=service_usage_id,proto3" json:"serviceUsageId,omitempty"`
	ServiceUsageIdDescription string  `protobuf:"bytes,2,opt,name=serviceUsageIdDescription,json=service_usage_id_description,proto3" json:"serviceUsageIdDescription,omitempty"`
	Owner                     string  `protobuf:"bytes,3,opt,name=owner,proto3" json:"owner,omitempty"`
	Usage                     float32 `protobuf:"fixed32,4,opt,name=usage,proto3" json:"usage,omitempty"`
}

func (x *KcsCostServiceUsage) Reset() {
	*x = KcsCostServiceUsage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_cost_v1_cost_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *KcsCostServiceUsage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KcsCostServiceUsage) ProtoMessage() {}

func (x *KcsCostServiceUsage) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_cost_v1_cost_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KcsCostServiceUsage.ProtoReflect.Descriptor instead.
func (*KcsCostServiceUsage) Descriptor() ([]byte, []int) {
	return file_aistudio_cost_v1_cost_proto_rawDescGZIP(), []int{3}
}

func (x *KcsCostServiceUsage) GetServiceUsageId() string {
	if x != nil {
		return x.ServiceUsageId
	}
	return ""
}

func (x *KcsCostServiceUsage) GetServiceUsageIdDescription() string {
	if x != nil {
		return x.ServiceUsageIdDescription
	}
	return ""
}

func (x *KcsCostServiceUsage) GetOwner() string {
	if x != nil {
		return x.Owner
	}
	return ""
}

func (x *KcsCostServiceUsage) GetUsage() float32 {
	if x != nil {
		return x.Usage
	}
	return 0
}

type GetProductSpecificationsTotalCostResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TotalCost float64 `protobuf:"fixed64,1,opt,name=totalCost,proto3" json:"totalCost,omitempty"`
}

func (x *GetProductSpecificationsTotalCostResult) Reset() {
	*x = GetProductSpecificationsTotalCostResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_cost_v1_cost_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetProductSpecificationsTotalCostResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetProductSpecificationsTotalCostResult) ProtoMessage() {}

func (x *GetProductSpecificationsTotalCostResult) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_cost_v1_cost_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetProductSpecificationsTotalCostResult.ProtoReflect.Descriptor instead.
func (*GetProductSpecificationsTotalCostResult) Descriptor() ([]byte, []int) {
	return file_aistudio_cost_v1_cost_proto_rawDescGZIP(), []int{4}
}

func (x *GetProductSpecificationsTotalCostResult) GetTotalCost() float64 {
	if x != nil {
		return x.TotalCost
	}
	return 0
}

type ListProductSpecificationsOption struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page     int32 `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	PageSize int32 `protobuf:"varint,2,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
}

func (x *ListProductSpecificationsOption) Reset() {
	*x = ListProductSpecificationsOption{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_cost_v1_cost_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListProductSpecificationsOption) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListProductSpecificationsOption) ProtoMessage() {}

func (x *ListProductSpecificationsOption) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_cost_v1_cost_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListProductSpecificationsOption.ProtoReflect.Descriptor instead.
func (*ListProductSpecificationsOption) Descriptor() ([]byte, []int) {
	return file_aistudio_cost_v1_cost_proto_rawDescGZIP(), []int{5}
}

func (x *ListProductSpecificationsOption) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListProductSpecificationsOption) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

type ListProductSpecificationsResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total                 int64                   `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	Page                  int32                   `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	PageSize              int32                   `protobuf:"varint,3,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
	ProductSpecifications []*ProductSpecification `protobuf:"bytes,4,rep,name=productSpecifications,proto3" json:"productSpecifications,omitempty"`
}

func (x *ListProductSpecificationsResult) Reset() {
	*x = ListProductSpecificationsResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_cost_v1_cost_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListProductSpecificationsResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListProductSpecificationsResult) ProtoMessage() {}

func (x *ListProductSpecificationsResult) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_cost_v1_cost_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListProductSpecificationsResult.ProtoReflect.Descriptor instead.
func (*ListProductSpecificationsResult) Descriptor() ([]byte, []int) {
	return file_aistudio_cost_v1_cost_proto_rawDescGZIP(), []int{6}
}

func (x *ListProductSpecificationsResult) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ListProductSpecificationsResult) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListProductSpecificationsResult) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListProductSpecificationsResult) GetProductSpecifications() []*ProductSpecification {
	if x != nil {
		return x.ProductSpecifications
	}
	return nil
}

type ProductSpecification struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                         string             `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	ProductName                string             `protobuf:"bytes,2,opt,name=productName,proto3" json:"productName,omitempty"`
	ProductNameCn              string             `protobuf:"bytes,3,opt,name=productNameCn,proto3" json:"productNameCn,omitempty"`
	ServiceSpecificationName   string             `protobuf:"bytes,4,opt,name=serviceSpecificationName,proto3" json:"serviceSpecificationName,omitempty"`
	ServiceSpecificationNameCn string             `protobuf:"bytes,5,opt,name=serviceSpecificationNameCn,proto3" json:"serviceSpecificationNameCn,omitempty"`
	Price                      float32            `protobuf:"fixed32,6,opt,name=price,proto3" json:"price,omitempty"`
	PriceUnit                  string             `protobuf:"bytes,7,opt,name=priceUnit,proto3" json:"priceUnit,omitempty"`
	PriceType                  PriceType          `protobuf:"varint,8,opt,name=priceType,proto3,enum=apis.aistudio.cost.v1.PriceType" json:"priceType,omitempty"`
	UsageType                  UsageType          `protobuf:"varint,9,opt,name=usageType,proto3,enum=apis.aistudio.cost.v1.UsageType" json:"usageType,omitempty"`
	BillingMode                BillingMode        `protobuf:"varint,10,opt,name=billingMode,proto3,enum=apis.aistudio.cost.v1.BillingMode" json:"billingMode,omitempty"`
	BillingCycle               BillingCycle       `protobuf:"varint,11,opt,name=billingCycle,proto3,enum=apis.aistudio.cost.v1.BillingCycle" json:"billingCycle,omitempty"`
	SpecificationExplanation   string             `protobuf:"bytes,12,opt,name=specificationExplanation,proto3" json:"specificationExplanation,omitempty"`
	IsEnabled                  bool               `protobuf:"varint,13,opt,name=isEnabled,proto3" json:"isEnabled,omitempty"`  // 默认为true
	CreateTime                 string             `protobuf:"bytes,14,opt,name=createTime,proto3" json:"createTime,omitempty"` //创建时间
	UpdateTime                 string             `protobuf:"bytes,15,opt,name=updateTime,proto3" json:"updateTime,omitempty"` //更新时间
	EffectiveResource          *EffectiveResource `protobuf:"bytes,16,opt,name=effectiveResource,proto3" json:"effectiveResource,omitempty"`
	EffectiveTimestamp         string             `protobuf:"bytes,17,opt,name=effectiveTimestamp,proto3" json:"effectiveTimestamp,omitempty"` //生效时间戳
}

func (x *ProductSpecification) Reset() {
	*x = ProductSpecification{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_cost_v1_cost_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProductSpecification) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProductSpecification) ProtoMessage() {}

func (x *ProductSpecification) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_cost_v1_cost_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProductSpecification.ProtoReflect.Descriptor instead.
func (*ProductSpecification) Descriptor() ([]byte, []int) {
	return file_aistudio_cost_v1_cost_proto_rawDescGZIP(), []int{7}
}

func (x *ProductSpecification) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ProductSpecification) GetProductName() string {
	if x != nil {
		return x.ProductName
	}
	return ""
}

func (x *ProductSpecification) GetProductNameCn() string {
	if x != nil {
		return x.ProductNameCn
	}
	return ""
}

func (x *ProductSpecification) GetServiceSpecificationName() string {
	if x != nil {
		return x.ServiceSpecificationName
	}
	return ""
}

func (x *ProductSpecification) GetServiceSpecificationNameCn() string {
	if x != nil {
		return x.ServiceSpecificationNameCn
	}
	return ""
}

func (x *ProductSpecification) GetPrice() float32 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *ProductSpecification) GetPriceUnit() string {
	if x != nil {
		return x.PriceUnit
	}
	return ""
}

func (x *ProductSpecification) GetPriceType() PriceType {
	if x != nil {
		return x.PriceType
	}
	return PriceType_FixedUnitPrice
}

func (x *ProductSpecification) GetUsageType() UsageType {
	if x != nil {
		return x.UsageType
	}
	return UsageType_Cumulative
}

func (x *ProductSpecification) GetBillingMode() BillingMode {
	if x != nil {
		return x.BillingMode
	}
	return BillingMode_UsageBilling
}

func (x *ProductSpecification) GetBillingCycle() BillingCycle {
	if x != nil {
		return x.BillingCycle
	}
	return BillingCycle_Hourly
}

func (x *ProductSpecification) GetSpecificationExplanation() string {
	if x != nil {
		return x.SpecificationExplanation
	}
	return ""
}

func (x *ProductSpecification) GetIsEnabled() bool {
	if x != nil {
		return x.IsEnabled
	}
	return false
}

func (x *ProductSpecification) GetCreateTime() string {
	if x != nil {
		return x.CreateTime
	}
	return ""
}

func (x *ProductSpecification) GetUpdateTime() string {
	if x != nil {
		return x.UpdateTime
	}
	return ""
}

func (x *ProductSpecification) GetEffectiveResource() *EffectiveResource {
	if x != nil {
		return x.EffectiveResource
	}
	return nil
}

func (x *ProductSpecification) GetEffectiveTimestamp() string {
	if x != nil {
		return x.EffectiveTimestamp
	}
	return ""
}

type EffectiveResource struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResourceType          string   `protobuf:"bytes,1,opt,name=resourceType,proto3" json:"resourceType,omitempty"`
	ResourceSpecification []string `protobuf:"bytes,2,rep,name=resourceSpecification,proto3" json:"resourceSpecification,omitempty"`
	ExtensionField1Values []string `protobuf:"bytes,3,rep,name=extensionField1Values,proto3" json:"extensionField1Values,omitempty"`
	ExtensionField2Values []string `protobuf:"bytes,4,rep,name=extensionField2Values,proto3" json:"extensionField2Values,omitempty"`
	ExtensionField3Values []string `protobuf:"bytes,5,rep,name=extensionField3Values,proto3" json:"extensionField3Values,omitempty"`
}

func (x *EffectiveResource) Reset() {
	*x = EffectiveResource{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_cost_v1_cost_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EffectiveResource) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EffectiveResource) ProtoMessage() {}

func (x *EffectiveResource) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_cost_v1_cost_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EffectiveResource.ProtoReflect.Descriptor instead.
func (*EffectiveResource) Descriptor() ([]byte, []int) {
	return file_aistudio_cost_v1_cost_proto_rawDescGZIP(), []int{8}
}

func (x *EffectiveResource) GetResourceType() string {
	if x != nil {
		return x.ResourceType
	}
	return ""
}

func (x *EffectiveResource) GetResourceSpecification() []string {
	if x != nil {
		return x.ResourceSpecification
	}
	return nil
}

func (x *EffectiveResource) GetExtensionField1Values() []string {
	if x != nil {
		return x.ExtensionField1Values
	}
	return nil
}

func (x *EffectiveResource) GetExtensionField2Values() []string {
	if x != nil {
		return x.ExtensionField2Values
	}
	return nil
}

func (x *EffectiveResource) GetExtensionField3Values() []string {
	if x != nil {
		return x.ExtensionField3Values
	}
	return nil
}

type CreateOrUpdateProductSpecificationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProductName                string             `protobuf:"bytes,1,opt,name=productName,proto3" json:"productName,omitempty"`
	ProductNameCn              string             `protobuf:"bytes,2,opt,name=productNameCn,proto3" json:"productNameCn,omitempty"`
	ServiceSpecificationName   string             `protobuf:"bytes,3,opt,name=serviceSpecificationName,proto3" json:"serviceSpecificationName,omitempty"`
	ServiceSpecificationNameCn string             `protobuf:"bytes,4,opt,name=serviceSpecificationNameCn,proto3" json:"serviceSpecificationNameCn,omitempty"`
	Price                      float32            `protobuf:"fixed32,5,opt,name=price,proto3" json:"price,omitempty"`
	PriceUnit                  string             `protobuf:"bytes,6,opt,name=priceUnit,proto3" json:"priceUnit,omitempty"`
	PriceType                  PriceType          `protobuf:"varint,7,opt,name=priceType,proto3,enum=apis.aistudio.cost.v1.PriceType" json:"priceType,omitempty"`
	UsageType                  UsageType          `protobuf:"varint,8,opt,name=usageType,proto3,enum=apis.aistudio.cost.v1.UsageType" json:"usageType,omitempty"`
	BillingMode                BillingMode        `protobuf:"varint,9,opt,name=billingMode,proto3,enum=apis.aistudio.cost.v1.BillingMode" json:"billingMode,omitempty"`
	BillingCycle               BillingCycle       `protobuf:"varint,10,opt,name=billingCycle,proto3,enum=apis.aistudio.cost.v1.BillingCycle" json:"billingCycle,omitempty"`
	SpecificationExplanation   string             `protobuf:"bytes,11,opt,name=specificationExplanation,proto3" json:"specificationExplanation,omitempty"`
	IsEnabled                  bool               `protobuf:"varint,12,opt,name=isEnabled,proto3" json:"isEnabled,omitempty"` // 默认为true
	EffectiveResource          *EffectiveResource `protobuf:"bytes,13,opt,name=effectiveResource,proto3" json:"effectiveResource,omitempty"`
	EffectiveTimestamp         string             `protobuf:"bytes,14,opt,name=effectiveTimestamp,proto3" json:"effectiveTimestamp,omitempty"` //生效时间戳
}

func (x *CreateOrUpdateProductSpecificationRequest) Reset() {
	*x = CreateOrUpdateProductSpecificationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_cost_v1_cost_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateOrUpdateProductSpecificationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateOrUpdateProductSpecificationRequest) ProtoMessage() {}

func (x *CreateOrUpdateProductSpecificationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_cost_v1_cost_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateOrUpdateProductSpecificationRequest.ProtoReflect.Descriptor instead.
func (*CreateOrUpdateProductSpecificationRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_cost_v1_cost_proto_rawDescGZIP(), []int{9}
}

func (x *CreateOrUpdateProductSpecificationRequest) GetProductName() string {
	if x != nil {
		return x.ProductName
	}
	return ""
}

func (x *CreateOrUpdateProductSpecificationRequest) GetProductNameCn() string {
	if x != nil {
		return x.ProductNameCn
	}
	return ""
}

func (x *CreateOrUpdateProductSpecificationRequest) GetServiceSpecificationName() string {
	if x != nil {
		return x.ServiceSpecificationName
	}
	return ""
}

func (x *CreateOrUpdateProductSpecificationRequest) GetServiceSpecificationNameCn() string {
	if x != nil {
		return x.ServiceSpecificationNameCn
	}
	return ""
}

func (x *CreateOrUpdateProductSpecificationRequest) GetPrice() float32 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *CreateOrUpdateProductSpecificationRequest) GetPriceUnit() string {
	if x != nil {
		return x.PriceUnit
	}
	return ""
}

func (x *CreateOrUpdateProductSpecificationRequest) GetPriceType() PriceType {
	if x != nil {
		return x.PriceType
	}
	return PriceType_FixedUnitPrice
}

func (x *CreateOrUpdateProductSpecificationRequest) GetUsageType() UsageType {
	if x != nil {
		return x.UsageType
	}
	return UsageType_Cumulative
}

func (x *CreateOrUpdateProductSpecificationRequest) GetBillingMode() BillingMode {
	if x != nil {
		return x.BillingMode
	}
	return BillingMode_UsageBilling
}

func (x *CreateOrUpdateProductSpecificationRequest) GetBillingCycle() BillingCycle {
	if x != nil {
		return x.BillingCycle
	}
	return BillingCycle_Hourly
}

func (x *CreateOrUpdateProductSpecificationRequest) GetSpecificationExplanation() string {
	if x != nil {
		return x.SpecificationExplanation
	}
	return ""
}

func (x *CreateOrUpdateProductSpecificationRequest) GetIsEnabled() bool {
	if x != nil {
		return x.IsEnabled
	}
	return false
}

func (x *CreateOrUpdateProductSpecificationRequest) GetEffectiveResource() *EffectiveResource {
	if x != nil {
		return x.EffectiveResource
	}
	return nil
}

func (x *CreateOrUpdateProductSpecificationRequest) GetEffectiveTimestamp() string {
	if x != nil {
		return x.EffectiveTimestamp
	}
	return ""
}

type DeleteProductSpecificationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProductName              string `protobuf:"bytes,1,opt,name=productName,proto3" json:"productName,omitempty"`
	ServiceSpecificationName string `protobuf:"bytes,2,opt,name=serviceSpecificationName,proto3" json:"serviceSpecificationName,omitempty"`
}

func (x *DeleteProductSpecificationRequest) Reset() {
	*x = DeleteProductSpecificationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_cost_v1_cost_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteProductSpecificationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteProductSpecificationRequest) ProtoMessage() {}

func (x *DeleteProductSpecificationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_cost_v1_cost_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteProductSpecificationRequest.ProtoReflect.Descriptor instead.
func (*DeleteProductSpecificationRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_cost_v1_cost_proto_rawDescGZIP(), []int{10}
}

func (x *DeleteProductSpecificationRequest) GetProductName() string {
	if x != nil {
		return x.ProductName
	}
	return ""
}

func (x *DeleteProductSpecificationRequest) GetServiceSpecificationName() string {
	if x != nil {
		return x.ServiceSpecificationName
	}
	return ""
}

type GetProductSpecificationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProductName              string `protobuf:"bytes,1,opt,name=productName,proto3" json:"productName,omitempty"`
	ServiceSpecificationName string `protobuf:"bytes,2,opt,name=serviceSpecificationName,proto3" json:"serviceSpecificationName,omitempty"`
}

func (x *GetProductSpecificationRequest) Reset() {
	*x = GetProductSpecificationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_cost_v1_cost_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetProductSpecificationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetProductSpecificationRequest) ProtoMessage() {}

func (x *GetProductSpecificationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_cost_v1_cost_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetProductSpecificationRequest.ProtoReflect.Descriptor instead.
func (*GetProductSpecificationRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_cost_v1_cost_proto_rawDescGZIP(), []int{11}
}

func (x *GetProductSpecificationRequest) GetProductName() string {
	if x != nil {
		return x.ProductName
	}
	return ""
}

func (x *GetProductSpecificationRequest) GetServiceSpecificationName() string {
	if x != nil {
		return x.ServiceSpecificationName
	}
	return ""
}

type ListProductSpecificationsCostDetailOption struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ServiceSpecificationName string     `protobuf:"bytes,1,opt,name=serviceSpecificationName,proto3" json:"serviceSpecificationName,omitempty"`
	WindowType               WindowType `protobuf:"varint,2,opt,name=windowType,proto3,enum=apis.aistudio.cost.v1.WindowType" json:"windowType,omitempty"`
	Page                     int32      `protobuf:"varint,3,opt,name=page,proto3" json:"page,omitempty"`
	PageSize                 int32      `protobuf:"varint,4,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
	WorkspaceName            string     `protobuf:"bytes,5,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	ResourceId               string     `protobuf:"bytes,6,opt,name=resourceId,proto3" json:"resourceId,omitempty"`
	Admin                    string     `protobuf:"bytes,7,opt,name=admin,proto3" json:"admin,omitempty"`
	DayTimestampRange        string     `protobuf:"bytes,8,opt,name=dayTimestampRange,proto3" json:"dayTimestampRange,omitempty"`
	MonthTimestampRange      string     `protobuf:"bytes,9,opt,name=monthTimestampRange,proto3" json:"monthTimestampRange,omitempty"`
	ResourceType             string     `protobuf:"bytes,10,opt,name=resourceType,proto3" json:"resourceType,omitempty"`
	ResourceSpecification    string     `protobuf:"bytes,11,opt,name=resourceSpecification,proto3" json:"resourceSpecification,omitempty"`
}

func (x *ListProductSpecificationsCostDetailOption) Reset() {
	*x = ListProductSpecificationsCostDetailOption{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_cost_v1_cost_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListProductSpecificationsCostDetailOption) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListProductSpecificationsCostDetailOption) ProtoMessage() {}

func (x *ListProductSpecificationsCostDetailOption) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_cost_v1_cost_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListProductSpecificationsCostDetailOption.ProtoReflect.Descriptor instead.
func (*ListProductSpecificationsCostDetailOption) Descriptor() ([]byte, []int) {
	return file_aistudio_cost_v1_cost_proto_rawDescGZIP(), []int{12}
}

func (x *ListProductSpecificationsCostDetailOption) GetServiceSpecificationName() string {
	if x != nil {
		return x.ServiceSpecificationName
	}
	return ""
}

func (x *ListProductSpecificationsCostDetailOption) GetWindowType() WindowType {
	if x != nil {
		return x.WindowType
	}
	return WindowType_Day
}

func (x *ListProductSpecificationsCostDetailOption) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListProductSpecificationsCostDetailOption) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListProductSpecificationsCostDetailOption) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *ListProductSpecificationsCostDetailOption) GetResourceId() string {
	if x != nil {
		return x.ResourceId
	}
	return ""
}

func (x *ListProductSpecificationsCostDetailOption) GetAdmin() string {
	if x != nil {
		return x.Admin
	}
	return ""
}

func (x *ListProductSpecificationsCostDetailOption) GetDayTimestampRange() string {
	if x != nil {
		return x.DayTimestampRange
	}
	return ""
}

func (x *ListProductSpecificationsCostDetailOption) GetMonthTimestampRange() string {
	if x != nil {
		return x.MonthTimestampRange
	}
	return ""
}

func (x *ListProductSpecificationsCostDetailOption) GetResourceType() string {
	if x != nil {
		return x.ResourceType
	}
	return ""
}

func (x *ListProductSpecificationsCostDetailOption) GetResourceSpecification() string {
	if x != nil {
		return x.ResourceSpecification
	}
	return ""
}

type ListProductSpecificationsCostDetailResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total                           int64                             `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	Page                            int32                             `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	PageSize                        int32                             `protobuf:"varint,3,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
	ProductSpecificationCostDetails []*ProductSpecificationCostDetail `protobuf:"bytes,4,rep,name=productSpecificationCostDetails,proto3" json:"productSpecificationCostDetails,omitempty"`
}

func (x *ListProductSpecificationsCostDetailResult) Reset() {
	*x = ListProductSpecificationsCostDetailResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_cost_v1_cost_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListProductSpecificationsCostDetailResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListProductSpecificationsCostDetailResult) ProtoMessage() {}

func (x *ListProductSpecificationsCostDetailResult) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_cost_v1_cost_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListProductSpecificationsCostDetailResult.ProtoReflect.Descriptor instead.
func (*ListProductSpecificationsCostDetailResult) Descriptor() ([]byte, []int) {
	return file_aistudio_cost_v1_cost_proto_rawDescGZIP(), []int{13}
}

func (x *ListProductSpecificationsCostDetailResult) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ListProductSpecificationsCostDetailResult) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListProductSpecificationsCostDetailResult) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListProductSpecificationsCostDetailResult) GetProductSpecificationCostDetails() []*ProductSpecificationCostDetail {
	if x != nil {
		return x.ProductSpecificationCostDetails
	}
	return nil
}

type ProductSpecificationCostDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProductNameCn              string  `protobuf:"bytes,1,opt,name=productNameCn,proto3" json:"productNameCn,omitempty"`
	ServiceSpecificationNameCn string  `protobuf:"bytes,2,opt,name=serviceSpecificationNameCn,proto3" json:"serviceSpecificationNameCn,omitempty"`
	BillingMode                string  `protobuf:"bytes,3,opt,name=billingMode,proto3" json:"billingMode,omitempty"`
	Region                     string  `protobuf:"bytes,4,opt,name=region,proto3" json:"region,omitempty"`
	Workspace                  string  `protobuf:"bytes,5,opt,name=workspace,proto3" json:"workspace,omitempty"`
	Usage                      float32 `protobuf:"fixed32,6,opt,name=usage,proto3" json:"usage,omitempty"`
	UsageUnit                  string  `protobuf:"bytes,7,opt,name=usageUnit,proto3" json:"usageUnit,omitempty"`
	Price                      float32 `protobuf:"fixed32,8,opt,name=price,proto3" json:"price,omitempty"`
	PriceUnit                  string  `protobuf:"bytes,9,opt,name=priceUnit,proto3" json:"priceUnit,omitempty"`
	ResourceIdExplanation      string  `protobuf:"bytes,10,opt,name=resourceIdExplanation,proto3" json:"resourceIdExplanation,omitempty"`
	Cost                       float64 `protobuf:"fixed64,11,opt,name=cost,proto3" json:"cost,omitempty"`
	ResourceId                 string  `protobuf:"bytes,12,opt,name=resourceId,proto3" json:"resourceId,omitempty"`
	Admin                      string  `protobuf:"bytes,13,opt,name=admin,proto3" json:"admin,omitempty"`
	ResourceSpecification      string  `protobuf:"bytes,14,opt,name=resourceSpecification,proto3" json:"resourceSpecification,omitempty"` // 资源规格
	ResourceType               string  `protobuf:"bytes,15,opt,name=resourceType,proto3" json:"resourceType,omitempty"`
}

func (x *ProductSpecificationCostDetail) Reset() {
	*x = ProductSpecificationCostDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_cost_v1_cost_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProductSpecificationCostDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProductSpecificationCostDetail) ProtoMessage() {}

func (x *ProductSpecificationCostDetail) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_cost_v1_cost_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProductSpecificationCostDetail.ProtoReflect.Descriptor instead.
func (*ProductSpecificationCostDetail) Descriptor() ([]byte, []int) {
	return file_aistudio_cost_v1_cost_proto_rawDescGZIP(), []int{14}
}

func (x *ProductSpecificationCostDetail) GetProductNameCn() string {
	if x != nil {
		return x.ProductNameCn
	}
	return ""
}

func (x *ProductSpecificationCostDetail) GetServiceSpecificationNameCn() string {
	if x != nil {
		return x.ServiceSpecificationNameCn
	}
	return ""
}

func (x *ProductSpecificationCostDetail) GetBillingMode() string {
	if x != nil {
		return x.BillingMode
	}
	return ""
}

func (x *ProductSpecificationCostDetail) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *ProductSpecificationCostDetail) GetWorkspace() string {
	if x != nil {
		return x.Workspace
	}
	return ""
}

func (x *ProductSpecificationCostDetail) GetUsage() float32 {
	if x != nil {
		return x.Usage
	}
	return 0
}

func (x *ProductSpecificationCostDetail) GetUsageUnit() string {
	if x != nil {
		return x.UsageUnit
	}
	return ""
}

func (x *ProductSpecificationCostDetail) GetPrice() float32 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *ProductSpecificationCostDetail) GetPriceUnit() string {
	if x != nil {
		return x.PriceUnit
	}
	return ""
}

func (x *ProductSpecificationCostDetail) GetResourceIdExplanation() string {
	if x != nil {
		return x.ResourceIdExplanation
	}
	return ""
}

func (x *ProductSpecificationCostDetail) GetCost() float64 {
	if x != nil {
		return x.Cost
	}
	return 0
}

func (x *ProductSpecificationCostDetail) GetResourceId() string {
	if x != nil {
		return x.ResourceId
	}
	return ""
}

func (x *ProductSpecificationCostDetail) GetAdmin() string {
	if x != nil {
		return x.Admin
	}
	return ""
}

func (x *ProductSpecificationCostDetail) GetResourceSpecification() string {
	if x != nil {
		return x.ResourceSpecification
	}
	return ""
}

func (x *ProductSpecificationCostDetail) GetResourceType() string {
	if x != nil {
		return x.ResourceType
	}
	return ""
}

type ListResourceStatisticsUsagesOption struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page                  int32  `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	PageSize              int32  `protobuf:"varint,2,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
	WorkspaceName         string `protobuf:"bytes,3,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	ResourceType          string `protobuf:"bytes,4,opt,name=resourceType,proto3" json:"resourceType,omitempty"`
	ResourceSpecification string `protobuf:"bytes,5,opt,name=resourceSpecification,proto3" json:"resourceSpecification,omitempty"`
	TimestampRange        string `protobuf:"bytes,6,opt,name=timestampRange,proto3" json:"timestampRange,omitempty"`
	ResourceId            string `protobuf:"bytes,7,opt,name=resourceId,proto3" json:"resourceId,omitempty"`
	Admin                 string `protobuf:"bytes,8,opt,name=admin,proto3" json:"admin,omitempty"`
}

func (x *ListResourceStatisticsUsagesOption) Reset() {
	*x = ListResourceStatisticsUsagesOption{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_cost_v1_cost_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListResourceStatisticsUsagesOption) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListResourceStatisticsUsagesOption) ProtoMessage() {}

func (x *ListResourceStatisticsUsagesOption) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_cost_v1_cost_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListResourceStatisticsUsagesOption.ProtoReflect.Descriptor instead.
func (*ListResourceStatisticsUsagesOption) Descriptor() ([]byte, []int) {
	return file_aistudio_cost_v1_cost_proto_rawDescGZIP(), []int{15}
}

func (x *ListResourceStatisticsUsagesOption) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListResourceStatisticsUsagesOption) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListResourceStatisticsUsagesOption) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *ListResourceStatisticsUsagesOption) GetResourceType() string {
	if x != nil {
		return x.ResourceType
	}
	return ""
}

func (x *ListResourceStatisticsUsagesOption) GetResourceSpecification() string {
	if x != nil {
		return x.ResourceSpecification
	}
	return ""
}

func (x *ListResourceStatisticsUsagesOption) GetTimestampRange() string {
	if x != nil {
		return x.TimestampRange
	}
	return ""
}

func (x *ListResourceStatisticsUsagesOption) GetResourceId() string {
	if x != nil {
		return x.ResourceId
	}
	return ""
}

func (x *ListResourceStatisticsUsagesOption) GetAdmin() string {
	if x != nil {
		return x.Admin
	}
	return ""
}

type ListResourceStatisticsUsagesResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total          int64            `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	Page           int32            `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	PageSize       int32            `protobuf:"varint,3,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
	ResourceUsages []*ResourceUsage `protobuf:"bytes,4,rep,name=resourceUsages,proto3" json:"resourceUsages,omitempty"`
}

func (x *ListResourceStatisticsUsagesResult) Reset() {
	*x = ListResourceStatisticsUsagesResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_cost_v1_cost_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListResourceStatisticsUsagesResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListResourceStatisticsUsagesResult) ProtoMessage() {}

func (x *ListResourceStatisticsUsagesResult) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_cost_v1_cost_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListResourceStatisticsUsagesResult.ProtoReflect.Descriptor instead.
func (*ListResourceStatisticsUsagesResult) Descriptor() ([]byte, []int) {
	return file_aistudio_cost_v1_cost_proto_rawDescGZIP(), []int{16}
}

func (x *ListResourceStatisticsUsagesResult) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ListResourceStatisticsUsagesResult) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListResourceStatisticsUsagesResult) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListResourceStatisticsUsagesResult) GetResourceUsages() []*ResourceUsage {
	if x != nil {
		return x.ResourceUsages
	}
	return nil
}

type ResourceUsage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResourceId            string  `protobuf:"bytes,1,opt,name=resourceId,proto3" json:"resourceId,omitempty"`
	ResourceName          string  `protobuf:"bytes,2,opt,name=resourceName,proto3" json:"resourceName,omitempty"`
	Region                string  `protobuf:"bytes,3,opt,name=region,proto3" json:"region,omitempty"`
	Workspace             string  `protobuf:"bytes,4,opt,name=workspace,proto3" json:"workspace,omitempty"`
	Usage                 float32 `protobuf:"fixed32,5,opt,name=usage,proto3" json:"usage,omitempty"`
	ResourceSpecification string  `protobuf:"bytes,6,opt,name=resourceSpecification,proto3" json:"resourceSpecification,omitempty"` // 资源规格
	ResourceType          string  `protobuf:"bytes,7,opt,name=resourceType,proto3" json:"resourceType,omitempty"`
	Admin                 string  `protobuf:"bytes,8,opt,name=admin,proto3" json:"admin,omitempty"`
	ResourceIdExplanation string  `protobuf:"bytes,9,opt,name=resourceIdExplanation,proto3" json:"resourceIdExplanation,omitempty"`
	UsageUnit             string  `protobuf:"bytes,10,opt,name=usageUnit,proto3" json:"usageUnit,omitempty"`
	ExtensionField1       string  `protobuf:"bytes,11,opt,name=extensionField1,proto3" json:"extensionField1,omitempty"` //资源扩展说明字段1
	ExtensionField2       string  `protobuf:"bytes,12,opt,name=extensionField2,proto3" json:"extensionField2,omitempty"` //资源扩展说明字段2
	ExtensionField3       string  `protobuf:"bytes,13,opt,name=extensionField3,proto3" json:"extensionField3,omitempty"` //资源扩展说明字段3
}

func (x *ResourceUsage) Reset() {
	*x = ResourceUsage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_cost_v1_cost_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResourceUsage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResourceUsage) ProtoMessage() {}

func (x *ResourceUsage) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_cost_v1_cost_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResourceUsage.ProtoReflect.Descriptor instead.
func (*ResourceUsage) Descriptor() ([]byte, []int) {
	return file_aistudio_cost_v1_cost_proto_rawDescGZIP(), []int{17}
}

func (x *ResourceUsage) GetResourceId() string {
	if x != nil {
		return x.ResourceId
	}
	return ""
}

func (x *ResourceUsage) GetResourceName() string {
	if x != nil {
		return x.ResourceName
	}
	return ""
}

func (x *ResourceUsage) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *ResourceUsage) GetWorkspace() string {
	if x != nil {
		return x.Workspace
	}
	return ""
}

func (x *ResourceUsage) GetUsage() float32 {
	if x != nil {
		return x.Usage
	}
	return 0
}

func (x *ResourceUsage) GetResourceSpecification() string {
	if x != nil {
		return x.ResourceSpecification
	}
	return ""
}

func (x *ResourceUsage) GetResourceType() string {
	if x != nil {
		return x.ResourceType
	}
	return ""
}

func (x *ResourceUsage) GetAdmin() string {
	if x != nil {
		return x.Admin
	}
	return ""
}

func (x *ResourceUsage) GetResourceIdExplanation() string {
	if x != nil {
		return x.ResourceIdExplanation
	}
	return ""
}

func (x *ResourceUsage) GetUsageUnit() string {
	if x != nil {
		return x.UsageUnit
	}
	return ""
}

func (x *ResourceUsage) GetExtensionField1() string {
	if x != nil {
		return x.ExtensionField1
	}
	return ""
}

func (x *ResourceUsage) GetExtensionField2() string {
	if x != nil {
		return x.ExtensionField2
	}
	return ""
}

func (x *ResourceUsage) GetExtensionField3() string {
	if x != nil {
		return x.ExtensionField3
	}
	return ""
}

var File_aistudio_cost_v1_cost_proto protoreflect.FileDescriptor

var file_aistudio_cost_v1_cost_proto_rawDesc = []byte{
	0x0a, 0x1b, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2f, 0x63, 0x6f, 0x73, 0x74, 0x2f,
	0x76, 0x31, 0x2f, 0x63, 0x6f, 0x73, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x15, 0x61,
	0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x63, 0x6f, 0x73,
	0x74, 0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x13, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xcd, 0x01,
	0x0a, 0x2f, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x53, 0x70, 0x65,
	0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x55, 0x73, 0x61, 0x67, 0x65,
	0x73, 0x46, 0x6f, 0x72, 0x4b, 0x63, 0x73, 0x43, 0x6f, 0x73, 0x74, 0x4f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x1e, 0x0a, 0x0a, 0x74, 0x69, 0x6d, 0x65, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x74, 0x69, 0x6d, 0x65, 0x50, 0x65, 0x72, 0x69, 0x6f,
	0x64, 0x12, 0x3a, 0x0a, 0x18, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x53, 0x70, 0x65, 0x63,
	0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x18, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x53, 0x70, 0x65, 0x63,
	0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a,
	0x0b, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x70, 0x61, 0x67, 0x65,
	0x12, 0x1b, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x22, 0xa3, 0x01,
	0x0a, 0x2f, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x53, 0x70, 0x65,
	0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x55, 0x73, 0x61, 0x67, 0x65,
	0x73, 0x46, 0x6f, 0x72, 0x4b, 0x63, 0x73, 0x43, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12,
	0x42, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e,
	0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x63, 0x6f,
	0x73, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4b, 0x63, 0x73, 0x43, 0x6f, 0x73, 0x74, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x55, 0x73, 0x61, 0x67, 0x65, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64,
	0x61, 0x74, 0x61, 0x22, 0xba, 0x01, 0x0a, 0x17, 0x4b, 0x63, 0x73, 0x43, 0x6f, 0x73, 0x74, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x55, 0x73, 0x61, 0x67, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12,
	0x21, 0x0a, 0x0b, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x67, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x70, 0x61,
	0x67, 0x65, 0x12, 0x1b, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x12,
	0x1f, 0x0a, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x12, 0x3e, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a,
	0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x63,
	0x6f, 0x73, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4b, 0x63, 0x73, 0x43, 0x6f, 0x73, 0x74, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x55, 0x73, 0x61, 0x67, 0x65, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74,
	0x22, 0xac, 0x01, 0x0a, 0x13, 0x4b, 0x63, 0x73, 0x43, 0x6f, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x55, 0x73, 0x61, 0x67, 0x65, 0x12, 0x28, 0x0a, 0x0e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x55, 0x73, 0x61, 0x67, 0x65, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x10, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x75, 0x73, 0x61, 0x67, 0x65, 0x5f,
	0x69, 0x64, 0x12, 0x3f, 0x0a, 0x19, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x55, 0x73, 0x61,
	0x67, 0x65, 0x49, 0x64, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x1c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x75,
	0x73, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x64, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x75, 0x73, 0x61,
	0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x02, 0x52, 0x05, 0x75, 0x73, 0x61, 0x67, 0x65, 0x22,
	0x47, 0x0a, 0x27, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x53, 0x70, 0x65,
	0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x54, 0x6f, 0x74, 0x61, 0x6c,
	0x43, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x43, 0x6f, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x52, 0x09, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x73, 0x74, 0x22, 0x51, 0x0a, 0x1f, 0x4c, 0x69, 0x73, 0x74,
	0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x70,
	0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12,
	0x1a, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x22, 0xca, 0x01, 0x0a, 0x1f,
	0x4c, 0x69, 0x73, 0x74, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x53, 0x70, 0x65, 0x63, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12,
	0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x67,
	0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67,
	0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x61, 0x0a, 0x15, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74,
	0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x04,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74,
	0x75, 0x64, 0x69, 0x6f, 0x2e, 0x63, 0x6f, 0x73, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x6f,
	0x64, 0x75, 0x63, 0x74, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x15, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0xf0, 0x06, 0x0a, 0x14, 0x50, 0x72, 0x6f,
	0x64, 0x75, 0x63, 0x74, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x19, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa,
	0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x1f, 0x52, 0x02, 0x69, 0x64, 0x12, 0x2b, 0x0a, 0x0b,
	0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x3f, 0x52, 0x0b, 0x70, 0x72,
	0x6f, 0x64, 0x75, 0x63, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x70, 0x72, 0x6f,
	0x64, 0x75, 0x63, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x43, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x43, 0x6e, 0x12,
	0x45, 0x0a, 0x18, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x3f, 0x52, 0x18, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x3e, 0x0a, 0x1a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61,
	0x6d, 0x65, 0x43, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x1a, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x4e, 0x61, 0x6d, 0x65, 0x43, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x02, 0x52, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x12, 0x1c, 0x0a, 0x09,
	0x70, 0x72, 0x69, 0x63, 0x65, 0x55, 0x6e, 0x69, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x70, 0x72, 0x69, 0x63, 0x65, 0x55, 0x6e, 0x69, 0x74, 0x12, 0x3e, 0x0a, 0x09, 0x70, 0x72,
	0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e,
	0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x63, 0x6f,
	0x73, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x09, 0x70, 0x72, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x3e, 0x0a, 0x09, 0x75, 0x73,
	0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e,
	0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x63, 0x6f,
	0x73, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x09, 0x75, 0x73, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x44, 0x0a, 0x0b, 0x62, 0x69,
	0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4d, 0x6f, 0x64, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x22, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e,
	0x63, 0x6f, 0x73, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4d,
	0x6f, 0x64, 0x65, 0x52, 0x0b, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4d, 0x6f, 0x64, 0x65,
	0x12, 0x47, 0x0a, 0x0c, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x43, 0x79, 0x63, 0x6c, 0x65,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69,
	0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x63, 0x6f, 0x73, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x42,
	0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x43, 0x79, 0x63, 0x6c, 0x65, 0x52, 0x0c, 0x62, 0x69, 0x6c,
	0x6c, 0x69, 0x6e, 0x67, 0x43, 0x79, 0x63, 0x6c, 0x65, 0x12, 0x3a, 0x0a, 0x18, 0x73, 0x70, 0x65,
	0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x78, 0x70, 0x6c, 0x61, 0x6e,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x18, 0x73, 0x70, 0x65,
	0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x78, 0x70, 0x6c, 0x61, 0x6e,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1c, 0x0a, 0x09, 0x69, 0x73, 0x45, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69, 0x73, 0x45, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d,
	0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d,
	0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x56, 0x0a, 0x11, 0x65, 0x66, 0x66, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28,
	0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x63,
	0x6f, 0x73, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x66, 0x66, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x11, 0x65, 0x66, 0x66, 0x65, 0x63, 0x74,
	0x69, 0x76, 0x65, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x2e, 0x0a, 0x12, 0x65,
	0x66, 0x66, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x65, 0x66, 0x66, 0x65, 0x63, 0x74, 0x69,
	0x76, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x22, 0x8f, 0x02, 0x0a, 0x11,
	0x45, 0x66, 0x66, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x12, 0x22, 0x0a, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x34, 0x0a, 0x15, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x15, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x53, 0x70,
	0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x34, 0x0a, 0x15, 0x65,
	0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x31, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x15, 0x65, 0x78, 0x74, 0x65,
	0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x31, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x73, 0x12, 0x34, 0x0a, 0x15, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x46, 0x69,
	0x65, 0x6c, 0x64, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x15, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x46, 0x69, 0x65, 0x6c, 0x64,
	0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x12, 0x34, 0x0a, 0x15, 0x65, 0x78, 0x74, 0x65, 0x6e,
	0x73, 0x69, 0x6f, 0x6e, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x33, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73,
	0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x15, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f,
	0x6e, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x33, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x22, 0xaa, 0x06,
	0x0a, 0x29, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2b, 0x0a, 0x0b, 0x70,
	0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x3f, 0x52, 0x0b, 0x70, 0x72, 0x6f,
	0x64, 0x75, 0x63, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x70, 0x72, 0x6f, 0x64,
	0x75, 0x63, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x43, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x43, 0x6e, 0x12, 0x45,
	0x0a, 0x18, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x3f, 0x52, 0x18, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x3e, 0x0a, 0x1a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d,
	0x65, 0x43, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x1a, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e,
	0x61, 0x6d, 0x65, 0x43, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x02, 0x52, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x70,
	0x72, 0x69, 0x63, 0x65, 0x55, 0x6e, 0x69, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x70, 0x72, 0x69, 0x63, 0x65, 0x55, 0x6e, 0x69, 0x74, 0x12, 0x3e, 0x0a, 0x09, 0x70, 0x72, 0x69,
	0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x61,
	0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x63, 0x6f, 0x73,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x09,
	0x70, 0x72, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x3e, 0x0a, 0x09, 0x75, 0x73, 0x61,
	0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x61,
	0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x63, 0x6f, 0x73,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x09,
	0x75, 0x73, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x44, 0x0a, 0x0b, 0x62, 0x69, 0x6c,
	0x6c, 0x69, 0x6e, 0x67, 0x4d, 0x6f, 0x64, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x22,
	0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x63,
	0x6f, 0x73, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4d, 0x6f,
	0x64, 0x65, 0x52, 0x0b, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4d, 0x6f, 0x64, 0x65, 0x12,
	0x47, 0x0a, 0x0c, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x43, 0x79, 0x63, 0x6c, 0x65, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73,
	0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x63, 0x6f, 0x73, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x69,
	0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x43, 0x79, 0x63, 0x6c, 0x65, 0x52, 0x0c, 0x62, 0x69, 0x6c, 0x6c,
	0x69, 0x6e, 0x67, 0x43, 0x79, 0x63, 0x6c, 0x65, 0x12, 0x3a, 0x0a, 0x18, 0x73, 0x70, 0x65, 0x63,
	0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x78, 0x70, 0x6c, 0x61, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x18, 0x73, 0x70, 0x65, 0x63,
	0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x78, 0x70, 0x6c, 0x61, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1c, 0x0a, 0x09, 0x69, 0x73, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65,
	0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69, 0x73, 0x45, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x64, 0x12, 0x56, 0x0a, 0x11, 0x65, 0x66, 0x66, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x52,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e,
	0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x63, 0x6f,
	0x73, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x66, 0x66, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x52,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x11, 0x65, 0x66, 0x66, 0x65, 0x63, 0x74, 0x69,
	0x76, 0x65, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x2e, 0x0a, 0x12, 0x65, 0x66,
	0x66, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x65, 0x66, 0x66, 0x65, 0x63, 0x74, 0x69, 0x76,
	0x65, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x22, 0x97, 0x01, 0x0a, 0x21, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x53, 0x70, 0x65, 0x63,
	0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x2b, 0x0a, 0x0b, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x3f,
	0x52, 0x0b, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x45, 0x0a,
	0x18, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x3f, 0x52, 0x18, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x4e, 0x61, 0x6d, 0x65, 0x22, 0x94, 0x01, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x64,
	0x75, 0x63, 0x74, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2b, 0x0a, 0x0b, 0x70, 0x72, 0x6f, 0x64, 0x75,
	0x63, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42,
	0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x3f, 0x52, 0x0b, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x45, 0x0a, 0x18, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x53,
	0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18,
	0x3f, 0x52, 0x18, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xf0, 0x03, 0x0a, 0x29,
	0x4c, 0x69, 0x73, 0x74, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x53, 0x70, 0x65, 0x63, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x43, 0x6f, 0x73, 0x74, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3a, 0x0a, 0x18, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x18, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x41, 0x0a, 0x0a, 0x77, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x54,
	0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x73,
	0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x63, 0x6f, 0x73, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x57, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x77, 0x69,
	0x6e, 0x64, 0x6f, 0x77, 0x54, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08,
	0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08,
	0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1e,
	0x0a, 0x0a, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x12, 0x14,
	0x0a, 0x05, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61,
	0x64, 0x6d, 0x69, 0x6e, 0x12, 0x2c, 0x0a, 0x11, 0x64, 0x61, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x11, 0x64, 0x61, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x61, 0x6e,
	0x67, 0x65, 0x12, 0x30, 0x0a, 0x13, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x13, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x61, 0x6e, 0x67, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x34, 0x0a, 0x15, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xf2,
	0x01, 0x0a, 0x29, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x53, 0x70,
	0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x43, 0x6f, 0x73, 0x74,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x14, 0x0a, 0x05,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69,
	0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69,
	0x7a, 0x65, 0x12, 0x7f, 0x0a, 0x1f, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x53, 0x70, 0x65,
	0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x73, 0x74, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x61, 0x70,
	0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x63, 0x6f, 0x73, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x53, 0x70, 0x65, 0x63, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x73, 0x74, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x52, 0x1f, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x53, 0x70, 0x65, 0x63, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x73, 0x74, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x22, 0xa0, 0x04, 0x0a, 0x1e, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x53,
	0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x73, 0x74,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x24, 0x0a, 0x0d, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63,
	0x74, 0x4e, 0x61, 0x6d, 0x65, 0x43, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x70,
	0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x43, 0x6e, 0x12, 0x3e, 0x0a, 0x1a,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x43, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x1a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x43, 0x6e, 0x12, 0x20, 0x0a, 0x0b,
	0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4d, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x16,
	0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x1c, 0x0a, 0x09, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x77, 0x6f, 0x72, 0x6b, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x75, 0x73, 0x61, 0x67, 0x65, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x02, 0x52, 0x05, 0x75, 0x73, 0x61, 0x67, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x75, 0x73,
	0x61, 0x67, 0x65, 0x55, 0x6e, 0x69, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75,
	0x73, 0x61, 0x67, 0x65, 0x55, 0x6e, 0x69, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63,
	0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x02, 0x52, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x12, 0x1c,
	0x0a, 0x09, 0x70, 0x72, 0x69, 0x63, 0x65, 0x55, 0x6e, 0x69, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x70, 0x72, 0x69, 0x63, 0x65, 0x55, 0x6e, 0x69, 0x74, 0x12, 0x34, 0x0a, 0x15,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x45, 0x78, 0x70, 0x6c, 0x61, 0x6e,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x45, 0x78, 0x70, 0x6c, 0x61, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x73, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x04, 0x63, 0x6f, 0x73, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x49, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x18,
	0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x12, 0x34, 0x0a, 0x15,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x22, 0x0a, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x22, 0xb2, 0x02, 0x0a, 0x22, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63,
	0x73, 0x55, 0x73, 0x61, 0x67, 0x65, 0x73, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a,
	0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67,
	0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x24, 0x0a,
	0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x34, 0x0a, 0x15, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x26, 0x0a,
	0x0e, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x49, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x22, 0xb8, 0x01, 0x0a, 0x22,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x53, 0x74, 0x61, 0x74,
	0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x55, 0x73, 0x61, 0x67, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08,
	0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08,
	0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x4c, 0x0a, 0x0e, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x55, 0x73, 0x61, 0x67, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f,
	0x2e, 0x63, 0x6f, 0x73, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x55, 0x73, 0x61, 0x67, 0x65, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x55, 0x73, 0x61, 0x67, 0x65, 0x73, 0x22, 0xe1, 0x03, 0x0a, 0x0d, 0x52, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x55, 0x73, 0x61, 0x67, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x0c, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06,
	0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65,
	0x67, 0x69, 0x6f, 0x6e, 0x12, 0x1c, 0x0a, 0x09, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x75, 0x73, 0x61, 0x67, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x02, 0x52, 0x05, 0x75, 0x73, 0x61, 0x67, 0x65, 0x12, 0x34, 0x0a, 0x15, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x22,
	0x0a, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x12, 0x34, 0x0a, 0x15, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x45, 0x78, 0x70, 0x6c, 0x61, 0x6e, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x49, 0x64, 0x45, 0x78, 0x70, 0x6c, 0x61, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1c,
	0x0a, 0x09, 0x75, 0x73, 0x61, 0x67, 0x65, 0x55, 0x6e, 0x69, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x75, 0x73, 0x61, 0x67, 0x65, 0x55, 0x6e, 0x69, 0x74, 0x12, 0x28, 0x0a, 0x0f,
	0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x31, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e,
	0x46, 0x69, 0x65, 0x6c, 0x64, 0x31, 0x12, 0x28, 0x0a, 0x0f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73,
	0x69, 0x6f, 0x6e, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x32, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x32,
	0x12, 0x28, 0x0a, 0x0f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x46, 0x69, 0x65,
	0x6c, 0x64, 0x33, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x65, 0x78, 0x74, 0x65, 0x6e,
	0x73, 0x69, 0x6f, 0x6e, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x33, 0x2a, 0x1f, 0x0a, 0x09, 0x50, 0x72,
	0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x0e, 0x46, 0x69, 0x78, 0x65, 0x64,
	0x55, 0x6e, 0x69, 0x74, 0x50, 0x72, 0x69, 0x63, 0x65, 0x10, 0x00, 0x2a, 0x26, 0x0a, 0x09, 0x55,
	0x73, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0e, 0x0a, 0x0a, 0x43, 0x75, 0x6d, 0x75,
	0x6c, 0x61, 0x74, 0x69, 0x76, 0x65, 0x10, 0x00, 0x12, 0x09, 0x0a, 0x05, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x10, 0x01, 0x2a, 0x1f, 0x0a, 0x0b, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4d, 0x6f,
	0x64, 0x65, 0x12, 0x10, 0x0a, 0x0c, 0x55, 0x73, 0x61, 0x67, 0x65, 0x42, 0x69, 0x6c, 0x6c, 0x69,
	0x6e, 0x67, 0x10, 0x00, 0x2a, 0x1a, 0x0a, 0x0c, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x43,
	0x79, 0x63, 0x6c, 0x65, 0x12, 0x0a, 0x0a, 0x06, 0x48, 0x6f, 0x75, 0x72, 0x6c, 0x79, 0x10, 0x00,
	0x2a, 0x20, 0x0a, 0x0a, 0x57, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x54, 0x79, 0x70, 0x65, 0x12, 0x07,
	0x0a, 0x03, 0x44, 0x61, 0x79, 0x10, 0x00, 0x12, 0x09, 0x0a, 0x05, 0x4d, 0x6f, 0x6e, 0x74, 0x68,
	0x10, 0x01, 0x32, 0xc9, 0x0d, 0x0a, 0x0b, 0x43, 0x6f, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x12, 0xa1, 0x01, 0x0a, 0x1a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x72, 0x6f,
	0x64, 0x75, 0x63, 0x74, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x40, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69,
	0x6f, 0x2e, 0x63, 0x6f, 0x73, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x4f, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x53,
	0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x29, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x23, 0x3a, 0x01, 0x2a, 0x22, 0x1e, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31,
	0x2f, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0xa1, 0x01, 0x0a, 0x1a, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x40, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73,
	0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x63, 0x6f, 0x73, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x72, 0x6f, 0x64,
	0x75, 0x63, 0x74, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22,
	0x29, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x23, 0x3a, 0x01, 0x2a, 0x1a, 0x1e, 0x2f, 0x61, 0x70, 0x69,
	0x73, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x73, 0x70, 0x65,
	0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x96, 0x01, 0x0a, 0x1a, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x53, 0x70, 0x65, 0x63,
	0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x38, 0x2e, 0x61, 0x70, 0x69, 0x73,
	0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x63, 0x6f, 0x73, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x53,
	0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x26, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x20, 0x2a, 0x1e, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x72,
	0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0xa5, 0x01, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x64, 0x75,
	0x63, 0x74, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x35, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e,
	0x63, 0x6f, 0x73, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x64, 0x75,
	0x63, 0x74, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69,
	0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x63, 0x6f, 0x73, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x50,
	0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x22, 0x26, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x20, 0x12, 0x1e, 0x2f, 0x61, 0x70,
	0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x73, 0x70,
	0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0xb4, 0x01, 0x0a, 0x19,
	0x4c, 0x69, 0x73, 0x74, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x53, 0x70, 0x65, 0x63, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x36, 0x2e, 0x61, 0x70, 0x69, 0x73,
	0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x63, 0x6f, 0x73, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x53, 0x70, 0x65,
	0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x4f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x1a, 0x36, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69,
	0x6f, 0x2e, 0x63, 0x6f, 0x73, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x72,
	0x6f, 0x64, 0x75, 0x63, 0x74, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x27, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x21, 0x12, 0x1f, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x72, 0x6f, 0x64,
	0x75, 0x63, 0x74, 0x5f, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x12, 0xde, 0x01, 0x0a, 0x23, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x72, 0x6f, 0x64, 0x75,
	0x63, 0x74, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x43, 0x6f, 0x73, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x40, 0x2e, 0x61, 0x70, 0x69,
	0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x63, 0x6f, 0x73, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x53, 0x70,
	0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x43, 0x6f, 0x73, 0x74,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0x40, 0x2e, 0x61,
	0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x63, 0x6f, 0x73,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74,
	0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x43, 0x6f,
	0x73, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x33,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2d, 0x12, 0x2b, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31,
	0x2f, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x63, 0x6f, 0x73, 0x74, 0x5f, 0x64, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x12, 0xf8, 0x01, 0x0a, 0x29, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x72, 0x6f, 0x64,
	0x75, 0x63, 0x74, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x55, 0x73, 0x61, 0x67, 0x65, 0x73, 0x46, 0x6f, 0x72, 0x4b, 0x63, 0x73, 0x43, 0x6f, 0x73,
	0x74, 0x12, 0x46, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69,
	0x6f, 0x2e, 0x63, 0x6f, 0x73, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x72,
	0x6f, 0x64, 0x75, 0x63, 0x74, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x55, 0x73, 0x61, 0x67, 0x65, 0x73, 0x46, 0x6f, 0x72, 0x4b, 0x63, 0x73, 0x43,
	0x6f, 0x73, 0x74, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0x46, 0x2e, 0x61, 0x70, 0x69, 0x73,
	0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x63, 0x6f, 0x73, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x53, 0x70, 0x65,
	0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x55, 0x73, 0x61, 0x67, 0x65,
	0x73, 0x46, 0x6f, 0x72, 0x4b, 0x63, 0x73, 0x43, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x22, 0x3b, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x35, 0x12, 0x33, 0x2f, 0x61, 0x70, 0x69, 0x73,
	0x2f, 0x76, 0x31, 0x2f, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x2d, 0x73, 0x70, 0x65, 0x63,
	0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x75, 0x73, 0x61, 0x67, 0x65,
	0x73, 0x2d, 0x66, 0x6f, 0x72, 0x2d, 0x6b, 0x63, 0x73, 0x2d, 0x63, 0x6f, 0x73, 0x74, 0x12, 0xd9,
	0x01, 0x0a, 0x21, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x53, 0x70, 0x65,
	0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x54, 0x6f, 0x74, 0x61, 0x6c,
	0x43, 0x6f, 0x73, 0x74, 0x12, 0x40, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74,
	0x75, 0x64, 0x69, 0x6f, 0x2e, 0x63, 0x6f, 0x73, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x43, 0x6f, 0x73, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0x3e, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69,
	0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x63, 0x6f, 0x73, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47,
	0x65, 0x74, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x73, 0x74,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x32, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2c, 0x12, 0x2a,
	0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74,
	0x5f, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x73, 0x74, 0x12, 0xc1, 0x01, 0x0a, 0x1c, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x53, 0x74, 0x61, 0x74, 0x69,
	0x73, 0x74, 0x69, 0x63, 0x73, 0x55, 0x73, 0x61, 0x67, 0x65, 0x73, 0x12, 0x39, 0x2e, 0x61, 0x70,
	0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x63, 0x6f, 0x73, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x55, 0x73, 0x61, 0x67, 0x65, 0x73,
	0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0x39, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69,
	0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x63, 0x6f, 0x73, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x53, 0x74, 0x61, 0x74, 0x69,
	0x73, 0x74, 0x69, 0x63, 0x73, 0x55, 0x73, 0x61, 0x67, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x22, 0x2b, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x25, 0x12, 0x23, 0x2f, 0x61, 0x70, 0x69, 0x73,
	0x2f, 0x76, 0x31, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x2f, 0x75, 0x73, 0x61, 0x67, 0x65, 0x73, 0x42, 0x47,
	0x5a, 0x45, 0x67, 0x69, 0x74, 0x2e, 0x6c, 0x69, 0x61, 0x6e, 0x6a, 0x69, 0x61, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x2f, 0x6b, 0x69,
	0x63, 0x2f, 0x6b, 0x69, 0x63, 0x2d, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x61,
	0x70, 0x69, 0x73, 0x2f, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2f, 0x63, 0x6f, 0x73,
	0x74, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_aistudio_cost_v1_cost_proto_rawDescOnce sync.Once
	file_aistudio_cost_v1_cost_proto_rawDescData = file_aistudio_cost_v1_cost_proto_rawDesc
)

func file_aistudio_cost_v1_cost_proto_rawDescGZIP() []byte {
	file_aistudio_cost_v1_cost_proto_rawDescOnce.Do(func() {
		file_aistudio_cost_v1_cost_proto_rawDescData = protoimpl.X.CompressGZIP(file_aistudio_cost_v1_cost_proto_rawDescData)
	})
	return file_aistudio_cost_v1_cost_proto_rawDescData
}

var file_aistudio_cost_v1_cost_proto_enumTypes = make([]protoimpl.EnumInfo, 5)
var file_aistudio_cost_v1_cost_proto_msgTypes = make([]protoimpl.MessageInfo, 18)
var file_aistudio_cost_v1_cost_proto_goTypes = []any{
	(PriceType)(0),    // 0: apis.aistudio.cost.v1.PriceType
	(UsageType)(0),    // 1: apis.aistudio.cost.v1.UsageType
	(BillingMode)(0),  // 2: apis.aistudio.cost.v1.BillingMode
	(BillingCycle)(0), // 3: apis.aistudio.cost.v1.BillingCycle
	(WindowType)(0),   // 4: apis.aistudio.cost.v1.WindowType
	(*ListProductSpecificationsUsagesForKcsCostOption)(nil), // 5: apis.aistudio.cost.v1.ListProductSpecificationsUsagesForKcsCostOption
	(*ListProductSpecificationsUsagesForKcsCostResult)(nil), // 6: apis.aistudio.cost.v1.ListProductSpecificationsUsagesForKcsCostResult
	(*KcsCostServiceUsageData)(nil),                         // 7: apis.aistudio.cost.v1.KcsCostServiceUsageData
	(*KcsCostServiceUsage)(nil),                             // 8: apis.aistudio.cost.v1.KcsCostServiceUsage
	(*GetProductSpecificationsTotalCostResult)(nil),         // 9: apis.aistudio.cost.v1.GetProductSpecificationsTotalCostResult
	(*ListProductSpecificationsOption)(nil),                 // 10: apis.aistudio.cost.v1.ListProductSpecificationsOption
	(*ListProductSpecificationsResult)(nil),                 // 11: apis.aistudio.cost.v1.ListProductSpecificationsResult
	(*ProductSpecification)(nil),                            // 12: apis.aistudio.cost.v1.ProductSpecification
	(*EffectiveResource)(nil),                               // 13: apis.aistudio.cost.v1.EffectiveResource
	(*CreateOrUpdateProductSpecificationRequest)(nil),       // 14: apis.aistudio.cost.v1.CreateOrUpdateProductSpecificationRequest
	(*DeleteProductSpecificationRequest)(nil),               // 15: apis.aistudio.cost.v1.DeleteProductSpecificationRequest
	(*GetProductSpecificationRequest)(nil),                  // 16: apis.aistudio.cost.v1.GetProductSpecificationRequest
	(*ListProductSpecificationsCostDetailOption)(nil),       // 17: apis.aistudio.cost.v1.ListProductSpecificationsCostDetailOption
	(*ListProductSpecificationsCostDetailResult)(nil),       // 18: apis.aistudio.cost.v1.ListProductSpecificationsCostDetailResult
	(*ProductSpecificationCostDetail)(nil),                  // 19: apis.aistudio.cost.v1.ProductSpecificationCostDetail
	(*ListResourceStatisticsUsagesOption)(nil),              // 20: apis.aistudio.cost.v1.ListResourceStatisticsUsagesOption
	(*ListResourceStatisticsUsagesResult)(nil),              // 21: apis.aistudio.cost.v1.ListResourceStatisticsUsagesResult
	(*ResourceUsage)(nil),                                   // 22: apis.aistudio.cost.v1.ResourceUsage
	(*emptypb.Empty)(nil),                                   // 23: google.protobuf.Empty
}
var file_aistudio_cost_v1_cost_proto_depIdxs = []int32{
	7,  // 0: apis.aistudio.cost.v1.ListProductSpecificationsUsagesForKcsCostResult.data:type_name -> apis.aistudio.cost.v1.KcsCostServiceUsageData
	8,  // 1: apis.aistudio.cost.v1.KcsCostServiceUsageData.list:type_name -> apis.aistudio.cost.v1.KcsCostServiceUsage
	12, // 2: apis.aistudio.cost.v1.ListProductSpecificationsResult.productSpecifications:type_name -> apis.aistudio.cost.v1.ProductSpecification
	0,  // 3: apis.aistudio.cost.v1.ProductSpecification.priceType:type_name -> apis.aistudio.cost.v1.PriceType
	1,  // 4: apis.aistudio.cost.v1.ProductSpecification.usageType:type_name -> apis.aistudio.cost.v1.UsageType
	2,  // 5: apis.aistudio.cost.v1.ProductSpecification.billingMode:type_name -> apis.aistudio.cost.v1.BillingMode
	3,  // 6: apis.aistudio.cost.v1.ProductSpecification.billingCycle:type_name -> apis.aistudio.cost.v1.BillingCycle
	13, // 7: apis.aistudio.cost.v1.ProductSpecification.effectiveResource:type_name -> apis.aistudio.cost.v1.EffectiveResource
	0,  // 8: apis.aistudio.cost.v1.CreateOrUpdateProductSpecificationRequest.priceType:type_name -> apis.aistudio.cost.v1.PriceType
	1,  // 9: apis.aistudio.cost.v1.CreateOrUpdateProductSpecificationRequest.usageType:type_name -> apis.aistudio.cost.v1.UsageType
	2,  // 10: apis.aistudio.cost.v1.CreateOrUpdateProductSpecificationRequest.billingMode:type_name -> apis.aistudio.cost.v1.BillingMode
	3,  // 11: apis.aistudio.cost.v1.CreateOrUpdateProductSpecificationRequest.billingCycle:type_name -> apis.aistudio.cost.v1.BillingCycle
	13, // 12: apis.aistudio.cost.v1.CreateOrUpdateProductSpecificationRequest.effectiveResource:type_name -> apis.aistudio.cost.v1.EffectiveResource
	4,  // 13: apis.aistudio.cost.v1.ListProductSpecificationsCostDetailOption.windowType:type_name -> apis.aistudio.cost.v1.WindowType
	19, // 14: apis.aistudio.cost.v1.ListProductSpecificationsCostDetailResult.productSpecificationCostDetails:type_name -> apis.aistudio.cost.v1.ProductSpecificationCostDetail
	22, // 15: apis.aistudio.cost.v1.ListResourceStatisticsUsagesResult.resourceUsages:type_name -> apis.aistudio.cost.v1.ResourceUsage
	14, // 16: apis.aistudio.cost.v1.CostService.CreateProductSpecification:input_type -> apis.aistudio.cost.v1.CreateOrUpdateProductSpecificationRequest
	14, // 17: apis.aistudio.cost.v1.CostService.UpdateProductSpecification:input_type -> apis.aistudio.cost.v1.CreateOrUpdateProductSpecificationRequest
	15, // 18: apis.aistudio.cost.v1.CostService.DeleteProductSpecification:input_type -> apis.aistudio.cost.v1.DeleteProductSpecificationRequest
	16, // 19: apis.aistudio.cost.v1.CostService.GetProductSpecification:input_type -> apis.aistudio.cost.v1.GetProductSpecificationRequest
	10, // 20: apis.aistudio.cost.v1.CostService.ListProductSpecifications:input_type -> apis.aistudio.cost.v1.ListProductSpecificationsOption
	17, // 21: apis.aistudio.cost.v1.CostService.ListProductSpecificationsCostDetail:input_type -> apis.aistudio.cost.v1.ListProductSpecificationsCostDetailOption
	5,  // 22: apis.aistudio.cost.v1.CostService.ListProductSpecificationsUsagesForKcsCost:input_type -> apis.aistudio.cost.v1.ListProductSpecificationsUsagesForKcsCostOption
	17, // 23: apis.aistudio.cost.v1.CostService.GetProductSpecificationsTotalCost:input_type -> apis.aistudio.cost.v1.ListProductSpecificationsCostDetailOption
	20, // 24: apis.aistudio.cost.v1.CostService.ListResourceStatisticsUsages:input_type -> apis.aistudio.cost.v1.ListResourceStatisticsUsagesOption
	23, // 25: apis.aistudio.cost.v1.CostService.CreateProductSpecification:output_type -> google.protobuf.Empty
	23, // 26: apis.aistudio.cost.v1.CostService.UpdateProductSpecification:output_type -> google.protobuf.Empty
	23, // 27: apis.aistudio.cost.v1.CostService.DeleteProductSpecification:output_type -> google.protobuf.Empty
	12, // 28: apis.aistudio.cost.v1.CostService.GetProductSpecification:output_type -> apis.aistudio.cost.v1.ProductSpecification
	11, // 29: apis.aistudio.cost.v1.CostService.ListProductSpecifications:output_type -> apis.aistudio.cost.v1.ListProductSpecificationsResult
	18, // 30: apis.aistudio.cost.v1.CostService.ListProductSpecificationsCostDetail:output_type -> apis.aistudio.cost.v1.ListProductSpecificationsCostDetailResult
	6,  // 31: apis.aistudio.cost.v1.CostService.ListProductSpecificationsUsagesForKcsCost:output_type -> apis.aistudio.cost.v1.ListProductSpecificationsUsagesForKcsCostResult
	9,  // 32: apis.aistudio.cost.v1.CostService.GetProductSpecificationsTotalCost:output_type -> apis.aistudio.cost.v1.GetProductSpecificationsTotalCostResult
	21, // 33: apis.aistudio.cost.v1.CostService.ListResourceStatisticsUsages:output_type -> apis.aistudio.cost.v1.ListResourceStatisticsUsagesResult
	25, // [25:34] is the sub-list for method output_type
	16, // [16:25] is the sub-list for method input_type
	16, // [16:16] is the sub-list for extension type_name
	16, // [16:16] is the sub-list for extension extendee
	0,  // [0:16] is the sub-list for field type_name
}

func init() { file_aistudio_cost_v1_cost_proto_init() }
func file_aistudio_cost_v1_cost_proto_init() {
	if File_aistudio_cost_v1_cost_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_aistudio_cost_v1_cost_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*ListProductSpecificationsUsagesForKcsCostOption); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_cost_v1_cost_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*ListProductSpecificationsUsagesForKcsCostResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_cost_v1_cost_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*KcsCostServiceUsageData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_cost_v1_cost_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*KcsCostServiceUsage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_cost_v1_cost_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*GetProductSpecificationsTotalCostResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_cost_v1_cost_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*ListProductSpecificationsOption); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_cost_v1_cost_proto_msgTypes[6].Exporter = func(v any, i int) any {
			switch v := v.(*ListProductSpecificationsResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_cost_v1_cost_proto_msgTypes[7].Exporter = func(v any, i int) any {
			switch v := v.(*ProductSpecification); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_cost_v1_cost_proto_msgTypes[8].Exporter = func(v any, i int) any {
			switch v := v.(*EffectiveResource); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_cost_v1_cost_proto_msgTypes[9].Exporter = func(v any, i int) any {
			switch v := v.(*CreateOrUpdateProductSpecificationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_cost_v1_cost_proto_msgTypes[10].Exporter = func(v any, i int) any {
			switch v := v.(*DeleteProductSpecificationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_cost_v1_cost_proto_msgTypes[11].Exporter = func(v any, i int) any {
			switch v := v.(*GetProductSpecificationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_cost_v1_cost_proto_msgTypes[12].Exporter = func(v any, i int) any {
			switch v := v.(*ListProductSpecificationsCostDetailOption); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_cost_v1_cost_proto_msgTypes[13].Exporter = func(v any, i int) any {
			switch v := v.(*ListProductSpecificationsCostDetailResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_cost_v1_cost_proto_msgTypes[14].Exporter = func(v any, i int) any {
			switch v := v.(*ProductSpecificationCostDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_cost_v1_cost_proto_msgTypes[15].Exporter = func(v any, i int) any {
			switch v := v.(*ListResourceStatisticsUsagesOption); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_cost_v1_cost_proto_msgTypes[16].Exporter = func(v any, i int) any {
			switch v := v.(*ListResourceStatisticsUsagesResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_cost_v1_cost_proto_msgTypes[17].Exporter = func(v any, i int) any {
			switch v := v.(*ResourceUsage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_aistudio_cost_v1_cost_proto_rawDesc,
			NumEnums:      5,
			NumMessages:   18,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_aistudio_cost_v1_cost_proto_goTypes,
		DependencyIndexes: file_aistudio_cost_v1_cost_proto_depIdxs,
		EnumInfos:         file_aistudio_cost_v1_cost_proto_enumTypes,
		MessageInfos:      file_aistudio_cost_v1_cost_proto_msgTypes,
	}.Build()
	File_aistudio_cost_v1_cost_proto = out.File
	file_aistudio_cost_v1_cost_proto_rawDesc = nil
	file_aistudio_cost_v1_cost_proto_goTypes = nil
	file_aistudio_cost_v1_cost_proto_depIdxs = nil
}
