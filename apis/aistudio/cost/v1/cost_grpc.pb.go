// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v5.29.3
// source: aistudio/cost/v1/cost.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	CostService_CreateProductSpecification_FullMethodName                = "/apis.aistudio.cost.v1.CostService/CreateProductSpecification"
	CostService_UpdateProductSpecification_FullMethodName                = "/apis.aistudio.cost.v1.CostService/UpdateProductSpecification"
	CostService_DeleteProductSpecification_FullMethodName                = "/apis.aistudio.cost.v1.CostService/DeleteProductSpecification"
	CostService_GetProductSpecification_FullMethodName                   = "/apis.aistudio.cost.v1.CostService/GetProductSpecification"
	CostService_ListProductSpecifications_FullMethodName                 = "/apis.aistudio.cost.v1.CostService/ListProductSpecifications"
	CostService_ListProductSpecificationsCostDetail_FullMethodName       = "/apis.aistudio.cost.v1.CostService/ListProductSpecificationsCostDetail"
	CostService_ListProductSpecificationsUsagesForKcsCost_FullMethodName = "/apis.aistudio.cost.v1.CostService/ListProductSpecificationsUsagesForKcsCost"
	CostService_GetProductSpecificationsTotalCost_FullMethodName         = "/apis.aistudio.cost.v1.CostService/GetProductSpecificationsTotalCost"
	CostService_ListResourceStatisticsUsages_FullMethodName              = "/apis.aistudio.cost.v1.CostService/ListResourceStatisticsUsages"
)

// CostServiceClient is the client API for CostService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// CostService成本计费服务
type CostServiceClient interface {
	// CreateProductSpecification 创建产品规格
	CreateProductSpecification(ctx context.Context, in *CreateOrUpdateProductSpecificationRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// UpdateProductSpecification 更新产品规格
	UpdateProductSpecification(ctx context.Context, in *CreateOrUpdateProductSpecificationRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// DeleteProductSpecification 删除产品规格
	DeleteProductSpecification(ctx context.Context, in *DeleteProductSpecificationRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// GetProductSpecification 获取产品规格
	GetProductSpecification(ctx context.Context, in *GetProductSpecificationRequest, opts ...grpc.CallOption) (*ProductSpecification, error)
	// ListProductSpecifications 获取产品规格列表
	ListProductSpecifications(ctx context.Context, in *ListProductSpecificationsOption, opts ...grpc.CallOption) (*ListProductSpecificationsResult, error)
	// ListProductSpecificationsCostDetail 获取产品规格成本明细
	ListProductSpecificationsCostDetail(ctx context.Context, in *ListProductSpecificationsCostDetailOption, opts ...grpc.CallOption) (*ListProductSpecificationsCostDetailResult, error)
	// ListProductSpecificationsUsagesForKcsCost KcsCost服务专用获取产品规格用量数据
	ListProductSpecificationsUsagesForKcsCost(ctx context.Context, in *ListProductSpecificationsUsagesForKcsCostOption, opts ...grpc.CallOption) (*ListProductSpecificationsUsagesForKcsCostResult, error)
	// GetProductSpecificationsTotalCost 获取产品规格总成本
	GetProductSpecificationsTotalCost(ctx context.Context, in *ListProductSpecificationsCostDetailOption, opts ...grpc.CallOption) (*GetProductSpecificationsTotalCostResult, error)
	// ListResourceStatisticsUsages 获取资源用量
	ListResourceStatisticsUsages(ctx context.Context, in *ListResourceStatisticsUsagesOption, opts ...grpc.CallOption) (*ListResourceStatisticsUsagesResult, error)
}

type costServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewCostServiceClient(cc grpc.ClientConnInterface) CostServiceClient {
	return &costServiceClient{cc}
}

func (c *costServiceClient) CreateProductSpecification(ctx context.Context, in *CreateOrUpdateProductSpecificationRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, CostService_CreateProductSpecification_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *costServiceClient) UpdateProductSpecification(ctx context.Context, in *CreateOrUpdateProductSpecificationRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, CostService_UpdateProductSpecification_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *costServiceClient) DeleteProductSpecification(ctx context.Context, in *DeleteProductSpecificationRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, CostService_DeleteProductSpecification_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *costServiceClient) GetProductSpecification(ctx context.Context, in *GetProductSpecificationRequest, opts ...grpc.CallOption) (*ProductSpecification, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ProductSpecification)
	err := c.cc.Invoke(ctx, CostService_GetProductSpecification_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *costServiceClient) ListProductSpecifications(ctx context.Context, in *ListProductSpecificationsOption, opts ...grpc.CallOption) (*ListProductSpecificationsResult, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListProductSpecificationsResult)
	err := c.cc.Invoke(ctx, CostService_ListProductSpecifications_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *costServiceClient) ListProductSpecificationsCostDetail(ctx context.Context, in *ListProductSpecificationsCostDetailOption, opts ...grpc.CallOption) (*ListProductSpecificationsCostDetailResult, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListProductSpecificationsCostDetailResult)
	err := c.cc.Invoke(ctx, CostService_ListProductSpecificationsCostDetail_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *costServiceClient) ListProductSpecificationsUsagesForKcsCost(ctx context.Context, in *ListProductSpecificationsUsagesForKcsCostOption, opts ...grpc.CallOption) (*ListProductSpecificationsUsagesForKcsCostResult, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListProductSpecificationsUsagesForKcsCostResult)
	err := c.cc.Invoke(ctx, CostService_ListProductSpecificationsUsagesForKcsCost_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *costServiceClient) GetProductSpecificationsTotalCost(ctx context.Context, in *ListProductSpecificationsCostDetailOption, opts ...grpc.CallOption) (*GetProductSpecificationsTotalCostResult, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetProductSpecificationsTotalCostResult)
	err := c.cc.Invoke(ctx, CostService_GetProductSpecificationsTotalCost_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *costServiceClient) ListResourceStatisticsUsages(ctx context.Context, in *ListResourceStatisticsUsagesOption, opts ...grpc.CallOption) (*ListResourceStatisticsUsagesResult, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListResourceStatisticsUsagesResult)
	err := c.cc.Invoke(ctx, CostService_ListResourceStatisticsUsages_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CostServiceServer is the server API for CostService service.
// All implementations must embed UnimplementedCostServiceServer
// for forward compatibility
//
// CostService成本计费服务
type CostServiceServer interface {
	// CreateProductSpecification 创建产品规格
	CreateProductSpecification(context.Context, *CreateOrUpdateProductSpecificationRequest) (*emptypb.Empty, error)
	// UpdateProductSpecification 更新产品规格
	UpdateProductSpecification(context.Context, *CreateOrUpdateProductSpecificationRequest) (*emptypb.Empty, error)
	// DeleteProductSpecification 删除产品规格
	DeleteProductSpecification(context.Context, *DeleteProductSpecificationRequest) (*emptypb.Empty, error)
	// GetProductSpecification 获取产品规格
	GetProductSpecification(context.Context, *GetProductSpecificationRequest) (*ProductSpecification, error)
	// ListProductSpecifications 获取产品规格列表
	ListProductSpecifications(context.Context, *ListProductSpecificationsOption) (*ListProductSpecificationsResult, error)
	// ListProductSpecificationsCostDetail 获取产品规格成本明细
	ListProductSpecificationsCostDetail(context.Context, *ListProductSpecificationsCostDetailOption) (*ListProductSpecificationsCostDetailResult, error)
	// ListProductSpecificationsUsagesForKcsCost KcsCost服务专用获取产品规格用量数据
	ListProductSpecificationsUsagesForKcsCost(context.Context, *ListProductSpecificationsUsagesForKcsCostOption) (*ListProductSpecificationsUsagesForKcsCostResult, error)
	// GetProductSpecificationsTotalCost 获取产品规格总成本
	GetProductSpecificationsTotalCost(context.Context, *ListProductSpecificationsCostDetailOption) (*GetProductSpecificationsTotalCostResult, error)
	// ListResourceStatisticsUsages 获取资源用量
	ListResourceStatisticsUsages(context.Context, *ListResourceStatisticsUsagesOption) (*ListResourceStatisticsUsagesResult, error)
	mustEmbedUnimplementedCostServiceServer()
}

// UnimplementedCostServiceServer must be embedded to have forward compatible implementations.
type UnimplementedCostServiceServer struct {
}

func (UnimplementedCostServiceServer) CreateProductSpecification(context.Context, *CreateOrUpdateProductSpecificationRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateProductSpecification not implemented")
}
func (UnimplementedCostServiceServer) UpdateProductSpecification(context.Context, *CreateOrUpdateProductSpecificationRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateProductSpecification not implemented")
}
func (UnimplementedCostServiceServer) DeleteProductSpecification(context.Context, *DeleteProductSpecificationRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteProductSpecification not implemented")
}
func (UnimplementedCostServiceServer) GetProductSpecification(context.Context, *GetProductSpecificationRequest) (*ProductSpecification, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetProductSpecification not implemented")
}
func (UnimplementedCostServiceServer) ListProductSpecifications(context.Context, *ListProductSpecificationsOption) (*ListProductSpecificationsResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListProductSpecifications not implemented")
}
func (UnimplementedCostServiceServer) ListProductSpecificationsCostDetail(context.Context, *ListProductSpecificationsCostDetailOption) (*ListProductSpecificationsCostDetailResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListProductSpecificationsCostDetail not implemented")
}
func (UnimplementedCostServiceServer) ListProductSpecificationsUsagesForKcsCost(context.Context, *ListProductSpecificationsUsagesForKcsCostOption) (*ListProductSpecificationsUsagesForKcsCostResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListProductSpecificationsUsagesForKcsCost not implemented")
}
func (UnimplementedCostServiceServer) GetProductSpecificationsTotalCost(context.Context, *ListProductSpecificationsCostDetailOption) (*GetProductSpecificationsTotalCostResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetProductSpecificationsTotalCost not implemented")
}
func (UnimplementedCostServiceServer) ListResourceStatisticsUsages(context.Context, *ListResourceStatisticsUsagesOption) (*ListResourceStatisticsUsagesResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListResourceStatisticsUsages not implemented")
}
func (UnimplementedCostServiceServer) mustEmbedUnimplementedCostServiceServer() {}

// UnsafeCostServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CostServiceServer will
// result in compilation errors.
type UnsafeCostServiceServer interface {
	mustEmbedUnimplementedCostServiceServer()
}

func RegisterCostServiceServer(s grpc.ServiceRegistrar, srv CostServiceServer) {
	s.RegisterService(&CostService_ServiceDesc, srv)
}

func _CostService_CreateProductSpecification_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateOrUpdateProductSpecificationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CostServiceServer).CreateProductSpecification(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CostService_CreateProductSpecification_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CostServiceServer).CreateProductSpecification(ctx, req.(*CreateOrUpdateProductSpecificationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CostService_UpdateProductSpecification_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateOrUpdateProductSpecificationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CostServiceServer).UpdateProductSpecification(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CostService_UpdateProductSpecification_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CostServiceServer).UpdateProductSpecification(ctx, req.(*CreateOrUpdateProductSpecificationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CostService_DeleteProductSpecification_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteProductSpecificationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CostServiceServer).DeleteProductSpecification(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CostService_DeleteProductSpecification_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CostServiceServer).DeleteProductSpecification(ctx, req.(*DeleteProductSpecificationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CostService_GetProductSpecification_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetProductSpecificationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CostServiceServer).GetProductSpecification(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CostService_GetProductSpecification_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CostServiceServer).GetProductSpecification(ctx, req.(*GetProductSpecificationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CostService_ListProductSpecifications_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListProductSpecificationsOption)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CostServiceServer).ListProductSpecifications(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CostService_ListProductSpecifications_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CostServiceServer).ListProductSpecifications(ctx, req.(*ListProductSpecificationsOption))
	}
	return interceptor(ctx, in, info, handler)
}

func _CostService_ListProductSpecificationsCostDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListProductSpecificationsCostDetailOption)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CostServiceServer).ListProductSpecificationsCostDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CostService_ListProductSpecificationsCostDetail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CostServiceServer).ListProductSpecificationsCostDetail(ctx, req.(*ListProductSpecificationsCostDetailOption))
	}
	return interceptor(ctx, in, info, handler)
}

func _CostService_ListProductSpecificationsUsagesForKcsCost_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListProductSpecificationsUsagesForKcsCostOption)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CostServiceServer).ListProductSpecificationsUsagesForKcsCost(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CostService_ListProductSpecificationsUsagesForKcsCost_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CostServiceServer).ListProductSpecificationsUsagesForKcsCost(ctx, req.(*ListProductSpecificationsUsagesForKcsCostOption))
	}
	return interceptor(ctx, in, info, handler)
}

func _CostService_GetProductSpecificationsTotalCost_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListProductSpecificationsCostDetailOption)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CostServiceServer).GetProductSpecificationsTotalCost(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CostService_GetProductSpecificationsTotalCost_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CostServiceServer).GetProductSpecificationsTotalCost(ctx, req.(*ListProductSpecificationsCostDetailOption))
	}
	return interceptor(ctx, in, info, handler)
}

func _CostService_ListResourceStatisticsUsages_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListResourceStatisticsUsagesOption)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CostServiceServer).ListResourceStatisticsUsages(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CostService_ListResourceStatisticsUsages_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CostServiceServer).ListResourceStatisticsUsages(ctx, req.(*ListResourceStatisticsUsagesOption))
	}
	return interceptor(ctx, in, info, handler)
}

// CostService_ServiceDesc is the grpc.ServiceDesc for CostService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var CostService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "apis.aistudio.cost.v1.CostService",
	HandlerType: (*CostServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateProductSpecification",
			Handler:    _CostService_CreateProductSpecification_Handler,
		},
		{
			MethodName: "UpdateProductSpecification",
			Handler:    _CostService_UpdateProductSpecification_Handler,
		},
		{
			MethodName: "DeleteProductSpecification",
			Handler:    _CostService_DeleteProductSpecification_Handler,
		},
		{
			MethodName: "GetProductSpecification",
			Handler:    _CostService_GetProductSpecification_Handler,
		},
		{
			MethodName: "ListProductSpecifications",
			Handler:    _CostService_ListProductSpecifications_Handler,
		},
		{
			MethodName: "ListProductSpecificationsCostDetail",
			Handler:    _CostService_ListProductSpecificationsCostDetail_Handler,
		},
		{
			MethodName: "ListProductSpecificationsUsagesForKcsCost",
			Handler:    _CostService_ListProductSpecificationsUsagesForKcsCost_Handler,
		},
		{
			MethodName: "GetProductSpecificationsTotalCost",
			Handler:    _CostService_GetProductSpecificationsTotalCost_Handler,
		},
		{
			MethodName: "ListResourceStatisticsUsages",
			Handler:    _CostService_ListResourceStatisticsUsages_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "aistudio/cost/v1/cost.proto",
}
