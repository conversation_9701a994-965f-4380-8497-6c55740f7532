// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v5.29.3
// source: aistudio/workspace/v1/workspace.proto

package v1

import (
	common "git.lianjia.com/cloudnative/kic/kic-platform/apis/common"
	v1 "git.lianjia.com/cloudnative/kic/kic-platform/apis/usercenter/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type WorkspaceBase struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name        string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	DisplayName string                 `protobuf:"bytes,2,opt,name=displayName,proto3" json:"displayName,omitempty"` // 空间别名
	Description string                 `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	Managers    []string               `protobuf:"bytes,4,rep,name=managers,proto3" json:"managers,omitempty"` //管理员
	Members     []string               `protobuf:"bytes,5,rep,name=members,proto3" json:"members,omitempty"`   //成员
	Timestamp   *common.TimestampModel `protobuf:"bytes,6,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	Enabled     bool                   `protobuf:"varint,7,opt,name=enabled,proto3" json:"enabled,omitempty"`      //是否可用 删除空间通过设置为不可用，不删除数据库记录
	ResourceID  string                 `protobuf:"bytes,8,opt,name=resourceID,proto3" json:"resourceID,omitempty"` //资源ID
}

func (x *WorkspaceBase) Reset() {
	*x = WorkspaceBase{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_workspace_v1_workspace_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WorkspaceBase) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WorkspaceBase) ProtoMessage() {}

func (x *WorkspaceBase) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_workspace_v1_workspace_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WorkspaceBase.ProtoReflect.Descriptor instead.
func (*WorkspaceBase) Descriptor() ([]byte, []int) {
	return file_aistudio_workspace_v1_workspace_proto_rawDescGZIP(), []int{0}
}

func (x *WorkspaceBase) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *WorkspaceBase) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *WorkspaceBase) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *WorkspaceBase) GetManagers() []string {
	if x != nil {
		return x.Managers
	}
	return nil
}

func (x *WorkspaceBase) GetMembers() []string {
	if x != nil {
		return x.Members
	}
	return nil
}

func (x *WorkspaceBase) GetTimestamp() *common.TimestampModel {
	if x != nil {
		return x.Timestamp
	}
	return nil
}

func (x *WorkspaceBase) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

func (x *WorkspaceBase) GetResourceID() string {
	if x != nil {
		return x.ResourceID
	}
	return ""
}

type CreateClusterBindingsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName string   `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Clusters      []string `protobuf:"bytes,2,rep,name=clusters,proto3" json:"clusters,omitempty"`
}

func (x *CreateClusterBindingsRequest) Reset() {
	*x = CreateClusterBindingsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_workspace_v1_workspace_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateClusterBindingsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateClusterBindingsRequest) ProtoMessage() {}

func (x *CreateClusterBindingsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_workspace_v1_workspace_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateClusterBindingsRequest.ProtoReflect.Descriptor instead.
func (*CreateClusterBindingsRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_workspace_v1_workspace_proto_rawDescGZIP(), []int{1}
}

func (x *CreateClusterBindingsRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *CreateClusterBindingsRequest) GetClusters() []string {
	if x != nil {
		return x.Clusters
	}
	return nil
}

type DeleteClusterBindingRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *DeleteClusterBindingRequest) Reset() {
	*x = DeleteClusterBindingRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_workspace_v1_workspace_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteClusterBindingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteClusterBindingRequest) ProtoMessage() {}

func (x *DeleteClusterBindingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_workspace_v1_workspace_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteClusterBindingRequest.ProtoReflect.Descriptor instead.
func (*DeleteClusterBindingRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_workspace_v1_workspace_proto_rawDescGZIP(), []int{2}
}

func (x *DeleteClusterBindingRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type ListClusterBindingRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName string `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
}

func (x *ListClusterBindingRequest) Reset() {
	*x = ListClusterBindingRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_workspace_v1_workspace_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListClusterBindingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListClusterBindingRequest) ProtoMessage() {}

func (x *ListClusterBindingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_workspace_v1_workspace_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListClusterBindingRequest.ProtoReflect.Descriptor instead.
func (*ListClusterBindingRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_workspace_v1_workspace_proto_rawDescGZIP(), []int{3}
}

func (x *ListClusterBindingRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

type ListClusterBindingsResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ClusterBindings []*ClusterBinding `protobuf:"bytes,1,rep,name=clusterBindings,proto3" json:"clusterBindings,omitempty"`
}

func (x *ListClusterBindingsResult) Reset() {
	*x = ListClusterBindingsResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_workspace_v1_workspace_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListClusterBindingsResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListClusterBindingsResult) ProtoMessage() {}

func (x *ListClusterBindingsResult) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_workspace_v1_workspace_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListClusterBindingsResult.ProtoReflect.Descriptor instead.
func (*ListClusterBindingsResult) Descriptor() ([]byte, []int) {
	return file_aistudio_workspace_v1_workspace_proto_rawDescGZIP(), []int{4}
}

func (x *ListClusterBindingsResult) GetClusterBindings() []*ClusterBinding {
	if x != nil {
		return x.ClusterBindings
	}
	return nil
}

type ClusterBinding struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName string `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Cluster       string `protobuf:"bytes,2,opt,name=cluster,proto3" json:"cluster,omitempty"`
	ClusterZone   string `protobuf:"bytes,3,opt,name=clusterZone,proto3" json:"clusterZone,omitempty"`
	ClusterRegion string `protobuf:"bytes,4,opt,name=clusterRegion,proto3" json:"clusterRegion,omitempty"`
	ClusterIDC    string `protobuf:"bytes,5,opt,name=clusterIDC,proto3" json:"clusterIDC,omitempty"`
	Namespace     string `protobuf:"bytes,6,opt,name=namespace,proto3" json:"namespace,omitempty"`
	Ready         bool   `protobuf:"varint,7,opt,name=ready,proto3" json:"ready,omitempty"`
}

func (x *ClusterBinding) Reset() {
	*x = ClusterBinding{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_workspace_v1_workspace_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClusterBinding) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClusterBinding) ProtoMessage() {}

func (x *ClusterBinding) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_workspace_v1_workspace_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClusterBinding.ProtoReflect.Descriptor instead.
func (*ClusterBinding) Descriptor() ([]byte, []int) {
	return file_aistudio_workspace_v1_workspace_proto_rawDescGZIP(), []int{5}
}

func (x *ClusterBinding) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *ClusterBinding) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

func (x *ClusterBinding) GetClusterZone() string {
	if x != nil {
		return x.ClusterZone
	}
	return ""
}

func (x *ClusterBinding) GetClusterRegion() string {
	if x != nil {
		return x.ClusterRegion
	}
	return ""
}

func (x *ClusterBinding) GetClusterIDC() string {
	if x != nil {
		return x.ClusterIDC
	}
	return ""
}

func (x *ClusterBinding) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *ClusterBinding) GetReady() bool {
	if x != nil {
		return x.Ready
	}
	return false
}

type GetWorkspaceSettingsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *GetWorkspaceSettingsRequest) Reset() {
	*x = GetWorkspaceSettingsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_workspace_v1_workspace_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetWorkspaceSettingsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWorkspaceSettingsRequest) ProtoMessage() {}

func (x *GetWorkspaceSettingsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_workspace_v1_workspace_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWorkspaceSettingsRequest.ProtoReflect.Descriptor instead.
func (*GetWorkspaceSettingsRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_workspace_v1_workspace_proto_rawDescGZIP(), []int{6}
}

func (x *GetWorkspaceSettingsRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type BindingClustersRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name     string   `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Clusters []string `protobuf:"bytes,2,rep,name=clusters,proto3" json:"clusters,omitempty"`
}

func (x *BindingClustersRequest) Reset() {
	*x = BindingClustersRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_workspace_v1_workspace_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BindingClustersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BindingClustersRequest) ProtoMessage() {}

func (x *BindingClustersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_workspace_v1_workspace_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BindingClustersRequest.ProtoReflect.Descriptor instead.
func (*BindingClustersRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_workspace_v1_workspace_proto_rawDescGZIP(), []int{7}
}

func (x *BindingClustersRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *BindingClustersRequest) GetClusters() []string {
	if x != nil {
		return x.Clusters
	}
	return nil
}

type CheckWorkspaceIsExistRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *CheckWorkspaceIsExistRequest) Reset() {
	*x = CheckWorkspaceIsExistRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_workspace_v1_workspace_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckWorkspaceIsExistRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckWorkspaceIsExistRequest) ProtoMessage() {}

func (x *CheckWorkspaceIsExistRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_workspace_v1_workspace_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckWorkspaceIsExistRequest.ProtoReflect.Descriptor instead.
func (*CheckWorkspaceIsExistRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_workspace_v1_workspace_proto_rawDescGZIP(), []int{8}
}

func (x *CheckWorkspaceIsExistRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type CheckWorkspaceIsExistResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status int32  `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"` // 适配 fcn，0 为校验成功， 422 为校验失败
	Errors string `protobuf:"bytes,2,opt,name=errors,proto3" json:"errors,omitempty"`
}

func (x *CheckWorkspaceIsExistResponse) Reset() {
	*x = CheckWorkspaceIsExistResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_workspace_v1_workspace_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckWorkspaceIsExistResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckWorkspaceIsExistResponse) ProtoMessage() {}

func (x *CheckWorkspaceIsExistResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_workspace_v1_workspace_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckWorkspaceIsExistResponse.ProtoReflect.Descriptor instead.
func (*CheckWorkspaceIsExistResponse) Descriptor() ([]byte, []int) {
	return file_aistudio_workspace_v1_workspace_proto_rawDescGZIP(), []int{9}
}

func (x *CheckWorkspaceIsExistResponse) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *CheckWorkspaceIsExistResponse) GetErrors() string {
	if x != nil {
		return x.Errors
	}
	return ""
}

type WorkspaceDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Base            *WorkspaceBase    `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
	ClusterBindings []*ClusterBinding `protobuf:"bytes,2,rep,name=clusterBindings,proto3" json:"clusterBindings,omitempty"`
}

func (x *WorkspaceDetail) Reset() {
	*x = WorkspaceDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_workspace_v1_workspace_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WorkspaceDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WorkspaceDetail) ProtoMessage() {}

func (x *WorkspaceDetail) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_workspace_v1_workspace_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WorkspaceDetail.ProtoReflect.Descriptor instead.
func (*WorkspaceDetail) Descriptor() ([]byte, []int) {
	return file_aistudio_workspace_v1_workspace_proto_rawDescGZIP(), []int{10}
}

func (x *WorkspaceDetail) GetBase() *WorkspaceBase {
	if x != nil {
		return x.Base
	}
	return nil
}

func (x *WorkspaceDetail) GetClusterBindings() []*ClusterBinding {
	if x != nil {
		return x.ClusterBindings
	}
	return nil
}

type WorkspaceResourceSummary struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CpuUsage       *common.CPUUsage       `protobuf:"bytes,1,opt,name=cpuUsage,proto3" json:"cpuUsage,omitempty"`
	MemoryUsage    *common.MemoryUsage    `protobuf:"bytes,2,opt,name=memoryUsage,proto3" json:"memoryUsage,omitempty"`
	GpuUsage       *common.GPUUsage       `protobuf:"bytes,3,opt,name=gpuUsage,proto3" json:"gpuUsage,omitempty"`
	NodeSummaries  []*common.NodeSummary  `protobuf:"bytes,4,rep,name=nodeSummaries,proto3" json:"nodeSummaries,omitempty"`
	QueueSummaries []*common.QueueSummary `protobuf:"bytes,5,rep,name=queueSummaries,proto3" json:"queueSummaries,omitempty"`
	QueueNum       int32                  `protobuf:"varint,6,opt,name=QueueNum,proto3" json:"QueueNum,omitempty"`
	TaskNum        int32                  `protobuf:"varint,7,opt,name=taskNum,proto3" json:"taskNum,omitempty"`
	TaskInQueue    int32                  `protobuf:"varint,8,opt,name=taskInQueue,proto3" json:"taskInQueue,omitempty"`      //队列中的任务数
	TaskRunning    int32                  `protobuf:"varint,9,opt,name=taskRunning,proto3" json:"taskRunning,omitempty"`      //队列中运行的任务数
	TaskPending    int32                  `protobuf:"varint,10,opt,name=taskPending,proto3" json:"taskPending,omitempty"`     //队列中等待的任务数
	TaskCompleted  int32                  `protobuf:"varint,11,opt,name=taskCompleted,proto3" json:"taskCompleted,omitempty"` //队列中完成的任务数
	TaskUnknown    int32                  `protobuf:"varint,12,opt,name=taskUnknown,proto3" json:"taskUnknown,omitempty"`     //队列中未知的任务数
}

func (x *WorkspaceResourceSummary) Reset() {
	*x = WorkspaceResourceSummary{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_workspace_v1_workspace_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WorkspaceResourceSummary) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WorkspaceResourceSummary) ProtoMessage() {}

func (x *WorkspaceResourceSummary) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_workspace_v1_workspace_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WorkspaceResourceSummary.ProtoReflect.Descriptor instead.
func (*WorkspaceResourceSummary) Descriptor() ([]byte, []int) {
	return file_aistudio_workspace_v1_workspace_proto_rawDescGZIP(), []int{11}
}

func (x *WorkspaceResourceSummary) GetCpuUsage() *common.CPUUsage {
	if x != nil {
		return x.CpuUsage
	}
	return nil
}

func (x *WorkspaceResourceSummary) GetMemoryUsage() *common.MemoryUsage {
	if x != nil {
		return x.MemoryUsage
	}
	return nil
}

func (x *WorkspaceResourceSummary) GetGpuUsage() *common.GPUUsage {
	if x != nil {
		return x.GpuUsage
	}
	return nil
}

func (x *WorkspaceResourceSummary) GetNodeSummaries() []*common.NodeSummary {
	if x != nil {
		return x.NodeSummaries
	}
	return nil
}

func (x *WorkspaceResourceSummary) GetQueueSummaries() []*common.QueueSummary {
	if x != nil {
		return x.QueueSummaries
	}
	return nil
}

func (x *WorkspaceResourceSummary) GetQueueNum() int32 {
	if x != nil {
		return x.QueueNum
	}
	return 0
}

func (x *WorkspaceResourceSummary) GetTaskNum() int32 {
	if x != nil {
		return x.TaskNum
	}
	return 0
}

func (x *WorkspaceResourceSummary) GetTaskInQueue() int32 {
	if x != nil {
		return x.TaskInQueue
	}
	return 0
}

func (x *WorkspaceResourceSummary) GetTaskRunning() int32 {
	if x != nil {
		return x.TaskRunning
	}
	return 0
}

func (x *WorkspaceResourceSummary) GetTaskPending() int32 {
	if x != nil {
		return x.TaskPending
	}
	return 0
}

func (x *WorkspaceResourceSummary) GetTaskCompleted() int32 {
	if x != nil {
		return x.TaskCompleted
	}
	return 0
}

func (x *WorkspaceResourceSummary) GetTaskUnknown() int32 {
	if x != nil {
		return x.TaskUnknown
	}
	return 0
}

type QueueSummary struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskNum       int32 `protobuf:"varint,1,opt,name=taskNum,proto3" json:"taskNum,omitempty"`
	TaskInQueue   int32 `protobuf:"varint,2,opt,name=taskInQueue,proto3" json:"taskInQueue,omitempty"`     //队列中的任务数
	TaskRunning   int32 `protobuf:"varint,3,opt,name=taskRunning,proto3" json:"taskRunning,omitempty"`     //队列中运行的任务数
	TaskPending   int32 `protobuf:"varint,4,opt,name=taskPending,proto3" json:"taskPending,omitempty"`     //队列中等待的任务数
	TaskCompleted int32 `protobuf:"varint,5,opt,name=taskCompleted,proto3" json:"taskCompleted,omitempty"` //队列中完成的任务数
	TaskUnknown   int32 `protobuf:"varint,6,opt,name=taskUnknown,proto3" json:"taskUnknown,omitempty"`     //队列中未知的任务数
	CpuNumUsed    int32 `protobuf:"varint,7,opt,name=cpuNumUsed,proto3" json:"cpuNumUsed,omitempty"`
	MemoryNumUsed int32 `protobuf:"varint,8,opt,name=memoryNumUsed,proto3" json:"memoryNumUsed,omitempty"`
	GpuNumUsed    int32 `protobuf:"varint,9,opt,name=gpuNumUsed,proto3" json:"gpuNumUsed,omitempty"`
}

func (x *QueueSummary) Reset() {
	*x = QueueSummary{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_workspace_v1_workspace_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueueSummary) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueueSummary) ProtoMessage() {}

func (x *QueueSummary) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_workspace_v1_workspace_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueueSummary.ProtoReflect.Descriptor instead.
func (*QueueSummary) Descriptor() ([]byte, []int) {
	return file_aistudio_workspace_v1_workspace_proto_rawDescGZIP(), []int{12}
}

func (x *QueueSummary) GetTaskNum() int32 {
	if x != nil {
		return x.TaskNum
	}
	return 0
}

func (x *QueueSummary) GetTaskInQueue() int32 {
	if x != nil {
		return x.TaskInQueue
	}
	return 0
}

func (x *QueueSummary) GetTaskRunning() int32 {
	if x != nil {
		return x.TaskRunning
	}
	return 0
}

func (x *QueueSummary) GetTaskPending() int32 {
	if x != nil {
		return x.TaskPending
	}
	return 0
}

func (x *QueueSummary) GetTaskCompleted() int32 {
	if x != nil {
		return x.TaskCompleted
	}
	return 0
}

func (x *QueueSummary) GetTaskUnknown() int32 {
	if x != nil {
		return x.TaskUnknown
	}
	return 0
}

func (x *QueueSummary) GetCpuNumUsed() int32 {
	if x != nil {
		return x.CpuNumUsed
	}
	return 0
}

func (x *QueueSummary) GetMemoryNumUsed() int32 {
	if x != nil {
		return x.MemoryNumUsed
	}
	return 0
}

func (x *QueueSummary) GetGpuNumUsed() int32 {
	if x != nil {
		return x.GpuNumUsed
	}
	return 0
}

type WorkspaceUser struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Account    string         `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`
	UserDetail *v1.UserDetail `protobuf:"bytes,2,opt,name=userDetail,proto3" json:"userDetail,omitempty"`
	Role       string         `protobuf:"bytes,3,opt,name=role,proto3" json:"role,omitempty"`
}

func (x *WorkspaceUser) Reset() {
	*x = WorkspaceUser{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_workspace_v1_workspace_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WorkspaceUser) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WorkspaceUser) ProtoMessage() {}

func (x *WorkspaceUser) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_workspace_v1_workspace_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WorkspaceUser.ProtoReflect.Descriptor instead.
func (*WorkspaceUser) Descriptor() ([]byte, []int) {
	return file_aistudio_workspace_v1_workspace_proto_rawDescGZIP(), []int{13}
}

func (x *WorkspaceUser) GetAccount() string {
	if x != nil {
		return x.Account
	}
	return ""
}

func (x *WorkspaceUser) GetUserDetail() *v1.UserDetail {
	if x != nil {
		return x.UserDetail
	}
	return nil
}

func (x *WorkspaceUser) GetRole() string {
	if x != nil {
		return x.Role
	}
	return ""
}

type CreateWorkspaceRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name                    string   `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Description             string   `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
	DisplayName             string   `protobuf:"bytes,3,opt,name=displayName,proto3" json:"displayName,omitempty"`                         //展示的名称
	Managers                []string `protobuf:"bytes,4,rep,name=managers,proto3" json:"managers,omitempty"`                               //管理员
	Members                 []string `protobuf:"bytes,5,rep,name=members,proto3" json:"members,omitempty"`                                 //成员
	AvailabilityRegionNames []string `protobuf:"bytes,6,rep,name=availabilityRegionNames,proto3" json:"availabilityRegionNames,omitempty"` //可用地域
}

func (x *CreateWorkspaceRequest) Reset() {
	*x = CreateWorkspaceRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_workspace_v1_workspace_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateWorkspaceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateWorkspaceRequest) ProtoMessage() {}

func (x *CreateWorkspaceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_workspace_v1_workspace_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateWorkspaceRequest.ProtoReflect.Descriptor instead.
func (*CreateWorkspaceRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_workspace_v1_workspace_proto_rawDescGZIP(), []int{14}
}

func (x *CreateWorkspaceRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateWorkspaceRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CreateWorkspaceRequest) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *CreateWorkspaceRequest) GetManagers() []string {
	if x != nil {
		return x.Managers
	}
	return nil
}

func (x *CreateWorkspaceRequest) GetMembers() []string {
	if x != nil {
		return x.Members
	}
	return nil
}

func (x *CreateWorkspaceRequest) GetAvailabilityRegionNames() []string {
	if x != nil {
		return x.AvailabilityRegionNames
	}
	return nil
}

type UpdateWorkspaceRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name                    string   `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Description             string   `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
	DisplayName             string   `protobuf:"bytes,3,opt,name=displayName,proto3" json:"displayName,omitempty"`                         //展示的名称
	Managers                []string `protobuf:"bytes,4,rep,name=managers,proto3" json:"managers,omitempty"`                               //管理员
	Members                 []string `protobuf:"bytes,5,rep,name=members,proto3" json:"members,omitempty"`                                 //成员
	AvailabilityRegionNames []string `protobuf:"bytes,6,rep,name=availabilityRegionNames,proto3" json:"availabilityRegionNames,omitempty"` //可用地域
}

func (x *UpdateWorkspaceRequest) Reset() {
	*x = UpdateWorkspaceRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_workspace_v1_workspace_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateWorkspaceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateWorkspaceRequest) ProtoMessage() {}

func (x *UpdateWorkspaceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_workspace_v1_workspace_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateWorkspaceRequest.ProtoReflect.Descriptor instead.
func (*UpdateWorkspaceRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_workspace_v1_workspace_proto_rawDescGZIP(), []int{15}
}

func (x *UpdateWorkspaceRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UpdateWorkspaceRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *UpdateWorkspaceRequest) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *UpdateWorkspaceRequest) GetManagers() []string {
	if x != nil {
		return x.Managers
	}
	return nil
}

func (x *UpdateWorkspaceRequest) GetMembers() []string {
	if x != nil {
		return x.Members
	}
	return nil
}

func (x *UpdateWorkspaceRequest) GetAvailabilityRegionNames() []string {
	if x != nil {
		return x.AvailabilityRegionNames
	}
	return nil
}

type DisableWorkspaceRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *DisableWorkspaceRequest) Reset() {
	*x = DisableWorkspaceRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_workspace_v1_workspace_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DisableWorkspaceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DisableWorkspaceRequest) ProtoMessage() {}

func (x *DisableWorkspaceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_workspace_v1_workspace_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DisableWorkspaceRequest.ProtoReflect.Descriptor instead.
func (*DisableWorkspaceRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_workspace_v1_workspace_proto_rawDescGZIP(), []int{16}
}

func (x *DisableWorkspaceRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type GetWorkspaceBaseRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *GetWorkspaceBaseRequest) Reset() {
	*x = GetWorkspaceBaseRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_workspace_v1_workspace_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetWorkspaceBaseRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWorkspaceBaseRequest) ProtoMessage() {}

func (x *GetWorkspaceBaseRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_workspace_v1_workspace_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWorkspaceBaseRequest.ProtoReflect.Descriptor instead.
func (*GetWorkspaceBaseRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_workspace_v1_workspace_proto_rawDescGZIP(), []int{17}
}

func (x *GetWorkspaceBaseRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type GetWorkspaceResourceSummaryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *GetWorkspaceResourceSummaryRequest) Reset() {
	*x = GetWorkspaceResourceSummaryRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_workspace_v1_workspace_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetWorkspaceResourceSummaryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWorkspaceResourceSummaryRequest) ProtoMessage() {}

func (x *GetWorkspaceResourceSummaryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_workspace_v1_workspace_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWorkspaceResourceSummaryRequest.ProtoReflect.Descriptor instead.
func (*GetWorkspaceResourceSummaryRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_workspace_v1_workspace_proto_rawDescGZIP(), []int{18}
}

func (x *GetWorkspaceResourceSummaryRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type GetWorkspaceDetailRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *GetWorkspaceDetailRequest) Reset() {
	*x = GetWorkspaceDetailRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_workspace_v1_workspace_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetWorkspaceDetailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWorkspaceDetailRequest) ProtoMessage() {}

func (x *GetWorkspaceDetailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_workspace_v1_workspace_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWorkspaceDetailRequest.ProtoReflect.Descriptor instead.
func (*GetWorkspaceDetailRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_workspace_v1_workspace_proto_rawDescGZIP(), []int{19}
}

func (x *GetWorkspaceDetailRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type ListOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Prefix        string `protobuf:"bytes,1,opt,name=prefix,proto3" json:"prefix,omitempty"` //空间名模糊使用
	Account       string `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty"`
	DisplayClosed bool   `protobuf:"varint,3,opt,name=displayClosed,proto3" json:"displayClosed,omitempty"` //是否展示关闭的空间
}

func (x *ListOptions) Reset() {
	*x = ListOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_workspace_v1_workspace_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListOptions) ProtoMessage() {}

func (x *ListOptions) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_workspace_v1_workspace_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListOptions.ProtoReflect.Descriptor instead.
func (*ListOptions) Descriptor() ([]byte, []int) {
	return file_aistudio_workspace_v1_workspace_proto_rawDescGZIP(), []int{20}
}

func (x *ListOptions) GetPrefix() string {
	if x != nil {
		return x.Prefix
	}
	return ""
}

func (x *ListOptions) GetAccount() string {
	if x != nil {
		return x.Account
	}
	return ""
}

func (x *ListOptions) GetDisplayClosed() bool {
	if x != nil {
		return x.DisplayClosed
	}
	return false
}

type ListWorkspaceResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Workspaces []*WorkspaceBase `protobuf:"bytes,1,rep,name=workspaces,proto3" json:"workspaces,omitempty"`
}

func (x *ListWorkspaceResult) Reset() {
	*x = ListWorkspaceResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_workspace_v1_workspace_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListWorkspaceResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListWorkspaceResult) ProtoMessage() {}

func (x *ListWorkspaceResult) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_workspace_v1_workspace_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListWorkspaceResult.ProtoReflect.Descriptor instead.
func (*ListWorkspaceResult) Descriptor() ([]byte, []int) {
	return file_aistudio_workspace_v1_workspace_proto_rawDescGZIP(), []int{21}
}

func (x *ListWorkspaceResult) GetWorkspaces() []*WorkspaceBase {
	if x != nil {
		return x.Workspaces
	}
	return nil
}

type AvailabilityRegionSettings struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Region                  string                   `protobuf:"bytes,1,opt,name=region,proto3" json:"region,omitempty"`
	Enabled                 bool                     `protobuf:"varint,2,opt,name=enabled,proto3" json:"enabled,omitempty"`
	ImageRepositorySettings *ImageRepositorySettings `protobuf:"bytes,3,opt,name=imageRepositorySettings,proto3" json:"imageRepositorySettings,omitempty"`
}

func (x *AvailabilityRegionSettings) Reset() {
	*x = AvailabilityRegionSettings{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_workspace_v1_workspace_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AvailabilityRegionSettings) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AvailabilityRegionSettings) ProtoMessage() {}

func (x *AvailabilityRegionSettings) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_workspace_v1_workspace_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AvailabilityRegionSettings.ProtoReflect.Descriptor instead.
func (*AvailabilityRegionSettings) Descriptor() ([]byte, []int) {
	return file_aistudio_workspace_v1_workspace_proto_rawDescGZIP(), []int{22}
}

func (x *AvailabilityRegionSettings) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *AvailabilityRegionSettings) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

func (x *AvailabilityRegionSettings) GetImageRepositorySettings() *ImageRepositorySettings {
	if x != nil {
		return x.ImageRepositorySettings
	}
	return nil
}

type ImageRepositorySettings struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Enabled     bool   `protobuf:"varint,1,opt,name=enabled,proto3" json:"enabled,omitempty"` //是否可用
	ProjectName string `protobuf:"bytes,2,opt,name=projectName,proto3" json:"projectName,omitempty"`
	Private     bool   `protobuf:"varint,3,opt,name=private,proto3" json:"private,omitempty"`
}

func (x *ImageRepositorySettings) Reset() {
	*x = ImageRepositorySettings{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_workspace_v1_workspace_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ImageRepositorySettings) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImageRepositorySettings) ProtoMessage() {}

func (x *ImageRepositorySettings) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_workspace_v1_workspace_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImageRepositorySettings.ProtoReflect.Descriptor instead.
func (*ImageRepositorySettings) Descriptor() ([]byte, []int) {
	return file_aistudio_workspace_v1_workspace_proto_rawDescGZIP(), []int{23}
}

func (x *ImageRepositorySettings) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

func (x *ImageRepositorySettings) GetProjectName() string {
	if x != nil {
		return x.ProjectName
	}
	return ""
}

func (x *ImageRepositorySettings) GetPrivate() bool {
	if x != nil {
		return x.Private
	}
	return false
}

type AddWorkspaceUsersRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName string   `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Account       []string `protobuf:"bytes,2,rep,name=account,proto3" json:"account,omitempty"`
	Role          string   `protobuf:"bytes,3,opt,name=role,proto3" json:"role,omitempty"`
	UserType      string   `protobuf:"bytes,4,opt,name=userType,proto3" json:"userType,omitempty"`
}

func (x *AddWorkspaceUsersRequest) Reset() {
	*x = AddWorkspaceUsersRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_workspace_v1_workspace_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddWorkspaceUsersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddWorkspaceUsersRequest) ProtoMessage() {}

func (x *AddWorkspaceUsersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_workspace_v1_workspace_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddWorkspaceUsersRequest.ProtoReflect.Descriptor instead.
func (*AddWorkspaceUsersRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_workspace_v1_workspace_proto_rawDescGZIP(), []int{24}
}

func (x *AddWorkspaceUsersRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *AddWorkspaceUsersRequest) GetAccount() []string {
	if x != nil {
		return x.Account
	}
	return nil
}

func (x *AddWorkspaceUsersRequest) GetRole() string {
	if x != nil {
		return x.Role
	}
	return ""
}

func (x *AddWorkspaceUsersRequest) GetUserType() string {
	if x != nil {
		return x.UserType
	}
	return ""
}

type DeleteWorkspaceUserRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName string `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Account       string `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty"`
	Role          string `protobuf:"bytes,3,opt,name=role,proto3" json:"role,omitempty"`
	UserType      string `protobuf:"bytes,4,opt,name=userType,proto3" json:"userType,omitempty"`
}

func (x *DeleteWorkspaceUserRequest) Reset() {
	*x = DeleteWorkspaceUserRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_workspace_v1_workspace_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteWorkspaceUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteWorkspaceUserRequest) ProtoMessage() {}

func (x *DeleteWorkspaceUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_workspace_v1_workspace_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteWorkspaceUserRequest.ProtoReflect.Descriptor instead.
func (*DeleteWorkspaceUserRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_workspace_v1_workspace_proto_rawDescGZIP(), []int{25}
}

func (x *DeleteWorkspaceUserRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *DeleteWorkspaceUserRequest) GetAccount() string {
	if x != nil {
		return x.Account
	}
	return ""
}

func (x *DeleteWorkspaceUserRequest) GetRole() string {
	if x != nil {
		return x.Role
	}
	return ""
}

func (x *DeleteWorkspaceUserRequest) GetUserType() string {
	if x != nil {
		return x.UserType
	}
	return ""
}

type ListWorkspaceMembersRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName string `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Prefix        string `protobuf:"bytes,2,opt,name=prefix,proto3" json:"prefix,omitempty"` // 用户 account 模糊搜索
	UserType      string `protobuf:"bytes,3,opt,name=userType,proto3" json:"userType,omitempty"`
}

func (x *ListWorkspaceMembersRequest) Reset() {
	*x = ListWorkspaceMembersRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_workspace_v1_workspace_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListWorkspaceMembersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListWorkspaceMembersRequest) ProtoMessage() {}

func (x *ListWorkspaceMembersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_workspace_v1_workspace_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListWorkspaceMembersRequest.ProtoReflect.Descriptor instead.
func (*ListWorkspaceMembersRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_workspace_v1_workspace_proto_rawDescGZIP(), []int{26}
}

func (x *ListWorkspaceMembersRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *ListWorkspaceMembersRequest) GetPrefix() string {
	if x != nil {
		return x.Prefix
	}
	return ""
}

func (x *ListWorkspaceMembersRequest) GetUserType() string {
	if x != nil {
		return x.UserType
	}
	return ""
}

type ListWorkspaceMembersResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceUsers []*WorkspaceUser `protobuf:"bytes,1,rep,name=workspaceUsers,proto3" json:"workspaceUsers,omitempty"`
}

func (x *ListWorkspaceMembersResponse) Reset() {
	*x = ListWorkspaceMembersResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_workspace_v1_workspace_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListWorkspaceMembersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListWorkspaceMembersResponse) ProtoMessage() {}

func (x *ListWorkspaceMembersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_workspace_v1_workspace_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListWorkspaceMembersResponse.ProtoReflect.Descriptor instead.
func (*ListWorkspaceMembersResponse) Descriptor() ([]byte, []int) {
	return file_aistudio_workspace_v1_workspace_proto_rawDescGZIP(), []int{27}
}

func (x *ListWorkspaceMembersResponse) GetWorkspaceUsers() []*WorkspaceUser {
	if x != nil {
		return x.WorkspaceUsers
	}
	return nil
}

type ChangeWorkspaceUserRoleRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName string `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Account       string `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty"`
	Role          string `protobuf:"bytes,3,opt,name=role,proto3" json:"role,omitempty"`
	UserType      string `protobuf:"bytes,4,opt,name=userType,proto3" json:"userType,omitempty"`
}

func (x *ChangeWorkspaceUserRoleRequest) Reset() {
	*x = ChangeWorkspaceUserRoleRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_workspace_v1_workspace_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChangeWorkspaceUserRoleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChangeWorkspaceUserRoleRequest) ProtoMessage() {}

func (x *ChangeWorkspaceUserRoleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_workspace_v1_workspace_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChangeWorkspaceUserRoleRequest.ProtoReflect.Descriptor instead.
func (*ChangeWorkspaceUserRoleRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_workspace_v1_workspace_proto_rawDescGZIP(), []int{28}
}

func (x *ChangeWorkspaceUserRoleRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *ChangeWorkspaceUserRoleRequest) GetAccount() string {
	if x != nil {
		return x.Account
	}
	return ""
}

func (x *ChangeWorkspaceUserRoleRequest) GetRole() string {
	if x != nil {
		return x.Role
	}
	return ""
}

func (x *ChangeWorkspaceUserRoleRequest) GetUserType() string {
	if x != nil {
		return x.UserType
	}
	return ""
}

type IsWorkspaceManagerRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName string `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Account       string `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty"`
}

func (x *IsWorkspaceManagerRequest) Reset() {
	*x = IsWorkspaceManagerRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_workspace_v1_workspace_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IsWorkspaceManagerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IsWorkspaceManagerRequest) ProtoMessage() {}

func (x *IsWorkspaceManagerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_workspace_v1_workspace_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IsWorkspaceManagerRequest.ProtoReflect.Descriptor instead.
func (*IsWorkspaceManagerRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_workspace_v1_workspace_proto_rawDescGZIP(), []int{29}
}

func (x *IsWorkspaceManagerRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *IsWorkspaceManagerRequest) GetAccount() string {
	if x != nil {
		return x.Account
	}
	return ""
}

type IsWorkspaceManagerResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IsManager bool `protobuf:"varint,1,opt,name=isManager,proto3" json:"isManager,omitempty"`
}

func (x *IsWorkspaceManagerResponse) Reset() {
	*x = IsWorkspaceManagerResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_workspace_v1_workspace_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IsWorkspaceManagerResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IsWorkspaceManagerResponse) ProtoMessage() {}

func (x *IsWorkspaceManagerResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_workspace_v1_workspace_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IsWorkspaceManagerResponse.ProtoReflect.Descriptor instead.
func (*IsWorkspaceManagerResponse) Descriptor() ([]byte, []int) {
	return file_aistudio_workspace_v1_workspace_proto_rawDescGZIP(), []int{30}
}

func (x *IsWorkspaceManagerResponse) GetIsManager() bool {
	if x != nil {
		return x.IsManager
	}
	return false
}

type CreateOrUpdateRobotAccountRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Account          string `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`
	DisplayName      string `protobuf:"bytes,2,opt,name=displayName,proto3" json:"displayName,omitempty"`           // 显示名称
	Description      string `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`           // 描述信息
	WeChatRobotToken string `protobuf:"bytes,4,opt,name=weChatRobotToken,proto3" json:"weChatRobotToken,omitempty"` // 微信机器人token
	WorkspaceName    string `protobuf:"bytes,5,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`       // 工作空间名称
	// int32 expires = 6; // 有效期
	Password  string `protobuf:"bytes,7,opt,name=password,proto3" json:"password,omitempty"`    // 密码
	Role      string `protobuf:"bytes,8,opt,name=role,proto3" json:"role,omitempty"`            // 角色
	IsDefault bool   `protobuf:"varint,9,opt,name=isDefault,proto3" json:"isDefault,omitempty"` // 是否默认
}

func (x *CreateOrUpdateRobotAccountRequest) Reset() {
	*x = CreateOrUpdateRobotAccountRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_workspace_v1_workspace_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateOrUpdateRobotAccountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateOrUpdateRobotAccountRequest) ProtoMessage() {}

func (x *CreateOrUpdateRobotAccountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_workspace_v1_workspace_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateOrUpdateRobotAccountRequest.ProtoReflect.Descriptor instead.
func (*CreateOrUpdateRobotAccountRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_workspace_v1_workspace_proto_rawDescGZIP(), []int{31}
}

func (x *CreateOrUpdateRobotAccountRequest) GetAccount() string {
	if x != nil {
		return x.Account
	}
	return ""
}

func (x *CreateOrUpdateRobotAccountRequest) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *CreateOrUpdateRobotAccountRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CreateOrUpdateRobotAccountRequest) GetWeChatRobotToken() string {
	if x != nil {
		return x.WeChatRobotToken
	}
	return ""
}

func (x *CreateOrUpdateRobotAccountRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *CreateOrUpdateRobotAccountRequest) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *CreateOrUpdateRobotAccountRequest) GetRole() string {
	if x != nil {
		return x.Role
	}
	return ""
}

func (x *CreateOrUpdateRobotAccountRequest) GetIsDefault() bool {
	if x != nil {
		return x.IsDefault
	}
	return false
}

var File_aistudio_workspace_v1_workspace_proto protoreflect.FileDescriptor

var file_aistudio_workspace_v1_workspace_proto_rawDesc = []byte{
	0x0a, 0x25, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x11, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x77, 0x6f,
	0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x13, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x75, 0x73, 0x65, 0x72,
	0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x63, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0xcd, 0x02, 0x0a, 0x0d, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x42, 0x61, 0x73, 0x65, 0x12, 0x3b, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x27, 0xfa, 0x42, 0x24, 0x72, 0x22, 0x10, 0x01, 0x18, 0x1f, 0x32, 0x1c,
	0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x5d, 0x28, 0x5b, 0x2d, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d,
	0x2a, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x29, 0x3f, 0x24, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x29, 0x0a, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x1f,
	0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x29, 0x0a,
	0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x64, 0x52, 0x0b, 0x64, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x6d, 0x61, 0x6e, 0x61,
	0x67, 0x65, 0x72, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x6d, 0x61, 0x6e, 0x61,
	0x67, 0x65, 0x72, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x18,
	0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x12, 0x39,
	0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x09,
	0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x6e, 0x61,
	0x62, 0x6c, 0x65, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x65, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49,
	0x44, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x49, 0x44, 0x22, 0x60, 0x0a, 0x1c, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x6c, 0x75,
	0x73, 0x74, 0x65, 0x72, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x6c, 0x75,
	0x73, 0x74, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6c, 0x75,
	0x73, 0x74, 0x65, 0x72, 0x73, 0x22, 0x2d, 0x0a, 0x1b, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43,
	0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x02, 0x69, 0x64, 0x22, 0x41, 0x0a, 0x19, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x6c, 0x75, 0x73,
	0x74, 0x65, 0x72, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x68, 0x0a, 0x19, 0x4c, 0x69, 0x73, 0x74, 0x43,
	0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0x4b, 0x0a, 0x0f, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x42,
	0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e,
	0x61, 0x70, 0x69, 0x73, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x52, 0x0f, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x73, 0x22, 0xec, 0x01, 0x0a, 0x0e, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x42, 0x69, 0x6e,
	0x64, 0x69, 0x6e, 0x67, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72,
	0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6c,
	0x75, 0x73, 0x74, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6c, 0x75,
	0x73, 0x74, 0x65, 0x72, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x5a,
	0x6f, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6c, 0x75, 0x73, 0x74,
	0x65, 0x72, 0x5a, 0x6f, 0x6e, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65,
	0x72, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63,
	0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x1e, 0x0a, 0x0a,
	0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x49, 0x44, 0x43, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x49, 0x44, 0x43, 0x12, 0x1c, 0x0a, 0x09,
	0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x72, 0x65,
	0x61, 0x64, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x72, 0x65, 0x61, 0x64, 0x79,
	0x22, 0x31, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x22, 0x48, 0x0a, 0x16, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x43, 0x6c,
	0x75, 0x73, 0x74, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x73, 0x22, 0x32, 0x0a,
	0x1c, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x49,
	0x73, 0x45, 0x78, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x22, 0x4f, 0x0a, 0x1d, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x49, 0x73, 0x45, 0x78, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x72,
	0x72, 0x6f, 0x72, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x73, 0x22, 0x94, 0x01, 0x0a, 0x0f, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x34, 0x0a, 0x04, 0x62, 0x61, 0x73, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x77, 0x6f, 0x72, 0x6b,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x42, 0x61, 0x73, 0x65, 0x52, 0x04, 0x62, 0x61, 0x73, 0x65, 0x12, 0x4b, 0x0a, 0x0f,
	0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x77, 0x6f, 0x72,
	0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65,
	0x72, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x52, 0x0f, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65,
	0x72, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x22, 0xa3, 0x04, 0x0a, 0x18, 0x57, 0x6f,
	0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x53,
	0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x12, 0x31, 0x0a, 0x08, 0x63, 0x70, 0x75, 0x55, 0x73, 0x61,
	0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x43, 0x50, 0x55, 0x55, 0x73, 0x61, 0x67, 0x65, 0x52,
	0x08, 0x63, 0x70, 0x75, 0x55, 0x73, 0x61, 0x67, 0x65, 0x12, 0x3a, 0x0a, 0x0b, 0x6d, 0x65, 0x6d,
	0x6f, 0x72, 0x79, 0x55, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18,
	0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4d, 0x65, 0x6d,
	0x6f, 0x72, 0x79, 0x55, 0x73, 0x61, 0x67, 0x65, 0x52, 0x0b, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79,
	0x55, 0x73, 0x61, 0x67, 0x65, 0x12, 0x31, 0x0a, 0x08, 0x67, 0x70, 0x75, 0x55, 0x73, 0x61, 0x67,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x47, 0x50, 0x55, 0x55, 0x73, 0x61, 0x67, 0x65, 0x52, 0x08,
	0x67, 0x70, 0x75, 0x55, 0x73, 0x61, 0x67, 0x65, 0x12, 0x3e, 0x0a, 0x0d, 0x6e, 0x6f, 0x64, 0x65,
	0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x69, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x18, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4e, 0x6f,
	0x64, 0x65, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x0d, 0x6e, 0x6f, 0x64, 0x65, 0x53,
	0x75, 0x6d, 0x6d, 0x61, 0x72, 0x69, 0x65, 0x73, 0x12, 0x41, 0x0a, 0x0e, 0x71, 0x75, 0x65, 0x75,
	0x65, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x69, 0x65, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x51,
	0x75, 0x65, 0x75, 0x65, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x0e, 0x71, 0x75, 0x65,
	0x75, 0x65, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x69, 0x65, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x51,
	0x75, 0x65, 0x75, 0x65, 0x4e, 0x75, 0x6d, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x51,
	0x75, 0x65, 0x75, 0x65, 0x4e, 0x75, 0x6d, 0x12, 0x18, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x4e,
	0x75, 0x6d, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x4e, 0x75,
	0x6d, 0x12, 0x20, 0x0a, 0x0b, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x6e, 0x51, 0x75, 0x65, 0x75, 0x65,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x6e, 0x51, 0x75,
	0x65, 0x75, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x74, 0x61, 0x73, 0x6b, 0x52, 0x75, 0x6e, 0x6e, 0x69,
	0x6e, 0x67, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x74, 0x61, 0x73, 0x6b, 0x52, 0x75,
	0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x12, 0x20, 0x0a, 0x0b, 0x74, 0x61, 0x73, 0x6b, 0x50, 0x65, 0x6e,
	0x64, 0x69, 0x6e, 0x67, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x74, 0x61, 0x73, 0x6b,
	0x50, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x24, 0x0a, 0x0d, 0x74, 0x61, 0x73, 0x6b, 0x43,
	0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d,
	0x74, 0x61, 0x73, 0x6b, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x12, 0x20, 0x0a,
	0x0b, 0x74, 0x61, 0x73, 0x6b, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0b, 0x74, 0x61, 0x73, 0x6b, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x22,
	0xbc, 0x02, 0x0a, 0x0c, 0x51, 0x75, 0x65, 0x75, 0x65, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79,
	0x12, 0x18, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x4e, 0x75, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x4e, 0x75, 0x6d, 0x12, 0x20, 0x0a, 0x0b, 0x74, 0x61,
	0x73, 0x6b, 0x49, 0x6e, 0x51, 0x75, 0x65, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0b, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x6e, 0x51, 0x75, 0x65, 0x75, 0x65, 0x12, 0x20, 0x0a, 0x0b,
	0x74, 0x61, 0x73, 0x6b, 0x52, 0x75, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0b, 0x74, 0x61, 0x73, 0x6b, 0x52, 0x75, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x12, 0x20,
	0x0a, 0x0b, 0x74, 0x61, 0x73, 0x6b, 0x50, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0b, 0x74, 0x61, 0x73, 0x6b, 0x50, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x12, 0x24, 0x0a, 0x0d, 0x74, 0x61, 0x73, 0x6b, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65,
	0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x74, 0x61, 0x73, 0x6b, 0x43, 0x6f, 0x6d,
	0x70, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x74, 0x61, 0x73, 0x6b, 0x55, 0x6e,
	0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x74, 0x61, 0x73,
	0x6b, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x70, 0x75, 0x4e,
	0x75, 0x6d, 0x55, 0x73, 0x65, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x63, 0x70,
	0x75, 0x4e, 0x75, 0x6d, 0x55, 0x73, 0x65, 0x64, 0x12, 0x24, 0x0a, 0x0d, 0x6d, 0x65, 0x6d, 0x6f,
	0x72, 0x79, 0x4e, 0x75, 0x6d, 0x55, 0x73, 0x65, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0d, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x4e, 0x75, 0x6d, 0x55, 0x73, 0x65, 0x64, 0x12, 0x1e,
	0x0a, 0x0a, 0x67, 0x70, 0x75, 0x4e, 0x75, 0x6d, 0x55, 0x73, 0x65, 0x64, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0a, 0x67, 0x70, 0x75, 0x4e, 0x75, 0x6d, 0x55, 0x73, 0x65, 0x64, 0x22, 0x7d,
	0x0a, 0x0d, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x55, 0x73, 0x65, 0x72, 0x12,
	0x18, 0x0a, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x3e, 0x0a, 0x0a, 0x75, 0x73, 0x65,
	0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e,
	0x61, 0x70, 0x69, 0x73, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x0a, 0x75,
	0x73, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x6f, 0x6c,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x72, 0x6f, 0x6c, 0x65, 0x22, 0x9b, 0x02,
	0x0a, 0x16, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3b, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x27, 0xfa, 0x42, 0x24, 0x72, 0x22, 0x10, 0x01, 0x18,
	0x1f, 0x32, 0x1c, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x5d, 0x28, 0x5b, 0x2d, 0x61, 0x2d, 0x7a, 0x30,
	0x2d, 0x39, 0x5d, 0x2a, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x29, 0x3f, 0x24, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x29, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72,
	0x02, 0x18, 0x64, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x29, 0x0a, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x1f, 0x52, 0x0b,
	0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x6d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x6d, 0x62, 0x65,
	0x72, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72,
	0x73, 0x12, 0x38, 0x0a, 0x17, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74,
	0x79, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x18, 0x06, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x17, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79,
	0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x22, 0xf2, 0x01, 0x0a, 0x16,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x29, 0x0a, 0x0b, 0x64, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x64, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x29, 0x0a, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72,
	0x02, 0x18, 0x1f, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x1a, 0x0a, 0x08, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x73, 0x18, 0x04, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x08, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x73, 0x12, 0x18, 0x0a, 0x07,
	0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x6d,
	0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x12, 0x38, 0x0a, 0x17, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61,
	0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65,
	0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x09, 0x52, 0x17, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62,
	0x69, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x73,
	0x22, 0x2d, 0x0a, 0x17, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22,
	0x2d, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x42,
	0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x38,
	0x0a, 0x22, 0x47, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x52, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x2f, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x57,
	0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x65, 0x0a, 0x0b, 0x4c, 0x69, 0x73,
	0x74, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x72, 0x65, 0x66,
	0x69, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x72, 0x65, 0x66, 0x69, 0x78,
	0x12, 0x18, 0x0a, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x64, 0x69,
	0x73, 0x70, 0x6c, 0x61, 0x79, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x0d, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x64,
	0x22, 0x57, 0x0a, 0x13, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x40, 0x0a, 0x0a, 0x77, 0x6f, 0x72, 0x6b, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x61, 0x70,
	0x69, 0x73, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2e, 0x76, 0x31, 0x2e,
	0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x42, 0x61, 0x73, 0x65, 0x52, 0x0a, 0x77,
	0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x73, 0x22, 0xb4, 0x01, 0x0a, 0x1a, 0x41, 0x76,
	0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e,
	0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69,
	0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e,
	0x12, 0x18, 0x0a, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x64, 0x0a, 0x17, 0x69, 0x6d,
	0x61, 0x67, 0x65, 0x52, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x6f, 0x72, 0x79, 0x53, 0x65, 0x74,
	0x74, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x61, 0x70,
	0x69, 0x73, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2e, 0x76, 0x31, 0x2e,
	0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x6f, 0x72, 0x79, 0x53,
	0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x17, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x65,
	0x70, 0x6f, 0x73, 0x69, 0x74, 0x6f, 0x72, 0x79, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73,
	0x22, 0x6f, 0x0a, 0x17, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74,
	0x6f, 0x72, 0x79, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x65,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x65, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x72, 0x69, 0x76, 0x61,
	0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x70, 0x72, 0x69, 0x76, 0x61, 0x74,
	0x65, 0x22, 0x8a, 0x01, 0x0a, 0x18, 0x41, 0x64, 0x64, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x55, 0x73, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24,
	0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x12,
	0x0a, 0x04, 0x72, 0x6f, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x72, 0x6f,
	0x6c, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x22, 0x8c,
	0x01, 0x0a, 0x1a, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a,
	0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x12, 0x0a,
	0x04, 0x72, 0x6f, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x72, 0x6f, 0x6c,
	0x65, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x22, 0x77, 0x0a,
	0x1b, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4d, 0x65,
	0x6d, 0x62, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x0d,
	0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x72, 0x65, 0x66, 0x69, 0x78, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x70, 0x72, 0x65, 0x66, 0x69, 0x78, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73,
	0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73,
	0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x22, 0x68, 0x0a, 0x1c, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x6f,
	0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x48, 0x0a, 0x0e, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x55, 0x73, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20,
	0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2e,
	0x76, 0x31, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x55, 0x73, 0x65, 0x72,
	0x52, 0x0e, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x55, 0x73, 0x65, 0x72, 0x73,
	0x22, 0x90, 0x01, 0x0a, 0x1e, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x55, 0x73, 0x65, 0x72, 0x52, 0x6f, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x6f, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x72, 0x6f, 0x6c, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x54,
	0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x54,
	0x79, 0x70, 0x65, 0x22, 0x5b, 0x0a, 0x19, 0x49, 0x73, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x22, 0x3a, 0x0a, 0x1a, 0x49, 0x73, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1c,
	0x0a, 0x09, 0x69, 0x73, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x09, 0x69, 0x73, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x22, 0xa1, 0x02, 0x0a,
	0x21, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52,
	0x6f, 0x62, 0x6f, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x20, 0x0a, 0x0b,
	0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x20,
	0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x2a, 0x0a, 0x10, 0x77, 0x65, 0x43, 0x68, 0x61, 0x74, 0x52, 0x6f, 0x62, 0x6f, 0x74, 0x54,
	0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x77, 0x65, 0x43, 0x68,
	0x61, 0x74, 0x52, 0x6f, 0x62, 0x6f, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x24, 0x0a, 0x0d,
	0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x12,
	0x0a, 0x04, 0x72, 0x6f, 0x6c, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x72, 0x6f,
	0x6c, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x69, 0x73, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69, 0x73, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74,
	0x32, 0x88, 0x14, 0x0a, 0x10, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x9c, 0x01, 0x0a, 0x15, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x57,
	0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x49, 0x73, 0x45, 0x78, 0x69, 0x73, 0x74, 0x12,
	0x2f, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x49, 0x73, 0x45, 0x78, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x30, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x49, 0x73, 0x45, 0x78, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x20, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1a, 0x12, 0x18, 0x2f, 0x61, 0x70, 0x69,
	0x73, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x5f, 0x65,
	0x78, 0x69, 0x73, 0x74, 0x12, 0x7f, 0x0a, 0x0f, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x57, 0x6f,
	0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x29, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x77,
	0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x22, 0x1d, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x17, 0x3a, 0x01,
	0x2a, 0x22, 0x12, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x6f, 0x72, 0x6b,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x86, 0x01, 0x0a, 0x0f, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x29, 0x2e, 0x61, 0x70, 0x69, 0x73,
	0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x77, 0x6f, 0x72, 0x6b,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x22, 0x24, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1e,
	0x3a, 0x01, 0x2a, 0x1a, 0x19, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x6f,
	0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x12, 0x85,
	0x01, 0x0a, 0x10, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x12, 0x2a, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x57,
	0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x22, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x22, 0x21, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1b, 0x2a, 0x19, 0x2f, 0x61, 0x70,
	0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2f,
	0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x12, 0x83, 0x01, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x57, 0x6f,
	0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x42, 0x61, 0x73, 0x65, 0x12, 0x2a, 0x2e, 0x61, 0x70,
	0x69, 0x73, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x42, 0x61, 0x73, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x77,
	0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x6f, 0x72, 0x6b,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x42, 0x61, 0x73, 0x65, 0x22, 0x21, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x1b, 0x12, 0x19, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x6f, 0x72, 0x6b,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x12, 0x90, 0x01, 0x0a,
	0x12, 0x47, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x12, 0x2c, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x22, 0x28, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x22, 0x12, 0x20, 0x2f,
	0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12,
	0x75, 0x0a, 0x0e, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x73, 0x12, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x1a, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x1b, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x15, 0x12, 0x13, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x6f, 0x72, 0x6b,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x73, 0x12, 0xb5, 0x01, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x57, 0x6f,
	0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x53,
	0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x12, 0x35, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x77, 0x6f,
	0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x57, 0x6f,
	0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x53,
	0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e,
	0x61, 0x70, 0x69, 0x73, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x52, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x22, 0x32, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x2c, 0x12, 0x2a, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x6f, 0x72,
	0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2d, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x12, 0xe2,
	0x01, 0x0a, 0x1c, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x52, 0x6f, 0x62, 0x6f, 0x74, 0x12,
	0x34, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x52, 0x6f, 0x62, 0x6f, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x75, 0x73, 0x65,
	0x72, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x22, 0x6c, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x66, 0x3a, 0x01, 0x2a,
	0x5a, 0x37, 0x3a, 0x01, 0x2a, 0x1a, 0x32, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f,
	0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2f, 0x7b, 0x77, 0x6f, 0x72, 0x6b, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x2f,
	0x7b, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x7d, 0x22, 0x28, 0x2f, 0x61, 0x70, 0x69, 0x73,
	0x2f, 0x76, 0x31, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2f, 0x7b, 0x77,
	0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x6f,
	0x62, 0x6f, 0x74, 0x12, 0x8d, 0x01, 0x0a, 0x11, 0x41, 0x64, 0x64, 0x57, 0x6f, 0x72, 0x6b, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x55, 0x73, 0x65, 0x72, 0x73, 0x12, 0x2b, 0x2e, 0x61, 0x70, 0x69, 0x73,
	0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64,
	0x64, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x55, 0x73, 0x65, 0x72, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x33,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2d, 0x3a, 0x01, 0x2a, 0x22, 0x28, 0x2f, 0x61, 0x70, 0x69, 0x73,
	0x2f, 0x76, 0x31, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2f, 0x7b, 0x77,
	0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x75, 0x73,
	0x65, 0x72, 0x73, 0x12, 0xa3, 0x01, 0x0a, 0x13, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x57, 0x6f,
	0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x55, 0x73, 0x65, 0x72, 0x12, 0x2d, 0x2e, 0x61, 0x70,
	0x69, 0x73, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2e, 0x76, 0x31, 0x2e,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x55,
	0x73, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70,
	0x74, 0x79, 0x22, 0x45, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x3f, 0x2a, 0x3d, 0x2f, 0x61, 0x70, 0x69,
	0x73, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2f, 0x7b,
	0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x75,
	0x73, 0x65, 0x72, 0x2f, 0x7b, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x7d, 0x2f, 0x72, 0x6f,
	0x6c, 0x65, 0x2f, 0x7b, 0x72, 0x6f, 0x6c, 0x65, 0x7d, 0x12, 0xab, 0x01, 0x0a, 0x14, 0x4c, 0x69,
	0x73, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4d, 0x65, 0x6d, 0x62, 0x65,
	0x72, 0x73, 0x12, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x2f, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x32, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2c, 0x12, 0x2a, 0x2f, 0x61, 0x70,
	0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2f,
	0x7b, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f,
	0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x12, 0xae, 0x01, 0x0a, 0x17, 0x43, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x55, 0x73, 0x65, 0x72, 0x52,
	0x6f, 0x6c, 0x65, 0x12, 0x31, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x57, 0x6f,
	0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x55, 0x73, 0x65, 0x72, 0x52, 0x6f, 0x6c, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x48,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x42, 0x3a, 0x01, 0x2a, 0x1a, 0x3d, 0x2f, 0x61, 0x70, 0x69, 0x73,
	0x2f, 0x76, 0x31, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2f, 0x7b, 0x77,
	0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x75, 0x73,
	0x65, 0x72, 0x2f, 0x7b, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x7d, 0x2f, 0x72, 0x6f, 0x6c,
	0x65, 0x2f, 0x7b, 0x72, 0x6f, 0x6c, 0x65, 0x7d, 0x12, 0xaf, 0x01, 0x0a, 0x12, 0x49, 0x73, 0x57,
	0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x12,
	0x2c, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x49, 0x73, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2d, 0x2e,
	0x61, 0x70, 0x69, 0x73, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x49, 0x73, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4d, 0x61, 0x6e,
	0x61, 0x67, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x3c, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x36, 0x12, 0x34, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x77,
	0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2f, 0x7b, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72,
	0x2f, 0x7b, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x7d, 0x12, 0x9d, 0x01, 0x0a, 0x12, 0x53,
	0x65, 0x74, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x73, 0x12, 0x2f, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x6c, 0x75, 0x73,
	0x74, 0x65, 0x72, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x3e, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x38, 0x3a, 0x01, 0x2a, 0x22, 0x33, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f,
	0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2f, 0x7b, 0x77, 0x6f, 0x72, 0x6b, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65,
	0x72, 0x2d, 0x62, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x12, 0xae, 0x01, 0x0a, 0x13, 0x4c,
	0x69, 0x73, 0x74, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x73, 0x12, 0x2c, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x6c, 0x75, 0x73, 0x74,
	0x65, 0x72, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x2c, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72,
	0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x3b,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x35, 0x12, 0x33, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31,
	0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2f, 0x7b, 0x77, 0x6f, 0x72, 0x6b,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x63, 0x6c, 0x75, 0x73, 0x74,
	0x65, 0x72, 0x2d, 0x62, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x42, 0x4c, 0x5a, 0x4a, 0x67,
	0x69, 0x74, 0x2e, 0x6c, 0x69, 0x61, 0x6e, 0x6a, 0x69, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x63,
	0x6c, 0x6f, 0x75, 0x64, 0x6e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x2f, 0x6b, 0x69, 0x63, 0x2f, 0x6b,
	0x69, 0x63, 0x2d, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x61, 0x70, 0x69, 0x73,
	0x2f, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_aistudio_workspace_v1_workspace_proto_rawDescOnce sync.Once
	file_aistudio_workspace_v1_workspace_proto_rawDescData = file_aistudio_workspace_v1_workspace_proto_rawDesc
)

func file_aistudio_workspace_v1_workspace_proto_rawDescGZIP() []byte {
	file_aistudio_workspace_v1_workspace_proto_rawDescOnce.Do(func() {
		file_aistudio_workspace_v1_workspace_proto_rawDescData = protoimpl.X.CompressGZIP(file_aistudio_workspace_v1_workspace_proto_rawDescData)
	})
	return file_aistudio_workspace_v1_workspace_proto_rawDescData
}

var file_aistudio_workspace_v1_workspace_proto_msgTypes = make([]protoimpl.MessageInfo, 32)
var file_aistudio_workspace_v1_workspace_proto_goTypes = []any{
	(*WorkspaceBase)(nil),                      // 0: apis.workspace.v1.WorkspaceBase
	(*CreateClusterBindingsRequest)(nil),       // 1: apis.workspace.v1.CreateClusterBindingsRequest
	(*DeleteClusterBindingRequest)(nil),        // 2: apis.workspace.v1.DeleteClusterBindingRequest
	(*ListClusterBindingRequest)(nil),          // 3: apis.workspace.v1.ListClusterBindingRequest
	(*ListClusterBindingsResult)(nil),          // 4: apis.workspace.v1.ListClusterBindingsResult
	(*ClusterBinding)(nil),                     // 5: apis.workspace.v1.ClusterBinding
	(*GetWorkspaceSettingsRequest)(nil),        // 6: apis.workspace.v1.GetWorkspaceSettingsRequest
	(*BindingClustersRequest)(nil),             // 7: apis.workspace.v1.BindingClustersRequest
	(*CheckWorkspaceIsExistRequest)(nil),       // 8: apis.workspace.v1.CheckWorkspaceIsExistRequest
	(*CheckWorkspaceIsExistResponse)(nil),      // 9: apis.workspace.v1.CheckWorkspaceIsExistResponse
	(*WorkspaceDetail)(nil),                    // 10: apis.workspace.v1.WorkspaceDetail
	(*WorkspaceResourceSummary)(nil),           // 11: apis.workspace.v1.WorkspaceResourceSummary
	(*QueueSummary)(nil),                       // 12: apis.workspace.v1.QueueSummary
	(*WorkspaceUser)(nil),                      // 13: apis.workspace.v1.WorkspaceUser
	(*CreateWorkspaceRequest)(nil),             // 14: apis.workspace.v1.CreateWorkspaceRequest
	(*UpdateWorkspaceRequest)(nil),             // 15: apis.workspace.v1.UpdateWorkspaceRequest
	(*DisableWorkspaceRequest)(nil),            // 16: apis.workspace.v1.DisableWorkspaceRequest
	(*GetWorkspaceBaseRequest)(nil),            // 17: apis.workspace.v1.GetWorkspaceBaseRequest
	(*GetWorkspaceResourceSummaryRequest)(nil), // 18: apis.workspace.v1.GetWorkspaceResourceSummaryRequest
	(*GetWorkspaceDetailRequest)(nil),          // 19: apis.workspace.v1.GetWorkspaceDetailRequest
	(*ListOptions)(nil),                        // 20: apis.workspace.v1.ListOptions
	(*ListWorkspaceResult)(nil),                // 21: apis.workspace.v1.ListWorkspaceResult
	(*AvailabilityRegionSettings)(nil),         // 22: apis.workspace.v1.AvailabilityRegionSettings
	(*ImageRepositorySettings)(nil),            // 23: apis.workspace.v1.ImageRepositorySettings
	(*AddWorkspaceUsersRequest)(nil),           // 24: apis.workspace.v1.AddWorkspaceUsersRequest
	(*DeleteWorkspaceUserRequest)(nil),         // 25: apis.workspace.v1.DeleteWorkspaceUserRequest
	(*ListWorkspaceMembersRequest)(nil),        // 26: apis.workspace.v1.ListWorkspaceMembersRequest
	(*ListWorkspaceMembersResponse)(nil),       // 27: apis.workspace.v1.ListWorkspaceMembersResponse
	(*ChangeWorkspaceUserRoleRequest)(nil),     // 28: apis.workspace.v1.ChangeWorkspaceUserRoleRequest
	(*IsWorkspaceManagerRequest)(nil),          // 29: apis.workspace.v1.IsWorkspaceManagerRequest
	(*IsWorkspaceManagerResponse)(nil),         // 30: apis.workspace.v1.IsWorkspaceManagerResponse
	(*CreateOrUpdateRobotAccountRequest)(nil),  // 31: apis.workspace.v1.CreateOrUpdateRobotAccountRequest
	(*common.TimestampModel)(nil),              // 32: apis.common.TimestampModel
	(*common.CPUUsage)(nil),                    // 33: apis.common.CPUUsage
	(*common.MemoryUsage)(nil),                 // 34: apis.common.MemoryUsage
	(*common.GPUUsage)(nil),                    // 35: apis.common.GPUUsage
	(*common.NodeSummary)(nil),                 // 36: apis.common.NodeSummary
	(*common.QueueSummary)(nil),                // 37: apis.common.QueueSummary
	(*v1.UserDetail)(nil),                      // 38: apis.usercenter.v1.UserDetail
	(*emptypb.Empty)(nil),                      // 39: google.protobuf.Empty
}
var file_aistudio_workspace_v1_workspace_proto_depIdxs = []int32{
	32, // 0: apis.workspace.v1.WorkspaceBase.timestamp:type_name -> apis.common.TimestampModel
	5,  // 1: apis.workspace.v1.ListClusterBindingsResult.clusterBindings:type_name -> apis.workspace.v1.ClusterBinding
	0,  // 2: apis.workspace.v1.WorkspaceDetail.base:type_name -> apis.workspace.v1.WorkspaceBase
	5,  // 3: apis.workspace.v1.WorkspaceDetail.clusterBindings:type_name -> apis.workspace.v1.ClusterBinding
	33, // 4: apis.workspace.v1.WorkspaceResourceSummary.cpuUsage:type_name -> apis.common.CPUUsage
	34, // 5: apis.workspace.v1.WorkspaceResourceSummary.memoryUsage:type_name -> apis.common.MemoryUsage
	35, // 6: apis.workspace.v1.WorkspaceResourceSummary.gpuUsage:type_name -> apis.common.GPUUsage
	36, // 7: apis.workspace.v1.WorkspaceResourceSummary.nodeSummaries:type_name -> apis.common.NodeSummary
	37, // 8: apis.workspace.v1.WorkspaceResourceSummary.queueSummaries:type_name -> apis.common.QueueSummary
	38, // 9: apis.workspace.v1.WorkspaceUser.userDetail:type_name -> apis.usercenter.v1.UserDetail
	0,  // 10: apis.workspace.v1.ListWorkspaceResult.workspaces:type_name -> apis.workspace.v1.WorkspaceBase
	23, // 11: apis.workspace.v1.AvailabilityRegionSettings.imageRepositorySettings:type_name -> apis.workspace.v1.ImageRepositorySettings
	13, // 12: apis.workspace.v1.ListWorkspaceMembersResponse.workspaceUsers:type_name -> apis.workspace.v1.WorkspaceUser
	8,  // 13: apis.workspace.v1.WorkspaceService.CheckWorkspaceIsExist:input_type -> apis.workspace.v1.CheckWorkspaceIsExistRequest
	14, // 14: apis.workspace.v1.WorkspaceService.CreateWorkspace:input_type -> apis.workspace.v1.CreateWorkspaceRequest
	15, // 15: apis.workspace.v1.WorkspaceService.UpdateWorkspace:input_type -> apis.workspace.v1.UpdateWorkspaceRequest
	16, // 16: apis.workspace.v1.WorkspaceService.DisableWorkspace:input_type -> apis.workspace.v1.DisableWorkspaceRequest
	17, // 17: apis.workspace.v1.WorkspaceService.GetWorkspaceBase:input_type -> apis.workspace.v1.GetWorkspaceBaseRequest
	19, // 18: apis.workspace.v1.WorkspaceService.GetWorkspaceDetail:input_type -> apis.workspace.v1.GetWorkspaceDetailRequest
	20, // 19: apis.workspace.v1.WorkspaceService.ListWorkspaces:input_type -> apis.workspace.v1.ListOptions
	18, // 20: apis.workspace.v1.WorkspaceService.GetWorkspaceResourceSummary:input_type -> apis.workspace.v1.GetWorkspaceResourceSummaryRequest
	31, // 21: apis.workspace.v1.WorkspaceService.CreateOrUpdateWorkspaceRobot:input_type -> apis.workspace.v1.CreateOrUpdateRobotAccountRequest
	24, // 22: apis.workspace.v1.WorkspaceService.AddWorkspaceUsers:input_type -> apis.workspace.v1.AddWorkspaceUsersRequest
	25, // 23: apis.workspace.v1.WorkspaceService.DeleteWorkspaceUser:input_type -> apis.workspace.v1.DeleteWorkspaceUserRequest
	26, // 24: apis.workspace.v1.WorkspaceService.ListWorkspaceMembers:input_type -> apis.workspace.v1.ListWorkspaceMembersRequest
	28, // 25: apis.workspace.v1.WorkspaceService.ChangeWorkspaceUserRole:input_type -> apis.workspace.v1.ChangeWorkspaceUserRoleRequest
	29, // 26: apis.workspace.v1.WorkspaceService.IsWorkspaceManager:input_type -> apis.workspace.v1.IsWorkspaceManagerRequest
	1,  // 27: apis.workspace.v1.WorkspaceService.SetClusterBindings:input_type -> apis.workspace.v1.CreateClusterBindingsRequest
	3,  // 28: apis.workspace.v1.WorkspaceService.ListClusterBindings:input_type -> apis.workspace.v1.ListClusterBindingRequest
	9,  // 29: apis.workspace.v1.WorkspaceService.CheckWorkspaceIsExist:output_type -> apis.workspace.v1.CheckWorkspaceIsExistResponse
	10, // 30: apis.workspace.v1.WorkspaceService.CreateWorkspace:output_type -> apis.workspace.v1.WorkspaceDetail
	10, // 31: apis.workspace.v1.WorkspaceService.UpdateWorkspace:output_type -> apis.workspace.v1.WorkspaceDetail
	10, // 32: apis.workspace.v1.WorkspaceService.DisableWorkspace:output_type -> apis.workspace.v1.WorkspaceDetail
	0,  // 33: apis.workspace.v1.WorkspaceService.GetWorkspaceBase:output_type -> apis.workspace.v1.WorkspaceBase
	10, // 34: apis.workspace.v1.WorkspaceService.GetWorkspaceDetail:output_type -> apis.workspace.v1.WorkspaceDetail
	21, // 35: apis.workspace.v1.WorkspaceService.ListWorkspaces:output_type -> apis.workspace.v1.ListWorkspaceResult
	11, // 36: apis.workspace.v1.WorkspaceService.GetWorkspaceResourceSummary:output_type -> apis.workspace.v1.WorkspaceResourceSummary
	38, // 37: apis.workspace.v1.WorkspaceService.CreateOrUpdateWorkspaceRobot:output_type -> apis.usercenter.v1.UserDetail
	39, // 38: apis.workspace.v1.WorkspaceService.AddWorkspaceUsers:output_type -> google.protobuf.Empty
	39, // 39: apis.workspace.v1.WorkspaceService.DeleteWorkspaceUser:output_type -> google.protobuf.Empty
	27, // 40: apis.workspace.v1.WorkspaceService.ListWorkspaceMembers:output_type -> apis.workspace.v1.ListWorkspaceMembersResponse
	39, // 41: apis.workspace.v1.WorkspaceService.ChangeWorkspaceUserRole:output_type -> google.protobuf.Empty
	30, // 42: apis.workspace.v1.WorkspaceService.IsWorkspaceManager:output_type -> apis.workspace.v1.IsWorkspaceManagerResponse
	39, // 43: apis.workspace.v1.WorkspaceService.SetClusterBindings:output_type -> google.protobuf.Empty
	4,  // 44: apis.workspace.v1.WorkspaceService.ListClusterBindings:output_type -> apis.workspace.v1.ListClusterBindingsResult
	29, // [29:45] is the sub-list for method output_type
	13, // [13:29] is the sub-list for method input_type
	13, // [13:13] is the sub-list for extension type_name
	13, // [13:13] is the sub-list for extension extendee
	0,  // [0:13] is the sub-list for field type_name
}

func init() { file_aistudio_workspace_v1_workspace_proto_init() }
func file_aistudio_workspace_v1_workspace_proto_init() {
	if File_aistudio_workspace_v1_workspace_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_aistudio_workspace_v1_workspace_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*WorkspaceBase); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_workspace_v1_workspace_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*CreateClusterBindingsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_workspace_v1_workspace_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*DeleteClusterBindingRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_workspace_v1_workspace_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*ListClusterBindingRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_workspace_v1_workspace_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*ListClusterBindingsResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_workspace_v1_workspace_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*ClusterBinding); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_workspace_v1_workspace_proto_msgTypes[6].Exporter = func(v any, i int) any {
			switch v := v.(*GetWorkspaceSettingsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_workspace_v1_workspace_proto_msgTypes[7].Exporter = func(v any, i int) any {
			switch v := v.(*BindingClustersRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_workspace_v1_workspace_proto_msgTypes[8].Exporter = func(v any, i int) any {
			switch v := v.(*CheckWorkspaceIsExistRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_workspace_v1_workspace_proto_msgTypes[9].Exporter = func(v any, i int) any {
			switch v := v.(*CheckWorkspaceIsExistResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_workspace_v1_workspace_proto_msgTypes[10].Exporter = func(v any, i int) any {
			switch v := v.(*WorkspaceDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_workspace_v1_workspace_proto_msgTypes[11].Exporter = func(v any, i int) any {
			switch v := v.(*WorkspaceResourceSummary); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_workspace_v1_workspace_proto_msgTypes[12].Exporter = func(v any, i int) any {
			switch v := v.(*QueueSummary); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_workspace_v1_workspace_proto_msgTypes[13].Exporter = func(v any, i int) any {
			switch v := v.(*WorkspaceUser); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_workspace_v1_workspace_proto_msgTypes[14].Exporter = func(v any, i int) any {
			switch v := v.(*CreateWorkspaceRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_workspace_v1_workspace_proto_msgTypes[15].Exporter = func(v any, i int) any {
			switch v := v.(*UpdateWorkspaceRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_workspace_v1_workspace_proto_msgTypes[16].Exporter = func(v any, i int) any {
			switch v := v.(*DisableWorkspaceRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_workspace_v1_workspace_proto_msgTypes[17].Exporter = func(v any, i int) any {
			switch v := v.(*GetWorkspaceBaseRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_workspace_v1_workspace_proto_msgTypes[18].Exporter = func(v any, i int) any {
			switch v := v.(*GetWorkspaceResourceSummaryRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_workspace_v1_workspace_proto_msgTypes[19].Exporter = func(v any, i int) any {
			switch v := v.(*GetWorkspaceDetailRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_workspace_v1_workspace_proto_msgTypes[20].Exporter = func(v any, i int) any {
			switch v := v.(*ListOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_workspace_v1_workspace_proto_msgTypes[21].Exporter = func(v any, i int) any {
			switch v := v.(*ListWorkspaceResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_workspace_v1_workspace_proto_msgTypes[22].Exporter = func(v any, i int) any {
			switch v := v.(*AvailabilityRegionSettings); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_workspace_v1_workspace_proto_msgTypes[23].Exporter = func(v any, i int) any {
			switch v := v.(*ImageRepositorySettings); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_workspace_v1_workspace_proto_msgTypes[24].Exporter = func(v any, i int) any {
			switch v := v.(*AddWorkspaceUsersRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_workspace_v1_workspace_proto_msgTypes[25].Exporter = func(v any, i int) any {
			switch v := v.(*DeleteWorkspaceUserRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_workspace_v1_workspace_proto_msgTypes[26].Exporter = func(v any, i int) any {
			switch v := v.(*ListWorkspaceMembersRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_workspace_v1_workspace_proto_msgTypes[27].Exporter = func(v any, i int) any {
			switch v := v.(*ListWorkspaceMembersResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_workspace_v1_workspace_proto_msgTypes[28].Exporter = func(v any, i int) any {
			switch v := v.(*ChangeWorkspaceUserRoleRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_workspace_v1_workspace_proto_msgTypes[29].Exporter = func(v any, i int) any {
			switch v := v.(*IsWorkspaceManagerRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_workspace_v1_workspace_proto_msgTypes[30].Exporter = func(v any, i int) any {
			switch v := v.(*IsWorkspaceManagerResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_workspace_v1_workspace_proto_msgTypes[31].Exporter = func(v any, i int) any {
			switch v := v.(*CreateOrUpdateRobotAccountRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_aistudio_workspace_v1_workspace_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   32,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_aistudio_workspace_v1_workspace_proto_goTypes,
		DependencyIndexes: file_aistudio_workspace_v1_workspace_proto_depIdxs,
		MessageInfos:      file_aistudio_workspace_v1_workspace_proto_msgTypes,
	}.Build()
	File_aistudio_workspace_v1_workspace_proto = out.File
	file_aistudio_workspace_v1_workspace_proto_rawDesc = nil
	file_aistudio_workspace_v1_workspace_proto_goTypes = nil
	file_aistudio_workspace_v1_workspace_proto_depIdxs = nil
}
