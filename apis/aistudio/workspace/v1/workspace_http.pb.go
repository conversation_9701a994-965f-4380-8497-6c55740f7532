// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.3
// - protoc             v5.29.3
// source: aistudio/workspace/v1/workspace.proto

package v1

import (
	context "context"
	v1 "git.lianjia.com/cloudnative/kic/kic-platform/apis/usercenter/v1"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationWorkspaceServiceAddWorkspaceUsers = "/apis.workspace.v1.WorkspaceService/AddWorkspaceUsers"
const OperationWorkspaceServiceChangeWorkspaceUserRole = "/apis.workspace.v1.WorkspaceService/ChangeWorkspaceUserRole"
const OperationWorkspaceServiceCheckWorkspaceIsExist = "/apis.workspace.v1.WorkspaceService/CheckWorkspaceIsExist"
const OperationWorkspaceServiceCreateOrUpdateWorkspaceRobot = "/apis.workspace.v1.WorkspaceService/CreateOrUpdateWorkspaceRobot"
const OperationWorkspaceServiceCreateWorkspace = "/apis.workspace.v1.WorkspaceService/CreateWorkspace"
const OperationWorkspaceServiceDeleteWorkspaceUser = "/apis.workspace.v1.WorkspaceService/DeleteWorkspaceUser"
const OperationWorkspaceServiceDisableWorkspace = "/apis.workspace.v1.WorkspaceService/DisableWorkspace"
const OperationWorkspaceServiceGetWorkspaceBase = "/apis.workspace.v1.WorkspaceService/GetWorkspaceBase"
const OperationWorkspaceServiceGetWorkspaceDetail = "/apis.workspace.v1.WorkspaceService/GetWorkspaceDetail"
const OperationWorkspaceServiceGetWorkspaceResourceSummary = "/apis.workspace.v1.WorkspaceService/GetWorkspaceResourceSummary"
const OperationWorkspaceServiceIsWorkspaceManager = "/apis.workspace.v1.WorkspaceService/IsWorkspaceManager"
const OperationWorkspaceServiceListClusterBindings = "/apis.workspace.v1.WorkspaceService/ListClusterBindings"
const OperationWorkspaceServiceListWorkspaceMembers = "/apis.workspace.v1.WorkspaceService/ListWorkspaceMembers"
const OperationWorkspaceServiceListWorkspaces = "/apis.workspace.v1.WorkspaceService/ListWorkspaces"
const OperationWorkspaceServiceSetClusterBindings = "/apis.workspace.v1.WorkspaceService/SetClusterBindings"
const OperationWorkspaceServiceUpdateWorkspace = "/apis.workspace.v1.WorkspaceService/UpdateWorkspace"

type WorkspaceServiceHTTPServer interface {
	// AddWorkspaceUsers 工作空间添加成员(多个)
	AddWorkspaceUsers(context.Context, *AddWorkspaceUsersRequest) (*emptypb.Empty, error)
	// ChangeWorkspaceUserRole 更改空间用户角色
	ChangeWorkspaceUserRole(context.Context, *ChangeWorkspaceUserRoleRequest) (*emptypb.Empty, error)
	// CheckWorkspaceIsExist CheckWorkspaceIsExist 检查工作空间是否存在
	CheckWorkspaceIsExist(context.Context, *CheckWorkspaceIsExistRequest) (*CheckWorkspaceIsExistResponse, error)
	// CreateOrUpdateWorkspaceRobot 创建空间机器人账号（先创建机器人用户，然后直接赋予机器人角色）
	CreateOrUpdateWorkspaceRobot(context.Context, *CreateOrUpdateRobotAccountRequest) (*v1.UserDetail, error)
	// CreateWorkspace CreateWorkspace 创建工作空间
	CreateWorkspace(context.Context, *CreateWorkspaceRequest) (*WorkspaceDetail, error)
	// DeleteWorkspaceUser 工作空间删除成员(单个)
	DeleteWorkspaceUser(context.Context, *DeleteWorkspaceUserRequest) (*emptypb.Empty, error)
	// DisableWorkspace DisableWorkspace 禁用工作空间
	DisableWorkspace(context.Context, *DisableWorkspaceRequest) (*WorkspaceDetail, error)
	// GetWorkspaceBase GetWorkspaceBase 获取工作空间基本信息
	GetWorkspaceBase(context.Context, *GetWorkspaceBaseRequest) (*WorkspaceBase, error)
	// GetWorkspaceDetail GetWorkspaceDetail 获取工作空间详细信息
	GetWorkspaceDetail(context.Context, *GetWorkspaceDetailRequest) (*WorkspaceDetail, error)
	// GetWorkspaceResourceSummary获取资源概括
	GetWorkspaceResourceSummary(context.Context, *GetWorkspaceResourceSummaryRequest) (*WorkspaceResourceSummary, error)
	// IsWorkspaceManager 判断用户是不是工作空间的管理员
	IsWorkspaceManager(context.Context, *IsWorkspaceManagerRequest) (*IsWorkspaceManagerResponse, error)
	ListClusterBindings(context.Context, *ListClusterBindingRequest) (*ListClusterBindingsResult, error)
	// ListWorkspaceMembers 获取工作空间的所有成员
	ListWorkspaceMembers(context.Context, *ListWorkspaceMembersRequest) (*ListWorkspaceMembersResponse, error)
	// ListWorkspaces获取Workspace列表, 通过访问用户的权限来返回具有权限的Workspace
	ListWorkspaces(context.Context, *ListOptions) (*ListWorkspaceResult, error)
	SetClusterBindings(context.Context, *CreateClusterBindingsRequest) (*emptypb.Empty, error)
	// UpdateWorkspace UpdateWorkspace 更新工作空间
	UpdateWorkspace(context.Context, *UpdateWorkspaceRequest) (*WorkspaceDetail, error)
}

func RegisterWorkspaceServiceHTTPServer(s *http.Server, srv WorkspaceServiceHTTPServer) {
	r := s.Route("/")
	r.GET("/apis/v1/workspace_exist", _WorkspaceService_CheckWorkspaceIsExist0_HTTP_Handler(srv))
	r.POST("/apis/v1/workspace", _WorkspaceService_CreateWorkspace0_HTTP_Handler(srv))
	r.PUT("/apis/v1/workspace/{name}", _WorkspaceService_UpdateWorkspace0_HTTP_Handler(srv))
	r.DELETE("/apis/v1/workspace/{name}", _WorkspaceService_DisableWorkspace0_HTTP_Handler(srv))
	r.GET("/apis/v1/workspace/{name}", _WorkspaceService_GetWorkspaceBase0_HTTP_Handler(srv))
	r.GET("/apis/v1/workspace/{name}/detail", _WorkspaceService_GetWorkspaceDetail0_HTTP_Handler(srv))
	r.GET("/apis/v1/workspaces", _WorkspaceService_ListWorkspaces0_HTTP_Handler(srv))
	r.GET("/apis/v1/workspace/{name}/resource-summary", _WorkspaceService_GetWorkspaceResourceSummary0_HTTP_Handler(srv))
	r.PUT("/apis/v1/workspace/{workspaceName}/robot/{account}", _WorkspaceService_CreateOrUpdateWorkspaceRobot0_HTTP_Handler(srv))
	r.POST("/apis/v1/workspace/{workspaceName}/robot", _WorkspaceService_CreateOrUpdateWorkspaceRobot1_HTTP_Handler(srv))
	r.POST("/apis/v1/workspace/{workspaceName}/users", _WorkspaceService_AddWorkspaceUsers0_HTTP_Handler(srv))
	r.DELETE("/apis/v1/workspace/{workspaceName}/user/{account}/role/{role}", _WorkspaceService_DeleteWorkspaceUser0_HTTP_Handler(srv))
	r.GET("/apis/v1/workspace/{workspaceName}/members", _WorkspaceService_ListWorkspaceMembers0_HTTP_Handler(srv))
	r.PUT("/apis/v1/workspace/{workspaceName}/user/{account}/role/{role}", _WorkspaceService_ChangeWorkspaceUserRole0_HTTP_Handler(srv))
	r.GET("/apis/v1/workspace/{workspaceName}/manager/{account}", _WorkspaceService_IsWorkspaceManager0_HTTP_Handler(srv))
	r.POST("/apis/v1/workspace/{workspaceName}/cluster-bindings", _WorkspaceService_SetClusterBindings0_HTTP_Handler(srv))
	r.GET("/apis/v1/workspace/{workspaceName}/cluster-bindings", _WorkspaceService_ListClusterBindings0_HTTP_Handler(srv))
}

func _WorkspaceService_CheckWorkspaceIsExist0_HTTP_Handler(srv WorkspaceServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CheckWorkspaceIsExistRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationWorkspaceServiceCheckWorkspaceIsExist)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CheckWorkspaceIsExist(ctx, req.(*CheckWorkspaceIsExistRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CheckWorkspaceIsExistResponse)
		return ctx.Result(200, reply)
	}
}

func _WorkspaceService_CreateWorkspace0_HTTP_Handler(srv WorkspaceServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateWorkspaceRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationWorkspaceServiceCreateWorkspace)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateWorkspace(ctx, req.(*CreateWorkspaceRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*WorkspaceDetail)
		return ctx.Result(200, reply)
	}
}

func _WorkspaceService_UpdateWorkspace0_HTTP_Handler(srv WorkspaceServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UpdateWorkspaceRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationWorkspaceServiceUpdateWorkspace)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateWorkspace(ctx, req.(*UpdateWorkspaceRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*WorkspaceDetail)
		return ctx.Result(200, reply)
	}
}

func _WorkspaceService_DisableWorkspace0_HTTP_Handler(srv WorkspaceServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DisableWorkspaceRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationWorkspaceServiceDisableWorkspace)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DisableWorkspace(ctx, req.(*DisableWorkspaceRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*WorkspaceDetail)
		return ctx.Result(200, reply)
	}
}

func _WorkspaceService_GetWorkspaceBase0_HTTP_Handler(srv WorkspaceServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetWorkspaceBaseRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationWorkspaceServiceGetWorkspaceBase)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetWorkspaceBase(ctx, req.(*GetWorkspaceBaseRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*WorkspaceBase)
		return ctx.Result(200, reply)
	}
}

func _WorkspaceService_GetWorkspaceDetail0_HTTP_Handler(srv WorkspaceServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetWorkspaceDetailRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationWorkspaceServiceGetWorkspaceDetail)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetWorkspaceDetail(ctx, req.(*GetWorkspaceDetailRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*WorkspaceDetail)
		return ctx.Result(200, reply)
	}
}

func _WorkspaceService_ListWorkspaces0_HTTP_Handler(srv WorkspaceServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListOptions
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationWorkspaceServiceListWorkspaces)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListWorkspaces(ctx, req.(*ListOptions))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListWorkspaceResult)
		return ctx.Result(200, reply)
	}
}

func _WorkspaceService_GetWorkspaceResourceSummary0_HTTP_Handler(srv WorkspaceServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetWorkspaceResourceSummaryRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationWorkspaceServiceGetWorkspaceResourceSummary)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetWorkspaceResourceSummary(ctx, req.(*GetWorkspaceResourceSummaryRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*WorkspaceResourceSummary)
		return ctx.Result(200, reply)
	}
}

func _WorkspaceService_CreateOrUpdateWorkspaceRobot0_HTTP_Handler(srv WorkspaceServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateOrUpdateRobotAccountRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationWorkspaceServiceCreateOrUpdateWorkspaceRobot)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateOrUpdateWorkspaceRobot(ctx, req.(*CreateOrUpdateRobotAccountRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v1.UserDetail)
		return ctx.Result(200, reply)
	}
}

func _WorkspaceService_CreateOrUpdateWorkspaceRobot1_HTTP_Handler(srv WorkspaceServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateOrUpdateRobotAccountRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationWorkspaceServiceCreateOrUpdateWorkspaceRobot)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateOrUpdateWorkspaceRobot(ctx, req.(*CreateOrUpdateRobotAccountRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v1.UserDetail)
		return ctx.Result(200, reply)
	}
}

func _WorkspaceService_AddWorkspaceUsers0_HTTP_Handler(srv WorkspaceServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in AddWorkspaceUsersRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationWorkspaceServiceAddWorkspaceUsers)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.AddWorkspaceUsers(ctx, req.(*AddWorkspaceUsersRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _WorkspaceService_DeleteWorkspaceUser0_HTTP_Handler(srv WorkspaceServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteWorkspaceUserRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationWorkspaceServiceDeleteWorkspaceUser)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteWorkspaceUser(ctx, req.(*DeleteWorkspaceUserRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _WorkspaceService_ListWorkspaceMembers0_HTTP_Handler(srv WorkspaceServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListWorkspaceMembersRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationWorkspaceServiceListWorkspaceMembers)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListWorkspaceMembers(ctx, req.(*ListWorkspaceMembersRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListWorkspaceMembersResponse)
		return ctx.Result(200, reply)
	}
}

func _WorkspaceService_ChangeWorkspaceUserRole0_HTTP_Handler(srv WorkspaceServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ChangeWorkspaceUserRoleRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationWorkspaceServiceChangeWorkspaceUserRole)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ChangeWorkspaceUserRole(ctx, req.(*ChangeWorkspaceUserRoleRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _WorkspaceService_IsWorkspaceManager0_HTTP_Handler(srv WorkspaceServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in IsWorkspaceManagerRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationWorkspaceServiceIsWorkspaceManager)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.IsWorkspaceManager(ctx, req.(*IsWorkspaceManagerRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*IsWorkspaceManagerResponse)
		return ctx.Result(200, reply)
	}
}

func _WorkspaceService_SetClusterBindings0_HTTP_Handler(srv WorkspaceServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateClusterBindingsRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationWorkspaceServiceSetClusterBindings)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SetClusterBindings(ctx, req.(*CreateClusterBindingsRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _WorkspaceService_ListClusterBindings0_HTTP_Handler(srv WorkspaceServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListClusterBindingRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationWorkspaceServiceListClusterBindings)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListClusterBindings(ctx, req.(*ListClusterBindingRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListClusterBindingsResult)
		return ctx.Result(200, reply)
	}
}

type WorkspaceServiceHTTPClient interface {
	AddWorkspaceUsers(ctx context.Context, req *AddWorkspaceUsersRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	ChangeWorkspaceUserRole(ctx context.Context, req *ChangeWorkspaceUserRoleRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	CheckWorkspaceIsExist(ctx context.Context, req *CheckWorkspaceIsExistRequest, opts ...http.CallOption) (rsp *CheckWorkspaceIsExistResponse, err error)
	CreateOrUpdateWorkspaceRobot(ctx context.Context, req *CreateOrUpdateRobotAccountRequest, opts ...http.CallOption) (rsp *v1.UserDetail, err error)
	CreateWorkspace(ctx context.Context, req *CreateWorkspaceRequest, opts ...http.CallOption) (rsp *WorkspaceDetail, err error)
	DeleteWorkspaceUser(ctx context.Context, req *DeleteWorkspaceUserRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	DisableWorkspace(ctx context.Context, req *DisableWorkspaceRequest, opts ...http.CallOption) (rsp *WorkspaceDetail, err error)
	GetWorkspaceBase(ctx context.Context, req *GetWorkspaceBaseRequest, opts ...http.CallOption) (rsp *WorkspaceBase, err error)
	GetWorkspaceDetail(ctx context.Context, req *GetWorkspaceDetailRequest, opts ...http.CallOption) (rsp *WorkspaceDetail, err error)
	GetWorkspaceResourceSummary(ctx context.Context, req *GetWorkspaceResourceSummaryRequest, opts ...http.CallOption) (rsp *WorkspaceResourceSummary, err error)
	IsWorkspaceManager(ctx context.Context, req *IsWorkspaceManagerRequest, opts ...http.CallOption) (rsp *IsWorkspaceManagerResponse, err error)
	ListClusterBindings(ctx context.Context, req *ListClusterBindingRequest, opts ...http.CallOption) (rsp *ListClusterBindingsResult, err error)
	ListWorkspaceMembers(ctx context.Context, req *ListWorkspaceMembersRequest, opts ...http.CallOption) (rsp *ListWorkspaceMembersResponse, err error)
	ListWorkspaces(ctx context.Context, req *ListOptions, opts ...http.CallOption) (rsp *ListWorkspaceResult, err error)
	SetClusterBindings(ctx context.Context, req *CreateClusterBindingsRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	UpdateWorkspace(ctx context.Context, req *UpdateWorkspaceRequest, opts ...http.CallOption) (rsp *WorkspaceDetail, err error)
}

type WorkspaceServiceHTTPClientImpl struct {
	cc *http.Client
}

func NewWorkspaceServiceHTTPClient(client *http.Client) WorkspaceServiceHTTPClient {
	return &WorkspaceServiceHTTPClientImpl{client}
}

func (c *WorkspaceServiceHTTPClientImpl) AddWorkspaceUsers(ctx context.Context, in *AddWorkspaceUsersRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/apis/v1/workspace/{workspaceName}/users"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationWorkspaceServiceAddWorkspaceUsers))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *WorkspaceServiceHTTPClientImpl) ChangeWorkspaceUserRole(ctx context.Context, in *ChangeWorkspaceUserRoleRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/apis/v1/workspace/{workspaceName}/user/{account}/role/{role}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationWorkspaceServiceChangeWorkspaceUserRole))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *WorkspaceServiceHTTPClientImpl) CheckWorkspaceIsExist(ctx context.Context, in *CheckWorkspaceIsExistRequest, opts ...http.CallOption) (*CheckWorkspaceIsExistResponse, error) {
	var out CheckWorkspaceIsExistResponse
	pattern := "/apis/v1/workspace_exist"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationWorkspaceServiceCheckWorkspaceIsExist))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *WorkspaceServiceHTTPClientImpl) CreateOrUpdateWorkspaceRobot(ctx context.Context, in *CreateOrUpdateRobotAccountRequest, opts ...http.CallOption) (*v1.UserDetail, error) {
	var out v1.UserDetail
	pattern := "/apis/v1/workspace/{workspaceName}/robot"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationWorkspaceServiceCreateOrUpdateWorkspaceRobot))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *WorkspaceServiceHTTPClientImpl) CreateWorkspace(ctx context.Context, in *CreateWorkspaceRequest, opts ...http.CallOption) (*WorkspaceDetail, error) {
	var out WorkspaceDetail
	pattern := "/apis/v1/workspace"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationWorkspaceServiceCreateWorkspace))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *WorkspaceServiceHTTPClientImpl) DeleteWorkspaceUser(ctx context.Context, in *DeleteWorkspaceUserRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/apis/v1/workspace/{workspaceName}/user/{account}/role/{role}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationWorkspaceServiceDeleteWorkspaceUser))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *WorkspaceServiceHTTPClientImpl) DisableWorkspace(ctx context.Context, in *DisableWorkspaceRequest, opts ...http.CallOption) (*WorkspaceDetail, error) {
	var out WorkspaceDetail
	pattern := "/apis/v1/workspace/{name}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationWorkspaceServiceDisableWorkspace))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *WorkspaceServiceHTTPClientImpl) GetWorkspaceBase(ctx context.Context, in *GetWorkspaceBaseRequest, opts ...http.CallOption) (*WorkspaceBase, error) {
	var out WorkspaceBase
	pattern := "/apis/v1/workspace/{name}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationWorkspaceServiceGetWorkspaceBase))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *WorkspaceServiceHTTPClientImpl) GetWorkspaceDetail(ctx context.Context, in *GetWorkspaceDetailRequest, opts ...http.CallOption) (*WorkspaceDetail, error) {
	var out WorkspaceDetail
	pattern := "/apis/v1/workspace/{name}/detail"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationWorkspaceServiceGetWorkspaceDetail))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *WorkspaceServiceHTTPClientImpl) GetWorkspaceResourceSummary(ctx context.Context, in *GetWorkspaceResourceSummaryRequest, opts ...http.CallOption) (*WorkspaceResourceSummary, error) {
	var out WorkspaceResourceSummary
	pattern := "/apis/v1/workspace/{name}/resource-summary"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationWorkspaceServiceGetWorkspaceResourceSummary))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *WorkspaceServiceHTTPClientImpl) IsWorkspaceManager(ctx context.Context, in *IsWorkspaceManagerRequest, opts ...http.CallOption) (*IsWorkspaceManagerResponse, error) {
	var out IsWorkspaceManagerResponse
	pattern := "/apis/v1/workspace/{workspaceName}/manager/{account}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationWorkspaceServiceIsWorkspaceManager))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *WorkspaceServiceHTTPClientImpl) ListClusterBindings(ctx context.Context, in *ListClusterBindingRequest, opts ...http.CallOption) (*ListClusterBindingsResult, error) {
	var out ListClusterBindingsResult
	pattern := "/apis/v1/workspace/{workspaceName}/cluster-bindings"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationWorkspaceServiceListClusterBindings))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *WorkspaceServiceHTTPClientImpl) ListWorkspaceMembers(ctx context.Context, in *ListWorkspaceMembersRequest, opts ...http.CallOption) (*ListWorkspaceMembersResponse, error) {
	var out ListWorkspaceMembersResponse
	pattern := "/apis/v1/workspace/{workspaceName}/members"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationWorkspaceServiceListWorkspaceMembers))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *WorkspaceServiceHTTPClientImpl) ListWorkspaces(ctx context.Context, in *ListOptions, opts ...http.CallOption) (*ListWorkspaceResult, error) {
	var out ListWorkspaceResult
	pattern := "/apis/v1/workspaces"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationWorkspaceServiceListWorkspaces))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *WorkspaceServiceHTTPClientImpl) SetClusterBindings(ctx context.Context, in *CreateClusterBindingsRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/apis/v1/workspace/{workspaceName}/cluster-bindings"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationWorkspaceServiceSetClusterBindings))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *WorkspaceServiceHTTPClientImpl) UpdateWorkspace(ctx context.Context, in *UpdateWorkspaceRequest, opts ...http.CallOption) (*WorkspaceDetail, error) {
	var out WorkspaceDetail
	pattern := "/apis/v1/workspace/{name}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationWorkspaceServiceUpdateWorkspace))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
