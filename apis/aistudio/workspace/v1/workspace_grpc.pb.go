// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v5.29.3
// source: aistudio/workspace/v1/workspace.proto

package v1

import (
	context "context"
	v1 "git.lianjia.com/cloudnative/kic/kic-platform/apis/usercenter/v1"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	WorkspaceService_CheckWorkspaceIsExist_FullMethodName        = "/apis.workspace.v1.WorkspaceService/CheckWorkspaceIsExist"
	WorkspaceService_CreateWorkspace_FullMethodName              = "/apis.workspace.v1.WorkspaceService/CreateWorkspace"
	WorkspaceService_UpdateWorkspace_FullMethodName              = "/apis.workspace.v1.WorkspaceService/UpdateWorkspace"
	WorkspaceService_DisableWorkspace_FullMethodName             = "/apis.workspace.v1.WorkspaceService/DisableWorkspace"
	WorkspaceService_GetWorkspaceBase_FullMethodName             = "/apis.workspace.v1.WorkspaceService/GetWorkspaceBase"
	WorkspaceService_GetWorkspaceDetail_FullMethodName           = "/apis.workspace.v1.WorkspaceService/GetWorkspaceDetail"
	WorkspaceService_ListWorkspaces_FullMethodName               = "/apis.workspace.v1.WorkspaceService/ListWorkspaces"
	WorkspaceService_GetWorkspaceResourceSummary_FullMethodName  = "/apis.workspace.v1.WorkspaceService/GetWorkspaceResourceSummary"
	WorkspaceService_CreateOrUpdateWorkspaceRobot_FullMethodName = "/apis.workspace.v1.WorkspaceService/CreateOrUpdateWorkspaceRobot"
	WorkspaceService_AddWorkspaceUsers_FullMethodName            = "/apis.workspace.v1.WorkspaceService/AddWorkspaceUsers"
	WorkspaceService_DeleteWorkspaceUser_FullMethodName          = "/apis.workspace.v1.WorkspaceService/DeleteWorkspaceUser"
	WorkspaceService_ListWorkspaceMembers_FullMethodName         = "/apis.workspace.v1.WorkspaceService/ListWorkspaceMembers"
	WorkspaceService_ChangeWorkspaceUserRole_FullMethodName      = "/apis.workspace.v1.WorkspaceService/ChangeWorkspaceUserRole"
	WorkspaceService_IsWorkspaceManager_FullMethodName           = "/apis.workspace.v1.WorkspaceService/IsWorkspaceManager"
	WorkspaceService_SetClusterBindings_FullMethodName           = "/apis.workspace.v1.WorkspaceService/SetClusterBindings"
	WorkspaceService_ListClusterBindings_FullMethodName          = "/apis.workspace.v1.WorkspaceService/ListClusterBindings"
)

// WorkspaceServiceClient is the client API for WorkspaceService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type WorkspaceServiceClient interface {
	// CheckWorkspaceIsExist 检查工作空间是否存在
	CheckWorkspaceIsExist(ctx context.Context, in *CheckWorkspaceIsExistRequest, opts ...grpc.CallOption) (*CheckWorkspaceIsExistResponse, error)
	// CreateWorkspace 创建工作空间
	CreateWorkspace(ctx context.Context, in *CreateWorkspaceRequest, opts ...grpc.CallOption) (*WorkspaceDetail, error)
	// UpdateWorkspace 更新工作空间
	UpdateWorkspace(ctx context.Context, in *UpdateWorkspaceRequest, opts ...grpc.CallOption) (*WorkspaceDetail, error)
	// DisableWorkspace 禁用工作空间
	DisableWorkspace(ctx context.Context, in *DisableWorkspaceRequest, opts ...grpc.CallOption) (*WorkspaceDetail, error)
	// GetWorkspaceBase 获取工作空间基本信息
	GetWorkspaceBase(ctx context.Context, in *GetWorkspaceBaseRequest, opts ...grpc.CallOption) (*WorkspaceBase, error)
	// GetWorkspaceDetail 获取工作空间详细信息
	GetWorkspaceDetail(ctx context.Context, in *GetWorkspaceDetailRequest, opts ...grpc.CallOption) (*WorkspaceDetail, error)
	// 获取Workspace列表, 通过访问用户的权限来返回具有权限的Workspace
	ListWorkspaces(ctx context.Context, in *ListOptions, opts ...grpc.CallOption) (*ListWorkspaceResult, error)
	// 获取资源概括
	GetWorkspaceResourceSummary(ctx context.Context, in *GetWorkspaceResourceSummaryRequest, opts ...grpc.CallOption) (*WorkspaceResourceSummary, error)
	// 创建空间机器人账号（先创建机器人用户，然后直接赋予机器人角色）
	CreateOrUpdateWorkspaceRobot(ctx context.Context, in *CreateOrUpdateRobotAccountRequest, opts ...grpc.CallOption) (*v1.UserDetail, error)
	// 工作空间添加成员(多个)
	AddWorkspaceUsers(ctx context.Context, in *AddWorkspaceUsersRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 工作空间删除成员(单个)
	DeleteWorkspaceUser(ctx context.Context, in *DeleteWorkspaceUserRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 获取工作空间的所有成员
	ListWorkspaceMembers(ctx context.Context, in *ListWorkspaceMembersRequest, opts ...grpc.CallOption) (*ListWorkspaceMembersResponse, error)
	// 更改空间用户角色
	ChangeWorkspaceUserRole(ctx context.Context, in *ChangeWorkspaceUserRoleRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 判断用户是不是工作空间的管理员
	IsWorkspaceManager(ctx context.Context, in *IsWorkspaceManagerRequest, opts ...grpc.CallOption) (*IsWorkspaceManagerResponse, error)
	SetClusterBindings(ctx context.Context, in *CreateClusterBindingsRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	ListClusterBindings(ctx context.Context, in *ListClusterBindingRequest, opts ...grpc.CallOption) (*ListClusterBindingsResult, error)
}

type workspaceServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewWorkspaceServiceClient(cc grpc.ClientConnInterface) WorkspaceServiceClient {
	return &workspaceServiceClient{cc}
}

func (c *workspaceServiceClient) CheckWorkspaceIsExist(ctx context.Context, in *CheckWorkspaceIsExistRequest, opts ...grpc.CallOption) (*CheckWorkspaceIsExistResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CheckWorkspaceIsExistResponse)
	err := c.cc.Invoke(ctx, WorkspaceService_CheckWorkspaceIsExist_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *workspaceServiceClient) CreateWorkspace(ctx context.Context, in *CreateWorkspaceRequest, opts ...grpc.CallOption) (*WorkspaceDetail, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(WorkspaceDetail)
	err := c.cc.Invoke(ctx, WorkspaceService_CreateWorkspace_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *workspaceServiceClient) UpdateWorkspace(ctx context.Context, in *UpdateWorkspaceRequest, opts ...grpc.CallOption) (*WorkspaceDetail, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(WorkspaceDetail)
	err := c.cc.Invoke(ctx, WorkspaceService_UpdateWorkspace_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *workspaceServiceClient) DisableWorkspace(ctx context.Context, in *DisableWorkspaceRequest, opts ...grpc.CallOption) (*WorkspaceDetail, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(WorkspaceDetail)
	err := c.cc.Invoke(ctx, WorkspaceService_DisableWorkspace_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *workspaceServiceClient) GetWorkspaceBase(ctx context.Context, in *GetWorkspaceBaseRequest, opts ...grpc.CallOption) (*WorkspaceBase, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(WorkspaceBase)
	err := c.cc.Invoke(ctx, WorkspaceService_GetWorkspaceBase_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *workspaceServiceClient) GetWorkspaceDetail(ctx context.Context, in *GetWorkspaceDetailRequest, opts ...grpc.CallOption) (*WorkspaceDetail, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(WorkspaceDetail)
	err := c.cc.Invoke(ctx, WorkspaceService_GetWorkspaceDetail_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *workspaceServiceClient) ListWorkspaces(ctx context.Context, in *ListOptions, opts ...grpc.CallOption) (*ListWorkspaceResult, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListWorkspaceResult)
	err := c.cc.Invoke(ctx, WorkspaceService_ListWorkspaces_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *workspaceServiceClient) GetWorkspaceResourceSummary(ctx context.Context, in *GetWorkspaceResourceSummaryRequest, opts ...grpc.CallOption) (*WorkspaceResourceSummary, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(WorkspaceResourceSummary)
	err := c.cc.Invoke(ctx, WorkspaceService_GetWorkspaceResourceSummary_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *workspaceServiceClient) CreateOrUpdateWorkspaceRobot(ctx context.Context, in *CreateOrUpdateRobotAccountRequest, opts ...grpc.CallOption) (*v1.UserDetail, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v1.UserDetail)
	err := c.cc.Invoke(ctx, WorkspaceService_CreateOrUpdateWorkspaceRobot_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *workspaceServiceClient) AddWorkspaceUsers(ctx context.Context, in *AddWorkspaceUsersRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, WorkspaceService_AddWorkspaceUsers_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *workspaceServiceClient) DeleteWorkspaceUser(ctx context.Context, in *DeleteWorkspaceUserRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, WorkspaceService_DeleteWorkspaceUser_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *workspaceServiceClient) ListWorkspaceMembers(ctx context.Context, in *ListWorkspaceMembersRequest, opts ...grpc.CallOption) (*ListWorkspaceMembersResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListWorkspaceMembersResponse)
	err := c.cc.Invoke(ctx, WorkspaceService_ListWorkspaceMembers_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *workspaceServiceClient) ChangeWorkspaceUserRole(ctx context.Context, in *ChangeWorkspaceUserRoleRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, WorkspaceService_ChangeWorkspaceUserRole_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *workspaceServiceClient) IsWorkspaceManager(ctx context.Context, in *IsWorkspaceManagerRequest, opts ...grpc.CallOption) (*IsWorkspaceManagerResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(IsWorkspaceManagerResponse)
	err := c.cc.Invoke(ctx, WorkspaceService_IsWorkspaceManager_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *workspaceServiceClient) SetClusterBindings(ctx context.Context, in *CreateClusterBindingsRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, WorkspaceService_SetClusterBindings_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *workspaceServiceClient) ListClusterBindings(ctx context.Context, in *ListClusterBindingRequest, opts ...grpc.CallOption) (*ListClusterBindingsResult, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListClusterBindingsResult)
	err := c.cc.Invoke(ctx, WorkspaceService_ListClusterBindings_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// WorkspaceServiceServer is the server API for WorkspaceService service.
// All implementations must embed UnimplementedWorkspaceServiceServer
// for forward compatibility
type WorkspaceServiceServer interface {
	// CheckWorkspaceIsExist 检查工作空间是否存在
	CheckWorkspaceIsExist(context.Context, *CheckWorkspaceIsExistRequest) (*CheckWorkspaceIsExistResponse, error)
	// CreateWorkspace 创建工作空间
	CreateWorkspace(context.Context, *CreateWorkspaceRequest) (*WorkspaceDetail, error)
	// UpdateWorkspace 更新工作空间
	UpdateWorkspace(context.Context, *UpdateWorkspaceRequest) (*WorkspaceDetail, error)
	// DisableWorkspace 禁用工作空间
	DisableWorkspace(context.Context, *DisableWorkspaceRequest) (*WorkspaceDetail, error)
	// GetWorkspaceBase 获取工作空间基本信息
	GetWorkspaceBase(context.Context, *GetWorkspaceBaseRequest) (*WorkspaceBase, error)
	// GetWorkspaceDetail 获取工作空间详细信息
	GetWorkspaceDetail(context.Context, *GetWorkspaceDetailRequest) (*WorkspaceDetail, error)
	// 获取Workspace列表, 通过访问用户的权限来返回具有权限的Workspace
	ListWorkspaces(context.Context, *ListOptions) (*ListWorkspaceResult, error)
	// 获取资源概括
	GetWorkspaceResourceSummary(context.Context, *GetWorkspaceResourceSummaryRequest) (*WorkspaceResourceSummary, error)
	// 创建空间机器人账号（先创建机器人用户，然后直接赋予机器人角色）
	CreateOrUpdateWorkspaceRobot(context.Context, *CreateOrUpdateRobotAccountRequest) (*v1.UserDetail, error)
	// 工作空间添加成员(多个)
	AddWorkspaceUsers(context.Context, *AddWorkspaceUsersRequest) (*emptypb.Empty, error)
	// 工作空间删除成员(单个)
	DeleteWorkspaceUser(context.Context, *DeleteWorkspaceUserRequest) (*emptypb.Empty, error)
	// 获取工作空间的所有成员
	ListWorkspaceMembers(context.Context, *ListWorkspaceMembersRequest) (*ListWorkspaceMembersResponse, error)
	// 更改空间用户角色
	ChangeWorkspaceUserRole(context.Context, *ChangeWorkspaceUserRoleRequest) (*emptypb.Empty, error)
	// 判断用户是不是工作空间的管理员
	IsWorkspaceManager(context.Context, *IsWorkspaceManagerRequest) (*IsWorkspaceManagerResponse, error)
	SetClusterBindings(context.Context, *CreateClusterBindingsRequest) (*emptypb.Empty, error)
	ListClusterBindings(context.Context, *ListClusterBindingRequest) (*ListClusterBindingsResult, error)
	mustEmbedUnimplementedWorkspaceServiceServer()
}

// UnimplementedWorkspaceServiceServer must be embedded to have forward compatible implementations.
type UnimplementedWorkspaceServiceServer struct {
}

func (UnimplementedWorkspaceServiceServer) CheckWorkspaceIsExist(context.Context, *CheckWorkspaceIsExistRequest) (*CheckWorkspaceIsExistResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckWorkspaceIsExist not implemented")
}
func (UnimplementedWorkspaceServiceServer) CreateWorkspace(context.Context, *CreateWorkspaceRequest) (*WorkspaceDetail, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateWorkspace not implemented")
}
func (UnimplementedWorkspaceServiceServer) UpdateWorkspace(context.Context, *UpdateWorkspaceRequest) (*WorkspaceDetail, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateWorkspace not implemented")
}
func (UnimplementedWorkspaceServiceServer) DisableWorkspace(context.Context, *DisableWorkspaceRequest) (*WorkspaceDetail, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DisableWorkspace not implemented")
}
func (UnimplementedWorkspaceServiceServer) GetWorkspaceBase(context.Context, *GetWorkspaceBaseRequest) (*WorkspaceBase, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetWorkspaceBase not implemented")
}
func (UnimplementedWorkspaceServiceServer) GetWorkspaceDetail(context.Context, *GetWorkspaceDetailRequest) (*WorkspaceDetail, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetWorkspaceDetail not implemented")
}
func (UnimplementedWorkspaceServiceServer) ListWorkspaces(context.Context, *ListOptions) (*ListWorkspaceResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListWorkspaces not implemented")
}
func (UnimplementedWorkspaceServiceServer) GetWorkspaceResourceSummary(context.Context, *GetWorkspaceResourceSummaryRequest) (*WorkspaceResourceSummary, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetWorkspaceResourceSummary not implemented")
}
func (UnimplementedWorkspaceServiceServer) CreateOrUpdateWorkspaceRobot(context.Context, *CreateOrUpdateRobotAccountRequest) (*v1.UserDetail, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateOrUpdateWorkspaceRobot not implemented")
}
func (UnimplementedWorkspaceServiceServer) AddWorkspaceUsers(context.Context, *AddWorkspaceUsersRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddWorkspaceUsers not implemented")
}
func (UnimplementedWorkspaceServiceServer) DeleteWorkspaceUser(context.Context, *DeleteWorkspaceUserRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteWorkspaceUser not implemented")
}
func (UnimplementedWorkspaceServiceServer) ListWorkspaceMembers(context.Context, *ListWorkspaceMembersRequest) (*ListWorkspaceMembersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListWorkspaceMembers not implemented")
}
func (UnimplementedWorkspaceServiceServer) ChangeWorkspaceUserRole(context.Context, *ChangeWorkspaceUserRoleRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ChangeWorkspaceUserRole not implemented")
}
func (UnimplementedWorkspaceServiceServer) IsWorkspaceManager(context.Context, *IsWorkspaceManagerRequest) (*IsWorkspaceManagerResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IsWorkspaceManager not implemented")
}
func (UnimplementedWorkspaceServiceServer) SetClusterBindings(context.Context, *CreateClusterBindingsRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetClusterBindings not implemented")
}
func (UnimplementedWorkspaceServiceServer) ListClusterBindings(context.Context, *ListClusterBindingRequest) (*ListClusterBindingsResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListClusterBindings not implemented")
}
func (UnimplementedWorkspaceServiceServer) mustEmbedUnimplementedWorkspaceServiceServer() {}

// UnsafeWorkspaceServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to WorkspaceServiceServer will
// result in compilation errors.
type UnsafeWorkspaceServiceServer interface {
	mustEmbedUnimplementedWorkspaceServiceServer()
}

func RegisterWorkspaceServiceServer(s grpc.ServiceRegistrar, srv WorkspaceServiceServer) {
	s.RegisterService(&WorkspaceService_ServiceDesc, srv)
}

func _WorkspaceService_CheckWorkspaceIsExist_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckWorkspaceIsExistRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WorkspaceServiceServer).CheckWorkspaceIsExist(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WorkspaceService_CheckWorkspaceIsExist_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WorkspaceServiceServer).CheckWorkspaceIsExist(ctx, req.(*CheckWorkspaceIsExistRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WorkspaceService_CreateWorkspace_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateWorkspaceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WorkspaceServiceServer).CreateWorkspace(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WorkspaceService_CreateWorkspace_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WorkspaceServiceServer).CreateWorkspace(ctx, req.(*CreateWorkspaceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WorkspaceService_UpdateWorkspace_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateWorkspaceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WorkspaceServiceServer).UpdateWorkspace(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WorkspaceService_UpdateWorkspace_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WorkspaceServiceServer).UpdateWorkspace(ctx, req.(*UpdateWorkspaceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WorkspaceService_DisableWorkspace_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DisableWorkspaceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WorkspaceServiceServer).DisableWorkspace(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WorkspaceService_DisableWorkspace_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WorkspaceServiceServer).DisableWorkspace(ctx, req.(*DisableWorkspaceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WorkspaceService_GetWorkspaceBase_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetWorkspaceBaseRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WorkspaceServiceServer).GetWorkspaceBase(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WorkspaceService_GetWorkspaceBase_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WorkspaceServiceServer).GetWorkspaceBase(ctx, req.(*GetWorkspaceBaseRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WorkspaceService_GetWorkspaceDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetWorkspaceDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WorkspaceServiceServer).GetWorkspaceDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WorkspaceService_GetWorkspaceDetail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WorkspaceServiceServer).GetWorkspaceDetail(ctx, req.(*GetWorkspaceDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WorkspaceService_ListWorkspaces_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListOptions)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WorkspaceServiceServer).ListWorkspaces(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WorkspaceService_ListWorkspaces_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WorkspaceServiceServer).ListWorkspaces(ctx, req.(*ListOptions))
	}
	return interceptor(ctx, in, info, handler)
}

func _WorkspaceService_GetWorkspaceResourceSummary_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetWorkspaceResourceSummaryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WorkspaceServiceServer).GetWorkspaceResourceSummary(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WorkspaceService_GetWorkspaceResourceSummary_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WorkspaceServiceServer).GetWorkspaceResourceSummary(ctx, req.(*GetWorkspaceResourceSummaryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WorkspaceService_CreateOrUpdateWorkspaceRobot_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateOrUpdateRobotAccountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WorkspaceServiceServer).CreateOrUpdateWorkspaceRobot(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WorkspaceService_CreateOrUpdateWorkspaceRobot_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WorkspaceServiceServer).CreateOrUpdateWorkspaceRobot(ctx, req.(*CreateOrUpdateRobotAccountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WorkspaceService_AddWorkspaceUsers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddWorkspaceUsersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WorkspaceServiceServer).AddWorkspaceUsers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WorkspaceService_AddWorkspaceUsers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WorkspaceServiceServer).AddWorkspaceUsers(ctx, req.(*AddWorkspaceUsersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WorkspaceService_DeleteWorkspaceUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteWorkspaceUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WorkspaceServiceServer).DeleteWorkspaceUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WorkspaceService_DeleteWorkspaceUser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WorkspaceServiceServer).DeleteWorkspaceUser(ctx, req.(*DeleteWorkspaceUserRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WorkspaceService_ListWorkspaceMembers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListWorkspaceMembersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WorkspaceServiceServer).ListWorkspaceMembers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WorkspaceService_ListWorkspaceMembers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WorkspaceServiceServer).ListWorkspaceMembers(ctx, req.(*ListWorkspaceMembersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WorkspaceService_ChangeWorkspaceUserRole_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChangeWorkspaceUserRoleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WorkspaceServiceServer).ChangeWorkspaceUserRole(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WorkspaceService_ChangeWorkspaceUserRole_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WorkspaceServiceServer).ChangeWorkspaceUserRole(ctx, req.(*ChangeWorkspaceUserRoleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WorkspaceService_IsWorkspaceManager_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IsWorkspaceManagerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WorkspaceServiceServer).IsWorkspaceManager(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WorkspaceService_IsWorkspaceManager_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WorkspaceServiceServer).IsWorkspaceManager(ctx, req.(*IsWorkspaceManagerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WorkspaceService_SetClusterBindings_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateClusterBindingsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WorkspaceServiceServer).SetClusterBindings(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WorkspaceService_SetClusterBindings_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WorkspaceServiceServer).SetClusterBindings(ctx, req.(*CreateClusterBindingsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WorkspaceService_ListClusterBindings_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListClusterBindingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WorkspaceServiceServer).ListClusterBindings(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WorkspaceService_ListClusterBindings_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WorkspaceServiceServer).ListClusterBindings(ctx, req.(*ListClusterBindingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// WorkspaceService_ServiceDesc is the grpc.ServiceDesc for WorkspaceService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var WorkspaceService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "apis.workspace.v1.WorkspaceService",
	HandlerType: (*WorkspaceServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CheckWorkspaceIsExist",
			Handler:    _WorkspaceService_CheckWorkspaceIsExist_Handler,
		},
		{
			MethodName: "CreateWorkspace",
			Handler:    _WorkspaceService_CreateWorkspace_Handler,
		},
		{
			MethodName: "UpdateWorkspace",
			Handler:    _WorkspaceService_UpdateWorkspace_Handler,
		},
		{
			MethodName: "DisableWorkspace",
			Handler:    _WorkspaceService_DisableWorkspace_Handler,
		},
		{
			MethodName: "GetWorkspaceBase",
			Handler:    _WorkspaceService_GetWorkspaceBase_Handler,
		},
		{
			MethodName: "GetWorkspaceDetail",
			Handler:    _WorkspaceService_GetWorkspaceDetail_Handler,
		},
		{
			MethodName: "ListWorkspaces",
			Handler:    _WorkspaceService_ListWorkspaces_Handler,
		},
		{
			MethodName: "GetWorkspaceResourceSummary",
			Handler:    _WorkspaceService_GetWorkspaceResourceSummary_Handler,
		},
		{
			MethodName: "CreateOrUpdateWorkspaceRobot",
			Handler:    _WorkspaceService_CreateOrUpdateWorkspaceRobot_Handler,
		},
		{
			MethodName: "AddWorkspaceUsers",
			Handler:    _WorkspaceService_AddWorkspaceUsers_Handler,
		},
		{
			MethodName: "DeleteWorkspaceUser",
			Handler:    _WorkspaceService_DeleteWorkspaceUser_Handler,
		},
		{
			MethodName: "ListWorkspaceMembers",
			Handler:    _WorkspaceService_ListWorkspaceMembers_Handler,
		},
		{
			MethodName: "ChangeWorkspaceUserRole",
			Handler:    _WorkspaceService_ChangeWorkspaceUserRole_Handler,
		},
		{
			MethodName: "IsWorkspaceManager",
			Handler:    _WorkspaceService_IsWorkspaceManager_Handler,
		},
		{
			MethodName: "SetClusterBindings",
			Handler:    _WorkspaceService_SetClusterBindings_Handler,
		},
		{
			MethodName: "ListClusterBindings",
			Handler:    _WorkspaceService_ListClusterBindings_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "aistudio/workspace/v1/workspace.proto",
}
