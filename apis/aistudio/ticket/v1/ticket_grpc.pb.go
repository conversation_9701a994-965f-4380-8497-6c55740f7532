// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v5.29.3
// source: aistudio/ticket/v1/ticket.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	TicketService_CreateTicket_FullMethodName    = "/apis.aistudio.ticket.v1.TicketService/CreateTicket"
	TicketService_RevokeTicket_FullMethodName    = "/apis.aistudio.ticket.v1.TicketService/RevokeTicket"
	TicketService_ApproveTicket_FullMethodName   = "/apis.aistudio.ticket.v1.TicketService/ApproveTicket"
	TicketService_RejectTicket_FullMethodName    = "/apis.aistudio.ticket.v1.TicketService/RejectTicket"
	TicketService_ListTickets_FullMethodName     = "/apis.aistudio.ticket.v1.TicketService/ListTickets"
	TicketService_GetTicketDetail_FullMethodName = "/apis.aistudio.ticket.v1.TicketService/GetTicketDetail"
)

// TicketServiceClient is the client API for TicketService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type TicketServiceClient interface {
	// 创建工单
	CreateTicket(ctx context.Context, in *CreateTicketRequest, opts ...grpc.CallOption) (*CreateTicketResponse, error)
	// 撤销工单
	RevokeTicket(ctx context.Context, in *OperateTicketRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	ApproveTicket(ctx context.Context, in *OperateTicketRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	RejectTicket(ctx context.Context, in *OperateTicketRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 获取工单列表
	ListTickets(ctx context.Context, in *ListTicketsOptions, opts ...grpc.CallOption) (*ListTicketsResult, error)
	GetTicketDetail(ctx context.Context, in *GetTicketDetailRequest, opts ...grpc.CallOption) (*DisplayTicket, error)
}

type ticketServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewTicketServiceClient(cc grpc.ClientConnInterface) TicketServiceClient {
	return &ticketServiceClient{cc}
}

func (c *ticketServiceClient) CreateTicket(ctx context.Context, in *CreateTicketRequest, opts ...grpc.CallOption) (*CreateTicketResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateTicketResponse)
	err := c.cc.Invoke(ctx, TicketService_CreateTicket_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ticketServiceClient) RevokeTicket(ctx context.Context, in *OperateTicketRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, TicketService_RevokeTicket_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ticketServiceClient) ApproveTicket(ctx context.Context, in *OperateTicketRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, TicketService_ApproveTicket_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ticketServiceClient) RejectTicket(ctx context.Context, in *OperateTicketRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, TicketService_RejectTicket_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ticketServiceClient) ListTickets(ctx context.Context, in *ListTicketsOptions, opts ...grpc.CallOption) (*ListTicketsResult, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListTicketsResult)
	err := c.cc.Invoke(ctx, TicketService_ListTickets_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ticketServiceClient) GetTicketDetail(ctx context.Context, in *GetTicketDetailRequest, opts ...grpc.CallOption) (*DisplayTicket, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DisplayTicket)
	err := c.cc.Invoke(ctx, TicketService_GetTicketDetail_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TicketServiceServer is the server API for TicketService service.
// All implementations must embed UnimplementedTicketServiceServer
// for forward compatibility
type TicketServiceServer interface {
	// 创建工单
	CreateTicket(context.Context, *CreateTicketRequest) (*CreateTicketResponse, error)
	// 撤销工单
	RevokeTicket(context.Context, *OperateTicketRequest) (*emptypb.Empty, error)
	ApproveTicket(context.Context, *OperateTicketRequest) (*emptypb.Empty, error)
	RejectTicket(context.Context, *OperateTicketRequest) (*emptypb.Empty, error)
	// 获取工单列表
	ListTickets(context.Context, *ListTicketsOptions) (*ListTicketsResult, error)
	GetTicketDetail(context.Context, *GetTicketDetailRequest) (*DisplayTicket, error)
	mustEmbedUnimplementedTicketServiceServer()
}

// UnimplementedTicketServiceServer must be embedded to have forward compatible implementations.
type UnimplementedTicketServiceServer struct {
}

func (UnimplementedTicketServiceServer) CreateTicket(context.Context, *CreateTicketRequest) (*CreateTicketResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateTicket not implemented")
}
func (UnimplementedTicketServiceServer) RevokeTicket(context.Context, *OperateTicketRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RevokeTicket not implemented")
}
func (UnimplementedTicketServiceServer) ApproveTicket(context.Context, *OperateTicketRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ApproveTicket not implemented")
}
func (UnimplementedTicketServiceServer) RejectTicket(context.Context, *OperateTicketRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RejectTicket not implemented")
}
func (UnimplementedTicketServiceServer) ListTickets(context.Context, *ListTicketsOptions) (*ListTicketsResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListTickets not implemented")
}
func (UnimplementedTicketServiceServer) GetTicketDetail(context.Context, *GetTicketDetailRequest) (*DisplayTicket, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTicketDetail not implemented")
}
func (UnimplementedTicketServiceServer) mustEmbedUnimplementedTicketServiceServer() {}

// UnsafeTicketServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to TicketServiceServer will
// result in compilation errors.
type UnsafeTicketServiceServer interface {
	mustEmbedUnimplementedTicketServiceServer()
}

func RegisterTicketServiceServer(s grpc.ServiceRegistrar, srv TicketServiceServer) {
	s.RegisterService(&TicketService_ServiceDesc, srv)
}

func _TicketService_CreateTicket_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateTicketRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TicketServiceServer).CreateTicket(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TicketService_CreateTicket_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TicketServiceServer).CreateTicket(ctx, req.(*CreateTicketRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TicketService_RevokeTicket_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OperateTicketRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TicketServiceServer).RevokeTicket(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TicketService_RevokeTicket_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TicketServiceServer).RevokeTicket(ctx, req.(*OperateTicketRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TicketService_ApproveTicket_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OperateTicketRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TicketServiceServer).ApproveTicket(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TicketService_ApproveTicket_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TicketServiceServer).ApproveTicket(ctx, req.(*OperateTicketRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TicketService_RejectTicket_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OperateTicketRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TicketServiceServer).RejectTicket(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TicketService_RejectTicket_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TicketServiceServer).RejectTicket(ctx, req.(*OperateTicketRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TicketService_ListTickets_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListTicketsOptions)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TicketServiceServer).ListTickets(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TicketService_ListTickets_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TicketServiceServer).ListTickets(ctx, req.(*ListTicketsOptions))
	}
	return interceptor(ctx, in, info, handler)
}

func _TicketService_GetTicketDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTicketDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TicketServiceServer).GetTicketDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TicketService_GetTicketDetail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TicketServiceServer).GetTicketDetail(ctx, req.(*GetTicketDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// TicketService_ServiceDesc is the grpc.ServiceDesc for TicketService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var TicketService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "apis.aistudio.ticket.v1.TicketService",
	HandlerType: (*TicketServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateTicket",
			Handler:    _TicketService_CreateTicket_Handler,
		},
		{
			MethodName: "RevokeTicket",
			Handler:    _TicketService_RevokeTicket_Handler,
		},
		{
			MethodName: "ApproveTicket",
			Handler:    _TicketService_ApproveTicket_Handler,
		},
		{
			MethodName: "RejectTicket",
			Handler:    _TicketService_RejectTicket_Handler,
		},
		{
			MethodName: "ListTickets",
			Handler:    _TicketService_ListTickets_Handler,
		},
		{
			MethodName: "GetTicketDetail",
			Handler:    _TicketService_GetTicketDetail_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "aistudio/ticket/v1/ticket.proto",
}
