// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v5.29.3
// source: aistudio/object/v1/object.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	ObjectStorageService_CreateOrUpdateBucket_FullMethodName = "/apis.aistudio.object.v1.ObjectStorageService/CreateOrUpdateBucket"
	ObjectStorageService_DeleteBucket_FullMethodName         = "/apis.aistudio.object.v1.ObjectStorageService/DeleteBucket"
	ObjectStorageService_ListBuckets_FullMethodName          = "/apis.aistudio.object.v1.ObjectStorageService/ListBuckets"
)

// ObjectStorageServiceClient is the client API for ObjectStorageService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ObjectStorageServiceClient interface {
	CreateOrUpdateBucket(ctx context.Context, in *CreateBucketRequest, opts ...grpc.CallOption) (*Bucket, error)
	DeleteBucket(ctx context.Context, in *DeleteBucketRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	ListBuckets(ctx context.Context, in *ListBucketRequest, opts ...grpc.CallOption) (*ListBucketResult, error)
}

type objectStorageServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewObjectStorageServiceClient(cc grpc.ClientConnInterface) ObjectStorageServiceClient {
	return &objectStorageServiceClient{cc}
}

func (c *objectStorageServiceClient) CreateOrUpdateBucket(ctx context.Context, in *CreateBucketRequest, opts ...grpc.CallOption) (*Bucket, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(Bucket)
	err := c.cc.Invoke(ctx, ObjectStorageService_CreateOrUpdateBucket_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *objectStorageServiceClient) DeleteBucket(ctx context.Context, in *DeleteBucketRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, ObjectStorageService_DeleteBucket_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *objectStorageServiceClient) ListBuckets(ctx context.Context, in *ListBucketRequest, opts ...grpc.CallOption) (*ListBucketResult, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListBucketResult)
	err := c.cc.Invoke(ctx, ObjectStorageService_ListBuckets_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ObjectStorageServiceServer is the server API for ObjectStorageService service.
// All implementations must embed UnimplementedObjectStorageServiceServer
// for forward compatibility
type ObjectStorageServiceServer interface {
	CreateOrUpdateBucket(context.Context, *CreateBucketRequest) (*Bucket, error)
	DeleteBucket(context.Context, *DeleteBucketRequest) (*emptypb.Empty, error)
	ListBuckets(context.Context, *ListBucketRequest) (*ListBucketResult, error)
	mustEmbedUnimplementedObjectStorageServiceServer()
}

// UnimplementedObjectStorageServiceServer must be embedded to have forward compatible implementations.
type UnimplementedObjectStorageServiceServer struct {
}

func (UnimplementedObjectStorageServiceServer) CreateOrUpdateBucket(context.Context, *CreateBucketRequest) (*Bucket, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateOrUpdateBucket not implemented")
}
func (UnimplementedObjectStorageServiceServer) DeleteBucket(context.Context, *DeleteBucketRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteBucket not implemented")
}
func (UnimplementedObjectStorageServiceServer) ListBuckets(context.Context, *ListBucketRequest) (*ListBucketResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListBuckets not implemented")
}
func (UnimplementedObjectStorageServiceServer) mustEmbedUnimplementedObjectStorageServiceServer() {}

// UnsafeObjectStorageServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ObjectStorageServiceServer will
// result in compilation errors.
type UnsafeObjectStorageServiceServer interface {
	mustEmbedUnimplementedObjectStorageServiceServer()
}

func RegisterObjectStorageServiceServer(s grpc.ServiceRegistrar, srv ObjectStorageServiceServer) {
	s.RegisterService(&ObjectStorageService_ServiceDesc, srv)
}

func _ObjectStorageService_CreateOrUpdateBucket_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateBucketRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ObjectStorageServiceServer).CreateOrUpdateBucket(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ObjectStorageService_CreateOrUpdateBucket_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ObjectStorageServiceServer).CreateOrUpdateBucket(ctx, req.(*CreateBucketRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ObjectStorageService_DeleteBucket_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteBucketRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ObjectStorageServiceServer).DeleteBucket(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ObjectStorageService_DeleteBucket_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ObjectStorageServiceServer).DeleteBucket(ctx, req.(*DeleteBucketRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ObjectStorageService_ListBuckets_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListBucketRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ObjectStorageServiceServer).ListBuckets(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ObjectStorageService_ListBuckets_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ObjectStorageServiceServer).ListBuckets(ctx, req.(*ListBucketRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// ObjectStorageService_ServiceDesc is the grpc.ServiceDesc for ObjectStorageService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ObjectStorageService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "apis.aistudio.object.v1.ObjectStorageService",
	HandlerType: (*ObjectStorageServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateOrUpdateBucket",
			Handler:    _ObjectStorageService_CreateOrUpdateBucket_Handler,
		},
		{
			MethodName: "DeleteBucket",
			Handler:    _ObjectStorageService_DeleteBucket_Handler,
		},
		{
			MethodName: "ListBuckets",
			Handler:    _ObjectStorageService_ListBuckets_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "aistudio/object/v1/object.proto",
}
