// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v5.29.3
// source: aistudio/object/v1/object.proto

package v1

import (
	common "git.lianjia.com/cloudnative/kic/kic-platform/apis/common"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type BackendType int32

const (
	BackendType_Direct     BackendType = 0
	BackendType_DeGradable BackendType = 1
)

// Enum value maps for BackendType.
var (
	BackendType_name = map[int32]string{
		0: "Direct",
		1: "DeGradable",
	}
	BackendType_value = map[string]int32{
		"Direct":     0,
		"DeGradable": 1,
	}
)

func (x BackendType) Enum() *BackendType {
	p := new(BackendType)
	*p = x
	return p
}

func (x BackendType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BackendType) Descriptor() protoreflect.EnumDescriptor {
	return file_aistudio_object_v1_object_proto_enumTypes[0].Descriptor()
}

func (BackendType) Type() protoreflect.EnumType {
	return &file_aistudio_object_v1_object_proto_enumTypes[0]
}

func (x BackendType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BackendType.Descriptor instead.
func (BackendType) EnumDescriptor() ([]byte, []int) {
	return file_aistudio_object_v1_object_proto_rawDescGZIP(), []int{0}
}

type CacheAcceleration_CachePolicy_Operator int32

const (
	CacheAcceleration_CachePolicy_Exact     CacheAcceleration_CachePolicy_Operator = 0
	CacheAcceleration_CachePolicy_Regexp    CacheAcceleration_CachePolicy_Operator = 1
	CacheAcceleration_CachePolicy_Contains  CacheAcceleration_CachePolicy_Operator = 2
	CacheAcceleration_CachePolicy_StartWith CacheAcceleration_CachePolicy_Operator = 3
	CacheAcceleration_CachePolicy_EndWith   CacheAcceleration_CachePolicy_Operator = 4
	CacheAcceleration_CachePolicy_In        CacheAcceleration_CachePolicy_Operator = 5
)

// Enum value maps for CacheAcceleration_CachePolicy_Operator.
var (
	CacheAcceleration_CachePolicy_Operator_name = map[int32]string{
		0: "Exact",
		1: "Regexp",
		2: "Contains",
		3: "StartWith",
		4: "EndWith",
		5: "In",
	}
	CacheAcceleration_CachePolicy_Operator_value = map[string]int32{
		"Exact":     0,
		"Regexp":    1,
		"Contains":  2,
		"StartWith": 3,
		"EndWith":   4,
		"In":        5,
	}
)

func (x CacheAcceleration_CachePolicy_Operator) Enum() *CacheAcceleration_CachePolicy_Operator {
	p := new(CacheAcceleration_CachePolicy_Operator)
	*p = x
	return p
}

func (x CacheAcceleration_CachePolicy_Operator) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CacheAcceleration_CachePolicy_Operator) Descriptor() protoreflect.EnumDescriptor {
	return file_aistudio_object_v1_object_proto_enumTypes[1].Descriptor()
}

func (CacheAcceleration_CachePolicy_Operator) Type() protoreflect.EnumType {
	return &file_aistudio_object_v1_object_proto_enumTypes[1]
}

func (x CacheAcceleration_CachePolicy_Operator) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CacheAcceleration_CachePolicy_Operator.Descriptor instead.
func (CacheAcceleration_CachePolicy_Operator) EnumDescriptor() ([]byte, []int) {
	return file_aistudio_object_v1_object_proto_rawDescGZIP(), []int{4, 0, 0}
}

type Bucket struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                  string             `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                string             `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	WorkspaceName       string             `protobuf:"bytes,3,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Region              string             `protobuf:"bytes,4,opt,name=region,proto3" json:"region,omitempty"`
	Owners              []string           `protobuf:"bytes,5,rep,name=owners,proto3" json:"owners,omitempty"`
	Creator             string             `protobuf:"bytes,6,opt,name=creator,proto3" json:"creator,omitempty"`
	MimeTransferEnabled bool               `protobuf:"varint,7,opt,name=mimeTransferEnabled,proto3" json:"mimeTransferEnabled,omitempty"`
	BackendType         BackendType        `protobuf:"varint,8,opt,name=backendType,proto3,enum=apis.aistudio.object.v1.BackendType" json:"backendType,omitempty"`
	Backend             *Backend           `protobuf:"bytes,9,opt,name=backend,proto3" json:"backend,omitempty"`
	CacheAcceleration   *CacheAcceleration `protobuf:"bytes,10,opt,name=cacheAcceleration,proto3" json:"cacheAcceleration,omitempty"` // 缓存加速配置
	Description         string             `protobuf:"bytes,11,opt,name=description,proto3" json:"description,omitempty"`
	DisplayName         string             `protobuf:"bytes,12,opt,name=displayName,proto3" json:"displayName,omitempty"`
	AccessPolicy        string             `protobuf:"bytes,13,opt,name=accessPolicy,proto3" json:"accessPolicy,omitempty"` //访问权限
	CreateTime          string             `protobuf:"bytes,14,opt,name=createTime,proto3" json:"createTime,omitempty"`
	UpdateTime          string             `protobuf:"bytes,15,opt,name=updateTime,proto3" json:"updateTime,omitempty"`
	Status              *BucketStatus      `protobuf:"bytes,16,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *Bucket) Reset() {
	*x = Bucket{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_object_v1_object_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Bucket) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Bucket) ProtoMessage() {}

func (x *Bucket) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_object_v1_object_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Bucket.ProtoReflect.Descriptor instead.
func (*Bucket) Descriptor() ([]byte, []int) {
	return file_aistudio_object_v1_object_proto_rawDescGZIP(), []int{0}
}

func (x *Bucket) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Bucket) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Bucket) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *Bucket) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *Bucket) GetOwners() []string {
	if x != nil {
		return x.Owners
	}
	return nil
}

func (x *Bucket) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *Bucket) GetMimeTransferEnabled() bool {
	if x != nil {
		return x.MimeTransferEnabled
	}
	return false
}

func (x *Bucket) GetBackendType() BackendType {
	if x != nil {
		return x.BackendType
	}
	return BackendType_Direct
}

func (x *Bucket) GetBackend() *Backend {
	if x != nil {
		return x.Backend
	}
	return nil
}

func (x *Bucket) GetCacheAcceleration() *CacheAcceleration {
	if x != nil {
		return x.CacheAcceleration
	}
	return nil
}

func (x *Bucket) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Bucket) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *Bucket) GetAccessPolicy() string {
	if x != nil {
		return x.AccessPolicy
	}
	return ""
}

func (x *Bucket) GetCreateTime() string {
	if x != nil {
		return x.CreateTime
	}
	return ""
}

func (x *Bucket) GetUpdateTime() string {
	if x != nil {
		return x.UpdateTime
	}
	return ""
}

func (x *Bucket) GetStatus() *BucketStatus {
	if x != nil {
		return x.Status
	}
	return nil
}

type BucketStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	State      string              `protobuf:"bytes,1,opt,name=state,proto3" json:"state,omitempty"`           //状态
	Message    string              `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`       //状态信息
	Conditions []*common.Condition `protobuf:"bytes,3,rep,name=conditions,proto3" json:"conditions,omitempty"` //状态条件
	AccessKey  string              `protobuf:"bytes,4,opt,name=accessKey,proto3" json:"accessKey,omitempty"`   //AccessKey
	SecretKey  string              `protobuf:"bytes,5,opt,name=secretKey,proto3" json:"secretKey,omitempty"`   //SecretKey
}

func (x *BucketStatus) Reset() {
	*x = BucketStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_object_v1_object_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BucketStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BucketStatus) ProtoMessage() {}

func (x *BucketStatus) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_object_v1_object_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BucketStatus.ProtoReflect.Descriptor instead.
func (*BucketStatus) Descriptor() ([]byte, []int) {
	return file_aistudio_object_v1_object_proto_rawDescGZIP(), []int{1}
}

func (x *BucketStatus) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

func (x *BucketStatus) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *BucketStatus) GetConditions() []*common.Condition {
	if x != nil {
		return x.Conditions
	}
	return nil
}

func (x *BucketStatus) GetAccessKey() string {
	if x != nil {
		return x.AccessKey
	}
	return ""
}

func (x *BucketStatus) GetSecretKey() string {
	if x != nil {
		return x.SecretKey
	}
	return ""
}

type CreateBucketRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name                string             `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	WorkspaceName       string             `protobuf:"bytes,2,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Owners              []string           `protobuf:"bytes,3,rep,name=owners,proto3" json:"owners,omitempty"`
	Members             []string           `protobuf:"bytes,4,rep,name=members,proto3" json:"members,omitempty"`
	MimeTransferEnabled bool               `protobuf:"varint,5,opt,name=mimeTransferEnabled,proto3" json:"mimeTransferEnabled,omitempty"`
	BackendType         BackendType        `protobuf:"varint,6,opt,name=backendType,proto3,enum=apis.aistudio.object.v1.BackendType" json:"backendType,omitempty"`
	Backend             *Backend           `protobuf:"bytes,7,opt,name=backend,proto3" json:"backend,omitempty"`
	CacheAcceleration   *CacheAcceleration `protobuf:"bytes,8,opt,name=cacheAcceleration,proto3" json:"cacheAcceleration,omitempty"` // 缓存加速配置
	Description         string             `protobuf:"bytes,9,opt,name=description,proto3" json:"description,omitempty"`
	DisplayName         string             `protobuf:"bytes,10,opt,name=displayName,proto3" json:"displayName,omitempty"`
}

func (x *CreateBucketRequest) Reset() {
	*x = CreateBucketRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_object_v1_object_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateBucketRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateBucketRequest) ProtoMessage() {}

func (x *CreateBucketRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_object_v1_object_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateBucketRequest.ProtoReflect.Descriptor instead.
func (*CreateBucketRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_object_v1_object_proto_rawDescGZIP(), []int{2}
}

func (x *CreateBucketRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateBucketRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *CreateBucketRequest) GetOwners() []string {
	if x != nil {
		return x.Owners
	}
	return nil
}

func (x *CreateBucketRequest) GetMembers() []string {
	if x != nil {
		return x.Members
	}
	return nil
}

func (x *CreateBucketRequest) GetMimeTransferEnabled() bool {
	if x != nil {
		return x.MimeTransferEnabled
	}
	return false
}

func (x *CreateBucketRequest) GetBackendType() BackendType {
	if x != nil {
		return x.BackendType
	}
	return BackendType_Direct
}

func (x *CreateBucketRequest) GetBackend() *Backend {
	if x != nil {
		return x.Backend
	}
	return nil
}

func (x *CreateBucketRequest) GetCacheAcceleration() *CacheAcceleration {
	if x != nil {
		return x.CacheAcceleration
	}
	return nil
}

func (x *CreateBucketRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CreateBucketRequest) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

type Backend struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Direct     *Backend_DirectBackend     `protobuf:"bytes,1,opt,name=direct,proto3" json:"direct,omitempty"`
	Degradable *Backend_DegradableBackend `protobuf:"bytes,2,opt,name=degradable,proto3" json:"degradable,omitempty"`
}

func (x *Backend) Reset() {
	*x = Backend{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_object_v1_object_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Backend) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Backend) ProtoMessage() {}

func (x *Backend) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_object_v1_object_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Backend.ProtoReflect.Descriptor instead.
func (*Backend) Descriptor() ([]byte, []int) {
	return file_aistudio_object_v1_object_proto_rawDescGZIP(), []int{3}
}

func (x *Backend) GetDirect() *Backend_DirectBackend {
	if x != nil {
		return x.Direct
	}
	return nil
}

func (x *Backend) GetDegradable() *Backend_DegradableBackend {
	if x != nil {
		return x.Degradable
	}
	return nil
}

type CacheAcceleration struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Enabled                bool                             `protobuf:"varint,1,opt,name=enabled,proto3" json:"enabled,omitempty"`
	Group                  string                           `protobuf:"bytes,2,opt,name=group,proto3" json:"group,omitempty"`
	DefaultMaxAgeSeconds   int64                            `protobuf:"varint,3,opt,name=defaultMaxAgeSeconds,proto3" json:"defaultMaxAgeSeconds,omitempty"`
	DefaultMinFreshSeconds int64                            `protobuf:"varint,4,opt,name=defaultMinFreshSeconds,proto3" json:"defaultMinFreshSeconds,omitempty"`
	OverridePolicies       []*CacheAcceleration_CachePolicy `protobuf:"bytes,5,rep,name=overridePolicies,proto3" json:"overridePolicies,omitempty"`
}

func (x *CacheAcceleration) Reset() {
	*x = CacheAcceleration{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_object_v1_object_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CacheAcceleration) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CacheAcceleration) ProtoMessage() {}

func (x *CacheAcceleration) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_object_v1_object_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CacheAcceleration.ProtoReflect.Descriptor instead.
func (*CacheAcceleration) Descriptor() ([]byte, []int) {
	return file_aistudio_object_v1_object_proto_rawDescGZIP(), []int{4}
}

func (x *CacheAcceleration) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

func (x *CacheAcceleration) GetGroup() string {
	if x != nil {
		return x.Group
	}
	return ""
}

func (x *CacheAcceleration) GetDefaultMaxAgeSeconds() int64 {
	if x != nil {
		return x.DefaultMaxAgeSeconds
	}
	return 0
}

func (x *CacheAcceleration) GetDefaultMinFreshSeconds() int64 {
	if x != nil {
		return x.DefaultMinFreshSeconds
	}
	return 0
}

func (x *CacheAcceleration) GetOverridePolicies() []*CacheAcceleration_CachePolicy {
	if x != nil {
		return x.OverridePolicies
	}
	return nil
}

type DeleteBucketRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name          string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	WorkspaceName string `protobuf:"bytes,2,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
}

func (x *DeleteBucketRequest) Reset() {
	*x = DeleteBucketRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_object_v1_object_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteBucketRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteBucketRequest) ProtoMessage() {}

func (x *DeleteBucketRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_object_v1_object_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteBucketRequest.ProtoReflect.Descriptor instead.
func (*DeleteBucketRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_object_v1_object_proto_rawDescGZIP(), []int{5}
}

func (x *DeleteBucketRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DeleteBucketRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

type ListBucketRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName string `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Owners        string `protobuf:"bytes,2,opt,name=owners,proto3" json:"owners,omitempty"`
}

func (x *ListBucketRequest) Reset() {
	*x = ListBucketRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_object_v1_object_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListBucketRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListBucketRequest) ProtoMessage() {}

func (x *ListBucketRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_object_v1_object_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListBucketRequest.ProtoReflect.Descriptor instead.
func (*ListBucketRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_object_v1_object_proto_rawDescGZIP(), []int{6}
}

func (x *ListBucketRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *ListBucketRequest) GetOwners() string {
	if x != nil {
		return x.Owners
	}
	return ""
}

type ListBucketResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Buckets []*Bucket `protobuf:"bytes,1,rep,name=buckets,proto3" json:"buckets,omitempty"`
}

func (x *ListBucketResult) Reset() {
	*x = ListBucketResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_object_v1_object_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListBucketResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListBucketResult) ProtoMessage() {}

func (x *ListBucketResult) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_object_v1_object_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListBucketResult.ProtoReflect.Descriptor instead.
func (*ListBucketResult) Descriptor() ([]byte, []int) {
	return file_aistudio_object_v1_object_proto_rawDescGZIP(), []int{7}
}

func (x *ListBucketResult) GetBuckets() []*Bucket {
	if x != nil {
		return x.Buckets
	}
	return nil
}

type Backend_DirectBackend struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ReplicationName string `protobuf:"bytes,1,opt,name=replicationName,proto3" json:"replicationName,omitempty"`
}

func (x *Backend_DirectBackend) Reset() {
	*x = Backend_DirectBackend{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_object_v1_object_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Backend_DirectBackend) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Backend_DirectBackend) ProtoMessage() {}

func (x *Backend_DirectBackend) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_object_v1_object_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Backend_DirectBackend.ProtoReflect.Descriptor instead.
func (*Backend_DirectBackend) Descriptor() ([]byte, []int) {
	return file_aistudio_object_v1_object_proto_rawDescGZIP(), []int{3, 0}
}

func (x *Backend_DirectBackend) GetReplicationName() string {
	if x != nil {
		return x.ReplicationName
	}
	return ""
}

type Backend_DegradableBackend struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ReplicationName           string `protobuf:"bytes,1,opt,name=replicationName,proto3" json:"replicationName,omitempty"`
	DegradableReplicationName string `protobuf:"bytes,2,opt,name=degradableReplicationName,proto3" json:"degradableReplicationName,omitempty"`
}

func (x *Backend_DegradableBackend) Reset() {
	*x = Backend_DegradableBackend{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_object_v1_object_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Backend_DegradableBackend) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Backend_DegradableBackend) ProtoMessage() {}

func (x *Backend_DegradableBackend) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_object_v1_object_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Backend_DegradableBackend.ProtoReflect.Descriptor instead.
func (*Backend_DegradableBackend) Descriptor() ([]byte, []int) {
	return file_aistudio_object_v1_object_proto_rawDescGZIP(), []int{3, 1}
}

func (x *Backend_DegradableBackend) GetReplicationName() string {
	if x != nil {
		return x.ReplicationName
	}
	return ""
}

func (x *Backend_DegradableBackend) GetDegradableReplicationName() string {
	if x != nil {
		return x.DegradableReplicationName
	}
	return ""
}

type CacheAcceleration_CachePolicy struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Enabled         bool                                     `protobuf:"varint,1,opt,name=enabled,proto3" json:"enabled,omitempty"`
	Condition       *CacheAcceleration_CachePolicy_Condition `protobuf:"bytes,2,opt,name=condition,proto3" json:"condition,omitempty"`
	MaxAgeSeconds   int64                                    `protobuf:"varint,3,opt,name=maxAgeSeconds,proto3" json:"maxAgeSeconds,omitempty"`
	MinFreshSeconds int64                                    `protobuf:"varint,4,opt,name=minFreshSeconds,proto3" json:"minFreshSeconds,omitempty"`
}

func (x *CacheAcceleration_CachePolicy) Reset() {
	*x = CacheAcceleration_CachePolicy{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_object_v1_object_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CacheAcceleration_CachePolicy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CacheAcceleration_CachePolicy) ProtoMessage() {}

func (x *CacheAcceleration_CachePolicy) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_object_v1_object_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CacheAcceleration_CachePolicy.ProtoReflect.Descriptor instead.
func (*CacheAcceleration_CachePolicy) Descriptor() ([]byte, []int) {
	return file_aistudio_object_v1_object_proto_rawDescGZIP(), []int{4, 0}
}

func (x *CacheAcceleration_CachePolicy) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

func (x *CacheAcceleration_CachePolicy) GetCondition() *CacheAcceleration_CachePolicy_Condition {
	if x != nil {
		return x.Condition
	}
	return nil
}

func (x *CacheAcceleration_CachePolicy) GetMaxAgeSeconds() int64 {
	if x != nil {
		return x.MaxAgeSeconds
	}
	return 0
}

func (x *CacheAcceleration_CachePolicy) GetMinFreshSeconds() int64 {
	if x != nil {
		return x.MinFreshSeconds
	}
	return 0
}

type CacheAcceleration_CachePolicy_Condition struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Operator CacheAcceleration_CachePolicy_Operator `protobuf:"varint,1,opt,name=operator,proto3,enum=apis.aistudio.object.v1.CacheAcceleration_CachePolicy_Operator" json:"operator,omitempty"`
	Expect   string                                 `protobuf:"bytes,2,opt,name=expect,proto3" json:"expect,omitempty"`
}

func (x *CacheAcceleration_CachePolicy_Condition) Reset() {
	*x = CacheAcceleration_CachePolicy_Condition{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_object_v1_object_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CacheAcceleration_CachePolicy_Condition) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CacheAcceleration_CachePolicy_Condition) ProtoMessage() {}

func (x *CacheAcceleration_CachePolicy_Condition) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_object_v1_object_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CacheAcceleration_CachePolicy_Condition.ProtoReflect.Descriptor instead.
func (*CacheAcceleration_CachePolicy_Condition) Descriptor() ([]byte, []int) {
	return file_aistudio_object_v1_object_proto_rawDescGZIP(), []int{4, 0, 0}
}

func (x *CacheAcceleration_CachePolicy_Condition) GetOperator() CacheAcceleration_CachePolicy_Operator {
	if x != nil {
		return x.Operator
	}
	return CacheAcceleration_CachePolicy_Exact
}

func (x *CacheAcceleration_CachePolicy_Condition) GetExpect() string {
	if x != nil {
		return x.Expect
	}
	return ""
}

var File_aistudio_object_v1_object_proto protoreflect.FileDescriptor

var file_aistudio_object_v1_object_proto_rawDesc = []byte{
	0x0a, 0x1f, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2f, 0x6f, 0x62, 0x6a, 0x65, 0x63,
	0x74, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x17, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f,
	0x2e, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x13, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0x93, 0x05, 0x0a, 0x06, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69,
	0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e,
	0x12, 0x16, 0x0a, 0x06, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x06, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x6f, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x6f, 0x72, 0x12, 0x30, 0x0a, 0x13, 0x6d, 0x69, 0x6d, 0x65, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66,
	0x65, 0x72, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x13, 0x6d, 0x69, 0x6d, 0x65, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x45, 0x6e, 0x61,
	0x62, 0x6c, 0x65, 0x64, 0x12, 0x46, 0x0a, 0x0b, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x54,
	0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x73,
	0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x0b, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x3a, 0x0a, 0x07,
	0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e,
	0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6f, 0x62,
	0x6a, 0x65, 0x63, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x52,
	0x07, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x12, 0x58, 0x0a, 0x11, 0x63, 0x61, 0x63, 0x68,
	0x65, 0x41, 0x63, 0x63, 0x65, 0x6c, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75,
	0x64, 0x69, 0x6f, 0x2e, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61,
	0x63, 0x68, 0x65, 0x41, 0x63, 0x63, 0x65, 0x6c, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x11, 0x63, 0x61, 0x63, 0x68, 0x65, 0x41, 0x63, 0x63, 0x65, 0x6c, 0x65, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e,
	0x61, 0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c,
	0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x63,
	0x63, 0x65, 0x73, 0x73, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x3d, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x61, 0x70, 0x69,
	0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6f, 0x62, 0x6a, 0x65, 0x63,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xb2, 0x01, 0x0a, 0x0c, 0x42, 0x75,
	0x63, 0x6b, 0x65, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74,
	0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65,
	0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x36, 0x0a, 0x0a, 0x63, 0x6f,
	0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16,
	0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x43, 0x6f, 0x6e,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0a, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x4b, 0x65, 0x79, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x4b, 0x65, 0x79,
	0x12, 0x1c, 0x0a, 0x09, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x4b, 0x65, 0x79, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x4b, 0x65, 0x79, 0x22, 0xd5,
	0x03, 0x0a, 0x13, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f,
	0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x16, 0x0a, 0x06, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x06, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x6d, 0x62,
	0x65, 0x72, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x6d, 0x62, 0x65,
	0x72, 0x73, 0x12, 0x30, 0x0a, 0x13, 0x6d, 0x69, 0x6d, 0x65, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66,
	0x65, 0x72, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x13, 0x6d, 0x69, 0x6d, 0x65, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x45, 0x6e, 0x61,
	0x62, 0x6c, 0x65, 0x64, 0x12, 0x46, 0x0a, 0x0b, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x54,
	0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x73,
	0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x0b, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x3a, 0x0a, 0x07,
	0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e,
	0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6f, 0x62,
	0x6a, 0x65, 0x63, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x52,
	0x07, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x12, 0x58, 0x0a, 0x11, 0x63, 0x61, 0x63, 0x68,
	0x65, 0x41, 0x63, 0x63, 0x65, 0x6c, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75,
	0x64, 0x69, 0x6f, 0x2e, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61,
	0x63, 0x68, 0x65, 0x41, 0x63, 0x63, 0x65, 0x6c, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x11, 0x63, 0x61, 0x63, 0x68, 0x65, 0x41, 0x63, 0x63, 0x65, 0x6c, 0x65, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e,
	0x61, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c,
	0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xdd, 0x02, 0x0a, 0x07, 0x42, 0x61, 0x63, 0x6b, 0x65,
	0x6e, 0x64, 0x12, 0x46, 0x0a, 0x06, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64,
	0x69, 0x6f, 0x2e, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x63,
	0x6b, 0x65, 0x6e, 0x64, 0x2e, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x42, 0x61, 0x63, 0x6b, 0x65,
	0x6e, 0x64, 0x52, 0x06, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x12, 0x52, 0x0a, 0x0a, 0x64, 0x65,
	0x67, 0x72, 0x61, 0x64, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32,
	0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6f,
	0x62, 0x6a, 0x65, 0x63, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64,
	0x2e, 0x44, 0x65, 0x67, 0x72, 0x61, 0x64, 0x61, 0x62, 0x6c, 0x65, 0x42, 0x61, 0x63, 0x6b, 0x65,
	0x6e, 0x64, 0x52, 0x0a, 0x64, 0x65, 0x67, 0x72, 0x61, 0x64, 0x61, 0x62, 0x6c, 0x65, 0x1a, 0x39,
	0x0a, 0x0d, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x42, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x12,
	0x28, 0x0a, 0x0f, 0x72, 0x65, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x72, 0x65, 0x70, 0x6c, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x1a, 0x7b, 0x0a, 0x11, 0x44, 0x65, 0x67,
	0x72, 0x61, 0x64, 0x61, 0x62, 0x6c, 0x65, 0x42, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x12, 0x28,
	0x0a, 0x0f, 0x72, 0x65, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x72, 0x65, 0x70, 0x6c, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x3c, 0x0a, 0x19, 0x64, 0x65, 0x67, 0x72,
	0x61, 0x64, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x19, 0x64, 0x65, 0x67,
	0x72, 0x61, 0x64, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xc5, 0x05, 0x0a, 0x11, 0x43, 0x61, 0x63, 0x68, 0x65,
	0x41, 0x63, 0x63, 0x65, 0x6c, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x18, 0x0a, 0x07,
	0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x65,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x32, 0x0a, 0x14,
	0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x4d, 0x61, 0x78, 0x41, 0x67, 0x65, 0x53, 0x65, 0x63,
	0x6f, 0x6e, 0x64, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x14, 0x64, 0x65, 0x66, 0x61,
	0x75, 0x6c, 0x74, 0x4d, 0x61, 0x78, 0x41, 0x67, 0x65, 0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73,
	0x12, 0x36, 0x0a, 0x16, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x4d, 0x69, 0x6e, 0x46, 0x72,
	0x65, 0x73, 0x68, 0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x16, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x4d, 0x69, 0x6e, 0x46, 0x72, 0x65, 0x73,
	0x68, 0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73, 0x12, 0x62, 0x0a, 0x10, 0x6f, 0x76, 0x65, 0x72,
	0x72, 0x69, 0x64, 0x65, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x69, 0x65, 0x73, 0x18, 0x05, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x36, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64,
	0x69, 0x6f, 0x2e, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x63,
	0x68, 0x65, 0x41, 0x63, 0x63, 0x65, 0x6c, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x43,
	0x61, 0x63, 0x68, 0x65, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52, 0x10, 0x6f, 0x76, 0x65, 0x72,
	0x72, 0x69, 0x64, 0x65, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x69, 0x65, 0x73, 0x1a, 0xaf, 0x03, 0x0a,
	0x0b, 0x43, 0x61, 0x63, 0x68, 0x65, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12, 0x18, 0x0a, 0x07,
	0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x65,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x5e, 0x0a, 0x09, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x40, 0x2e, 0x61, 0x70, 0x69, 0x73,
	0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x63, 0x68, 0x65, 0x41, 0x63, 0x63, 0x65, 0x6c, 0x65, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x43, 0x61, 0x63, 0x68, 0x65, 0x50, 0x6f, 0x6c, 0x69, 0x63,
	0x79, 0x2e, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x09, 0x63, 0x6f, 0x6e,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x24, 0x0a, 0x0d, 0x6d, 0x61, 0x78, 0x41, 0x67, 0x65,
	0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x6d,
	0x61, 0x78, 0x41, 0x67, 0x65, 0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73, 0x12, 0x28, 0x0a, 0x0f,
	0x6d, 0x69, 0x6e, 0x46, 0x72, 0x65, 0x73, 0x68, 0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x6d, 0x69, 0x6e, 0x46, 0x72, 0x65, 0x73, 0x68, 0x53,
	0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73, 0x1a, 0x80, 0x01, 0x0a, 0x09, 0x43, 0x6f, 0x6e, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x5b, 0x0a, 0x08, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3f, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69,
	0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x61, 0x63, 0x68, 0x65, 0x41, 0x63, 0x63, 0x65, 0x6c, 0x65, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x43, 0x61, 0x63, 0x68, 0x65, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x2e, 0x4f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x52, 0x08, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f,
	0x72, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x78, 0x70, 0x65, 0x63, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x65, 0x78, 0x70, 0x65, 0x63, 0x74, 0x22, 0x53, 0x0a, 0x08, 0x4f, 0x70, 0x65,
	0x72, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x09, 0x0a, 0x05, 0x45, 0x78, 0x61, 0x63, 0x74, 0x10, 0x00,
	0x12, 0x0a, 0x0a, 0x06, 0x52, 0x65, 0x67, 0x65, 0x78, 0x70, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08,
	0x43, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x73, 0x10, 0x02, 0x12, 0x0d, 0x0a, 0x09, 0x53, 0x74,
	0x61, 0x72, 0x74, 0x57, 0x69, 0x74, 0x68, 0x10, 0x03, 0x12, 0x0b, 0x0a, 0x07, 0x45, 0x6e, 0x64,
	0x57, 0x69, 0x74, 0x68, 0x10, 0x04, 0x12, 0x06, 0x0a, 0x02, 0x49, 0x6e, 0x10, 0x05, 0x22, 0x4f,
	0x0a, 0x13, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72,
	0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x22,
	0x51, 0x0a, 0x11, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72,
	0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x77,
	0x6e, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x77, 0x6e, 0x65,
	0x72, 0x73, 0x22, 0x4d, 0x0a, 0x10, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x39, 0x0a, 0x07, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61,
	0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x52, 0x07, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74,
	0x73, 0x2a, 0x29, 0x0a, 0x0b, 0x42, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x0a, 0x0a, 0x06, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x10, 0x00, 0x12, 0x0e, 0x0a, 0x0a,
	0x44, 0x65, 0x47, 0x72, 0x61, 0x64, 0x61, 0x62, 0x6c, 0x65, 0x10, 0x01, 0x32, 0xac, 0x04, 0x0a,
	0x14, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x53, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0xdd, 0x01, 0x0a, 0x14, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x4f, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x12, 0x2c,
	0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6f,
	0x62, 0x6a, 0x65, 0x63, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42,
	0x75, 0x63, 0x6b, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1f, 0x2e, 0x61,
	0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6f, 0x62, 0x6a,
	0x65, 0x63, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x22, 0x76, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x70, 0x3a, 0x01, 0x2a, 0x5a, 0x3c, 0x3a, 0x01, 0x2a, 0x1a, 0x37, 0x2f,
	0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x2f, 0x7b, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x7d, 0x2f, 0x73, 0x33, 0x2f, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x2f, 0x7b, 0x6e, 0x61,
	0x6d, 0x65, 0x3d, 0x2a, 0x2a, 0x7d, 0x22, 0x2d, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31,
	0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2f, 0x7b, 0x77, 0x6f, 0x72, 0x6b,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x73, 0x33, 0x2f, 0x62, 0x75,
	0x63, 0x6b, 0x65, 0x74, 0x73, 0x12, 0x95, 0x01, 0x0a, 0x0c, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x12, 0x2c, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69,
	0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x3f, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x39, 0x2a, 0x37, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x77,
	0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2f, 0x7b, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x73, 0x33, 0x2f, 0x62, 0x75, 0x63, 0x6b,
	0x65, 0x74, 0x73, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x2a, 0x2a, 0x7d, 0x12, 0x9b, 0x01,
	0x0a, 0x0b, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x12, 0x2a, 0x2e,
	0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6f, 0x62,
	0x6a, 0x65, 0x63, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x75, 0x63, 0x6b,
	0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e, 0x61, 0x70, 0x69, 0x73,
	0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x22, 0x35, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2f, 0x12, 0x2d, 0x2f, 0x61,
	0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x2f, 0x7b, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d,
	0x2f, 0x73, 0x33, 0x2f, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x42, 0x49, 0x5a, 0x47, 0x67,
	0x69, 0x74, 0x2e, 0x6c, 0x69, 0x61, 0x6e, 0x6a, 0x69, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x63,
	0x6c, 0x6f, 0x75, 0x64, 0x6e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x2f, 0x6b, 0x69, 0x63, 0x2f, 0x6b,
	0x69, 0x63, 0x2d, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x61, 0x70, 0x69, 0x73,
	0x2f, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2f, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74,
	0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_aistudio_object_v1_object_proto_rawDescOnce sync.Once
	file_aistudio_object_v1_object_proto_rawDescData = file_aistudio_object_v1_object_proto_rawDesc
)

func file_aistudio_object_v1_object_proto_rawDescGZIP() []byte {
	file_aistudio_object_v1_object_proto_rawDescOnce.Do(func() {
		file_aistudio_object_v1_object_proto_rawDescData = protoimpl.X.CompressGZIP(file_aistudio_object_v1_object_proto_rawDescData)
	})
	return file_aistudio_object_v1_object_proto_rawDescData
}

var file_aistudio_object_v1_object_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_aistudio_object_v1_object_proto_msgTypes = make([]protoimpl.MessageInfo, 12)
var file_aistudio_object_v1_object_proto_goTypes = []any{
	(BackendType)(0), // 0: apis.aistudio.object.v1.BackendType
	(CacheAcceleration_CachePolicy_Operator)(0), // 1: apis.aistudio.object.v1.CacheAcceleration.CachePolicy.Operator
	(*Bucket)(nil),                                  // 2: apis.aistudio.object.v1.Bucket
	(*BucketStatus)(nil),                            // 3: apis.aistudio.object.v1.BucketStatus
	(*CreateBucketRequest)(nil),                     // 4: apis.aistudio.object.v1.CreateBucketRequest
	(*Backend)(nil),                                 // 5: apis.aistudio.object.v1.Backend
	(*CacheAcceleration)(nil),                       // 6: apis.aistudio.object.v1.CacheAcceleration
	(*DeleteBucketRequest)(nil),                     // 7: apis.aistudio.object.v1.DeleteBucketRequest
	(*ListBucketRequest)(nil),                       // 8: apis.aistudio.object.v1.ListBucketRequest
	(*ListBucketResult)(nil),                        // 9: apis.aistudio.object.v1.ListBucketResult
	(*Backend_DirectBackend)(nil),                   // 10: apis.aistudio.object.v1.Backend.DirectBackend
	(*Backend_DegradableBackend)(nil),               // 11: apis.aistudio.object.v1.Backend.DegradableBackend
	(*CacheAcceleration_CachePolicy)(nil),           // 12: apis.aistudio.object.v1.CacheAcceleration.CachePolicy
	(*CacheAcceleration_CachePolicy_Condition)(nil), // 13: apis.aistudio.object.v1.CacheAcceleration.CachePolicy.Condition
	(*common.Condition)(nil),                        // 14: apis.common.Condition
	(*emptypb.Empty)(nil),                           // 15: google.protobuf.Empty
}
var file_aistudio_object_v1_object_proto_depIdxs = []int32{
	0,  // 0: apis.aistudio.object.v1.Bucket.backendType:type_name -> apis.aistudio.object.v1.BackendType
	5,  // 1: apis.aistudio.object.v1.Bucket.backend:type_name -> apis.aistudio.object.v1.Backend
	6,  // 2: apis.aistudio.object.v1.Bucket.cacheAcceleration:type_name -> apis.aistudio.object.v1.CacheAcceleration
	3,  // 3: apis.aistudio.object.v1.Bucket.status:type_name -> apis.aistudio.object.v1.BucketStatus
	14, // 4: apis.aistudio.object.v1.BucketStatus.conditions:type_name -> apis.common.Condition
	0,  // 5: apis.aistudio.object.v1.CreateBucketRequest.backendType:type_name -> apis.aistudio.object.v1.BackendType
	5,  // 6: apis.aistudio.object.v1.CreateBucketRequest.backend:type_name -> apis.aistudio.object.v1.Backend
	6,  // 7: apis.aistudio.object.v1.CreateBucketRequest.cacheAcceleration:type_name -> apis.aistudio.object.v1.CacheAcceleration
	10, // 8: apis.aistudio.object.v1.Backend.direct:type_name -> apis.aistudio.object.v1.Backend.DirectBackend
	11, // 9: apis.aistudio.object.v1.Backend.degradable:type_name -> apis.aistudio.object.v1.Backend.DegradableBackend
	12, // 10: apis.aistudio.object.v1.CacheAcceleration.overridePolicies:type_name -> apis.aistudio.object.v1.CacheAcceleration.CachePolicy
	2,  // 11: apis.aistudio.object.v1.ListBucketResult.buckets:type_name -> apis.aistudio.object.v1.Bucket
	13, // 12: apis.aistudio.object.v1.CacheAcceleration.CachePolicy.condition:type_name -> apis.aistudio.object.v1.CacheAcceleration.CachePolicy.Condition
	1,  // 13: apis.aistudio.object.v1.CacheAcceleration.CachePolicy.Condition.operator:type_name -> apis.aistudio.object.v1.CacheAcceleration.CachePolicy.Operator
	4,  // 14: apis.aistudio.object.v1.ObjectStorageService.CreateOrUpdateBucket:input_type -> apis.aistudio.object.v1.CreateBucketRequest
	7,  // 15: apis.aistudio.object.v1.ObjectStorageService.DeleteBucket:input_type -> apis.aistudio.object.v1.DeleteBucketRequest
	8,  // 16: apis.aistudio.object.v1.ObjectStorageService.ListBuckets:input_type -> apis.aistudio.object.v1.ListBucketRequest
	2,  // 17: apis.aistudio.object.v1.ObjectStorageService.CreateOrUpdateBucket:output_type -> apis.aistudio.object.v1.Bucket
	15, // 18: apis.aistudio.object.v1.ObjectStorageService.DeleteBucket:output_type -> google.protobuf.Empty
	9,  // 19: apis.aistudio.object.v1.ObjectStorageService.ListBuckets:output_type -> apis.aistudio.object.v1.ListBucketResult
	17, // [17:20] is the sub-list for method output_type
	14, // [14:17] is the sub-list for method input_type
	14, // [14:14] is the sub-list for extension type_name
	14, // [14:14] is the sub-list for extension extendee
	0,  // [0:14] is the sub-list for field type_name
}

func init() { file_aistudio_object_v1_object_proto_init() }
func file_aistudio_object_v1_object_proto_init() {
	if File_aistudio_object_v1_object_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_aistudio_object_v1_object_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*Bucket); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_object_v1_object_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*BucketStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_object_v1_object_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*CreateBucketRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_object_v1_object_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*Backend); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_object_v1_object_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*CacheAcceleration); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_object_v1_object_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*DeleteBucketRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_object_v1_object_proto_msgTypes[6].Exporter = func(v any, i int) any {
			switch v := v.(*ListBucketRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_object_v1_object_proto_msgTypes[7].Exporter = func(v any, i int) any {
			switch v := v.(*ListBucketResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_object_v1_object_proto_msgTypes[8].Exporter = func(v any, i int) any {
			switch v := v.(*Backend_DirectBackend); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_object_v1_object_proto_msgTypes[9].Exporter = func(v any, i int) any {
			switch v := v.(*Backend_DegradableBackend); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_object_v1_object_proto_msgTypes[10].Exporter = func(v any, i int) any {
			switch v := v.(*CacheAcceleration_CachePolicy); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_object_v1_object_proto_msgTypes[11].Exporter = func(v any, i int) any {
			switch v := v.(*CacheAcceleration_CachePolicy_Condition); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_aistudio_object_v1_object_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   12,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_aistudio_object_v1_object_proto_goTypes,
		DependencyIndexes: file_aistudio_object_v1_object_proto_depIdxs,
		EnumInfos:         file_aistudio_object_v1_object_proto_enumTypes,
		MessageInfos:      file_aistudio_object_v1_object_proto_msgTypes,
	}.Build()
	File_aistudio_object_v1_object_proto = out.File
	file_aistudio_object_v1_object_proto_rawDesc = nil
	file_aistudio_object_v1_object_proto_goTypes = nil
	file_aistudio_object_v1_object_proto_depIdxs = nil
}
