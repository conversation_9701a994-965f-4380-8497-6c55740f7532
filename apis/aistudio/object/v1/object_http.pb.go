// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.3
// - protoc             v5.29.3
// source: aistudio/object/v1/object.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationObjectStorageServiceCreateOrUpdateBucket = "/apis.aistudio.object.v1.ObjectStorageService/CreateOrUpdateBucket"
const OperationObjectStorageServiceDeleteBucket = "/apis.aistudio.object.v1.ObjectStorageService/DeleteBucket"
const OperationObjectStorageServiceListBuckets = "/apis.aistudio.object.v1.ObjectStorageService/ListBuckets"

type ObjectStorageServiceHTTPServer interface {
	CreateOrUpdateBucket(context.Context, *CreateBucketRequest) (*Bucket, error)
	DeleteBucket(context.Context, *DeleteBucketRequest) (*emptypb.Empty, error)
	ListBuckets(context.Context, *ListBucketRequest) (*ListBucketResult, error)
}

func RegisterObjectStorageServiceHTTPServer(s *http.Server, srv ObjectStorageServiceHTTPServer) {
	r := s.Route("/")
	r.PUT("/apis/v1/workspace/{workspaceName}/s3/buckets/{name:.*.*}", _ObjectStorageService_CreateOrUpdateBucket0_HTTP_Handler(srv))
	r.POST("/apis/v1/workspace/{workspaceName}/s3/buckets", _ObjectStorageService_CreateOrUpdateBucket1_HTTP_Handler(srv))
	r.DELETE("/apis/v1/workspace/{workspaceName}/s3/buckets/{name:.*.*}", _ObjectStorageService_DeleteBucket0_HTTP_Handler(srv))
	r.GET("/apis/v1/workspace/{workspaceName}/s3/buckets", _ObjectStorageService_ListBuckets0_HTTP_Handler(srv))
}

func _ObjectStorageService_CreateOrUpdateBucket0_HTTP_Handler(srv ObjectStorageServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateBucketRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationObjectStorageServiceCreateOrUpdateBucket)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateOrUpdateBucket(ctx, req.(*CreateBucketRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Bucket)
		return ctx.Result(200, reply)
	}
}

func _ObjectStorageService_CreateOrUpdateBucket1_HTTP_Handler(srv ObjectStorageServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateBucketRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationObjectStorageServiceCreateOrUpdateBucket)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateOrUpdateBucket(ctx, req.(*CreateBucketRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Bucket)
		return ctx.Result(200, reply)
	}
}

func _ObjectStorageService_DeleteBucket0_HTTP_Handler(srv ObjectStorageServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteBucketRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationObjectStorageServiceDeleteBucket)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteBucket(ctx, req.(*DeleteBucketRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _ObjectStorageService_ListBuckets0_HTTP_Handler(srv ObjectStorageServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListBucketRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationObjectStorageServiceListBuckets)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListBuckets(ctx, req.(*ListBucketRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListBucketResult)
		return ctx.Result(200, reply)
	}
}

type ObjectStorageServiceHTTPClient interface {
	CreateOrUpdateBucket(ctx context.Context, req *CreateBucketRequest, opts ...http.CallOption) (rsp *Bucket, err error)
	DeleteBucket(ctx context.Context, req *DeleteBucketRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	ListBuckets(ctx context.Context, req *ListBucketRequest, opts ...http.CallOption) (rsp *ListBucketResult, err error)
}

type ObjectStorageServiceHTTPClientImpl struct {
	cc *http.Client
}

func NewObjectStorageServiceHTTPClient(client *http.Client) ObjectStorageServiceHTTPClient {
	return &ObjectStorageServiceHTTPClientImpl{client}
}

func (c *ObjectStorageServiceHTTPClientImpl) CreateOrUpdateBucket(ctx context.Context, in *CreateBucketRequest, opts ...http.CallOption) (*Bucket, error) {
	var out Bucket
	pattern := "/apis/v1/workspace/{workspaceName}/s3/buckets"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationObjectStorageServiceCreateOrUpdateBucket))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ObjectStorageServiceHTTPClientImpl) DeleteBucket(ctx context.Context, in *DeleteBucketRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/apis/v1/workspace/{workspaceName}/s3/buckets/{name:.*.*}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationObjectStorageServiceDeleteBucket))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ObjectStorageServiceHTTPClientImpl) ListBuckets(ctx context.Context, in *ListBucketRequest, opts ...http.CallOption) (*ListBucketResult, error) {
	var out ListBucketResult
	pattern := "/apis/v1/workspace/{workspaceName}/s3/buckets"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationObjectStorageServiceListBuckets))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
