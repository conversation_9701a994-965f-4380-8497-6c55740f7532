// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v5.29.3
// source: aistudio/cubefs/v1/cubefs.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	CubeFSService_CreateVolume_FullMethodName               = "/apis.aistudio.cubefs.v1.CubeFSService/CreateVolume"
	CubeFSService_UpdateVolume_FullMethodName               = "/apis.aistudio.cubefs.v1.CubeFSService/UpdateVolume"
	CubeFSService_GetVolume_FullMethodName                  = "/apis.aistudio.cubefs.v1.CubeFSService/GetVolume"
	CubeFSService_ListVolumes_FullMethodName                = "/apis.aistudio.cubefs.v1.CubeFSService/ListVolumes"
	CubeFSService_DeleteVolume_FullMethodName               = "/apis.aistudio.cubefs.v1.CubeFSService/DeleteVolume"
	CubeFSService_ListCubeFSClusters_FullMethodName         = "/apis.aistudio.cubefs.v1.CubeFSService/ListCubeFSClusters"
	CubeFSService_ListCubeFSVolumesByCluster_FullMethodName = "/apis.aistudio.cubefs.v1.CubeFSService/ListCubeFSVolumesByCluster"
)

// CubeFSServiceClient is the client API for CubeFSService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type CubeFSServiceClient interface {
	CreateVolume(ctx context.Context, in *CreateVolumeRequest, opts ...grpc.CallOption) (*Volume, error)
	UpdateVolume(ctx context.Context, in *UpdateVolumeRequest, opts ...grpc.CallOption) (*Volume, error)
	GetVolume(ctx context.Context, in *GetVolumeRequest, opts ...grpc.CallOption) (*Volume, error)
	ListVolumes(ctx context.Context, in *ListVolumeOptions, opts ...grpc.CallOption) (*ListVolumeResult, error)
	DeleteVolume(ctx context.Context, in *DeleteVolumeRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	ListCubeFSClusters(ctx context.Context, in *ListCubeFSClustersOptions, opts ...grpc.CallOption) (*ListCubeFSClustersResult, error)
	ListCubeFSVolumesByCluster(ctx context.Context, in *ListCubeFSVolumesByClusterOptions, opts ...grpc.CallOption) (*ListCubeFSVolumesResult, error)
}

type cubeFSServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewCubeFSServiceClient(cc grpc.ClientConnInterface) CubeFSServiceClient {
	return &cubeFSServiceClient{cc}
}

func (c *cubeFSServiceClient) CreateVolume(ctx context.Context, in *CreateVolumeRequest, opts ...grpc.CallOption) (*Volume, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(Volume)
	err := c.cc.Invoke(ctx, CubeFSService_CreateVolume_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cubeFSServiceClient) UpdateVolume(ctx context.Context, in *UpdateVolumeRequest, opts ...grpc.CallOption) (*Volume, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(Volume)
	err := c.cc.Invoke(ctx, CubeFSService_UpdateVolume_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cubeFSServiceClient) GetVolume(ctx context.Context, in *GetVolumeRequest, opts ...grpc.CallOption) (*Volume, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(Volume)
	err := c.cc.Invoke(ctx, CubeFSService_GetVolume_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cubeFSServiceClient) ListVolumes(ctx context.Context, in *ListVolumeOptions, opts ...grpc.CallOption) (*ListVolumeResult, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListVolumeResult)
	err := c.cc.Invoke(ctx, CubeFSService_ListVolumes_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cubeFSServiceClient) DeleteVolume(ctx context.Context, in *DeleteVolumeRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, CubeFSService_DeleteVolume_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cubeFSServiceClient) ListCubeFSClusters(ctx context.Context, in *ListCubeFSClustersOptions, opts ...grpc.CallOption) (*ListCubeFSClustersResult, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListCubeFSClustersResult)
	err := c.cc.Invoke(ctx, CubeFSService_ListCubeFSClusters_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cubeFSServiceClient) ListCubeFSVolumesByCluster(ctx context.Context, in *ListCubeFSVolumesByClusterOptions, opts ...grpc.CallOption) (*ListCubeFSVolumesResult, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListCubeFSVolumesResult)
	err := c.cc.Invoke(ctx, CubeFSService_ListCubeFSVolumesByCluster_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CubeFSServiceServer is the server API for CubeFSService service.
// All implementations must embed UnimplementedCubeFSServiceServer
// for forward compatibility
type CubeFSServiceServer interface {
	CreateVolume(context.Context, *CreateVolumeRequest) (*Volume, error)
	UpdateVolume(context.Context, *UpdateVolumeRequest) (*Volume, error)
	GetVolume(context.Context, *GetVolumeRequest) (*Volume, error)
	ListVolumes(context.Context, *ListVolumeOptions) (*ListVolumeResult, error)
	DeleteVolume(context.Context, *DeleteVolumeRequest) (*emptypb.Empty, error)
	ListCubeFSClusters(context.Context, *ListCubeFSClustersOptions) (*ListCubeFSClustersResult, error)
	ListCubeFSVolumesByCluster(context.Context, *ListCubeFSVolumesByClusterOptions) (*ListCubeFSVolumesResult, error)
	mustEmbedUnimplementedCubeFSServiceServer()
}

// UnimplementedCubeFSServiceServer must be embedded to have forward compatible implementations.
type UnimplementedCubeFSServiceServer struct {
}

func (UnimplementedCubeFSServiceServer) CreateVolume(context.Context, *CreateVolumeRequest) (*Volume, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateVolume not implemented")
}
func (UnimplementedCubeFSServiceServer) UpdateVolume(context.Context, *UpdateVolumeRequest) (*Volume, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateVolume not implemented")
}
func (UnimplementedCubeFSServiceServer) GetVolume(context.Context, *GetVolumeRequest) (*Volume, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetVolume not implemented")
}
func (UnimplementedCubeFSServiceServer) ListVolumes(context.Context, *ListVolumeOptions) (*ListVolumeResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListVolumes not implemented")
}
func (UnimplementedCubeFSServiceServer) DeleteVolume(context.Context, *DeleteVolumeRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteVolume not implemented")
}
func (UnimplementedCubeFSServiceServer) ListCubeFSClusters(context.Context, *ListCubeFSClustersOptions) (*ListCubeFSClustersResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListCubeFSClusters not implemented")
}
func (UnimplementedCubeFSServiceServer) ListCubeFSVolumesByCluster(context.Context, *ListCubeFSVolumesByClusterOptions) (*ListCubeFSVolumesResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListCubeFSVolumesByCluster not implemented")
}
func (UnimplementedCubeFSServiceServer) mustEmbedUnimplementedCubeFSServiceServer() {}

// UnsafeCubeFSServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CubeFSServiceServer will
// result in compilation errors.
type UnsafeCubeFSServiceServer interface {
	mustEmbedUnimplementedCubeFSServiceServer()
}

func RegisterCubeFSServiceServer(s grpc.ServiceRegistrar, srv CubeFSServiceServer) {
	s.RegisterService(&CubeFSService_ServiceDesc, srv)
}

func _CubeFSService_CreateVolume_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateVolumeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CubeFSServiceServer).CreateVolume(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CubeFSService_CreateVolume_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CubeFSServiceServer).CreateVolume(ctx, req.(*CreateVolumeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CubeFSService_UpdateVolume_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateVolumeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CubeFSServiceServer).UpdateVolume(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CubeFSService_UpdateVolume_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CubeFSServiceServer).UpdateVolume(ctx, req.(*UpdateVolumeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CubeFSService_GetVolume_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetVolumeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CubeFSServiceServer).GetVolume(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CubeFSService_GetVolume_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CubeFSServiceServer).GetVolume(ctx, req.(*GetVolumeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CubeFSService_ListVolumes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListVolumeOptions)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CubeFSServiceServer).ListVolumes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CubeFSService_ListVolumes_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CubeFSServiceServer).ListVolumes(ctx, req.(*ListVolumeOptions))
	}
	return interceptor(ctx, in, info, handler)
}

func _CubeFSService_DeleteVolume_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteVolumeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CubeFSServiceServer).DeleteVolume(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CubeFSService_DeleteVolume_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CubeFSServiceServer).DeleteVolume(ctx, req.(*DeleteVolumeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CubeFSService_ListCubeFSClusters_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListCubeFSClustersOptions)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CubeFSServiceServer).ListCubeFSClusters(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CubeFSService_ListCubeFSClusters_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CubeFSServiceServer).ListCubeFSClusters(ctx, req.(*ListCubeFSClustersOptions))
	}
	return interceptor(ctx, in, info, handler)
}

func _CubeFSService_ListCubeFSVolumesByCluster_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListCubeFSVolumesByClusterOptions)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CubeFSServiceServer).ListCubeFSVolumesByCluster(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CubeFSService_ListCubeFSVolumesByCluster_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CubeFSServiceServer).ListCubeFSVolumesByCluster(ctx, req.(*ListCubeFSVolumesByClusterOptions))
	}
	return interceptor(ctx, in, info, handler)
}

// CubeFSService_ServiceDesc is the grpc.ServiceDesc for CubeFSService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var CubeFSService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "apis.aistudio.cubefs.v1.CubeFSService",
	HandlerType: (*CubeFSServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateVolume",
			Handler:    _CubeFSService_CreateVolume_Handler,
		},
		{
			MethodName: "UpdateVolume",
			Handler:    _CubeFSService_UpdateVolume_Handler,
		},
		{
			MethodName: "GetVolume",
			Handler:    _CubeFSService_GetVolume_Handler,
		},
		{
			MethodName: "ListVolumes",
			Handler:    _CubeFSService_ListVolumes_Handler,
		},
		{
			MethodName: "DeleteVolume",
			Handler:    _CubeFSService_DeleteVolume_Handler,
		},
		{
			MethodName: "ListCubeFSClusters",
			Handler:    _CubeFSService_ListCubeFSClusters_Handler,
		},
		{
			MethodName: "ListCubeFSVolumesByCluster",
			Handler:    _CubeFSService_ListCubeFSVolumesByCluster_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "aistudio/cubefs/v1/cubefs.proto",
}
