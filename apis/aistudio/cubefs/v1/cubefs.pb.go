// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v5.29.3
// source: aistudio/cubefs/v1/cubefs.proto

package v1

import (
	common "git.lianjia.com/cloudnative/kic/kic-platform/apis/common"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	_ "google.golang.org/protobuf/types/known/durationpb"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type VolumeSpecification int32

const (
	VolumeSpecification_Powerful VolumeSpecification = 0
	VolumeSpecification_Normal   VolumeSpecification = 1
	VolumeSpecification_Capacity VolumeSpecification = 2
)

// Enum value maps for VolumeSpecification.
var (
	VolumeSpecification_name = map[int32]string{
		0: "Powerful",
		1: "Normal",
		2: "Capacity",
	}
	VolumeSpecification_value = map[string]int32{
		"Powerful": 0,
		"Normal":   1,
		"Capacity": 2,
	}
)

func (x VolumeSpecification) Enum() *VolumeSpecification {
	p := new(VolumeSpecification)
	*p = x
	return p
}

func (x VolumeSpecification) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (VolumeSpecification) Descriptor() protoreflect.EnumDescriptor {
	return file_aistudio_cubefs_v1_cubefs_proto_enumTypes[0].Descriptor()
}

func (VolumeSpecification) Type() protoreflect.EnumType {
	return &file_aistudio_cubefs_v1_cubefs_proto_enumTypes[0]
}

func (x VolumeSpecification) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use VolumeSpecification.Descriptor instead.
func (VolumeSpecification) EnumDescriptor() ([]byte, []int) {
	return file_aistudio_cubefs_v1_cubefs_proto_rawDescGZIP(), []int{0}
}

type Volume struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName string                 `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Description   string                 `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	Region        string                 `protobuf:"bytes,4,opt,name=region,proto3" json:"region,omitempty"`           // 区域
	Zone          string                 `protobuf:"bytes,5,opt,name=zone,proto3" json:"zone,omitempty"`               //分区
	DisplayName   string                 `protobuf:"bytes,6,opt,name=displayName,proto3" json:"displayName,omitempty"` // 显示名称
	Capacity      uint64                 `protobuf:"varint,7,opt,name=capacity,proto3" json:"capacity,omitempty"`      // 容量  GiB
	Timestamp     *common.TimestampModel `protobuf:"bytes,8,opt,name=timestamp,proto3" json:"timestamp,omitempty"`     //时间
	Id            string                 `protobuf:"bytes,9,opt,name=id,proto3" json:"id,omitempty"`
	VolumeStatus  *VolumeStatus          `protobuf:"bytes,10,opt,name=volumeStatus,proto3" json:"volumeStatus,omitempty"`
	Creator       string                 `protobuf:"bytes,11,opt,name=creator,proto3" json:"creator,omitempty"`
	Specification VolumeSpecification    `protobuf:"varint,12,opt,name=specification,proto3,enum=apis.aistudio.cubefs.v1.VolumeSpecification" json:"specification,omitempty"` //规格
	Binding       *VolumeBinding         `protobuf:"bytes,13,opt,name=binding,proto3" json:"binding,omitempty"`
	Cluster       string                 `protobuf:"bytes,14,opt,name=cluster,proto3" json:"cluster,omitempty"`
	Managers      []string               `protobuf:"bytes,15,rep,name=managers,proto3" json:"managers,omitempty"`
	Members       []string               `protobuf:"bytes,16,rep,name=members,proto3" json:"members,omitempty"`
}

func (x *Volume) Reset() {
	*x = Volume{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_cubefs_v1_cubefs_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Volume) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Volume) ProtoMessage() {}

func (x *Volume) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_cubefs_v1_cubefs_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Volume.ProtoReflect.Descriptor instead.
func (*Volume) Descriptor() ([]byte, []int) {
	return file_aistudio_cubefs_v1_cubefs_proto_rawDescGZIP(), []int{0}
}

func (x *Volume) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *Volume) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Volume) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Volume) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *Volume) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *Volume) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *Volume) GetCapacity() uint64 {
	if x != nil {
		return x.Capacity
	}
	return 0
}

func (x *Volume) GetTimestamp() *common.TimestampModel {
	if x != nil {
		return x.Timestamp
	}
	return nil
}

func (x *Volume) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Volume) GetVolumeStatus() *VolumeStatus {
	if x != nil {
		return x.VolumeStatus
	}
	return nil
}

func (x *Volume) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *Volume) GetSpecification() VolumeSpecification {
	if x != nil {
		return x.Specification
	}
	return VolumeSpecification_Powerful
}

func (x *Volume) GetBinding() *VolumeBinding {
	if x != nil {
		return x.Binding
	}
	return nil
}

func (x *Volume) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

func (x *Volume) GetManagers() []string {
	if x != nil {
		return x.Managers
	}
	return nil
}

func (x *Volume) GetMembers() []string {
	if x != nil {
		return x.Members
	}
	return nil
}

type VolumeStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	State              string `protobuf:"bytes,1,opt,name=state,proto3" json:"state,omitempty"`
	Message            string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Reason             string `protobuf:"bytes,3,opt,name=reason,proto3" json:"reason,omitempty"`
	LastTransitionTime string `protobuf:"bytes,4,opt,name=lastTransitionTime,proto3" json:"lastTransitionTime,omitempty"`
	Cluster            string `protobuf:"bytes,5,opt,name=cluster,proto3" json:"cluster,omitempty"`           //归属的集群
	UsagePercent       string `protobuf:"bytes,6,opt,name=usagePercent,proto3" json:"usagePercent,omitempty"` //使用率d
	VolumeId           string `protobuf:"bytes,7,opt,name=volumeId,proto3" json:"volumeId,omitempty"`         //卷id
	InodeCount         uint64 `protobuf:"varint,8,opt,name=inodeCount,proto3" json:"inodeCount,omitempty"`    //inode数量
}

func (x *VolumeStatus) Reset() {
	*x = VolumeStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_cubefs_v1_cubefs_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VolumeStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VolumeStatus) ProtoMessage() {}

func (x *VolumeStatus) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_cubefs_v1_cubefs_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VolumeStatus.ProtoReflect.Descriptor instead.
func (*VolumeStatus) Descriptor() ([]byte, []int) {
	return file_aistudio_cubefs_v1_cubefs_proto_rawDescGZIP(), []int{1}
}

func (x *VolumeStatus) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

func (x *VolumeStatus) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *VolumeStatus) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *VolumeStatus) GetLastTransitionTime() string {
	if x != nil {
		return x.LastTransitionTime
	}
	return ""
}

func (x *VolumeStatus) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

func (x *VolumeStatus) GetUsagePercent() string {
	if x != nil {
		return x.UsagePercent
	}
	return ""
}

func (x *VolumeStatus) GetVolumeId() string {
	if x != nil {
		return x.VolumeId
	}
	return ""
}

func (x *VolumeStatus) GetInodeCount() uint64 {
	if x != nil {
		return x.InodeCount
	}
	return 0
}

type GetBareMetalAccessConfigurationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName string `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	VolumeName    string `protobuf:"bytes,2,opt,name=volumeName,proto3" json:"volumeName,omitempty"`
}

func (x *GetBareMetalAccessConfigurationRequest) Reset() {
	*x = GetBareMetalAccessConfigurationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_cubefs_v1_cubefs_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBareMetalAccessConfigurationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBareMetalAccessConfigurationRequest) ProtoMessage() {}

func (x *GetBareMetalAccessConfigurationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_cubefs_v1_cubefs_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBareMetalAccessConfigurationRequest.ProtoReflect.Descriptor instead.
func (*GetBareMetalAccessConfigurationRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_cubefs_v1_cubefs_proto_rawDescGZIP(), []int{2}
}

func (x *GetBareMetalAccessConfigurationRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *GetBareMetalAccessConfigurationRequest) GetVolumeName() string {
	if x != nil {
		return x.VolumeName
	}
	return ""
}

type GetBareMetalAccessConfigurationResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName string `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	VolName       string `protobuf:"bytes,2,opt,name=volName,proto3" json:"volName,omitempty"`
	AccessKey     string `protobuf:"bytes,3,opt,name=accessKey,proto3" json:"accessKey,omitempty"`
	SecretKey     string `protobuf:"bytes,4,opt,name=secretKey,proto3" json:"secretKey,omitempty"`
	MountPoint    string `protobuf:"bytes,5,opt,name=mountPoint,proto3" json:"mountPoint,omitempty"`  //挂载点
	Owner         string `protobuf:"bytes,6,opt,name=owner,proto3" json:"owner,omitempty"`            //卷的拥有者
	ProfPort      int32  `protobuf:"varint,7,opt,name=profPort,proto3" json:"profPort,omitempty"`     //prof端口
	LogDir        string `protobuf:"bytes,8,opt,name=logDir,proto3" json:"logDir,omitempty"`          //日志目录
	LogLevel      string `protobuf:"bytes,9,opt,name=logLevel,proto3" json:"logLevel,omitempty"`      //日志级别
	MasterAddr    string `protobuf:"bytes,10,opt,name=masterAddr,proto3" json:"masterAddr,omitempty"` //master地址
}

func (x *GetBareMetalAccessConfigurationResponse) Reset() {
	*x = GetBareMetalAccessConfigurationResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_cubefs_v1_cubefs_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBareMetalAccessConfigurationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBareMetalAccessConfigurationResponse) ProtoMessage() {}

func (x *GetBareMetalAccessConfigurationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_cubefs_v1_cubefs_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBareMetalAccessConfigurationResponse.ProtoReflect.Descriptor instead.
func (*GetBareMetalAccessConfigurationResponse) Descriptor() ([]byte, []int) {
	return file_aistudio_cubefs_v1_cubefs_proto_rawDescGZIP(), []int{3}
}

func (x *GetBareMetalAccessConfigurationResponse) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *GetBareMetalAccessConfigurationResponse) GetVolName() string {
	if x != nil {
		return x.VolName
	}
	return ""
}

func (x *GetBareMetalAccessConfigurationResponse) GetAccessKey() string {
	if x != nil {
		return x.AccessKey
	}
	return ""
}

func (x *GetBareMetalAccessConfigurationResponse) GetSecretKey() string {
	if x != nil {
		return x.SecretKey
	}
	return ""
}

func (x *GetBareMetalAccessConfigurationResponse) GetMountPoint() string {
	if x != nil {
		return x.MountPoint
	}
	return ""
}

func (x *GetBareMetalAccessConfigurationResponse) GetOwner() string {
	if x != nil {
		return x.Owner
	}
	return ""
}

func (x *GetBareMetalAccessConfigurationResponse) GetProfPort() int32 {
	if x != nil {
		return x.ProfPort
	}
	return 0
}

func (x *GetBareMetalAccessConfigurationResponse) GetLogDir() string {
	if x != nil {
		return x.LogDir
	}
	return ""
}

func (x *GetBareMetalAccessConfigurationResponse) GetLogLevel() string {
	if x != nil {
		return x.LogLevel
	}
	return ""
}

func (x *GetBareMetalAccessConfigurationResponse) GetMasterAddr() string {
	if x != nil {
		return x.MasterAddr
	}
	return ""
}

type CreateVolumeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName string              `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Name          string              `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Description   string              `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	Zone          string              `protobuf:"bytes,4,opt,name=zone,proto3" json:"zone,omitempty"`               // 区域
	Region        string              `protobuf:"bytes,5,opt,name=region,proto3" json:"region,omitempty"`           // 地域
	DisplayName   string              `protobuf:"bytes,6,opt,name=displayName,proto3" json:"displayName,omitempty"` // 显示名称
	Capacity      uint64              `protobuf:"varint,7,opt,name=capacity,proto3" json:"capacity,omitempty"`      // 容量  Gib
	Creator       string              `protobuf:"bytes,8,opt,name=creator,proto3" json:"creator,omitempty"`
	ReadOnly      bool                `protobuf:"varint,9,opt,name=readOnly,proto3" json:"readOnly,omitempty"`
	Specification VolumeSpecification `protobuf:"varint,10,opt,name=specification,proto3,enum=apis.aistudio.cubefs.v1.VolumeSpecification" json:"specification,omitempty"` //规格
	Binding       *VolumeBinding      `protobuf:"bytes,11,opt,name=binding,proto3" json:"binding,omitempty"`
	Managers      []string            `protobuf:"bytes,12,rep,name=managers,proto3" json:"managers,omitempty"`
	Members       []string            `protobuf:"bytes,13,rep,name=members,proto3" json:"members,omitempty"`
	Cluster       string              `protobuf:"bytes,14,opt,name=cluster,proto3" json:"cluster,omitempty"` //集群
}

func (x *CreateVolumeRequest) Reset() {
	*x = CreateVolumeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_cubefs_v1_cubefs_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateVolumeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateVolumeRequest) ProtoMessage() {}

func (x *CreateVolumeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_cubefs_v1_cubefs_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateVolumeRequest.ProtoReflect.Descriptor instead.
func (*CreateVolumeRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_cubefs_v1_cubefs_proto_rawDescGZIP(), []int{4}
}

func (x *CreateVolumeRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *CreateVolumeRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateVolumeRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CreateVolumeRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *CreateVolumeRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *CreateVolumeRequest) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *CreateVolumeRequest) GetCapacity() uint64 {
	if x != nil {
		return x.Capacity
	}
	return 0
}

func (x *CreateVolumeRequest) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *CreateVolumeRequest) GetReadOnly() bool {
	if x != nil {
		return x.ReadOnly
	}
	return false
}

func (x *CreateVolumeRequest) GetSpecification() VolumeSpecification {
	if x != nil {
		return x.Specification
	}
	return VolumeSpecification_Powerful
}

func (x *CreateVolumeRequest) GetBinding() *VolumeBinding {
	if x != nil {
		return x.Binding
	}
	return nil
}

func (x *CreateVolumeRequest) GetManagers() []string {
	if x != nil {
		return x.Managers
	}
	return nil
}

func (x *CreateVolumeRequest) GetMembers() []string {
	if x != nil {
		return x.Members
	}
	return nil
}

func (x *CreateVolumeRequest) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

type VolumeBinding struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Enabled   bool   `protobuf:"varint,1,opt,name=enabled,proto3" json:"enabled,omitempty"`
	VolumeID  string `protobuf:"bytes,2,opt,name=volumeID,proto3" json:"volumeID,omitempty"`
	Cluster   string `protobuf:"bytes,3,opt,name=cluster,proto3" json:"cluster,omitempty"`
	Owner     string `protobuf:"bytes,4,opt,name=owner,proto3" json:"owner,omitempty"`
	AccessKey string `protobuf:"bytes,5,opt,name=accessKey,proto3" json:"accessKey,omitempty"`
	SecretKey string `protobuf:"bytes,6,opt,name=secretKey,proto3" json:"secretKey,omitempty"`
}

func (x *VolumeBinding) Reset() {
	*x = VolumeBinding{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_cubefs_v1_cubefs_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VolumeBinding) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VolumeBinding) ProtoMessage() {}

func (x *VolumeBinding) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_cubefs_v1_cubefs_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VolumeBinding.ProtoReflect.Descriptor instead.
func (*VolumeBinding) Descriptor() ([]byte, []int) {
	return file_aistudio_cubefs_v1_cubefs_proto_rawDescGZIP(), []int{5}
}

func (x *VolumeBinding) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

func (x *VolumeBinding) GetVolumeID() string {
	if x != nil {
		return x.VolumeID
	}
	return ""
}

func (x *VolumeBinding) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

func (x *VolumeBinding) GetOwner() string {
	if x != nil {
		return x.Owner
	}
	return ""
}

func (x *VolumeBinding) GetAccessKey() string {
	if x != nil {
		return x.AccessKey
	}
	return ""
}

func (x *VolumeBinding) GetSecretKey() string {
	if x != nil {
		return x.SecretKey
	}
	return ""
}

type UpdateVolumeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName string   `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Name          string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Description   string   `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	DisplayName   string   `protobuf:"bytes,4,opt,name=displayName,proto3" json:"displayName,omitempty"`
	Capacity      uint64   `protobuf:"varint,5,opt,name=capacity,proto3" json:"capacity,omitempty"`
	Managers      []string `protobuf:"bytes,6,rep,name=managers,proto3" json:"managers,omitempty"`
	Members       []string `protobuf:"bytes,7,rep,name=members,proto3" json:"members,omitempty"`
}

func (x *UpdateVolumeRequest) Reset() {
	*x = UpdateVolumeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_cubefs_v1_cubefs_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateVolumeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateVolumeRequest) ProtoMessage() {}

func (x *UpdateVolumeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_cubefs_v1_cubefs_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateVolumeRequest.ProtoReflect.Descriptor instead.
func (*UpdateVolumeRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_cubefs_v1_cubefs_proto_rawDescGZIP(), []int{6}
}

func (x *UpdateVolumeRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *UpdateVolumeRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UpdateVolumeRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *UpdateVolumeRequest) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *UpdateVolumeRequest) GetCapacity() uint64 {
	if x != nil {
		return x.Capacity
	}
	return 0
}

func (x *UpdateVolumeRequest) GetManagers() []string {
	if x != nil {
		return x.Managers
	}
	return nil
}

func (x *UpdateVolumeRequest) GetMembers() []string {
	if x != nil {
		return x.Members
	}
	return nil
}

type GetVolumeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName string `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Name          string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Region        string `protobuf:"bytes,3,opt,name=region,proto3" json:"region,omitempty"`
}

func (x *GetVolumeRequest) Reset() {
	*x = GetVolumeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_cubefs_v1_cubefs_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetVolumeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetVolumeRequest) ProtoMessage() {}

func (x *GetVolumeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_cubefs_v1_cubefs_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetVolumeRequest.ProtoReflect.Descriptor instead.
func (*GetVolumeRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_cubefs_v1_cubefs_proto_rawDescGZIP(), []int{7}
}

func (x *GetVolumeRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *GetVolumeRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GetVolumeRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

type ListVolumeOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Region        string `protobuf:"bytes,1,opt,name=region,proto3" json:"region,omitempty"`
	WorkspaceName string `protobuf:"bytes,2,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Page          int32  `protobuf:"varint,3,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      int32  `protobuf:"varint,4,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
	Member        string `protobuf:"bytes,5,opt,name=member,proto3" json:"member,omitempty"`
}

func (x *ListVolumeOptions) Reset() {
	*x = ListVolumeOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_cubefs_v1_cubefs_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListVolumeOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListVolumeOptions) ProtoMessage() {}

func (x *ListVolumeOptions) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_cubefs_v1_cubefs_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListVolumeOptions.ProtoReflect.Descriptor instead.
func (*ListVolumeOptions) Descriptor() ([]byte, []int) {
	return file_aistudio_cubefs_v1_cubefs_proto_rawDescGZIP(), []int{8}
}

func (x *ListVolumeOptions) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *ListVolumeOptions) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *ListVolumeOptions) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListVolumeOptions) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListVolumeOptions) GetMember() string {
	if x != nil {
		return x.Member
	}
	return ""
}

type ListCubeFSClustersOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ClusterName string `protobuf:"bytes,1,opt,name=clusterName,proto3" json:"clusterName,omitempty"`
}

func (x *ListCubeFSClustersOptions) Reset() {
	*x = ListCubeFSClustersOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_cubefs_v1_cubefs_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListCubeFSClustersOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCubeFSClustersOptions) ProtoMessage() {}

func (x *ListCubeFSClustersOptions) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_cubefs_v1_cubefs_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCubeFSClustersOptions.ProtoReflect.Descriptor instead.
func (*ListCubeFSClustersOptions) Descriptor() ([]byte, []int) {
	return file_aistudio_cubefs_v1_cubefs_proto_rawDescGZIP(), []int{9}
}

func (x *ListCubeFSClustersOptions) GetClusterName() string {
	if x != nil {
		return x.ClusterName
	}
	return ""
}

type ListCubeFSVolumesByClusterOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ClusterName string `protobuf:"bytes,1,opt,name=clusterName,proto3" json:"clusterName,omitempty"`
	VolumeName  string `protobuf:"bytes,2,opt,name=volumeName,proto3" json:"volumeName,omitempty"`
}

func (x *ListCubeFSVolumesByClusterOptions) Reset() {
	*x = ListCubeFSVolumesByClusterOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_cubefs_v1_cubefs_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListCubeFSVolumesByClusterOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCubeFSVolumesByClusterOptions) ProtoMessage() {}

func (x *ListCubeFSVolumesByClusterOptions) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_cubefs_v1_cubefs_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCubeFSVolumesByClusterOptions.ProtoReflect.Descriptor instead.
func (*ListCubeFSVolumesByClusterOptions) Descriptor() ([]byte, []int) {
	return file_aistudio_cubefs_v1_cubefs_proto_rawDescGZIP(), []int{10}
}

func (x *ListCubeFSVolumesByClusterOptions) GetClusterName() string {
	if x != nil {
		return x.ClusterName
	}
	return ""
}

func (x *ListCubeFSVolumesByClusterOptions) GetVolumeName() string {
	if x != nil {
		return x.VolumeName
	}
	return ""
}

type ListVolumeResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items []*Volume `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	Total int32     `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
}

func (x *ListVolumeResult) Reset() {
	*x = ListVolumeResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_cubefs_v1_cubefs_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListVolumeResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListVolumeResult) ProtoMessage() {}

func (x *ListVolumeResult) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_cubefs_v1_cubefs_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListVolumeResult.ProtoReflect.Descriptor instead.
func (*ListVolumeResult) Descriptor() ([]byte, []int) {
	return file_aistudio_cubefs_v1_cubefs_proto_rawDescGZIP(), []int{11}
}

func (x *ListVolumeResult) GetItems() []*Volume {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *ListVolumeResult) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

type CubeFSVolume struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	VolumeName string `protobuf:"bytes,1,opt,name=volumeName,proto3" json:"volumeName,omitempty"`
	Owner      string `protobuf:"bytes,2,opt,name=owner,proto3" json:"owner,omitempty"`
}

func (x *CubeFSVolume) Reset() {
	*x = CubeFSVolume{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_cubefs_v1_cubefs_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CubeFSVolume) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CubeFSVolume) ProtoMessage() {}

func (x *CubeFSVolume) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_cubefs_v1_cubefs_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CubeFSVolume.ProtoReflect.Descriptor instead.
func (*CubeFSVolume) Descriptor() ([]byte, []int) {
	return file_aistudio_cubefs_v1_cubefs_proto_rawDescGZIP(), []int{12}
}

func (x *CubeFSVolume) GetVolumeName() string {
	if x != nil {
		return x.VolumeName
	}
	return ""
}

func (x *CubeFSVolume) GetOwner() string {
	if x != nil {
		return x.Owner
	}
	return ""
}

type ListCubeFSVolumesResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Vols  []*CubeFSVolume `protobuf:"bytes,1,rep,name=vols,proto3" json:"vols,omitempty"`
	Total int32           `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
}

func (x *ListCubeFSVolumesResult) Reset() {
	*x = ListCubeFSVolumesResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_cubefs_v1_cubefs_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListCubeFSVolumesResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCubeFSVolumesResult) ProtoMessage() {}

func (x *ListCubeFSVolumesResult) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_cubefs_v1_cubefs_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCubeFSVolumesResult.ProtoReflect.Descriptor instead.
func (*ListCubeFSVolumesResult) Descriptor() ([]byte, []int) {
	return file_aistudio_cubefs_v1_cubefs_proto_rawDescGZIP(), []int{13}
}

func (x *ListCubeFSVolumesResult) GetVols() []*CubeFSVolume {
	if x != nil {
		return x.Vols
	}
	return nil
}

func (x *ListCubeFSVolumesResult) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

type CubeFSCluster struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ClusterName   string              `protobuf:"bytes,1,opt,name=clusterName,proto3" json:"clusterName,omitempty"`
	DisplayName   string              `protobuf:"bytes,2,opt,name=displayName,proto3" json:"displayName,omitempty"`
	ClusterDesc   string              `protobuf:"bytes,3,opt,name=clusterDesc,proto3" json:"clusterDesc,omitempty"`
	Region        string              `protobuf:"bytes,4,opt,name=region,proto3" json:"region,omitempty"`
	Zones         []string            `protobuf:"bytes,5,rep,name=zones,proto3" json:"zones,omitempty"`
	Specification VolumeSpecification `protobuf:"varint,6,opt,name=specification,proto3,enum=apis.aistudio.cubefs.v1.VolumeSpecification" json:"specification,omitempty"`
	MasterAddr    string              `protobuf:"bytes,7,opt,name=masterAddr,proto3" json:"masterAddr,omitempty"`
}

func (x *CubeFSCluster) Reset() {
	*x = CubeFSCluster{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_cubefs_v1_cubefs_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CubeFSCluster) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CubeFSCluster) ProtoMessage() {}

func (x *CubeFSCluster) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_cubefs_v1_cubefs_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CubeFSCluster.ProtoReflect.Descriptor instead.
func (*CubeFSCluster) Descriptor() ([]byte, []int) {
	return file_aistudio_cubefs_v1_cubefs_proto_rawDescGZIP(), []int{14}
}

func (x *CubeFSCluster) GetClusterName() string {
	if x != nil {
		return x.ClusterName
	}
	return ""
}

func (x *CubeFSCluster) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *CubeFSCluster) GetClusterDesc() string {
	if x != nil {
		return x.ClusterDesc
	}
	return ""
}

func (x *CubeFSCluster) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *CubeFSCluster) GetZones() []string {
	if x != nil {
		return x.Zones
	}
	return nil
}

func (x *CubeFSCluster) GetSpecification() VolumeSpecification {
	if x != nil {
		return x.Specification
	}
	return VolumeSpecification_Powerful
}

func (x *CubeFSCluster) GetMasterAddr() string {
	if x != nil {
		return x.MasterAddr
	}
	return ""
}

type ListCubeFSClustersResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items []*CubeFSCluster `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
}

func (x *ListCubeFSClustersResult) Reset() {
	*x = ListCubeFSClustersResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_cubefs_v1_cubefs_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListCubeFSClustersResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCubeFSClustersResult) ProtoMessage() {}

func (x *ListCubeFSClustersResult) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_cubefs_v1_cubefs_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCubeFSClustersResult.ProtoReflect.Descriptor instead.
func (*ListCubeFSClustersResult) Descriptor() ([]byte, []int) {
	return file_aistudio_cubefs_v1_cubefs_proto_rawDescGZIP(), []int{15}
}

func (x *ListCubeFSClustersResult) GetItems() []*CubeFSCluster {
	if x != nil {
		return x.Items
	}
	return nil
}

type DeleteVolumeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name          string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	WorkspaceName string `protobuf:"bytes,2,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
}

func (x *DeleteVolumeRequest) Reset() {
	*x = DeleteVolumeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_cubefs_v1_cubefs_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteVolumeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteVolumeRequest) ProtoMessage() {}

func (x *DeleteVolumeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_cubefs_v1_cubefs_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteVolumeRequest.ProtoReflect.Descriptor instead.
func (*DeleteVolumeRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_cubefs_v1_cubefs_proto_rawDescGZIP(), []int{16}
}

func (x *DeleteVolumeRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DeleteVolumeRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

var File_aistudio_cubefs_v1_cubefs_proto protoreflect.FileDescriptor

var file_aistudio_cubefs_v1_cubefs_proto_rawDesc = []byte{
	0x0a, 0x1f, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2f, 0x63, 0x75, 0x62, 0x65, 0x66,
	0x73, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x75, 0x62, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x17, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f,
	0x2e, 0x63, 0x75, 0x62, 0x65, 0x66, 0x73, 0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x13, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65,
	0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xe4, 0x04, 0x0a, 0x06, 0x56,
	0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f,
	0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x6e,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x20, 0x0a,
	0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x1a, 0x0a, 0x08, 0x63, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x08, 0x63, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x12, 0x39, 0x0a, 0x09, 0x74,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b,
	0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x09, 0x74, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x49, 0x0a, 0x0c, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x61,
	0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x63, 0x75, 0x62,
	0x65, 0x66, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x0c, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x52, 0x0a, 0x0d, 0x73,
	0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x2c, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64,
	0x69, 0x6f, 0x2e, 0x63, 0x75, 0x62, 0x65, 0x66, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x6f, 0x6c,
	0x75, 0x6d, 0x65, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x0d, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x40, 0x0a, 0x07, 0x62, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f,
	0x2e, 0x63, 0x75, 0x62, 0x65, 0x66, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x6f, 0x6c, 0x75, 0x6d,
	0x65, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x52, 0x07, 0x62, 0x69, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x18, 0x0e, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x12, 0x1a, 0x0a, 0x08, 0x6d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x73, 0x18, 0x0f, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x6d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x6d, 0x62, 0x65,
	0x72, 0x73, 0x18, 0x10, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72,
	0x73, 0x22, 0x80, 0x02, 0x0a, 0x0c, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x2e, 0x0a, 0x12, 0x6c, 0x61,
	0x73, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x69, 0x6d, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x6c, 0x61, 0x73, 0x74, 0x54, 0x72, 0x61, 0x6e,
	0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6c,
	0x75, 0x73, 0x74, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6c, 0x75,
	0x73, 0x74, 0x65, 0x72, 0x12, 0x22, 0x0a, 0x0c, 0x75, 0x73, 0x61, 0x67, 0x65, 0x50, 0x65, 0x72,
	0x63, 0x65, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x75, 0x73, 0x61, 0x67,
	0x65, 0x50, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x76, 0x6f, 0x6c, 0x75,
	0x6d, 0x65, 0x49, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x76, 0x6f, 0x6c, 0x75,
	0x6d, 0x65, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x69, 0x6e, 0x6f, 0x64, 0x65, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x69, 0x6e, 0x6f, 0x64, 0x65, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x22, 0x6e, 0x0a, 0x26, 0x47, 0x65, 0x74, 0x42, 0x61, 0x72, 0x65, 0x4d,
	0x65, 0x74, 0x61, 0x6c, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24,
	0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x22, 0xcb, 0x02, 0x0a, 0x27, 0x47, 0x65, 0x74, 0x42, 0x61, 0x72, 0x65,
	0x4d, 0x65, 0x74, 0x61, 0x6c, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x6f, 0x6c, 0x4e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x6f, 0x6c, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x1c, 0x0a, 0x09, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x4b, 0x65, 0x79, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x4b, 0x65, 0x79, 0x12, 0x1c,
	0x0a, 0x09, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x4b, 0x65, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x4b, 0x65, 0x79, 0x12, 0x1e, 0x0a, 0x0a,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x12, 0x14, 0x0a, 0x05,
	0x6f, 0x77, 0x6e, 0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6f, 0x77, 0x6e,
	0x65, 0x72, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x66, 0x50, 0x6f, 0x72, 0x74, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x66, 0x50, 0x6f, 0x72, 0x74, 0x12, 0x16,
	0x0a, 0x06, 0x6c, 0x6f, 0x67, 0x44, 0x69, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x6c, 0x6f, 0x67, 0x44, 0x69, 0x72, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x6f, 0x67, 0x4c, 0x65, 0x76,
	0x65, 0x6c, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x6f, 0x67, 0x4c, 0x65, 0x76,
	0x65, 0x6c, 0x12, 0x1e, 0x0a, 0x0a, 0x6d, 0x61, 0x73, 0x74, 0x65, 0x72, 0x41, 0x64, 0x64, 0x72,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6d, 0x61, 0x73, 0x74, 0x65, 0x72, 0x41, 0x64,
	0x64, 0x72, 0x22, 0xf7, 0x03, 0x0a, 0x13, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x56, 0x6f, 0x6c,
	0x75, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f,
	0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65,
	0x67, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69,
	0x6f, 0x6e, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d,
	0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x63, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79,
	0x12, 0x18, 0x0a, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65,
	0x61, 0x64, 0x4f, 0x6e, 0x6c, 0x79, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x72, 0x65,
	0x61, 0x64, 0x4f, 0x6e, 0x6c, 0x79, 0x12, 0x52, 0x0a, 0x0d, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2c, 0x2e,
	0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x63, 0x75,
	0x62, 0x65, 0x66, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x53, 0x70,
	0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0d, 0x73, 0x70, 0x65,
	0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x40, 0x0a, 0x07, 0x62, 0x69,
	0x6e, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x61, 0x70,
	0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x63, 0x75, 0x62, 0x65,
	0x66, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x42, 0x69, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x52, 0x07, 0x62, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x1a, 0x0a, 0x08,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x73, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x6d, 0x62,
	0x65, 0x72, 0x73, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x6d, 0x62, 0x65,
	0x72, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x18, 0x0e, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x22, 0xb1, 0x01, 0x0a,
	0x0d, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x18,
	0x0a, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x76, 0x6f, 0x6c, 0x75,
	0x6d, 0x65, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x76, 0x6f, 0x6c, 0x75,
	0x6d, 0x65, 0x49, 0x44, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x12, 0x14,
	0x0a, 0x05, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6f,
	0x77, 0x6e, 0x65, 0x72, 0x12, 0x1c, 0x0a, 0x09, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x4b, 0x65,
	0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x4b,
	0x65, 0x79, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x4b, 0x65, 0x79, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x4b, 0x65, 0x79,
	0x22, 0xe5, 0x01, 0x0a, 0x13, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x56, 0x6f, 0x6c, 0x75, 0x6d,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e,
	0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c,
	0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x61, 0x70, 0x61, 0x63, 0x69,
	0x74, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x63, 0x61, 0x70, 0x61, 0x63, 0x69,
	0x74, 0x79, 0x12, 0x1a, 0x0a, 0x08, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x73, 0x18, 0x06,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x73, 0x12, 0x18,
	0x0a, 0x07, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x07, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x22, 0x64, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x56,
	0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x0d,
	0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x22, 0x99,
	0x01, 0x0a, 0x11, 0x4c, 0x69, 0x73, 0x74, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x4f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x24, 0x0a, 0x0d,
	0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69,
	0x7a, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69,
	0x7a, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x22, 0x3d, 0x0a, 0x19, 0x4c, 0x69,
	0x73, 0x74, 0x43, 0x75, 0x62, 0x65, 0x46, 0x53, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x73,
	0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x6c, 0x75, 0x73, 0x74,
	0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6c,
	0x75, 0x73, 0x74, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x65, 0x0a, 0x21, 0x4c, 0x69, 0x73,
	0x74, 0x43, 0x75, 0x62, 0x65, 0x46, 0x53, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x73, 0x42, 0x79,
	0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x20,
	0x0a, 0x0b, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x1e, 0x0a, 0x0a, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x22, 0x5f, 0x0a, 0x10, 0x4c, 0x69, 0x73, 0x74, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0x35, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75,
	0x64, 0x69, 0x6f, 0x2e, 0x63, 0x75, 0x62, 0x65, 0x66, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x6f,
	0x6c, 0x75, 0x6d, 0x65, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x22, 0x44, 0x0a, 0x0c, 0x43, 0x75, 0x62, 0x65, 0x46, 0x53, 0x56, 0x6f, 0x6c, 0x75, 0x6d,
	0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x14, 0x0a, 0x05, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x22, 0x6a, 0x0a, 0x17, 0x4c, 0x69, 0x73, 0x74, 0x43,
	0x75, 0x62, 0x65, 0x46, 0x53, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x12, 0x39, 0x0a, 0x04, 0x76, 0x6f, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f,
	0x2e, 0x63, 0x75, 0x62, 0x65, 0x66, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x75, 0x62, 0x65, 0x46,
	0x53, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x52, 0x04, 0x76, 0x6f, 0x6c, 0x73, 0x12, 0x14, 0x0a,
	0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x22, 0x97, 0x02, 0x0a, 0x0d, 0x43, 0x75, 0x62, 0x65, 0x46, 0x53, 0x43, 0x6c,
	0x75, 0x73, 0x74, 0x65, 0x72, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6c, 0x75, 0x73,
	0x74, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c,
	0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x69,
	0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x6c, 0x75,
	0x73, 0x74, 0x65, 0x72, 0x44, 0x65, 0x73, 0x63, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x44, 0x65, 0x73, 0x63, 0x12, 0x16, 0x0a, 0x06, 0x72,
	0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67,
	0x69, 0x6f, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x18, 0x05, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x05, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x12, 0x52, 0x0a, 0x0d, 0x73, 0x70, 0x65,
	0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x2c, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f,
	0x2e, 0x63, 0x75, 0x62, 0x65, 0x66, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x6f, 0x6c, 0x75, 0x6d,
	0x65, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0d,
	0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1e, 0x0a,
	0x0a, 0x6d, 0x61, 0x73, 0x74, 0x65, 0x72, 0x41, 0x64, 0x64, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x6d, 0x61, 0x73, 0x74, 0x65, 0x72, 0x41, 0x64, 0x64, 0x72, 0x22, 0x58, 0x0a,
	0x18, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x75, 0x62, 0x65, 0x46, 0x53, 0x43, 0x6c, 0x75, 0x73, 0x74,
	0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x3c, 0x0a, 0x05, 0x69, 0x74, 0x65,
	0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e,
	0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x63, 0x75, 0x62, 0x65, 0x66, 0x73, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x75, 0x62, 0x65, 0x46, 0x53, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72,
	0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x22, 0x4f, 0x0a, 0x13, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x2a, 0x3d, 0x0a, 0x13, 0x56, 0x6f, 0x6c, 0x75,
	0x6d, 0x65, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x0c, 0x0a, 0x08, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x66, 0x75, 0x6c, 0x10, 0x00, 0x12, 0x0a, 0x0a,
	0x06, 0x4e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x43, 0x61, 0x70,
	0x61, 0x63, 0x69, 0x74, 0x79, 0x10, 0x02, 0x32, 0x89, 0x09, 0x0a, 0x0d, 0x43, 0x75, 0x62, 0x65,
	0x46, 0x53, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x9a, 0x01, 0x0a, 0x0c, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x12, 0x2c, 0x2e, 0x61, 0x70, 0x69,
	0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x63, 0x75, 0x62, 0x65, 0x66,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x56, 0x6f, 0x6c, 0x75, 0x6d,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e,
	0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x63, 0x75, 0x62, 0x65, 0x66, 0x73, 0x2e,
	0x76, 0x31, 0x2e, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x22, 0x3b, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x35, 0x3a, 0x01, 0x2a, 0x22, 0x30, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x77,
	0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2f, 0x7b, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x63, 0x75, 0x62, 0x65, 0x66, 0x73, 0x2f,
	0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x12, 0xa1, 0x01, 0x0a, 0x0c, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x12, 0x2c, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61,
	0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x63, 0x75, 0x62, 0x65, 0x66, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73,
	0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x63, 0x75, 0x62, 0x65, 0x66, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x22, 0x42, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x3c, 0x3a, 0x01,
	0x2a, 0x1a, 0x37, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x6f, 0x72, 0x6b,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x2f, 0x7b, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x63, 0x75, 0x62, 0x65, 0x66, 0x73, 0x2f, 0x76, 0x6f, 0x6c,
	0x75, 0x6d, 0x65, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x12, 0x98, 0x01, 0x0a, 0x09, 0x47,
	0x65, 0x74, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x12, 0x29, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e,
	0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x63, 0x75, 0x62, 0x65, 0x66, 0x73, 0x2e,
	0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75,
	0x64, 0x69, 0x6f, 0x2e, 0x63, 0x75, 0x62, 0x65, 0x66, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x6f,
	0x6c, 0x75, 0x6d, 0x65, 0x22, 0x3f, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x39, 0x12, 0x37, 0x2f, 0x61,
	0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x2f, 0x7b, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d,
	0x2f, 0x63, 0x75, 0x62, 0x65, 0x66, 0x73, 0x2f, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x2f, 0x7b,
	0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x12, 0x9f, 0x01, 0x0a, 0x0b, 0x4c, 0x69, 0x73, 0x74, 0x56, 0x6f,
	0x6c, 0x75, 0x6d, 0x65, 0x73, 0x12, 0x2a, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73,
	0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x63, 0x75, 0x62, 0x65, 0x66, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x4c, 0x69, 0x73, 0x74, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x1a, 0x29, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69,
	0x6f, 0x2e, 0x63, 0x75, 0x62, 0x65, 0x66, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74,
	0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x39, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x33, 0x12, 0x31, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x77,
	0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2f, 0x7b, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x63, 0x75, 0x62, 0x65, 0x66, 0x73, 0x2f,
	0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x73, 0x12, 0x95, 0x01, 0x0a, 0x0c, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x12, 0x2c, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e,
	0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x63, 0x75, 0x62, 0x65, 0x66, 0x73, 0x2e,
	0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x3f,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x39, 0x2a, 0x37, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31,
	0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2f, 0x7b, 0x77, 0x6f, 0x72, 0x6b,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x63, 0x75, 0x62, 0x65, 0x66,
	0x73, 0x2f, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x12,
	0x9d, 0x01, 0x0a, 0x12, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x75, 0x62, 0x65, 0x46, 0x53, 0x43, 0x6c,
	0x75, 0x73, 0x74, 0x65, 0x72, 0x73, 0x12, 0x32, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69,
	0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x63, 0x75, 0x62, 0x65, 0x66, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x75, 0x62, 0x65, 0x46, 0x53, 0x43, 0x6c, 0x75, 0x73, 0x74,
	0x65, 0x72, 0x73, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x1a, 0x31, 0x2e, 0x61, 0x70, 0x69,
	0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x63, 0x75, 0x62, 0x65, 0x66,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x75, 0x62, 0x65, 0x46, 0x53, 0x43,
	0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x20, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x1a, 0x12, 0x18, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f,
	0x63, 0x75, 0x62, 0x65, 0x66, 0x73, 0x2f, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x73, 0x12,
	0xc1, 0x01, 0x0a, 0x1a, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x75, 0x62, 0x65, 0x46, 0x53, 0x56, 0x6f,
	0x6c, 0x75, 0x6d, 0x65, 0x73, 0x42, 0x79, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x12, 0x3a,
	0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x63,
	0x75, 0x62, 0x65, 0x66, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x75, 0x62,
	0x65, 0x46, 0x53, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x73, 0x42, 0x79, 0x43, 0x6c, 0x75, 0x73,
	0x74, 0x65, 0x72, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x1a, 0x30, 0x2e, 0x61, 0x70, 0x69,
	0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x63, 0x75, 0x62, 0x65, 0x66,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x75, 0x62, 0x65, 0x46, 0x53, 0x56,
	0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x35, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x2f, 0x12, 0x2d, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x63,
	0x75, 0x62, 0x65, 0x66, 0x73, 0x2f, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x73, 0x2f, 0x63, 0x6c,
	0x75, 0x73, 0x74, 0x65, 0x72, 0x2f, 0x7b, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x4e, 0x61,
	0x6d, 0x65, 0x7d, 0x42, 0x49, 0x5a, 0x47, 0x67, 0x69, 0x74, 0x2e, 0x6c, 0x69, 0x61, 0x6e, 0x6a,
	0x69, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6e, 0x61, 0x74, 0x69,
	0x76, 0x65, 0x2f, 0x6b, 0x69, 0x63, 0x2f, 0x6b, 0x69, 0x63, 0x2d, 0x70, 0x6c, 0x61, 0x74, 0x66,
	0x6f, 0x72, 0x6d, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69,
	0x6f, 0x2f, 0x63, 0x75, 0x62, 0x65, 0x66, 0x73, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_aistudio_cubefs_v1_cubefs_proto_rawDescOnce sync.Once
	file_aistudio_cubefs_v1_cubefs_proto_rawDescData = file_aistudio_cubefs_v1_cubefs_proto_rawDesc
)

func file_aistudio_cubefs_v1_cubefs_proto_rawDescGZIP() []byte {
	file_aistudio_cubefs_v1_cubefs_proto_rawDescOnce.Do(func() {
		file_aistudio_cubefs_v1_cubefs_proto_rawDescData = protoimpl.X.CompressGZIP(file_aistudio_cubefs_v1_cubefs_proto_rawDescData)
	})
	return file_aistudio_cubefs_v1_cubefs_proto_rawDescData
}

var file_aistudio_cubefs_v1_cubefs_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_aistudio_cubefs_v1_cubefs_proto_msgTypes = make([]protoimpl.MessageInfo, 17)
var file_aistudio_cubefs_v1_cubefs_proto_goTypes = []any{
	(VolumeSpecification)(0),                        // 0: apis.aistudio.cubefs.v1.VolumeSpecification
	(*Volume)(nil),                                  // 1: apis.aistudio.cubefs.v1.Volume
	(*VolumeStatus)(nil),                            // 2: apis.aistudio.cubefs.v1.VolumeStatus
	(*GetBareMetalAccessConfigurationRequest)(nil),  // 3: apis.aistudio.cubefs.v1.GetBareMetalAccessConfigurationRequest
	(*GetBareMetalAccessConfigurationResponse)(nil), // 4: apis.aistudio.cubefs.v1.GetBareMetalAccessConfigurationResponse
	(*CreateVolumeRequest)(nil),                     // 5: apis.aistudio.cubefs.v1.CreateVolumeRequest
	(*VolumeBinding)(nil),                           // 6: apis.aistudio.cubefs.v1.VolumeBinding
	(*UpdateVolumeRequest)(nil),                     // 7: apis.aistudio.cubefs.v1.UpdateVolumeRequest
	(*GetVolumeRequest)(nil),                        // 8: apis.aistudio.cubefs.v1.GetVolumeRequest
	(*ListVolumeOptions)(nil),                       // 9: apis.aistudio.cubefs.v1.ListVolumeOptions
	(*ListCubeFSClustersOptions)(nil),               // 10: apis.aistudio.cubefs.v1.ListCubeFSClustersOptions
	(*ListCubeFSVolumesByClusterOptions)(nil),       // 11: apis.aistudio.cubefs.v1.ListCubeFSVolumesByClusterOptions
	(*ListVolumeResult)(nil),                        // 12: apis.aistudio.cubefs.v1.ListVolumeResult
	(*CubeFSVolume)(nil),                            // 13: apis.aistudio.cubefs.v1.CubeFSVolume
	(*ListCubeFSVolumesResult)(nil),                 // 14: apis.aistudio.cubefs.v1.ListCubeFSVolumesResult
	(*CubeFSCluster)(nil),                           // 15: apis.aistudio.cubefs.v1.CubeFSCluster
	(*ListCubeFSClustersResult)(nil),                // 16: apis.aistudio.cubefs.v1.ListCubeFSClustersResult
	(*DeleteVolumeRequest)(nil),                     // 17: apis.aistudio.cubefs.v1.DeleteVolumeRequest
	(*common.TimestampModel)(nil),                   // 18: apis.common.TimestampModel
	(*emptypb.Empty)(nil),                           // 19: google.protobuf.Empty
}
var file_aistudio_cubefs_v1_cubefs_proto_depIdxs = []int32{
	18, // 0: apis.aistudio.cubefs.v1.Volume.timestamp:type_name -> apis.common.TimestampModel
	2,  // 1: apis.aistudio.cubefs.v1.Volume.volumeStatus:type_name -> apis.aistudio.cubefs.v1.VolumeStatus
	0,  // 2: apis.aistudio.cubefs.v1.Volume.specification:type_name -> apis.aistudio.cubefs.v1.VolumeSpecification
	6,  // 3: apis.aistudio.cubefs.v1.Volume.binding:type_name -> apis.aistudio.cubefs.v1.VolumeBinding
	0,  // 4: apis.aistudio.cubefs.v1.CreateVolumeRequest.specification:type_name -> apis.aistudio.cubefs.v1.VolumeSpecification
	6,  // 5: apis.aistudio.cubefs.v1.CreateVolumeRequest.binding:type_name -> apis.aistudio.cubefs.v1.VolumeBinding
	1,  // 6: apis.aistudio.cubefs.v1.ListVolumeResult.items:type_name -> apis.aistudio.cubefs.v1.Volume
	13, // 7: apis.aistudio.cubefs.v1.ListCubeFSVolumesResult.vols:type_name -> apis.aistudio.cubefs.v1.CubeFSVolume
	0,  // 8: apis.aistudio.cubefs.v1.CubeFSCluster.specification:type_name -> apis.aistudio.cubefs.v1.VolumeSpecification
	15, // 9: apis.aistudio.cubefs.v1.ListCubeFSClustersResult.items:type_name -> apis.aistudio.cubefs.v1.CubeFSCluster
	5,  // 10: apis.aistudio.cubefs.v1.CubeFSService.CreateVolume:input_type -> apis.aistudio.cubefs.v1.CreateVolumeRequest
	7,  // 11: apis.aistudio.cubefs.v1.CubeFSService.UpdateVolume:input_type -> apis.aistudio.cubefs.v1.UpdateVolumeRequest
	8,  // 12: apis.aistudio.cubefs.v1.CubeFSService.GetVolume:input_type -> apis.aistudio.cubefs.v1.GetVolumeRequest
	9,  // 13: apis.aistudio.cubefs.v1.CubeFSService.ListVolumes:input_type -> apis.aistudio.cubefs.v1.ListVolumeOptions
	17, // 14: apis.aistudio.cubefs.v1.CubeFSService.DeleteVolume:input_type -> apis.aistudio.cubefs.v1.DeleteVolumeRequest
	10, // 15: apis.aistudio.cubefs.v1.CubeFSService.ListCubeFSClusters:input_type -> apis.aistudio.cubefs.v1.ListCubeFSClustersOptions
	11, // 16: apis.aistudio.cubefs.v1.CubeFSService.ListCubeFSVolumesByCluster:input_type -> apis.aistudio.cubefs.v1.ListCubeFSVolumesByClusterOptions
	1,  // 17: apis.aistudio.cubefs.v1.CubeFSService.CreateVolume:output_type -> apis.aistudio.cubefs.v1.Volume
	1,  // 18: apis.aistudio.cubefs.v1.CubeFSService.UpdateVolume:output_type -> apis.aistudio.cubefs.v1.Volume
	1,  // 19: apis.aistudio.cubefs.v1.CubeFSService.GetVolume:output_type -> apis.aistudio.cubefs.v1.Volume
	12, // 20: apis.aistudio.cubefs.v1.CubeFSService.ListVolumes:output_type -> apis.aistudio.cubefs.v1.ListVolumeResult
	19, // 21: apis.aistudio.cubefs.v1.CubeFSService.DeleteVolume:output_type -> google.protobuf.Empty
	16, // 22: apis.aistudio.cubefs.v1.CubeFSService.ListCubeFSClusters:output_type -> apis.aistudio.cubefs.v1.ListCubeFSClustersResult
	14, // 23: apis.aistudio.cubefs.v1.CubeFSService.ListCubeFSVolumesByCluster:output_type -> apis.aistudio.cubefs.v1.ListCubeFSVolumesResult
	17, // [17:24] is the sub-list for method output_type
	10, // [10:17] is the sub-list for method input_type
	10, // [10:10] is the sub-list for extension type_name
	10, // [10:10] is the sub-list for extension extendee
	0,  // [0:10] is the sub-list for field type_name
}

func init() { file_aistudio_cubefs_v1_cubefs_proto_init() }
func file_aistudio_cubefs_v1_cubefs_proto_init() {
	if File_aistudio_cubefs_v1_cubefs_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_aistudio_cubefs_v1_cubefs_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*Volume); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_cubefs_v1_cubefs_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*VolumeStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_cubefs_v1_cubefs_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*GetBareMetalAccessConfigurationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_cubefs_v1_cubefs_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*GetBareMetalAccessConfigurationResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_cubefs_v1_cubefs_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*CreateVolumeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_cubefs_v1_cubefs_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*VolumeBinding); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_cubefs_v1_cubefs_proto_msgTypes[6].Exporter = func(v any, i int) any {
			switch v := v.(*UpdateVolumeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_cubefs_v1_cubefs_proto_msgTypes[7].Exporter = func(v any, i int) any {
			switch v := v.(*GetVolumeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_cubefs_v1_cubefs_proto_msgTypes[8].Exporter = func(v any, i int) any {
			switch v := v.(*ListVolumeOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_cubefs_v1_cubefs_proto_msgTypes[9].Exporter = func(v any, i int) any {
			switch v := v.(*ListCubeFSClustersOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_cubefs_v1_cubefs_proto_msgTypes[10].Exporter = func(v any, i int) any {
			switch v := v.(*ListCubeFSVolumesByClusterOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_cubefs_v1_cubefs_proto_msgTypes[11].Exporter = func(v any, i int) any {
			switch v := v.(*ListVolumeResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_cubefs_v1_cubefs_proto_msgTypes[12].Exporter = func(v any, i int) any {
			switch v := v.(*CubeFSVolume); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_cubefs_v1_cubefs_proto_msgTypes[13].Exporter = func(v any, i int) any {
			switch v := v.(*ListCubeFSVolumesResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_cubefs_v1_cubefs_proto_msgTypes[14].Exporter = func(v any, i int) any {
			switch v := v.(*CubeFSCluster); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_cubefs_v1_cubefs_proto_msgTypes[15].Exporter = func(v any, i int) any {
			switch v := v.(*ListCubeFSClustersResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_cubefs_v1_cubefs_proto_msgTypes[16].Exporter = func(v any, i int) any {
			switch v := v.(*DeleteVolumeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_aistudio_cubefs_v1_cubefs_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   17,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_aistudio_cubefs_v1_cubefs_proto_goTypes,
		DependencyIndexes: file_aistudio_cubefs_v1_cubefs_proto_depIdxs,
		EnumInfos:         file_aistudio_cubefs_v1_cubefs_proto_enumTypes,
		MessageInfos:      file_aistudio_cubefs_v1_cubefs_proto_msgTypes,
	}.Build()
	File_aistudio_cubefs_v1_cubefs_proto = out.File
	file_aistudio_cubefs_v1_cubefs_proto_rawDesc = nil
	file_aistudio_cubefs_v1_cubefs_proto_goTypes = nil
	file_aistudio_cubefs_v1_cubefs_proto_depIdxs = nil
}
