// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.3
// - protoc             v5.29.3
// source: aistudio/cubefs/v1/cubefs.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationCubeFSServiceCreateVolume = "/apis.aistudio.cubefs.v1.CubeFSService/CreateVolume"
const OperationCubeFSServiceDeleteVolume = "/apis.aistudio.cubefs.v1.CubeFSService/DeleteVolume"
const OperationCubeFSServiceGetVolume = "/apis.aistudio.cubefs.v1.CubeFSService/GetVolume"
const OperationCubeFSServiceListCubeFSClusters = "/apis.aistudio.cubefs.v1.CubeFSService/ListCubeFSClusters"
const OperationCubeFSServiceListCubeFSVolumesByCluster = "/apis.aistudio.cubefs.v1.CubeFSService/ListCubeFSVolumesByCluster"
const OperationCubeFSServiceListVolumes = "/apis.aistudio.cubefs.v1.CubeFSService/ListVolumes"
const OperationCubeFSServiceUpdateVolume = "/apis.aistudio.cubefs.v1.CubeFSService/UpdateVolume"

type CubeFSServiceHTTPServer interface {
	CreateVolume(context.Context, *CreateVolumeRequest) (*Volume, error)
	DeleteVolume(context.Context, *DeleteVolumeRequest) (*emptypb.Empty, error)
	GetVolume(context.Context, *GetVolumeRequest) (*Volume, error)
	ListCubeFSClusters(context.Context, *ListCubeFSClustersOptions) (*ListCubeFSClustersResult, error)
	ListCubeFSVolumesByCluster(context.Context, *ListCubeFSVolumesByClusterOptions) (*ListCubeFSVolumesResult, error)
	ListVolumes(context.Context, *ListVolumeOptions) (*ListVolumeResult, error)
	UpdateVolume(context.Context, *UpdateVolumeRequest) (*Volume, error)
}

func RegisterCubeFSServiceHTTPServer(s *http.Server, srv CubeFSServiceHTTPServer) {
	r := s.Route("/")
	r.POST("/apis/v1/workspace/{workspaceName}/cubefs/volume", _CubeFSService_CreateVolume1_HTTP_Handler(srv))
	r.PUT("/apis/v1/workspace/{workspaceName}/cubefs/volume/{name}", _CubeFSService_UpdateVolume1_HTTP_Handler(srv))
	r.GET("/apis/v1/workspace/{workspaceName}/cubefs/volume/{name}", _CubeFSService_GetVolume1_HTTP_Handler(srv))
	r.GET("/apis/v1/workspace/{workspaceName}/cubefs/volumes", _CubeFSService_ListVolumes1_HTTP_Handler(srv))
	r.DELETE("/apis/v1/workspace/{workspaceName}/cubefs/volume/{name}", _CubeFSService_DeleteVolume1_HTTP_Handler(srv))
	r.GET("/apis/v1/cubefs/clusters", _CubeFSService_ListCubeFSClusters0_HTTP_Handler(srv))
	r.GET("/apis/v1/cubefs/volumes/cluster/{clusterName}", _CubeFSService_ListCubeFSVolumesByCluster0_HTTP_Handler(srv))
}

func _CubeFSService_CreateVolume1_HTTP_Handler(srv CubeFSServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateVolumeRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCubeFSServiceCreateVolume)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateVolume(ctx, req.(*CreateVolumeRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Volume)
		return ctx.Result(200, reply)
	}
}

func _CubeFSService_UpdateVolume1_HTTP_Handler(srv CubeFSServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UpdateVolumeRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCubeFSServiceUpdateVolume)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateVolume(ctx, req.(*UpdateVolumeRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Volume)
		return ctx.Result(200, reply)
	}
}

func _CubeFSService_GetVolume1_HTTP_Handler(srv CubeFSServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetVolumeRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCubeFSServiceGetVolume)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetVolume(ctx, req.(*GetVolumeRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Volume)
		return ctx.Result(200, reply)
	}
}

func _CubeFSService_ListVolumes1_HTTP_Handler(srv CubeFSServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListVolumeOptions
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCubeFSServiceListVolumes)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListVolumes(ctx, req.(*ListVolumeOptions))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListVolumeResult)
		return ctx.Result(200, reply)
	}
}

func _CubeFSService_DeleteVolume1_HTTP_Handler(srv CubeFSServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteVolumeRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCubeFSServiceDeleteVolume)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteVolume(ctx, req.(*DeleteVolumeRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _CubeFSService_ListCubeFSClusters0_HTTP_Handler(srv CubeFSServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListCubeFSClustersOptions
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCubeFSServiceListCubeFSClusters)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListCubeFSClusters(ctx, req.(*ListCubeFSClustersOptions))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListCubeFSClustersResult)
		return ctx.Result(200, reply)
	}
}

func _CubeFSService_ListCubeFSVolumesByCluster0_HTTP_Handler(srv CubeFSServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListCubeFSVolumesByClusterOptions
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCubeFSServiceListCubeFSVolumesByCluster)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListCubeFSVolumesByCluster(ctx, req.(*ListCubeFSVolumesByClusterOptions))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListCubeFSVolumesResult)
		return ctx.Result(200, reply)
	}
}

type CubeFSServiceHTTPClient interface {
	CreateVolume(ctx context.Context, req *CreateVolumeRequest, opts ...http.CallOption) (rsp *Volume, err error)
	DeleteVolume(ctx context.Context, req *DeleteVolumeRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	GetVolume(ctx context.Context, req *GetVolumeRequest, opts ...http.CallOption) (rsp *Volume, err error)
	ListCubeFSClusters(ctx context.Context, req *ListCubeFSClustersOptions, opts ...http.CallOption) (rsp *ListCubeFSClustersResult, err error)
	ListCubeFSVolumesByCluster(ctx context.Context, req *ListCubeFSVolumesByClusterOptions, opts ...http.CallOption) (rsp *ListCubeFSVolumesResult, err error)
	ListVolumes(ctx context.Context, req *ListVolumeOptions, opts ...http.CallOption) (rsp *ListVolumeResult, err error)
	UpdateVolume(ctx context.Context, req *UpdateVolumeRequest, opts ...http.CallOption) (rsp *Volume, err error)
}

type CubeFSServiceHTTPClientImpl struct {
	cc *http.Client
}

func NewCubeFSServiceHTTPClient(client *http.Client) CubeFSServiceHTTPClient {
	return &CubeFSServiceHTTPClientImpl{client}
}

func (c *CubeFSServiceHTTPClientImpl) CreateVolume(ctx context.Context, in *CreateVolumeRequest, opts ...http.CallOption) (*Volume, error) {
	var out Volume
	pattern := "/apis/v1/workspace/{workspaceName}/cubefs/volume"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCubeFSServiceCreateVolume))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *CubeFSServiceHTTPClientImpl) DeleteVolume(ctx context.Context, in *DeleteVolumeRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/apis/v1/workspace/{workspaceName}/cubefs/volume/{name}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationCubeFSServiceDeleteVolume))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *CubeFSServiceHTTPClientImpl) GetVolume(ctx context.Context, in *GetVolumeRequest, opts ...http.CallOption) (*Volume, error) {
	var out Volume
	pattern := "/apis/v1/workspace/{workspaceName}/cubefs/volume/{name}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationCubeFSServiceGetVolume))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *CubeFSServiceHTTPClientImpl) ListCubeFSClusters(ctx context.Context, in *ListCubeFSClustersOptions, opts ...http.CallOption) (*ListCubeFSClustersResult, error) {
	var out ListCubeFSClustersResult
	pattern := "/apis/v1/cubefs/clusters"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationCubeFSServiceListCubeFSClusters))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *CubeFSServiceHTTPClientImpl) ListCubeFSVolumesByCluster(ctx context.Context, in *ListCubeFSVolumesByClusterOptions, opts ...http.CallOption) (*ListCubeFSVolumesResult, error) {
	var out ListCubeFSVolumesResult
	pattern := "/apis/v1/cubefs/volumes/cluster/{clusterName}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationCubeFSServiceListCubeFSVolumesByCluster))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *CubeFSServiceHTTPClientImpl) ListVolumes(ctx context.Context, in *ListVolumeOptions, opts ...http.CallOption) (*ListVolumeResult, error) {
	var out ListVolumeResult
	pattern := "/apis/v1/workspace/{workspaceName}/cubefs/volumes"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationCubeFSServiceListVolumes))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *CubeFSServiceHTTPClientImpl) UpdateVolume(ctx context.Context, in *UpdateVolumeRequest, opts ...http.CallOption) (*Volume, error) {
	var out Volume
	pattern := "/apis/v1/workspace/{workspaceName}/cubefs/volume/{name}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCubeFSServiceUpdateVolume))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
