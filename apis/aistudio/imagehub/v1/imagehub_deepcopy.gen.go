// Code generated by protoc-gen-deepcopy. DO NOT EDIT.
package v1

import (
	proto "google.golang.org/protobuf/proto"
)

// DeepCopyInto supports using CheckImageHubStatusRequest within kubernetes types, where deepcopy-gen is used.
func (in *CheckImageHubStatusRequest) DeepCopyInto(out *CheckImageHubStatusRequest) {
	p := proto.Clone(in).(*CheckImageHubStatusRequest)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new CheckImageHubStatusRequest. Required by controller-gen.
func (in *CheckImageHubStatusRequest) DeepCopy() *CheckImageHubStatusRequest {
	if in == nil {
		return nil
	}
	out := new(CheckImageHubStatusRequest)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new CheckImageHubStatusRequest. Required by controller-gen.
func (in *CheckImageHubStatusRequest) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using CheckImageHubStatusResponse within kubernetes types, where deepcopy-gen is used.
func (in *CheckImageHubStatusResponse) DeepCopyInto(out *CheckImageHubStatusResponse) {
	p := proto.Clone(in).(*CheckImageHubStatusResponse)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new CheckImageHubStatusResponse. Required by controller-gen.
func (in *CheckImageHubStatusResponse) DeepCopy() *CheckImageHubStatusResponse {
	if in == nil {
		return nil
	}
	out := new(CheckImageHubStatusResponse)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new CheckImageHubStatusResponse. Required by controller-gen.
func (in *CheckImageHubStatusResponse) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using OpenImageHubRequest within kubernetes types, where deepcopy-gen is used.
func (in *OpenImageHubRequest) DeepCopyInto(out *OpenImageHubRequest) {
	p := proto.Clone(in).(*OpenImageHubRequest)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new OpenImageHubRequest. Required by controller-gen.
func (in *OpenImageHubRequest) DeepCopy() *OpenImageHubRequest {
	if in == nil {
		return nil
	}
	out := new(OpenImageHubRequest)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new OpenImageHubRequest. Required by controller-gen.
func (in *OpenImageHubRequest) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using CloseImageHubRequest within kubernetes types, where deepcopy-gen is used.
func (in *CloseImageHubRequest) DeepCopyInto(out *CloseImageHubRequest) {
	p := proto.Clone(in).(*CloseImageHubRequest)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new CloseImageHubRequest. Required by controller-gen.
func (in *CloseImageHubRequest) DeepCopy() *CloseImageHubRequest {
	if in == nil {
		return nil
	}
	out := new(CloseImageHubRequest)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new CloseImageHubRequest. Required by controller-gen.
func (in *CloseImageHubRequest) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using DeleteImageRequest within kubernetes types, where deepcopy-gen is used.
func (in *DeleteImageRequest) DeepCopyInto(out *DeleteImageRequest) {
	p := proto.Clone(in).(*DeleteImageRequest)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new DeleteImageRequest. Required by controller-gen.
func (in *DeleteImageRequest) DeepCopy() *DeleteImageRequest {
	if in == nil {
		return nil
	}
	out := new(DeleteImageRequest)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new DeleteImageRequest. Required by controller-gen.
func (in *DeleteImageRequest) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using AddImagesRequest within kubernetes types, where deepcopy-gen is used.
func (in *AddImagesRequest) DeepCopyInto(out *AddImagesRequest) {
	p := proto.Clone(in).(*AddImagesRequest)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new AddImagesRequest. Required by controller-gen.
func (in *AddImagesRequest) DeepCopy() *AddImagesRequest {
	if in == nil {
		return nil
	}
	out := new(AddImagesRequest)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new AddImagesRequest. Required by controller-gen.
func (in *AddImagesRequest) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using UpdateImageRequest within kubernetes types, where deepcopy-gen is used.
func (in *UpdateImageRequest) DeepCopyInto(out *UpdateImageRequest) {
	p := proto.Clone(in).(*UpdateImageRequest)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new UpdateImageRequest. Required by controller-gen.
func (in *UpdateImageRequest) DeepCopy() *UpdateImageRequest {
	if in == nil {
		return nil
	}
	out := new(UpdateImageRequest)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new UpdateImageRequest. Required by controller-gen.
func (in *UpdateImageRequest) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using ListImagesOptions within kubernetes types, where deepcopy-gen is used.
func (in *ListImagesOptions) DeepCopyInto(out *ListImagesOptions) {
	p := proto.Clone(in).(*ListImagesOptions)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ListImagesOptions. Required by controller-gen.
func (in *ListImagesOptions) DeepCopy() *ListImagesOptions {
	if in == nil {
		return nil
	}
	out := new(ListImagesOptions)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new ListImagesOptions. Required by controller-gen.
func (in *ListImagesOptions) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using Image within kubernetes types, where deepcopy-gen is used.
func (in *Image) DeepCopyInto(out *Image) {
	p := proto.Clone(in).(*Image)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Image. Required by controller-gen.
func (in *Image) DeepCopy() *Image {
	if in == nil {
		return nil
	}
	out := new(Image)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new Image. Required by controller-gen.
func (in *Image) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using ListImagesResponse within kubernetes types, where deepcopy-gen is used.
func (in *ListImagesResponse) DeepCopyInto(out *ListImagesResponse) {
	p := proto.Clone(in).(*ListImagesResponse)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ListImagesResponse. Required by controller-gen.
func (in *ListImagesResponse) DeepCopy() *ListImagesResponse {
	if in == nil {
		return nil
	}
	out := new(ListImagesResponse)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new ListImagesResponse. Required by controller-gen.
func (in *ListImagesResponse) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using CascadeImage within kubernetes types, where deepcopy-gen is used.
func (in *CascadeImage) DeepCopyInto(out *CascadeImage) {
	p := proto.Clone(in).(*CascadeImage)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new CascadeImage. Required by controller-gen.
func (in *CascadeImage) DeepCopy() *CascadeImage {
	if in == nil {
		return nil
	}
	out := new(CascadeImage)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new CascadeImage. Required by controller-gen.
func (in *CascadeImage) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using ImageChildren within kubernetes types, where deepcopy-gen is used.
func (in *ImageChildren) DeepCopyInto(out *ImageChildren) {
	p := proto.Clone(in).(*ImageChildren)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ImageChildren. Required by controller-gen.
func (in *ImageChildren) DeepCopy() *ImageChildren {
	if in == nil {
		return nil
	}
	out := new(ImageChildren)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new ImageChildren. Required by controller-gen.
func (in *ImageChildren) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using CascadeImages within kubernetes types, where deepcopy-gen is used.
func (in *CascadeImages) DeepCopyInto(out *CascadeImages) {
	p := proto.Clone(in).(*CascadeImages)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new CascadeImages. Required by controller-gen.
func (in *CascadeImages) DeepCopy() *CascadeImages {
	if in == nil {
		return nil
	}
	out := new(CascadeImages)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new CascadeImages. Required by controller-gen.
func (in *CascadeImages) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using GetServiceAccountsRequest within kubernetes types, where deepcopy-gen is used.
func (in *GetServiceAccountsRequest) DeepCopyInto(out *GetServiceAccountsRequest) {
	p := proto.Clone(in).(*GetServiceAccountsRequest)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new GetServiceAccountsRequest. Required by controller-gen.
func (in *GetServiceAccountsRequest) DeepCopy() *GetServiceAccountsRequest {
	if in == nil {
		return nil
	}
	out := new(GetServiceAccountsRequest)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new GetServiceAccountsRequest. Required by controller-gen.
func (in *GetServiceAccountsRequest) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using ServiceAccounts within kubernetes types, where deepcopy-gen is used.
func (in *ServiceAccounts) DeepCopyInto(out *ServiceAccounts) {
	p := proto.Clone(in).(*ServiceAccounts)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ServiceAccounts. Required by controller-gen.
func (in *ServiceAccounts) DeepCopy() *ServiceAccounts {
	if in == nil {
		return nil
	}
	out := new(ServiceAccounts)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new ServiceAccounts. Required by controller-gen.
func (in *ServiceAccounts) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using ServiceAccount within kubernetes types, where deepcopy-gen is used.
func (in *ServiceAccount) DeepCopyInto(out *ServiceAccount) {
	p := proto.Clone(in).(*ServiceAccount)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ServiceAccount. Required by controller-gen.
func (in *ServiceAccount) DeepCopy() *ServiceAccount {
	if in == nil {
		return nil
	}
	out := new(ServiceAccount)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new ServiceAccount. Required by controller-gen.
func (in *ServiceAccount) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using PreheatJob within kubernetes types, where deepcopy-gen is used.
func (in *PreheatJob) DeepCopyInto(out *PreheatJob) {
	p := proto.Clone(in).(*PreheatJob)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new PreheatJob. Required by controller-gen.
func (in *PreheatJob) DeepCopy() *PreheatJob {
	if in == nil {
		return nil
	}
	out := new(PreheatJob)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new PreheatJob. Required by controller-gen.
func (in *PreheatJob) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using ImagePullJob within kubernetes types, where deepcopy-gen is used.
func (in *ImagePullJob) DeepCopyInto(out *ImagePullJob) {
	p := proto.Clone(in).(*ImagePullJob)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ImagePullJob. Required by controller-gen.
func (in *ImagePullJob) DeepCopy() *ImagePullJob {
	if in == nil {
		return nil
	}
	out := new(ImagePullJob)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new ImagePullJob. Required by controller-gen.
func (in *ImagePullJob) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using PreheatJobs within kubernetes types, where deepcopy-gen is used.
func (in *PreheatJobs) DeepCopyInto(out *PreheatJobs) {
	p := proto.Clone(in).(*PreheatJobs)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new PreheatJobs. Required by controller-gen.
func (in *PreheatJobs) DeepCopy() *PreheatJobs {
	if in == nil {
		return nil
	}
	out := new(PreheatJobs)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new PreheatJobs. Required by controller-gen.
func (in *PreheatJobs) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using CreatePreheatJobRequest within kubernetes types, where deepcopy-gen is used.
func (in *CreatePreheatJobRequest) DeepCopyInto(out *CreatePreheatJobRequest) {
	p := proto.Clone(in).(*CreatePreheatJobRequest)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new CreatePreheatJobRequest. Required by controller-gen.
func (in *CreatePreheatJobRequest) DeepCopy() *CreatePreheatJobRequest {
	if in == nil {
		return nil
	}
	out := new(CreatePreheatJobRequest)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new CreatePreheatJobRequest. Required by controller-gen.
func (in *CreatePreheatJobRequest) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using GetPreheatJobRequest within kubernetes types, where deepcopy-gen is used.
func (in *GetPreheatJobRequest) DeepCopyInto(out *GetPreheatJobRequest) {
	p := proto.Clone(in).(*GetPreheatJobRequest)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new GetPreheatJobRequest. Required by controller-gen.
func (in *GetPreheatJobRequest) DeepCopy() *GetPreheatJobRequest {
	if in == nil {
		return nil
	}
	out := new(GetPreheatJobRequest)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new GetPreheatJobRequest. Required by controller-gen.
func (in *GetPreheatJobRequest) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using ListPreheatJobOptions within kubernetes types, where deepcopy-gen is used.
func (in *ListPreheatJobOptions) DeepCopyInto(out *ListPreheatJobOptions) {
	p := proto.Clone(in).(*ListPreheatJobOptions)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ListPreheatJobOptions. Required by controller-gen.
func (in *ListPreheatJobOptions) DeepCopy() *ListPreheatJobOptions {
	if in == nil {
		return nil
	}
	out := new(ListPreheatJobOptions)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new ListPreheatJobOptions. Required by controller-gen.
func (in *ListPreheatJobOptions) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using DeletePreheatJobRequest within kubernetes types, where deepcopy-gen is used.
func (in *DeletePreheatJobRequest) DeepCopyInto(out *DeletePreheatJobRequest) {
	p := proto.Clone(in).(*DeletePreheatJobRequest)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new DeletePreheatJobRequest. Required by controller-gen.
func (in *DeletePreheatJobRequest) DeepCopy() *DeletePreheatJobRequest {
	if in == nil {
		return nil
	}
	out := new(DeletePreheatJobRequest)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new DeletePreheatJobRequest. Required by controller-gen.
func (in *DeletePreheatJobRequest) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using RedeployPreheatJobRequest within kubernetes types, where deepcopy-gen is used.
func (in *RedeployPreheatJobRequest) DeepCopyInto(out *RedeployPreheatJobRequest) {
	p := proto.Clone(in).(*RedeployPreheatJobRequest)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new RedeployPreheatJobRequest. Required by controller-gen.
func (in *RedeployPreheatJobRequest) DeepCopy() *RedeployPreheatJobRequest {
	if in == nil {
		return nil
	}
	out := new(RedeployPreheatJobRequest)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new RedeployPreheatJobRequest. Required by controller-gen.
func (in *RedeployPreheatJobRequest) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using ListImageNamesOptions within kubernetes types, where deepcopy-gen is used.
func (in *ListImageNamesOptions) DeepCopyInto(out *ListImageNamesOptions) {
	p := proto.Clone(in).(*ListImageNamesOptions)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ListImageNamesOptions. Required by controller-gen.
func (in *ListImageNamesOptions) DeepCopy() *ListImageNamesOptions {
	if in == nil {
		return nil
	}
	out := new(ListImageNamesOptions)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new ListImageNamesOptions. Required by controller-gen.
func (in *ListImageNamesOptions) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using ImageName within kubernetes types, where deepcopy-gen is used.
func (in *ImageName) DeepCopyInto(out *ImageName) {
	p := proto.Clone(in).(*ImageName)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ImageName. Required by controller-gen.
func (in *ImageName) DeepCopy() *ImageName {
	if in == nil {
		return nil
	}
	out := new(ImageName)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new ImageName. Required by controller-gen.
func (in *ImageName) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using ListImageNamesResponse within kubernetes types, where deepcopy-gen is used.
func (in *ListImageNamesResponse) DeepCopyInto(out *ListImageNamesResponse) {
	p := proto.Clone(in).(*ListImageNamesResponse)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ListImageNamesResponse. Required by controller-gen.
func (in *ListImageNamesResponse) DeepCopy() *ListImageNamesResponse {
	if in == nil {
		return nil
	}
	out := new(ListImageNamesResponse)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new ListImageNamesResponse. Required by controller-gen.
func (in *ListImageNamesResponse) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using ListImageTagsOptions within kubernetes types, where deepcopy-gen is used.
func (in *ListImageTagsOptions) DeepCopyInto(out *ListImageTagsOptions) {
	p := proto.Clone(in).(*ListImageTagsOptions)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ListImageTagsOptions. Required by controller-gen.
func (in *ListImageTagsOptions) DeepCopy() *ListImageTagsOptions {
	if in == nil {
		return nil
	}
	out := new(ListImageTagsOptions)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new ListImageTagsOptions. Required by controller-gen.
func (in *ListImageTagsOptions) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using ListImageTagsResponse within kubernetes types, where deepcopy-gen is used.
func (in *ListImageTagsResponse) DeepCopyInto(out *ListImageTagsResponse) {
	p := proto.Clone(in).(*ListImageTagsResponse)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ListImageTagsResponse. Required by controller-gen.
func (in *ListImageTagsResponse) DeepCopy() *ListImageTagsResponse {
	if in == nil {
		return nil
	}
	out := new(ListImageTagsResponse)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new ListImageTagsResponse. Required by controller-gen.
func (in *ListImageTagsResponse) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using ImageTag within kubernetes types, where deepcopy-gen is used.
func (in *ImageTag) DeepCopyInto(out *ImageTag) {
	p := proto.Clone(in).(*ImageTag)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ImageTag. Required by controller-gen.
func (in *ImageTag) DeepCopy() *ImageTag {
	if in == nil {
		return nil
	}
	out := new(ImageTag)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new ImageTag. Required by controller-gen.
func (in *ImageTag) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using GetImageTagRequest within kubernetes types, where deepcopy-gen is used.
func (in *GetImageTagRequest) DeepCopyInto(out *GetImageTagRequest) {
	p := proto.Clone(in).(*GetImageTagRequest)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new GetImageTagRequest. Required by controller-gen.
func (in *GetImageTagRequest) DeepCopy() *GetImageTagRequest {
	if in == nil {
		return nil
	}
	out := new(GetImageTagRequest)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new GetImageTagRequest. Required by controller-gen.
func (in *GetImageTagRequest) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using ModifyImageDescriptionRequest within kubernetes types, where deepcopy-gen is used.
func (in *ModifyImageDescriptionRequest) DeepCopyInto(out *ModifyImageDescriptionRequest) {
	p := proto.Clone(in).(*ModifyImageDescriptionRequest)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ModifyImageDescriptionRequest. Required by controller-gen.
func (in *ModifyImageDescriptionRequest) DeepCopy() *ModifyImageDescriptionRequest {
	if in == nil {
		return nil
	}
	out := new(ModifyImageDescriptionRequest)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new ModifyImageDescriptionRequest. Required by controller-gen.
func (in *ModifyImageDescriptionRequest) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using BuildImageRequest within kubernetes types, where deepcopy-gen is used.
func (in *BuildImageRequest) DeepCopyInto(out *BuildImageRequest) {
	p := proto.Clone(in).(*BuildImageRequest)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new BuildImageRequest. Required by controller-gen.
func (in *BuildImageRequest) DeepCopy() *BuildImageRequest {
	if in == nil {
		return nil
	}
	out := new(BuildImageRequest)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new BuildImageRequest. Required by controller-gen.
func (in *BuildImageRequest) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using BuildImageResponse within kubernetes types, where deepcopy-gen is used.
func (in *BuildImageResponse) DeepCopyInto(out *BuildImageResponse) {
	p := proto.Clone(in).(*BuildImageResponse)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new BuildImageResponse. Required by controller-gen.
func (in *BuildImageResponse) DeepCopy() *BuildImageResponse {
	if in == nil {
		return nil
	}
	out := new(BuildImageResponse)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new BuildImageResponse. Required by controller-gen.
func (in *BuildImageResponse) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using BuildImageData within kubernetes types, where deepcopy-gen is used.
func (in *BuildImageData) DeepCopyInto(out *BuildImageData) {
	p := proto.Clone(in).(*BuildImageData)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new BuildImageData. Required by controller-gen.
func (in *BuildImageData) DeepCopy() *BuildImageData {
	if in == nil {
		return nil
	}
	out := new(BuildImageData)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new BuildImageData. Required by controller-gen.
func (in *BuildImageData) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using CreateImageBaseRequest within kubernetes types, where deepcopy-gen is used.
func (in *CreateImageBaseRequest) DeepCopyInto(out *CreateImageBaseRequest) {
	p := proto.Clone(in).(*CreateImageBaseRequest)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new CreateImageBaseRequest. Required by controller-gen.
func (in *CreateImageBaseRequest) DeepCopy() *CreateImageBaseRequest {
	if in == nil {
		return nil
	}
	out := new(CreateImageBaseRequest)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new CreateImageBaseRequest. Required by controller-gen.
func (in *CreateImageBaseRequest) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using CreateImageBaseResponse within kubernetes types, where deepcopy-gen is used.
func (in *CreateImageBaseResponse) DeepCopyInto(out *CreateImageBaseResponse) {
	p := proto.Clone(in).(*CreateImageBaseResponse)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new CreateImageBaseResponse. Required by controller-gen.
func (in *CreateImageBaseResponse) DeepCopy() *CreateImageBaseResponse {
	if in == nil {
		return nil
	}
	out := new(CreateImageBaseResponse)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new CreateImageBaseResponse. Required by controller-gen.
func (in *CreateImageBaseResponse) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using UpdateImageBaseRequest within kubernetes types, where deepcopy-gen is used.
func (in *UpdateImageBaseRequest) DeepCopyInto(out *UpdateImageBaseRequest) {
	p := proto.Clone(in).(*UpdateImageBaseRequest)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new UpdateImageBaseRequest. Required by controller-gen.
func (in *UpdateImageBaseRequest) DeepCopy() *UpdateImageBaseRequest {
	if in == nil {
		return nil
	}
	out := new(UpdateImageBaseRequest)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new UpdateImageBaseRequest. Required by controller-gen.
func (in *UpdateImageBaseRequest) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using UpdateImageBaseResponse within kubernetes types, where deepcopy-gen is used.
func (in *UpdateImageBaseResponse) DeepCopyInto(out *UpdateImageBaseResponse) {
	p := proto.Clone(in).(*UpdateImageBaseResponse)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new UpdateImageBaseResponse. Required by controller-gen.
func (in *UpdateImageBaseResponse) DeepCopy() *UpdateImageBaseResponse {
	if in == nil {
		return nil
	}
	out := new(UpdateImageBaseResponse)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new UpdateImageBaseResponse. Required by controller-gen.
func (in *UpdateImageBaseResponse) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using DeleteImageBaseRequest within kubernetes types, where deepcopy-gen is used.
func (in *DeleteImageBaseRequest) DeepCopyInto(out *DeleteImageBaseRequest) {
	p := proto.Clone(in).(*DeleteImageBaseRequest)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new DeleteImageBaseRequest. Required by controller-gen.
func (in *DeleteImageBaseRequest) DeepCopy() *DeleteImageBaseRequest {
	if in == nil {
		return nil
	}
	out := new(DeleteImageBaseRequest)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new DeleteImageBaseRequest. Required by controller-gen.
func (in *DeleteImageBaseRequest) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using DeleteImageBaseResponse within kubernetes types, where deepcopy-gen is used.
func (in *DeleteImageBaseResponse) DeepCopyInto(out *DeleteImageBaseResponse) {
	p := proto.Clone(in).(*DeleteImageBaseResponse)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new DeleteImageBaseResponse. Required by controller-gen.
func (in *DeleteImageBaseResponse) DeepCopy() *DeleteImageBaseResponse {
	if in == nil {
		return nil
	}
	out := new(DeleteImageBaseResponse)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new DeleteImageBaseResponse. Required by controller-gen.
func (in *DeleteImageBaseResponse) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using ListImageBaseRequest within kubernetes types, where deepcopy-gen is used.
func (in *ListImageBaseRequest) DeepCopyInto(out *ListImageBaseRequest) {
	p := proto.Clone(in).(*ListImageBaseRequest)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ListImageBaseRequest. Required by controller-gen.
func (in *ListImageBaseRequest) DeepCopy() *ListImageBaseRequest {
	if in == nil {
		return nil
	}
	out := new(ListImageBaseRequest)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new ListImageBaseRequest. Required by controller-gen.
func (in *ListImageBaseRequest) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using ListImageBaseResponse within kubernetes types, where deepcopy-gen is used.
func (in *ListImageBaseResponse) DeepCopyInto(out *ListImageBaseResponse) {
	p := proto.Clone(in).(*ListImageBaseResponse)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ListImageBaseResponse. Required by controller-gen.
func (in *ListImageBaseResponse) DeepCopy() *ListImageBaseResponse {
	if in == nil {
		return nil
	}
	out := new(ListImageBaseResponse)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new ListImageBaseResponse. Required by controller-gen.
func (in *ListImageBaseResponse) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using ImageBase within kubernetes types, where deepcopy-gen is used.
func (in *ImageBase) DeepCopyInto(out *ImageBase) {
	p := proto.Clone(in).(*ImageBase)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ImageBase. Required by controller-gen.
func (in *ImageBase) DeepCopy() *ImageBase {
	if in == nil {
		return nil
	}
	out := new(ImageBase)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new ImageBase. Required by controller-gen.
func (in *ImageBase) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using ListImageBuildHistoryRequest within kubernetes types, where deepcopy-gen is used.
func (in *ListImageBuildHistoryRequest) DeepCopyInto(out *ListImageBuildHistoryRequest) {
	p := proto.Clone(in).(*ListImageBuildHistoryRequest)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ListImageBuildHistoryRequest. Required by controller-gen.
func (in *ListImageBuildHistoryRequest) DeepCopy() *ListImageBuildHistoryRequest {
	if in == nil {
		return nil
	}
	out := new(ListImageBuildHistoryRequest)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new ListImageBuildHistoryRequest. Required by controller-gen.
func (in *ListImageBuildHistoryRequest) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using ListImageBuildHistoryResponse within kubernetes types, where deepcopy-gen is used.
func (in *ListImageBuildHistoryResponse) DeepCopyInto(out *ListImageBuildHistoryResponse) {
	p := proto.Clone(in).(*ListImageBuildHistoryResponse)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ListImageBuildHistoryResponse. Required by controller-gen.
func (in *ListImageBuildHistoryResponse) DeepCopy() *ListImageBuildHistoryResponse {
	if in == nil {
		return nil
	}
	out := new(ListImageBuildHistoryResponse)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new ListImageBuildHistoryResponse. Required by controller-gen.
func (in *ListImageBuildHistoryResponse) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using ImageBuildHistory within kubernetes types, where deepcopy-gen is used.
func (in *ImageBuildHistory) DeepCopyInto(out *ImageBuildHistory) {
	p := proto.Clone(in).(*ImageBuildHistory)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ImageBuildHistory. Required by controller-gen.
func (in *ImageBuildHistory) DeepCopy() *ImageBuildHistory {
	if in == nil {
		return nil
	}
	out := new(ImageBuildHistory)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new ImageBuildHistory. Required by controller-gen.
func (in *ImageBuildHistory) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using ImageCondition within kubernetes types, where deepcopy-gen is used.
func (in *ImageCondition) DeepCopyInto(out *ImageCondition) {
	p := proto.Clone(in).(*ImageCondition)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ImageCondition. Required by controller-gen.
func (in *ImageCondition) DeepCopy() *ImageCondition {
	if in == nil {
		return nil
	}
	out := new(ImageCondition)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new ImageCondition. Required by controller-gen.
func (in *ImageCondition) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using StopBuildImageRequest within kubernetes types, where deepcopy-gen is used.
func (in *StopBuildImageRequest) DeepCopyInto(out *StopBuildImageRequest) {
	p := proto.Clone(in).(*StopBuildImageRequest)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new StopBuildImageRequest. Required by controller-gen.
func (in *StopBuildImageRequest) DeepCopy() *StopBuildImageRequest {
	if in == nil {
		return nil
	}
	out := new(StopBuildImageRequest)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new StopBuildImageRequest. Required by controller-gen.
func (in *StopBuildImageRequest) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using StopBuildImageResponse within kubernetes types, where deepcopy-gen is used.
func (in *StopBuildImageResponse) DeepCopyInto(out *StopBuildImageResponse) {
	p := proto.Clone(in).(*StopBuildImageResponse)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new StopBuildImageResponse. Required by controller-gen.
func (in *StopBuildImageResponse) DeepCopy() *StopBuildImageResponse {
	if in == nil {
		return nil
	}
	out := new(StopBuildImageResponse)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new StopBuildImageResponse. Required by controller-gen.
func (in *StopBuildImageResponse) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using CheckImageTagRequest within kubernetes types, where deepcopy-gen is used.
func (in *CheckImageTagRequest) DeepCopyInto(out *CheckImageTagRequest) {
	p := proto.Clone(in).(*CheckImageTagRequest)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new CheckImageTagRequest. Required by controller-gen.
func (in *CheckImageTagRequest) DeepCopy() *CheckImageTagRequest {
	if in == nil {
		return nil
	}
	out := new(CheckImageTagRequest)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new CheckImageTagRequest. Required by controller-gen.
func (in *CheckImageTagRequest) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using CheckImageTagResponse within kubernetes types, where deepcopy-gen is used.
func (in *CheckImageTagResponse) DeepCopyInto(out *CheckImageTagResponse) {
	p := proto.Clone(in).(*CheckImageTagResponse)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new CheckImageTagResponse. Required by controller-gen.
func (in *CheckImageTagResponse) DeepCopy() *CheckImageTagResponse {
	if in == nil {
		return nil
	}
	out := new(CheckImageTagResponse)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new CheckImageTagResponse. Required by controller-gen.
func (in *CheckImageTagResponse) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}
