// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v5.29.3
// source: aistudio/imagehub/v1/imagehub.proto

package v1

import (
	common "git.lianjia.com/cloudnative/kic/kic-platform/apis/common"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	_ "google.golang.org/protobuf/types/known/durationpb"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	_ "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ImagePullPolicy int32

const (
	ImagePullPolicy_Always       ImagePullPolicy = 0
	ImagePullPolicy_IfNotPresent ImagePullPolicy = 1
)

// Enum value maps for ImagePullPolicy.
var (
	ImagePullPolicy_name = map[int32]string{
		0: "Always",
		1: "IfNotPresent",
	}
	ImagePullPolicy_value = map[string]int32{
		"Always":       0,
		"IfNotPresent": 1,
	}
)

func (x ImagePullPolicy) Enum() *ImagePullPolicy {
	p := new(ImagePullPolicy)
	*p = x
	return p
}

func (x ImagePullPolicy) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ImagePullPolicy) Descriptor() protoreflect.EnumDescriptor {
	return file_aistudio_imagehub_v1_imagehub_proto_enumTypes[0].Descriptor()
}

func (ImagePullPolicy) Type() protoreflect.EnumType {
	return &file_aistudio_imagehub_v1_imagehub_proto_enumTypes[0]
}

func (x ImagePullPolicy) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ImagePullPolicy.Descriptor instead.
func (ImagePullPolicy) EnumDescriptor() ([]byte, []int) {
	return file_aistudio_imagehub_v1_imagehub_proto_rawDescGZIP(), []int{0}
}

type CheckImageHubStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName string `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Region        string `protobuf:"bytes,2,opt,name=region,proto3" json:"region,omitempty"`
}

func (x *CheckImageHubStatusRequest) Reset() {
	*x = CheckImageHubStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckImageHubStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckImageHubStatusRequest) ProtoMessage() {}

func (x *CheckImageHubStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckImageHubStatusRequest.ProtoReflect.Descriptor instead.
func (*CheckImageHubStatusRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_imagehub_v1_imagehub_proto_rawDescGZIP(), []int{0}
}

func (x *CheckImageHubStatusRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *CheckImageHubStatusRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

type CheckImageHubStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Activated bool `protobuf:"varint,1,opt,name=activated,proto3" json:"activated,omitempty"`
	Supported bool `protobuf:"varint,2,opt,name=supported,proto3" json:"supported,omitempty"`
}

func (x *CheckImageHubStatusResponse) Reset() {
	*x = CheckImageHubStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckImageHubStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckImageHubStatusResponse) ProtoMessage() {}

func (x *CheckImageHubStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckImageHubStatusResponse.ProtoReflect.Descriptor instead.
func (*CheckImageHubStatusResponse) Descriptor() ([]byte, []int) {
	return file_aistudio_imagehub_v1_imagehub_proto_rawDescGZIP(), []int{1}
}

func (x *CheckImageHubStatusResponse) GetActivated() bool {
	if x != nil {
		return x.Activated
	}
	return false
}

func (x *CheckImageHubStatusResponse) GetSupported() bool {
	if x != nil {
		return x.Supported
	}
	return false
}

type OpenImageHubRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName string `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Region        string `protobuf:"bytes,2,opt,name=region,proto3" json:"region,omitempty"`
}

func (x *OpenImageHubRequest) Reset() {
	*x = OpenImageHubRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OpenImageHubRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OpenImageHubRequest) ProtoMessage() {}

func (x *OpenImageHubRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OpenImageHubRequest.ProtoReflect.Descriptor instead.
func (*OpenImageHubRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_imagehub_v1_imagehub_proto_rawDescGZIP(), []int{2}
}

func (x *OpenImageHubRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *OpenImageHubRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

type CloseImageHubRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName string `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Region        string `protobuf:"bytes,2,opt,name=region,proto3" json:"region,omitempty"`
}

func (x *CloseImageHubRequest) Reset() {
	*x = CloseImageHubRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CloseImageHubRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CloseImageHubRequest) ProtoMessage() {}

func (x *CloseImageHubRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CloseImageHubRequest.ProtoReflect.Descriptor instead.
func (*CloseImageHubRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_imagehub_v1_imagehub_proto_rawDescGZIP(), []int{3}
}

func (x *CloseImageHubRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *CloseImageHubRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

type DeleteImageRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *DeleteImageRequest) Reset() {
	*x = DeleteImageRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteImageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteImageRequest) ProtoMessage() {}

func (x *DeleteImageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteImageRequest.ProtoReflect.Descriptor instead.
func (*DeleteImageRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_imagehub_v1_imagehub_proto_rawDescGZIP(), []int{4}
}

func (x *DeleteImageRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type AddImagesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Region        string   `protobuf:"bytes,1,opt,name=region,proto3" json:"region,omitempty"`
	Namespace     string   `protobuf:"bytes,2,opt,name=namespace,proto3" json:"namespace,omitempty"`
	Name          string   `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Domain        string   `protobuf:"bytes,4,opt,name=domain,proto3" json:"domain,omitempty"`
	Tags          []string `protobuf:"bytes,5,rep,name=tags,proto3" json:"tags,omitempty"`
	Description   string   `protobuf:"bytes,6,opt,name=description,proto3" json:"description,omitempty"`
	Purposes      []string `protobuf:"bytes,8,rep,name=purposes,proto3" json:"purposes,omitempty"`
	Label         []string `protobuf:"bytes,9,rep,name=label,proto3" json:"label,omitempty"`
	Preset        bool     `protobuf:"varint,10,opt,name=preset,proto3" json:"preset,omitempty"`
	WorkspaceName string   `protobuf:"bytes,11,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
}

func (x *AddImagesRequest) Reset() {
	*x = AddImagesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddImagesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddImagesRequest) ProtoMessage() {}

func (x *AddImagesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddImagesRequest.ProtoReflect.Descriptor instead.
func (*AddImagesRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_imagehub_v1_imagehub_proto_rawDescGZIP(), []int{5}
}

func (x *AddImagesRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *AddImagesRequest) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *AddImagesRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *AddImagesRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *AddImagesRequest) GetTags() []string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *AddImagesRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *AddImagesRequest) GetPurposes() []string {
	if x != nil {
		return x.Purposes
	}
	return nil
}

func (x *AddImagesRequest) GetLabel() []string {
	if x != nil {
		return x.Label
	}
	return nil
}

func (x *AddImagesRequest) GetPreset() bool {
	if x != nil {
		return x.Preset
	}
	return false
}

func (x *AddImagesRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

type UpdateImageRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id            string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Namespace     string   `protobuf:"bytes,2,opt,name=namespace,proto3" json:"namespace,omitempty"`
	Name          string   `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Domain        string   `protobuf:"bytes,4,opt,name=domain,proto3" json:"domain,omitempty"`
	Tags          []string `protobuf:"bytes,5,rep,name=tags,proto3" json:"tags,omitempty"`
	Description   string   `protobuf:"bytes,6,opt,name=description,proto3" json:"description,omitempty"`
	Purposes      []string `protobuf:"bytes,8,rep,name=purposes,proto3" json:"purposes,omitempty"`
	Label         []string `protobuf:"bytes,9,rep,name=label,proto3" json:"label,omitempty"`
	Preset        bool     `protobuf:"varint,10,opt,name=preset,proto3" json:"preset,omitempty"`
	WorkspaceName string   `protobuf:"bytes,11,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Region        string   `protobuf:"bytes,12,opt,name=region,proto3" json:"region,omitempty"`
}

func (x *UpdateImageRequest) Reset() {
	*x = UpdateImageRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateImageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateImageRequest) ProtoMessage() {}

func (x *UpdateImageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateImageRequest.ProtoReflect.Descriptor instead.
func (*UpdateImageRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_imagehub_v1_imagehub_proto_rawDescGZIP(), []int{6}
}

func (x *UpdateImageRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *UpdateImageRequest) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *UpdateImageRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UpdateImageRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *UpdateImageRequest) GetTags() []string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *UpdateImageRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *UpdateImageRequest) GetPurposes() []string {
	if x != nil {
		return x.Purposes
	}
	return nil
}

func (x *UpdateImageRequest) GetLabel() []string {
	if x != nil {
		return x.Label
	}
	return nil
}

func (x *UpdateImageRequest) GetPreset() bool {
	if x != nil {
		return x.Preset
	}
	return false
}

func (x *UpdateImageRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *UpdateImageRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

type ListImagesOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Region        string `protobuf:"bytes,1,opt,name=region,proto3" json:"region,omitempty"`
	Namespace     string `protobuf:"bytes,2,opt,name=namespace,proto3" json:"namespace,omitempty"`
	Name          string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Domain        string `protobuf:"bytes,4,opt,name=domain,proto3" json:"domain,omitempty"`
	WorkspaceName string `protobuf:"bytes,5,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Preset        bool   `protobuf:"varint,6,opt,name=preset,proto3" json:"preset,omitempty"`
	Page          int32  `protobuf:"varint,7,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      int32  `protobuf:"varint,8,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
	SpecificName  string `protobuf:"bytes,9,opt,name=specificName,proto3" json:"specificName,omitempty"`
	Tag           string `protobuf:"bytes,10,opt,name=tag,proto3" json:"tag,omitempty"`
	Creator       string `protobuf:"bytes,11,opt,name=creator,proto3" json:"creator,omitempty"`
}

func (x *ListImagesOptions) Reset() {
	*x = ListImagesOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListImagesOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListImagesOptions) ProtoMessage() {}

func (x *ListImagesOptions) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListImagesOptions.ProtoReflect.Descriptor instead.
func (*ListImagesOptions) Descriptor() ([]byte, []int) {
	return file_aistudio_imagehub_v1_imagehub_proto_rawDescGZIP(), []int{7}
}

func (x *ListImagesOptions) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *ListImagesOptions) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *ListImagesOptions) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ListImagesOptions) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *ListImagesOptions) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *ListImagesOptions) GetPreset() bool {
	if x != nil {
		return x.Preset
	}
	return false
}

func (x *ListImagesOptions) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListImagesOptions) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListImagesOptions) GetSpecificName() string {
	if x != nil {
		return x.SpecificName
	}
	return ""
}

func (x *ListImagesOptions) GetTag() string {
	if x != nil {
		return x.Tag
	}
	return ""
}

func (x *ListImagesOptions) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

type Image struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Namespace        string   `protobuf:"bytes,1,opt,name=namespace,proto3" json:"namespace,omitempty"`
	Name             string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Preset           bool     `protobuf:"varint,3,opt,name=preset,proto3" json:"preset,omitempty"`
	Domain           string   `protobuf:"bytes,4,opt,name=domain,proto3" json:"domain,omitempty"`
	Tag              string   `protobuf:"bytes,5,opt,name=tag,proto3" json:"tag,omitempty"`
	Description      string   `protobuf:"bytes,6,opt,name=description,proto3" json:"description,omitempty"`
	FullPath         string   `protobuf:"bytes,7,opt,name=fullPath,proto3" json:"fullPath,omitempty"`
	Purposes         []string `protobuf:"bytes,8,rep,name=purposes,proto3" json:"purposes,omitempty"`
	Label            []string `protobuf:"bytes,9,rep,name=label,proto3" json:"label,omitempty"`
	Size             string   `protobuf:"bytes,10,opt,name=size,proto3" json:"size,omitempty"`
	Architecture     string   `protobuf:"bytes,11,opt,name=architecture,proto3" json:"architecture,omitempty"`
	CreateTime       string   `protobuf:"bytes,12,opt,name=createTime,proto3" json:"createTime,omitempty"`
	UpdateTime       string   `protobuf:"bytes,13,opt,name=updateTime,proto3" json:"updateTime,omitempty"`
	Region           string   `protobuf:"bytes,14,opt,name=region,proto3" json:"region,omitempty"`
	Id               string   `protobuf:"bytes,15,opt,name=id,proto3" json:"id,omitempty"`
	FullName         string   `protobuf:"bytes,16,opt,name=fullName,proto3" json:"fullName,omitempty"`
	SshKey           string   `protobuf:"bytes,17,opt,name=sshKey,proto3" json:"sshKey,omitempty"`
	BuildType        string   `protobuf:"bytes,18,opt,name=buildType,proto3" json:"buildType,omitempty"`
	BaseImage        string   `protobuf:"bytes,19,opt,name=baseImage,proto3" json:"baseImage,omitempty"`
	DockerfileSource string   `protobuf:"bytes,20,opt,name=dockerfileSource,proto3" json:"dockerfileSource,omitempty"`
	PresetImage      string   `protobuf:"bytes,21,opt,name=presetImage,proto3" json:"presetImage,omitempty"`
	CustomImage      string   `protobuf:"bytes,22,opt,name=customImage,proto3" json:"customImage,omitempty"`
	ImageURL         string   `protobuf:"bytes,23,opt,name=imageURL,proto3" json:"imageURL,omitempty"`
	Run              string   `protobuf:"bytes,24,opt,name=run,proto3" json:"run,omitempty"`
	Cmd              string   `protobuf:"bytes,25,opt,name=cmd,proto3" json:"cmd,omitempty"`
	GitUrl           string   `protobuf:"bytes,26,opt,name=gitUrl,proto3" json:"gitUrl,omitempty"`
	Creator          string   `protobuf:"bytes,27,opt,name=creator,proto3" json:"creator,omitempty"`
	JobName          string   `protobuf:"bytes,28,opt,name=jobName,proto3" json:"jobName,omitempty"`
	Content          string   `protobuf:"bytes,29,opt,name=content,proto3" json:"content,omitempty"`
	DevID            string   `protobuf:"bytes,30,opt,name=devID,proto3" json:"devID,omitempty"`
	ExcludePath      string   `protobuf:"bytes,31,opt,name=excludePath,proto3" json:"excludePath,omitempty"`
	GitDir           string   `protobuf:"bytes,32,opt,name=gitDir,proto3" json:"gitDir,omitempty"`
	GitBranch        string   `protobuf:"bytes,33,opt,name=gitBranch,proto3" json:"gitBranch,omitempty"`
	BuildArg         string   `protobuf:"bytes,34,opt,name=buildArg,proto3" json:"buildArg,omitempty"`
	UseCache         bool     `protobuf:"varint,35,opt,name=useCache,proto3" json:"useCache,omitempty"`
	UseSpeedUp       bool     `protobuf:"varint,36,opt,name=useSpeedUp,proto3" json:"useSpeedUp,omitempty"`
}

func (x *Image) Reset() {
	*x = Image{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Image) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Image) ProtoMessage() {}

func (x *Image) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Image.ProtoReflect.Descriptor instead.
func (*Image) Descriptor() ([]byte, []int) {
	return file_aistudio_imagehub_v1_imagehub_proto_rawDescGZIP(), []int{8}
}

func (x *Image) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *Image) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Image) GetPreset() bool {
	if x != nil {
		return x.Preset
	}
	return false
}

func (x *Image) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *Image) GetTag() string {
	if x != nil {
		return x.Tag
	}
	return ""
}

func (x *Image) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Image) GetFullPath() string {
	if x != nil {
		return x.FullPath
	}
	return ""
}

func (x *Image) GetPurposes() []string {
	if x != nil {
		return x.Purposes
	}
	return nil
}

func (x *Image) GetLabel() []string {
	if x != nil {
		return x.Label
	}
	return nil
}

func (x *Image) GetSize() string {
	if x != nil {
		return x.Size
	}
	return ""
}

func (x *Image) GetArchitecture() string {
	if x != nil {
		return x.Architecture
	}
	return ""
}

func (x *Image) GetCreateTime() string {
	if x != nil {
		return x.CreateTime
	}
	return ""
}

func (x *Image) GetUpdateTime() string {
	if x != nil {
		return x.UpdateTime
	}
	return ""
}

func (x *Image) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *Image) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Image) GetFullName() string {
	if x != nil {
		return x.FullName
	}
	return ""
}

func (x *Image) GetSshKey() string {
	if x != nil {
		return x.SshKey
	}
	return ""
}

func (x *Image) GetBuildType() string {
	if x != nil {
		return x.BuildType
	}
	return ""
}

func (x *Image) GetBaseImage() string {
	if x != nil {
		return x.BaseImage
	}
	return ""
}

func (x *Image) GetDockerfileSource() string {
	if x != nil {
		return x.DockerfileSource
	}
	return ""
}

func (x *Image) GetPresetImage() string {
	if x != nil {
		return x.PresetImage
	}
	return ""
}

func (x *Image) GetCustomImage() string {
	if x != nil {
		return x.CustomImage
	}
	return ""
}

func (x *Image) GetImageURL() string {
	if x != nil {
		return x.ImageURL
	}
	return ""
}

func (x *Image) GetRun() string {
	if x != nil {
		return x.Run
	}
	return ""
}

func (x *Image) GetCmd() string {
	if x != nil {
		return x.Cmd
	}
	return ""
}

func (x *Image) GetGitUrl() string {
	if x != nil {
		return x.GitUrl
	}
	return ""
}

func (x *Image) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *Image) GetJobName() string {
	if x != nil {
		return x.JobName
	}
	return ""
}

func (x *Image) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *Image) GetDevID() string {
	if x != nil {
		return x.DevID
	}
	return ""
}

func (x *Image) GetExcludePath() string {
	if x != nil {
		return x.ExcludePath
	}
	return ""
}

func (x *Image) GetGitDir() string {
	if x != nil {
		return x.GitDir
	}
	return ""
}

func (x *Image) GetGitBranch() string {
	if x != nil {
		return x.GitBranch
	}
	return ""
}

func (x *Image) GetBuildArg() string {
	if x != nil {
		return x.BuildArg
	}
	return ""
}

func (x *Image) GetUseCache() bool {
	if x != nil {
		return x.UseCache
	}
	return false
}

func (x *Image) GetUseSpeedUp() bool {
	if x != nil {
		return x.UseSpeedUp
	}
	return false
}

type ListImagesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Images []*Image `protobuf:"bytes,1,rep,name=images,proto3" json:"images,omitempty"`
	Total  int32    `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
}

func (x *ListImagesResponse) Reset() {
	*x = ListImagesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListImagesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListImagesResponse) ProtoMessage() {}

func (x *ListImagesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListImagesResponse.ProtoReflect.Descriptor instead.
func (*ListImagesResponse) Descriptor() ([]byte, []int) {
	return file_aistudio_imagehub_v1_imagehub_proto_rawDescGZIP(), []int{9}
}

func (x *ListImagesResponse) GetImages() []*Image {
	if x != nil {
		return x.Images
	}
	return nil
}

func (x *ListImagesResponse) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

type CascadeImage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Label    string           `protobuf:"bytes,2,opt,name=label,proto3" json:"label,omitempty"`       // 显示的name，例如python, torch,tensorflow
	Children []*ImageChildren `protobuf:"bytes,3,rep,name=children,proto3" json:"children,omitempty"` // 子标签
}

func (x *CascadeImage) Reset() {
	*x = CascadeImage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CascadeImage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CascadeImage) ProtoMessage() {}

func (x *CascadeImage) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CascadeImage.ProtoReflect.Descriptor instead.
func (*CascadeImage) Descriptor() ([]byte, []int) {
	return file_aistudio_imagehub_v1_imagehub_proto_rawDescGZIP(), []int{10}
}

func (x *CascadeImage) GetLabel() string {
	if x != nil {
		return x.Label
	}
	return ""
}

func (x *CascadeImage) GetChildren() []*ImageChildren {
	if x != nil {
		return x.Children
	}
	return nil
}

type ImageChildren struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Label string `protobuf:"bytes,1,opt,name=label,proto3" json:"label,omitempty"`
	Value string `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *ImageChildren) Reset() {
	*x = ImageChildren{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ImageChildren) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImageChildren) ProtoMessage() {}

func (x *ImageChildren) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImageChildren.ProtoReflect.Descriptor instead.
func (*ImageChildren) Descriptor() ([]byte, []int) {
	return file_aistudio_imagehub_v1_imagehub_proto_rawDescGZIP(), []int{11}
}

func (x *ImageChildren) GetLabel() string {
	if x != nil {
		return x.Label
	}
	return ""
}

func (x *ImageChildren) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

type CascadeImages struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Images []*CascadeImage `protobuf:"bytes,1,rep,name=images,proto3" json:"images,omitempty"`
	Total  int32           `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
}

func (x *CascadeImages) Reset() {
	*x = CascadeImages{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CascadeImages) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CascadeImages) ProtoMessage() {}

func (x *CascadeImages) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CascadeImages.ProtoReflect.Descriptor instead.
func (*CascadeImages) Descriptor() ([]byte, []int) {
	return file_aistudio_imagehub_v1_imagehub_proto_rawDescGZIP(), []int{12}
}

func (x *CascadeImages) GetImages() []*CascadeImage {
	if x != nil {
		return x.Images
	}
	return nil
}

func (x *CascadeImages) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

type GetServiceAccountsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName string `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Region        string `protobuf:"bytes,2,opt,name=region,proto3" json:"region,omitempty"`
}

func (x *GetServiceAccountsRequest) Reset() {
	*x = GetServiceAccountsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetServiceAccountsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetServiceAccountsRequest) ProtoMessage() {}

func (x *GetServiceAccountsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetServiceAccountsRequest.ProtoReflect.Descriptor instead.
func (*GetServiceAccountsRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_imagehub_v1_imagehub_proto_rawDescGZIP(), []int{13}
}

func (x *GetServiceAccountsRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *GetServiceAccountsRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

type ServiceAccounts struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ServiceAccounts []*ServiceAccount `protobuf:"bytes,1,rep,name=serviceAccounts,proto3" json:"serviceAccounts,omitempty"`
}

func (x *ServiceAccounts) Reset() {
	*x = ServiceAccounts{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceAccounts) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceAccounts) ProtoMessage() {}

func (x *ServiceAccounts) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceAccounts.ProtoReflect.Descriptor instead.
func (*ServiceAccounts) Descriptor() ([]byte, []int) {
	return file_aistudio_imagehub_v1_imagehub_proto_rawDescGZIP(), []int{14}
}

func (x *ServiceAccounts) GetServiceAccounts() []*ServiceAccount {
	if x != nil {
		return x.ServiceAccounts
	}
	return nil
}

type ServiceAccount struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Timestamp          *common.TimestampModel `protobuf:"bytes,1,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	Name               string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Namespace          string                 `protobuf:"bytes,3,opt,name=namespace,proto3" json:"namespace,omitempty"`
	Description        string                 `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	ServiceAccountType string                 `protobuf:"bytes,5,opt,name=serviceAccountType,proto3" json:"serviceAccountType,omitempty"`
	Password           string                 `protobuf:"bytes,6,opt,name=password,proto3" json:"password,omitempty"`
	Region             string                 `protobuf:"bytes,7,opt,name=region,proto3" json:"region,omitempty"`
}

func (x *ServiceAccount) Reset() {
	*x = ServiceAccount{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceAccount) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceAccount) ProtoMessage() {}

func (x *ServiceAccount) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceAccount.ProtoReflect.Descriptor instead.
func (*ServiceAccount) Descriptor() ([]byte, []int) {
	return file_aistudio_imagehub_v1_imagehub_proto_rawDescGZIP(), []int{15}
}

func (x *ServiceAccount) GetTimestamp() *common.TimestampModel {
	if x != nil {
		return x.Timestamp
	}
	return nil
}

func (x *ServiceAccount) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ServiceAccount) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *ServiceAccount) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *ServiceAccount) GetServiceAccountType() string {
	if x != nil {
		return x.ServiceAccountType
	}
	return ""
}

func (x *ServiceAccount) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *ServiceAccount) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

type PreheatJob struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Timestamp       *common.TimestampModel `protobuf:"bytes,1,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	Id              string                 `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`
	WorkspaceName   string                 `protobuf:"bytes,3,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Region          string                 `protobuf:"bytes,4,opt,name=region,proto3" json:"region,omitempty"`
	Images          []string               `protobuf:"bytes,5,rep,name=images,proto3" json:"images,omitempty"`
	Queues          []string               `protobuf:"bytes,7,rep,name=queues,proto3" json:"queues,omitempty"`
	JobStatus       string                 `protobuf:"bytes,8,opt,name=jobStatus,proto3" json:"jobStatus,omitempty"`
	Rate            int32                  `protobuf:"varint,9,opt,name=rate,proto3" json:"rate,omitempty"` // 进度
	Creator         string                 `protobuf:"bytes,10,opt,name=creator,proto3" json:"creator,omitempty"`
	ImagePullPolicy ImagePullPolicy        `protobuf:"varint,11,opt,name=imagePullPolicy,proto3,enum=apis.aistudio.imagehub.v1.ImagePullPolicy" json:"imagePullPolicy,omitempty"`
	ImagePullJobs   []*ImagePullJob        `protobuf:"bytes,12,rep,name=imagePullJobs,proto3" json:"imagePullJobs,omitempty"`
	Desired         int32                  `protobuf:"varint,13,opt,name=desired,proto3" json:"desired,omitempty"`
	Failed          int32                  `protobuf:"varint,14,opt,name=failed,proto3" json:"failed,omitempty"`
	Succeeded       int32                  `protobuf:"varint,15,opt,name=succeeded,proto3" json:"succeeded,omitempty"`
	Active          int32                  `protobuf:"varint,16,opt,name=active,proto3" json:"active,omitempty"`
	FailedNodes     []string               `protobuf:"bytes,17,rep,name=failedNodes,proto3" json:"failedNodes,omitempty"`
}

func (x *PreheatJob) Reset() {
	*x = PreheatJob{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PreheatJob) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PreheatJob) ProtoMessage() {}

func (x *PreheatJob) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PreheatJob.ProtoReflect.Descriptor instead.
func (*PreheatJob) Descriptor() ([]byte, []int) {
	return file_aistudio_imagehub_v1_imagehub_proto_rawDescGZIP(), []int{16}
}

func (x *PreheatJob) GetTimestamp() *common.TimestampModel {
	if x != nil {
		return x.Timestamp
	}
	return nil
}

func (x *PreheatJob) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *PreheatJob) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *PreheatJob) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *PreheatJob) GetImages() []string {
	if x != nil {
		return x.Images
	}
	return nil
}

func (x *PreheatJob) GetQueues() []string {
	if x != nil {
		return x.Queues
	}
	return nil
}

func (x *PreheatJob) GetJobStatus() string {
	if x != nil {
		return x.JobStatus
	}
	return ""
}

func (x *PreheatJob) GetRate() int32 {
	if x != nil {
		return x.Rate
	}
	return 0
}

func (x *PreheatJob) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *PreheatJob) GetImagePullPolicy() ImagePullPolicy {
	if x != nil {
		return x.ImagePullPolicy
	}
	return ImagePullPolicy_Always
}

func (x *PreheatJob) GetImagePullJobs() []*ImagePullJob {
	if x != nil {
		return x.ImagePullJobs
	}
	return nil
}

func (x *PreheatJob) GetDesired() int32 {
	if x != nil {
		return x.Desired
	}
	return 0
}

func (x *PreheatJob) GetFailed() int32 {
	if x != nil {
		return x.Failed
	}
	return 0
}

func (x *PreheatJob) GetSucceeded() int32 {
	if x != nil {
		return x.Succeeded
	}
	return 0
}

func (x *PreheatJob) GetActive() int32 {
	if x != nil {
		return x.Active
	}
	return 0
}

func (x *PreheatJob) GetFailedNodes() []string {
	if x != nil {
		return x.FailedNodes
	}
	return nil
}

type ImagePullJob struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Image       string   `protobuf:"bytes,1,opt,name=image,proto3" json:"image,omitempty"`
	Status      string   `protobuf:"bytes,2,opt,name=status,proto3" json:"status,omitempty"`
	Rate        int32    `protobuf:"varint,3,opt,name=rate,proto3" json:"rate,omitempty"`
	Desired     int32    `protobuf:"varint,4,opt,name=desired,proto3" json:"desired,omitempty"`
	Failed      int32    `protobuf:"varint,5,opt,name=failed,proto3" json:"failed,omitempty"`
	Succeeded   int32    `protobuf:"varint,6,opt,name=succeeded,proto3" json:"succeeded,omitempty"`
	Active      int32    `protobuf:"varint,7,opt,name=active,proto3" json:"active,omitempty"`
	FailedNodes []string `protobuf:"bytes,8,rep,name=failedNodes,proto3" json:"failedNodes,omitempty"`
	Message     string   `protobuf:"bytes,9,opt,name=message,proto3" json:"message,omitempty"`
	Cluster     string   `protobuf:"bytes,10,opt,name=cluster,proto3" json:"cluster,omitempty"`
	Id          string   `protobuf:"bytes,11,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *ImagePullJob) Reset() {
	*x = ImagePullJob{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ImagePullJob) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImagePullJob) ProtoMessage() {}

func (x *ImagePullJob) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImagePullJob.ProtoReflect.Descriptor instead.
func (*ImagePullJob) Descriptor() ([]byte, []int) {
	return file_aistudio_imagehub_v1_imagehub_proto_rawDescGZIP(), []int{17}
}

func (x *ImagePullJob) GetImage() string {
	if x != nil {
		return x.Image
	}
	return ""
}

func (x *ImagePullJob) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *ImagePullJob) GetRate() int32 {
	if x != nil {
		return x.Rate
	}
	return 0
}

func (x *ImagePullJob) GetDesired() int32 {
	if x != nil {
		return x.Desired
	}
	return 0
}

func (x *ImagePullJob) GetFailed() int32 {
	if x != nil {
		return x.Failed
	}
	return 0
}

func (x *ImagePullJob) GetSucceeded() int32 {
	if x != nil {
		return x.Succeeded
	}
	return 0
}

func (x *ImagePullJob) GetActive() int32 {
	if x != nil {
		return x.Active
	}
	return 0
}

func (x *ImagePullJob) GetFailedNodes() []string {
	if x != nil {
		return x.FailedNodes
	}
	return nil
}

func (x *ImagePullJob) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ImagePullJob) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

func (x *ImagePullJob) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type PreheatJobs struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PreheatJobs []*PreheatJob `protobuf:"bytes,1,rep,name=preheatJobs,proto3" json:"preheatJobs,omitempty"`
}

func (x *PreheatJobs) Reset() {
	*x = PreheatJobs{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PreheatJobs) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PreheatJobs) ProtoMessage() {}

func (x *PreheatJobs) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PreheatJobs.ProtoReflect.Descriptor instead.
func (*PreheatJobs) Descriptor() ([]byte, []int) {
	return file_aistudio_imagehub_v1_imagehub_proto_rawDescGZIP(), []int{18}
}

func (x *PreheatJobs) GetPreheatJobs() []*PreheatJob {
	if x != nil {
		return x.PreheatJobs
	}
	return nil
}

type CreatePreheatJobRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName   string          `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Region          string          `protobuf:"bytes,2,opt,name=region,proto3" json:"region,omitempty"`
	Images          []string        `protobuf:"bytes,3,rep,name=images,proto3" json:"images,omitempty"`
	Queues          []string        `protobuf:"bytes,4,rep,name=queues,proto3" json:"queues,omitempty"`
	ImagePullPolicy ImagePullPolicy `protobuf:"varint,5,opt,name=imagePullPolicy,proto3,enum=apis.aistudio.imagehub.v1.ImagePullPolicy" json:"imagePullPolicy,omitempty"`
}

func (x *CreatePreheatJobRequest) Reset() {
	*x = CreatePreheatJobRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreatePreheatJobRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePreheatJobRequest) ProtoMessage() {}

func (x *CreatePreheatJobRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePreheatJobRequest.ProtoReflect.Descriptor instead.
func (*CreatePreheatJobRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_imagehub_v1_imagehub_proto_rawDescGZIP(), []int{19}
}

func (x *CreatePreheatJobRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *CreatePreheatJobRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *CreatePreheatJobRequest) GetImages() []string {
	if x != nil {
		return x.Images
	}
	return nil
}

func (x *CreatePreheatJobRequest) GetQueues() []string {
	if x != nil {
		return x.Queues
	}
	return nil
}

func (x *CreatePreheatJobRequest) GetImagePullPolicy() ImagePullPolicy {
	if x != nil {
		return x.ImagePullPolicy
	}
	return ImagePullPolicy_Always
}

type GetPreheatJobRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id            string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	WorkspaceName string `protobuf:"bytes,2,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Region        string `protobuf:"bytes,3,opt,name=region,proto3" json:"region,omitempty"`
}

func (x *GetPreheatJobRequest) Reset() {
	*x = GetPreheatJobRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPreheatJobRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPreheatJobRequest) ProtoMessage() {}

func (x *GetPreheatJobRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPreheatJobRequest.ProtoReflect.Descriptor instead.
func (*GetPreheatJobRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_imagehub_v1_imagehub_proto_rawDescGZIP(), []int{20}
}

func (x *GetPreheatJobRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *GetPreheatJobRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *GetPreheatJobRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

type ListPreheatJobOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName string `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Region        string `protobuf:"bytes,2,opt,name=region,proto3" json:"region,omitempty"`
	PageNo        int32  `protobuf:"varint,3,opt,name=pageNo,proto3" json:"pageNo,omitempty"`
	PageSize      int32  `protobuf:"varint,4,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
}

func (x *ListPreheatJobOptions) Reset() {
	*x = ListPreheatJobOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPreheatJobOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPreheatJobOptions) ProtoMessage() {}

func (x *ListPreheatJobOptions) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPreheatJobOptions.ProtoReflect.Descriptor instead.
func (*ListPreheatJobOptions) Descriptor() ([]byte, []int) {
	return file_aistudio_imagehub_v1_imagehub_proto_rawDescGZIP(), []int{21}
}

func (x *ListPreheatJobOptions) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *ListPreheatJobOptions) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *ListPreheatJobOptions) GetPageNo() int32 {
	if x != nil {
		return x.PageNo
	}
	return 0
}

func (x *ListPreheatJobOptions) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

type DeletePreheatJobRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *DeletePreheatJobRequest) Reset() {
	*x = DeletePreheatJobRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeletePreheatJobRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePreheatJobRequest) ProtoMessage() {}

func (x *DeletePreheatJobRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePreheatJobRequest.ProtoReflect.Descriptor instead.
func (*DeletePreheatJobRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_imagehub_v1_imagehub_proto_rawDescGZIP(), []int{22}
}

func (x *DeletePreheatJobRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type RedeployPreheatJobRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *RedeployPreheatJobRequest) Reset() {
	*x = RedeployPreheatJobRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RedeployPreheatJobRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RedeployPreheatJobRequest) ProtoMessage() {}

func (x *RedeployPreheatJobRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RedeployPreheatJobRequest.ProtoReflect.Descriptor instead.
func (*RedeployPreheatJobRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_imagehub_v1_imagehub_proto_rawDescGZIP(), []int{23}
}

func (x *RedeployPreheatJobRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type ListImageNamesOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Region        string `protobuf:"bytes,1,opt,name=region,proto3" json:"region,omitempty"`
	WorkspaceName string `protobuf:"bytes,2,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Name          string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *ListImageNamesOptions) Reset() {
	*x = ListImageNamesOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListImageNamesOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListImageNamesOptions) ProtoMessage() {}

func (x *ListImageNamesOptions) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListImageNamesOptions.ProtoReflect.Descriptor instead.
func (*ListImageNamesOptions) Descriptor() ([]byte, []int) {
	return file_aistudio_imagehub_v1_imagehub_proto_rawDescGZIP(), []int{24}
}

func (x *ListImageNamesOptions) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *ListImageNamesOptions) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *ListImageNamesOptions) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type ImageName struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *ImageName) Reset() {
	*x = ImageName{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ImageName) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImageName) ProtoMessage() {}

func (x *ImageName) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImageName.ProtoReflect.Descriptor instead.
func (*ImageName) Descriptor() ([]byte, []int) {
	return file_aistudio_imagehub_v1_imagehub_proto_rawDescGZIP(), []int{25}
}

func (x *ImageName) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type ListImageNamesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ImageNames []*ImageName `protobuf:"bytes,1,rep,name=imageNames,proto3" json:"imageNames,omitempty"`
}

func (x *ListImageNamesResponse) Reset() {
	*x = ListImageNamesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListImageNamesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListImageNamesResponse) ProtoMessage() {}

func (x *ListImageNamesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListImageNamesResponse.ProtoReflect.Descriptor instead.
func (*ListImageNamesResponse) Descriptor() ([]byte, []int) {
	return file_aistudio_imagehub_v1_imagehub_proto_rawDescGZIP(), []int{26}
}

func (x *ListImageNamesResponse) GetImageNames() []*ImageName {
	if x != nil {
		return x.ImageNames
	}
	return nil
}

type ListImageTagsOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Region        string `protobuf:"bytes,1,opt,name=region,proto3" json:"region,omitempty"`
	WorkspaceName string `protobuf:"bytes,2,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	ImageName     string `protobuf:"bytes,3,opt,name=imageName,proto3" json:"imageName,omitempty"`
}

func (x *ListImageTagsOptions) Reset() {
	*x = ListImageTagsOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListImageTagsOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListImageTagsOptions) ProtoMessage() {}

func (x *ListImageTagsOptions) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListImageTagsOptions.ProtoReflect.Descriptor instead.
func (*ListImageTagsOptions) Descriptor() ([]byte, []int) {
	return file_aistudio_imagehub_v1_imagehub_proto_rawDescGZIP(), []int{27}
}

func (x *ListImageTagsOptions) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *ListImageTagsOptions) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *ListImageTagsOptions) GetImageName() string {
	if x != nil {
		return x.ImageName
	}
	return ""
}

type ListImageTagsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ImageTags []*ImageTag `protobuf:"bytes,1,rep,name=imageTags,proto3" json:"imageTags,omitempty"`
}

func (x *ListImageTagsResponse) Reset() {
	*x = ListImageTagsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListImageTagsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListImageTagsResponse) ProtoMessage() {}

func (x *ListImageTagsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListImageTagsResponse.ProtoReflect.Descriptor instead.
func (*ListImageTagsResponse) Descriptor() ([]byte, []int) {
	return file_aistudio_imagehub_v1_imagehub_proto_rawDescGZIP(), []int{28}
}

func (x *ListImageTagsResponse) GetImageTags() []*ImageTag {
	if x != nil {
		return x.ImageTags
	}
	return nil
}

type ImageTag struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Tag  string `protobuf:"bytes,1,opt,name=tag,proto3" json:"tag,omitempty"`
	Path string `protobuf:"bytes,2,opt,name=path,proto3" json:"path,omitempty"`
}

func (x *ImageTag) Reset() {
	*x = ImageTag{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ImageTag) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImageTag) ProtoMessage() {}

func (x *ImageTag) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImageTag.ProtoReflect.Descriptor instead.
func (*ImageTag) Descriptor() ([]byte, []int) {
	return file_aistudio_imagehub_v1_imagehub_proto_rawDescGZIP(), []int{29}
}

func (x *ImageTag) GetTag() string {
	if x != nil {
		return x.Tag
	}
	return ""
}

func (x *ImageTag) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

type GetImageTagRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Region        string `protobuf:"bytes,1,opt,name=region,proto3" json:"region,omitempty"`
	WorkspaceName string `protobuf:"bytes,2,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	ImageName     string `protobuf:"bytes,3,opt,name=imageName,proto3" json:"imageName,omitempty"`
	Tag           string `protobuf:"bytes,4,opt,name=tag,proto3" json:"tag,omitempty"`
	Id            string `protobuf:"bytes,5,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *GetImageTagRequest) Reset() {
	*x = GetImageTagRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetImageTagRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetImageTagRequest) ProtoMessage() {}

func (x *GetImageTagRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetImageTagRequest.ProtoReflect.Descriptor instead.
func (*GetImageTagRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_imagehub_v1_imagehub_proto_rawDescGZIP(), []int{30}
}

func (x *GetImageTagRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *GetImageTagRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *GetImageTagRequest) GetImageName() string {
	if x != nil {
		return x.ImageName
	}
	return ""
}

func (x *GetImageTagRequest) GetTag() string {
	if x != nil {
		return x.Tag
	}
	return ""
}

func (x *GetImageTagRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type ModifyImageDescriptionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Description string `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
}

func (x *ModifyImageDescriptionRequest) Reset() {
	*x = ModifyImageDescriptionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModifyImageDescriptionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModifyImageDescriptionRequest) ProtoMessage() {}

func (x *ModifyImageDescriptionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModifyImageDescriptionRequest.ProtoReflect.Descriptor instead.
func (*ModifyImageDescriptionRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_imagehub_v1_imagehub_proto_rawDescGZIP(), []int{31}
}

func (x *ModifyImageDescriptionRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ModifyImageDescriptionRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

type BuildImageRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name             string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Tag              string `protobuf:"bytes,2,opt,name=tag,proto3" json:"tag,omitempty"`
	QueueName        string `protobuf:"bytes,3,opt,name=queueName,proto3" json:"queueName,omitempty"`
	Description      string `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	WorkspaceName    string `protobuf:"bytes,5,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Region           string `protobuf:"bytes,6,opt,name=region,proto3" json:"region,omitempty"`
	BuildType        string `protobuf:"bytes,7,opt,name=buildType,proto3" json:"buildType,omitempty"`
	BaseImage        string `protobuf:"bytes,8,opt,name=baseImage,proto3" json:"baseImage,omitempty"`
	DockerfileSource string `protobuf:"bytes,9,opt,name=dockerfileSource,proto3" json:"dockerfileSource,omitempty"`
	PresetImage      string `protobuf:"bytes,10,opt,name=presetImage,proto3" json:"presetImage,omitempty"`
	CustomImage      string `protobuf:"bytes,11,opt,name=customImage,proto3" json:"customImage,omitempty"`
	ImageURL         string `protobuf:"bytes,12,opt,name=imageURL,proto3" json:"imageURL,omitempty"`
	Run              string `protobuf:"bytes,13,opt,name=run,proto3" json:"run,omitempty"`
	Cmd              string `protobuf:"bytes,14,opt,name=cmd,proto3" json:"cmd,omitempty"`
	GitUrl           string `protobuf:"bytes,15,opt,name=gitUrl,proto3" json:"gitUrl,omitempty"`
	Creator          string `protobuf:"bytes,16,opt,name=creator,proto3" json:"creator,omitempty"`
	JobName          string `protobuf:"bytes,17,opt,name=jobName,proto3" json:"jobName,omitempty"`
	Content          string `protobuf:"bytes,18,opt,name=content,proto3" json:"content,omitempty"`
	DevID            string `protobuf:"bytes,19,opt,name=devID,proto3" json:"devID,omitempty"`
	ExcludePath      string `protobuf:"bytes,20,opt,name=excludePath,proto3" json:"excludePath,omitempty"`
	GitDir           string `protobuf:"bytes,21,opt,name=gitDir,proto3" json:"gitDir,omitempty"`
	GitBranch        string `protobuf:"bytes,22,opt,name=gitBranch,proto3" json:"gitBranch,omitempty"`
	BuildArg         string `protobuf:"bytes,23,opt,name=buildArg,proto3" json:"buildArg,omitempty"`
	SshKey           string `protobuf:"bytes,24,opt,name=sshKey,proto3" json:"sshKey,omitempty"`
	UseCache         bool   `protobuf:"varint,25,opt,name=useCache,proto3" json:"useCache,omitempty"`
	UseSpeedUp       bool   `protobuf:"varint,26,opt,name=useSpeedUp,proto3" json:"useSpeedUp,omitempty"`
	StopDev          bool   `protobuf:"varint,27,opt,name=stopDev,proto3" json:"stopDev,omitempty"`
}

func (x *BuildImageRequest) Reset() {
	*x = BuildImageRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BuildImageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BuildImageRequest) ProtoMessage() {}

func (x *BuildImageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BuildImageRequest.ProtoReflect.Descriptor instead.
func (*BuildImageRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_imagehub_v1_imagehub_proto_rawDescGZIP(), []int{32}
}

func (x *BuildImageRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *BuildImageRequest) GetTag() string {
	if x != nil {
		return x.Tag
	}
	return ""
}

func (x *BuildImageRequest) GetQueueName() string {
	if x != nil {
		return x.QueueName
	}
	return ""
}

func (x *BuildImageRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *BuildImageRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *BuildImageRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *BuildImageRequest) GetBuildType() string {
	if x != nil {
		return x.BuildType
	}
	return ""
}

func (x *BuildImageRequest) GetBaseImage() string {
	if x != nil {
		return x.BaseImage
	}
	return ""
}

func (x *BuildImageRequest) GetDockerfileSource() string {
	if x != nil {
		return x.DockerfileSource
	}
	return ""
}

func (x *BuildImageRequest) GetPresetImage() string {
	if x != nil {
		return x.PresetImage
	}
	return ""
}

func (x *BuildImageRequest) GetCustomImage() string {
	if x != nil {
		return x.CustomImage
	}
	return ""
}

func (x *BuildImageRequest) GetImageURL() string {
	if x != nil {
		return x.ImageURL
	}
	return ""
}

func (x *BuildImageRequest) GetRun() string {
	if x != nil {
		return x.Run
	}
	return ""
}

func (x *BuildImageRequest) GetCmd() string {
	if x != nil {
		return x.Cmd
	}
	return ""
}

func (x *BuildImageRequest) GetGitUrl() string {
	if x != nil {
		return x.GitUrl
	}
	return ""
}

func (x *BuildImageRequest) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *BuildImageRequest) GetJobName() string {
	if x != nil {
		return x.JobName
	}
	return ""
}

func (x *BuildImageRequest) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *BuildImageRequest) GetDevID() string {
	if x != nil {
		return x.DevID
	}
	return ""
}

func (x *BuildImageRequest) GetExcludePath() string {
	if x != nil {
		return x.ExcludePath
	}
	return ""
}

func (x *BuildImageRequest) GetGitDir() string {
	if x != nil {
		return x.GitDir
	}
	return ""
}

func (x *BuildImageRequest) GetGitBranch() string {
	if x != nil {
		return x.GitBranch
	}
	return ""
}

func (x *BuildImageRequest) GetBuildArg() string {
	if x != nil {
		return x.BuildArg
	}
	return ""
}

func (x *BuildImageRequest) GetSshKey() string {
	if x != nil {
		return x.SshKey
	}
	return ""
}

func (x *BuildImageRequest) GetUseCache() bool {
	if x != nil {
		return x.UseCache
	}
	return false
}

func (x *BuildImageRequest) GetUseSpeedUp() bool {
	if x != nil {
		return x.UseSpeedUp
	}
	return false
}

func (x *BuildImageRequest) GetStopDev() bool {
	if x != nil {
		return x.StopDev
	}
	return false
}

type BuildImageResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Msg    string          `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg,omitempty"`
	Status int32           `protobuf:"varint,2,opt,name=status,proto3" json:"status,omitempty"`
	Data   *BuildImageData `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *BuildImageResponse) Reset() {
	*x = BuildImageResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BuildImageResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BuildImageResponse) ProtoMessage() {}

func (x *BuildImageResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BuildImageResponse.ProtoReflect.Descriptor instead.
func (*BuildImageResponse) Descriptor() ([]byte, []int) {
	return file_aistudio_imagehub_v1_imagehub_proto_rawDescGZIP(), []int{33}
}

func (x *BuildImageResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *BuildImageResponse) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *BuildImageResponse) GetData() *BuildImageData {
	if x != nil {
		return x.Data
	}
	return nil
}

type BuildImageData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	JobName string `protobuf:"bytes,3,opt,name=jobName,proto3" json:"jobName,omitempty"`
	Tag     string `protobuf:"bytes,4,opt,name=tag,proto3" json:"tag,omitempty"`
}

func (x *BuildImageData) Reset() {
	*x = BuildImageData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BuildImageData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BuildImageData) ProtoMessage() {}

func (x *BuildImageData) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BuildImageData.ProtoReflect.Descriptor instead.
func (*BuildImageData) Descriptor() ([]byte, []int) {
	return file_aistudio_imagehub_v1_imagehub_proto_rawDescGZIP(), []int{34}
}

func (x *BuildImageData) GetJobName() string {
	if x != nil {
		return x.JobName
	}
	return ""
}

func (x *BuildImageData) GetTag() string {
	if x != nil {
		return x.Tag
	}
	return ""
}

type CreateImageBaseRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName string   `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Region        string   `protobuf:"bytes,2,opt,name=region,proto3" json:"region,omitempty"`
	Name          string   `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Description   string   `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	Managers      []string `protobuf:"bytes,5,rep,name=managers,proto3" json:"managers,omitempty"`
	Members       []string `protobuf:"bytes,6,rep,name=members,proto3" json:"members,omitempty"`
}

func (x *CreateImageBaseRequest) Reset() {
	*x = CreateImageBaseRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateImageBaseRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateImageBaseRequest) ProtoMessage() {}

func (x *CreateImageBaseRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateImageBaseRequest.ProtoReflect.Descriptor instead.
func (*CreateImageBaseRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_imagehub_v1_imagehub_proto_rawDescGZIP(), []int{35}
}

func (x *CreateImageBaseRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *CreateImageBaseRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *CreateImageBaseRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateImageBaseRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CreateImageBaseRequest) GetManagers() []string {
	if x != nil {
		return x.Managers
	}
	return nil
}

func (x *CreateImageBaseRequest) GetMembers() []string {
	if x != nil {
		return x.Members
	}
	return nil
}

type CreateImageBaseResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Msg    string `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg,omitempty"`
	Status int32  `protobuf:"varint,2,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *CreateImageBaseResponse) Reset() {
	*x = CreateImageBaseResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateImageBaseResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateImageBaseResponse) ProtoMessage() {}

func (x *CreateImageBaseResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateImageBaseResponse.ProtoReflect.Descriptor instead.
func (*CreateImageBaseResponse) Descriptor() ([]byte, []int) {
	return file_aistudio_imagehub_v1_imagehub_proto_rawDescGZIP(), []int{36}
}

func (x *CreateImageBaseResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *CreateImageBaseResponse) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

type UpdateImageBaseRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ID            string   `protobuf:"bytes,1,opt,name=ID,proto3" json:"ID,omitempty"`
	WorkspaceName string   `protobuf:"bytes,2,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Region        string   `protobuf:"bytes,3,opt,name=region,proto3" json:"region,omitempty"`
	Name          string   `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	Description   string   `protobuf:"bytes,5,opt,name=description,proto3" json:"description,omitempty"`
	Managers      []string `protobuf:"bytes,6,rep,name=managers,proto3" json:"managers,omitempty"`
	Members       []string `protobuf:"bytes,7,rep,name=members,proto3" json:"members,omitempty"`
}

func (x *UpdateImageBaseRequest) Reset() {
	*x = UpdateImageBaseRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateImageBaseRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateImageBaseRequest) ProtoMessage() {}

func (x *UpdateImageBaseRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateImageBaseRequest.ProtoReflect.Descriptor instead.
func (*UpdateImageBaseRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_imagehub_v1_imagehub_proto_rawDescGZIP(), []int{37}
}

func (x *UpdateImageBaseRequest) GetID() string {
	if x != nil {
		return x.ID
	}
	return ""
}

func (x *UpdateImageBaseRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *UpdateImageBaseRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *UpdateImageBaseRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UpdateImageBaseRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *UpdateImageBaseRequest) GetManagers() []string {
	if x != nil {
		return x.Managers
	}
	return nil
}

func (x *UpdateImageBaseRequest) GetMembers() []string {
	if x != nil {
		return x.Members
	}
	return nil
}

type UpdateImageBaseResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdateImageBaseResponse) Reset() {
	*x = UpdateImageBaseResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateImageBaseResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateImageBaseResponse) ProtoMessage() {}

func (x *UpdateImageBaseResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateImageBaseResponse.ProtoReflect.Descriptor instead.
func (*UpdateImageBaseResponse) Descriptor() ([]byte, []int) {
	return file_aistudio_imagehub_v1_imagehub_proto_rawDescGZIP(), []int{38}
}

type DeleteImageBaseRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ID string `protobuf:"bytes,1,opt,name=ID,proto3" json:"ID,omitempty"`
}

func (x *DeleteImageBaseRequest) Reset() {
	*x = DeleteImageBaseRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteImageBaseRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteImageBaseRequest) ProtoMessage() {}

func (x *DeleteImageBaseRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteImageBaseRequest.ProtoReflect.Descriptor instead.
func (*DeleteImageBaseRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_imagehub_v1_imagehub_proto_rawDescGZIP(), []int{39}
}

func (x *DeleteImageBaseRequest) GetID() string {
	if x != nil {
		return x.ID
	}
	return ""
}

type DeleteImageBaseResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Msg    string `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg,omitempty"`
	Status int32  `protobuf:"varint,2,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *DeleteImageBaseResponse) Reset() {
	*x = DeleteImageBaseResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteImageBaseResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteImageBaseResponse) ProtoMessage() {}

func (x *DeleteImageBaseResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteImageBaseResponse.ProtoReflect.Descriptor instead.
func (*DeleteImageBaseResponse) Descriptor() ([]byte, []int) {
	return file_aistudio_imagehub_v1_imagehub_proto_rawDescGZIP(), []int{40}
}

func (x *DeleteImageBaseResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *DeleteImageBaseResponse) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

type ListImageBaseRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName string   `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Region        string   `protobuf:"bytes,2,opt,name=region,proto3" json:"region,omitempty"`
	Name          string   `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Description   string   `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	Managers      []string `protobuf:"bytes,5,rep,name=managers,proto3" json:"managers,omitempty"`
	Members       []string `protobuf:"bytes,6,rep,name=members,proto3" json:"members,omitempty"`
	Page          int32    `protobuf:"varint,7,opt,name=page,proto3" json:"page,omitempty"`
	PerPage       int32    `protobuf:"varint,8,opt,name=perPage,proto3" json:"perPage,omitempty"`
	SpecificName  string   `protobuf:"bytes,9,opt,name=specificName,proto3" json:"specificName,omitempty"`
	Creator       string   `protobuf:"bytes,10,opt,name=creator,proto3" json:"creator,omitempty"`
}

func (x *ListImageBaseRequest) Reset() {
	*x = ListImageBaseRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListImageBaseRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListImageBaseRequest) ProtoMessage() {}

func (x *ListImageBaseRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListImageBaseRequest.ProtoReflect.Descriptor instead.
func (*ListImageBaseRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_imagehub_v1_imagehub_proto_rawDescGZIP(), []int{41}
}

func (x *ListImageBaseRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *ListImageBaseRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *ListImageBaseRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ListImageBaseRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *ListImageBaseRequest) GetManagers() []string {
	if x != nil {
		return x.Managers
	}
	return nil
}

func (x *ListImageBaseRequest) GetMembers() []string {
	if x != nil {
		return x.Members
	}
	return nil
}

func (x *ListImageBaseRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListImageBaseRequest) GetPerPage() int32 {
	if x != nil {
		return x.PerPage
	}
	return 0
}

func (x *ListImageBaseRequest) GetSpecificName() string {
	if x != nil {
		return x.SpecificName
	}
	return ""
}

func (x *ListImageBaseRequest) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

type ListImageBaseResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Images []*ImageBase `protobuf:"bytes,1,rep,name=images,proto3" json:"images,omitempty"`
	Total  int32        `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
}

func (x *ListImageBaseResponse) Reset() {
	*x = ListImageBaseResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListImageBaseResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListImageBaseResponse) ProtoMessage() {}

func (x *ListImageBaseResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListImageBaseResponse.ProtoReflect.Descriptor instead.
func (*ListImageBaseResponse) Descriptor() ([]byte, []int) {
	return file_aistudio_imagehub_v1_imagehub_proto_rawDescGZIP(), []int{42}
}

func (x *ListImageBaseResponse) GetImages() []*ImageBase {
	if x != nil {
		return x.Images
	}
	return nil
}

func (x *ListImageBaseResponse) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

type ImageBase struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ID            string   `protobuf:"bytes,1,opt,name=ID,proto3" json:"ID,omitempty"`
	WorkspaceName string   `protobuf:"bytes,2,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Region        string   `protobuf:"bytes,3,opt,name=region,proto3" json:"region,omitempty"`
	Name          string   `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	Description   string   `protobuf:"bytes,5,opt,name=description,proto3" json:"description,omitempty"`
	Managers      []string `protobuf:"bytes,6,rep,name=managers,proto3" json:"managers,omitempty"`
	Members       []string `protobuf:"bytes,7,rep,name=members,proto3" json:"members,omitempty"`
	Creator       string   `protobuf:"bytes,8,opt,name=creator,proto3" json:"creator,omitempty"`
	CreateTime    string   `protobuf:"bytes,9,opt,name=createTime,proto3" json:"createTime,omitempty"`
}

func (x *ImageBase) Reset() {
	*x = ImageBase{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ImageBase) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImageBase) ProtoMessage() {}

func (x *ImageBase) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImageBase.ProtoReflect.Descriptor instead.
func (*ImageBase) Descriptor() ([]byte, []int) {
	return file_aistudio_imagehub_v1_imagehub_proto_rawDescGZIP(), []int{43}
}

func (x *ImageBase) GetID() string {
	if x != nil {
		return x.ID
	}
	return ""
}

func (x *ImageBase) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *ImageBase) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *ImageBase) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ImageBase) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *ImageBase) GetManagers() []string {
	if x != nil {
		return x.Managers
	}
	return nil
}

func (x *ImageBase) GetMembers() []string {
	if x != nil {
		return x.Members
	}
	return nil
}

func (x *ImageBase) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *ImageBase) GetCreateTime() string {
	if x != nil {
		return x.CreateTime
	}
	return ""
}

type ListImageBuildHistoryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName string `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Region        string `protobuf:"bytes,2,opt,name=region,proto3" json:"region,omitempty"`
	Name          string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Tag           string `protobuf:"bytes,4,opt,name=tag,proto3" json:"tag,omitempty"`
	Page          int32  `protobuf:"varint,5,opt,name=page,proto3" json:"page,omitempty"`
	PerPage       int32  `protobuf:"varint,6,opt,name=perPage,proto3" json:"perPage,omitempty"`
	JobName       string `protobuf:"bytes,7,opt,name=jobName,proto3" json:"jobName,omitempty"`
	Creator       string `protobuf:"bytes,8,opt,name=creator,proto3" json:"creator,omitempty"`
	Status        string `protobuf:"bytes,9,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *ListImageBuildHistoryRequest) Reset() {
	*x = ListImageBuildHistoryRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListImageBuildHistoryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListImageBuildHistoryRequest) ProtoMessage() {}

func (x *ListImageBuildHistoryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListImageBuildHistoryRequest.ProtoReflect.Descriptor instead.
func (*ListImageBuildHistoryRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_imagehub_v1_imagehub_proto_rawDescGZIP(), []int{44}
}

func (x *ListImageBuildHistoryRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *ListImageBuildHistoryRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *ListImageBuildHistoryRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ListImageBuildHistoryRequest) GetTag() string {
	if x != nil {
		return x.Tag
	}
	return ""
}

func (x *ListImageBuildHistoryRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListImageBuildHistoryRequest) GetPerPage() int32 {
	if x != nil {
		return x.PerPage
	}
	return 0
}

func (x *ListImageBuildHistoryRequest) GetJobName() string {
	if x != nil {
		return x.JobName
	}
	return ""
}

func (x *ListImageBuildHistoryRequest) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *ListImageBuildHistoryRequest) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

type ListImageBuildHistoryResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ImageBuildHistories []*ImageBuildHistory `protobuf:"bytes,1,rep,name=imageBuildHistories,proto3" json:"imageBuildHistories,omitempty"`
	Total               int32                `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
}

func (x *ListImageBuildHistoryResponse) Reset() {
	*x = ListImageBuildHistoryResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListImageBuildHistoryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListImageBuildHistoryResponse) ProtoMessage() {}

func (x *ListImageBuildHistoryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListImageBuildHistoryResponse.ProtoReflect.Descriptor instead.
func (*ListImageBuildHistoryResponse) Descriptor() ([]byte, []int) {
	return file_aistudio_imagehub_v1_imagehub_proto_rawDescGZIP(), []int{45}
}

func (x *ListImageBuildHistoryResponse) GetImageBuildHistories() []*ImageBuildHistory {
	if x != nil {
		return x.ImageBuildHistories
	}
	return nil
}

func (x *ListImageBuildHistoryResponse) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

type ImageBuildHistory struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name             string            `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Tag              string            `protobuf:"bytes,2,opt,name=tag,proto3" json:"tag,omitempty"`
	QueueName        string            `protobuf:"bytes,3,opt,name=queueName,proto3" json:"queueName,omitempty"`
	Description      string            `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	WorkspaceName    string            `protobuf:"bytes,5,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Region           string            `protobuf:"bytes,6,opt,name=region,proto3" json:"region,omitempty"`
	BuildType        string            `protobuf:"bytes,7,opt,name=buildType,proto3" json:"buildType,omitempty"`
	BaseImage        string            `protobuf:"bytes,8,opt,name=baseImage,proto3" json:"baseImage,omitempty"`
	DockerfileSource string            `protobuf:"bytes,9,opt,name=dockerfileSource,proto3" json:"dockerfileSource,omitempty"`
	PresetImage      string            `protobuf:"bytes,10,opt,name=presetImage,proto3" json:"presetImage,omitempty"`
	CustomImage      string            `protobuf:"bytes,11,opt,name=customImage,proto3" json:"customImage,omitempty"`
	ImageURL         string            `protobuf:"bytes,12,opt,name=imageURL,proto3" json:"imageURL,omitempty"`
	Run              string            `protobuf:"bytes,13,opt,name=run,proto3" json:"run,omitempty"`
	Cmd              string            `protobuf:"bytes,14,opt,name=cmd,proto3" json:"cmd,omitempty"`
	GitUrl           string            `protobuf:"bytes,15,opt,name=gitUrl,proto3" json:"gitUrl,omitempty"`
	BuildTime        string            `protobuf:"bytes,16,opt,name=buildTime,proto3" json:"buildTime,omitempty"`
	FullPath         string            `protobuf:"bytes,17,opt,name=fullPath,proto3" json:"fullPath,omitempty"`
	Content          string            `protobuf:"bytes,18,opt,name=content,proto3" json:"content,omitempty"`
	DevID            string            `protobuf:"bytes,19,opt,name=devID,proto3" json:"devID,omitempty"`
	ExcludePath      string            `protobuf:"bytes,20,opt,name=excludePath,proto3" json:"excludePath,omitempty"`
	GitDir           string            `protobuf:"bytes,21,opt,name=gitDir,proto3" json:"gitDir,omitempty"`
	Status           string            `protobuf:"bytes,22,opt,name=status,proto3" json:"status,omitempty"`
	Reason           string            `protobuf:"bytes,23,opt,name=reason,proto3" json:"reason,omitempty"`
	Creator          string            `protobuf:"bytes,24,opt,name=creator,proto3" json:"creator,omitempty"`
	CreateTime       string            `protobuf:"bytes,25,opt,name=createTime,proto3" json:"createTime,omitempty"`
	JobName          string            `protobuf:"bytes,26,opt,name=jobName,proto3" json:"jobName,omitempty"`
	PodName          string            `protobuf:"bytes,27,opt,name=podName,proto3" json:"podName,omitempty"`
	StartTime        string            `protobuf:"bytes,28,opt,name=startTime,proto3" json:"startTime,omitempty"`
	EndTime          string            `protobuf:"bytes,29,opt,name=endTime,proto3" json:"endTime,omitempty"`
	Cluster          string            `protobuf:"bytes,30,opt,name=cluster,proto3" json:"cluster,omitempty"`
	LogFrom          int64             `protobuf:"varint,31,opt,name=logFrom,proto3" json:"logFrom,omitempty"`
	LogTo            int64             `protobuf:"varint,32,opt,name=logTo,proto3" json:"logTo,omitempty"`
	Msg              string            `protobuf:"bytes,33,opt,name=msg,proto3" json:"msg,omitempty"`
	Conditions       []*ImageCondition `protobuf:"bytes,34,rep,name=conditions,proto3" json:"conditions,omitempty"`
	GitBranch        string            `protobuf:"bytes,35,opt,name=gitBranch,proto3" json:"gitBranch,omitempty"`
	BuildArg         string            `protobuf:"bytes,36,opt,name=buildArg,proto3" json:"buildArg,omitempty"`
	SshKey           string            `protobuf:"bytes,37,opt,name=sshKey,proto3" json:"sshKey,omitempty"`
	UseCache         bool              `protobuf:"varint,38,opt,name=useCache,proto3" json:"useCache,omitempty"`
	UseSpeedUp       bool              `protobuf:"varint,39,opt,name=useSpeedUp,proto3" json:"useSpeedUp,omitempty"`
}

func (x *ImageBuildHistory) Reset() {
	*x = ImageBuildHistory{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ImageBuildHistory) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImageBuildHistory) ProtoMessage() {}

func (x *ImageBuildHistory) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImageBuildHistory.ProtoReflect.Descriptor instead.
func (*ImageBuildHistory) Descriptor() ([]byte, []int) {
	return file_aistudio_imagehub_v1_imagehub_proto_rawDescGZIP(), []int{46}
}

func (x *ImageBuildHistory) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ImageBuildHistory) GetTag() string {
	if x != nil {
		return x.Tag
	}
	return ""
}

func (x *ImageBuildHistory) GetQueueName() string {
	if x != nil {
		return x.QueueName
	}
	return ""
}

func (x *ImageBuildHistory) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *ImageBuildHistory) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *ImageBuildHistory) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *ImageBuildHistory) GetBuildType() string {
	if x != nil {
		return x.BuildType
	}
	return ""
}

func (x *ImageBuildHistory) GetBaseImage() string {
	if x != nil {
		return x.BaseImage
	}
	return ""
}

func (x *ImageBuildHistory) GetDockerfileSource() string {
	if x != nil {
		return x.DockerfileSource
	}
	return ""
}

func (x *ImageBuildHistory) GetPresetImage() string {
	if x != nil {
		return x.PresetImage
	}
	return ""
}

func (x *ImageBuildHistory) GetCustomImage() string {
	if x != nil {
		return x.CustomImage
	}
	return ""
}

func (x *ImageBuildHistory) GetImageURL() string {
	if x != nil {
		return x.ImageURL
	}
	return ""
}

func (x *ImageBuildHistory) GetRun() string {
	if x != nil {
		return x.Run
	}
	return ""
}

func (x *ImageBuildHistory) GetCmd() string {
	if x != nil {
		return x.Cmd
	}
	return ""
}

func (x *ImageBuildHistory) GetGitUrl() string {
	if x != nil {
		return x.GitUrl
	}
	return ""
}

func (x *ImageBuildHistory) GetBuildTime() string {
	if x != nil {
		return x.BuildTime
	}
	return ""
}

func (x *ImageBuildHistory) GetFullPath() string {
	if x != nil {
		return x.FullPath
	}
	return ""
}

func (x *ImageBuildHistory) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *ImageBuildHistory) GetDevID() string {
	if x != nil {
		return x.DevID
	}
	return ""
}

func (x *ImageBuildHistory) GetExcludePath() string {
	if x != nil {
		return x.ExcludePath
	}
	return ""
}

func (x *ImageBuildHistory) GetGitDir() string {
	if x != nil {
		return x.GitDir
	}
	return ""
}

func (x *ImageBuildHistory) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *ImageBuildHistory) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *ImageBuildHistory) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *ImageBuildHistory) GetCreateTime() string {
	if x != nil {
		return x.CreateTime
	}
	return ""
}

func (x *ImageBuildHistory) GetJobName() string {
	if x != nil {
		return x.JobName
	}
	return ""
}

func (x *ImageBuildHistory) GetPodName() string {
	if x != nil {
		return x.PodName
	}
	return ""
}

func (x *ImageBuildHistory) GetStartTime() string {
	if x != nil {
		return x.StartTime
	}
	return ""
}

func (x *ImageBuildHistory) GetEndTime() string {
	if x != nil {
		return x.EndTime
	}
	return ""
}

func (x *ImageBuildHistory) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

func (x *ImageBuildHistory) GetLogFrom() int64 {
	if x != nil {
		return x.LogFrom
	}
	return 0
}

func (x *ImageBuildHistory) GetLogTo() int64 {
	if x != nil {
		return x.LogTo
	}
	return 0
}

func (x *ImageBuildHistory) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *ImageBuildHistory) GetConditions() []*ImageCondition {
	if x != nil {
		return x.Conditions
	}
	return nil
}

func (x *ImageBuildHistory) GetGitBranch() string {
	if x != nil {
		return x.GitBranch
	}
	return ""
}

func (x *ImageBuildHistory) GetBuildArg() string {
	if x != nil {
		return x.BuildArg
	}
	return ""
}

func (x *ImageBuildHistory) GetSshKey() string {
	if x != nil {
		return x.SshKey
	}
	return ""
}

func (x *ImageBuildHistory) GetUseCache() bool {
	if x != nil {
		return x.UseCache
	}
	return false
}

func (x *ImageBuildHistory) GetUseSpeedUp() bool {
	if x != nil {
		return x.UseSpeedUp
	}
	return false
}

type ImageCondition struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type               string `protobuf:"bytes,1,opt,name=type,proto3" json:"type,omitempty"`
	Status             string `protobuf:"bytes,2,opt,name=status,proto3" json:"status,omitempty"`
	LastTransitionTime string `protobuf:"bytes,3,opt,name=lastTransitionTime,proto3" json:"lastTransitionTime,omitempty"` //最后一次状态变更时间
	Reason             string `protobuf:"bytes,4,opt,name=reason,proto3" json:"reason,omitempty"`                         //the reason of the job
	Message            string `protobuf:"bytes,5,opt,name=message,proto3" json:"message,omitempty"`                       //the message of the job
}

func (x *ImageCondition) Reset() {
	*x = ImageCondition{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ImageCondition) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImageCondition) ProtoMessage() {}

func (x *ImageCondition) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImageCondition.ProtoReflect.Descriptor instead.
func (*ImageCondition) Descriptor() ([]byte, []int) {
	return file_aistudio_imagehub_v1_imagehub_proto_rawDescGZIP(), []int{47}
}

func (x *ImageCondition) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *ImageCondition) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *ImageCondition) GetLastTransitionTime() string {
	if x != nil {
		return x.LastTransitionTime
	}
	return ""
}

func (x *ImageCondition) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *ImageCondition) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type StopBuildImageRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	JobName       string `protobuf:"bytes,1,opt,name=jobName,proto3" json:"jobName,omitempty"`
	WorkspaceName string `protobuf:"bytes,2,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Region        string `protobuf:"bytes,3,opt,name=region,proto3" json:"region,omitempty"`
}

func (x *StopBuildImageRequest) Reset() {
	*x = StopBuildImageRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StopBuildImageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StopBuildImageRequest) ProtoMessage() {}

func (x *StopBuildImageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StopBuildImageRequest.ProtoReflect.Descriptor instead.
func (*StopBuildImageRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_imagehub_v1_imagehub_proto_rawDescGZIP(), []int{48}
}

func (x *StopBuildImageRequest) GetJobName() string {
	if x != nil {
		return x.JobName
	}
	return ""
}

func (x *StopBuildImageRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *StopBuildImageRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

type StopBuildImageResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Msg    string `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg,omitempty"`
	Status int32  `protobuf:"varint,2,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *StopBuildImageResponse) Reset() {
	*x = StopBuildImageResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StopBuildImageResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StopBuildImageResponse) ProtoMessage() {}

func (x *StopBuildImageResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StopBuildImageResponse.ProtoReflect.Descriptor instead.
func (*StopBuildImageResponse) Descriptor() ([]byte, []int) {
	return file_aistudio_imagehub_v1_imagehub_proto_rawDescGZIP(), []int{49}
}

func (x *StopBuildImageResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *StopBuildImageResponse) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

type CheckImageTagRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Region        string `protobuf:"bytes,1,opt,name=region,proto3" json:"region,omitempty"`
	WorkspaceName string `protobuf:"bytes,2,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Name          string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Tag           string `protobuf:"bytes,4,opt,name=tag,proto3" json:"tag,omitempty"`
}

func (x *CheckImageTagRequest) Reset() {
	*x = CheckImageTagRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckImageTagRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckImageTagRequest) ProtoMessage() {}

func (x *CheckImageTagRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckImageTagRequest.ProtoReflect.Descriptor instead.
func (*CheckImageTagRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_imagehub_v1_imagehub_proto_rawDescGZIP(), []int{50}
}

func (x *CheckImageTagRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *CheckImageTagRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *CheckImageTagRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CheckImageTagRequest) GetTag() string {
	if x != nil {
		return x.Tag
	}
	return ""
}

type CheckImageTagResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` // string msg = 2;
}

func (x *CheckImageTagResponse) Reset() {
	*x = CheckImageTagResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckImageTagResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckImageTagResponse) ProtoMessage() {}

func (x *CheckImageTagResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_imagehub_v1_imagehub_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckImageTagResponse.ProtoReflect.Descriptor instead.
func (*CheckImageTagResponse) Descriptor() ([]byte, []int) {
	return file_aistudio_imagehub_v1_imagehub_proto_rawDescGZIP(), []int{51}
}

func (x *CheckImageTagResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

var File_aistudio_imagehub_v1_imagehub_proto protoreflect.FileDescriptor

var file_aistudio_imagehub_v1_imagehub_proto_rawDesc = []byte{
	0x0a, 0x23, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2f, 0x69, 0x6d, 0x61, 0x67, 0x65,
	0x68, 0x75, 0x62, 0x2f, 0x76, 0x31, 0x2f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x68, 0x75, 0x62, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x19, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74,
	0x75, 0x64, 0x69, 0x6f, 0x2e, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31,
	0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e,
	0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f,
	0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f,
	0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x13, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65,
	0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x5a, 0x0a, 0x1a, 0x43, 0x68,
	0x65, 0x63, 0x6b, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x48, 0x75, 0x62, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16,
	0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x22, 0x59, 0x0a, 0x1b, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49,
	0x6d, 0x61, 0x67, 0x65, 0x48, 0x75, 0x62, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x61, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74,
	0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x61, 0x63, 0x74, 0x69, 0x76, 0x61,
	0x74, 0x65, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x65,
	0x64, 0x22, 0x53, 0x0a, 0x13, 0x4f, 0x70, 0x65, 0x6e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x48, 0x75,
	0x62, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16,
	0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x22, 0x54, 0x0a, 0x14, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x49,
	0x6d, 0x61, 0x67, 0x65, 0x48, 0x75, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24,
	0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x22, 0x24, 0x0a, 0x12,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02,
	0x69, 0x64, 0x22, 0x9a, 0x02, 0x0a, 0x10, 0x41, 0x64, 0x64, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f,
	0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12,
	0x1c, 0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x61, 0x67,
	0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x12, 0x20, 0x0a,
	0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x1a, 0x0a, 0x08, 0x70, 0x75, 0x72, 0x70, 0x6f, 0x73, 0x65, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x08, 0x70, 0x75, 0x72, 0x70, 0x6f, 0x73, 0x65, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x6c,
	0x61, 0x62, 0x65, 0x6c, 0x18, 0x09, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x6c, 0x61, 0x62, 0x65,
	0x6c, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x72, 0x65, 0x73, 0x65, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x06, 0x70, 0x72, 0x65, 0x73, 0x65, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72,
	0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x22,
	0xac, 0x02, 0x0a, 0x12, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61,
	0x69, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e,
	0x12, 0x12, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04,
	0x74, 0x61, 0x67, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x75, 0x72, 0x70, 0x6f, 0x73,
	0x65, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x70, 0x75, 0x72, 0x70, 0x6f, 0x73,
	0x65, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x18, 0x09, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x72, 0x65, 0x73,
	0x65, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x70, 0x72, 0x65, 0x73, 0x65, 0x74,
	0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x22, 0xb3,
	0x02, 0x0a, 0x11, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x4f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x1c, 0x0a, 0x09,
	0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16,
	0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77,
	0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06,
	0x70, 0x72, 0x65, 0x73, 0x65, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x70, 0x72,
	0x65, 0x73, 0x65, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65,
	0x53, 0x69, 0x7a, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65,
	0x53, 0x69, 0x7a, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x73, 0x70, 0x65, 0x63,
	0x69, 0x66, 0x69, 0x63, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x74, 0x61, 0x67, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x74, 0x61, 0x67, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x6f, 0x72, 0x22, 0xd7, 0x07, 0x0a, 0x05, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x1c,
	0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x16, 0x0a, 0x06, 0x70, 0x72, 0x65, 0x73, 0x65, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x06, 0x70, 0x72, 0x65, 0x73, 0x65, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61,
	0x69, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e,
	0x12, 0x10, 0x0a, 0x03, 0x74, 0x61, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x74,
	0x61, 0x67, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x75, 0x6c, 0x6c, 0x50, 0x61, 0x74, 0x68,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x75, 0x6c, 0x6c, 0x50, 0x61, 0x74, 0x68,
	0x12, 0x1a, 0x0a, 0x08, 0x70, 0x75, 0x72, 0x70, 0x6f, 0x73, 0x65, 0x73, 0x18, 0x08, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x08, 0x70, 0x75, 0x72, 0x70, 0x6f, 0x73, 0x65, 0x73, 0x12, 0x14, 0x0a, 0x05,
	0x6c, 0x61, 0x62, 0x65, 0x6c, 0x18, 0x09, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x6c, 0x61, 0x62,
	0x65, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x61, 0x72, 0x63, 0x68, 0x69, 0x74,
	0x65, 0x63, 0x74, 0x75, 0x72, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x72,
	0x63, 0x68, 0x69, 0x74, 0x65, 0x63, 0x74, 0x75, 0x72, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65,
	0x67, 0x69, 0x6f, 0x6e, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69,
	0x6f, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x75, 0x6c, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x10,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x75, 0x6c, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16,
	0x0a, 0x06, 0x73, 0x73, 0x68, 0x4b, 0x65, 0x79, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x73, 0x73, 0x68, 0x4b, 0x65, 0x79, 0x12, 0x1c, 0x0a, 0x09, 0x62, 0x75, 0x69, 0x6c, 0x64, 0x54,
	0x79, 0x70, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x62, 0x75, 0x69, 0x6c, 0x64,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x62, 0x61, 0x73, 0x65, 0x49, 0x6d, 0x61, 0x67,
	0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x62, 0x61, 0x73, 0x65, 0x49, 0x6d, 0x61,
	0x67, 0x65, 0x12, 0x2a, 0x0a, 0x10, 0x64, 0x6f, 0x63, 0x6b, 0x65, 0x72, 0x66, 0x69, 0x6c, 0x65,
	0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x64, 0x6f,
	0x63, 0x6b, 0x65, 0x72, 0x66, 0x69, 0x6c, 0x65, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x20,
	0x0a, 0x0b, 0x70, 0x72, 0x65, 0x73, 0x65, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x15, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x72, 0x65, 0x73, 0x65, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65,
	0x12, 0x20, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x18,
	0x16, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x49, 0x6d, 0x61,
	0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x55, 0x52, 0x4c, 0x18, 0x17,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x55, 0x52, 0x4c, 0x12, 0x10,
	0x0a, 0x03, 0x72, 0x75, 0x6e, 0x18, 0x18, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x72, 0x75, 0x6e,
	0x12, 0x10, 0x0a, 0x03, 0x63, 0x6d, 0x64, 0x18, 0x19, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x63,
	0x6d, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x67, 0x69, 0x74, 0x55, 0x72, 0x6c, 0x18, 0x1a, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x67, 0x69, 0x74, 0x55, 0x72, 0x6c, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x6f, 0x72, 0x12, 0x18, 0x0a, 0x07, 0x6a, 0x6f, 0x62, 0x4e, 0x61, 0x6d, 0x65, 0x18,
	0x1c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6a, 0x6f, 0x62, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18,
	0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x64, 0x65, 0x76, 0x49,
	0x44, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x64, 0x65, 0x76, 0x49, 0x44, 0x12, 0x20,
	0x0a, 0x0b, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x50, 0x61, 0x74, 0x68, 0x18, 0x1f, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x50, 0x61, 0x74, 0x68,
	0x12, 0x16, 0x0a, 0x06, 0x67, 0x69, 0x74, 0x44, 0x69, 0x72, 0x18, 0x20, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x67, 0x69, 0x74, 0x44, 0x69, 0x72, 0x12, 0x1c, 0x0a, 0x09, 0x67, 0x69, 0x74, 0x42,
	0x72, 0x61, 0x6e, 0x63, 0x68, 0x18, 0x21, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x67, 0x69, 0x74,
	0x42, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x12, 0x1a, 0x0a, 0x08, 0x62, 0x75, 0x69, 0x6c, 0x64, 0x41,
	0x72, 0x67, 0x18, 0x22, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x62, 0x75, 0x69, 0x6c, 0x64, 0x41,
	0x72, 0x67, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x43, 0x61, 0x63, 0x68, 0x65, 0x18, 0x23,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x75, 0x73, 0x65, 0x43, 0x61, 0x63, 0x68, 0x65, 0x12, 0x1e,
	0x0a, 0x0a, 0x75, 0x73, 0x65, 0x53, 0x70, 0x65, 0x65, 0x64, 0x55, 0x70, 0x18, 0x24, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x0a, 0x75, 0x73, 0x65, 0x53, 0x70, 0x65, 0x65, 0x64, 0x55, 0x70, 0x22, 0x64,
	0x0a, 0x12, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x38, 0x0a, 0x06, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74,
	0x75, 0x64, 0x69, 0x6f, 0x2e, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31,
	0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x06, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x12, 0x14,
	0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x22, 0x6a, 0x0a, 0x0c, 0x43, 0x61, 0x73, 0x63, 0x61, 0x64, 0x65, 0x49,
	0x6d, 0x61, 0x67, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x12, 0x44, 0x0a, 0x08, 0x63, 0x68,
	0x69, 0x6c, 0x64, 0x72, 0x65, 0x6e, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x61,
	0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x69, 0x6d, 0x61,
	0x67, 0x65, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x43, 0x68,
	0x69, 0x6c, 0x64, 0x72, 0x65, 0x6e, 0x52, 0x08, 0x63, 0x68, 0x69, 0x6c, 0x64, 0x72, 0x65, 0x6e,
	0x22, 0x3b, 0x0a, 0x0d, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x43, 0x68, 0x69, 0x6c, 0x64, 0x72, 0x65,
	0x6e, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x66, 0x0a,
	0x0d, 0x43, 0x61, 0x73, 0x63, 0x61, 0x64, 0x65, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x12, 0x3f,
	0x0a, 0x06, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27,
	0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x69,
	0x6d, 0x61, 0x67, 0x65, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x73, 0x63, 0x61,
	0x64, 0x65, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x06, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x12,
	0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x22, 0x59, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69,
	0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e,
	0x22, 0x66, 0x0a, 0x0f, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x73, 0x12, 0x53, 0x0a, 0x0f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x61,
	0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x69, 0x6d, 0x61,
	0x67, 0x65, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x0f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x22, 0x83, 0x02, 0x0a, 0x0e, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x39, 0x0a, 0x09, 0x74,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b,
	0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x09, 0x74, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x6e, 0x61,
	0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6e,
	0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2e, 0x0a, 0x12, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61,
	0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x61,
	0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x22, 0xc0,
	0x04, 0x0a, 0x0a, 0x50, 0x72, 0x65, 0x68, 0x65, 0x61, 0x74, 0x4a, 0x6f, 0x62, 0x12, 0x39, 0x0a,
	0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x09, 0x74,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16,
	0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73,
	0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x12, 0x16,
	0x0a, 0x06, 0x71, 0x75, 0x65, 0x75, 0x65, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06,
	0x71, 0x75, 0x65, 0x75, 0x65, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x6a, 0x6f, 0x62, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6a, 0x6f, 0x62, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x61, 0x74, 0x65, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x04, 0x72, 0x61, 0x74, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x6f, 0x72, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x6f, 0x72, 0x12, 0x54, 0x0a, 0x0f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x50, 0x75, 0x6c, 0x6c, 0x50,
	0x6f, 0x6c, 0x69, 0x63, 0x79, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2a, 0x2e, 0x61, 0x70,
	0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x69, 0x6d, 0x61, 0x67,
	0x65, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x50, 0x75, 0x6c,
	0x6c, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52, 0x0f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x50, 0x75,
	0x6c, 0x6c, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12, 0x4d, 0x0a, 0x0d, 0x69, 0x6d, 0x61, 0x67,
	0x65, 0x50, 0x75, 0x6c, 0x6c, 0x4a, 0x6f, 0x62, 0x73, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x27, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e,
	0x69, 0x6d, 0x61, 0x67, 0x65, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6d, 0x61, 0x67,
	0x65, 0x50, 0x75, 0x6c, 0x6c, 0x4a, 0x6f, 0x62, 0x52, 0x0d, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x50,
	0x75, 0x6c, 0x6c, 0x4a, 0x6f, 0x62, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x64, 0x65, 0x73, 0x69, 0x72,
	0x65, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x64, 0x65, 0x73, 0x69, 0x72, 0x65,
	0x64, 0x12, 0x16, 0x0a, 0x06, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x18, 0x0e, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x06, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x75, 0x63,
	0x63, 0x65, 0x65, 0x64, 0x65, 0x64, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x73, 0x75,
	0x63, 0x63, 0x65, 0x65, 0x64, 0x65, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x12,
	0x20, 0x0a, 0x0b, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x4e, 0x6f, 0x64, 0x65, 0x73, 0x18, 0x11,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x4e, 0x6f, 0x64, 0x65,
	0x73, 0x22, 0x9e, 0x02, 0x0a, 0x0c, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x50, 0x75, 0x6c, 0x6c, 0x4a,
	0x6f, 0x62, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x12, 0x0a, 0x04, 0x72, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04,
	0x72, 0x61, 0x74, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x64, 0x65, 0x73, 0x69, 0x72, 0x65, 0x64, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x64, 0x65, 0x73, 0x69, 0x72, 0x65, 0x64, 0x12, 0x16,
	0x0a, 0x06, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06,
	0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x75, 0x63, 0x63, 0x65, 0x65,
	0x64, 0x65, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x73, 0x75, 0x63, 0x63, 0x65,
	0x65, 0x64, 0x65, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x12, 0x20, 0x0a, 0x0b,
	0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x4e, 0x6f, 0x64, 0x65, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x0b, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x4e, 0x6f, 0x64, 0x65, 0x73, 0x12, 0x18,
	0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6c, 0x75, 0x73,
	0x74, 0x65, 0x72, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74,
	0x65, 0x72, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02,
	0x69, 0x64, 0x22, 0x56, 0x0a, 0x0b, 0x50, 0x72, 0x65, 0x68, 0x65, 0x61, 0x74, 0x4a, 0x6f, 0x62,
	0x73, 0x12, 0x47, 0x0a, 0x0b, 0x70, 0x72, 0x65, 0x68, 0x65, 0x61, 0x74, 0x4a, 0x6f, 0x62, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69,
	0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x68, 0x75, 0x62, 0x2e,
	0x76, 0x31, 0x2e, 0x50, 0x72, 0x65, 0x68, 0x65, 0x61, 0x74, 0x4a, 0x6f, 0x62, 0x52, 0x0b, 0x70,
	0x72, 0x65, 0x68, 0x65, 0x61, 0x74, 0x4a, 0x6f, 0x62, 0x73, 0x22, 0xdd, 0x01, 0x0a, 0x17, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x72, 0x65, 0x68, 0x65, 0x61, 0x74, 0x4a, 0x6f, 0x62, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77,
	0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06,
	0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65,
	0x67, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x12, 0x16, 0x0a, 0x06,
	0x71, 0x75, 0x65, 0x75, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x71, 0x75,
	0x65, 0x75, 0x65, 0x73, 0x12, 0x54, 0x0a, 0x0f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x50, 0x75, 0x6c,
	0x6c, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2a, 0x2e,
	0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x69, 0x6d,
	0x61, 0x67, 0x65, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x50,
	0x75, 0x6c, 0x6c, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52, 0x0f, 0x69, 0x6d, 0x61, 0x67, 0x65,
	0x50, 0x75, 0x6c, 0x6c, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x22, 0x64, 0x0a, 0x14, 0x47, 0x65,
	0x74, 0x50, 0x72, 0x65, 0x68, 0x65, 0x61, 0x74, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69,
	0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e,
	0x22, 0x89, 0x01, 0x0a, 0x15, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x72, 0x65, 0x68, 0x65, 0x61, 0x74,
	0x4a, 0x6f, 0x62, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f,
	0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x61, 0x67, 0x65,
	0x4e, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x70, 0x61, 0x67, 0x65, 0x4e, 0x6f,
	0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x22, 0x29, 0x0a, 0x17,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x72, 0x65, 0x68, 0x65, 0x61, 0x74, 0x4a, 0x6f, 0x62,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0x2b, 0x0a, 0x19, 0x52, 0x65, 0x64, 0x65, 0x70,
	0x6c, 0x6f, 0x79, 0x50, 0x72, 0x65, 0x68, 0x65, 0x61, 0x74, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x02, 0x69, 0x64, 0x22, 0x69, 0x0a, 0x15, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x6d, 0x61, 0x67,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x16, 0x0a,
	0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72,
	0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f,
	0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22,
	0x1f, 0x0a, 0x09, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x22, 0x5e, 0x0a, 0x16, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x44, 0x0a, 0x0a, 0x69, 0x6d,
	0x61, 0x67, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24,
	0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x69,
	0x6d, 0x61, 0x67, 0x65, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x52, 0x0a, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x73,
	0x22, 0x72, 0x0a, 0x14, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x54, 0x61, 0x67,
	0x73, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69,
	0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e,
	0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x69, 0x6d, 0x61, 0x67, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x22, 0x5a, 0x0a, 0x15, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x6d, 0x61, 0x67,
	0x65, 0x54, 0x61, 0x67, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x41, 0x0a,
	0x09, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x54, 0x61, 0x67, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f,
	0x2e, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6d, 0x61,
	0x67, 0x65, 0x54, 0x61, 0x67, 0x52, 0x09, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x54, 0x61, 0x67, 0x73,
	0x22, 0x30, 0x0a, 0x08, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x54, 0x61, 0x67, 0x12, 0x10, 0x0a, 0x03,
	0x74, 0x61, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x74, 0x61, 0x67, 0x12, 0x12,
	0x0a, 0x04, 0x70, 0x61, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x61,
	0x74, 0x68, 0x22, 0x92, 0x01, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x54,
	0x61, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67,
	0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f,
	0x6e, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x69, 0x6d, 0x61, 0x67, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x69, 0x6d, 0x61, 0x67,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x74, 0x61, 0x67, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x74, 0x61, 0x67, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0x51, 0x0a, 0x1d, 0x4d, 0x6f, 0x64, 0x69, 0x66,
	0x79, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x81, 0x06, 0x0a, 0x11, 0x42,
	0x75, 0x69, 0x6c, 0x64, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x74, 0x61, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x74, 0x61, 0x67, 0x12, 0x1c, 0x0a, 0x09, 0x71, 0x75, 0x65, 0x75, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x71, 0x75, 0x65, 0x75, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77,
	0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06,
	0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65,
	0x67, 0x69, 0x6f, 0x6e, 0x12, 0x1c, 0x0a, 0x09, 0x62, 0x75, 0x69, 0x6c, 0x64, 0x54, 0x79, 0x70,
	0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x62, 0x75, 0x69, 0x6c, 0x64, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x62, 0x61, 0x73, 0x65, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x62, 0x61, 0x73, 0x65, 0x49, 0x6d, 0x61, 0x67, 0x65,
	0x12, 0x2a, 0x0a, 0x10, 0x64, 0x6f, 0x63, 0x6b, 0x65, 0x72, 0x66, 0x69, 0x6c, 0x65, 0x53, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x64, 0x6f, 0x63, 0x6b,
	0x65, 0x72, 0x66, 0x69, 0x6c, 0x65, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x20, 0x0a, 0x0b,
	0x70, 0x72, 0x65, 0x73, 0x65, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x70, 0x72, 0x65, 0x73, 0x65, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x20,
	0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x49, 0x6d, 0x61, 0x67, 0x65,
	0x12, 0x1a, 0x0a, 0x08, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x55, 0x52, 0x4c, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x55, 0x52, 0x4c, 0x12, 0x10, 0x0a, 0x03,
	0x72, 0x75, 0x6e, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x72, 0x75, 0x6e, 0x12, 0x10,
	0x0a, 0x03, 0x63, 0x6d, 0x64, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x63, 0x6d, 0x64,
	0x12, 0x16, 0x0a, 0x06, 0x67, 0x69, 0x74, 0x55, 0x72, 0x6c, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x67, 0x69, 0x74, 0x55, 0x72, 0x6c, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x6f, 0x72, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x6f, 0x72, 0x12, 0x18, 0x0a, 0x07, 0x6a, 0x6f, 0x62, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x11, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x6a, 0x6f, 0x62, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07,
	0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x64, 0x65, 0x76, 0x49, 0x44, 0x18,
	0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x64, 0x65, 0x76, 0x49, 0x44, 0x12, 0x20, 0x0a, 0x0b,
	0x65, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x50, 0x61, 0x74, 0x68, 0x18, 0x14, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x50, 0x61, 0x74, 0x68, 0x12, 0x16,
	0x0a, 0x06, 0x67, 0x69, 0x74, 0x44, 0x69, 0x72, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x67, 0x69, 0x74, 0x44, 0x69, 0x72, 0x12, 0x1c, 0x0a, 0x09, 0x67, 0x69, 0x74, 0x42, 0x72, 0x61,
	0x6e, 0x63, 0x68, 0x18, 0x16, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x67, 0x69, 0x74, 0x42, 0x72,
	0x61, 0x6e, 0x63, 0x68, 0x12, 0x1a, 0x0a, 0x08, 0x62, 0x75, 0x69, 0x6c, 0x64, 0x41, 0x72, 0x67,
	0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x62, 0x75, 0x69, 0x6c, 0x64, 0x41, 0x72, 0x67,
	0x12, 0x16, 0x0a, 0x06, 0x73, 0x73, 0x68, 0x4b, 0x65, 0x79, 0x18, 0x18, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x73, 0x73, 0x68, 0x4b, 0x65, 0x79, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x43,
	0x61, 0x63, 0x68, 0x65, 0x18, 0x19, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x75, 0x73, 0x65, 0x43,
	0x61, 0x63, 0x68, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x75, 0x73, 0x65, 0x53, 0x70, 0x65, 0x65, 0x64,
	0x55, 0x70, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x75, 0x73, 0x65, 0x53, 0x70, 0x65,
	0x65, 0x64, 0x55, 0x70, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x74, 0x6f, 0x70, 0x44, 0x65, 0x76, 0x18,
	0x1b, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x74, 0x6f, 0x70, 0x44, 0x65, 0x76, 0x22, 0x7d,
	0x0a, 0x12, 0x42, 0x75, 0x69, 0x6c, 0x64, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3d,
	0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x61,
	0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x69, 0x6d, 0x61,
	0x67, 0x65, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x69, 0x6c, 0x64, 0x49, 0x6d,
	0x61, 0x67, 0x65, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x3c, 0x0a,
	0x0e, 0x42, 0x75, 0x69, 0x6c, 0x64, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12,
	0x18, 0x0a, 0x07, 0x6a, 0x6f, 0x62, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x6a, 0x6f, 0x62, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x74, 0x61, 0x67,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x74, 0x61, 0x67, 0x22, 0xc2, 0x01, 0x0a, 0x16,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77,
	0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06,
	0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65,
	0x67, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x72, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x72, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72,
	0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73,
	0x22, 0x43, 0x0a, 0x17, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x42,
	0x61, 0x73, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d,
	0x73, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x16, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xd2, 0x01, 0x0a, 0x16, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x49, 0x6d, 0x61, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x0e, 0x0a, 0x02, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x49, 0x44,
	0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x73,
	0x18, 0x06, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x73,
	0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x07, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x22, 0x19, 0x0a, 0x17, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x28, 0x0a, 0x16, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x49,
	0x6d, 0x61, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x0e, 0x0a, 0x02, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x49, 0x44, 0x22,
	0x43, 0x0a, 0x17, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x42, 0x61,
	0x73, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73,
	0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x16, 0x0a, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x22, 0xac, 0x02, 0x0a, 0x14, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x6d, 0x61,
	0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a,
	0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x73, 0x18, 0x05, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x08, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x73, 0x12, 0x18, 0x0a,
	0x07, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07,
	0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x70,
	0x65, 0x72, 0x50, 0x61, 0x67, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x70, 0x65,
	0x72, 0x50, 0x61, 0x67, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69,
	0x63, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x73, 0x70, 0x65,
	0x63, 0x69, 0x66, 0x69, 0x63, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x6f, 0x72, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x6f, 0x72, 0x22, 0x6b, 0x0a, 0x15, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65,
	0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3c, 0x0a, 0x06,
	0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x61,
	0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x69, 0x6d, 0x61,
	0x67, 0x65, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x42, 0x61,
	0x73, 0x65, 0x52, 0x06, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x22, 0xff, 0x01, 0x0a, 0x09, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x12, 0x0e,
	0x0a, 0x02, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x49, 0x44, 0x12, 0x24,
	0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x73, 0x18, 0x06,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x73, 0x12, 0x18,
	0x0a, 0x07, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x07, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x6f, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x6f, 0x72, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69,
	0x6d, 0x65, 0x22, 0xfc, 0x01, 0x0a, 0x1c, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65,
	0x42, 0x75, 0x69, 0x6c, 0x64, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67,
	0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f,
	0x6e, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x74, 0x61, 0x67, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x74, 0x61, 0x67, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x70,
	0x65, 0x72, 0x50, 0x61, 0x67, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x70, 0x65,
	0x72, 0x50, 0x61, 0x67, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6a, 0x6f, 0x62, 0x4e, 0x61, 0x6d, 0x65,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6a, 0x6f, 0x62, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x18, 0x0a, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x22, 0x95, 0x01, 0x0a, 0x1d, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x42,
	0x75, 0x69, 0x6c, 0x64, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x5e, 0x0a, 0x13, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x42, 0x75, 0x69, 0x6c,
	0x64, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x2c, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f,
	0x2e, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6d, 0x61,
	0x67, 0x65, 0x42, 0x75, 0x69, 0x6c, 0x64, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x13,
	0x69, 0x6d, 0x61, 0x67, 0x65, 0x42, 0x75, 0x69, 0x6c, 0x64, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72,
	0x69, 0x65, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x22, 0xea, 0x08, 0x0a, 0x11, 0x49, 0x6d,
	0x61, 0x67, 0x65, 0x42, 0x75, 0x69, 0x6c, 0x64, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x74, 0x61, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x74, 0x61, 0x67, 0x12, 0x1c, 0x0a, 0x09, 0x71, 0x75, 0x65, 0x75, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x71, 0x75, 0x65, 0x75, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f,
	0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72,
	0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67,
	0x69, 0x6f, 0x6e, 0x12, 0x1c, 0x0a, 0x09, 0x62, 0x75, 0x69, 0x6c, 0x64, 0x54, 0x79, 0x70, 0x65,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x62, 0x75, 0x69, 0x6c, 0x64, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x1c, 0x0a, 0x09, 0x62, 0x61, 0x73, 0x65, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x62, 0x61, 0x73, 0x65, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12,
	0x2a, 0x0a, 0x10, 0x64, 0x6f, 0x63, 0x6b, 0x65, 0x72, 0x66, 0x69, 0x6c, 0x65, 0x53, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x64, 0x6f, 0x63, 0x6b, 0x65,
	0x72, 0x66, 0x69, 0x6c, 0x65, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x70,
	0x72, 0x65, 0x73, 0x65, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x70, 0x72, 0x65, 0x73, 0x65, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x20, 0x0a,
	0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12,
	0x1a, 0x0a, 0x08, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x55, 0x52, 0x4c, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x55, 0x52, 0x4c, 0x12, 0x10, 0x0a, 0x03, 0x72,
	0x75, 0x6e, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x72, 0x75, 0x6e, 0x12, 0x10, 0x0a,
	0x03, 0x63, 0x6d, 0x64, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x63, 0x6d, 0x64, 0x12,
	0x16, 0x0a, 0x06, 0x67, 0x69, 0x74, 0x55, 0x72, 0x6c, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x67, 0x69, 0x74, 0x55, 0x72, 0x6c, 0x12, 0x1c, 0x0a, 0x09, 0x62, 0x75, 0x69, 0x6c, 0x64,
	0x54, 0x69, 0x6d, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x62, 0x75, 0x69, 0x6c,
	0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x75, 0x6c, 0x6c, 0x50, 0x61, 0x74,
	0x68, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x75, 0x6c, 0x6c, 0x50, 0x61, 0x74,
	0x68, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x12, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x64,
	0x65, 0x76, 0x49, 0x44, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x64, 0x65, 0x76, 0x49,
	0x44, 0x12, 0x20, 0x0a, 0x0b, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x50, 0x61, 0x74, 0x68,
	0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x50,
	0x61, 0x74, 0x68, 0x12, 0x16, 0x0a, 0x06, 0x67, 0x69, 0x74, 0x44, 0x69, 0x72, 0x18, 0x15, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x67, 0x69, 0x74, 0x44, 0x69, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x16, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x17, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x18, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x18, 0x19, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6a, 0x6f, 0x62, 0x4e, 0x61, 0x6d, 0x65,
	0x18, 0x1a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6a, 0x6f, 0x62, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x18, 0x0a, 0x07, 0x70, 0x6f, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x70, 0x6f, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69,
	0x6d, 0x65, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x18, 0x1e, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x12, 0x18, 0x0a, 0x07, 0x6c,
	0x6f, 0x67, 0x46, 0x72, 0x6f, 0x6d, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x6c, 0x6f,
	0x67, 0x46, 0x72, 0x6f, 0x6d, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x6f, 0x67, 0x54, 0x6f, 0x18, 0x20,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x6c, 0x6f, 0x67, 0x54, 0x6f, 0x12, 0x10, 0x0a, 0x03, 0x6d,
	0x73, 0x67, 0x18, 0x21, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x49, 0x0a,
	0x0a, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x22, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x29, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69,
	0x6f, 0x2e, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6d,
	0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0a, 0x63, 0x6f,
	0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x67, 0x69, 0x74, 0x42,
	0x72, 0x61, 0x6e, 0x63, 0x68, 0x18, 0x23, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x67, 0x69, 0x74,
	0x42, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x12, 0x1a, 0x0a, 0x08, 0x62, 0x75, 0x69, 0x6c, 0x64, 0x41,
	0x72, 0x67, 0x18, 0x24, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x62, 0x75, 0x69, 0x6c, 0x64, 0x41,
	0x72, 0x67, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x73, 0x68, 0x4b, 0x65, 0x79, 0x18, 0x25, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x73, 0x73, 0x68, 0x4b, 0x65, 0x79, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73,
	0x65, 0x43, 0x61, 0x63, 0x68, 0x65, 0x18, 0x26, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x75, 0x73,
	0x65, 0x43, 0x61, 0x63, 0x68, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x75, 0x73, 0x65, 0x53, 0x70, 0x65,
	0x65, 0x64, 0x55, 0x70, 0x18, 0x27, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x75, 0x73, 0x65, 0x53,
	0x70, 0x65, 0x65, 0x64, 0x55, 0x70, 0x22, 0x9e, 0x01, 0x0a, 0x0e, 0x49, 0x6d, 0x61, 0x67, 0x65,
	0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2e, 0x0a, 0x12, 0x6c, 0x61, 0x73, 0x74, 0x54, 0x72, 0x61,
	0x6e, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x12, 0x6c, 0x61, 0x73, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x18, 0x0a,
	0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x6f, 0x0a, 0x15, 0x53, 0x74, 0x6f, 0x70, 0x42,
	0x75, 0x69, 0x6c, 0x64, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x18, 0x0a, 0x07, 0x6a, 0x6f, 0x62, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x6a, 0x6f, 0x62, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f,
	0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x22, 0x42, 0x0a, 0x16, 0x53, 0x74, 0x6f, 0x70,
	0x42, 0x75, 0x69, 0x6c, 0x64, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6d, 0x73, 0x67, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x7a, 0x0a, 0x14,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x54, 0x61, 0x67, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x24, 0x0a, 0x0d,
	0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x74, 0x61, 0x67, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x74, 0x61, 0x67, 0x22, 0x2b, 0x0a, 0x15, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x54, 0x61, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x2a, 0x2f, 0x0a, 0x0f, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x50, 0x75,
	0x6c, 0x6c, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12, 0x0a, 0x0a, 0x06, 0x41, 0x6c, 0x77, 0x61,
	0x79, 0x73, 0x10, 0x00, 0x12, 0x10, 0x0a, 0x0c, 0x49, 0x66, 0x4e, 0x6f, 0x74, 0x50, 0x72, 0x65,
	0x73, 0x65, 0x6e, 0x74, 0x10, 0x01, 0x32, 0xe1, 0x1d, 0x0a, 0x0f, 0x49, 0x6d, 0x61, 0x67, 0x65,
	0x48, 0x75, 0x62, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0xa7, 0x01, 0x0a, 0x13, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x48, 0x75, 0x62, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x35, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64,
	0x69, 0x6f, 0x2e, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x48, 0x75, 0x62, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x36, 0x2e, 0x61, 0x70, 0x69, 0x73,
	0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x68,
	0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x6d, 0x61, 0x67, 0x65,
	0x48, 0x75, 0x62, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x21, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1b, 0x12, 0x19, 0x2f, 0x61, 0x70, 0x69, 0x73,
	0x2f, 0x76, 0x31, 0x2f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x2d, 0x68, 0x75, 0x62, 0x2f, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x7a, 0x0a, 0x0c, 0x4f, 0x70, 0x65, 0x6e, 0x49, 0x6d, 0x61, 0x67,
	0x65, 0x48, 0x75, 0x62, 0x12, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74,
	0x75, 0x64, 0x69, 0x6f, 0x2e, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31,
	0x2e, 0x4f, 0x70, 0x65, 0x6e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x48, 0x75, 0x62, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x22, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x1c, 0x3a, 0x01, 0x2a, 0x22, 0x17, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76,
	0x31, 0x2f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x2d, 0x68, 0x75, 0x62, 0x2f, 0x6f, 0x70, 0x65, 0x6e,
	0x12, 0x7d, 0x0a, 0x0d, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x48, 0x75,
	0x62, 0x12, 0x2f, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69,
	0x6f, 0x2e, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6c,
	0x6f, 0x73, 0x65, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x48, 0x75, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x23, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x1d, 0x3a, 0x01, 0x2a, 0x22, 0x18, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f,
	0x69, 0x6d, 0x61, 0x67, 0x65, 0x2d, 0x68, 0x75, 0x62, 0x2f, 0x63, 0x6c, 0x6f, 0x73, 0x65, 0x12,
	0x8c, 0x01, 0x0a, 0x0a, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x12, 0x2c,
	0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x69,
	0x6d, 0x61, 0x67, 0x65, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x49,
	0x6d, 0x61, 0x67, 0x65, 0x73, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x1a, 0x2d, 0x2e, 0x61,
	0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x69, 0x6d, 0x61,
	0x67, 0x65, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x6d, 0x61,
	0x67, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x21, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x1b, 0x12, 0x19, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x69, 0x6d,
	0x61, 0x67, 0x65, 0x2d, 0x68, 0x75, 0x62, 0x2f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x12, 0x75,
	0x0a, 0x09, 0x41, 0x64, 0x64, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x12, 0x2b, 0x2e, 0x61, 0x70,
	0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x69, 0x6d, 0x61, 0x67,
	0x65, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x64, 0x49, 0x6d, 0x61, 0x67, 0x65,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79,
	0x22, 0x23, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1d, 0x3a, 0x01, 0x2a, 0x22, 0x18, 0x2f, 0x61, 0x70,
	0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x2d, 0x68, 0x75, 0x62, 0x2f,
	0x69, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x88, 0x01, 0x0a, 0x0b, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x2d, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73,
	0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x68, 0x75, 0x62, 0x2e, 0x76,
	0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74,
	0x75, 0x64, 0x69, 0x6f, 0x2e, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31,
	0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x22, 0x28, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x22, 0x3a, 0x01,
	0x2a, 0x1a, 0x1d, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x69, 0x6d, 0x61, 0x67,
	0x65, 0x2d, 0x68, 0x75, 0x62, 0x2f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x2f, 0x7b, 0x69, 0x64, 0x7d,
	0x12, 0x7b, 0x0a, 0x0b, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12,
	0x2d, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e,
	0x69, 0x6d, 0x61, 0x67, 0x65, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x25, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1f, 0x2a, 0x1d,
	0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x2d, 0x68,
	0x75, 0x62, 0x2f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x12, 0xa3, 0x01,
	0x0a, 0x12, 0x47, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x73, 0x12, 0x34, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74,
	0x75, 0x64, 0x69, 0x6f, 0x2e, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2a, 0x2e, 0x61, 0x70, 0x69,
	0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x69, 0x6d, 0x61, 0x67, 0x65,
	0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x22, 0x2b, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x25, 0x12, 0x23,
	0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x2d, 0x68,
	0x75, 0x62, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2d, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x73, 0x12, 0x89, 0x01, 0x0a, 0x10, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x72,
	0x65, 0x68, 0x65, 0x61, 0x74, 0x4a, 0x6f, 0x62, 0x12, 0x32, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e,
	0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x68, 0x75,
	0x62, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x72, 0x65, 0x68, 0x65,
	0x61, 0x74, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x22, 0x29, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x23, 0x3a, 0x01, 0x2a, 0x22,
	0x1e, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x2d,
	0x68, 0x75, 0x62, 0x2f, 0x70, 0x72, 0x65, 0x68, 0x65, 0x61, 0x74, 0x2d, 0x6a, 0x6f, 0x62, 0x12,
	0x94, 0x01, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x50, 0x72, 0x65, 0x68, 0x65, 0x61, 0x74, 0x4a, 0x6f,
	0x62, 0x12, 0x2f, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69,
	0x6f, 0x2e, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65,
	0x74, 0x50, 0x72, 0x65, 0x68, 0x65, 0x61, 0x74, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64,
	0x69, 0x6f, 0x2e, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x50,
	0x72, 0x65, 0x68, 0x65, 0x61, 0x74, 0x4a, 0x6f, 0x62, 0x22, 0x2b, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x25, 0x12, 0x23, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x69, 0x6d, 0x61, 0x67,
	0x65, 0x2d, 0x68, 0x75, 0x62, 0x2f, 0x70, 0x72, 0x65, 0x68, 0x65, 0x61, 0x74, 0x2d, 0x6a, 0x6f,
	0x62, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x12, 0x94, 0x01, 0x0a, 0x0f, 0x4c, 0x69, 0x73, 0x74, 0x50,
	0x72, 0x65, 0x68, 0x65, 0x61, 0x74, 0x4a, 0x6f, 0x62, 0x73, 0x12, 0x30, 0x2e, 0x61, 0x70, 0x69,
	0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x69, 0x6d, 0x61, 0x67, 0x65,
	0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x72, 0x65, 0x68, 0x65,
	0x61, 0x74, 0x4a, 0x6f, 0x62, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x1a, 0x26, 0x2e, 0x61,
	0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x69, 0x6d, 0x61,
	0x67, 0x65, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x65, 0x68, 0x65, 0x61, 0x74,
	0x4a, 0x6f, 0x62, 0x73, 0x22, 0x27, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x21, 0x12, 0x1f, 0x2f, 0x61,
	0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x2d, 0x68, 0x75, 0x62,
	0x2f, 0x70, 0x72, 0x65, 0x68, 0x65, 0x61, 0x74, 0x2d, 0x6a, 0x6f, 0x62, 0x73, 0x12, 0x8b, 0x01,
	0x0a, 0x10, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x72, 0x65, 0x68, 0x65, 0x61, 0x74, 0x4a,
	0x6f, 0x62, 0x12, 0x32, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64,
	0x69, 0x6f, 0x2e, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x72, 0x65, 0x68, 0x65, 0x61, 0x74, 0x4a, 0x6f, 0x62, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x2b,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x25, 0x2a, 0x23, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31,
	0x2f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x2d, 0x68, 0x75, 0x62, 0x2f, 0x70, 0x72, 0x65, 0x68, 0x65,
	0x61, 0x74, 0x2d, 0x6a, 0x6f, 0x62, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x12, 0x9b, 0x01, 0x0a, 0x12,
	0x52, 0x65, 0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x50, 0x72, 0x65, 0x68, 0x65, 0x61, 0x74, 0x4a,
	0x6f, 0x62, 0x12, 0x34, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64,
	0x69, 0x6f, 0x2e, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x52,
	0x65, 0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x50, 0x72, 0x65, 0x68, 0x65, 0x61, 0x74, 0x4a, 0x6f,
	0x62, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79,
	0x22, 0x37, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x31, 0x3a, 0x01, 0x2a, 0x22, 0x2c, 0x2f, 0x61, 0x70,
	0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x2d, 0x68, 0x75, 0x62, 0x2f,
	0x70, 0x72, 0x65, 0x68, 0x65, 0x61, 0x74, 0x2d, 0x6a, 0x6f, 0x62, 0x2f, 0x7b, 0x69, 0x64, 0x7d,
	0x2f, 0x72, 0x65, 0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x12, 0x9d, 0x01, 0x0a, 0x0e, 0x4c, 0x69,
	0x73, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x12, 0x30, 0x2e, 0x61,
	0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x69, 0x6d, 0x61,
	0x67, 0x65, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x6d, 0x61,
	0x67, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x1a, 0x31,
	0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x69,
	0x6d, 0x61, 0x67, 0x65, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x49,
	0x6d, 0x61, 0x67, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x26, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x20, 0x12, 0x1e, 0x2f, 0x61, 0x70, 0x69, 0x73,
	0x2f, 0x76, 0x31, 0x2f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x2d, 0x68, 0x75, 0x62, 0x2f, 0x69, 0x6d,
	0x61, 0x67, 0x65, 0x2d, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x12, 0x99, 0x01, 0x0a, 0x0d, 0x4c, 0x69,
	0x73, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x54, 0x61, 0x67, 0x73, 0x12, 0x2f, 0x2e, 0x61, 0x70,
	0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x69, 0x6d, 0x61, 0x67,
	0x65, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x6d, 0x61, 0x67,
	0x65, 0x54, 0x61, 0x67, 0x73, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x1a, 0x30, 0x2e, 0x61,
	0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x69, 0x6d, 0x61,
	0x67, 0x65, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x6d, 0x61,
	0x67, 0x65, 0x54, 0x61, 0x67, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x25,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1f, 0x12, 0x1d, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31,
	0x2f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x2d, 0x68, 0x75, 0x62, 0x2f, 0x69, 0x6d, 0x61, 0x67, 0x65,
	0x2d, 0x74, 0x61, 0x67, 0x73, 0x12, 0x96, 0x01, 0x0a, 0x0b, 0x47, 0x65, 0x74, 0x49, 0x6d, 0x61,
	0x67, 0x65, 0x54, 0x61, 0x67, 0x12, 0x2d, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73,
	0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x68, 0x75, 0x62, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x65, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x54, 0x61, 0x67, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74,
	0x75, 0x64, 0x69, 0x6f, 0x2e, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31,
	0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x22, 0x36, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x30, 0x12, 0x2e,
	0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x2d, 0x68,
	0x75, 0x62, 0x2f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x2f, 0x7b, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x74, 0x61, 0x67, 0x2f, 0x7b, 0x74, 0x61, 0x67, 0x7d, 0x12, 0xa0,
	0x01, 0x0a, 0x16, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x44, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x38, 0x2e, 0x61, 0x70, 0x69, 0x73,
	0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x68,
	0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x49, 0x6d, 0x61, 0x67,
	0x65, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x34, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x2e, 0x3a, 0x01, 0x2a, 0x22, 0x29, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31,
	0x2f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x2d, 0x68, 0x75, 0x62, 0x2f, 0x69, 0x6d, 0x61, 0x67, 0x65,
	0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x2f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x94, 0x01, 0x0a, 0x0a, 0x42, 0x75, 0x69, 0x6c, 0x64, 0x49, 0x6d, 0x61, 0x67, 0x65,
	0x12, 0x2c, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f,
	0x2e, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x69,
	0x6c, 0x64, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2d,
	0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x69,
	0x6d, 0x61, 0x67, 0x65, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x69, 0x6c, 0x64,
	0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x29, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x23, 0x3a, 0x01, 0x2a, 0x22, 0x1e, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f,
	0x76, 0x31, 0x2f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x2d, 0x68, 0x75, 0x62, 0x2f, 0x69, 0x6d, 0x61,
	0x67, 0x65, 0x2f, 0x62, 0x75, 0x69, 0x6c, 0x64, 0x12, 0xa1, 0x01, 0x0a, 0x0f, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x12, 0x31, 0x2e, 0x61,
	0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x69, 0x6d, 0x61,
	0x67, 0x65, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x49,
	0x6d, 0x61, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x32, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e,
	0x69, 0x6d, 0x61, 0x67, 0x65, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x27, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x21, 0x3a, 0x01, 0x2a, 0x22, 0x1c,
	0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x2d, 0x68,
	0x75, 0x62, 0x2f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x62, 0x61, 0x73, 0x65, 0x12, 0xa1, 0x01, 0x0a,
	0x0f, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65,
	0x12, 0x31, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f,
	0x2e, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x32, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75,
	0x64, 0x69, 0x6f, 0x2e, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x27, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x21, 0x3a,
	0x01, 0x2a, 0x1a, 0x1c, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x69, 0x6d, 0x61,
	0x67, 0x65, 0x2d, 0x68, 0x75, 0x62, 0x2f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x62, 0x61, 0x73, 0x65,
	0x12, 0x9e, 0x01, 0x0a, 0x0f, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x49, 0x6d, 0x61, 0x67, 0x65,
	0x42, 0x61, 0x73, 0x65, 0x12, 0x31, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74,
	0x75, 0x64, 0x69, 0x6f, 0x2e, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31,
	0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x32, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61,
	0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x68, 0x75, 0x62,
	0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x42,
	0x61, 0x73, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x24, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x1e, 0x2a, 0x1c, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x69, 0x6d,
	0x61, 0x67, 0x65, 0x2d, 0x68, 0x75, 0x62, 0x2f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x62, 0x61, 0x73,
	0x65, 0x12, 0x98, 0x01, 0x0a, 0x0d, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x42,
	0x61, 0x73, 0x65, 0x12, 0x2f, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75,
	0x64, 0x69, 0x6f, 0x2e, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e,
	0x4c, 0x69, 0x73, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x30, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74,
	0x75, 0x64, 0x69, 0x6f, 0x2e, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x24, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1e, 0x12, 0x1c,
	0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x2d, 0x68,
	0x75, 0x62, 0x2f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x62, 0x61, 0x73, 0x65, 0x12, 0xb8, 0x01, 0x0a,
	0x15, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x42, 0x75, 0x69, 0x6c, 0x64, 0x48,
	0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x12, 0x37, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69,
	0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x68, 0x75, 0x62, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x42, 0x75, 0x69, 0x6c,
	0x64, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x38, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e,
	0x69, 0x6d, 0x61, 0x67, 0x65, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74,
	0x49, 0x6d, 0x61, 0x67, 0x65, 0x42, 0x75, 0x69, 0x6c, 0x64, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72,
	0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x2c, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x26, 0x12, 0x24, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x69, 0x6d, 0x61, 0x67,
	0x65, 0x2d, 0x68, 0x75, 0x62, 0x2f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x62, 0x75, 0x69, 0x6c, 0x64,
	0x68, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x12, 0xa3, 0x01, 0x0a, 0x0e, 0x53, 0x74, 0x6f, 0x70,
	0x42, 0x75, 0x69, 0x6c, 0x64, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x30, 0x2e, 0x61, 0x70, 0x69,
	0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x69, 0x6d, 0x61, 0x67, 0x65,
	0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x6f, 0x70, 0x42, 0x75, 0x69, 0x6c, 0x64,
	0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x31, 0x2e, 0x61,
	0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x69, 0x6d, 0x61,
	0x67, 0x65, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x6f, 0x70, 0x42, 0x75, 0x69,
	0x6c, 0x64, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x2c, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x26, 0x3a, 0x01, 0x2a, 0x22, 0x21, 0x2f, 0x61, 0x70, 0x69,
	0x73, 0x2f, 0x76, 0x31, 0x2f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x2d, 0x68, 0x75, 0x62, 0x2f, 0x73,
	0x74, 0x6f, 0x70, 0x62, 0x75, 0x69, 0x6c, 0x64, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x9a, 0x01,
	0x0a, 0x0d, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x54, 0x61, 0x67, 0x12,
	0x2f, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e,
	0x69, 0x6d, 0x61, 0x67, 0x65, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x54, 0x61, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x30, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f,
	0x2e, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x65,
	0x63, 0x6b, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x54, 0x61, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x26, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x20, 0x12, 0x1e, 0x2f, 0x61, 0x70, 0x69,
	0x73, 0x2f, 0x76, 0x31, 0x2f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x2d, 0x68, 0x75, 0x62, 0x2f, 0x69,
	0x6d, 0x61, 0x67, 0x65, 0x2f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x42, 0x4b, 0x5a, 0x49, 0x67, 0x69,
	0x74, 0x2e, 0x6c, 0x69, 0x61, 0x6e, 0x6a, 0x69, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x63, 0x6c,
	0x6f, 0x75, 0x64, 0x6e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x2f, 0x6b, 0x69, 0x63, 0x2f, 0x6b, 0x69,
	0x63, 0x2d, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f,
	0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x68, 0x75,
	0x62, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_aistudio_imagehub_v1_imagehub_proto_rawDescOnce sync.Once
	file_aistudio_imagehub_v1_imagehub_proto_rawDescData = file_aistudio_imagehub_v1_imagehub_proto_rawDesc
)

func file_aistudio_imagehub_v1_imagehub_proto_rawDescGZIP() []byte {
	file_aistudio_imagehub_v1_imagehub_proto_rawDescOnce.Do(func() {
		file_aistudio_imagehub_v1_imagehub_proto_rawDescData = protoimpl.X.CompressGZIP(file_aistudio_imagehub_v1_imagehub_proto_rawDescData)
	})
	return file_aistudio_imagehub_v1_imagehub_proto_rawDescData
}

var file_aistudio_imagehub_v1_imagehub_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_aistudio_imagehub_v1_imagehub_proto_msgTypes = make([]protoimpl.MessageInfo, 52)
var file_aistudio_imagehub_v1_imagehub_proto_goTypes = []any{
	(ImagePullPolicy)(0),                  // 0: apis.aistudio.imagehub.v1.ImagePullPolicy
	(*CheckImageHubStatusRequest)(nil),    // 1: apis.aistudio.imagehub.v1.CheckImageHubStatusRequest
	(*CheckImageHubStatusResponse)(nil),   // 2: apis.aistudio.imagehub.v1.CheckImageHubStatusResponse
	(*OpenImageHubRequest)(nil),           // 3: apis.aistudio.imagehub.v1.OpenImageHubRequest
	(*CloseImageHubRequest)(nil),          // 4: apis.aistudio.imagehub.v1.CloseImageHubRequest
	(*DeleteImageRequest)(nil),            // 5: apis.aistudio.imagehub.v1.DeleteImageRequest
	(*AddImagesRequest)(nil),              // 6: apis.aistudio.imagehub.v1.AddImagesRequest
	(*UpdateImageRequest)(nil),            // 7: apis.aistudio.imagehub.v1.UpdateImageRequest
	(*ListImagesOptions)(nil),             // 8: apis.aistudio.imagehub.v1.ListImagesOptions
	(*Image)(nil),                         // 9: apis.aistudio.imagehub.v1.Image
	(*ListImagesResponse)(nil),            // 10: apis.aistudio.imagehub.v1.ListImagesResponse
	(*CascadeImage)(nil),                  // 11: apis.aistudio.imagehub.v1.CascadeImage
	(*ImageChildren)(nil),                 // 12: apis.aistudio.imagehub.v1.ImageChildren
	(*CascadeImages)(nil),                 // 13: apis.aistudio.imagehub.v1.CascadeImages
	(*GetServiceAccountsRequest)(nil),     // 14: apis.aistudio.imagehub.v1.GetServiceAccountsRequest
	(*ServiceAccounts)(nil),               // 15: apis.aistudio.imagehub.v1.ServiceAccounts
	(*ServiceAccount)(nil),                // 16: apis.aistudio.imagehub.v1.ServiceAccount
	(*PreheatJob)(nil),                    // 17: apis.aistudio.imagehub.v1.PreheatJob
	(*ImagePullJob)(nil),                  // 18: apis.aistudio.imagehub.v1.ImagePullJob
	(*PreheatJobs)(nil),                   // 19: apis.aistudio.imagehub.v1.PreheatJobs
	(*CreatePreheatJobRequest)(nil),       // 20: apis.aistudio.imagehub.v1.CreatePreheatJobRequest
	(*GetPreheatJobRequest)(nil),          // 21: apis.aistudio.imagehub.v1.GetPreheatJobRequest
	(*ListPreheatJobOptions)(nil),         // 22: apis.aistudio.imagehub.v1.ListPreheatJobOptions
	(*DeletePreheatJobRequest)(nil),       // 23: apis.aistudio.imagehub.v1.DeletePreheatJobRequest
	(*RedeployPreheatJobRequest)(nil),     // 24: apis.aistudio.imagehub.v1.RedeployPreheatJobRequest
	(*ListImageNamesOptions)(nil),         // 25: apis.aistudio.imagehub.v1.ListImageNamesOptions
	(*ImageName)(nil),                     // 26: apis.aistudio.imagehub.v1.ImageName
	(*ListImageNamesResponse)(nil),        // 27: apis.aistudio.imagehub.v1.ListImageNamesResponse
	(*ListImageTagsOptions)(nil),          // 28: apis.aistudio.imagehub.v1.ListImageTagsOptions
	(*ListImageTagsResponse)(nil),         // 29: apis.aistudio.imagehub.v1.ListImageTagsResponse
	(*ImageTag)(nil),                      // 30: apis.aistudio.imagehub.v1.ImageTag
	(*GetImageTagRequest)(nil),            // 31: apis.aistudio.imagehub.v1.GetImageTagRequest
	(*ModifyImageDescriptionRequest)(nil), // 32: apis.aistudio.imagehub.v1.ModifyImageDescriptionRequest
	(*BuildImageRequest)(nil),             // 33: apis.aistudio.imagehub.v1.BuildImageRequest
	(*BuildImageResponse)(nil),            // 34: apis.aistudio.imagehub.v1.BuildImageResponse
	(*BuildImageData)(nil),                // 35: apis.aistudio.imagehub.v1.BuildImageData
	(*CreateImageBaseRequest)(nil),        // 36: apis.aistudio.imagehub.v1.CreateImageBaseRequest
	(*CreateImageBaseResponse)(nil),       // 37: apis.aistudio.imagehub.v1.CreateImageBaseResponse
	(*UpdateImageBaseRequest)(nil),        // 38: apis.aistudio.imagehub.v1.UpdateImageBaseRequest
	(*UpdateImageBaseResponse)(nil),       // 39: apis.aistudio.imagehub.v1.UpdateImageBaseResponse
	(*DeleteImageBaseRequest)(nil),        // 40: apis.aistudio.imagehub.v1.DeleteImageBaseRequest
	(*DeleteImageBaseResponse)(nil),       // 41: apis.aistudio.imagehub.v1.DeleteImageBaseResponse
	(*ListImageBaseRequest)(nil),          // 42: apis.aistudio.imagehub.v1.ListImageBaseRequest
	(*ListImageBaseResponse)(nil),         // 43: apis.aistudio.imagehub.v1.ListImageBaseResponse
	(*ImageBase)(nil),                     // 44: apis.aistudio.imagehub.v1.ImageBase
	(*ListImageBuildHistoryRequest)(nil),  // 45: apis.aistudio.imagehub.v1.ListImageBuildHistoryRequest
	(*ListImageBuildHistoryResponse)(nil), // 46: apis.aistudio.imagehub.v1.ListImageBuildHistoryResponse
	(*ImageBuildHistory)(nil),             // 47: apis.aistudio.imagehub.v1.ImageBuildHistory
	(*ImageCondition)(nil),                // 48: apis.aistudio.imagehub.v1.ImageCondition
	(*StopBuildImageRequest)(nil),         // 49: apis.aistudio.imagehub.v1.StopBuildImageRequest
	(*StopBuildImageResponse)(nil),        // 50: apis.aistudio.imagehub.v1.StopBuildImageResponse
	(*CheckImageTagRequest)(nil),          // 51: apis.aistudio.imagehub.v1.CheckImageTagRequest
	(*CheckImageTagResponse)(nil),         // 52: apis.aistudio.imagehub.v1.CheckImageTagResponse
	(*common.TimestampModel)(nil),         // 53: apis.common.TimestampModel
	(*emptypb.Empty)(nil),                 // 54: google.protobuf.Empty
}
var file_aistudio_imagehub_v1_imagehub_proto_depIdxs = []int32{
	9,  // 0: apis.aistudio.imagehub.v1.ListImagesResponse.images:type_name -> apis.aistudio.imagehub.v1.Image
	12, // 1: apis.aistudio.imagehub.v1.CascadeImage.children:type_name -> apis.aistudio.imagehub.v1.ImageChildren
	11, // 2: apis.aistudio.imagehub.v1.CascadeImages.images:type_name -> apis.aistudio.imagehub.v1.CascadeImage
	16, // 3: apis.aistudio.imagehub.v1.ServiceAccounts.serviceAccounts:type_name -> apis.aistudio.imagehub.v1.ServiceAccount
	53, // 4: apis.aistudio.imagehub.v1.ServiceAccount.timestamp:type_name -> apis.common.TimestampModel
	53, // 5: apis.aistudio.imagehub.v1.PreheatJob.timestamp:type_name -> apis.common.TimestampModel
	0,  // 6: apis.aistudio.imagehub.v1.PreheatJob.imagePullPolicy:type_name -> apis.aistudio.imagehub.v1.ImagePullPolicy
	18, // 7: apis.aistudio.imagehub.v1.PreheatJob.imagePullJobs:type_name -> apis.aistudio.imagehub.v1.ImagePullJob
	17, // 8: apis.aistudio.imagehub.v1.PreheatJobs.preheatJobs:type_name -> apis.aistudio.imagehub.v1.PreheatJob
	0,  // 9: apis.aistudio.imagehub.v1.CreatePreheatJobRequest.imagePullPolicy:type_name -> apis.aistudio.imagehub.v1.ImagePullPolicy
	26, // 10: apis.aistudio.imagehub.v1.ListImageNamesResponse.imageNames:type_name -> apis.aistudio.imagehub.v1.ImageName
	30, // 11: apis.aistudio.imagehub.v1.ListImageTagsResponse.imageTags:type_name -> apis.aistudio.imagehub.v1.ImageTag
	35, // 12: apis.aistudio.imagehub.v1.BuildImageResponse.data:type_name -> apis.aistudio.imagehub.v1.BuildImageData
	44, // 13: apis.aistudio.imagehub.v1.ListImageBaseResponse.images:type_name -> apis.aistudio.imagehub.v1.ImageBase
	47, // 14: apis.aistudio.imagehub.v1.ListImageBuildHistoryResponse.imageBuildHistories:type_name -> apis.aistudio.imagehub.v1.ImageBuildHistory
	48, // 15: apis.aistudio.imagehub.v1.ImageBuildHistory.conditions:type_name -> apis.aistudio.imagehub.v1.ImageCondition
	1,  // 16: apis.aistudio.imagehub.v1.ImageHubService.CheckImageHubStatus:input_type -> apis.aistudio.imagehub.v1.CheckImageHubStatusRequest
	3,  // 17: apis.aistudio.imagehub.v1.ImageHubService.OpenImageHub:input_type -> apis.aistudio.imagehub.v1.OpenImageHubRequest
	4,  // 18: apis.aistudio.imagehub.v1.ImageHubService.CloseImageHub:input_type -> apis.aistudio.imagehub.v1.CloseImageHubRequest
	8,  // 19: apis.aistudio.imagehub.v1.ImageHubService.ListImages:input_type -> apis.aistudio.imagehub.v1.ListImagesOptions
	6,  // 20: apis.aistudio.imagehub.v1.ImageHubService.AddImages:input_type -> apis.aistudio.imagehub.v1.AddImagesRequest
	7,  // 21: apis.aistudio.imagehub.v1.ImageHubService.UpdateImage:input_type -> apis.aistudio.imagehub.v1.UpdateImageRequest
	5,  // 22: apis.aistudio.imagehub.v1.ImageHubService.DeleteImage:input_type -> apis.aistudio.imagehub.v1.DeleteImageRequest
	14, // 23: apis.aistudio.imagehub.v1.ImageHubService.GetServiceAccounts:input_type -> apis.aistudio.imagehub.v1.GetServiceAccountsRequest
	20, // 24: apis.aistudio.imagehub.v1.ImageHubService.CreatePreheatJob:input_type -> apis.aistudio.imagehub.v1.CreatePreheatJobRequest
	21, // 25: apis.aistudio.imagehub.v1.ImageHubService.GetPreheatJob:input_type -> apis.aistudio.imagehub.v1.GetPreheatJobRequest
	22, // 26: apis.aistudio.imagehub.v1.ImageHubService.ListPreheatJobs:input_type -> apis.aistudio.imagehub.v1.ListPreheatJobOptions
	23, // 27: apis.aistudio.imagehub.v1.ImageHubService.DeletePreheatJob:input_type -> apis.aistudio.imagehub.v1.DeletePreheatJobRequest
	24, // 28: apis.aistudio.imagehub.v1.ImageHubService.RedeployPreheatJob:input_type -> apis.aistudio.imagehub.v1.RedeployPreheatJobRequest
	25, // 29: apis.aistudio.imagehub.v1.ImageHubService.ListImageNames:input_type -> apis.aistudio.imagehub.v1.ListImageNamesOptions
	28, // 30: apis.aistudio.imagehub.v1.ImageHubService.ListImageTags:input_type -> apis.aistudio.imagehub.v1.ListImageTagsOptions
	31, // 31: apis.aistudio.imagehub.v1.ImageHubService.GetImageTag:input_type -> apis.aistudio.imagehub.v1.GetImageTagRequest
	32, // 32: apis.aistudio.imagehub.v1.ImageHubService.ModifyImageDescription:input_type -> apis.aistudio.imagehub.v1.ModifyImageDescriptionRequest
	33, // 33: apis.aistudio.imagehub.v1.ImageHubService.BuildImage:input_type -> apis.aistudio.imagehub.v1.BuildImageRequest
	36, // 34: apis.aistudio.imagehub.v1.ImageHubService.CreateImageBase:input_type -> apis.aistudio.imagehub.v1.CreateImageBaseRequest
	38, // 35: apis.aistudio.imagehub.v1.ImageHubService.UpdateImageBase:input_type -> apis.aistudio.imagehub.v1.UpdateImageBaseRequest
	40, // 36: apis.aistudio.imagehub.v1.ImageHubService.DeleteImageBase:input_type -> apis.aistudio.imagehub.v1.DeleteImageBaseRequest
	42, // 37: apis.aistudio.imagehub.v1.ImageHubService.ListImageBase:input_type -> apis.aistudio.imagehub.v1.ListImageBaseRequest
	45, // 38: apis.aistudio.imagehub.v1.ImageHubService.ListImageBuildHistory:input_type -> apis.aistudio.imagehub.v1.ListImageBuildHistoryRequest
	49, // 39: apis.aistudio.imagehub.v1.ImageHubService.StopBuildImage:input_type -> apis.aistudio.imagehub.v1.StopBuildImageRequest
	51, // 40: apis.aistudio.imagehub.v1.ImageHubService.CheckImageTag:input_type -> apis.aistudio.imagehub.v1.CheckImageTagRequest
	2,  // 41: apis.aistudio.imagehub.v1.ImageHubService.CheckImageHubStatus:output_type -> apis.aistudio.imagehub.v1.CheckImageHubStatusResponse
	54, // 42: apis.aistudio.imagehub.v1.ImageHubService.OpenImageHub:output_type -> google.protobuf.Empty
	54, // 43: apis.aistudio.imagehub.v1.ImageHubService.CloseImageHub:output_type -> google.protobuf.Empty
	10, // 44: apis.aistudio.imagehub.v1.ImageHubService.ListImages:output_type -> apis.aistudio.imagehub.v1.ListImagesResponse
	54, // 45: apis.aistudio.imagehub.v1.ImageHubService.AddImages:output_type -> google.protobuf.Empty
	9,  // 46: apis.aistudio.imagehub.v1.ImageHubService.UpdateImage:output_type -> apis.aistudio.imagehub.v1.Image
	54, // 47: apis.aistudio.imagehub.v1.ImageHubService.DeleteImage:output_type -> google.protobuf.Empty
	15, // 48: apis.aistudio.imagehub.v1.ImageHubService.GetServiceAccounts:output_type -> apis.aistudio.imagehub.v1.ServiceAccounts
	54, // 49: apis.aistudio.imagehub.v1.ImageHubService.CreatePreheatJob:output_type -> google.protobuf.Empty
	17, // 50: apis.aistudio.imagehub.v1.ImageHubService.GetPreheatJob:output_type -> apis.aistudio.imagehub.v1.PreheatJob
	19, // 51: apis.aistudio.imagehub.v1.ImageHubService.ListPreheatJobs:output_type -> apis.aistudio.imagehub.v1.PreheatJobs
	54, // 52: apis.aistudio.imagehub.v1.ImageHubService.DeletePreheatJob:output_type -> google.protobuf.Empty
	54, // 53: apis.aistudio.imagehub.v1.ImageHubService.RedeployPreheatJob:output_type -> google.protobuf.Empty
	27, // 54: apis.aistudio.imagehub.v1.ImageHubService.ListImageNames:output_type -> apis.aistudio.imagehub.v1.ListImageNamesResponse
	29, // 55: apis.aistudio.imagehub.v1.ImageHubService.ListImageTags:output_type -> apis.aistudio.imagehub.v1.ListImageTagsResponse
	9,  // 56: apis.aistudio.imagehub.v1.ImageHubService.GetImageTag:output_type -> apis.aistudio.imagehub.v1.Image
	54, // 57: apis.aistudio.imagehub.v1.ImageHubService.ModifyImageDescription:output_type -> google.protobuf.Empty
	34, // 58: apis.aistudio.imagehub.v1.ImageHubService.BuildImage:output_type -> apis.aistudio.imagehub.v1.BuildImageResponse
	37, // 59: apis.aistudio.imagehub.v1.ImageHubService.CreateImageBase:output_type -> apis.aistudio.imagehub.v1.CreateImageBaseResponse
	39, // 60: apis.aistudio.imagehub.v1.ImageHubService.UpdateImageBase:output_type -> apis.aistudio.imagehub.v1.UpdateImageBaseResponse
	41, // 61: apis.aistudio.imagehub.v1.ImageHubService.DeleteImageBase:output_type -> apis.aistudio.imagehub.v1.DeleteImageBaseResponse
	43, // 62: apis.aistudio.imagehub.v1.ImageHubService.ListImageBase:output_type -> apis.aistudio.imagehub.v1.ListImageBaseResponse
	46, // 63: apis.aistudio.imagehub.v1.ImageHubService.ListImageBuildHistory:output_type -> apis.aistudio.imagehub.v1.ListImageBuildHistoryResponse
	50, // 64: apis.aistudio.imagehub.v1.ImageHubService.StopBuildImage:output_type -> apis.aistudio.imagehub.v1.StopBuildImageResponse
	52, // 65: apis.aistudio.imagehub.v1.ImageHubService.CheckImageTag:output_type -> apis.aistudio.imagehub.v1.CheckImageTagResponse
	41, // [41:66] is the sub-list for method output_type
	16, // [16:41] is the sub-list for method input_type
	16, // [16:16] is the sub-list for extension type_name
	16, // [16:16] is the sub-list for extension extendee
	0,  // [0:16] is the sub-list for field type_name
}

func init() { file_aistudio_imagehub_v1_imagehub_proto_init() }
func file_aistudio_imagehub_v1_imagehub_proto_init() {
	if File_aistudio_imagehub_v1_imagehub_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_aistudio_imagehub_v1_imagehub_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*CheckImageHubStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_imagehub_v1_imagehub_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*CheckImageHubStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_imagehub_v1_imagehub_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*OpenImageHubRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_imagehub_v1_imagehub_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*CloseImageHubRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_imagehub_v1_imagehub_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*DeleteImageRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_imagehub_v1_imagehub_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*AddImagesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_imagehub_v1_imagehub_proto_msgTypes[6].Exporter = func(v any, i int) any {
			switch v := v.(*UpdateImageRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_imagehub_v1_imagehub_proto_msgTypes[7].Exporter = func(v any, i int) any {
			switch v := v.(*ListImagesOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_imagehub_v1_imagehub_proto_msgTypes[8].Exporter = func(v any, i int) any {
			switch v := v.(*Image); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_imagehub_v1_imagehub_proto_msgTypes[9].Exporter = func(v any, i int) any {
			switch v := v.(*ListImagesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_imagehub_v1_imagehub_proto_msgTypes[10].Exporter = func(v any, i int) any {
			switch v := v.(*CascadeImage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_imagehub_v1_imagehub_proto_msgTypes[11].Exporter = func(v any, i int) any {
			switch v := v.(*ImageChildren); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_imagehub_v1_imagehub_proto_msgTypes[12].Exporter = func(v any, i int) any {
			switch v := v.(*CascadeImages); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_imagehub_v1_imagehub_proto_msgTypes[13].Exporter = func(v any, i int) any {
			switch v := v.(*GetServiceAccountsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_imagehub_v1_imagehub_proto_msgTypes[14].Exporter = func(v any, i int) any {
			switch v := v.(*ServiceAccounts); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_imagehub_v1_imagehub_proto_msgTypes[15].Exporter = func(v any, i int) any {
			switch v := v.(*ServiceAccount); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_imagehub_v1_imagehub_proto_msgTypes[16].Exporter = func(v any, i int) any {
			switch v := v.(*PreheatJob); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_imagehub_v1_imagehub_proto_msgTypes[17].Exporter = func(v any, i int) any {
			switch v := v.(*ImagePullJob); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_imagehub_v1_imagehub_proto_msgTypes[18].Exporter = func(v any, i int) any {
			switch v := v.(*PreheatJobs); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_imagehub_v1_imagehub_proto_msgTypes[19].Exporter = func(v any, i int) any {
			switch v := v.(*CreatePreheatJobRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_imagehub_v1_imagehub_proto_msgTypes[20].Exporter = func(v any, i int) any {
			switch v := v.(*GetPreheatJobRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_imagehub_v1_imagehub_proto_msgTypes[21].Exporter = func(v any, i int) any {
			switch v := v.(*ListPreheatJobOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_imagehub_v1_imagehub_proto_msgTypes[22].Exporter = func(v any, i int) any {
			switch v := v.(*DeletePreheatJobRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_imagehub_v1_imagehub_proto_msgTypes[23].Exporter = func(v any, i int) any {
			switch v := v.(*RedeployPreheatJobRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_imagehub_v1_imagehub_proto_msgTypes[24].Exporter = func(v any, i int) any {
			switch v := v.(*ListImageNamesOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_imagehub_v1_imagehub_proto_msgTypes[25].Exporter = func(v any, i int) any {
			switch v := v.(*ImageName); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_imagehub_v1_imagehub_proto_msgTypes[26].Exporter = func(v any, i int) any {
			switch v := v.(*ListImageNamesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_imagehub_v1_imagehub_proto_msgTypes[27].Exporter = func(v any, i int) any {
			switch v := v.(*ListImageTagsOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_imagehub_v1_imagehub_proto_msgTypes[28].Exporter = func(v any, i int) any {
			switch v := v.(*ListImageTagsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_imagehub_v1_imagehub_proto_msgTypes[29].Exporter = func(v any, i int) any {
			switch v := v.(*ImageTag); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_imagehub_v1_imagehub_proto_msgTypes[30].Exporter = func(v any, i int) any {
			switch v := v.(*GetImageTagRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_imagehub_v1_imagehub_proto_msgTypes[31].Exporter = func(v any, i int) any {
			switch v := v.(*ModifyImageDescriptionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_imagehub_v1_imagehub_proto_msgTypes[32].Exporter = func(v any, i int) any {
			switch v := v.(*BuildImageRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_imagehub_v1_imagehub_proto_msgTypes[33].Exporter = func(v any, i int) any {
			switch v := v.(*BuildImageResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_imagehub_v1_imagehub_proto_msgTypes[34].Exporter = func(v any, i int) any {
			switch v := v.(*BuildImageData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_imagehub_v1_imagehub_proto_msgTypes[35].Exporter = func(v any, i int) any {
			switch v := v.(*CreateImageBaseRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_imagehub_v1_imagehub_proto_msgTypes[36].Exporter = func(v any, i int) any {
			switch v := v.(*CreateImageBaseResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_imagehub_v1_imagehub_proto_msgTypes[37].Exporter = func(v any, i int) any {
			switch v := v.(*UpdateImageBaseRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_imagehub_v1_imagehub_proto_msgTypes[38].Exporter = func(v any, i int) any {
			switch v := v.(*UpdateImageBaseResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_imagehub_v1_imagehub_proto_msgTypes[39].Exporter = func(v any, i int) any {
			switch v := v.(*DeleteImageBaseRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_imagehub_v1_imagehub_proto_msgTypes[40].Exporter = func(v any, i int) any {
			switch v := v.(*DeleteImageBaseResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_imagehub_v1_imagehub_proto_msgTypes[41].Exporter = func(v any, i int) any {
			switch v := v.(*ListImageBaseRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_imagehub_v1_imagehub_proto_msgTypes[42].Exporter = func(v any, i int) any {
			switch v := v.(*ListImageBaseResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_imagehub_v1_imagehub_proto_msgTypes[43].Exporter = func(v any, i int) any {
			switch v := v.(*ImageBase); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_imagehub_v1_imagehub_proto_msgTypes[44].Exporter = func(v any, i int) any {
			switch v := v.(*ListImageBuildHistoryRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_imagehub_v1_imagehub_proto_msgTypes[45].Exporter = func(v any, i int) any {
			switch v := v.(*ListImageBuildHistoryResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_imagehub_v1_imagehub_proto_msgTypes[46].Exporter = func(v any, i int) any {
			switch v := v.(*ImageBuildHistory); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_imagehub_v1_imagehub_proto_msgTypes[47].Exporter = func(v any, i int) any {
			switch v := v.(*ImageCondition); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_imagehub_v1_imagehub_proto_msgTypes[48].Exporter = func(v any, i int) any {
			switch v := v.(*StopBuildImageRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_imagehub_v1_imagehub_proto_msgTypes[49].Exporter = func(v any, i int) any {
			switch v := v.(*StopBuildImageResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_imagehub_v1_imagehub_proto_msgTypes[50].Exporter = func(v any, i int) any {
			switch v := v.(*CheckImageTagRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_imagehub_v1_imagehub_proto_msgTypes[51].Exporter = func(v any, i int) any {
			switch v := v.(*CheckImageTagResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_aistudio_imagehub_v1_imagehub_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   52,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_aistudio_imagehub_v1_imagehub_proto_goTypes,
		DependencyIndexes: file_aistudio_imagehub_v1_imagehub_proto_depIdxs,
		EnumInfos:         file_aistudio_imagehub_v1_imagehub_proto_enumTypes,
		MessageInfos:      file_aistudio_imagehub_v1_imagehub_proto_msgTypes,
	}.Build()
	File_aistudio_imagehub_v1_imagehub_proto = out.File
	file_aistudio_imagehub_v1_imagehub_proto_rawDesc = nil
	file_aistudio_imagehub_v1_imagehub_proto_goTypes = nil
	file_aistudio_imagehub_v1_imagehub_proto_depIdxs = nil
}
