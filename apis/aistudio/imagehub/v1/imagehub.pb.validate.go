// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: aistudio/imagehub/v1/imagehub.proto

package v1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on CheckImageHubStatusRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CheckImageHubStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckImageHubStatusRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CheckImageHubStatusRequestMultiError, or nil if none found.
func (m *CheckImageHubStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckImageHubStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for WorkspaceName

	// no validation rules for Region

	if len(errors) > 0 {
		return CheckImageHubStatusRequestMultiError(errors)
	}

	return nil
}

// CheckImageHubStatusRequestMultiError is an error wrapping multiple
// validation errors returned by CheckImageHubStatusRequest.ValidateAll() if
// the designated constraints aren't met.
type CheckImageHubStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckImageHubStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckImageHubStatusRequestMultiError) AllErrors() []error { return m }

// CheckImageHubStatusRequestValidationError is the validation error returned
// by CheckImageHubStatusRequest.Validate if the designated constraints aren't met.
type CheckImageHubStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckImageHubStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckImageHubStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckImageHubStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckImageHubStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckImageHubStatusRequestValidationError) ErrorName() string {
	return "CheckImageHubStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CheckImageHubStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckImageHubStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckImageHubStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckImageHubStatusRequestValidationError{}

// Validate checks the field values on CheckImageHubStatusResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CheckImageHubStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckImageHubStatusResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CheckImageHubStatusResponseMultiError, or nil if none found.
func (m *CheckImageHubStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckImageHubStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Activated

	// no validation rules for Supported

	if len(errors) > 0 {
		return CheckImageHubStatusResponseMultiError(errors)
	}

	return nil
}

// CheckImageHubStatusResponseMultiError is an error wrapping multiple
// validation errors returned by CheckImageHubStatusResponse.ValidateAll() if
// the designated constraints aren't met.
type CheckImageHubStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckImageHubStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckImageHubStatusResponseMultiError) AllErrors() []error { return m }

// CheckImageHubStatusResponseValidationError is the validation error returned
// by CheckImageHubStatusResponse.Validate if the designated constraints
// aren't met.
type CheckImageHubStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckImageHubStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckImageHubStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckImageHubStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckImageHubStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckImageHubStatusResponseValidationError) ErrorName() string {
	return "CheckImageHubStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CheckImageHubStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckImageHubStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckImageHubStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckImageHubStatusResponseValidationError{}

// Validate checks the field values on OpenImageHubRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *OpenImageHubRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OpenImageHubRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// OpenImageHubRequestMultiError, or nil if none found.
func (m *OpenImageHubRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *OpenImageHubRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for WorkspaceName

	// no validation rules for Region

	if len(errors) > 0 {
		return OpenImageHubRequestMultiError(errors)
	}

	return nil
}

// OpenImageHubRequestMultiError is an error wrapping multiple validation
// errors returned by OpenImageHubRequest.ValidateAll() if the designated
// constraints aren't met.
type OpenImageHubRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OpenImageHubRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OpenImageHubRequestMultiError) AllErrors() []error { return m }

// OpenImageHubRequestValidationError is the validation error returned by
// OpenImageHubRequest.Validate if the designated constraints aren't met.
type OpenImageHubRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OpenImageHubRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OpenImageHubRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OpenImageHubRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OpenImageHubRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OpenImageHubRequestValidationError) ErrorName() string {
	return "OpenImageHubRequestValidationError"
}

// Error satisfies the builtin error interface
func (e OpenImageHubRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOpenImageHubRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OpenImageHubRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OpenImageHubRequestValidationError{}

// Validate checks the field values on CloseImageHubRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CloseImageHubRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CloseImageHubRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CloseImageHubRequestMultiError, or nil if none found.
func (m *CloseImageHubRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CloseImageHubRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for WorkspaceName

	// no validation rules for Region

	if len(errors) > 0 {
		return CloseImageHubRequestMultiError(errors)
	}

	return nil
}

// CloseImageHubRequestMultiError is an error wrapping multiple validation
// errors returned by CloseImageHubRequest.ValidateAll() if the designated
// constraints aren't met.
type CloseImageHubRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CloseImageHubRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CloseImageHubRequestMultiError) AllErrors() []error { return m }

// CloseImageHubRequestValidationError is the validation error returned by
// CloseImageHubRequest.Validate if the designated constraints aren't met.
type CloseImageHubRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CloseImageHubRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CloseImageHubRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CloseImageHubRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CloseImageHubRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CloseImageHubRequestValidationError) ErrorName() string {
	return "CloseImageHubRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CloseImageHubRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCloseImageHubRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CloseImageHubRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CloseImageHubRequestValidationError{}

// Validate checks the field values on DeleteImageRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteImageRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteImageRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteImageRequestMultiError, or nil if none found.
func (m *DeleteImageRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteImageRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(errors) > 0 {
		return DeleteImageRequestMultiError(errors)
	}

	return nil
}

// DeleteImageRequestMultiError is an error wrapping multiple validation errors
// returned by DeleteImageRequest.ValidateAll() if the designated constraints
// aren't met.
type DeleteImageRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteImageRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteImageRequestMultiError) AllErrors() []error { return m }

// DeleteImageRequestValidationError is the validation error returned by
// DeleteImageRequest.Validate if the designated constraints aren't met.
type DeleteImageRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteImageRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteImageRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteImageRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteImageRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteImageRequestValidationError) ErrorName() string {
	return "DeleteImageRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteImageRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteImageRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteImageRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteImageRequestValidationError{}

// Validate checks the field values on AddImagesRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AddImagesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AddImagesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AddImagesRequestMultiError, or nil if none found.
func (m *AddImagesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *AddImagesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Region

	// no validation rules for Namespace

	// no validation rules for Name

	// no validation rules for Domain

	// no validation rules for Description

	// no validation rules for Preset

	// no validation rules for WorkspaceName

	if len(errors) > 0 {
		return AddImagesRequestMultiError(errors)
	}

	return nil
}

// AddImagesRequestMultiError is an error wrapping multiple validation errors
// returned by AddImagesRequest.ValidateAll() if the designated constraints
// aren't met.
type AddImagesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddImagesRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddImagesRequestMultiError) AllErrors() []error { return m }

// AddImagesRequestValidationError is the validation error returned by
// AddImagesRequest.Validate if the designated constraints aren't met.
type AddImagesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddImagesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddImagesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddImagesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddImagesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddImagesRequestValidationError) ErrorName() string { return "AddImagesRequestValidationError" }

// Error satisfies the builtin error interface
func (e AddImagesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddImagesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddImagesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddImagesRequestValidationError{}

// Validate checks the field values on UpdateImageRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateImageRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateImageRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateImageRequestMultiError, or nil if none found.
func (m *UpdateImageRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateImageRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Namespace

	// no validation rules for Name

	// no validation rules for Domain

	// no validation rules for Description

	// no validation rules for Preset

	// no validation rules for WorkspaceName

	// no validation rules for Region

	if len(errors) > 0 {
		return UpdateImageRequestMultiError(errors)
	}

	return nil
}

// UpdateImageRequestMultiError is an error wrapping multiple validation errors
// returned by UpdateImageRequest.ValidateAll() if the designated constraints
// aren't met.
type UpdateImageRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateImageRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateImageRequestMultiError) AllErrors() []error { return m }

// UpdateImageRequestValidationError is the validation error returned by
// UpdateImageRequest.Validate if the designated constraints aren't met.
type UpdateImageRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateImageRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateImageRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateImageRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateImageRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateImageRequestValidationError) ErrorName() string {
	return "UpdateImageRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateImageRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateImageRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateImageRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateImageRequestValidationError{}

// Validate checks the field values on ListImagesOptions with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ListImagesOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListImagesOptions with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListImagesOptionsMultiError, or nil if none found.
func (m *ListImagesOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *ListImagesOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Region

	// no validation rules for Namespace

	// no validation rules for Name

	// no validation rules for Domain

	// no validation rules for WorkspaceName

	// no validation rules for Preset

	// no validation rules for Page

	// no validation rules for PageSize

	// no validation rules for SpecificName

	// no validation rules for Tag

	// no validation rules for Creator

	if len(errors) > 0 {
		return ListImagesOptionsMultiError(errors)
	}

	return nil
}

// ListImagesOptionsMultiError is an error wrapping multiple validation errors
// returned by ListImagesOptions.ValidateAll() if the designated constraints
// aren't met.
type ListImagesOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListImagesOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListImagesOptionsMultiError) AllErrors() []error { return m }

// ListImagesOptionsValidationError is the validation error returned by
// ListImagesOptions.Validate if the designated constraints aren't met.
type ListImagesOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListImagesOptionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListImagesOptionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListImagesOptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListImagesOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListImagesOptionsValidationError) ErrorName() string {
	return "ListImagesOptionsValidationError"
}

// Error satisfies the builtin error interface
func (e ListImagesOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListImagesOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListImagesOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListImagesOptionsValidationError{}

// Validate checks the field values on Image with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Image) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Image with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in ImageMultiError, or nil if none found.
func (m *Image) ValidateAll() error {
	return m.validate(true)
}

func (m *Image) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Namespace

	// no validation rules for Name

	// no validation rules for Preset

	// no validation rules for Domain

	// no validation rules for Tag

	// no validation rules for Description

	// no validation rules for FullPath

	// no validation rules for Size

	// no validation rules for Architecture

	// no validation rules for CreateTime

	// no validation rules for UpdateTime

	// no validation rules for Region

	// no validation rules for Id

	// no validation rules for FullName

	// no validation rules for SshKey

	// no validation rules for BuildType

	// no validation rules for BaseImage

	// no validation rules for DockerfileSource

	// no validation rules for PresetImage

	// no validation rules for CustomImage

	// no validation rules for ImageURL

	// no validation rules for Run

	// no validation rules for Cmd

	// no validation rules for GitUrl

	// no validation rules for Creator

	// no validation rules for JobName

	// no validation rules for Content

	// no validation rules for DevID

	// no validation rules for ExcludePath

	// no validation rules for GitDir

	// no validation rules for GitBranch

	// no validation rules for BuildArg

	// no validation rules for UseCache

	// no validation rules for UseSpeedUp

	if len(errors) > 0 {
		return ImageMultiError(errors)
	}

	return nil
}

// ImageMultiError is an error wrapping multiple validation errors returned by
// Image.ValidateAll() if the designated constraints aren't met.
type ImageMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ImageMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ImageMultiError) AllErrors() []error { return m }

// ImageValidationError is the validation error returned by Image.Validate if
// the designated constraints aren't met.
type ImageValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ImageValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ImageValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ImageValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ImageValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ImageValidationError) ErrorName() string { return "ImageValidationError" }

// Error satisfies the builtin error interface
func (e ImageValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sImage.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ImageValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ImageValidationError{}

// Validate checks the field values on ListImagesResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListImagesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListImagesResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListImagesResponseMultiError, or nil if none found.
func (m *ListImagesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListImagesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetImages() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListImagesResponseValidationError{
						field:  fmt.Sprintf("Images[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListImagesResponseValidationError{
						field:  fmt.Sprintf("Images[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListImagesResponseValidationError{
					field:  fmt.Sprintf("Images[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Total

	if len(errors) > 0 {
		return ListImagesResponseMultiError(errors)
	}

	return nil
}

// ListImagesResponseMultiError is an error wrapping multiple validation errors
// returned by ListImagesResponse.ValidateAll() if the designated constraints
// aren't met.
type ListImagesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListImagesResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListImagesResponseMultiError) AllErrors() []error { return m }

// ListImagesResponseValidationError is the validation error returned by
// ListImagesResponse.Validate if the designated constraints aren't met.
type ListImagesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListImagesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListImagesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListImagesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListImagesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListImagesResponseValidationError) ErrorName() string {
	return "ListImagesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListImagesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListImagesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListImagesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListImagesResponseValidationError{}

// Validate checks the field values on CascadeImage with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CascadeImage) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CascadeImage with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CascadeImageMultiError, or
// nil if none found.
func (m *CascadeImage) ValidateAll() error {
	return m.validate(true)
}

func (m *CascadeImage) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Label

	for idx, item := range m.GetChildren() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CascadeImageValidationError{
						field:  fmt.Sprintf("Children[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CascadeImageValidationError{
						field:  fmt.Sprintf("Children[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CascadeImageValidationError{
					field:  fmt.Sprintf("Children[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return CascadeImageMultiError(errors)
	}

	return nil
}

// CascadeImageMultiError is an error wrapping multiple validation errors
// returned by CascadeImage.ValidateAll() if the designated constraints aren't met.
type CascadeImageMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CascadeImageMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CascadeImageMultiError) AllErrors() []error { return m }

// CascadeImageValidationError is the validation error returned by
// CascadeImage.Validate if the designated constraints aren't met.
type CascadeImageValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CascadeImageValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CascadeImageValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CascadeImageValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CascadeImageValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CascadeImageValidationError) ErrorName() string { return "CascadeImageValidationError" }

// Error satisfies the builtin error interface
func (e CascadeImageValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCascadeImage.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CascadeImageValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CascadeImageValidationError{}

// Validate checks the field values on ImageChildren with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ImageChildren) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ImageChildren with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ImageChildrenMultiError, or
// nil if none found.
func (m *ImageChildren) ValidateAll() error {
	return m.validate(true)
}

func (m *ImageChildren) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Label

	// no validation rules for Value

	if len(errors) > 0 {
		return ImageChildrenMultiError(errors)
	}

	return nil
}

// ImageChildrenMultiError is an error wrapping multiple validation errors
// returned by ImageChildren.ValidateAll() if the designated constraints
// aren't met.
type ImageChildrenMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ImageChildrenMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ImageChildrenMultiError) AllErrors() []error { return m }

// ImageChildrenValidationError is the validation error returned by
// ImageChildren.Validate if the designated constraints aren't met.
type ImageChildrenValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ImageChildrenValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ImageChildrenValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ImageChildrenValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ImageChildrenValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ImageChildrenValidationError) ErrorName() string { return "ImageChildrenValidationError" }

// Error satisfies the builtin error interface
func (e ImageChildrenValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sImageChildren.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ImageChildrenValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ImageChildrenValidationError{}

// Validate checks the field values on CascadeImages with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CascadeImages) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CascadeImages with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CascadeImagesMultiError, or
// nil if none found.
func (m *CascadeImages) ValidateAll() error {
	return m.validate(true)
}

func (m *CascadeImages) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetImages() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CascadeImagesValidationError{
						field:  fmt.Sprintf("Images[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CascadeImagesValidationError{
						field:  fmt.Sprintf("Images[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CascadeImagesValidationError{
					field:  fmt.Sprintf("Images[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Total

	if len(errors) > 0 {
		return CascadeImagesMultiError(errors)
	}

	return nil
}

// CascadeImagesMultiError is an error wrapping multiple validation errors
// returned by CascadeImages.ValidateAll() if the designated constraints
// aren't met.
type CascadeImagesMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CascadeImagesMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CascadeImagesMultiError) AllErrors() []error { return m }

// CascadeImagesValidationError is the validation error returned by
// CascadeImages.Validate if the designated constraints aren't met.
type CascadeImagesValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CascadeImagesValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CascadeImagesValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CascadeImagesValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CascadeImagesValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CascadeImagesValidationError) ErrorName() string { return "CascadeImagesValidationError" }

// Error satisfies the builtin error interface
func (e CascadeImagesValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCascadeImages.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CascadeImagesValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CascadeImagesValidationError{}

// Validate checks the field values on GetServiceAccountsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetServiceAccountsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetServiceAccountsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetServiceAccountsRequestMultiError, or nil if none found.
func (m *GetServiceAccountsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetServiceAccountsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for WorkspaceName

	// no validation rules for Region

	if len(errors) > 0 {
		return GetServiceAccountsRequestMultiError(errors)
	}

	return nil
}

// GetServiceAccountsRequestMultiError is an error wrapping multiple validation
// errors returned by GetServiceAccountsRequest.ValidateAll() if the
// designated constraints aren't met.
type GetServiceAccountsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetServiceAccountsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetServiceAccountsRequestMultiError) AllErrors() []error { return m }

// GetServiceAccountsRequestValidationError is the validation error returned by
// GetServiceAccountsRequest.Validate if the designated constraints aren't met.
type GetServiceAccountsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetServiceAccountsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetServiceAccountsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetServiceAccountsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetServiceAccountsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetServiceAccountsRequestValidationError) ErrorName() string {
	return "GetServiceAccountsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetServiceAccountsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetServiceAccountsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetServiceAccountsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetServiceAccountsRequestValidationError{}

// Validate checks the field values on ServiceAccounts with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ServiceAccounts) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ServiceAccounts with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ServiceAccountsMultiError, or nil if none found.
func (m *ServiceAccounts) ValidateAll() error {
	return m.validate(true)
}

func (m *ServiceAccounts) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetServiceAccounts() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ServiceAccountsValidationError{
						field:  fmt.Sprintf("ServiceAccounts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ServiceAccountsValidationError{
						field:  fmt.Sprintf("ServiceAccounts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ServiceAccountsValidationError{
					field:  fmt.Sprintf("ServiceAccounts[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ServiceAccountsMultiError(errors)
	}

	return nil
}

// ServiceAccountsMultiError is an error wrapping multiple validation errors
// returned by ServiceAccounts.ValidateAll() if the designated constraints
// aren't met.
type ServiceAccountsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ServiceAccountsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ServiceAccountsMultiError) AllErrors() []error { return m }

// ServiceAccountsValidationError is the validation error returned by
// ServiceAccounts.Validate if the designated constraints aren't met.
type ServiceAccountsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ServiceAccountsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ServiceAccountsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ServiceAccountsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ServiceAccountsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ServiceAccountsValidationError) ErrorName() string { return "ServiceAccountsValidationError" }

// Error satisfies the builtin error interface
func (e ServiceAccountsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sServiceAccounts.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ServiceAccountsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ServiceAccountsValidationError{}

// Validate checks the field values on ServiceAccount with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ServiceAccount) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ServiceAccount with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ServiceAccountMultiError,
// or nil if none found.
func (m *ServiceAccount) ValidateAll() error {
	return m.validate(true)
}

func (m *ServiceAccount) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTimestamp()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ServiceAccountValidationError{
					field:  "Timestamp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ServiceAccountValidationError{
					field:  "Timestamp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTimestamp()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ServiceAccountValidationError{
				field:  "Timestamp",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Name

	// no validation rules for Namespace

	// no validation rules for Description

	// no validation rules for ServiceAccountType

	// no validation rules for Password

	// no validation rules for Region

	if len(errors) > 0 {
		return ServiceAccountMultiError(errors)
	}

	return nil
}

// ServiceAccountMultiError is an error wrapping multiple validation errors
// returned by ServiceAccount.ValidateAll() if the designated constraints
// aren't met.
type ServiceAccountMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ServiceAccountMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ServiceAccountMultiError) AllErrors() []error { return m }

// ServiceAccountValidationError is the validation error returned by
// ServiceAccount.Validate if the designated constraints aren't met.
type ServiceAccountValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ServiceAccountValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ServiceAccountValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ServiceAccountValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ServiceAccountValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ServiceAccountValidationError) ErrorName() string { return "ServiceAccountValidationError" }

// Error satisfies the builtin error interface
func (e ServiceAccountValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sServiceAccount.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ServiceAccountValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ServiceAccountValidationError{}

// Validate checks the field values on PreheatJob with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PreheatJob) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PreheatJob with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in PreheatJobMultiError, or
// nil if none found.
func (m *PreheatJob) ValidateAll() error {
	return m.validate(true)
}

func (m *PreheatJob) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTimestamp()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PreheatJobValidationError{
					field:  "Timestamp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PreheatJobValidationError{
					field:  "Timestamp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTimestamp()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PreheatJobValidationError{
				field:  "Timestamp",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Id

	// no validation rules for WorkspaceName

	// no validation rules for Region

	// no validation rules for JobStatus

	// no validation rules for Rate

	// no validation rules for Creator

	// no validation rules for ImagePullPolicy

	for idx, item := range m.GetImagePullJobs() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PreheatJobValidationError{
						field:  fmt.Sprintf("ImagePullJobs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PreheatJobValidationError{
						field:  fmt.Sprintf("ImagePullJobs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PreheatJobValidationError{
					field:  fmt.Sprintf("ImagePullJobs[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Desired

	// no validation rules for Failed

	// no validation rules for Succeeded

	// no validation rules for Active

	if len(errors) > 0 {
		return PreheatJobMultiError(errors)
	}

	return nil
}

// PreheatJobMultiError is an error wrapping multiple validation errors
// returned by PreheatJob.ValidateAll() if the designated constraints aren't met.
type PreheatJobMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PreheatJobMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PreheatJobMultiError) AllErrors() []error { return m }

// PreheatJobValidationError is the validation error returned by
// PreheatJob.Validate if the designated constraints aren't met.
type PreheatJobValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PreheatJobValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PreheatJobValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PreheatJobValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PreheatJobValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PreheatJobValidationError) ErrorName() string { return "PreheatJobValidationError" }

// Error satisfies the builtin error interface
func (e PreheatJobValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPreheatJob.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PreheatJobValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PreheatJobValidationError{}

// Validate checks the field values on ImagePullJob with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ImagePullJob) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ImagePullJob with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ImagePullJobMultiError, or
// nil if none found.
func (m *ImagePullJob) ValidateAll() error {
	return m.validate(true)
}

func (m *ImagePullJob) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Image

	// no validation rules for Status

	// no validation rules for Rate

	// no validation rules for Desired

	// no validation rules for Failed

	// no validation rules for Succeeded

	// no validation rules for Active

	// no validation rules for Message

	// no validation rules for Cluster

	// no validation rules for Id

	if len(errors) > 0 {
		return ImagePullJobMultiError(errors)
	}

	return nil
}

// ImagePullJobMultiError is an error wrapping multiple validation errors
// returned by ImagePullJob.ValidateAll() if the designated constraints aren't met.
type ImagePullJobMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ImagePullJobMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ImagePullJobMultiError) AllErrors() []error { return m }

// ImagePullJobValidationError is the validation error returned by
// ImagePullJob.Validate if the designated constraints aren't met.
type ImagePullJobValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ImagePullJobValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ImagePullJobValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ImagePullJobValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ImagePullJobValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ImagePullJobValidationError) ErrorName() string { return "ImagePullJobValidationError" }

// Error satisfies the builtin error interface
func (e ImagePullJobValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sImagePullJob.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ImagePullJobValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ImagePullJobValidationError{}

// Validate checks the field values on PreheatJobs with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PreheatJobs) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PreheatJobs with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in PreheatJobsMultiError, or
// nil if none found.
func (m *PreheatJobs) ValidateAll() error {
	return m.validate(true)
}

func (m *PreheatJobs) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetPreheatJobs() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PreheatJobsValidationError{
						field:  fmt.Sprintf("PreheatJobs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PreheatJobsValidationError{
						field:  fmt.Sprintf("PreheatJobs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PreheatJobsValidationError{
					field:  fmt.Sprintf("PreheatJobs[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return PreheatJobsMultiError(errors)
	}

	return nil
}

// PreheatJobsMultiError is an error wrapping multiple validation errors
// returned by PreheatJobs.ValidateAll() if the designated constraints aren't met.
type PreheatJobsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PreheatJobsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PreheatJobsMultiError) AllErrors() []error { return m }

// PreheatJobsValidationError is the validation error returned by
// PreheatJobs.Validate if the designated constraints aren't met.
type PreheatJobsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PreheatJobsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PreheatJobsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PreheatJobsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PreheatJobsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PreheatJobsValidationError) ErrorName() string { return "PreheatJobsValidationError" }

// Error satisfies the builtin error interface
func (e PreheatJobsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPreheatJobs.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PreheatJobsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PreheatJobsValidationError{}

// Validate checks the field values on CreatePreheatJobRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreatePreheatJobRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreatePreheatJobRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreatePreheatJobRequestMultiError, or nil if none found.
func (m *CreatePreheatJobRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreatePreheatJobRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for WorkspaceName

	// no validation rules for Region

	// no validation rules for ImagePullPolicy

	if len(errors) > 0 {
		return CreatePreheatJobRequestMultiError(errors)
	}

	return nil
}

// CreatePreheatJobRequestMultiError is an error wrapping multiple validation
// errors returned by CreatePreheatJobRequest.ValidateAll() if the designated
// constraints aren't met.
type CreatePreheatJobRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreatePreheatJobRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreatePreheatJobRequestMultiError) AllErrors() []error { return m }

// CreatePreheatJobRequestValidationError is the validation error returned by
// CreatePreheatJobRequest.Validate if the designated constraints aren't met.
type CreatePreheatJobRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreatePreheatJobRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreatePreheatJobRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreatePreheatJobRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreatePreheatJobRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreatePreheatJobRequestValidationError) ErrorName() string {
	return "CreatePreheatJobRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreatePreheatJobRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreatePreheatJobRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreatePreheatJobRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreatePreheatJobRequestValidationError{}

// Validate checks the field values on GetPreheatJobRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetPreheatJobRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPreheatJobRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetPreheatJobRequestMultiError, or nil if none found.
func (m *GetPreheatJobRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPreheatJobRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for WorkspaceName

	// no validation rules for Region

	if len(errors) > 0 {
		return GetPreheatJobRequestMultiError(errors)
	}

	return nil
}

// GetPreheatJobRequestMultiError is an error wrapping multiple validation
// errors returned by GetPreheatJobRequest.ValidateAll() if the designated
// constraints aren't met.
type GetPreheatJobRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPreheatJobRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPreheatJobRequestMultiError) AllErrors() []error { return m }

// GetPreheatJobRequestValidationError is the validation error returned by
// GetPreheatJobRequest.Validate if the designated constraints aren't met.
type GetPreheatJobRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPreheatJobRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPreheatJobRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPreheatJobRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPreheatJobRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPreheatJobRequestValidationError) ErrorName() string {
	return "GetPreheatJobRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetPreheatJobRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPreheatJobRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPreheatJobRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPreheatJobRequestValidationError{}

// Validate checks the field values on ListPreheatJobOptions with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListPreheatJobOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListPreheatJobOptions with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListPreheatJobOptionsMultiError, or nil if none found.
func (m *ListPreheatJobOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *ListPreheatJobOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for WorkspaceName

	// no validation rules for Region

	// no validation rules for PageNo

	// no validation rules for PageSize

	if len(errors) > 0 {
		return ListPreheatJobOptionsMultiError(errors)
	}

	return nil
}

// ListPreheatJobOptionsMultiError is an error wrapping multiple validation
// errors returned by ListPreheatJobOptions.ValidateAll() if the designated
// constraints aren't met.
type ListPreheatJobOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListPreheatJobOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListPreheatJobOptionsMultiError) AllErrors() []error { return m }

// ListPreheatJobOptionsValidationError is the validation error returned by
// ListPreheatJobOptions.Validate if the designated constraints aren't met.
type ListPreheatJobOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListPreheatJobOptionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListPreheatJobOptionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListPreheatJobOptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListPreheatJobOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListPreheatJobOptionsValidationError) ErrorName() string {
	return "ListPreheatJobOptionsValidationError"
}

// Error satisfies the builtin error interface
func (e ListPreheatJobOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListPreheatJobOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListPreheatJobOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListPreheatJobOptionsValidationError{}

// Validate checks the field values on DeletePreheatJobRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeletePreheatJobRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeletePreheatJobRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeletePreheatJobRequestMultiError, or nil if none found.
func (m *DeletePreheatJobRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeletePreheatJobRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(errors) > 0 {
		return DeletePreheatJobRequestMultiError(errors)
	}

	return nil
}

// DeletePreheatJobRequestMultiError is an error wrapping multiple validation
// errors returned by DeletePreheatJobRequest.ValidateAll() if the designated
// constraints aren't met.
type DeletePreheatJobRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeletePreheatJobRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeletePreheatJobRequestMultiError) AllErrors() []error { return m }

// DeletePreheatJobRequestValidationError is the validation error returned by
// DeletePreheatJobRequest.Validate if the designated constraints aren't met.
type DeletePreheatJobRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeletePreheatJobRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeletePreheatJobRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeletePreheatJobRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeletePreheatJobRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeletePreheatJobRequestValidationError) ErrorName() string {
	return "DeletePreheatJobRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeletePreheatJobRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeletePreheatJobRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeletePreheatJobRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeletePreheatJobRequestValidationError{}

// Validate checks the field values on RedeployPreheatJobRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RedeployPreheatJobRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RedeployPreheatJobRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RedeployPreheatJobRequestMultiError, or nil if none found.
func (m *RedeployPreheatJobRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *RedeployPreheatJobRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(errors) > 0 {
		return RedeployPreheatJobRequestMultiError(errors)
	}

	return nil
}

// RedeployPreheatJobRequestMultiError is an error wrapping multiple validation
// errors returned by RedeployPreheatJobRequest.ValidateAll() if the
// designated constraints aren't met.
type RedeployPreheatJobRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RedeployPreheatJobRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RedeployPreheatJobRequestMultiError) AllErrors() []error { return m }

// RedeployPreheatJobRequestValidationError is the validation error returned by
// RedeployPreheatJobRequest.Validate if the designated constraints aren't met.
type RedeployPreheatJobRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RedeployPreheatJobRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RedeployPreheatJobRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RedeployPreheatJobRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RedeployPreheatJobRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RedeployPreheatJobRequestValidationError) ErrorName() string {
	return "RedeployPreheatJobRequestValidationError"
}

// Error satisfies the builtin error interface
func (e RedeployPreheatJobRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRedeployPreheatJobRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RedeployPreheatJobRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RedeployPreheatJobRequestValidationError{}

// Validate checks the field values on ListImageNamesOptions with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListImageNamesOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListImageNamesOptions with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListImageNamesOptionsMultiError, or nil if none found.
func (m *ListImageNamesOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *ListImageNamesOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Region

	// no validation rules for WorkspaceName

	// no validation rules for Name

	if len(errors) > 0 {
		return ListImageNamesOptionsMultiError(errors)
	}

	return nil
}

// ListImageNamesOptionsMultiError is an error wrapping multiple validation
// errors returned by ListImageNamesOptions.ValidateAll() if the designated
// constraints aren't met.
type ListImageNamesOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListImageNamesOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListImageNamesOptionsMultiError) AllErrors() []error { return m }

// ListImageNamesOptionsValidationError is the validation error returned by
// ListImageNamesOptions.Validate if the designated constraints aren't met.
type ListImageNamesOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListImageNamesOptionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListImageNamesOptionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListImageNamesOptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListImageNamesOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListImageNamesOptionsValidationError) ErrorName() string {
	return "ListImageNamesOptionsValidationError"
}

// Error satisfies the builtin error interface
func (e ListImageNamesOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListImageNamesOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListImageNamesOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListImageNamesOptionsValidationError{}

// Validate checks the field values on ImageName with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ImageName) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ImageName with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ImageNameMultiError, or nil
// if none found.
func (m *ImageName) ValidateAll() error {
	return m.validate(true)
}

func (m *ImageName) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	if len(errors) > 0 {
		return ImageNameMultiError(errors)
	}

	return nil
}

// ImageNameMultiError is an error wrapping multiple validation errors returned
// by ImageName.ValidateAll() if the designated constraints aren't met.
type ImageNameMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ImageNameMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ImageNameMultiError) AllErrors() []error { return m }

// ImageNameValidationError is the validation error returned by
// ImageName.Validate if the designated constraints aren't met.
type ImageNameValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ImageNameValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ImageNameValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ImageNameValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ImageNameValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ImageNameValidationError) ErrorName() string { return "ImageNameValidationError" }

// Error satisfies the builtin error interface
func (e ImageNameValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sImageName.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ImageNameValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ImageNameValidationError{}

// Validate checks the field values on ListImageNamesResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListImageNamesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListImageNamesResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListImageNamesResponseMultiError, or nil if none found.
func (m *ListImageNamesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListImageNamesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetImageNames() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListImageNamesResponseValidationError{
						field:  fmt.Sprintf("ImageNames[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListImageNamesResponseValidationError{
						field:  fmt.Sprintf("ImageNames[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListImageNamesResponseValidationError{
					field:  fmt.Sprintf("ImageNames[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListImageNamesResponseMultiError(errors)
	}

	return nil
}

// ListImageNamesResponseMultiError is an error wrapping multiple validation
// errors returned by ListImageNamesResponse.ValidateAll() if the designated
// constraints aren't met.
type ListImageNamesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListImageNamesResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListImageNamesResponseMultiError) AllErrors() []error { return m }

// ListImageNamesResponseValidationError is the validation error returned by
// ListImageNamesResponse.Validate if the designated constraints aren't met.
type ListImageNamesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListImageNamesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListImageNamesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListImageNamesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListImageNamesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListImageNamesResponseValidationError) ErrorName() string {
	return "ListImageNamesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListImageNamesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListImageNamesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListImageNamesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListImageNamesResponseValidationError{}

// Validate checks the field values on ListImageTagsOptions with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListImageTagsOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListImageTagsOptions with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListImageTagsOptionsMultiError, or nil if none found.
func (m *ListImageTagsOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *ListImageTagsOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Region

	// no validation rules for WorkspaceName

	// no validation rules for ImageName

	if len(errors) > 0 {
		return ListImageTagsOptionsMultiError(errors)
	}

	return nil
}

// ListImageTagsOptionsMultiError is an error wrapping multiple validation
// errors returned by ListImageTagsOptions.ValidateAll() if the designated
// constraints aren't met.
type ListImageTagsOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListImageTagsOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListImageTagsOptionsMultiError) AllErrors() []error { return m }

// ListImageTagsOptionsValidationError is the validation error returned by
// ListImageTagsOptions.Validate if the designated constraints aren't met.
type ListImageTagsOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListImageTagsOptionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListImageTagsOptionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListImageTagsOptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListImageTagsOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListImageTagsOptionsValidationError) ErrorName() string {
	return "ListImageTagsOptionsValidationError"
}

// Error satisfies the builtin error interface
func (e ListImageTagsOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListImageTagsOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListImageTagsOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListImageTagsOptionsValidationError{}

// Validate checks the field values on ListImageTagsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListImageTagsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListImageTagsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListImageTagsResponseMultiError, or nil if none found.
func (m *ListImageTagsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListImageTagsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetImageTags() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListImageTagsResponseValidationError{
						field:  fmt.Sprintf("ImageTags[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListImageTagsResponseValidationError{
						field:  fmt.Sprintf("ImageTags[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListImageTagsResponseValidationError{
					field:  fmt.Sprintf("ImageTags[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListImageTagsResponseMultiError(errors)
	}

	return nil
}

// ListImageTagsResponseMultiError is an error wrapping multiple validation
// errors returned by ListImageTagsResponse.ValidateAll() if the designated
// constraints aren't met.
type ListImageTagsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListImageTagsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListImageTagsResponseMultiError) AllErrors() []error { return m }

// ListImageTagsResponseValidationError is the validation error returned by
// ListImageTagsResponse.Validate if the designated constraints aren't met.
type ListImageTagsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListImageTagsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListImageTagsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListImageTagsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListImageTagsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListImageTagsResponseValidationError) ErrorName() string {
	return "ListImageTagsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListImageTagsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListImageTagsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListImageTagsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListImageTagsResponseValidationError{}

// Validate checks the field values on ImageTag with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ImageTag) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ImageTag with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ImageTagMultiError, or nil
// if none found.
func (m *ImageTag) ValidateAll() error {
	return m.validate(true)
}

func (m *ImageTag) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Tag

	// no validation rules for Path

	if len(errors) > 0 {
		return ImageTagMultiError(errors)
	}

	return nil
}

// ImageTagMultiError is an error wrapping multiple validation errors returned
// by ImageTag.ValidateAll() if the designated constraints aren't met.
type ImageTagMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ImageTagMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ImageTagMultiError) AllErrors() []error { return m }

// ImageTagValidationError is the validation error returned by
// ImageTag.Validate if the designated constraints aren't met.
type ImageTagValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ImageTagValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ImageTagValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ImageTagValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ImageTagValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ImageTagValidationError) ErrorName() string { return "ImageTagValidationError" }

// Error satisfies the builtin error interface
func (e ImageTagValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sImageTag.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ImageTagValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ImageTagValidationError{}

// Validate checks the field values on GetImageTagRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetImageTagRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetImageTagRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetImageTagRequestMultiError, or nil if none found.
func (m *GetImageTagRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetImageTagRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Region

	// no validation rules for WorkspaceName

	// no validation rules for ImageName

	// no validation rules for Tag

	// no validation rules for Id

	if len(errors) > 0 {
		return GetImageTagRequestMultiError(errors)
	}

	return nil
}

// GetImageTagRequestMultiError is an error wrapping multiple validation errors
// returned by GetImageTagRequest.ValidateAll() if the designated constraints
// aren't met.
type GetImageTagRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetImageTagRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetImageTagRequestMultiError) AllErrors() []error { return m }

// GetImageTagRequestValidationError is the validation error returned by
// GetImageTagRequest.Validate if the designated constraints aren't met.
type GetImageTagRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetImageTagRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetImageTagRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetImageTagRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetImageTagRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetImageTagRequestValidationError) ErrorName() string {
	return "GetImageTagRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetImageTagRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetImageTagRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetImageTagRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetImageTagRequestValidationError{}

// Validate checks the field values on ModifyImageDescriptionRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ModifyImageDescriptionRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ModifyImageDescriptionRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ModifyImageDescriptionRequestMultiError, or nil if none found.
func (m *ModifyImageDescriptionRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ModifyImageDescriptionRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Description

	if len(errors) > 0 {
		return ModifyImageDescriptionRequestMultiError(errors)
	}

	return nil
}

// ModifyImageDescriptionRequestMultiError is an error wrapping multiple
// validation errors returned by ModifyImageDescriptionRequest.ValidateAll()
// if the designated constraints aren't met.
type ModifyImageDescriptionRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ModifyImageDescriptionRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ModifyImageDescriptionRequestMultiError) AllErrors() []error { return m }

// ModifyImageDescriptionRequestValidationError is the validation error
// returned by ModifyImageDescriptionRequest.Validate if the designated
// constraints aren't met.
type ModifyImageDescriptionRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ModifyImageDescriptionRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ModifyImageDescriptionRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ModifyImageDescriptionRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ModifyImageDescriptionRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ModifyImageDescriptionRequestValidationError) ErrorName() string {
	return "ModifyImageDescriptionRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ModifyImageDescriptionRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sModifyImageDescriptionRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ModifyImageDescriptionRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ModifyImageDescriptionRequestValidationError{}

// Validate checks the field values on BuildImageRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *BuildImageRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BuildImageRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BuildImageRequestMultiError, or nil if none found.
func (m *BuildImageRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *BuildImageRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for Tag

	// no validation rules for QueueName

	// no validation rules for Description

	// no validation rules for WorkspaceName

	// no validation rules for Region

	// no validation rules for BuildType

	// no validation rules for BaseImage

	// no validation rules for DockerfileSource

	// no validation rules for PresetImage

	// no validation rules for CustomImage

	// no validation rules for ImageURL

	// no validation rules for Run

	// no validation rules for Cmd

	// no validation rules for GitUrl

	// no validation rules for Creator

	// no validation rules for JobName

	// no validation rules for Content

	// no validation rules for DevID

	// no validation rules for ExcludePath

	// no validation rules for GitDir

	// no validation rules for GitBranch

	// no validation rules for BuildArg

	// no validation rules for SshKey

	// no validation rules for UseCache

	// no validation rules for UseSpeedUp

	// no validation rules for StopDev

	if len(errors) > 0 {
		return BuildImageRequestMultiError(errors)
	}

	return nil
}

// BuildImageRequestMultiError is an error wrapping multiple validation errors
// returned by BuildImageRequest.ValidateAll() if the designated constraints
// aren't met.
type BuildImageRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BuildImageRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BuildImageRequestMultiError) AllErrors() []error { return m }

// BuildImageRequestValidationError is the validation error returned by
// BuildImageRequest.Validate if the designated constraints aren't met.
type BuildImageRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BuildImageRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BuildImageRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BuildImageRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BuildImageRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BuildImageRequestValidationError) ErrorName() string {
	return "BuildImageRequestValidationError"
}

// Error satisfies the builtin error interface
func (e BuildImageRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBuildImageRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BuildImageRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BuildImageRequestValidationError{}

// Validate checks the field values on BuildImageResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BuildImageResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BuildImageResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BuildImageResponseMultiError, or nil if none found.
func (m *BuildImageResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *BuildImageResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Msg

	// no validation rules for Status

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BuildImageResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BuildImageResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BuildImageResponseValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return BuildImageResponseMultiError(errors)
	}

	return nil
}

// BuildImageResponseMultiError is an error wrapping multiple validation errors
// returned by BuildImageResponse.ValidateAll() if the designated constraints
// aren't met.
type BuildImageResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BuildImageResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BuildImageResponseMultiError) AllErrors() []error { return m }

// BuildImageResponseValidationError is the validation error returned by
// BuildImageResponse.Validate if the designated constraints aren't met.
type BuildImageResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BuildImageResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BuildImageResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BuildImageResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BuildImageResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BuildImageResponseValidationError) ErrorName() string {
	return "BuildImageResponseValidationError"
}

// Error satisfies the builtin error interface
func (e BuildImageResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBuildImageResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BuildImageResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BuildImageResponseValidationError{}

// Validate checks the field values on BuildImageData with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *BuildImageData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BuildImageData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in BuildImageDataMultiError,
// or nil if none found.
func (m *BuildImageData) ValidateAll() error {
	return m.validate(true)
}

func (m *BuildImageData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for JobName

	// no validation rules for Tag

	if len(errors) > 0 {
		return BuildImageDataMultiError(errors)
	}

	return nil
}

// BuildImageDataMultiError is an error wrapping multiple validation errors
// returned by BuildImageData.ValidateAll() if the designated constraints
// aren't met.
type BuildImageDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BuildImageDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BuildImageDataMultiError) AllErrors() []error { return m }

// BuildImageDataValidationError is the validation error returned by
// BuildImageData.Validate if the designated constraints aren't met.
type BuildImageDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BuildImageDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BuildImageDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BuildImageDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BuildImageDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BuildImageDataValidationError) ErrorName() string { return "BuildImageDataValidationError" }

// Error satisfies the builtin error interface
func (e BuildImageDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBuildImageData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BuildImageDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BuildImageDataValidationError{}

// Validate checks the field values on CreateImageBaseRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateImageBaseRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateImageBaseRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateImageBaseRequestMultiError, or nil if none found.
func (m *CreateImageBaseRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateImageBaseRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for WorkspaceName

	// no validation rules for Region

	// no validation rules for Name

	// no validation rules for Description

	if len(errors) > 0 {
		return CreateImageBaseRequestMultiError(errors)
	}

	return nil
}

// CreateImageBaseRequestMultiError is an error wrapping multiple validation
// errors returned by CreateImageBaseRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateImageBaseRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateImageBaseRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateImageBaseRequestMultiError) AllErrors() []error { return m }

// CreateImageBaseRequestValidationError is the validation error returned by
// CreateImageBaseRequest.Validate if the designated constraints aren't met.
type CreateImageBaseRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateImageBaseRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateImageBaseRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateImageBaseRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateImageBaseRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateImageBaseRequestValidationError) ErrorName() string {
	return "CreateImageBaseRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateImageBaseRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateImageBaseRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateImageBaseRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateImageBaseRequestValidationError{}

// Validate checks the field values on CreateImageBaseResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateImageBaseResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateImageBaseResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateImageBaseResponseMultiError, or nil if none found.
func (m *CreateImageBaseResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateImageBaseResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Msg

	// no validation rules for Status

	if len(errors) > 0 {
		return CreateImageBaseResponseMultiError(errors)
	}

	return nil
}

// CreateImageBaseResponseMultiError is an error wrapping multiple validation
// errors returned by CreateImageBaseResponse.ValidateAll() if the designated
// constraints aren't met.
type CreateImageBaseResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateImageBaseResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateImageBaseResponseMultiError) AllErrors() []error { return m }

// CreateImageBaseResponseValidationError is the validation error returned by
// CreateImageBaseResponse.Validate if the designated constraints aren't met.
type CreateImageBaseResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateImageBaseResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateImageBaseResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateImageBaseResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateImageBaseResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateImageBaseResponseValidationError) ErrorName() string {
	return "CreateImageBaseResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateImageBaseResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateImageBaseResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateImageBaseResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateImageBaseResponseValidationError{}

// Validate checks the field values on UpdateImageBaseRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateImageBaseRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateImageBaseRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateImageBaseRequestMultiError, or nil if none found.
func (m *UpdateImageBaseRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateImageBaseRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ID

	// no validation rules for WorkspaceName

	// no validation rules for Region

	// no validation rules for Name

	// no validation rules for Description

	if len(errors) > 0 {
		return UpdateImageBaseRequestMultiError(errors)
	}

	return nil
}

// UpdateImageBaseRequestMultiError is an error wrapping multiple validation
// errors returned by UpdateImageBaseRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateImageBaseRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateImageBaseRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateImageBaseRequestMultiError) AllErrors() []error { return m }

// UpdateImageBaseRequestValidationError is the validation error returned by
// UpdateImageBaseRequest.Validate if the designated constraints aren't met.
type UpdateImageBaseRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateImageBaseRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateImageBaseRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateImageBaseRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateImageBaseRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateImageBaseRequestValidationError) ErrorName() string {
	return "UpdateImageBaseRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateImageBaseRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateImageBaseRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateImageBaseRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateImageBaseRequestValidationError{}

// Validate checks the field values on UpdateImageBaseResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateImageBaseResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateImageBaseResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateImageBaseResponseMultiError, or nil if none found.
func (m *UpdateImageBaseResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateImageBaseResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return UpdateImageBaseResponseMultiError(errors)
	}

	return nil
}

// UpdateImageBaseResponseMultiError is an error wrapping multiple validation
// errors returned by UpdateImageBaseResponse.ValidateAll() if the designated
// constraints aren't met.
type UpdateImageBaseResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateImageBaseResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateImageBaseResponseMultiError) AllErrors() []error { return m }

// UpdateImageBaseResponseValidationError is the validation error returned by
// UpdateImageBaseResponse.Validate if the designated constraints aren't met.
type UpdateImageBaseResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateImageBaseResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateImageBaseResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateImageBaseResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateImageBaseResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateImageBaseResponseValidationError) ErrorName() string {
	return "UpdateImageBaseResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateImageBaseResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateImageBaseResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateImageBaseResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateImageBaseResponseValidationError{}

// Validate checks the field values on DeleteImageBaseRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteImageBaseRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteImageBaseRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteImageBaseRequestMultiError, or nil if none found.
func (m *DeleteImageBaseRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteImageBaseRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ID

	if len(errors) > 0 {
		return DeleteImageBaseRequestMultiError(errors)
	}

	return nil
}

// DeleteImageBaseRequestMultiError is an error wrapping multiple validation
// errors returned by DeleteImageBaseRequest.ValidateAll() if the designated
// constraints aren't met.
type DeleteImageBaseRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteImageBaseRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteImageBaseRequestMultiError) AllErrors() []error { return m }

// DeleteImageBaseRequestValidationError is the validation error returned by
// DeleteImageBaseRequest.Validate if the designated constraints aren't met.
type DeleteImageBaseRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteImageBaseRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteImageBaseRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteImageBaseRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteImageBaseRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteImageBaseRequestValidationError) ErrorName() string {
	return "DeleteImageBaseRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteImageBaseRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteImageBaseRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteImageBaseRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteImageBaseRequestValidationError{}

// Validate checks the field values on DeleteImageBaseResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteImageBaseResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteImageBaseResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteImageBaseResponseMultiError, or nil if none found.
func (m *DeleteImageBaseResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteImageBaseResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Msg

	// no validation rules for Status

	if len(errors) > 0 {
		return DeleteImageBaseResponseMultiError(errors)
	}

	return nil
}

// DeleteImageBaseResponseMultiError is an error wrapping multiple validation
// errors returned by DeleteImageBaseResponse.ValidateAll() if the designated
// constraints aren't met.
type DeleteImageBaseResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteImageBaseResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteImageBaseResponseMultiError) AllErrors() []error { return m }

// DeleteImageBaseResponseValidationError is the validation error returned by
// DeleteImageBaseResponse.Validate if the designated constraints aren't met.
type DeleteImageBaseResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteImageBaseResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteImageBaseResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteImageBaseResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteImageBaseResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteImageBaseResponseValidationError) ErrorName() string {
	return "DeleteImageBaseResponseValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteImageBaseResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteImageBaseResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteImageBaseResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteImageBaseResponseValidationError{}

// Validate checks the field values on ListImageBaseRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListImageBaseRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListImageBaseRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListImageBaseRequestMultiError, or nil if none found.
func (m *ListImageBaseRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListImageBaseRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for WorkspaceName

	// no validation rules for Region

	// no validation rules for Name

	// no validation rules for Description

	// no validation rules for Page

	// no validation rules for PerPage

	// no validation rules for SpecificName

	// no validation rules for Creator

	if len(errors) > 0 {
		return ListImageBaseRequestMultiError(errors)
	}

	return nil
}

// ListImageBaseRequestMultiError is an error wrapping multiple validation
// errors returned by ListImageBaseRequest.ValidateAll() if the designated
// constraints aren't met.
type ListImageBaseRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListImageBaseRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListImageBaseRequestMultiError) AllErrors() []error { return m }

// ListImageBaseRequestValidationError is the validation error returned by
// ListImageBaseRequest.Validate if the designated constraints aren't met.
type ListImageBaseRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListImageBaseRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListImageBaseRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListImageBaseRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListImageBaseRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListImageBaseRequestValidationError) ErrorName() string {
	return "ListImageBaseRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListImageBaseRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListImageBaseRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListImageBaseRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListImageBaseRequestValidationError{}

// Validate checks the field values on ListImageBaseResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListImageBaseResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListImageBaseResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListImageBaseResponseMultiError, or nil if none found.
func (m *ListImageBaseResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListImageBaseResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetImages() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListImageBaseResponseValidationError{
						field:  fmt.Sprintf("Images[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListImageBaseResponseValidationError{
						field:  fmt.Sprintf("Images[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListImageBaseResponseValidationError{
					field:  fmt.Sprintf("Images[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Total

	if len(errors) > 0 {
		return ListImageBaseResponseMultiError(errors)
	}

	return nil
}

// ListImageBaseResponseMultiError is an error wrapping multiple validation
// errors returned by ListImageBaseResponse.ValidateAll() if the designated
// constraints aren't met.
type ListImageBaseResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListImageBaseResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListImageBaseResponseMultiError) AllErrors() []error { return m }

// ListImageBaseResponseValidationError is the validation error returned by
// ListImageBaseResponse.Validate if the designated constraints aren't met.
type ListImageBaseResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListImageBaseResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListImageBaseResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListImageBaseResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListImageBaseResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListImageBaseResponseValidationError) ErrorName() string {
	return "ListImageBaseResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListImageBaseResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListImageBaseResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListImageBaseResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListImageBaseResponseValidationError{}

// Validate checks the field values on ImageBase with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ImageBase) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ImageBase with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ImageBaseMultiError, or nil
// if none found.
func (m *ImageBase) ValidateAll() error {
	return m.validate(true)
}

func (m *ImageBase) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ID

	// no validation rules for WorkspaceName

	// no validation rules for Region

	// no validation rules for Name

	// no validation rules for Description

	// no validation rules for Creator

	// no validation rules for CreateTime

	if len(errors) > 0 {
		return ImageBaseMultiError(errors)
	}

	return nil
}

// ImageBaseMultiError is an error wrapping multiple validation errors returned
// by ImageBase.ValidateAll() if the designated constraints aren't met.
type ImageBaseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ImageBaseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ImageBaseMultiError) AllErrors() []error { return m }

// ImageBaseValidationError is the validation error returned by
// ImageBase.Validate if the designated constraints aren't met.
type ImageBaseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ImageBaseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ImageBaseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ImageBaseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ImageBaseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ImageBaseValidationError) ErrorName() string { return "ImageBaseValidationError" }

// Error satisfies the builtin error interface
func (e ImageBaseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sImageBase.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ImageBaseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ImageBaseValidationError{}

// Validate checks the field values on ListImageBuildHistoryRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListImageBuildHistoryRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListImageBuildHistoryRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListImageBuildHistoryRequestMultiError, or nil if none found.
func (m *ListImageBuildHistoryRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListImageBuildHistoryRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for WorkspaceName

	// no validation rules for Region

	// no validation rules for Name

	// no validation rules for Tag

	// no validation rules for Page

	// no validation rules for PerPage

	// no validation rules for JobName

	// no validation rules for Creator

	// no validation rules for Status

	if len(errors) > 0 {
		return ListImageBuildHistoryRequestMultiError(errors)
	}

	return nil
}

// ListImageBuildHistoryRequestMultiError is an error wrapping multiple
// validation errors returned by ListImageBuildHistoryRequest.ValidateAll() if
// the designated constraints aren't met.
type ListImageBuildHistoryRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListImageBuildHistoryRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListImageBuildHistoryRequestMultiError) AllErrors() []error { return m }

// ListImageBuildHistoryRequestValidationError is the validation error returned
// by ListImageBuildHistoryRequest.Validate if the designated constraints
// aren't met.
type ListImageBuildHistoryRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListImageBuildHistoryRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListImageBuildHistoryRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListImageBuildHistoryRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListImageBuildHistoryRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListImageBuildHistoryRequestValidationError) ErrorName() string {
	return "ListImageBuildHistoryRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListImageBuildHistoryRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListImageBuildHistoryRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListImageBuildHistoryRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListImageBuildHistoryRequestValidationError{}

// Validate checks the field values on ListImageBuildHistoryResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListImageBuildHistoryResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListImageBuildHistoryResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ListImageBuildHistoryResponseMultiError, or nil if none found.
func (m *ListImageBuildHistoryResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListImageBuildHistoryResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetImageBuildHistories() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListImageBuildHistoryResponseValidationError{
						field:  fmt.Sprintf("ImageBuildHistories[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListImageBuildHistoryResponseValidationError{
						field:  fmt.Sprintf("ImageBuildHistories[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListImageBuildHistoryResponseValidationError{
					field:  fmt.Sprintf("ImageBuildHistories[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Total

	if len(errors) > 0 {
		return ListImageBuildHistoryResponseMultiError(errors)
	}

	return nil
}

// ListImageBuildHistoryResponseMultiError is an error wrapping multiple
// validation errors returned by ListImageBuildHistoryResponse.ValidateAll()
// if the designated constraints aren't met.
type ListImageBuildHistoryResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListImageBuildHistoryResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListImageBuildHistoryResponseMultiError) AllErrors() []error { return m }

// ListImageBuildHistoryResponseValidationError is the validation error
// returned by ListImageBuildHistoryResponse.Validate if the designated
// constraints aren't met.
type ListImageBuildHistoryResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListImageBuildHistoryResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListImageBuildHistoryResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListImageBuildHistoryResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListImageBuildHistoryResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListImageBuildHistoryResponseValidationError) ErrorName() string {
	return "ListImageBuildHistoryResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListImageBuildHistoryResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListImageBuildHistoryResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListImageBuildHistoryResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListImageBuildHistoryResponseValidationError{}

// Validate checks the field values on ImageBuildHistory with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ImageBuildHistory) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ImageBuildHistory with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ImageBuildHistoryMultiError, or nil if none found.
func (m *ImageBuildHistory) ValidateAll() error {
	return m.validate(true)
}

func (m *ImageBuildHistory) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for Tag

	// no validation rules for QueueName

	// no validation rules for Description

	// no validation rules for WorkspaceName

	// no validation rules for Region

	// no validation rules for BuildType

	// no validation rules for BaseImage

	// no validation rules for DockerfileSource

	// no validation rules for PresetImage

	// no validation rules for CustomImage

	// no validation rules for ImageURL

	// no validation rules for Run

	// no validation rules for Cmd

	// no validation rules for GitUrl

	// no validation rules for BuildTime

	// no validation rules for FullPath

	// no validation rules for Content

	// no validation rules for DevID

	// no validation rules for ExcludePath

	// no validation rules for GitDir

	// no validation rules for Status

	// no validation rules for Reason

	// no validation rules for Creator

	// no validation rules for CreateTime

	// no validation rules for JobName

	// no validation rules for PodName

	// no validation rules for StartTime

	// no validation rules for EndTime

	// no validation rules for Cluster

	// no validation rules for LogFrom

	// no validation rules for LogTo

	// no validation rules for Msg

	for idx, item := range m.GetConditions() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ImageBuildHistoryValidationError{
						field:  fmt.Sprintf("Conditions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ImageBuildHistoryValidationError{
						field:  fmt.Sprintf("Conditions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ImageBuildHistoryValidationError{
					field:  fmt.Sprintf("Conditions[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for GitBranch

	// no validation rules for BuildArg

	// no validation rules for SshKey

	// no validation rules for UseCache

	// no validation rules for UseSpeedUp

	if len(errors) > 0 {
		return ImageBuildHistoryMultiError(errors)
	}

	return nil
}

// ImageBuildHistoryMultiError is an error wrapping multiple validation errors
// returned by ImageBuildHistory.ValidateAll() if the designated constraints
// aren't met.
type ImageBuildHistoryMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ImageBuildHistoryMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ImageBuildHistoryMultiError) AllErrors() []error { return m }

// ImageBuildHistoryValidationError is the validation error returned by
// ImageBuildHistory.Validate if the designated constraints aren't met.
type ImageBuildHistoryValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ImageBuildHistoryValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ImageBuildHistoryValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ImageBuildHistoryValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ImageBuildHistoryValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ImageBuildHistoryValidationError) ErrorName() string {
	return "ImageBuildHistoryValidationError"
}

// Error satisfies the builtin error interface
func (e ImageBuildHistoryValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sImageBuildHistory.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ImageBuildHistoryValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ImageBuildHistoryValidationError{}

// Validate checks the field values on ImageCondition with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ImageCondition) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ImageCondition with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ImageConditionMultiError,
// or nil if none found.
func (m *ImageCondition) ValidateAll() error {
	return m.validate(true)
}

func (m *ImageCondition) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Type

	// no validation rules for Status

	// no validation rules for LastTransitionTime

	// no validation rules for Reason

	// no validation rules for Message

	if len(errors) > 0 {
		return ImageConditionMultiError(errors)
	}

	return nil
}

// ImageConditionMultiError is an error wrapping multiple validation errors
// returned by ImageCondition.ValidateAll() if the designated constraints
// aren't met.
type ImageConditionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ImageConditionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ImageConditionMultiError) AllErrors() []error { return m }

// ImageConditionValidationError is the validation error returned by
// ImageCondition.Validate if the designated constraints aren't met.
type ImageConditionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ImageConditionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ImageConditionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ImageConditionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ImageConditionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ImageConditionValidationError) ErrorName() string { return "ImageConditionValidationError" }

// Error satisfies the builtin error interface
func (e ImageConditionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sImageCondition.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ImageConditionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ImageConditionValidationError{}

// Validate checks the field values on StopBuildImageRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StopBuildImageRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StopBuildImageRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StopBuildImageRequestMultiError, or nil if none found.
func (m *StopBuildImageRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *StopBuildImageRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for JobName

	// no validation rules for WorkspaceName

	// no validation rules for Region

	if len(errors) > 0 {
		return StopBuildImageRequestMultiError(errors)
	}

	return nil
}

// StopBuildImageRequestMultiError is an error wrapping multiple validation
// errors returned by StopBuildImageRequest.ValidateAll() if the designated
// constraints aren't met.
type StopBuildImageRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StopBuildImageRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StopBuildImageRequestMultiError) AllErrors() []error { return m }

// StopBuildImageRequestValidationError is the validation error returned by
// StopBuildImageRequest.Validate if the designated constraints aren't met.
type StopBuildImageRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StopBuildImageRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StopBuildImageRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StopBuildImageRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StopBuildImageRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StopBuildImageRequestValidationError) ErrorName() string {
	return "StopBuildImageRequestValidationError"
}

// Error satisfies the builtin error interface
func (e StopBuildImageRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStopBuildImageRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StopBuildImageRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StopBuildImageRequestValidationError{}

// Validate checks the field values on StopBuildImageResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StopBuildImageResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StopBuildImageResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StopBuildImageResponseMultiError, or nil if none found.
func (m *StopBuildImageResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *StopBuildImageResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Msg

	// no validation rules for Status

	if len(errors) > 0 {
		return StopBuildImageResponseMultiError(errors)
	}

	return nil
}

// StopBuildImageResponseMultiError is an error wrapping multiple validation
// errors returned by StopBuildImageResponse.ValidateAll() if the designated
// constraints aren't met.
type StopBuildImageResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StopBuildImageResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StopBuildImageResponseMultiError) AllErrors() []error { return m }

// StopBuildImageResponseValidationError is the validation error returned by
// StopBuildImageResponse.Validate if the designated constraints aren't met.
type StopBuildImageResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StopBuildImageResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StopBuildImageResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StopBuildImageResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StopBuildImageResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StopBuildImageResponseValidationError) ErrorName() string {
	return "StopBuildImageResponseValidationError"
}

// Error satisfies the builtin error interface
func (e StopBuildImageResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStopBuildImageResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StopBuildImageResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StopBuildImageResponseValidationError{}

// Validate checks the field values on CheckImageTagRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CheckImageTagRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckImageTagRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CheckImageTagRequestMultiError, or nil if none found.
func (m *CheckImageTagRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckImageTagRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Region

	// no validation rules for WorkspaceName

	// no validation rules for Name

	// no validation rules for Tag

	if len(errors) > 0 {
		return CheckImageTagRequestMultiError(errors)
	}

	return nil
}

// CheckImageTagRequestMultiError is an error wrapping multiple validation
// errors returned by CheckImageTagRequest.ValidateAll() if the designated
// constraints aren't met.
type CheckImageTagRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckImageTagRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckImageTagRequestMultiError) AllErrors() []error { return m }

// CheckImageTagRequestValidationError is the validation error returned by
// CheckImageTagRequest.Validate if the designated constraints aren't met.
type CheckImageTagRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckImageTagRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckImageTagRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckImageTagRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckImageTagRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckImageTagRequestValidationError) ErrorName() string {
	return "CheckImageTagRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CheckImageTagRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckImageTagRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckImageTagRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckImageTagRequestValidationError{}

// Validate checks the field values on CheckImageTagResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CheckImageTagResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckImageTagResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CheckImageTagResponseMultiError, or nil if none found.
func (m *CheckImageTagResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckImageTagResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	if len(errors) > 0 {
		return CheckImageTagResponseMultiError(errors)
	}

	return nil
}

// CheckImageTagResponseMultiError is an error wrapping multiple validation
// errors returned by CheckImageTagResponse.ValidateAll() if the designated
// constraints aren't met.
type CheckImageTagResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckImageTagResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckImageTagResponseMultiError) AllErrors() []error { return m }

// CheckImageTagResponseValidationError is the validation error returned by
// CheckImageTagResponse.Validate if the designated constraints aren't met.
type CheckImageTagResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckImageTagResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckImageTagResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckImageTagResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckImageTagResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckImageTagResponseValidationError) ErrorName() string {
	return "CheckImageTagResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CheckImageTagResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckImageTagResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckImageTagResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckImageTagResponseValidationError{}
