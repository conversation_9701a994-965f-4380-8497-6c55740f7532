// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.3
// - protoc             v5.29.3
// source: aistudio/imagehub/v1/imagehub.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationImageHubServiceAddImages = "/apis.aistudio.imagehub.v1.ImageHubService/AddImages"
const OperationImageHubServiceBuildImage = "/apis.aistudio.imagehub.v1.ImageHubService/BuildImage"
const OperationImageHubServiceCheckImageHubStatus = "/apis.aistudio.imagehub.v1.ImageHubService/CheckImageHubStatus"
const OperationImageHubServiceCheckImageTag = "/apis.aistudio.imagehub.v1.ImageHubService/CheckImageTag"
const OperationImageHubServiceCloseImageHub = "/apis.aistudio.imagehub.v1.ImageHubService/CloseImageHub"
const OperationImageHubServiceCreateImageBase = "/apis.aistudio.imagehub.v1.ImageHubService/CreateImageBase"
const OperationImageHubServiceCreatePreheatJob = "/apis.aistudio.imagehub.v1.ImageHubService/CreatePreheatJob"
const OperationImageHubServiceDeleteImage = "/apis.aistudio.imagehub.v1.ImageHubService/DeleteImage"
const OperationImageHubServiceDeleteImageBase = "/apis.aistudio.imagehub.v1.ImageHubService/DeleteImageBase"
const OperationImageHubServiceDeletePreheatJob = "/apis.aistudio.imagehub.v1.ImageHubService/DeletePreheatJob"
const OperationImageHubServiceGetImageTag = "/apis.aistudio.imagehub.v1.ImageHubService/GetImageTag"
const OperationImageHubServiceGetPreheatJob = "/apis.aistudio.imagehub.v1.ImageHubService/GetPreheatJob"
const OperationImageHubServiceGetServiceAccounts = "/apis.aistudio.imagehub.v1.ImageHubService/GetServiceAccounts"
const OperationImageHubServiceListImageBase = "/apis.aistudio.imagehub.v1.ImageHubService/ListImageBase"
const OperationImageHubServiceListImageBuildHistory = "/apis.aistudio.imagehub.v1.ImageHubService/ListImageBuildHistory"
const OperationImageHubServiceListImageNames = "/apis.aistudio.imagehub.v1.ImageHubService/ListImageNames"
const OperationImageHubServiceListImageTags = "/apis.aistudio.imagehub.v1.ImageHubService/ListImageTags"
const OperationImageHubServiceListImages = "/apis.aistudio.imagehub.v1.ImageHubService/ListImages"
const OperationImageHubServiceListPreheatJobs = "/apis.aistudio.imagehub.v1.ImageHubService/ListPreheatJobs"
const OperationImageHubServiceModifyImageDescription = "/apis.aistudio.imagehub.v1.ImageHubService/ModifyImageDescription"
const OperationImageHubServiceOpenImageHub = "/apis.aistudio.imagehub.v1.ImageHubService/OpenImageHub"
const OperationImageHubServiceRedeployPreheatJob = "/apis.aistudio.imagehub.v1.ImageHubService/RedeployPreheatJob"
const OperationImageHubServiceStopBuildImage = "/apis.aistudio.imagehub.v1.ImageHubService/StopBuildImage"
const OperationImageHubServiceUpdateImage = "/apis.aistudio.imagehub.v1.ImageHubService/UpdateImage"
const OperationImageHubServiceUpdateImageBase = "/apis.aistudio.imagehub.v1.ImageHubService/UpdateImageBase"

type ImageHubServiceHTTPServer interface {
	// AddImages 添加镜像
	AddImages(context.Context, *AddImagesRequest) (*emptypb.Empty, error)
	// BuildImage 构建镜像
	BuildImage(context.Context, *BuildImageRequest) (*BuildImageResponse, error)
	// CheckImageHubStatus 检查镜像中心是否开通
	CheckImageHubStatus(context.Context, *CheckImageHubStatusRequest) (*CheckImageHubStatusResponse, error)
	// CheckImageTag 查看tag是否存在
	CheckImageTag(context.Context, *CheckImageTagRequest) (*CheckImageTagResponse, error)
	// CloseImageHub 关闭镜像中心
	CloseImageHub(context.Context, *CloseImageHubRequest) (*emptypb.Empty, error)
	// CreateImageBase 创建镜像基础信息
	CreateImageBase(context.Context, *CreateImageBaseRequest) (*CreateImageBaseResponse, error)
	CreatePreheatJob(context.Context, *CreatePreheatJobRequest) (*emptypb.Empty, error)
	DeleteImage(context.Context, *DeleteImageRequest) (*emptypb.Empty, error)
	// DeleteImageBase 删除镜像基础信息
	DeleteImageBase(context.Context, *DeleteImageBaseRequest) (*DeleteImageBaseResponse, error)
	DeletePreheatJob(context.Context, *DeletePreheatJobRequest) (*emptypb.Empty, error)
	GetImageTag(context.Context, *GetImageTagRequest) (*Image, error)
	GetPreheatJob(context.Context, *GetPreheatJobRequest) (*PreheatJob, error)
	GetServiceAccounts(context.Context, *GetServiceAccountsRequest) (*ServiceAccounts, error)
	// ListImageBase 展示镜像列表
	ListImageBase(context.Context, *ListImageBaseRequest) (*ListImageBaseResponse, error)
	// ListImageBuildHistory 展示镜像构建历史
	ListImageBuildHistory(context.Context, *ListImageBuildHistoryRequest) (*ListImageBuildHistoryResponse, error)
	ListImageNames(context.Context, *ListImageNamesOptions) (*ListImageNamesResponse, error)
	ListImageTags(context.Context, *ListImageTagsOptions) (*ListImageTagsResponse, error)
	// ListImages 获取镜像列表
	ListImages(context.Context, *ListImagesOptions) (*ListImagesResponse, error)
	ListPreheatJobs(context.Context, *ListPreheatJobOptions) (*PreheatJobs, error)
	ModifyImageDescription(context.Context, *ModifyImageDescriptionRequest) (*emptypb.Empty, error)
	// OpenImageHub 开通镜像中心
	OpenImageHub(context.Context, *OpenImageHubRequest) (*emptypb.Empty, error)
	RedeployPreheatJob(context.Context, *RedeployPreheatJobRequest) (*emptypb.Empty, error)
	// StopBuildImage 停止构建
	StopBuildImage(context.Context, *StopBuildImageRequest) (*StopBuildImageResponse, error)
	UpdateImage(context.Context, *UpdateImageRequest) (*Image, error)
	// UpdateImageBase 更新镜像基础信息
	UpdateImageBase(context.Context, *UpdateImageBaseRequest) (*UpdateImageBaseResponse, error)
}

func RegisterImageHubServiceHTTPServer(s *http.Server, srv ImageHubServiceHTTPServer) {
	r := s.Route("/")
	r.GET("/apis/v1/image-hub/status", _ImageHubService_CheckImageHubStatus0_HTTP_Handler(srv))
	r.POST("/apis/v1/image-hub/open", _ImageHubService_OpenImageHub0_HTTP_Handler(srv))
	r.POST("/apis/v1/image-hub/close", _ImageHubService_CloseImageHub0_HTTP_Handler(srv))
	r.GET("/apis/v1/image-hub/images", _ImageHubService_ListImages0_HTTP_Handler(srv))
	r.POST("/apis/v1/image-hub/image", _ImageHubService_AddImages0_HTTP_Handler(srv))
	r.PUT("/apis/v1/image-hub/image/{id}", _ImageHubService_UpdateImage0_HTTP_Handler(srv))
	r.DELETE("/apis/v1/image-hub/image/{id}", _ImageHubService_DeleteImage0_HTTP_Handler(srv))
	r.GET("/apis/v1/image-hub/service-accounts", _ImageHubService_GetServiceAccounts0_HTTP_Handler(srv))
	r.POST("/apis/v1/image-hub/preheat-job", _ImageHubService_CreatePreheatJob0_HTTP_Handler(srv))
	r.GET("/apis/v1/image-hub/preheat-job/{id}", _ImageHubService_GetPreheatJob0_HTTP_Handler(srv))
	r.GET("/apis/v1/image-hub/preheat-jobs", _ImageHubService_ListPreheatJobs0_HTTP_Handler(srv))
	r.DELETE("/apis/v1/image-hub/preheat-job/{id}", _ImageHubService_DeletePreheatJob0_HTTP_Handler(srv))
	r.POST("/apis/v1/image-hub/preheat-job/{id}/redeploy", _ImageHubService_RedeployPreheatJob0_HTTP_Handler(srv))
	r.GET("/apis/v1/image-hub/image-names", _ImageHubService_ListImageNames0_HTTP_Handler(srv))
	r.GET("/apis/v1/image-hub/image-tags", _ImageHubService_ListImageTags0_HTTP_Handler(srv))
	r.GET("/apis/v1/image-hub/image/{imageName}/tag/{tag}", _ImageHubService_GetImageTag0_HTTP_Handler(srv))
	r.POST("/apis/v1/image-hub/image/{id}/description", _ImageHubService_ModifyImageDescription0_HTTP_Handler(srv))
	r.POST("/apis/v1/image-hub/image/build", _ImageHubService_BuildImage0_HTTP_Handler(srv))
	r.POST("/apis/v1/image-hub/imagebase", _ImageHubService_CreateImageBase0_HTTP_Handler(srv))
	r.PUT("/apis/v1/image-hub/imagebase", _ImageHubService_UpdateImageBase0_HTTP_Handler(srv))
	r.DELETE("/apis/v1/image-hub/imagebase", _ImageHubService_DeleteImageBase0_HTTP_Handler(srv))
	r.GET("/apis/v1/image-hub/imagebase", _ImageHubService_ListImageBase0_HTTP_Handler(srv))
	r.GET("/apis/v1/image-hub/imagebuildhistory", _ImageHubService_ListImageBuildHistory0_HTTP_Handler(srv))
	r.POST("/apis/v1/image-hub/stopbuildimage", _ImageHubService_StopBuildImage0_HTTP_Handler(srv))
	r.GET("/apis/v1/image-hub/image/check", _ImageHubService_CheckImageTag0_HTTP_Handler(srv))
}

func _ImageHubService_CheckImageHubStatus0_HTTP_Handler(srv ImageHubServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CheckImageHubStatusRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationImageHubServiceCheckImageHubStatus)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CheckImageHubStatus(ctx, req.(*CheckImageHubStatusRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CheckImageHubStatusResponse)
		return ctx.Result(200, reply)
	}
}

func _ImageHubService_OpenImageHub0_HTTP_Handler(srv ImageHubServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in OpenImageHubRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationImageHubServiceOpenImageHub)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.OpenImageHub(ctx, req.(*OpenImageHubRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _ImageHubService_CloseImageHub0_HTTP_Handler(srv ImageHubServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CloseImageHubRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationImageHubServiceCloseImageHub)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CloseImageHub(ctx, req.(*CloseImageHubRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _ImageHubService_ListImages0_HTTP_Handler(srv ImageHubServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListImagesOptions
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationImageHubServiceListImages)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListImages(ctx, req.(*ListImagesOptions))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListImagesResponse)
		return ctx.Result(200, reply)
	}
}

func _ImageHubService_AddImages0_HTTP_Handler(srv ImageHubServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in AddImagesRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationImageHubServiceAddImages)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.AddImages(ctx, req.(*AddImagesRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _ImageHubService_UpdateImage0_HTTP_Handler(srv ImageHubServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UpdateImageRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationImageHubServiceUpdateImage)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateImage(ctx, req.(*UpdateImageRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Image)
		return ctx.Result(200, reply)
	}
}

func _ImageHubService_DeleteImage0_HTTP_Handler(srv ImageHubServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteImageRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationImageHubServiceDeleteImage)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteImage(ctx, req.(*DeleteImageRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _ImageHubService_GetServiceAccounts0_HTTP_Handler(srv ImageHubServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetServiceAccountsRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationImageHubServiceGetServiceAccounts)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetServiceAccounts(ctx, req.(*GetServiceAccountsRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ServiceAccounts)
		return ctx.Result(200, reply)
	}
}

func _ImageHubService_CreatePreheatJob0_HTTP_Handler(srv ImageHubServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreatePreheatJobRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationImageHubServiceCreatePreheatJob)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreatePreheatJob(ctx, req.(*CreatePreheatJobRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _ImageHubService_GetPreheatJob0_HTTP_Handler(srv ImageHubServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetPreheatJobRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationImageHubServiceGetPreheatJob)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetPreheatJob(ctx, req.(*GetPreheatJobRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*PreheatJob)
		return ctx.Result(200, reply)
	}
}

func _ImageHubService_ListPreheatJobs0_HTTP_Handler(srv ImageHubServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListPreheatJobOptions
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationImageHubServiceListPreheatJobs)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListPreheatJobs(ctx, req.(*ListPreheatJobOptions))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*PreheatJobs)
		return ctx.Result(200, reply)
	}
}

func _ImageHubService_DeletePreheatJob0_HTTP_Handler(srv ImageHubServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeletePreheatJobRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationImageHubServiceDeletePreheatJob)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeletePreheatJob(ctx, req.(*DeletePreheatJobRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _ImageHubService_RedeployPreheatJob0_HTTP_Handler(srv ImageHubServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in RedeployPreheatJobRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationImageHubServiceRedeployPreheatJob)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.RedeployPreheatJob(ctx, req.(*RedeployPreheatJobRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _ImageHubService_ListImageNames0_HTTP_Handler(srv ImageHubServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListImageNamesOptions
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationImageHubServiceListImageNames)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListImageNames(ctx, req.(*ListImageNamesOptions))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListImageNamesResponse)
		return ctx.Result(200, reply)
	}
}

func _ImageHubService_ListImageTags0_HTTP_Handler(srv ImageHubServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListImageTagsOptions
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationImageHubServiceListImageTags)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListImageTags(ctx, req.(*ListImageTagsOptions))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListImageTagsResponse)
		return ctx.Result(200, reply)
	}
}

func _ImageHubService_GetImageTag0_HTTP_Handler(srv ImageHubServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetImageTagRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationImageHubServiceGetImageTag)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetImageTag(ctx, req.(*GetImageTagRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Image)
		return ctx.Result(200, reply)
	}
}

func _ImageHubService_ModifyImageDescription0_HTTP_Handler(srv ImageHubServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ModifyImageDescriptionRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationImageHubServiceModifyImageDescription)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ModifyImageDescription(ctx, req.(*ModifyImageDescriptionRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _ImageHubService_BuildImage0_HTTP_Handler(srv ImageHubServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in BuildImageRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationImageHubServiceBuildImage)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.BuildImage(ctx, req.(*BuildImageRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*BuildImageResponse)
		return ctx.Result(200, reply)
	}
}

func _ImageHubService_CreateImageBase0_HTTP_Handler(srv ImageHubServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateImageBaseRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationImageHubServiceCreateImageBase)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateImageBase(ctx, req.(*CreateImageBaseRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CreateImageBaseResponse)
		return ctx.Result(200, reply)
	}
}

func _ImageHubService_UpdateImageBase0_HTTP_Handler(srv ImageHubServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UpdateImageBaseRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationImageHubServiceUpdateImageBase)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateImageBase(ctx, req.(*UpdateImageBaseRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*UpdateImageBaseResponse)
		return ctx.Result(200, reply)
	}
}

func _ImageHubService_DeleteImageBase0_HTTP_Handler(srv ImageHubServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteImageBaseRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationImageHubServiceDeleteImageBase)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteImageBase(ctx, req.(*DeleteImageBaseRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DeleteImageBaseResponse)
		return ctx.Result(200, reply)
	}
}

func _ImageHubService_ListImageBase0_HTTP_Handler(srv ImageHubServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListImageBaseRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationImageHubServiceListImageBase)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListImageBase(ctx, req.(*ListImageBaseRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListImageBaseResponse)
		return ctx.Result(200, reply)
	}
}

func _ImageHubService_ListImageBuildHistory0_HTTP_Handler(srv ImageHubServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListImageBuildHistoryRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationImageHubServiceListImageBuildHistory)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListImageBuildHistory(ctx, req.(*ListImageBuildHistoryRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListImageBuildHistoryResponse)
		return ctx.Result(200, reply)
	}
}

func _ImageHubService_StopBuildImage0_HTTP_Handler(srv ImageHubServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in StopBuildImageRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationImageHubServiceStopBuildImage)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.StopBuildImage(ctx, req.(*StopBuildImageRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*StopBuildImageResponse)
		return ctx.Result(200, reply)
	}
}

func _ImageHubService_CheckImageTag0_HTTP_Handler(srv ImageHubServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CheckImageTagRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationImageHubServiceCheckImageTag)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CheckImageTag(ctx, req.(*CheckImageTagRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CheckImageTagResponse)
		return ctx.Result(200, reply)
	}
}

type ImageHubServiceHTTPClient interface {
	AddImages(ctx context.Context, req *AddImagesRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	BuildImage(ctx context.Context, req *BuildImageRequest, opts ...http.CallOption) (rsp *BuildImageResponse, err error)
	CheckImageHubStatus(ctx context.Context, req *CheckImageHubStatusRequest, opts ...http.CallOption) (rsp *CheckImageHubStatusResponse, err error)
	CheckImageTag(ctx context.Context, req *CheckImageTagRequest, opts ...http.CallOption) (rsp *CheckImageTagResponse, err error)
	CloseImageHub(ctx context.Context, req *CloseImageHubRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	CreateImageBase(ctx context.Context, req *CreateImageBaseRequest, opts ...http.CallOption) (rsp *CreateImageBaseResponse, err error)
	CreatePreheatJob(ctx context.Context, req *CreatePreheatJobRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	DeleteImage(ctx context.Context, req *DeleteImageRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	DeleteImageBase(ctx context.Context, req *DeleteImageBaseRequest, opts ...http.CallOption) (rsp *DeleteImageBaseResponse, err error)
	DeletePreheatJob(ctx context.Context, req *DeletePreheatJobRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	GetImageTag(ctx context.Context, req *GetImageTagRequest, opts ...http.CallOption) (rsp *Image, err error)
	GetPreheatJob(ctx context.Context, req *GetPreheatJobRequest, opts ...http.CallOption) (rsp *PreheatJob, err error)
	GetServiceAccounts(ctx context.Context, req *GetServiceAccountsRequest, opts ...http.CallOption) (rsp *ServiceAccounts, err error)
	ListImageBase(ctx context.Context, req *ListImageBaseRequest, opts ...http.CallOption) (rsp *ListImageBaseResponse, err error)
	ListImageBuildHistory(ctx context.Context, req *ListImageBuildHistoryRequest, opts ...http.CallOption) (rsp *ListImageBuildHistoryResponse, err error)
	ListImageNames(ctx context.Context, req *ListImageNamesOptions, opts ...http.CallOption) (rsp *ListImageNamesResponse, err error)
	ListImageTags(ctx context.Context, req *ListImageTagsOptions, opts ...http.CallOption) (rsp *ListImageTagsResponse, err error)
	ListImages(ctx context.Context, req *ListImagesOptions, opts ...http.CallOption) (rsp *ListImagesResponse, err error)
	ListPreheatJobs(ctx context.Context, req *ListPreheatJobOptions, opts ...http.CallOption) (rsp *PreheatJobs, err error)
	ModifyImageDescription(ctx context.Context, req *ModifyImageDescriptionRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	OpenImageHub(ctx context.Context, req *OpenImageHubRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	RedeployPreheatJob(ctx context.Context, req *RedeployPreheatJobRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	StopBuildImage(ctx context.Context, req *StopBuildImageRequest, opts ...http.CallOption) (rsp *StopBuildImageResponse, err error)
	UpdateImage(ctx context.Context, req *UpdateImageRequest, opts ...http.CallOption) (rsp *Image, err error)
	UpdateImageBase(ctx context.Context, req *UpdateImageBaseRequest, opts ...http.CallOption) (rsp *UpdateImageBaseResponse, err error)
}

type ImageHubServiceHTTPClientImpl struct {
	cc *http.Client
}

func NewImageHubServiceHTTPClient(client *http.Client) ImageHubServiceHTTPClient {
	return &ImageHubServiceHTTPClientImpl{client}
}

func (c *ImageHubServiceHTTPClientImpl) AddImages(ctx context.Context, in *AddImagesRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/apis/v1/image-hub/image"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationImageHubServiceAddImages))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ImageHubServiceHTTPClientImpl) BuildImage(ctx context.Context, in *BuildImageRequest, opts ...http.CallOption) (*BuildImageResponse, error) {
	var out BuildImageResponse
	pattern := "/apis/v1/image-hub/image/build"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationImageHubServiceBuildImage))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ImageHubServiceHTTPClientImpl) CheckImageHubStatus(ctx context.Context, in *CheckImageHubStatusRequest, opts ...http.CallOption) (*CheckImageHubStatusResponse, error) {
	var out CheckImageHubStatusResponse
	pattern := "/apis/v1/image-hub/status"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationImageHubServiceCheckImageHubStatus))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ImageHubServiceHTTPClientImpl) CheckImageTag(ctx context.Context, in *CheckImageTagRequest, opts ...http.CallOption) (*CheckImageTagResponse, error) {
	var out CheckImageTagResponse
	pattern := "/apis/v1/image-hub/image/check"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationImageHubServiceCheckImageTag))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ImageHubServiceHTTPClientImpl) CloseImageHub(ctx context.Context, in *CloseImageHubRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/apis/v1/image-hub/close"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationImageHubServiceCloseImageHub))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ImageHubServiceHTTPClientImpl) CreateImageBase(ctx context.Context, in *CreateImageBaseRequest, opts ...http.CallOption) (*CreateImageBaseResponse, error) {
	var out CreateImageBaseResponse
	pattern := "/apis/v1/image-hub/imagebase"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationImageHubServiceCreateImageBase))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ImageHubServiceHTTPClientImpl) CreatePreheatJob(ctx context.Context, in *CreatePreheatJobRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/apis/v1/image-hub/preheat-job"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationImageHubServiceCreatePreheatJob))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ImageHubServiceHTTPClientImpl) DeleteImage(ctx context.Context, in *DeleteImageRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/apis/v1/image-hub/image/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationImageHubServiceDeleteImage))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ImageHubServiceHTTPClientImpl) DeleteImageBase(ctx context.Context, in *DeleteImageBaseRequest, opts ...http.CallOption) (*DeleteImageBaseResponse, error) {
	var out DeleteImageBaseResponse
	pattern := "/apis/v1/image-hub/imagebase"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationImageHubServiceDeleteImageBase))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ImageHubServiceHTTPClientImpl) DeletePreheatJob(ctx context.Context, in *DeletePreheatJobRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/apis/v1/image-hub/preheat-job/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationImageHubServiceDeletePreheatJob))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ImageHubServiceHTTPClientImpl) GetImageTag(ctx context.Context, in *GetImageTagRequest, opts ...http.CallOption) (*Image, error) {
	var out Image
	pattern := "/apis/v1/image-hub/image/{imageName}/tag/{tag}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationImageHubServiceGetImageTag))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ImageHubServiceHTTPClientImpl) GetPreheatJob(ctx context.Context, in *GetPreheatJobRequest, opts ...http.CallOption) (*PreheatJob, error) {
	var out PreheatJob
	pattern := "/apis/v1/image-hub/preheat-job/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationImageHubServiceGetPreheatJob))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ImageHubServiceHTTPClientImpl) GetServiceAccounts(ctx context.Context, in *GetServiceAccountsRequest, opts ...http.CallOption) (*ServiceAccounts, error) {
	var out ServiceAccounts
	pattern := "/apis/v1/image-hub/service-accounts"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationImageHubServiceGetServiceAccounts))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ImageHubServiceHTTPClientImpl) ListImageBase(ctx context.Context, in *ListImageBaseRequest, opts ...http.CallOption) (*ListImageBaseResponse, error) {
	var out ListImageBaseResponse
	pattern := "/apis/v1/image-hub/imagebase"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationImageHubServiceListImageBase))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ImageHubServiceHTTPClientImpl) ListImageBuildHistory(ctx context.Context, in *ListImageBuildHistoryRequest, opts ...http.CallOption) (*ListImageBuildHistoryResponse, error) {
	var out ListImageBuildHistoryResponse
	pattern := "/apis/v1/image-hub/imagebuildhistory"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationImageHubServiceListImageBuildHistory))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ImageHubServiceHTTPClientImpl) ListImageNames(ctx context.Context, in *ListImageNamesOptions, opts ...http.CallOption) (*ListImageNamesResponse, error) {
	var out ListImageNamesResponse
	pattern := "/apis/v1/image-hub/image-names"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationImageHubServiceListImageNames))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ImageHubServiceHTTPClientImpl) ListImageTags(ctx context.Context, in *ListImageTagsOptions, opts ...http.CallOption) (*ListImageTagsResponse, error) {
	var out ListImageTagsResponse
	pattern := "/apis/v1/image-hub/image-tags"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationImageHubServiceListImageTags))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ImageHubServiceHTTPClientImpl) ListImages(ctx context.Context, in *ListImagesOptions, opts ...http.CallOption) (*ListImagesResponse, error) {
	var out ListImagesResponse
	pattern := "/apis/v1/image-hub/images"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationImageHubServiceListImages))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ImageHubServiceHTTPClientImpl) ListPreheatJobs(ctx context.Context, in *ListPreheatJobOptions, opts ...http.CallOption) (*PreheatJobs, error) {
	var out PreheatJobs
	pattern := "/apis/v1/image-hub/preheat-jobs"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationImageHubServiceListPreheatJobs))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ImageHubServiceHTTPClientImpl) ModifyImageDescription(ctx context.Context, in *ModifyImageDescriptionRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/apis/v1/image-hub/image/{id}/description"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationImageHubServiceModifyImageDescription))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ImageHubServiceHTTPClientImpl) OpenImageHub(ctx context.Context, in *OpenImageHubRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/apis/v1/image-hub/open"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationImageHubServiceOpenImageHub))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ImageHubServiceHTTPClientImpl) RedeployPreheatJob(ctx context.Context, in *RedeployPreheatJobRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/apis/v1/image-hub/preheat-job/{id}/redeploy"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationImageHubServiceRedeployPreheatJob))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ImageHubServiceHTTPClientImpl) StopBuildImage(ctx context.Context, in *StopBuildImageRequest, opts ...http.CallOption) (*StopBuildImageResponse, error) {
	var out StopBuildImageResponse
	pattern := "/apis/v1/image-hub/stopbuildimage"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationImageHubServiceStopBuildImage))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ImageHubServiceHTTPClientImpl) UpdateImage(ctx context.Context, in *UpdateImageRequest, opts ...http.CallOption) (*Image, error) {
	var out Image
	pattern := "/apis/v1/image-hub/image/{id}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationImageHubServiceUpdateImage))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ImageHubServiceHTTPClientImpl) UpdateImageBase(ctx context.Context, in *UpdateImageBaseRequest, opts ...http.CallOption) (*UpdateImageBaseResponse, error) {
	var out UpdateImageBaseResponse
	pattern := "/apis/v1/image-hub/imagebase"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationImageHubServiceUpdateImageBase))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
