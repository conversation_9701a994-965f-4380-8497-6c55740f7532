// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v5.29.3
// source: aistudio/imagehub/v1/imagehub.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	ImageHubService_CheckImageHubStatus_FullMethodName    = "/apis.aistudio.imagehub.v1.ImageHubService/CheckImageHubStatus"
	ImageHubService_OpenImageHub_FullMethodName           = "/apis.aistudio.imagehub.v1.ImageHubService/OpenImageHub"
	ImageHubService_CloseImageHub_FullMethodName          = "/apis.aistudio.imagehub.v1.ImageHubService/CloseImageHub"
	ImageHubService_ListImages_FullMethodName             = "/apis.aistudio.imagehub.v1.ImageHubService/ListImages"
	ImageHubService_AddImages_FullMethodName              = "/apis.aistudio.imagehub.v1.ImageHubService/AddImages"
	ImageHubService_UpdateImage_FullMethodName            = "/apis.aistudio.imagehub.v1.ImageHubService/UpdateImage"
	ImageHubService_DeleteImage_FullMethodName            = "/apis.aistudio.imagehub.v1.ImageHubService/DeleteImage"
	ImageHubService_GetServiceAccounts_FullMethodName     = "/apis.aistudio.imagehub.v1.ImageHubService/GetServiceAccounts"
	ImageHubService_CreatePreheatJob_FullMethodName       = "/apis.aistudio.imagehub.v1.ImageHubService/CreatePreheatJob"
	ImageHubService_GetPreheatJob_FullMethodName          = "/apis.aistudio.imagehub.v1.ImageHubService/GetPreheatJob"
	ImageHubService_ListPreheatJobs_FullMethodName        = "/apis.aistudio.imagehub.v1.ImageHubService/ListPreheatJobs"
	ImageHubService_DeletePreheatJob_FullMethodName       = "/apis.aistudio.imagehub.v1.ImageHubService/DeletePreheatJob"
	ImageHubService_RedeployPreheatJob_FullMethodName     = "/apis.aistudio.imagehub.v1.ImageHubService/RedeployPreheatJob"
	ImageHubService_ListImageNames_FullMethodName         = "/apis.aistudio.imagehub.v1.ImageHubService/ListImageNames"
	ImageHubService_ListImageTags_FullMethodName          = "/apis.aistudio.imagehub.v1.ImageHubService/ListImageTags"
	ImageHubService_GetImageTag_FullMethodName            = "/apis.aistudio.imagehub.v1.ImageHubService/GetImageTag"
	ImageHubService_ModifyImageDescription_FullMethodName = "/apis.aistudio.imagehub.v1.ImageHubService/ModifyImageDescription"
	ImageHubService_BuildImage_FullMethodName             = "/apis.aistudio.imagehub.v1.ImageHubService/BuildImage"
	ImageHubService_CreateImageBase_FullMethodName        = "/apis.aistudio.imagehub.v1.ImageHubService/CreateImageBase"
	ImageHubService_UpdateImageBase_FullMethodName        = "/apis.aistudio.imagehub.v1.ImageHubService/UpdateImageBase"
	ImageHubService_DeleteImageBase_FullMethodName        = "/apis.aistudio.imagehub.v1.ImageHubService/DeleteImageBase"
	ImageHubService_ListImageBase_FullMethodName          = "/apis.aistudio.imagehub.v1.ImageHubService/ListImageBase"
	ImageHubService_ListImageBuildHistory_FullMethodName  = "/apis.aistudio.imagehub.v1.ImageHubService/ListImageBuildHistory"
	ImageHubService_StopBuildImage_FullMethodName         = "/apis.aistudio.imagehub.v1.ImageHubService/StopBuildImage"
	ImageHubService_CheckImageTag_FullMethodName          = "/apis.aistudio.imagehub.v1.ImageHubService/CheckImageTag"
)

// ImageHubServiceClient is the client API for ImageHubService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ImageHubServiceClient interface {
	// 检查镜像中心是否开通
	CheckImageHubStatus(ctx context.Context, in *CheckImageHubStatusRequest, opts ...grpc.CallOption) (*CheckImageHubStatusResponse, error)
	// 开通镜像中心
	OpenImageHub(ctx context.Context, in *OpenImageHubRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 关闭镜像中心
	CloseImageHub(ctx context.Context, in *CloseImageHubRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 获取镜像列表
	ListImages(ctx context.Context, in *ListImagesOptions, opts ...grpc.CallOption) (*ListImagesResponse, error)
	// 添加镜像
	AddImages(ctx context.Context, in *AddImagesRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	UpdateImage(ctx context.Context, in *UpdateImageRequest, opts ...grpc.CallOption) (*Image, error)
	DeleteImage(ctx context.Context, in *DeleteImageRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	GetServiceAccounts(ctx context.Context, in *GetServiceAccountsRequest, opts ...grpc.CallOption) (*ServiceAccounts, error)
	CreatePreheatJob(ctx context.Context, in *CreatePreheatJobRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	GetPreheatJob(ctx context.Context, in *GetPreheatJobRequest, opts ...grpc.CallOption) (*PreheatJob, error)
	ListPreheatJobs(ctx context.Context, in *ListPreheatJobOptions, opts ...grpc.CallOption) (*PreheatJobs, error)
	DeletePreheatJob(ctx context.Context, in *DeletePreheatJobRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	RedeployPreheatJob(ctx context.Context, in *RedeployPreheatJobRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	ListImageNames(ctx context.Context, in *ListImageNamesOptions, opts ...grpc.CallOption) (*ListImageNamesResponse, error)
	ListImageTags(ctx context.Context, in *ListImageTagsOptions, opts ...grpc.CallOption) (*ListImageTagsResponse, error)
	GetImageTag(ctx context.Context, in *GetImageTagRequest, opts ...grpc.CallOption) (*Image, error)
	ModifyImageDescription(ctx context.Context, in *ModifyImageDescriptionRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 构建镜像
	BuildImage(ctx context.Context, in *BuildImageRequest, opts ...grpc.CallOption) (*BuildImageResponse, error)
	// 创建镜像基础信息
	CreateImageBase(ctx context.Context, in *CreateImageBaseRequest, opts ...grpc.CallOption) (*CreateImageBaseResponse, error)
	// 更新镜像基础信息
	UpdateImageBase(ctx context.Context, in *UpdateImageBaseRequest, opts ...grpc.CallOption) (*UpdateImageBaseResponse, error)
	// 删除镜像基础信息
	DeleteImageBase(ctx context.Context, in *DeleteImageBaseRequest, opts ...grpc.CallOption) (*DeleteImageBaseResponse, error)
	// 展示镜像列表
	ListImageBase(ctx context.Context, in *ListImageBaseRequest, opts ...grpc.CallOption) (*ListImageBaseResponse, error)
	// 展示镜像构建历史
	ListImageBuildHistory(ctx context.Context, in *ListImageBuildHistoryRequest, opts ...grpc.CallOption) (*ListImageBuildHistoryResponse, error)
	// 停止构建
	StopBuildImage(ctx context.Context, in *StopBuildImageRequest, opts ...grpc.CallOption) (*StopBuildImageResponse, error)
	// 查看tag是否存在
	CheckImageTag(ctx context.Context, in *CheckImageTagRequest, opts ...grpc.CallOption) (*CheckImageTagResponse, error)
}

type imageHubServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewImageHubServiceClient(cc grpc.ClientConnInterface) ImageHubServiceClient {
	return &imageHubServiceClient{cc}
}

func (c *imageHubServiceClient) CheckImageHubStatus(ctx context.Context, in *CheckImageHubStatusRequest, opts ...grpc.CallOption) (*CheckImageHubStatusResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CheckImageHubStatusResponse)
	err := c.cc.Invoke(ctx, ImageHubService_CheckImageHubStatus_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *imageHubServiceClient) OpenImageHub(ctx context.Context, in *OpenImageHubRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, ImageHubService_OpenImageHub_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *imageHubServiceClient) CloseImageHub(ctx context.Context, in *CloseImageHubRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, ImageHubService_CloseImageHub_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *imageHubServiceClient) ListImages(ctx context.Context, in *ListImagesOptions, opts ...grpc.CallOption) (*ListImagesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListImagesResponse)
	err := c.cc.Invoke(ctx, ImageHubService_ListImages_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *imageHubServiceClient) AddImages(ctx context.Context, in *AddImagesRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, ImageHubService_AddImages_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *imageHubServiceClient) UpdateImage(ctx context.Context, in *UpdateImageRequest, opts ...grpc.CallOption) (*Image, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(Image)
	err := c.cc.Invoke(ctx, ImageHubService_UpdateImage_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *imageHubServiceClient) DeleteImage(ctx context.Context, in *DeleteImageRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, ImageHubService_DeleteImage_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *imageHubServiceClient) GetServiceAccounts(ctx context.Context, in *GetServiceAccountsRequest, opts ...grpc.CallOption) (*ServiceAccounts, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ServiceAccounts)
	err := c.cc.Invoke(ctx, ImageHubService_GetServiceAccounts_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *imageHubServiceClient) CreatePreheatJob(ctx context.Context, in *CreatePreheatJobRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, ImageHubService_CreatePreheatJob_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *imageHubServiceClient) GetPreheatJob(ctx context.Context, in *GetPreheatJobRequest, opts ...grpc.CallOption) (*PreheatJob, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PreheatJob)
	err := c.cc.Invoke(ctx, ImageHubService_GetPreheatJob_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *imageHubServiceClient) ListPreheatJobs(ctx context.Context, in *ListPreheatJobOptions, opts ...grpc.CallOption) (*PreheatJobs, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PreheatJobs)
	err := c.cc.Invoke(ctx, ImageHubService_ListPreheatJobs_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *imageHubServiceClient) DeletePreheatJob(ctx context.Context, in *DeletePreheatJobRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, ImageHubService_DeletePreheatJob_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *imageHubServiceClient) RedeployPreheatJob(ctx context.Context, in *RedeployPreheatJobRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, ImageHubService_RedeployPreheatJob_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *imageHubServiceClient) ListImageNames(ctx context.Context, in *ListImageNamesOptions, opts ...grpc.CallOption) (*ListImageNamesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListImageNamesResponse)
	err := c.cc.Invoke(ctx, ImageHubService_ListImageNames_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *imageHubServiceClient) ListImageTags(ctx context.Context, in *ListImageTagsOptions, opts ...grpc.CallOption) (*ListImageTagsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListImageTagsResponse)
	err := c.cc.Invoke(ctx, ImageHubService_ListImageTags_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *imageHubServiceClient) GetImageTag(ctx context.Context, in *GetImageTagRequest, opts ...grpc.CallOption) (*Image, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(Image)
	err := c.cc.Invoke(ctx, ImageHubService_GetImageTag_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *imageHubServiceClient) ModifyImageDescription(ctx context.Context, in *ModifyImageDescriptionRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, ImageHubService_ModifyImageDescription_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *imageHubServiceClient) BuildImage(ctx context.Context, in *BuildImageRequest, opts ...grpc.CallOption) (*BuildImageResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BuildImageResponse)
	err := c.cc.Invoke(ctx, ImageHubService_BuildImage_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *imageHubServiceClient) CreateImageBase(ctx context.Context, in *CreateImageBaseRequest, opts ...grpc.CallOption) (*CreateImageBaseResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateImageBaseResponse)
	err := c.cc.Invoke(ctx, ImageHubService_CreateImageBase_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *imageHubServiceClient) UpdateImageBase(ctx context.Context, in *UpdateImageBaseRequest, opts ...grpc.CallOption) (*UpdateImageBaseResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateImageBaseResponse)
	err := c.cc.Invoke(ctx, ImageHubService_UpdateImageBase_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *imageHubServiceClient) DeleteImageBase(ctx context.Context, in *DeleteImageBaseRequest, opts ...grpc.CallOption) (*DeleteImageBaseResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteImageBaseResponse)
	err := c.cc.Invoke(ctx, ImageHubService_DeleteImageBase_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *imageHubServiceClient) ListImageBase(ctx context.Context, in *ListImageBaseRequest, opts ...grpc.CallOption) (*ListImageBaseResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListImageBaseResponse)
	err := c.cc.Invoke(ctx, ImageHubService_ListImageBase_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *imageHubServiceClient) ListImageBuildHistory(ctx context.Context, in *ListImageBuildHistoryRequest, opts ...grpc.CallOption) (*ListImageBuildHistoryResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListImageBuildHistoryResponse)
	err := c.cc.Invoke(ctx, ImageHubService_ListImageBuildHistory_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *imageHubServiceClient) StopBuildImage(ctx context.Context, in *StopBuildImageRequest, opts ...grpc.CallOption) (*StopBuildImageResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(StopBuildImageResponse)
	err := c.cc.Invoke(ctx, ImageHubService_StopBuildImage_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *imageHubServiceClient) CheckImageTag(ctx context.Context, in *CheckImageTagRequest, opts ...grpc.CallOption) (*CheckImageTagResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CheckImageTagResponse)
	err := c.cc.Invoke(ctx, ImageHubService_CheckImageTag_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ImageHubServiceServer is the server API for ImageHubService service.
// All implementations must embed UnimplementedImageHubServiceServer
// for forward compatibility
type ImageHubServiceServer interface {
	// 检查镜像中心是否开通
	CheckImageHubStatus(context.Context, *CheckImageHubStatusRequest) (*CheckImageHubStatusResponse, error)
	// 开通镜像中心
	OpenImageHub(context.Context, *OpenImageHubRequest) (*emptypb.Empty, error)
	// 关闭镜像中心
	CloseImageHub(context.Context, *CloseImageHubRequest) (*emptypb.Empty, error)
	// 获取镜像列表
	ListImages(context.Context, *ListImagesOptions) (*ListImagesResponse, error)
	// 添加镜像
	AddImages(context.Context, *AddImagesRequest) (*emptypb.Empty, error)
	UpdateImage(context.Context, *UpdateImageRequest) (*Image, error)
	DeleteImage(context.Context, *DeleteImageRequest) (*emptypb.Empty, error)
	GetServiceAccounts(context.Context, *GetServiceAccountsRequest) (*ServiceAccounts, error)
	CreatePreheatJob(context.Context, *CreatePreheatJobRequest) (*emptypb.Empty, error)
	GetPreheatJob(context.Context, *GetPreheatJobRequest) (*PreheatJob, error)
	ListPreheatJobs(context.Context, *ListPreheatJobOptions) (*PreheatJobs, error)
	DeletePreheatJob(context.Context, *DeletePreheatJobRequest) (*emptypb.Empty, error)
	RedeployPreheatJob(context.Context, *RedeployPreheatJobRequest) (*emptypb.Empty, error)
	ListImageNames(context.Context, *ListImageNamesOptions) (*ListImageNamesResponse, error)
	ListImageTags(context.Context, *ListImageTagsOptions) (*ListImageTagsResponse, error)
	GetImageTag(context.Context, *GetImageTagRequest) (*Image, error)
	ModifyImageDescription(context.Context, *ModifyImageDescriptionRequest) (*emptypb.Empty, error)
	// 构建镜像
	BuildImage(context.Context, *BuildImageRequest) (*BuildImageResponse, error)
	// 创建镜像基础信息
	CreateImageBase(context.Context, *CreateImageBaseRequest) (*CreateImageBaseResponse, error)
	// 更新镜像基础信息
	UpdateImageBase(context.Context, *UpdateImageBaseRequest) (*UpdateImageBaseResponse, error)
	// 删除镜像基础信息
	DeleteImageBase(context.Context, *DeleteImageBaseRequest) (*DeleteImageBaseResponse, error)
	// 展示镜像列表
	ListImageBase(context.Context, *ListImageBaseRequest) (*ListImageBaseResponse, error)
	// 展示镜像构建历史
	ListImageBuildHistory(context.Context, *ListImageBuildHistoryRequest) (*ListImageBuildHistoryResponse, error)
	// 停止构建
	StopBuildImage(context.Context, *StopBuildImageRequest) (*StopBuildImageResponse, error)
	// 查看tag是否存在
	CheckImageTag(context.Context, *CheckImageTagRequest) (*CheckImageTagResponse, error)
	mustEmbedUnimplementedImageHubServiceServer()
}

// UnimplementedImageHubServiceServer must be embedded to have forward compatible implementations.
type UnimplementedImageHubServiceServer struct {
}

func (UnimplementedImageHubServiceServer) CheckImageHubStatus(context.Context, *CheckImageHubStatusRequest) (*CheckImageHubStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckImageHubStatus not implemented")
}
func (UnimplementedImageHubServiceServer) OpenImageHub(context.Context, *OpenImageHubRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OpenImageHub not implemented")
}
func (UnimplementedImageHubServiceServer) CloseImageHub(context.Context, *CloseImageHubRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CloseImageHub not implemented")
}
func (UnimplementedImageHubServiceServer) ListImages(context.Context, *ListImagesOptions) (*ListImagesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListImages not implemented")
}
func (UnimplementedImageHubServiceServer) AddImages(context.Context, *AddImagesRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddImages not implemented")
}
func (UnimplementedImageHubServiceServer) UpdateImage(context.Context, *UpdateImageRequest) (*Image, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateImage not implemented")
}
func (UnimplementedImageHubServiceServer) DeleteImage(context.Context, *DeleteImageRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteImage not implemented")
}
func (UnimplementedImageHubServiceServer) GetServiceAccounts(context.Context, *GetServiceAccountsRequest) (*ServiceAccounts, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetServiceAccounts not implemented")
}
func (UnimplementedImageHubServiceServer) CreatePreheatJob(context.Context, *CreatePreheatJobRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreatePreheatJob not implemented")
}
func (UnimplementedImageHubServiceServer) GetPreheatJob(context.Context, *GetPreheatJobRequest) (*PreheatJob, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPreheatJob not implemented")
}
func (UnimplementedImageHubServiceServer) ListPreheatJobs(context.Context, *ListPreheatJobOptions) (*PreheatJobs, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListPreheatJobs not implemented")
}
func (UnimplementedImageHubServiceServer) DeletePreheatJob(context.Context, *DeletePreheatJobRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeletePreheatJob not implemented")
}
func (UnimplementedImageHubServiceServer) RedeployPreheatJob(context.Context, *RedeployPreheatJobRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RedeployPreheatJob not implemented")
}
func (UnimplementedImageHubServiceServer) ListImageNames(context.Context, *ListImageNamesOptions) (*ListImageNamesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListImageNames not implemented")
}
func (UnimplementedImageHubServiceServer) ListImageTags(context.Context, *ListImageTagsOptions) (*ListImageTagsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListImageTags not implemented")
}
func (UnimplementedImageHubServiceServer) GetImageTag(context.Context, *GetImageTagRequest) (*Image, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetImageTag not implemented")
}
func (UnimplementedImageHubServiceServer) ModifyImageDescription(context.Context, *ModifyImageDescriptionRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ModifyImageDescription not implemented")
}
func (UnimplementedImageHubServiceServer) BuildImage(context.Context, *BuildImageRequest) (*BuildImageResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BuildImage not implemented")
}
func (UnimplementedImageHubServiceServer) CreateImageBase(context.Context, *CreateImageBaseRequest) (*CreateImageBaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateImageBase not implemented")
}
func (UnimplementedImageHubServiceServer) UpdateImageBase(context.Context, *UpdateImageBaseRequest) (*UpdateImageBaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateImageBase not implemented")
}
func (UnimplementedImageHubServiceServer) DeleteImageBase(context.Context, *DeleteImageBaseRequest) (*DeleteImageBaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteImageBase not implemented")
}
func (UnimplementedImageHubServiceServer) ListImageBase(context.Context, *ListImageBaseRequest) (*ListImageBaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListImageBase not implemented")
}
func (UnimplementedImageHubServiceServer) ListImageBuildHistory(context.Context, *ListImageBuildHistoryRequest) (*ListImageBuildHistoryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListImageBuildHistory not implemented")
}
func (UnimplementedImageHubServiceServer) StopBuildImage(context.Context, *StopBuildImageRequest) (*StopBuildImageResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StopBuildImage not implemented")
}
func (UnimplementedImageHubServiceServer) CheckImageTag(context.Context, *CheckImageTagRequest) (*CheckImageTagResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckImageTag not implemented")
}
func (UnimplementedImageHubServiceServer) mustEmbedUnimplementedImageHubServiceServer() {}

// UnsafeImageHubServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ImageHubServiceServer will
// result in compilation errors.
type UnsafeImageHubServiceServer interface {
	mustEmbedUnimplementedImageHubServiceServer()
}

func RegisterImageHubServiceServer(s grpc.ServiceRegistrar, srv ImageHubServiceServer) {
	s.RegisterService(&ImageHubService_ServiceDesc, srv)
}

func _ImageHubService_CheckImageHubStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckImageHubStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ImageHubServiceServer).CheckImageHubStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ImageHubService_CheckImageHubStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ImageHubServiceServer).CheckImageHubStatus(ctx, req.(*CheckImageHubStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ImageHubService_OpenImageHub_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OpenImageHubRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ImageHubServiceServer).OpenImageHub(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ImageHubService_OpenImageHub_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ImageHubServiceServer).OpenImageHub(ctx, req.(*OpenImageHubRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ImageHubService_CloseImageHub_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CloseImageHubRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ImageHubServiceServer).CloseImageHub(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ImageHubService_CloseImageHub_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ImageHubServiceServer).CloseImageHub(ctx, req.(*CloseImageHubRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ImageHubService_ListImages_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListImagesOptions)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ImageHubServiceServer).ListImages(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ImageHubService_ListImages_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ImageHubServiceServer).ListImages(ctx, req.(*ListImagesOptions))
	}
	return interceptor(ctx, in, info, handler)
}

func _ImageHubService_AddImages_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddImagesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ImageHubServiceServer).AddImages(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ImageHubService_AddImages_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ImageHubServiceServer).AddImages(ctx, req.(*AddImagesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ImageHubService_UpdateImage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateImageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ImageHubServiceServer).UpdateImage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ImageHubService_UpdateImage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ImageHubServiceServer).UpdateImage(ctx, req.(*UpdateImageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ImageHubService_DeleteImage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteImageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ImageHubServiceServer).DeleteImage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ImageHubService_DeleteImage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ImageHubServiceServer).DeleteImage(ctx, req.(*DeleteImageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ImageHubService_GetServiceAccounts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetServiceAccountsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ImageHubServiceServer).GetServiceAccounts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ImageHubService_GetServiceAccounts_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ImageHubServiceServer).GetServiceAccounts(ctx, req.(*GetServiceAccountsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ImageHubService_CreatePreheatJob_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreatePreheatJobRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ImageHubServiceServer).CreatePreheatJob(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ImageHubService_CreatePreheatJob_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ImageHubServiceServer).CreatePreheatJob(ctx, req.(*CreatePreheatJobRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ImageHubService_GetPreheatJob_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPreheatJobRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ImageHubServiceServer).GetPreheatJob(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ImageHubService_GetPreheatJob_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ImageHubServiceServer).GetPreheatJob(ctx, req.(*GetPreheatJobRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ImageHubService_ListPreheatJobs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListPreheatJobOptions)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ImageHubServiceServer).ListPreheatJobs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ImageHubService_ListPreheatJobs_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ImageHubServiceServer).ListPreheatJobs(ctx, req.(*ListPreheatJobOptions))
	}
	return interceptor(ctx, in, info, handler)
}

func _ImageHubService_DeletePreheatJob_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeletePreheatJobRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ImageHubServiceServer).DeletePreheatJob(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ImageHubService_DeletePreheatJob_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ImageHubServiceServer).DeletePreheatJob(ctx, req.(*DeletePreheatJobRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ImageHubService_RedeployPreheatJob_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RedeployPreheatJobRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ImageHubServiceServer).RedeployPreheatJob(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ImageHubService_RedeployPreheatJob_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ImageHubServiceServer).RedeployPreheatJob(ctx, req.(*RedeployPreheatJobRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ImageHubService_ListImageNames_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListImageNamesOptions)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ImageHubServiceServer).ListImageNames(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ImageHubService_ListImageNames_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ImageHubServiceServer).ListImageNames(ctx, req.(*ListImageNamesOptions))
	}
	return interceptor(ctx, in, info, handler)
}

func _ImageHubService_ListImageTags_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListImageTagsOptions)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ImageHubServiceServer).ListImageTags(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ImageHubService_ListImageTags_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ImageHubServiceServer).ListImageTags(ctx, req.(*ListImageTagsOptions))
	}
	return interceptor(ctx, in, info, handler)
}

func _ImageHubService_GetImageTag_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetImageTagRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ImageHubServiceServer).GetImageTag(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ImageHubService_GetImageTag_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ImageHubServiceServer).GetImageTag(ctx, req.(*GetImageTagRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ImageHubService_ModifyImageDescription_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModifyImageDescriptionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ImageHubServiceServer).ModifyImageDescription(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ImageHubService_ModifyImageDescription_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ImageHubServiceServer).ModifyImageDescription(ctx, req.(*ModifyImageDescriptionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ImageHubService_BuildImage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BuildImageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ImageHubServiceServer).BuildImage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ImageHubService_BuildImage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ImageHubServiceServer).BuildImage(ctx, req.(*BuildImageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ImageHubService_CreateImageBase_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateImageBaseRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ImageHubServiceServer).CreateImageBase(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ImageHubService_CreateImageBase_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ImageHubServiceServer).CreateImageBase(ctx, req.(*CreateImageBaseRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ImageHubService_UpdateImageBase_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateImageBaseRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ImageHubServiceServer).UpdateImageBase(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ImageHubService_UpdateImageBase_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ImageHubServiceServer).UpdateImageBase(ctx, req.(*UpdateImageBaseRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ImageHubService_DeleteImageBase_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteImageBaseRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ImageHubServiceServer).DeleteImageBase(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ImageHubService_DeleteImageBase_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ImageHubServiceServer).DeleteImageBase(ctx, req.(*DeleteImageBaseRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ImageHubService_ListImageBase_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListImageBaseRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ImageHubServiceServer).ListImageBase(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ImageHubService_ListImageBase_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ImageHubServiceServer).ListImageBase(ctx, req.(*ListImageBaseRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ImageHubService_ListImageBuildHistory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListImageBuildHistoryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ImageHubServiceServer).ListImageBuildHistory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ImageHubService_ListImageBuildHistory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ImageHubServiceServer).ListImageBuildHistory(ctx, req.(*ListImageBuildHistoryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ImageHubService_StopBuildImage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StopBuildImageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ImageHubServiceServer).StopBuildImage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ImageHubService_StopBuildImage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ImageHubServiceServer).StopBuildImage(ctx, req.(*StopBuildImageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ImageHubService_CheckImageTag_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckImageTagRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ImageHubServiceServer).CheckImageTag(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ImageHubService_CheckImageTag_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ImageHubServiceServer).CheckImageTag(ctx, req.(*CheckImageTagRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// ImageHubService_ServiceDesc is the grpc.ServiceDesc for ImageHubService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ImageHubService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "apis.aistudio.imagehub.v1.ImageHubService",
	HandlerType: (*ImageHubServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CheckImageHubStatus",
			Handler:    _ImageHubService_CheckImageHubStatus_Handler,
		},
		{
			MethodName: "OpenImageHub",
			Handler:    _ImageHubService_OpenImageHub_Handler,
		},
		{
			MethodName: "CloseImageHub",
			Handler:    _ImageHubService_CloseImageHub_Handler,
		},
		{
			MethodName: "ListImages",
			Handler:    _ImageHubService_ListImages_Handler,
		},
		{
			MethodName: "AddImages",
			Handler:    _ImageHubService_AddImages_Handler,
		},
		{
			MethodName: "UpdateImage",
			Handler:    _ImageHubService_UpdateImage_Handler,
		},
		{
			MethodName: "DeleteImage",
			Handler:    _ImageHubService_DeleteImage_Handler,
		},
		{
			MethodName: "GetServiceAccounts",
			Handler:    _ImageHubService_GetServiceAccounts_Handler,
		},
		{
			MethodName: "CreatePreheatJob",
			Handler:    _ImageHubService_CreatePreheatJob_Handler,
		},
		{
			MethodName: "GetPreheatJob",
			Handler:    _ImageHubService_GetPreheatJob_Handler,
		},
		{
			MethodName: "ListPreheatJobs",
			Handler:    _ImageHubService_ListPreheatJobs_Handler,
		},
		{
			MethodName: "DeletePreheatJob",
			Handler:    _ImageHubService_DeletePreheatJob_Handler,
		},
		{
			MethodName: "RedeployPreheatJob",
			Handler:    _ImageHubService_RedeployPreheatJob_Handler,
		},
		{
			MethodName: "ListImageNames",
			Handler:    _ImageHubService_ListImageNames_Handler,
		},
		{
			MethodName: "ListImageTags",
			Handler:    _ImageHubService_ListImageTags_Handler,
		},
		{
			MethodName: "GetImageTag",
			Handler:    _ImageHubService_GetImageTag_Handler,
		},
		{
			MethodName: "ModifyImageDescription",
			Handler:    _ImageHubService_ModifyImageDescription_Handler,
		},
		{
			MethodName: "BuildImage",
			Handler:    _ImageHubService_BuildImage_Handler,
		},
		{
			MethodName: "CreateImageBase",
			Handler:    _ImageHubService_CreateImageBase_Handler,
		},
		{
			MethodName: "UpdateImageBase",
			Handler:    _ImageHubService_UpdateImageBase_Handler,
		},
		{
			MethodName: "DeleteImageBase",
			Handler:    _ImageHubService_DeleteImageBase_Handler,
		},
		{
			MethodName: "ListImageBase",
			Handler:    _ImageHubService_ListImageBase_Handler,
		},
		{
			MethodName: "ListImageBuildHistory",
			Handler:    _ImageHubService_ListImageBuildHistory_Handler,
		},
		{
			MethodName: "StopBuildImage",
			Handler:    _ImageHubService_StopBuildImage_Handler,
		},
		{
			MethodName: "CheckImageTag",
			Handler:    _ImageHubService_CheckImageTag_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "aistudio/imagehub/v1/imagehub.proto",
}
