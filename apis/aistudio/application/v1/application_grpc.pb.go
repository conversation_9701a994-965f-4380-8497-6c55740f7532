// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v5.29.3
// source: aistudio/application/v1/application.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	ApplicationService_CreateApplication_FullMethodName               = "/apis.aistudio.application.v1.ApplicationService/CreateApplication"
	ApplicationService_UpdateApplication_FullMethodName               = "/apis.aistudio.application.v1.ApplicationService/UpdateApplication"
	ApplicationService_DeleteApplication_FullMethodName               = "/apis.aistudio.application.v1.ApplicationService/DeleteApplication"
	ApplicationService_ListApplications_FullMethodName                = "/apis.aistudio.application.v1.ApplicationService/ListApplications"
	ApplicationService_CreateDeploymentGroup_FullMethodName           = "/apis.aistudio.application.v1.ApplicationService/CreateDeploymentGroup"
	ApplicationService_UpdateDeploymentGroup_FullMethodName           = "/apis.aistudio.application.v1.ApplicationService/UpdateDeploymentGroup"
	ApplicationService_ScaleDeploymentGroup_FullMethodName            = "/apis.aistudio.application.v1.ApplicationService/ScaleDeploymentGroup"
	ApplicationService_DeployDeploymentGroup_FullMethodName           = "/apis.aistudio.application.v1.ApplicationService/DeployDeploymentGroup"
	ApplicationService_DeleteDeploymentGroup_FullMethodName           = "/apis.aistudio.application.v1.ApplicationService/DeleteDeploymentGroup"
	ApplicationService_ListDeploymentGroups_FullMethodName            = "/apis.aistudio.application.v1.ApplicationService/ListDeploymentGroups"
	ApplicationService_GetApplicationBase_FullMethodName              = "/apis.aistudio.application.v1.ApplicationService/GetApplicationBase"
	ApplicationService_ListDeploymentGroupRecords_FullMethodName      = "/apis.aistudio.application.v1.ApplicationService/ListDeploymentGroupRecords"
	ApplicationService_DetailDeploymentGroupRecord_FullMethodName     = "/apis.aistudio.application.v1.ApplicationService/DetailDeploymentGroupRecord"
	ApplicationService_GetDeploymentGroupDetail_FullMethodName        = "/apis.aistudio.application.v1.ApplicationService/GetDeploymentGroupDetail"
	ApplicationService_ListDeploymentGroupRevisions_FullMethodName    = "/apis.aistudio.application.v1.ApplicationService/ListDeploymentGroupRevisions"
	ApplicationService_DetailDeploymentGroupRevision_FullMethodName   = "/apis.aistudio.application.v1.ApplicationService/DetailDeploymentGroupRevision"
	ApplicationService_RollbackDeploymentGroupRevision_FullMethodName = "/apis.aistudio.application.v1.ApplicationService/RollbackDeploymentGroupRevision"
	ApplicationService_RollbackDeploymentGroupRecord_FullMethodName   = "/apis.aistudio.application.v1.ApplicationService/RollbackDeploymentGroupRecord"
	ApplicationService_TerminateDeploymentGroupRecord_FullMethodName  = "/apis.aistudio.application.v1.ApplicationService/TerminateDeploymentGroupRecord"
	ApplicationService_ResumeDeploymentGroupRecord_FullMethodName     = "/apis.aistudio.application.v1.ApplicationService/ResumeDeploymentGroupRecord"
	ApplicationService_GetDeploymentGroupRecordLogs_FullMethodName    = "/apis.aistudio.application.v1.ApplicationService/GetDeploymentGroupRecordLogs"
	ApplicationService_RecycleDeploymentGroupWorkload_FullMethodName  = "/apis.aistudio.application.v1.ApplicationService/RecycleDeploymentGroupWorkload"
	ApplicationService_GetDeploymentGroupVolumes_FullMethodName       = "/apis.aistudio.application.v1.ApplicationService/GetDeploymentGroupVolumes"
	ApplicationService_ListServiceExports_FullMethodName              = "/apis.aistudio.application.v1.ApplicationService/ListServiceExports"
)

// ApplicationServiceClient is the client API for ApplicationService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ApplicationServiceClient interface {
	CreateApplication(ctx context.Context, in *CreateApplicationRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	UpdateApplication(ctx context.Context, in *UpdateApplicationRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	DeleteApplication(ctx context.Context, in *DeleteApplicationRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	ListApplications(ctx context.Context, in *ListApplicationOptions, opts ...grpc.CallOption) (*ListApplicationResult, error)
	// 新增分布式推理
	CreateDeploymentGroup(ctx context.Context, in *CreateDeploymentGroupRequest, opts ...grpc.CallOption) (*DeploymentGroup, error)
	UpdateDeploymentGroup(ctx context.Context, in *UpdateDeploymentGroupRequest, opts ...grpc.CallOption) (*DeploymentGroup, error)
	// 扩缩容
	ScaleDeploymentGroup(ctx context.Context, in *ScaleDeploymentGroupRequest, opts ...grpc.CallOption) (*ScaleDeploymentGroupResponse, error)
	DeployDeploymentGroup(ctx context.Context, in *DeployRequest, opts ...grpc.CallOption) (*DeployResponse, error)
	DeleteDeploymentGroup(ctx context.Context, in *DeleteDeploymentGroupRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	ListDeploymentGroups(ctx context.Context, in *ListDeploymentGroupOptions, opts ...grpc.CallOption) (*ListDeploymentGroupResult, error)
	GetApplicationBase(ctx context.Context, in *GetApplicationRequest, opts ...grpc.CallOption) (*ApplicationBase, error)
	ListDeploymentGroupRecords(ctx context.Context, in *ListWorkflowRecordsRequest, opts ...grpc.CallOption) (*ListWorkflowRecordsResponse, error)
	DetailDeploymentGroupRecord(ctx context.Context, in *DetailDeploymentGroupRecordRequest, opts ...grpc.CallOption) (*WorkflowRecord, error)
	GetDeploymentGroupDetail(ctx context.Context, in *GetDeploymentStatusRequest, opts ...grpc.CallOption) (*DeploymentGroup, error)
	ListDeploymentGroupRevisions(ctx context.Context, in *ListDeploymentGroupRevisionsOptions, opts ...grpc.CallOption) (*ListDeploymentGroupRevisionsResult, error)
	DetailDeploymentGroupRevision(ctx context.Context, in *DetailDeploymentGroupRevisionRequest, opts ...grpc.CallOption) (*DeploymentGroupRevision, error)
	RollbackDeploymentGroupRevision(ctx context.Context, in *RollbackDeploymentGroupRevisionRequest, opts ...grpc.CallOption) (*RollbackDeploymentGroupRevisionResponse, error)
	RollbackDeploymentGroupRecord(ctx context.Context, in *RollbackDeploymentGroupRecordRequest, opts ...grpc.CallOption) (*WorkflowRecord, error)
	TerminateDeploymentGroupRecord(ctx context.Context, in *TerminateDeploymentGroupRecordRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	ResumeDeploymentGroupRecord(ctx context.Context, in *ResumeDeploymentGroupRecordRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	GetDeploymentGroupRecordLogs(ctx context.Context, in *DeploymentGroupRecordLogsRequest, opts ...grpc.CallOption) (*DeploymentGroupRecordLogsResponse, error)
	RecycleDeploymentGroupWorkload(ctx context.Context, in *RecycleDeploymentGroupWorkloadRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	GetDeploymentGroupVolumes(ctx context.Context, in *GetDeploymentGroupVolumesRequest, opts ...grpc.CallOption) (*ListVolumes, error)
	ListServiceExports(ctx context.Context, in *ListServiceExportRequest, opts ...grpc.CallOption) (*ListServiceExportResponse, error)
}

type applicationServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewApplicationServiceClient(cc grpc.ClientConnInterface) ApplicationServiceClient {
	return &applicationServiceClient{cc}
}

func (c *applicationServiceClient) CreateApplication(ctx context.Context, in *CreateApplicationRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, ApplicationService_CreateApplication_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *applicationServiceClient) UpdateApplication(ctx context.Context, in *UpdateApplicationRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, ApplicationService_UpdateApplication_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *applicationServiceClient) DeleteApplication(ctx context.Context, in *DeleteApplicationRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, ApplicationService_DeleteApplication_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *applicationServiceClient) ListApplications(ctx context.Context, in *ListApplicationOptions, opts ...grpc.CallOption) (*ListApplicationResult, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListApplicationResult)
	err := c.cc.Invoke(ctx, ApplicationService_ListApplications_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *applicationServiceClient) CreateDeploymentGroup(ctx context.Context, in *CreateDeploymentGroupRequest, opts ...grpc.CallOption) (*DeploymentGroup, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeploymentGroup)
	err := c.cc.Invoke(ctx, ApplicationService_CreateDeploymentGroup_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *applicationServiceClient) UpdateDeploymentGroup(ctx context.Context, in *UpdateDeploymentGroupRequest, opts ...grpc.CallOption) (*DeploymentGroup, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeploymentGroup)
	err := c.cc.Invoke(ctx, ApplicationService_UpdateDeploymentGroup_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *applicationServiceClient) ScaleDeploymentGroup(ctx context.Context, in *ScaleDeploymentGroupRequest, opts ...grpc.CallOption) (*ScaleDeploymentGroupResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ScaleDeploymentGroupResponse)
	err := c.cc.Invoke(ctx, ApplicationService_ScaleDeploymentGroup_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *applicationServiceClient) DeployDeploymentGroup(ctx context.Context, in *DeployRequest, opts ...grpc.CallOption) (*DeployResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeployResponse)
	err := c.cc.Invoke(ctx, ApplicationService_DeployDeploymentGroup_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *applicationServiceClient) DeleteDeploymentGroup(ctx context.Context, in *DeleteDeploymentGroupRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, ApplicationService_DeleteDeploymentGroup_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *applicationServiceClient) ListDeploymentGroups(ctx context.Context, in *ListDeploymentGroupOptions, opts ...grpc.CallOption) (*ListDeploymentGroupResult, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListDeploymentGroupResult)
	err := c.cc.Invoke(ctx, ApplicationService_ListDeploymentGroups_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *applicationServiceClient) GetApplicationBase(ctx context.Context, in *GetApplicationRequest, opts ...grpc.CallOption) (*ApplicationBase, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ApplicationBase)
	err := c.cc.Invoke(ctx, ApplicationService_GetApplicationBase_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *applicationServiceClient) ListDeploymentGroupRecords(ctx context.Context, in *ListWorkflowRecordsRequest, opts ...grpc.CallOption) (*ListWorkflowRecordsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListWorkflowRecordsResponse)
	err := c.cc.Invoke(ctx, ApplicationService_ListDeploymentGroupRecords_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *applicationServiceClient) DetailDeploymentGroupRecord(ctx context.Context, in *DetailDeploymentGroupRecordRequest, opts ...grpc.CallOption) (*WorkflowRecord, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(WorkflowRecord)
	err := c.cc.Invoke(ctx, ApplicationService_DetailDeploymentGroupRecord_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *applicationServiceClient) GetDeploymentGroupDetail(ctx context.Context, in *GetDeploymentStatusRequest, opts ...grpc.CallOption) (*DeploymentGroup, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeploymentGroup)
	err := c.cc.Invoke(ctx, ApplicationService_GetDeploymentGroupDetail_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *applicationServiceClient) ListDeploymentGroupRevisions(ctx context.Context, in *ListDeploymentGroupRevisionsOptions, opts ...grpc.CallOption) (*ListDeploymentGroupRevisionsResult, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListDeploymentGroupRevisionsResult)
	err := c.cc.Invoke(ctx, ApplicationService_ListDeploymentGroupRevisions_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *applicationServiceClient) DetailDeploymentGroupRevision(ctx context.Context, in *DetailDeploymentGroupRevisionRequest, opts ...grpc.CallOption) (*DeploymentGroupRevision, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeploymentGroupRevision)
	err := c.cc.Invoke(ctx, ApplicationService_DetailDeploymentGroupRevision_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *applicationServiceClient) RollbackDeploymentGroupRevision(ctx context.Context, in *RollbackDeploymentGroupRevisionRequest, opts ...grpc.CallOption) (*RollbackDeploymentGroupRevisionResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RollbackDeploymentGroupRevisionResponse)
	err := c.cc.Invoke(ctx, ApplicationService_RollbackDeploymentGroupRevision_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *applicationServiceClient) RollbackDeploymentGroupRecord(ctx context.Context, in *RollbackDeploymentGroupRecordRequest, opts ...grpc.CallOption) (*WorkflowRecord, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(WorkflowRecord)
	err := c.cc.Invoke(ctx, ApplicationService_RollbackDeploymentGroupRecord_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *applicationServiceClient) TerminateDeploymentGroupRecord(ctx context.Context, in *TerminateDeploymentGroupRecordRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, ApplicationService_TerminateDeploymentGroupRecord_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *applicationServiceClient) ResumeDeploymentGroupRecord(ctx context.Context, in *ResumeDeploymentGroupRecordRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, ApplicationService_ResumeDeploymentGroupRecord_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *applicationServiceClient) GetDeploymentGroupRecordLogs(ctx context.Context, in *DeploymentGroupRecordLogsRequest, opts ...grpc.CallOption) (*DeploymentGroupRecordLogsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeploymentGroupRecordLogsResponse)
	err := c.cc.Invoke(ctx, ApplicationService_GetDeploymentGroupRecordLogs_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *applicationServiceClient) RecycleDeploymentGroupWorkload(ctx context.Context, in *RecycleDeploymentGroupWorkloadRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, ApplicationService_RecycleDeploymentGroupWorkload_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *applicationServiceClient) GetDeploymentGroupVolumes(ctx context.Context, in *GetDeploymentGroupVolumesRequest, opts ...grpc.CallOption) (*ListVolumes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListVolumes)
	err := c.cc.Invoke(ctx, ApplicationService_GetDeploymentGroupVolumes_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *applicationServiceClient) ListServiceExports(ctx context.Context, in *ListServiceExportRequest, opts ...grpc.CallOption) (*ListServiceExportResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListServiceExportResponse)
	err := c.cc.Invoke(ctx, ApplicationService_ListServiceExports_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ApplicationServiceServer is the server API for ApplicationService service.
// All implementations must embed UnimplementedApplicationServiceServer
// for forward compatibility
type ApplicationServiceServer interface {
	CreateApplication(context.Context, *CreateApplicationRequest) (*emptypb.Empty, error)
	UpdateApplication(context.Context, *UpdateApplicationRequest) (*emptypb.Empty, error)
	DeleteApplication(context.Context, *DeleteApplicationRequest) (*emptypb.Empty, error)
	ListApplications(context.Context, *ListApplicationOptions) (*ListApplicationResult, error)
	// 新增分布式推理
	CreateDeploymentGroup(context.Context, *CreateDeploymentGroupRequest) (*DeploymentGroup, error)
	UpdateDeploymentGroup(context.Context, *UpdateDeploymentGroupRequest) (*DeploymentGroup, error)
	// 扩缩容
	ScaleDeploymentGroup(context.Context, *ScaleDeploymentGroupRequest) (*ScaleDeploymentGroupResponse, error)
	DeployDeploymentGroup(context.Context, *DeployRequest) (*DeployResponse, error)
	DeleteDeploymentGroup(context.Context, *DeleteDeploymentGroupRequest) (*emptypb.Empty, error)
	ListDeploymentGroups(context.Context, *ListDeploymentGroupOptions) (*ListDeploymentGroupResult, error)
	GetApplicationBase(context.Context, *GetApplicationRequest) (*ApplicationBase, error)
	ListDeploymentGroupRecords(context.Context, *ListWorkflowRecordsRequest) (*ListWorkflowRecordsResponse, error)
	DetailDeploymentGroupRecord(context.Context, *DetailDeploymentGroupRecordRequest) (*WorkflowRecord, error)
	GetDeploymentGroupDetail(context.Context, *GetDeploymentStatusRequest) (*DeploymentGroup, error)
	ListDeploymentGroupRevisions(context.Context, *ListDeploymentGroupRevisionsOptions) (*ListDeploymentGroupRevisionsResult, error)
	DetailDeploymentGroupRevision(context.Context, *DetailDeploymentGroupRevisionRequest) (*DeploymentGroupRevision, error)
	RollbackDeploymentGroupRevision(context.Context, *RollbackDeploymentGroupRevisionRequest) (*RollbackDeploymentGroupRevisionResponse, error)
	RollbackDeploymentGroupRecord(context.Context, *RollbackDeploymentGroupRecordRequest) (*WorkflowRecord, error)
	TerminateDeploymentGroupRecord(context.Context, *TerminateDeploymentGroupRecordRequest) (*emptypb.Empty, error)
	ResumeDeploymentGroupRecord(context.Context, *ResumeDeploymentGroupRecordRequest) (*emptypb.Empty, error)
	GetDeploymentGroupRecordLogs(context.Context, *DeploymentGroupRecordLogsRequest) (*DeploymentGroupRecordLogsResponse, error)
	RecycleDeploymentGroupWorkload(context.Context, *RecycleDeploymentGroupWorkloadRequest) (*emptypb.Empty, error)
	GetDeploymentGroupVolumes(context.Context, *GetDeploymentGroupVolumesRequest) (*ListVolumes, error)
	ListServiceExports(context.Context, *ListServiceExportRequest) (*ListServiceExportResponse, error)
	mustEmbedUnimplementedApplicationServiceServer()
}

// UnimplementedApplicationServiceServer must be embedded to have forward compatible implementations.
type UnimplementedApplicationServiceServer struct {
}

func (UnimplementedApplicationServiceServer) CreateApplication(context.Context, *CreateApplicationRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateApplication not implemented")
}
func (UnimplementedApplicationServiceServer) UpdateApplication(context.Context, *UpdateApplicationRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateApplication not implemented")
}
func (UnimplementedApplicationServiceServer) DeleteApplication(context.Context, *DeleteApplicationRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteApplication not implemented")
}
func (UnimplementedApplicationServiceServer) ListApplications(context.Context, *ListApplicationOptions) (*ListApplicationResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListApplications not implemented")
}
func (UnimplementedApplicationServiceServer) CreateDeploymentGroup(context.Context, *CreateDeploymentGroupRequest) (*DeploymentGroup, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateDeploymentGroup not implemented")
}
func (UnimplementedApplicationServiceServer) UpdateDeploymentGroup(context.Context, *UpdateDeploymentGroupRequest) (*DeploymentGroup, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateDeploymentGroup not implemented")
}
func (UnimplementedApplicationServiceServer) ScaleDeploymentGroup(context.Context, *ScaleDeploymentGroupRequest) (*ScaleDeploymentGroupResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ScaleDeploymentGroup not implemented")
}
func (UnimplementedApplicationServiceServer) DeployDeploymentGroup(context.Context, *DeployRequest) (*DeployResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeployDeploymentGroup not implemented")
}
func (UnimplementedApplicationServiceServer) DeleteDeploymentGroup(context.Context, *DeleteDeploymentGroupRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteDeploymentGroup not implemented")
}
func (UnimplementedApplicationServiceServer) ListDeploymentGroups(context.Context, *ListDeploymentGroupOptions) (*ListDeploymentGroupResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListDeploymentGroups not implemented")
}
func (UnimplementedApplicationServiceServer) GetApplicationBase(context.Context, *GetApplicationRequest) (*ApplicationBase, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetApplicationBase not implemented")
}
func (UnimplementedApplicationServiceServer) ListDeploymentGroupRecords(context.Context, *ListWorkflowRecordsRequest) (*ListWorkflowRecordsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListDeploymentGroupRecords not implemented")
}
func (UnimplementedApplicationServiceServer) DetailDeploymentGroupRecord(context.Context, *DetailDeploymentGroupRecordRequest) (*WorkflowRecord, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DetailDeploymentGroupRecord not implemented")
}
func (UnimplementedApplicationServiceServer) GetDeploymentGroupDetail(context.Context, *GetDeploymentStatusRequest) (*DeploymentGroup, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDeploymentGroupDetail not implemented")
}
func (UnimplementedApplicationServiceServer) ListDeploymentGroupRevisions(context.Context, *ListDeploymentGroupRevisionsOptions) (*ListDeploymentGroupRevisionsResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListDeploymentGroupRevisions not implemented")
}
func (UnimplementedApplicationServiceServer) DetailDeploymentGroupRevision(context.Context, *DetailDeploymentGroupRevisionRequest) (*DeploymentGroupRevision, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DetailDeploymentGroupRevision not implemented")
}
func (UnimplementedApplicationServiceServer) RollbackDeploymentGroupRevision(context.Context, *RollbackDeploymentGroupRevisionRequest) (*RollbackDeploymentGroupRevisionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RollbackDeploymentGroupRevision not implemented")
}
func (UnimplementedApplicationServiceServer) RollbackDeploymentGroupRecord(context.Context, *RollbackDeploymentGroupRecordRequest) (*WorkflowRecord, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RollbackDeploymentGroupRecord not implemented")
}
func (UnimplementedApplicationServiceServer) TerminateDeploymentGroupRecord(context.Context, *TerminateDeploymentGroupRecordRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TerminateDeploymentGroupRecord not implemented")
}
func (UnimplementedApplicationServiceServer) ResumeDeploymentGroupRecord(context.Context, *ResumeDeploymentGroupRecordRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ResumeDeploymentGroupRecord not implemented")
}
func (UnimplementedApplicationServiceServer) GetDeploymentGroupRecordLogs(context.Context, *DeploymentGroupRecordLogsRequest) (*DeploymentGroupRecordLogsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDeploymentGroupRecordLogs not implemented")
}
func (UnimplementedApplicationServiceServer) RecycleDeploymentGroupWorkload(context.Context, *RecycleDeploymentGroupWorkloadRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RecycleDeploymentGroupWorkload not implemented")
}
func (UnimplementedApplicationServiceServer) GetDeploymentGroupVolumes(context.Context, *GetDeploymentGroupVolumesRequest) (*ListVolumes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDeploymentGroupVolumes not implemented")
}
func (UnimplementedApplicationServiceServer) ListServiceExports(context.Context, *ListServiceExportRequest) (*ListServiceExportResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListServiceExports not implemented")
}
func (UnimplementedApplicationServiceServer) mustEmbedUnimplementedApplicationServiceServer() {}

// UnsafeApplicationServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ApplicationServiceServer will
// result in compilation errors.
type UnsafeApplicationServiceServer interface {
	mustEmbedUnimplementedApplicationServiceServer()
}

func RegisterApplicationServiceServer(s grpc.ServiceRegistrar, srv ApplicationServiceServer) {
	s.RegisterService(&ApplicationService_ServiceDesc, srv)
}

func _ApplicationService_CreateApplication_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateApplicationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApplicationServiceServer).CreateApplication(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApplicationService_CreateApplication_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApplicationServiceServer).CreateApplication(ctx, req.(*CreateApplicationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApplicationService_UpdateApplication_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateApplicationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApplicationServiceServer).UpdateApplication(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApplicationService_UpdateApplication_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApplicationServiceServer).UpdateApplication(ctx, req.(*UpdateApplicationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApplicationService_DeleteApplication_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteApplicationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApplicationServiceServer).DeleteApplication(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApplicationService_DeleteApplication_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApplicationServiceServer).DeleteApplication(ctx, req.(*DeleteApplicationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApplicationService_ListApplications_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListApplicationOptions)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApplicationServiceServer).ListApplications(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApplicationService_ListApplications_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApplicationServiceServer).ListApplications(ctx, req.(*ListApplicationOptions))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApplicationService_CreateDeploymentGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateDeploymentGroupRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApplicationServiceServer).CreateDeploymentGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApplicationService_CreateDeploymentGroup_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApplicationServiceServer).CreateDeploymentGroup(ctx, req.(*CreateDeploymentGroupRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApplicationService_UpdateDeploymentGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateDeploymentGroupRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApplicationServiceServer).UpdateDeploymentGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApplicationService_UpdateDeploymentGroup_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApplicationServiceServer).UpdateDeploymentGroup(ctx, req.(*UpdateDeploymentGroupRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApplicationService_ScaleDeploymentGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ScaleDeploymentGroupRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApplicationServiceServer).ScaleDeploymentGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApplicationService_ScaleDeploymentGroup_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApplicationServiceServer).ScaleDeploymentGroup(ctx, req.(*ScaleDeploymentGroupRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApplicationService_DeployDeploymentGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeployRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApplicationServiceServer).DeployDeploymentGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApplicationService_DeployDeploymentGroup_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApplicationServiceServer).DeployDeploymentGroup(ctx, req.(*DeployRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApplicationService_DeleteDeploymentGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteDeploymentGroupRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApplicationServiceServer).DeleteDeploymentGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApplicationService_DeleteDeploymentGroup_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApplicationServiceServer).DeleteDeploymentGroup(ctx, req.(*DeleteDeploymentGroupRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApplicationService_ListDeploymentGroups_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListDeploymentGroupOptions)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApplicationServiceServer).ListDeploymentGroups(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApplicationService_ListDeploymentGroups_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApplicationServiceServer).ListDeploymentGroups(ctx, req.(*ListDeploymentGroupOptions))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApplicationService_GetApplicationBase_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetApplicationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApplicationServiceServer).GetApplicationBase(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApplicationService_GetApplicationBase_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApplicationServiceServer).GetApplicationBase(ctx, req.(*GetApplicationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApplicationService_ListDeploymentGroupRecords_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListWorkflowRecordsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApplicationServiceServer).ListDeploymentGroupRecords(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApplicationService_ListDeploymentGroupRecords_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApplicationServiceServer).ListDeploymentGroupRecords(ctx, req.(*ListWorkflowRecordsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApplicationService_DetailDeploymentGroupRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DetailDeploymentGroupRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApplicationServiceServer).DetailDeploymentGroupRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApplicationService_DetailDeploymentGroupRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApplicationServiceServer).DetailDeploymentGroupRecord(ctx, req.(*DetailDeploymentGroupRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApplicationService_GetDeploymentGroupDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDeploymentStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApplicationServiceServer).GetDeploymentGroupDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApplicationService_GetDeploymentGroupDetail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApplicationServiceServer).GetDeploymentGroupDetail(ctx, req.(*GetDeploymentStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApplicationService_ListDeploymentGroupRevisions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListDeploymentGroupRevisionsOptions)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApplicationServiceServer).ListDeploymentGroupRevisions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApplicationService_ListDeploymentGroupRevisions_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApplicationServiceServer).ListDeploymentGroupRevisions(ctx, req.(*ListDeploymentGroupRevisionsOptions))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApplicationService_DetailDeploymentGroupRevision_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DetailDeploymentGroupRevisionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApplicationServiceServer).DetailDeploymentGroupRevision(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApplicationService_DetailDeploymentGroupRevision_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApplicationServiceServer).DetailDeploymentGroupRevision(ctx, req.(*DetailDeploymentGroupRevisionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApplicationService_RollbackDeploymentGroupRevision_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RollbackDeploymentGroupRevisionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApplicationServiceServer).RollbackDeploymentGroupRevision(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApplicationService_RollbackDeploymentGroupRevision_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApplicationServiceServer).RollbackDeploymentGroupRevision(ctx, req.(*RollbackDeploymentGroupRevisionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApplicationService_RollbackDeploymentGroupRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RollbackDeploymentGroupRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApplicationServiceServer).RollbackDeploymentGroupRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApplicationService_RollbackDeploymentGroupRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApplicationServiceServer).RollbackDeploymentGroupRecord(ctx, req.(*RollbackDeploymentGroupRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApplicationService_TerminateDeploymentGroupRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TerminateDeploymentGroupRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApplicationServiceServer).TerminateDeploymentGroupRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApplicationService_TerminateDeploymentGroupRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApplicationServiceServer).TerminateDeploymentGroupRecord(ctx, req.(*TerminateDeploymentGroupRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApplicationService_ResumeDeploymentGroupRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ResumeDeploymentGroupRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApplicationServiceServer).ResumeDeploymentGroupRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApplicationService_ResumeDeploymentGroupRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApplicationServiceServer).ResumeDeploymentGroupRecord(ctx, req.(*ResumeDeploymentGroupRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApplicationService_GetDeploymentGroupRecordLogs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeploymentGroupRecordLogsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApplicationServiceServer).GetDeploymentGroupRecordLogs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApplicationService_GetDeploymentGroupRecordLogs_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApplicationServiceServer).GetDeploymentGroupRecordLogs(ctx, req.(*DeploymentGroupRecordLogsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApplicationService_RecycleDeploymentGroupWorkload_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecycleDeploymentGroupWorkloadRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApplicationServiceServer).RecycleDeploymentGroupWorkload(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApplicationService_RecycleDeploymentGroupWorkload_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApplicationServiceServer).RecycleDeploymentGroupWorkload(ctx, req.(*RecycleDeploymentGroupWorkloadRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApplicationService_GetDeploymentGroupVolumes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDeploymentGroupVolumesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApplicationServiceServer).GetDeploymentGroupVolumes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApplicationService_GetDeploymentGroupVolumes_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApplicationServiceServer).GetDeploymentGroupVolumes(ctx, req.(*GetDeploymentGroupVolumesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApplicationService_ListServiceExports_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListServiceExportRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApplicationServiceServer).ListServiceExports(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApplicationService_ListServiceExports_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApplicationServiceServer).ListServiceExports(ctx, req.(*ListServiceExportRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// ApplicationService_ServiceDesc is the grpc.ServiceDesc for ApplicationService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ApplicationService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "apis.aistudio.application.v1.ApplicationService",
	HandlerType: (*ApplicationServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateApplication",
			Handler:    _ApplicationService_CreateApplication_Handler,
		},
		{
			MethodName: "UpdateApplication",
			Handler:    _ApplicationService_UpdateApplication_Handler,
		},
		{
			MethodName: "DeleteApplication",
			Handler:    _ApplicationService_DeleteApplication_Handler,
		},
		{
			MethodName: "ListApplications",
			Handler:    _ApplicationService_ListApplications_Handler,
		},
		{
			MethodName: "CreateDeploymentGroup",
			Handler:    _ApplicationService_CreateDeploymentGroup_Handler,
		},
		{
			MethodName: "UpdateDeploymentGroup",
			Handler:    _ApplicationService_UpdateDeploymentGroup_Handler,
		},
		{
			MethodName: "ScaleDeploymentGroup",
			Handler:    _ApplicationService_ScaleDeploymentGroup_Handler,
		},
		{
			MethodName: "DeployDeploymentGroup",
			Handler:    _ApplicationService_DeployDeploymentGroup_Handler,
		},
		{
			MethodName: "DeleteDeploymentGroup",
			Handler:    _ApplicationService_DeleteDeploymentGroup_Handler,
		},
		{
			MethodName: "ListDeploymentGroups",
			Handler:    _ApplicationService_ListDeploymentGroups_Handler,
		},
		{
			MethodName: "GetApplicationBase",
			Handler:    _ApplicationService_GetApplicationBase_Handler,
		},
		{
			MethodName: "ListDeploymentGroupRecords",
			Handler:    _ApplicationService_ListDeploymentGroupRecords_Handler,
		},
		{
			MethodName: "DetailDeploymentGroupRecord",
			Handler:    _ApplicationService_DetailDeploymentGroupRecord_Handler,
		},
		{
			MethodName: "GetDeploymentGroupDetail",
			Handler:    _ApplicationService_GetDeploymentGroupDetail_Handler,
		},
		{
			MethodName: "ListDeploymentGroupRevisions",
			Handler:    _ApplicationService_ListDeploymentGroupRevisions_Handler,
		},
		{
			MethodName: "DetailDeploymentGroupRevision",
			Handler:    _ApplicationService_DetailDeploymentGroupRevision_Handler,
		},
		{
			MethodName: "RollbackDeploymentGroupRevision",
			Handler:    _ApplicationService_RollbackDeploymentGroupRevision_Handler,
		},
		{
			MethodName: "RollbackDeploymentGroupRecord",
			Handler:    _ApplicationService_RollbackDeploymentGroupRecord_Handler,
		},
		{
			MethodName: "TerminateDeploymentGroupRecord",
			Handler:    _ApplicationService_TerminateDeploymentGroupRecord_Handler,
		},
		{
			MethodName: "ResumeDeploymentGroupRecord",
			Handler:    _ApplicationService_ResumeDeploymentGroupRecord_Handler,
		},
		{
			MethodName: "GetDeploymentGroupRecordLogs",
			Handler:    _ApplicationService_GetDeploymentGroupRecordLogs_Handler,
		},
		{
			MethodName: "RecycleDeploymentGroupWorkload",
			Handler:    _ApplicationService_RecycleDeploymentGroupWorkload_Handler,
		},
		{
			MethodName: "GetDeploymentGroupVolumes",
			Handler:    _ApplicationService_GetDeploymentGroupVolumes_Handler,
		},
		{
			MethodName: "ListServiceExports",
			Handler:    _ApplicationService_ListServiceExports_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "aistudio/application/v1/application.proto",
}
