// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.3
// - protoc             v5.29.3
// source: aistudio/application/v1/application.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationApplicationServiceCreateApplication = "/apis.aistudio.application.v1.ApplicationService/CreateApplication"
const OperationApplicationServiceCreateDeploymentGroup = "/apis.aistudio.application.v1.ApplicationService/CreateDeploymentGroup"
const OperationApplicationServiceDeleteApplication = "/apis.aistudio.application.v1.ApplicationService/DeleteApplication"
const OperationApplicationServiceDeleteDeploymentGroup = "/apis.aistudio.application.v1.ApplicationService/DeleteDeploymentGroup"
const OperationApplicationServiceDeployDeploymentGroup = "/apis.aistudio.application.v1.ApplicationService/DeployDeploymentGroup"
const OperationApplicationServiceDetailDeploymentGroupRecord = "/apis.aistudio.application.v1.ApplicationService/DetailDeploymentGroupRecord"
const OperationApplicationServiceDetailDeploymentGroupRevision = "/apis.aistudio.application.v1.ApplicationService/DetailDeploymentGroupRevision"
const OperationApplicationServiceGetApplicationBase = "/apis.aistudio.application.v1.ApplicationService/GetApplicationBase"
const OperationApplicationServiceGetDeploymentGroupDetail = "/apis.aistudio.application.v1.ApplicationService/GetDeploymentGroupDetail"
const OperationApplicationServiceGetDeploymentGroupRecordLogs = "/apis.aistudio.application.v1.ApplicationService/GetDeploymentGroupRecordLogs"
const OperationApplicationServiceGetDeploymentGroupVolumes = "/apis.aistudio.application.v1.ApplicationService/GetDeploymentGroupVolumes"
const OperationApplicationServiceListApplications = "/apis.aistudio.application.v1.ApplicationService/ListApplications"
const OperationApplicationServiceListDeploymentGroupRecords = "/apis.aistudio.application.v1.ApplicationService/ListDeploymentGroupRecords"
const OperationApplicationServiceListDeploymentGroupRevisions = "/apis.aistudio.application.v1.ApplicationService/ListDeploymentGroupRevisions"
const OperationApplicationServiceListDeploymentGroups = "/apis.aistudio.application.v1.ApplicationService/ListDeploymentGroups"
const OperationApplicationServiceListServiceExports = "/apis.aistudio.application.v1.ApplicationService/ListServiceExports"
const OperationApplicationServiceRecycleDeploymentGroupWorkload = "/apis.aistudio.application.v1.ApplicationService/RecycleDeploymentGroupWorkload"
const OperationApplicationServiceResumeDeploymentGroupRecord = "/apis.aistudio.application.v1.ApplicationService/ResumeDeploymentGroupRecord"
const OperationApplicationServiceRollbackDeploymentGroupRecord = "/apis.aistudio.application.v1.ApplicationService/RollbackDeploymentGroupRecord"
const OperationApplicationServiceRollbackDeploymentGroupRevision = "/apis.aistudio.application.v1.ApplicationService/RollbackDeploymentGroupRevision"
const OperationApplicationServiceScaleDeploymentGroup = "/apis.aistudio.application.v1.ApplicationService/ScaleDeploymentGroup"
const OperationApplicationServiceTerminateDeploymentGroupRecord = "/apis.aistudio.application.v1.ApplicationService/TerminateDeploymentGroupRecord"
const OperationApplicationServiceUpdateApplication = "/apis.aistudio.application.v1.ApplicationService/UpdateApplication"
const OperationApplicationServiceUpdateDeploymentGroup = "/apis.aistudio.application.v1.ApplicationService/UpdateDeploymentGroup"

type ApplicationServiceHTTPServer interface {
	CreateApplication(context.Context, *CreateApplicationRequest) (*emptypb.Empty, error)
	// CreateDeploymentGroup 新增分布式推理
	CreateDeploymentGroup(context.Context, *CreateDeploymentGroupRequest) (*DeploymentGroup, error)
	DeleteApplication(context.Context, *DeleteApplicationRequest) (*emptypb.Empty, error)
	DeleteDeploymentGroup(context.Context, *DeleteDeploymentGroupRequest) (*emptypb.Empty, error)
	DeployDeploymentGroup(context.Context, *DeployRequest) (*DeployResponse, error)
	DetailDeploymentGroupRecord(context.Context, *DetailDeploymentGroupRecordRequest) (*WorkflowRecord, error)
	DetailDeploymentGroupRevision(context.Context, *DetailDeploymentGroupRevisionRequest) (*DeploymentGroupRevision, error)
	GetApplicationBase(context.Context, *GetApplicationRequest) (*ApplicationBase, error)
	GetDeploymentGroupDetail(context.Context, *GetDeploymentStatusRequest) (*DeploymentGroup, error)
	GetDeploymentGroupRecordLogs(context.Context, *DeploymentGroupRecordLogsRequest) (*DeploymentGroupRecordLogsResponse, error)
	GetDeploymentGroupVolumes(context.Context, *GetDeploymentGroupVolumesRequest) (*ListVolumes, error)
	ListApplications(context.Context, *ListApplicationOptions) (*ListApplicationResult, error)
	ListDeploymentGroupRecords(context.Context, *ListWorkflowRecordsRequest) (*ListWorkflowRecordsResponse, error)
	ListDeploymentGroupRevisions(context.Context, *ListDeploymentGroupRevisionsOptions) (*ListDeploymentGroupRevisionsResult, error)
	ListDeploymentGroups(context.Context, *ListDeploymentGroupOptions) (*ListDeploymentGroupResult, error)
	ListServiceExports(context.Context, *ListServiceExportRequest) (*ListServiceExportResponse, error)
	RecycleDeploymentGroupWorkload(context.Context, *RecycleDeploymentGroupWorkloadRequest) (*emptypb.Empty, error)
	ResumeDeploymentGroupRecord(context.Context, *ResumeDeploymentGroupRecordRequest) (*emptypb.Empty, error)
	RollbackDeploymentGroupRecord(context.Context, *RollbackDeploymentGroupRecordRequest) (*WorkflowRecord, error)
	RollbackDeploymentGroupRevision(context.Context, *RollbackDeploymentGroupRevisionRequest) (*RollbackDeploymentGroupRevisionResponse, error)
	// ScaleDeploymentGroup 扩缩容
	ScaleDeploymentGroup(context.Context, *ScaleDeploymentGroupRequest) (*ScaleDeploymentGroupResponse, error)
	TerminateDeploymentGroupRecord(context.Context, *TerminateDeploymentGroupRecordRequest) (*emptypb.Empty, error)
	UpdateApplication(context.Context, *UpdateApplicationRequest) (*emptypb.Empty, error)
	UpdateDeploymentGroup(context.Context, *UpdateDeploymentGroupRequest) (*DeploymentGroup, error)
}

func RegisterApplicationServiceHTTPServer(s *http.Server, srv ApplicationServiceHTTPServer) {
	r := s.Route("/")
	r.POST("/apis/v1/application", _ApplicationService_CreateApplication0_HTTP_Handler(srv))
	r.PUT("/apis/v1/application/{appName}", _ApplicationService_UpdateApplication0_HTTP_Handler(srv))
	r.DELETE("/apis/v1/application/{appName}", _ApplicationService_DeleteApplication0_HTTP_Handler(srv))
	r.GET("/apis/v1/applications", _ApplicationService_ListApplications0_HTTP_Handler(srv))
	r.POST("/apis/v1/deployment", _ApplicationService_CreateDeploymentGroup0_HTTP_Handler(srv))
	r.PUT("/apis/v1/deployment/{deploymentGroupId}", _ApplicationService_UpdateDeploymentGroup0_HTTP_Handler(srv))
	r.PUT("/apis/v1/deployment/{deploymentGroupId}/scale", _ApplicationService_ScaleDeploymentGroup0_HTTP_Handler(srv))
	r.POST("/apis/v1/deployment/{deploymentGroupId}/deploy", _ApplicationService_DeployDeploymentGroup0_HTTP_Handler(srv))
	r.DELETE("/apis/v1/deployment/{deploymentGroupId}", _ApplicationService_DeleteDeploymentGroup0_HTTP_Handler(srv))
	r.GET("/apis/v1/application/{appName}/deployments", _ApplicationService_ListDeploymentGroups0_HTTP_Handler(srv))
	r.GET("/apis/v1/application/{appName}/base", _ApplicationService_GetApplicationBase0_HTTP_Handler(srv))
	r.GET("/apis/v1/deployment/{deploymentGroupId}/records", _ApplicationService_ListDeploymentGroupRecords0_HTTP_Handler(srv))
	r.GET("/apis/v1/deployment/{deploymentGroupId}/records/{recordName}", _ApplicationService_DetailDeploymentGroupRecord0_HTTP_Handler(srv))
	r.GET("/apis/v1/deployment/{deploymentGroupId}/detail", _ApplicationService_GetDeploymentGroupDetail0_HTTP_Handler(srv))
	r.GET("/apis/v1/deployment/{deploymentGroupId}/revisions", _ApplicationService_ListDeploymentGroupRevisions0_HTTP_Handler(srv))
	r.GET("/apis/v1/deployment/{deploymentGroupId}/revisions/{revision}", _ApplicationService_DetailDeploymentGroupRevision0_HTTP_Handler(srv))
	r.POST("/apis/v1/deployment/{deploymentGroupId}/revisions/{revision}/rollback", _ApplicationService_RollbackDeploymentGroupRevision0_HTTP_Handler(srv))
	r.POST("/apis/v1/deployment/{deploymentGroupId}/record/{recordName}/rollback", _ApplicationService_RollbackDeploymentGroupRecord0_HTTP_Handler(srv))
	r.POST("/apis/v1/deployment/{deploymentGroupId}/record/{recordName}/terminate", _ApplicationService_TerminateDeploymentGroupRecord0_HTTP_Handler(srv))
	r.POST("/apis/v1/deployment/{deploymentGroupId}/record/{recordName}/resume", _ApplicationService_ResumeDeploymentGroupRecord0_HTTP_Handler(srv))
	r.GET("/apis/v1/deployment/{deploymentGroupId}/record/{recordName}/logs", _ApplicationService_GetDeploymentGroupRecordLogs0_HTTP_Handler(srv))
	r.POST("/apis/v1/deployment/{deploymentGroupId}/recycle", _ApplicationService_RecycleDeploymentGroupWorkload0_HTTP_Handler(srv))
	r.GET("/apis/v1/deployment/{deploymentGroupId}/volumes", _ApplicationService_GetDeploymentGroupVolumes0_HTTP_Handler(srv))
	r.GET("/apis/v1/application/{appName}/service-exports", _ApplicationService_ListServiceExports0_HTTP_Handler(srv))
}

func _ApplicationService_CreateApplication0_HTTP_Handler(srv ApplicationServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateApplicationRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationApplicationServiceCreateApplication)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateApplication(ctx, req.(*CreateApplicationRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _ApplicationService_UpdateApplication0_HTTP_Handler(srv ApplicationServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UpdateApplicationRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationApplicationServiceUpdateApplication)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateApplication(ctx, req.(*UpdateApplicationRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _ApplicationService_DeleteApplication0_HTTP_Handler(srv ApplicationServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteApplicationRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationApplicationServiceDeleteApplication)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteApplication(ctx, req.(*DeleteApplicationRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _ApplicationService_ListApplications0_HTTP_Handler(srv ApplicationServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListApplicationOptions
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationApplicationServiceListApplications)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListApplications(ctx, req.(*ListApplicationOptions))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListApplicationResult)
		return ctx.Result(200, reply)
	}
}

func _ApplicationService_CreateDeploymentGroup0_HTTP_Handler(srv ApplicationServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateDeploymentGroupRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationApplicationServiceCreateDeploymentGroup)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateDeploymentGroup(ctx, req.(*CreateDeploymentGroupRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DeploymentGroup)
		return ctx.Result(200, reply)
	}
}

func _ApplicationService_UpdateDeploymentGroup0_HTTP_Handler(srv ApplicationServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UpdateDeploymentGroupRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationApplicationServiceUpdateDeploymentGroup)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateDeploymentGroup(ctx, req.(*UpdateDeploymentGroupRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DeploymentGroup)
		return ctx.Result(200, reply)
	}
}

func _ApplicationService_ScaleDeploymentGroup0_HTTP_Handler(srv ApplicationServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ScaleDeploymentGroupRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationApplicationServiceScaleDeploymentGroup)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ScaleDeploymentGroup(ctx, req.(*ScaleDeploymentGroupRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ScaleDeploymentGroupResponse)
		return ctx.Result(200, reply)
	}
}

func _ApplicationService_DeployDeploymentGroup0_HTTP_Handler(srv ApplicationServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeployRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationApplicationServiceDeployDeploymentGroup)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeployDeploymentGroup(ctx, req.(*DeployRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DeployResponse)
		return ctx.Result(200, reply)
	}
}

func _ApplicationService_DeleteDeploymentGroup0_HTTP_Handler(srv ApplicationServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteDeploymentGroupRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationApplicationServiceDeleteDeploymentGroup)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteDeploymentGroup(ctx, req.(*DeleteDeploymentGroupRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _ApplicationService_ListDeploymentGroups0_HTTP_Handler(srv ApplicationServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListDeploymentGroupOptions
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationApplicationServiceListDeploymentGroups)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListDeploymentGroups(ctx, req.(*ListDeploymentGroupOptions))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListDeploymentGroupResult)
		return ctx.Result(200, reply)
	}
}

func _ApplicationService_GetApplicationBase0_HTTP_Handler(srv ApplicationServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetApplicationRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationApplicationServiceGetApplicationBase)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetApplicationBase(ctx, req.(*GetApplicationRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ApplicationBase)
		return ctx.Result(200, reply)
	}
}

func _ApplicationService_ListDeploymentGroupRecords0_HTTP_Handler(srv ApplicationServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListWorkflowRecordsRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationApplicationServiceListDeploymentGroupRecords)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListDeploymentGroupRecords(ctx, req.(*ListWorkflowRecordsRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListWorkflowRecordsResponse)
		return ctx.Result(200, reply)
	}
}

func _ApplicationService_DetailDeploymentGroupRecord0_HTTP_Handler(srv ApplicationServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DetailDeploymentGroupRecordRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationApplicationServiceDetailDeploymentGroupRecord)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DetailDeploymentGroupRecord(ctx, req.(*DetailDeploymentGroupRecordRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*WorkflowRecord)
		return ctx.Result(200, reply)
	}
}

func _ApplicationService_GetDeploymentGroupDetail0_HTTP_Handler(srv ApplicationServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetDeploymentStatusRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationApplicationServiceGetDeploymentGroupDetail)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetDeploymentGroupDetail(ctx, req.(*GetDeploymentStatusRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DeploymentGroup)
		return ctx.Result(200, reply)
	}
}

func _ApplicationService_ListDeploymentGroupRevisions0_HTTP_Handler(srv ApplicationServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListDeploymentGroupRevisionsOptions
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationApplicationServiceListDeploymentGroupRevisions)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListDeploymentGroupRevisions(ctx, req.(*ListDeploymentGroupRevisionsOptions))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListDeploymentGroupRevisionsResult)
		return ctx.Result(200, reply)
	}
}

func _ApplicationService_DetailDeploymentGroupRevision0_HTTP_Handler(srv ApplicationServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DetailDeploymentGroupRevisionRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationApplicationServiceDetailDeploymentGroupRevision)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DetailDeploymentGroupRevision(ctx, req.(*DetailDeploymentGroupRevisionRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DeploymentGroupRevision)
		return ctx.Result(200, reply)
	}
}

func _ApplicationService_RollbackDeploymentGroupRevision0_HTTP_Handler(srv ApplicationServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in RollbackDeploymentGroupRevisionRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationApplicationServiceRollbackDeploymentGroupRevision)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.RollbackDeploymentGroupRevision(ctx, req.(*RollbackDeploymentGroupRevisionRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*RollbackDeploymentGroupRevisionResponse)
		return ctx.Result(200, reply)
	}
}

func _ApplicationService_RollbackDeploymentGroupRecord0_HTTP_Handler(srv ApplicationServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in RollbackDeploymentGroupRecordRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationApplicationServiceRollbackDeploymentGroupRecord)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.RollbackDeploymentGroupRecord(ctx, req.(*RollbackDeploymentGroupRecordRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*WorkflowRecord)
		return ctx.Result(200, reply)
	}
}

func _ApplicationService_TerminateDeploymentGroupRecord0_HTTP_Handler(srv ApplicationServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in TerminateDeploymentGroupRecordRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationApplicationServiceTerminateDeploymentGroupRecord)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.TerminateDeploymentGroupRecord(ctx, req.(*TerminateDeploymentGroupRecordRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _ApplicationService_ResumeDeploymentGroupRecord0_HTTP_Handler(srv ApplicationServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ResumeDeploymentGroupRecordRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationApplicationServiceResumeDeploymentGroupRecord)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ResumeDeploymentGroupRecord(ctx, req.(*ResumeDeploymentGroupRecordRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _ApplicationService_GetDeploymentGroupRecordLogs0_HTTP_Handler(srv ApplicationServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeploymentGroupRecordLogsRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationApplicationServiceGetDeploymentGroupRecordLogs)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetDeploymentGroupRecordLogs(ctx, req.(*DeploymentGroupRecordLogsRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DeploymentGroupRecordLogsResponse)
		return ctx.Result(200, reply)
	}
}

func _ApplicationService_RecycleDeploymentGroupWorkload0_HTTP_Handler(srv ApplicationServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in RecycleDeploymentGroupWorkloadRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationApplicationServiceRecycleDeploymentGroupWorkload)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.RecycleDeploymentGroupWorkload(ctx, req.(*RecycleDeploymentGroupWorkloadRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _ApplicationService_GetDeploymentGroupVolumes0_HTTP_Handler(srv ApplicationServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetDeploymentGroupVolumesRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationApplicationServiceGetDeploymentGroupVolumes)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetDeploymentGroupVolumes(ctx, req.(*GetDeploymentGroupVolumesRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListVolumes)
		return ctx.Result(200, reply)
	}
}

func _ApplicationService_ListServiceExports0_HTTP_Handler(srv ApplicationServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListServiceExportRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationApplicationServiceListServiceExports)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListServiceExports(ctx, req.(*ListServiceExportRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListServiceExportResponse)
		return ctx.Result(200, reply)
	}
}

type ApplicationServiceHTTPClient interface {
	CreateApplication(ctx context.Context, req *CreateApplicationRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	CreateDeploymentGroup(ctx context.Context, req *CreateDeploymentGroupRequest, opts ...http.CallOption) (rsp *DeploymentGroup, err error)
	DeleteApplication(ctx context.Context, req *DeleteApplicationRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	DeleteDeploymentGroup(ctx context.Context, req *DeleteDeploymentGroupRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	DeployDeploymentGroup(ctx context.Context, req *DeployRequest, opts ...http.CallOption) (rsp *DeployResponse, err error)
	DetailDeploymentGroupRecord(ctx context.Context, req *DetailDeploymentGroupRecordRequest, opts ...http.CallOption) (rsp *WorkflowRecord, err error)
	DetailDeploymentGroupRevision(ctx context.Context, req *DetailDeploymentGroupRevisionRequest, opts ...http.CallOption) (rsp *DeploymentGroupRevision, err error)
	GetApplicationBase(ctx context.Context, req *GetApplicationRequest, opts ...http.CallOption) (rsp *ApplicationBase, err error)
	GetDeploymentGroupDetail(ctx context.Context, req *GetDeploymentStatusRequest, opts ...http.CallOption) (rsp *DeploymentGroup, err error)
	GetDeploymentGroupRecordLogs(ctx context.Context, req *DeploymentGroupRecordLogsRequest, opts ...http.CallOption) (rsp *DeploymentGroupRecordLogsResponse, err error)
	GetDeploymentGroupVolumes(ctx context.Context, req *GetDeploymentGroupVolumesRequest, opts ...http.CallOption) (rsp *ListVolumes, err error)
	ListApplications(ctx context.Context, req *ListApplicationOptions, opts ...http.CallOption) (rsp *ListApplicationResult, err error)
	ListDeploymentGroupRecords(ctx context.Context, req *ListWorkflowRecordsRequest, opts ...http.CallOption) (rsp *ListWorkflowRecordsResponse, err error)
	ListDeploymentGroupRevisions(ctx context.Context, req *ListDeploymentGroupRevisionsOptions, opts ...http.CallOption) (rsp *ListDeploymentGroupRevisionsResult, err error)
	ListDeploymentGroups(ctx context.Context, req *ListDeploymentGroupOptions, opts ...http.CallOption) (rsp *ListDeploymentGroupResult, err error)
	ListServiceExports(ctx context.Context, req *ListServiceExportRequest, opts ...http.CallOption) (rsp *ListServiceExportResponse, err error)
	RecycleDeploymentGroupWorkload(ctx context.Context, req *RecycleDeploymentGroupWorkloadRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	ResumeDeploymentGroupRecord(ctx context.Context, req *ResumeDeploymentGroupRecordRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	RollbackDeploymentGroupRecord(ctx context.Context, req *RollbackDeploymentGroupRecordRequest, opts ...http.CallOption) (rsp *WorkflowRecord, err error)
	RollbackDeploymentGroupRevision(ctx context.Context, req *RollbackDeploymentGroupRevisionRequest, opts ...http.CallOption) (rsp *RollbackDeploymentGroupRevisionResponse, err error)
	ScaleDeploymentGroup(ctx context.Context, req *ScaleDeploymentGroupRequest, opts ...http.CallOption) (rsp *ScaleDeploymentGroupResponse, err error)
	TerminateDeploymentGroupRecord(ctx context.Context, req *TerminateDeploymentGroupRecordRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	UpdateApplication(ctx context.Context, req *UpdateApplicationRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	UpdateDeploymentGroup(ctx context.Context, req *UpdateDeploymentGroupRequest, opts ...http.CallOption) (rsp *DeploymentGroup, err error)
}

type ApplicationServiceHTTPClientImpl struct {
	cc *http.Client
}

func NewApplicationServiceHTTPClient(client *http.Client) ApplicationServiceHTTPClient {
	return &ApplicationServiceHTTPClientImpl{client}
}

func (c *ApplicationServiceHTTPClientImpl) CreateApplication(ctx context.Context, in *CreateApplicationRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/apis/v1/application"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationApplicationServiceCreateApplication))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ApplicationServiceHTTPClientImpl) CreateDeploymentGroup(ctx context.Context, in *CreateDeploymentGroupRequest, opts ...http.CallOption) (*DeploymentGroup, error) {
	var out DeploymentGroup
	pattern := "/apis/v1/deployment"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationApplicationServiceCreateDeploymentGroup))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ApplicationServiceHTTPClientImpl) DeleteApplication(ctx context.Context, in *DeleteApplicationRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/apis/v1/application/{appName}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationApplicationServiceDeleteApplication))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ApplicationServiceHTTPClientImpl) DeleteDeploymentGroup(ctx context.Context, in *DeleteDeploymentGroupRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/apis/v1/deployment/{deploymentGroupId}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationApplicationServiceDeleteDeploymentGroup))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ApplicationServiceHTTPClientImpl) DeployDeploymentGroup(ctx context.Context, in *DeployRequest, opts ...http.CallOption) (*DeployResponse, error) {
	var out DeployResponse
	pattern := "/apis/v1/deployment/{deploymentGroupId}/deploy"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationApplicationServiceDeployDeploymentGroup))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ApplicationServiceHTTPClientImpl) DetailDeploymentGroupRecord(ctx context.Context, in *DetailDeploymentGroupRecordRequest, opts ...http.CallOption) (*WorkflowRecord, error) {
	var out WorkflowRecord
	pattern := "/apis/v1/deployment/{deploymentGroupId}/records/{recordName}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationApplicationServiceDetailDeploymentGroupRecord))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ApplicationServiceHTTPClientImpl) DetailDeploymentGroupRevision(ctx context.Context, in *DetailDeploymentGroupRevisionRequest, opts ...http.CallOption) (*DeploymentGroupRevision, error) {
	var out DeploymentGroupRevision
	pattern := "/apis/v1/deployment/{deploymentGroupId}/revisions/{revision}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationApplicationServiceDetailDeploymentGroupRevision))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ApplicationServiceHTTPClientImpl) GetApplicationBase(ctx context.Context, in *GetApplicationRequest, opts ...http.CallOption) (*ApplicationBase, error) {
	var out ApplicationBase
	pattern := "/apis/v1/application/{appName}/base"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationApplicationServiceGetApplicationBase))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ApplicationServiceHTTPClientImpl) GetDeploymentGroupDetail(ctx context.Context, in *GetDeploymentStatusRequest, opts ...http.CallOption) (*DeploymentGroup, error) {
	var out DeploymentGroup
	pattern := "/apis/v1/deployment/{deploymentGroupId}/detail"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationApplicationServiceGetDeploymentGroupDetail))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ApplicationServiceHTTPClientImpl) GetDeploymentGroupRecordLogs(ctx context.Context, in *DeploymentGroupRecordLogsRequest, opts ...http.CallOption) (*DeploymentGroupRecordLogsResponse, error) {
	var out DeploymentGroupRecordLogsResponse
	pattern := "/apis/v1/deployment/{deploymentGroupId}/record/{recordName}/logs"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationApplicationServiceGetDeploymentGroupRecordLogs))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ApplicationServiceHTTPClientImpl) GetDeploymentGroupVolumes(ctx context.Context, in *GetDeploymentGroupVolumesRequest, opts ...http.CallOption) (*ListVolumes, error) {
	var out ListVolumes
	pattern := "/apis/v1/deployment/{deploymentGroupId}/volumes"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationApplicationServiceGetDeploymentGroupVolumes))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ApplicationServiceHTTPClientImpl) ListApplications(ctx context.Context, in *ListApplicationOptions, opts ...http.CallOption) (*ListApplicationResult, error) {
	var out ListApplicationResult
	pattern := "/apis/v1/applications"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationApplicationServiceListApplications))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ApplicationServiceHTTPClientImpl) ListDeploymentGroupRecords(ctx context.Context, in *ListWorkflowRecordsRequest, opts ...http.CallOption) (*ListWorkflowRecordsResponse, error) {
	var out ListWorkflowRecordsResponse
	pattern := "/apis/v1/deployment/{deploymentGroupId}/records"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationApplicationServiceListDeploymentGroupRecords))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ApplicationServiceHTTPClientImpl) ListDeploymentGroupRevisions(ctx context.Context, in *ListDeploymentGroupRevisionsOptions, opts ...http.CallOption) (*ListDeploymentGroupRevisionsResult, error) {
	var out ListDeploymentGroupRevisionsResult
	pattern := "/apis/v1/deployment/{deploymentGroupId}/revisions"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationApplicationServiceListDeploymentGroupRevisions))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ApplicationServiceHTTPClientImpl) ListDeploymentGroups(ctx context.Context, in *ListDeploymentGroupOptions, opts ...http.CallOption) (*ListDeploymentGroupResult, error) {
	var out ListDeploymentGroupResult
	pattern := "/apis/v1/application/{appName}/deployments"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationApplicationServiceListDeploymentGroups))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ApplicationServiceHTTPClientImpl) ListServiceExports(ctx context.Context, in *ListServiceExportRequest, opts ...http.CallOption) (*ListServiceExportResponse, error) {
	var out ListServiceExportResponse
	pattern := "/apis/v1/application/{appName}/service-exports"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationApplicationServiceListServiceExports))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ApplicationServiceHTTPClientImpl) RecycleDeploymentGroupWorkload(ctx context.Context, in *RecycleDeploymentGroupWorkloadRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/apis/v1/deployment/{deploymentGroupId}/recycle"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationApplicationServiceRecycleDeploymentGroupWorkload))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ApplicationServiceHTTPClientImpl) ResumeDeploymentGroupRecord(ctx context.Context, in *ResumeDeploymentGroupRecordRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/apis/v1/deployment/{deploymentGroupId}/record/{recordName}/resume"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationApplicationServiceResumeDeploymentGroupRecord))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ApplicationServiceHTTPClientImpl) RollbackDeploymentGroupRecord(ctx context.Context, in *RollbackDeploymentGroupRecordRequest, opts ...http.CallOption) (*WorkflowRecord, error) {
	var out WorkflowRecord
	pattern := "/apis/v1/deployment/{deploymentGroupId}/record/{recordName}/rollback"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationApplicationServiceRollbackDeploymentGroupRecord))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ApplicationServiceHTTPClientImpl) RollbackDeploymentGroupRevision(ctx context.Context, in *RollbackDeploymentGroupRevisionRequest, opts ...http.CallOption) (*RollbackDeploymentGroupRevisionResponse, error) {
	var out RollbackDeploymentGroupRevisionResponse
	pattern := "/apis/v1/deployment/{deploymentGroupId}/revisions/{revision}/rollback"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationApplicationServiceRollbackDeploymentGroupRevision))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ApplicationServiceHTTPClientImpl) ScaleDeploymentGroup(ctx context.Context, in *ScaleDeploymentGroupRequest, opts ...http.CallOption) (*ScaleDeploymentGroupResponse, error) {
	var out ScaleDeploymentGroupResponse
	pattern := "/apis/v1/deployment/{deploymentGroupId}/scale"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationApplicationServiceScaleDeploymentGroup))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ApplicationServiceHTTPClientImpl) TerminateDeploymentGroupRecord(ctx context.Context, in *TerminateDeploymentGroupRecordRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/apis/v1/deployment/{deploymentGroupId}/record/{recordName}/terminate"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationApplicationServiceTerminateDeploymentGroupRecord))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ApplicationServiceHTTPClientImpl) UpdateApplication(ctx context.Context, in *UpdateApplicationRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/apis/v1/application/{appName}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationApplicationServiceUpdateApplication))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ApplicationServiceHTTPClientImpl) UpdateDeploymentGroup(ctx context.Context, in *UpdateDeploymentGroupRequest, opts ...http.CallOption) (*DeploymentGroup, error) {
	var out DeploymentGroup
	pattern := "/apis/v1/deployment/{deploymentGroupId}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationApplicationServiceUpdateDeploymentGroup))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
