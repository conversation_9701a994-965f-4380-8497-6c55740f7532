// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.3
// - protoc             v5.29.3
// source: aistudio/domain/v1/domain.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationDomainServiceCreateDomain = "/apis.aistudio.domain.v1.DomainService/CreateDomain"
const OperationDomainServiceDeleteDomain = "/apis.aistudio.domain.v1.DomainService/DeleteDomain"
const OperationDomainServiceGetDomain = "/apis.aistudio.domain.v1.DomainService/GetDomain"
const OperationDomainServiceGetDomainStatus = "/apis.aistudio.domain.v1.DomainService/GetDomainStatus"
const OperationDomainServiceListAllDomain = "/apis.aistudio.domain.v1.DomainService/ListAllDomain"
const OperationDomainServiceListDomain = "/apis.aistudio.domain.v1.DomainService/ListDomain"
const OperationDomainServiceUpdateDomain = "/apis.aistudio.domain.v1.DomainService/UpdateDomain"

type DomainServiceHTTPServer interface {
	CreateDomain(context.Context, *CreateOrUpdateDomainRequest) (*Domain, error)
	DeleteDomain(context.Context, *DeleteDomainRequest) (*emptypb.Empty, error)
	GetDomain(context.Context, *GetDomainRequest) (*Domain, error)
	GetDomainStatus(context.Context, *GetDomainRequest) (*DomainStatus, error)
	ListAllDomain(context.Context, *ListDomainOptions) (*ListDomainResponse, error)
	ListDomain(context.Context, *ListDomainOptions) (*ListDomainResponse, error)
	UpdateDomain(context.Context, *CreateOrUpdateDomainRequest) (*Domain, error)
}

func RegisterDomainServiceHTTPServer(s *http.Server, srv DomainServiceHTTPServer) {
	r := s.Route("/")
	r.POST("/apis/v1/workspace/{workspaceName}/domain", _DomainService_CreateDomain0_HTTP_Handler(srv))
	r.PUT("/apis/v1/workspace/{workspaceName}/domain/{name}", _DomainService_UpdateDomain0_HTTP_Handler(srv))
	r.GET("/apis/v1/workspace/{workspaceName}/domain/{name}", _DomainService_GetDomain0_HTTP_Handler(srv))
	r.GET("/apis/v1/workspace/{workspaceName}/domains", _DomainService_ListDomain0_HTTP_Handler(srv))
	r.GET("/apis/v1/domains", _DomainService_ListAllDomain0_HTTP_Handler(srv))
	r.DELETE("/apis/v1/workspace/{workspaceName}/domain/{name}", _DomainService_DeleteDomain0_HTTP_Handler(srv))
	r.GET("/apis/v1/workspace/{workspaceName}/domain/{name}/status", _DomainService_GetDomainStatus0_HTTP_Handler(srv))
}

func _DomainService_CreateDomain0_HTTP_Handler(srv DomainServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateOrUpdateDomainRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationDomainServiceCreateDomain)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateDomain(ctx, req.(*CreateOrUpdateDomainRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Domain)
		return ctx.Result(200, reply)
	}
}

func _DomainService_UpdateDomain0_HTTP_Handler(srv DomainServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateOrUpdateDomainRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationDomainServiceUpdateDomain)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateDomain(ctx, req.(*CreateOrUpdateDomainRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Domain)
		return ctx.Result(200, reply)
	}
}

func _DomainService_GetDomain0_HTTP_Handler(srv DomainServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetDomainRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationDomainServiceGetDomain)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetDomain(ctx, req.(*GetDomainRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Domain)
		return ctx.Result(200, reply)
	}
}

func _DomainService_ListDomain0_HTTP_Handler(srv DomainServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListDomainOptions
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationDomainServiceListDomain)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListDomain(ctx, req.(*ListDomainOptions))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListDomainResponse)
		return ctx.Result(200, reply)
	}
}

func _DomainService_ListAllDomain0_HTTP_Handler(srv DomainServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListDomainOptions
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationDomainServiceListAllDomain)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListAllDomain(ctx, req.(*ListDomainOptions))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListDomainResponse)
		return ctx.Result(200, reply)
	}
}

func _DomainService_DeleteDomain0_HTTP_Handler(srv DomainServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteDomainRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationDomainServiceDeleteDomain)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteDomain(ctx, req.(*DeleteDomainRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _DomainService_GetDomainStatus0_HTTP_Handler(srv DomainServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetDomainRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationDomainServiceGetDomainStatus)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetDomainStatus(ctx, req.(*GetDomainRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DomainStatus)
		return ctx.Result(200, reply)
	}
}

type DomainServiceHTTPClient interface {
	CreateDomain(ctx context.Context, req *CreateOrUpdateDomainRequest, opts ...http.CallOption) (rsp *Domain, err error)
	DeleteDomain(ctx context.Context, req *DeleteDomainRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	GetDomain(ctx context.Context, req *GetDomainRequest, opts ...http.CallOption) (rsp *Domain, err error)
	GetDomainStatus(ctx context.Context, req *GetDomainRequest, opts ...http.CallOption) (rsp *DomainStatus, err error)
	ListAllDomain(ctx context.Context, req *ListDomainOptions, opts ...http.CallOption) (rsp *ListDomainResponse, err error)
	ListDomain(ctx context.Context, req *ListDomainOptions, opts ...http.CallOption) (rsp *ListDomainResponse, err error)
	UpdateDomain(ctx context.Context, req *CreateOrUpdateDomainRequest, opts ...http.CallOption) (rsp *Domain, err error)
}

type DomainServiceHTTPClientImpl struct {
	cc *http.Client
}

func NewDomainServiceHTTPClient(client *http.Client) DomainServiceHTTPClient {
	return &DomainServiceHTTPClientImpl{client}
}

func (c *DomainServiceHTTPClientImpl) CreateDomain(ctx context.Context, in *CreateOrUpdateDomainRequest, opts ...http.CallOption) (*Domain, error) {
	var out Domain
	pattern := "/apis/v1/workspace/{workspaceName}/domain"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationDomainServiceCreateDomain))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *DomainServiceHTTPClientImpl) DeleteDomain(ctx context.Context, in *DeleteDomainRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/apis/v1/workspace/{workspaceName}/domain/{name}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationDomainServiceDeleteDomain))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *DomainServiceHTTPClientImpl) GetDomain(ctx context.Context, in *GetDomainRequest, opts ...http.CallOption) (*Domain, error) {
	var out Domain
	pattern := "/apis/v1/workspace/{workspaceName}/domain/{name}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationDomainServiceGetDomain))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *DomainServiceHTTPClientImpl) GetDomainStatus(ctx context.Context, in *GetDomainRequest, opts ...http.CallOption) (*DomainStatus, error) {
	var out DomainStatus
	pattern := "/apis/v1/workspace/{workspaceName}/domain/{name}/status"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationDomainServiceGetDomainStatus))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *DomainServiceHTTPClientImpl) ListAllDomain(ctx context.Context, in *ListDomainOptions, opts ...http.CallOption) (*ListDomainResponse, error) {
	var out ListDomainResponse
	pattern := "/apis/v1/domains"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationDomainServiceListAllDomain))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *DomainServiceHTTPClientImpl) ListDomain(ctx context.Context, in *ListDomainOptions, opts ...http.CallOption) (*ListDomainResponse, error) {
	var out ListDomainResponse
	pattern := "/apis/v1/workspace/{workspaceName}/domains"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationDomainServiceListDomain))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *DomainServiceHTTPClientImpl) UpdateDomain(ctx context.Context, in *CreateOrUpdateDomainRequest, opts ...http.CallOption) (*Domain, error) {
	var out Domain
	pattern := "/apis/v1/workspace/{workspaceName}/domain/{name}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationDomainServiceUpdateDomain))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
