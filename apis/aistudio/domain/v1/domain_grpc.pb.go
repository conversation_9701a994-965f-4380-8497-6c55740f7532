// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v5.29.3
// source: aistudio/domain/v1/domain.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	DomainService_CreateDomain_FullMethodName    = "/apis.aistudio.domain.v1.DomainService/CreateDomain"
	DomainService_UpdateDomain_FullMethodName    = "/apis.aistudio.domain.v1.DomainService/UpdateDomain"
	DomainService_GetDomain_FullMethodName       = "/apis.aistudio.domain.v1.DomainService/GetDomain"
	DomainService_ListDomain_FullMethodName      = "/apis.aistudio.domain.v1.DomainService/ListDomain"
	DomainService_ListAllDomain_FullMethodName   = "/apis.aistudio.domain.v1.DomainService/ListAllDomain"
	DomainService_DeleteDomain_FullMethodName    = "/apis.aistudio.domain.v1.DomainService/DeleteDomain"
	DomainService_GetDomainStatus_FullMethodName = "/apis.aistudio.domain.v1.DomainService/GetDomainStatus"
)

// DomainServiceClient is the client API for DomainService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type DomainServiceClient interface {
	CreateDomain(ctx context.Context, in *CreateOrUpdateDomainRequest, opts ...grpc.CallOption) (*Domain, error)
	UpdateDomain(ctx context.Context, in *CreateOrUpdateDomainRequest, opts ...grpc.CallOption) (*Domain, error)
	GetDomain(ctx context.Context, in *GetDomainRequest, opts ...grpc.CallOption) (*Domain, error)
	ListDomain(ctx context.Context, in *ListDomainOptions, opts ...grpc.CallOption) (*ListDomainResponse, error)
	ListAllDomain(ctx context.Context, in *ListDomainOptions, opts ...grpc.CallOption) (*ListDomainResponse, error)
	DeleteDomain(ctx context.Context, in *DeleteDomainRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	GetDomainStatus(ctx context.Context, in *GetDomainRequest, opts ...grpc.CallOption) (*DomainStatus, error)
}

type domainServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewDomainServiceClient(cc grpc.ClientConnInterface) DomainServiceClient {
	return &domainServiceClient{cc}
}

func (c *domainServiceClient) CreateDomain(ctx context.Context, in *CreateOrUpdateDomainRequest, opts ...grpc.CallOption) (*Domain, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(Domain)
	err := c.cc.Invoke(ctx, DomainService_CreateDomain_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *domainServiceClient) UpdateDomain(ctx context.Context, in *CreateOrUpdateDomainRequest, opts ...grpc.CallOption) (*Domain, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(Domain)
	err := c.cc.Invoke(ctx, DomainService_UpdateDomain_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *domainServiceClient) GetDomain(ctx context.Context, in *GetDomainRequest, opts ...grpc.CallOption) (*Domain, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(Domain)
	err := c.cc.Invoke(ctx, DomainService_GetDomain_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *domainServiceClient) ListDomain(ctx context.Context, in *ListDomainOptions, opts ...grpc.CallOption) (*ListDomainResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListDomainResponse)
	err := c.cc.Invoke(ctx, DomainService_ListDomain_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *domainServiceClient) ListAllDomain(ctx context.Context, in *ListDomainOptions, opts ...grpc.CallOption) (*ListDomainResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListDomainResponse)
	err := c.cc.Invoke(ctx, DomainService_ListAllDomain_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *domainServiceClient) DeleteDomain(ctx context.Context, in *DeleteDomainRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, DomainService_DeleteDomain_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *domainServiceClient) GetDomainStatus(ctx context.Context, in *GetDomainRequest, opts ...grpc.CallOption) (*DomainStatus, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DomainStatus)
	err := c.cc.Invoke(ctx, DomainService_GetDomainStatus_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// DomainServiceServer is the server API for DomainService service.
// All implementations must embed UnimplementedDomainServiceServer
// for forward compatibility
type DomainServiceServer interface {
	CreateDomain(context.Context, *CreateOrUpdateDomainRequest) (*Domain, error)
	UpdateDomain(context.Context, *CreateOrUpdateDomainRequest) (*Domain, error)
	GetDomain(context.Context, *GetDomainRequest) (*Domain, error)
	ListDomain(context.Context, *ListDomainOptions) (*ListDomainResponse, error)
	ListAllDomain(context.Context, *ListDomainOptions) (*ListDomainResponse, error)
	DeleteDomain(context.Context, *DeleteDomainRequest) (*emptypb.Empty, error)
	GetDomainStatus(context.Context, *GetDomainRequest) (*DomainStatus, error)
	mustEmbedUnimplementedDomainServiceServer()
}

// UnimplementedDomainServiceServer must be embedded to have forward compatible implementations.
type UnimplementedDomainServiceServer struct {
}

func (UnimplementedDomainServiceServer) CreateDomain(context.Context, *CreateOrUpdateDomainRequest) (*Domain, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateDomain not implemented")
}
func (UnimplementedDomainServiceServer) UpdateDomain(context.Context, *CreateOrUpdateDomainRequest) (*Domain, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateDomain not implemented")
}
func (UnimplementedDomainServiceServer) GetDomain(context.Context, *GetDomainRequest) (*Domain, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDomain not implemented")
}
func (UnimplementedDomainServiceServer) ListDomain(context.Context, *ListDomainOptions) (*ListDomainResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListDomain not implemented")
}
func (UnimplementedDomainServiceServer) ListAllDomain(context.Context, *ListDomainOptions) (*ListDomainResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListAllDomain not implemented")
}
func (UnimplementedDomainServiceServer) DeleteDomain(context.Context, *DeleteDomainRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteDomain not implemented")
}
func (UnimplementedDomainServiceServer) GetDomainStatus(context.Context, *GetDomainRequest) (*DomainStatus, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDomainStatus not implemented")
}
func (UnimplementedDomainServiceServer) mustEmbedUnimplementedDomainServiceServer() {}

// UnsafeDomainServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to DomainServiceServer will
// result in compilation errors.
type UnsafeDomainServiceServer interface {
	mustEmbedUnimplementedDomainServiceServer()
}

func RegisterDomainServiceServer(s grpc.ServiceRegistrar, srv DomainServiceServer) {
	s.RegisterService(&DomainService_ServiceDesc, srv)
}

func _DomainService_CreateDomain_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateOrUpdateDomainRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DomainServiceServer).CreateDomain(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DomainService_CreateDomain_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DomainServiceServer).CreateDomain(ctx, req.(*CreateOrUpdateDomainRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DomainService_UpdateDomain_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateOrUpdateDomainRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DomainServiceServer).UpdateDomain(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DomainService_UpdateDomain_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DomainServiceServer).UpdateDomain(ctx, req.(*CreateOrUpdateDomainRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DomainService_GetDomain_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDomainRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DomainServiceServer).GetDomain(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DomainService_GetDomain_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DomainServiceServer).GetDomain(ctx, req.(*GetDomainRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DomainService_ListDomain_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListDomainOptions)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DomainServiceServer).ListDomain(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DomainService_ListDomain_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DomainServiceServer).ListDomain(ctx, req.(*ListDomainOptions))
	}
	return interceptor(ctx, in, info, handler)
}

func _DomainService_ListAllDomain_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListDomainOptions)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DomainServiceServer).ListAllDomain(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DomainService_ListAllDomain_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DomainServiceServer).ListAllDomain(ctx, req.(*ListDomainOptions))
	}
	return interceptor(ctx, in, info, handler)
}

func _DomainService_DeleteDomain_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteDomainRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DomainServiceServer).DeleteDomain(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DomainService_DeleteDomain_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DomainServiceServer).DeleteDomain(ctx, req.(*DeleteDomainRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DomainService_GetDomainStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDomainRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DomainServiceServer).GetDomainStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DomainService_GetDomainStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DomainServiceServer).GetDomainStatus(ctx, req.(*GetDomainRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// DomainService_ServiceDesc is the grpc.ServiceDesc for DomainService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var DomainService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "apis.aistudio.domain.v1.DomainService",
	HandlerType: (*DomainServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateDomain",
			Handler:    _DomainService_CreateDomain_Handler,
		},
		{
			MethodName: "UpdateDomain",
			Handler:    _DomainService_UpdateDomain_Handler,
		},
		{
			MethodName: "GetDomain",
			Handler:    _DomainService_GetDomain_Handler,
		},
		{
			MethodName: "ListDomain",
			Handler:    _DomainService_ListDomain_Handler,
		},
		{
			MethodName: "ListAllDomain",
			Handler:    _DomainService_ListAllDomain_Handler,
		},
		{
			MethodName: "DeleteDomain",
			Handler:    _DomainService_DeleteDomain_Handler,
		},
		{
			MethodName: "GetDomainStatus",
			Handler:    _DomainService_GetDomainStatus_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "aistudio/domain/v1/domain.proto",
}
