// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.3
// - protoc             v5.29.3
// source: aistudio/config/v1/config.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationConfigServiceCheckConfigExistence = "/apis.aistudio.config.v1.ConfigService/CheckConfigExistence"
const OperationConfigServiceCreateConfig = "/apis.aistudio.config.v1.ConfigService/CreateConfig"
const OperationConfigServiceDeleteConfig = "/apis.aistudio.config.v1.ConfigService/DeleteConfig"
const OperationConfigServiceListConfigs = "/apis.aistudio.config.v1.ConfigService/ListConfigs"
const OperationConfigServiceUpdateConfig = "/apis.aistudio.config.v1.ConfigService/UpdateConfig"

type ConfigServiceHTTPServer interface {
	// CheckConfigExistence检查配置
	CheckConfigExistence(context.Context, *CheckConfigExistenceRequest) (*CheckConfigExistenceResponse, error)
	// CreateConfig添加配置
	CreateConfig(context.Context, *CreateOrUpdateConfigRequest) (*CreateConfigResponse, error)
	// DeleteConfig删除配置
	DeleteConfig(context.Context, *DeleteConfigRequest) (*DeleteConfigResponse, error)
	// ListConfigs获取配置列表
	ListConfigs(context.Context, *ListConfigsRequest) (*ListConfigsResponse, error)
	// UpdateConfig更新配置
	UpdateConfig(context.Context, *CreateOrUpdateConfigRequest) (*UpdateConfigResponse, error)
}

func RegisterConfigServiceHTTPServer(s *http.Server, srv ConfigServiceHTTPServer) {
	r := s.Route("/")
	r.POST("/apis/v1/workspace/{workspaceName}/config", _ConfigService_CreateConfig0_HTTP_Handler(srv))
	r.GET("/apis/v1/workspace/{workspaceName}/configs", _ConfigService_ListConfigs0_HTTP_Handler(srv))
	r.PUT("/apis/v1/workspace/{workspaceName}/config/{name}", _ConfigService_UpdateConfig0_HTTP_Handler(srv))
	r.DELETE("/apis/v1/workspace/{workspaceName}/config/{name}", _ConfigService_DeleteConfig0_HTTP_Handler(srv))
	r.GET("/apis/v1/workspace/{workspaceName}/config/{name}/existence", _ConfigService_CheckConfigExistence0_HTTP_Handler(srv))
}

func _ConfigService_CreateConfig0_HTTP_Handler(srv ConfigServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateOrUpdateConfigRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationConfigServiceCreateConfig)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateConfig(ctx, req.(*CreateOrUpdateConfigRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CreateConfigResponse)
		return ctx.Result(200, reply)
	}
}

func _ConfigService_ListConfigs0_HTTP_Handler(srv ConfigServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListConfigsRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationConfigServiceListConfigs)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListConfigs(ctx, req.(*ListConfigsRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListConfigsResponse)
		return ctx.Result(200, reply)
	}
}

func _ConfigService_UpdateConfig0_HTTP_Handler(srv ConfigServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateOrUpdateConfigRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationConfigServiceUpdateConfig)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateConfig(ctx, req.(*CreateOrUpdateConfigRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*UpdateConfigResponse)
		return ctx.Result(200, reply)
	}
}

func _ConfigService_DeleteConfig0_HTTP_Handler(srv ConfigServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteConfigRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationConfigServiceDeleteConfig)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteConfig(ctx, req.(*DeleteConfigRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DeleteConfigResponse)
		return ctx.Result(200, reply)
	}
}

func _ConfigService_CheckConfigExistence0_HTTP_Handler(srv ConfigServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CheckConfigExistenceRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationConfigServiceCheckConfigExistence)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CheckConfigExistence(ctx, req.(*CheckConfigExistenceRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CheckConfigExistenceResponse)
		return ctx.Result(200, reply)
	}
}

type ConfigServiceHTTPClient interface {
	CheckConfigExistence(ctx context.Context, req *CheckConfigExistenceRequest, opts ...http.CallOption) (rsp *CheckConfigExistenceResponse, err error)
	CreateConfig(ctx context.Context, req *CreateOrUpdateConfigRequest, opts ...http.CallOption) (rsp *CreateConfigResponse, err error)
	DeleteConfig(ctx context.Context, req *DeleteConfigRequest, opts ...http.CallOption) (rsp *DeleteConfigResponse, err error)
	ListConfigs(ctx context.Context, req *ListConfigsRequest, opts ...http.CallOption) (rsp *ListConfigsResponse, err error)
	UpdateConfig(ctx context.Context, req *CreateOrUpdateConfigRequest, opts ...http.CallOption) (rsp *UpdateConfigResponse, err error)
}

type ConfigServiceHTTPClientImpl struct {
	cc *http.Client
}

func NewConfigServiceHTTPClient(client *http.Client) ConfigServiceHTTPClient {
	return &ConfigServiceHTTPClientImpl{client}
}

func (c *ConfigServiceHTTPClientImpl) CheckConfigExistence(ctx context.Context, in *CheckConfigExistenceRequest, opts ...http.CallOption) (*CheckConfigExistenceResponse, error) {
	var out CheckConfigExistenceResponse
	pattern := "/apis/v1/workspace/{workspaceName}/config/{name}/existence"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationConfigServiceCheckConfigExistence))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ConfigServiceHTTPClientImpl) CreateConfig(ctx context.Context, in *CreateOrUpdateConfigRequest, opts ...http.CallOption) (*CreateConfigResponse, error) {
	var out CreateConfigResponse
	pattern := "/apis/v1/workspace/{workspaceName}/config"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationConfigServiceCreateConfig))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ConfigServiceHTTPClientImpl) DeleteConfig(ctx context.Context, in *DeleteConfigRequest, opts ...http.CallOption) (*DeleteConfigResponse, error) {
	var out DeleteConfigResponse
	pattern := "/apis/v1/workspace/{workspaceName}/config/{name}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationConfigServiceDeleteConfig))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ConfigServiceHTTPClientImpl) ListConfigs(ctx context.Context, in *ListConfigsRequest, opts ...http.CallOption) (*ListConfigsResponse, error) {
	var out ListConfigsResponse
	pattern := "/apis/v1/workspace/{workspaceName}/configs"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationConfigServiceListConfigs))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ConfigServiceHTTPClientImpl) UpdateConfig(ctx context.Context, in *CreateOrUpdateConfigRequest, opts ...http.CallOption) (*UpdateConfigResponse, error) {
	var out UpdateConfigResponse
	pattern := "/apis/v1/workspace/{workspaceName}/config/{name}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationConfigServiceUpdateConfig))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
