// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.3
// - protoc             v5.29.3
// source: aistudio/resourcegroup/v1/resourcegroup.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationResourceGroupServiceGetNodeStatistics = "/apis.aistudio.resourcegroup.v1.ResourceGroupService/GetNodeStatistics"
const OperationResourceGroupServiceGetResourceGroupMetrics = "/apis.aistudio.resourcegroup.v1.ResourceGroupService/GetResourceGroupMetrics"
const OperationResourceGroupServiceListNodes = "/apis.aistudio.resourcegroup.v1.ResourceGroupService/ListNodes"

type ResourceGroupServiceHTTPServer interface {
	// GetNodeStatistics 获取节点统计信息
	GetNodeStatistics(context.Context, *GetNodeStatisticsRequest) (*NodeStatistics, error)
	GetResourceGroupMetrics(context.Context, *GetResourceGroupMetricsRequest) (*ResourceGroupMetric, error)
	// ListNodes返回节点列表
	ListNodes(context.Context, *ListNodeDetailOptions) (*ListNodeDetailResult, error)
}

func RegisterResourceGroupServiceHTTPServer(s *http.Server, srv ResourceGroupServiceHTTPServer) {
	r := s.Route("/")
	r.GET("/apis/v1/workspace/{workspaceName}/nodes", _ResourceGroupService_ListNodes0_HTTP_Handler(srv))
	r.GET("/apis/v1/workspace/{workspaceName}/nodes/statistics", _ResourceGroupService_GetNodeStatistics0_HTTP_Handler(srv))
	r.GET("/apis/v1/workspace/{workspaceName}/resources/metrics", _ResourceGroupService_GetResourceGroupMetrics0_HTTP_Handler(srv))
}

func _ResourceGroupService_ListNodes0_HTTP_Handler(srv ResourceGroupServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListNodeDetailOptions
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationResourceGroupServiceListNodes)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListNodes(ctx, req.(*ListNodeDetailOptions))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListNodeDetailResult)
		return ctx.Result(200, reply)
	}
}

func _ResourceGroupService_GetNodeStatistics0_HTTP_Handler(srv ResourceGroupServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetNodeStatisticsRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationResourceGroupServiceGetNodeStatistics)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetNodeStatistics(ctx, req.(*GetNodeStatisticsRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*NodeStatistics)
		return ctx.Result(200, reply)
	}
}

func _ResourceGroupService_GetResourceGroupMetrics0_HTTP_Handler(srv ResourceGroupServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetResourceGroupMetricsRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationResourceGroupServiceGetResourceGroupMetrics)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetResourceGroupMetrics(ctx, req.(*GetResourceGroupMetricsRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ResourceGroupMetric)
		return ctx.Result(200, reply)
	}
}

type ResourceGroupServiceHTTPClient interface {
	GetNodeStatistics(ctx context.Context, req *GetNodeStatisticsRequest, opts ...http.CallOption) (rsp *NodeStatistics, err error)
	GetResourceGroupMetrics(ctx context.Context, req *GetResourceGroupMetricsRequest, opts ...http.CallOption) (rsp *ResourceGroupMetric, err error)
	ListNodes(ctx context.Context, req *ListNodeDetailOptions, opts ...http.CallOption) (rsp *ListNodeDetailResult, err error)
}

type ResourceGroupServiceHTTPClientImpl struct {
	cc *http.Client
}

func NewResourceGroupServiceHTTPClient(client *http.Client) ResourceGroupServiceHTTPClient {
	return &ResourceGroupServiceHTTPClientImpl{client}
}

func (c *ResourceGroupServiceHTTPClientImpl) GetNodeStatistics(ctx context.Context, in *GetNodeStatisticsRequest, opts ...http.CallOption) (*NodeStatistics, error) {
	var out NodeStatistics
	pattern := "/apis/v1/workspace/{workspaceName}/nodes/statistics"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationResourceGroupServiceGetNodeStatistics))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ResourceGroupServiceHTTPClientImpl) GetResourceGroupMetrics(ctx context.Context, in *GetResourceGroupMetricsRequest, opts ...http.CallOption) (*ResourceGroupMetric, error) {
	var out ResourceGroupMetric
	pattern := "/apis/v1/workspace/{workspaceName}/resources/metrics"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationResourceGroupServiceGetResourceGroupMetrics))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ResourceGroupServiceHTTPClientImpl) ListNodes(ctx context.Context, in *ListNodeDetailOptions, opts ...http.CallOption) (*ListNodeDetailResult, error) {
	var out ListNodeDetailResult
	pattern := "/apis/v1/workspace/{workspaceName}/nodes"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationResourceGroupServiceListNodes))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

const OperationNodeServiceChangeNodeGpuAllocationMode = "/apis.aistudio.resourcegroup.v1.NodeService/ChangeNodeGpuAllocationMode"
const OperationNodeServiceDisableNode = "/apis.aistudio.resourcegroup.v1.NodeService/DisableNode"
const OperationNodeServiceEvictNodeWorkloads = "/apis.aistudio.resourcegroup.v1.NodeService/EvictNodeWorkloads"
const OperationNodeServiceGetNodeDetail = "/apis.aistudio.resourcegroup.v1.NodeService/GetNodeDetail"
const OperationNodeServiceGetNodeMetric = "/apis.aistudio.resourcegroup.v1.NodeService/GetNodeMetric"
const OperationNodeServiceGetNodeMetricsView = "/apis.aistudio.resourcegroup.v1.NodeService/GetNodeMetricsView"
const OperationNodeServiceGetNodeWorkloads = "/apis.aistudio.resourcegroup.v1.NodeService/GetNodeWorkloads"
const OperationNodeServiceListNodeDetailForFrontend = "/apis.aistudio.resourcegroup.v1.NodeService/ListNodeDetailForFrontend"

type NodeServiceHTTPServer interface {
	// ChangeNodeGpuAllocationMode 改变节点GPU分配模式
	ChangeNodeGpuAllocationMode(context.Context, *ChangeNodeGpuAllocationModeRequest) (*emptypb.Empty, error)
	DisableNode(context.Context, *DisableNodeRequest) (*emptypb.Empty, error)
	// EvictNodeWorkloads 驱逐节点上的工作负载
	EvictNodeWorkloads(context.Context, *EvictNodeWorkloadsRequest) (*emptypb.Empty, error)
	GetNodeDetail(context.Context, *GetNodeDetailRequest) (*NodeDetail, error)
	GetNodeMetric(context.Context, *GetNodeMetricRequest) (*NodeMetric, error)
	GetNodeMetricsView(context.Context, *GetNodeViewRequest) (*NodeMetricView, error)
	// GetNodeWorkloads 获取节点上工作负载信息
	GetNodeWorkloads(context.Context, *GetNodeWorkloadsRequest) (*GetNodeWorkloadsResponse, error)
	// ListNodeDetailForFrontend 为了前端的一个数据结构
	ListNodeDetailForFrontend(context.Context, *ListNodeDetailForFrontendOptions) (*CascadeNodeDetails, error)
}

func RegisterNodeServiceHTTPServer(s *http.Server, srv NodeServiceHTTPServer) {
	r := s.Route("/")
	r.GET("/apis/v1/workspace/{workspaceName}/node/{ip}/detail", _NodeService_GetNodeDetail0_HTTP_Handler(srv))
	r.GET("/apis/v1/workspace/{workspaceName}/nodes/cascade", _NodeService_ListNodeDetailForFrontend0_HTTP_Handler(srv))
	r.GET("/apis/v1/workspace/{workspaceName}/node/metrics/view", _NodeService_GetNodeMetricsView0_HTTP_Handler(srv))
	r.GET("/apis/v1/workspace/{workspaceName}/node/{ip}/metrics", _NodeService_GetNodeMetric0_HTTP_Handler(srv))
	r.POST("/apis/v1/workspace/{workspaceName}/node/{ip}/disable", _NodeService_DisableNode0_HTTP_Handler(srv))
	r.GET("/apis/v1/workspace/{workspaceName}/node/{ip}/workloads", _NodeService_GetNodeWorkloads0_HTTP_Handler(srv))
	r.POST("/apis/v1/workspace/{workspaceName}/node/{ip}/evict", _NodeService_EvictNodeWorkloads0_HTTP_Handler(srv))
	r.POST("/apis/v1/workspace/{workspaceName}/node/{ip}/gpu-mode", _NodeService_ChangeNodeGpuAllocationMode0_HTTP_Handler(srv))
}

func _NodeService_GetNodeDetail0_HTTP_Handler(srv NodeServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetNodeDetailRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationNodeServiceGetNodeDetail)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetNodeDetail(ctx, req.(*GetNodeDetailRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*NodeDetail)
		return ctx.Result(200, reply)
	}
}

func _NodeService_ListNodeDetailForFrontend0_HTTP_Handler(srv NodeServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListNodeDetailForFrontendOptions
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationNodeServiceListNodeDetailForFrontend)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListNodeDetailForFrontend(ctx, req.(*ListNodeDetailForFrontendOptions))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CascadeNodeDetails)
		return ctx.Result(200, reply)
	}
}

func _NodeService_GetNodeMetricsView0_HTTP_Handler(srv NodeServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetNodeViewRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationNodeServiceGetNodeMetricsView)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetNodeMetricsView(ctx, req.(*GetNodeViewRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*NodeMetricView)
		return ctx.Result(200, reply)
	}
}

func _NodeService_GetNodeMetric0_HTTP_Handler(srv NodeServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetNodeMetricRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationNodeServiceGetNodeMetric)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetNodeMetric(ctx, req.(*GetNodeMetricRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*NodeMetric)
		return ctx.Result(200, reply)
	}
}

func _NodeService_DisableNode0_HTTP_Handler(srv NodeServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DisableNodeRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationNodeServiceDisableNode)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DisableNode(ctx, req.(*DisableNodeRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _NodeService_GetNodeWorkloads0_HTTP_Handler(srv NodeServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetNodeWorkloadsRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationNodeServiceGetNodeWorkloads)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetNodeWorkloads(ctx, req.(*GetNodeWorkloadsRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetNodeWorkloadsResponse)
		return ctx.Result(200, reply)
	}
}

func _NodeService_EvictNodeWorkloads0_HTTP_Handler(srv NodeServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in EvictNodeWorkloadsRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationNodeServiceEvictNodeWorkloads)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.EvictNodeWorkloads(ctx, req.(*EvictNodeWorkloadsRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _NodeService_ChangeNodeGpuAllocationMode0_HTTP_Handler(srv NodeServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ChangeNodeGpuAllocationModeRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationNodeServiceChangeNodeGpuAllocationMode)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ChangeNodeGpuAllocationMode(ctx, req.(*ChangeNodeGpuAllocationModeRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

type NodeServiceHTTPClient interface {
	ChangeNodeGpuAllocationMode(ctx context.Context, req *ChangeNodeGpuAllocationModeRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	DisableNode(ctx context.Context, req *DisableNodeRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	EvictNodeWorkloads(ctx context.Context, req *EvictNodeWorkloadsRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	GetNodeDetail(ctx context.Context, req *GetNodeDetailRequest, opts ...http.CallOption) (rsp *NodeDetail, err error)
	GetNodeMetric(ctx context.Context, req *GetNodeMetricRequest, opts ...http.CallOption) (rsp *NodeMetric, err error)
	GetNodeMetricsView(ctx context.Context, req *GetNodeViewRequest, opts ...http.CallOption) (rsp *NodeMetricView, err error)
	GetNodeWorkloads(ctx context.Context, req *GetNodeWorkloadsRequest, opts ...http.CallOption) (rsp *GetNodeWorkloadsResponse, err error)
	ListNodeDetailForFrontend(ctx context.Context, req *ListNodeDetailForFrontendOptions, opts ...http.CallOption) (rsp *CascadeNodeDetails, err error)
}

type NodeServiceHTTPClientImpl struct {
	cc *http.Client
}

func NewNodeServiceHTTPClient(client *http.Client) NodeServiceHTTPClient {
	return &NodeServiceHTTPClientImpl{client}
}

func (c *NodeServiceHTTPClientImpl) ChangeNodeGpuAllocationMode(ctx context.Context, in *ChangeNodeGpuAllocationModeRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/apis/v1/workspace/{workspaceName}/node/{ip}/gpu-mode"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationNodeServiceChangeNodeGpuAllocationMode))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *NodeServiceHTTPClientImpl) DisableNode(ctx context.Context, in *DisableNodeRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/apis/v1/workspace/{workspaceName}/node/{ip}/disable"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationNodeServiceDisableNode))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *NodeServiceHTTPClientImpl) EvictNodeWorkloads(ctx context.Context, in *EvictNodeWorkloadsRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/apis/v1/workspace/{workspaceName}/node/{ip}/evict"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationNodeServiceEvictNodeWorkloads))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *NodeServiceHTTPClientImpl) GetNodeDetail(ctx context.Context, in *GetNodeDetailRequest, opts ...http.CallOption) (*NodeDetail, error) {
	var out NodeDetail
	pattern := "/apis/v1/workspace/{workspaceName}/node/{ip}/detail"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationNodeServiceGetNodeDetail))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *NodeServiceHTTPClientImpl) GetNodeMetric(ctx context.Context, in *GetNodeMetricRequest, opts ...http.CallOption) (*NodeMetric, error) {
	var out NodeMetric
	pattern := "/apis/v1/workspace/{workspaceName}/node/{ip}/metrics"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationNodeServiceGetNodeMetric))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *NodeServiceHTTPClientImpl) GetNodeMetricsView(ctx context.Context, in *GetNodeViewRequest, opts ...http.CallOption) (*NodeMetricView, error) {
	var out NodeMetricView
	pattern := "/apis/v1/workspace/{workspaceName}/node/metrics/view"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationNodeServiceGetNodeMetricsView))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *NodeServiceHTTPClientImpl) GetNodeWorkloads(ctx context.Context, in *GetNodeWorkloadsRequest, opts ...http.CallOption) (*GetNodeWorkloadsResponse, error) {
	var out GetNodeWorkloadsResponse
	pattern := "/apis/v1/workspace/{workspaceName}/node/{ip}/workloads"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationNodeServiceGetNodeWorkloads))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *NodeServiceHTTPClientImpl) ListNodeDetailForFrontend(ctx context.Context, in *ListNodeDetailForFrontendOptions, opts ...http.CallOption) (*CascadeNodeDetails, error) {
	var out CascadeNodeDetails
	pattern := "/apis/v1/workspace/{workspaceName}/nodes/cascade"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationNodeServiceListNodeDetailForFrontend))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
