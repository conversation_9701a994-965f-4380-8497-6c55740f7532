// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v5.29.3
// source: aistudio/resourcegroup/v1/resourcegroup.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	ResourceGroupService_ListNodes_FullMethodName               = "/apis.aistudio.resourcegroup.v1.ResourceGroupService/ListNodes"
	ResourceGroupService_GetNodeStatistics_FullMethodName       = "/apis.aistudio.resourcegroup.v1.ResourceGroupService/GetNodeStatistics"
	ResourceGroupService_GetResourceGroupMetrics_FullMethodName = "/apis.aistudio.resourcegroup.v1.ResourceGroupService/GetResourceGroupMetrics"
)

// ResourceGroupServiceClient is the client API for ResourceGroupService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ResourceGroupServiceClient interface {
	// 返回节点列表
	ListNodes(ctx context.Context, in *ListNodeDetailOptions, opts ...grpc.CallOption) (*ListNodeDetailResult, error)
	// 获取节点统计信息
	GetNodeStatistics(ctx context.Context, in *GetNodeStatisticsRequest, opts ...grpc.CallOption) (*NodeStatistics, error)
	GetResourceGroupMetrics(ctx context.Context, in *GetResourceGroupMetricsRequest, opts ...grpc.CallOption) (*ResourceGroupMetric, error)
}

type resourceGroupServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewResourceGroupServiceClient(cc grpc.ClientConnInterface) ResourceGroupServiceClient {
	return &resourceGroupServiceClient{cc}
}

func (c *resourceGroupServiceClient) ListNodes(ctx context.Context, in *ListNodeDetailOptions, opts ...grpc.CallOption) (*ListNodeDetailResult, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListNodeDetailResult)
	err := c.cc.Invoke(ctx, ResourceGroupService_ListNodes_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *resourceGroupServiceClient) GetNodeStatistics(ctx context.Context, in *GetNodeStatisticsRequest, opts ...grpc.CallOption) (*NodeStatistics, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(NodeStatistics)
	err := c.cc.Invoke(ctx, ResourceGroupService_GetNodeStatistics_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *resourceGroupServiceClient) GetResourceGroupMetrics(ctx context.Context, in *GetResourceGroupMetricsRequest, opts ...grpc.CallOption) (*ResourceGroupMetric, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ResourceGroupMetric)
	err := c.cc.Invoke(ctx, ResourceGroupService_GetResourceGroupMetrics_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ResourceGroupServiceServer is the server API for ResourceGroupService service.
// All implementations must embed UnimplementedResourceGroupServiceServer
// for forward compatibility
type ResourceGroupServiceServer interface {
	// 返回节点列表
	ListNodes(context.Context, *ListNodeDetailOptions) (*ListNodeDetailResult, error)
	// 获取节点统计信息
	GetNodeStatistics(context.Context, *GetNodeStatisticsRequest) (*NodeStatistics, error)
	GetResourceGroupMetrics(context.Context, *GetResourceGroupMetricsRequest) (*ResourceGroupMetric, error)
	mustEmbedUnimplementedResourceGroupServiceServer()
}

// UnimplementedResourceGroupServiceServer must be embedded to have forward compatible implementations.
type UnimplementedResourceGroupServiceServer struct {
}

func (UnimplementedResourceGroupServiceServer) ListNodes(context.Context, *ListNodeDetailOptions) (*ListNodeDetailResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListNodes not implemented")
}
func (UnimplementedResourceGroupServiceServer) GetNodeStatistics(context.Context, *GetNodeStatisticsRequest) (*NodeStatistics, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNodeStatistics not implemented")
}
func (UnimplementedResourceGroupServiceServer) GetResourceGroupMetrics(context.Context, *GetResourceGroupMetricsRequest) (*ResourceGroupMetric, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetResourceGroupMetrics not implemented")
}
func (UnimplementedResourceGroupServiceServer) mustEmbedUnimplementedResourceGroupServiceServer() {}

// UnsafeResourceGroupServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ResourceGroupServiceServer will
// result in compilation errors.
type UnsafeResourceGroupServiceServer interface {
	mustEmbedUnimplementedResourceGroupServiceServer()
}

func RegisterResourceGroupServiceServer(s grpc.ServiceRegistrar, srv ResourceGroupServiceServer) {
	s.RegisterService(&ResourceGroupService_ServiceDesc, srv)
}

func _ResourceGroupService_ListNodes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListNodeDetailOptions)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ResourceGroupServiceServer).ListNodes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ResourceGroupService_ListNodes_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ResourceGroupServiceServer).ListNodes(ctx, req.(*ListNodeDetailOptions))
	}
	return interceptor(ctx, in, info, handler)
}

func _ResourceGroupService_GetNodeStatistics_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNodeStatisticsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ResourceGroupServiceServer).GetNodeStatistics(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ResourceGroupService_GetNodeStatistics_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ResourceGroupServiceServer).GetNodeStatistics(ctx, req.(*GetNodeStatisticsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ResourceGroupService_GetResourceGroupMetrics_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetResourceGroupMetricsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ResourceGroupServiceServer).GetResourceGroupMetrics(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ResourceGroupService_GetResourceGroupMetrics_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ResourceGroupServiceServer).GetResourceGroupMetrics(ctx, req.(*GetResourceGroupMetricsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// ResourceGroupService_ServiceDesc is the grpc.ServiceDesc for ResourceGroupService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ResourceGroupService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "apis.aistudio.resourcegroup.v1.ResourceGroupService",
	HandlerType: (*ResourceGroupServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListNodes",
			Handler:    _ResourceGroupService_ListNodes_Handler,
		},
		{
			MethodName: "GetNodeStatistics",
			Handler:    _ResourceGroupService_GetNodeStatistics_Handler,
		},
		{
			MethodName: "GetResourceGroupMetrics",
			Handler:    _ResourceGroupService_GetResourceGroupMetrics_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "aistudio/resourcegroup/v1/resourcegroup.proto",
}

const (
	NodeService_GetNodeDetail_FullMethodName               = "/apis.aistudio.resourcegroup.v1.NodeService/GetNodeDetail"
	NodeService_ListNodeDetailForFrontend_FullMethodName   = "/apis.aistudio.resourcegroup.v1.NodeService/ListNodeDetailForFrontend"
	NodeService_GetNodeMetricsView_FullMethodName          = "/apis.aistudio.resourcegroup.v1.NodeService/GetNodeMetricsView"
	NodeService_GetNodeMetric_FullMethodName               = "/apis.aistudio.resourcegroup.v1.NodeService/GetNodeMetric"
	NodeService_DisableNode_FullMethodName                 = "/apis.aistudio.resourcegroup.v1.NodeService/DisableNode"
	NodeService_GetNodeWorkloads_FullMethodName            = "/apis.aistudio.resourcegroup.v1.NodeService/GetNodeWorkloads"
	NodeService_EvictNodeWorkloads_FullMethodName          = "/apis.aistudio.resourcegroup.v1.NodeService/EvictNodeWorkloads"
	NodeService_ChangeNodeGpuAllocationMode_FullMethodName = "/apis.aistudio.resourcegroup.v1.NodeService/ChangeNodeGpuAllocationMode"
)

// NodeServiceClient is the client API for NodeService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type NodeServiceClient interface {
	GetNodeDetail(ctx context.Context, in *GetNodeDetailRequest, opts ...grpc.CallOption) (*NodeDetail, error)
	// 为了前端的一个数据结构
	ListNodeDetailForFrontend(ctx context.Context, in *ListNodeDetailForFrontendOptions, opts ...grpc.CallOption) (*CascadeNodeDetails, error)
	GetNodeMetricsView(ctx context.Context, in *GetNodeViewRequest, opts ...grpc.CallOption) (*NodeMetricView, error)
	GetNodeMetric(ctx context.Context, in *GetNodeMetricRequest, opts ...grpc.CallOption) (*NodeMetric, error)
	DisableNode(ctx context.Context, in *DisableNodeRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 获取节点上工作负载信息
	GetNodeWorkloads(ctx context.Context, in *GetNodeWorkloadsRequest, opts ...grpc.CallOption) (*GetNodeWorkloadsResponse, error)
	// 驱逐节点上的工作负载
	EvictNodeWorkloads(ctx context.Context, in *EvictNodeWorkloadsRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 改变节点GPU分配模式
	ChangeNodeGpuAllocationMode(ctx context.Context, in *ChangeNodeGpuAllocationModeRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
}

type nodeServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewNodeServiceClient(cc grpc.ClientConnInterface) NodeServiceClient {
	return &nodeServiceClient{cc}
}

func (c *nodeServiceClient) GetNodeDetail(ctx context.Context, in *GetNodeDetailRequest, opts ...grpc.CallOption) (*NodeDetail, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(NodeDetail)
	err := c.cc.Invoke(ctx, NodeService_GetNodeDetail_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeServiceClient) ListNodeDetailForFrontend(ctx context.Context, in *ListNodeDetailForFrontendOptions, opts ...grpc.CallOption) (*CascadeNodeDetails, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CascadeNodeDetails)
	err := c.cc.Invoke(ctx, NodeService_ListNodeDetailForFrontend_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeServiceClient) GetNodeMetricsView(ctx context.Context, in *GetNodeViewRequest, opts ...grpc.CallOption) (*NodeMetricView, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(NodeMetricView)
	err := c.cc.Invoke(ctx, NodeService_GetNodeMetricsView_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeServiceClient) GetNodeMetric(ctx context.Context, in *GetNodeMetricRequest, opts ...grpc.CallOption) (*NodeMetric, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(NodeMetric)
	err := c.cc.Invoke(ctx, NodeService_GetNodeMetric_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeServiceClient) DisableNode(ctx context.Context, in *DisableNodeRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, NodeService_DisableNode_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeServiceClient) GetNodeWorkloads(ctx context.Context, in *GetNodeWorkloadsRequest, opts ...grpc.CallOption) (*GetNodeWorkloadsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetNodeWorkloadsResponse)
	err := c.cc.Invoke(ctx, NodeService_GetNodeWorkloads_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeServiceClient) EvictNodeWorkloads(ctx context.Context, in *EvictNodeWorkloadsRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, NodeService_EvictNodeWorkloads_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeServiceClient) ChangeNodeGpuAllocationMode(ctx context.Context, in *ChangeNodeGpuAllocationModeRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, NodeService_ChangeNodeGpuAllocationMode_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// NodeServiceServer is the server API for NodeService service.
// All implementations must embed UnimplementedNodeServiceServer
// for forward compatibility
type NodeServiceServer interface {
	GetNodeDetail(context.Context, *GetNodeDetailRequest) (*NodeDetail, error)
	// 为了前端的一个数据结构
	ListNodeDetailForFrontend(context.Context, *ListNodeDetailForFrontendOptions) (*CascadeNodeDetails, error)
	GetNodeMetricsView(context.Context, *GetNodeViewRequest) (*NodeMetricView, error)
	GetNodeMetric(context.Context, *GetNodeMetricRequest) (*NodeMetric, error)
	DisableNode(context.Context, *DisableNodeRequest) (*emptypb.Empty, error)
	// 获取节点上工作负载信息
	GetNodeWorkloads(context.Context, *GetNodeWorkloadsRequest) (*GetNodeWorkloadsResponse, error)
	// 驱逐节点上的工作负载
	EvictNodeWorkloads(context.Context, *EvictNodeWorkloadsRequest) (*emptypb.Empty, error)
	// 改变节点GPU分配模式
	ChangeNodeGpuAllocationMode(context.Context, *ChangeNodeGpuAllocationModeRequest) (*emptypb.Empty, error)
	mustEmbedUnimplementedNodeServiceServer()
}

// UnimplementedNodeServiceServer must be embedded to have forward compatible implementations.
type UnimplementedNodeServiceServer struct {
}

func (UnimplementedNodeServiceServer) GetNodeDetail(context.Context, *GetNodeDetailRequest) (*NodeDetail, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNodeDetail not implemented")
}
func (UnimplementedNodeServiceServer) ListNodeDetailForFrontend(context.Context, *ListNodeDetailForFrontendOptions) (*CascadeNodeDetails, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListNodeDetailForFrontend not implemented")
}
func (UnimplementedNodeServiceServer) GetNodeMetricsView(context.Context, *GetNodeViewRequest) (*NodeMetricView, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNodeMetricsView not implemented")
}
func (UnimplementedNodeServiceServer) GetNodeMetric(context.Context, *GetNodeMetricRequest) (*NodeMetric, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNodeMetric not implemented")
}
func (UnimplementedNodeServiceServer) DisableNode(context.Context, *DisableNodeRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DisableNode not implemented")
}
func (UnimplementedNodeServiceServer) GetNodeWorkloads(context.Context, *GetNodeWorkloadsRequest) (*GetNodeWorkloadsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNodeWorkloads not implemented")
}
func (UnimplementedNodeServiceServer) EvictNodeWorkloads(context.Context, *EvictNodeWorkloadsRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method EvictNodeWorkloads not implemented")
}
func (UnimplementedNodeServiceServer) ChangeNodeGpuAllocationMode(context.Context, *ChangeNodeGpuAllocationModeRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ChangeNodeGpuAllocationMode not implemented")
}
func (UnimplementedNodeServiceServer) mustEmbedUnimplementedNodeServiceServer() {}

// UnsafeNodeServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to NodeServiceServer will
// result in compilation errors.
type UnsafeNodeServiceServer interface {
	mustEmbedUnimplementedNodeServiceServer()
}

func RegisterNodeServiceServer(s grpc.ServiceRegistrar, srv NodeServiceServer) {
	s.RegisterService(&NodeService_ServiceDesc, srv)
}

func _NodeService_GetNodeDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNodeDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeServiceServer).GetNodeDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeService_GetNodeDetail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeServiceServer).GetNodeDetail(ctx, req.(*GetNodeDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeService_ListNodeDetailForFrontend_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListNodeDetailForFrontendOptions)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeServiceServer).ListNodeDetailForFrontend(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeService_ListNodeDetailForFrontend_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeServiceServer).ListNodeDetailForFrontend(ctx, req.(*ListNodeDetailForFrontendOptions))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeService_GetNodeMetricsView_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNodeViewRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeServiceServer).GetNodeMetricsView(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeService_GetNodeMetricsView_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeServiceServer).GetNodeMetricsView(ctx, req.(*GetNodeViewRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeService_GetNodeMetric_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNodeMetricRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeServiceServer).GetNodeMetric(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeService_GetNodeMetric_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeServiceServer).GetNodeMetric(ctx, req.(*GetNodeMetricRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeService_DisableNode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DisableNodeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeServiceServer).DisableNode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeService_DisableNode_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeServiceServer).DisableNode(ctx, req.(*DisableNodeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeService_GetNodeWorkloads_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNodeWorkloadsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeServiceServer).GetNodeWorkloads(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeService_GetNodeWorkloads_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeServiceServer).GetNodeWorkloads(ctx, req.(*GetNodeWorkloadsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeService_EvictNodeWorkloads_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EvictNodeWorkloadsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeServiceServer).EvictNodeWorkloads(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeService_EvictNodeWorkloads_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeServiceServer).EvictNodeWorkloads(ctx, req.(*EvictNodeWorkloadsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeService_ChangeNodeGpuAllocationMode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChangeNodeGpuAllocationModeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeServiceServer).ChangeNodeGpuAllocationMode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeService_ChangeNodeGpuAllocationMode_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeServiceServer).ChangeNodeGpuAllocationMode(ctx, req.(*ChangeNodeGpuAllocationModeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// NodeService_ServiceDesc is the grpc.ServiceDesc for NodeService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var NodeService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "apis.aistudio.resourcegroup.v1.NodeService",
	HandlerType: (*NodeServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetNodeDetail",
			Handler:    _NodeService_GetNodeDetail_Handler,
		},
		{
			MethodName: "ListNodeDetailForFrontend",
			Handler:    _NodeService_ListNodeDetailForFrontend_Handler,
		},
		{
			MethodName: "GetNodeMetricsView",
			Handler:    _NodeService_GetNodeMetricsView_Handler,
		},
		{
			MethodName: "GetNodeMetric",
			Handler:    _NodeService_GetNodeMetric_Handler,
		},
		{
			MethodName: "DisableNode",
			Handler:    _NodeService_DisableNode_Handler,
		},
		{
			MethodName: "GetNodeWorkloads",
			Handler:    _NodeService_GetNodeWorkloads_Handler,
		},
		{
			MethodName: "EvictNodeWorkloads",
			Handler:    _NodeService_EvictNodeWorkloads_Handler,
		},
		{
			MethodName: "ChangeNodeGpuAllocationMode",
			Handler:    _NodeService_ChangeNodeGpuAllocationMode_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "aistudio/resourcegroup/v1/resourcegroup.proto",
}
