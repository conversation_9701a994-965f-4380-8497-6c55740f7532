// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v5.29.3
// source: aistudio/resourcegroup/v1/resourcegroup.proto

package v1

import (
	common "git.lianjia.com/cloudnative/kic/kic-platform/apis/common"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Kind int32

const (
	Kind_Training   Kind = 0
	Kind_Inference  Kind = 1
	Kind_Job        Kind = 2
	Kind_WebService Kind = 3
)

// Enum value maps for Kind.
var (
	Kind_name = map[int32]string{
		0: "Training",
		1: "Inference",
		2: "Job",
		3: "WebService",
	}
	Kind_value = map[string]int32{
		"Training":   0,
		"Inference":  1,
		"Job":        2,
		"WebService": 3,
	}
)

func (x Kind) Enum() *Kind {
	p := new(Kind)
	*p = x
	return p
}

func (x Kind) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Kind) Descriptor() protoreflect.EnumDescriptor {
	return file_aistudio_resourcegroup_v1_resourcegroup_proto_enumTypes[0].Descriptor()
}

func (Kind) Type() protoreflect.EnumType {
	return &file_aistudio_resourcegroup_v1_resourcegroup_proto_enumTypes[0]
}

func (x Kind) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Kind.Descriptor instead.
func (Kind) EnumDescriptor() ([]byte, []int) {
	return file_aistudio_resourcegroup_v1_resourcegroup_proto_rawDescGZIP(), []int{0}
}

type MachineType int32

const (
	MachineType_BareMetal MachineType = 0
	MachineType_CloudHost MachineType = 1
)

// Enum value maps for MachineType.
var (
	MachineType_name = map[int32]string{
		0: "BareMetal",
		1: "CloudHost",
	}
	MachineType_value = map[string]int32{
		"BareMetal": 0,
		"CloudHost": 1,
	}
)

func (x MachineType) Enum() *MachineType {
	p := new(MachineType)
	*p = x
	return p
}

func (x MachineType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MachineType) Descriptor() protoreflect.EnumDescriptor {
	return file_aistudio_resourcegroup_v1_resourcegroup_proto_enumTypes[1].Descriptor()
}

func (MachineType) Type() protoreflect.EnumType {
	return &file_aistudio_resourcegroup_v1_resourcegroup_proto_enumTypes[1]
}

func (x MachineType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MachineType.Descriptor instead.
func (MachineType) EnumDescriptor() ([]byte, []int) {
	return file_aistudio_resourcegroup_v1_resourcegroup_proto_rawDescGZIP(), []int{1}
}

type GpuAllocationMode int32

const (
	GpuAllocationMode_None      GpuAllocationMode = 0 // CPU节点
	GpuAllocationMode_Exclusive GpuAllocationMode = 1 // GPU节点独占
	GpuAllocationMode_Shared    GpuAllocationMode = 2 // GPU节点共享显存
)

// Enum value maps for GpuAllocationMode.
var (
	GpuAllocationMode_name = map[int32]string{
		0: "None",
		1: "Exclusive",
		2: "Shared",
	}
	GpuAllocationMode_value = map[string]int32{
		"None":      0,
		"Exclusive": 1,
		"Shared":    2,
	}
)

func (x GpuAllocationMode) Enum() *GpuAllocationMode {
	p := new(GpuAllocationMode)
	*p = x
	return p
}

func (x GpuAllocationMode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GpuAllocationMode) Descriptor() protoreflect.EnumDescriptor {
	return file_aistudio_resourcegroup_v1_resourcegroup_proto_enumTypes[2].Descriptor()
}

func (GpuAllocationMode) Type() protoreflect.EnumType {
	return &file_aistudio_resourcegroup_v1_resourcegroup_proto_enumTypes[2]
}

func (x GpuAllocationMode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GpuAllocationMode.Descriptor instead.
func (GpuAllocationMode) EnumDescriptor() ([]byte, []int) {
	return file_aistudio_resourcegroup_v1_resourcegroup_proto_rawDescGZIP(), []int{2}
}

type NodeWorkload_AppType int32

const (
	NodeWorkload_WebService NodeWorkload_AppType = 0
	NodeWorkload_Inference  NodeWorkload_AppType = 1
	NodeWorkload_Unknown    NodeWorkload_AppType = 2
)

// Enum value maps for NodeWorkload_AppType.
var (
	NodeWorkload_AppType_name = map[int32]string{
		0: "WebService",
		1: "Inference",
		2: "Unknown",
	}
	NodeWorkload_AppType_value = map[string]int32{
		"WebService": 0,
		"Inference":  1,
		"Unknown":    2,
	}
)

func (x NodeWorkload_AppType) Enum() *NodeWorkload_AppType {
	p := new(NodeWorkload_AppType)
	*p = x
	return p
}

func (x NodeWorkload_AppType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (NodeWorkload_AppType) Descriptor() protoreflect.EnumDescriptor {
	return file_aistudio_resourcegroup_v1_resourcegroup_proto_enumTypes[3].Descriptor()
}

func (NodeWorkload_AppType) Type() protoreflect.EnumType {
	return &file_aistudio_resourcegroup_v1_resourcegroup_proto_enumTypes[3]
}

func (x NodeWorkload_AppType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use NodeWorkload_AppType.Descriptor instead.
func (NodeWorkload_AppType) EnumDescriptor() ([]byte, []int) {
	return file_aistudio_resourcegroup_v1_resourcegroup_proto_rawDescGZIP(), []int{30, 0}
}

type ListClustersRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName string `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
}

func (x *ListClustersRequest) Reset() {
	*x = ListClustersRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListClustersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListClustersRequest) ProtoMessage() {}

func (x *ListClustersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListClustersRequest.ProtoReflect.Descriptor instead.
func (*ListClustersRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_resourcegroup_v1_resourcegroup_proto_rawDescGZIP(), []int{0}
}

func (x *ListClustersRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

type ApplyResourceResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName string `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	OrderId       string `protobuf:"bytes,2,opt,name=orderId,proto3" json:"orderId,omitempty"` //工单号
	Region        string `protobuf:"bytes,3,opt,name=region,proto3" json:"region,omitempty"`   // 区域
}

func (x *ApplyResourceResult) Reset() {
	*x = ApplyResourceResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ApplyResourceResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApplyResourceResult) ProtoMessage() {}

func (x *ApplyResourceResult) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApplyResourceResult.ProtoReflect.Descriptor instead.
func (*ApplyResourceResult) Descriptor() ([]byte, []int) {
	return file_aistudio_resourcegroup_v1_resourcegroup_proto_rawDescGZIP(), []int{1}
}

func (x *ApplyResourceResult) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *ApplyResourceResult) GetOrderId() string {
	if x != nil {
		return x.OrderId
	}
	return ""
}

func (x *ApplyResourceResult) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

// 资源申请服务
type ApplyResourceRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName string           `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Region        string           `protobuf:"bytes,2,opt,name=region,proto3" json:"region,omitempty"`           // 区域
	Description   string           `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"` // 描述
	ResourcesList []*ApplyResource `protobuf:"bytes,4,rep,name=resourcesList,proto3" json:"resourcesList,omitempty"`
}

func (x *ApplyResourceRequest) Reset() {
	*x = ApplyResourceRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ApplyResourceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApplyResourceRequest) ProtoMessage() {}

func (x *ApplyResourceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApplyResourceRequest.ProtoReflect.Descriptor instead.
func (*ApplyResourceRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_resourcegroup_v1_resourcegroup_proto_rawDescGZIP(), []int{2}
}

func (x *ApplyResourceRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *ApplyResourceRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *ApplyResourceRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *ApplyResourceRequest) GetResourcesList() []*ApplyResource {
	if x != nil {
		return x.ResourcesList
	}
	return nil
}

// 可申请多个不同规格
type ApplyResource struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NodeSpecificationId string `protobuf:"bytes,1,opt,name=nodeSpecificationId,proto3" json:"nodeSpecificationId,omitempty"`
	ApplyNum            int32  `protobuf:"varint,2,opt,name=applyNum,proto3" json:"applyNum,omitempty"`
}

func (x *ApplyResource) Reset() {
	*x = ApplyResource{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ApplyResource) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApplyResource) ProtoMessage() {}

func (x *ApplyResource) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApplyResource.ProtoReflect.Descriptor instead.
func (*ApplyResource) Descriptor() ([]byte, []int) {
	return file_aistudio_resourcegroup_v1_resourcegroup_proto_rawDescGZIP(), []int{3}
}

func (x *ApplyResource) GetNodeSpecificationId() string {
	if x != nil {
		return x.NodeSpecificationId
	}
	return ""
}

func (x *ApplyResource) GetApplyNum() int32 {
	if x != nil {
		return x.ApplyNum
	}
	return 0
}

type RecyclingNodeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ips           []string `protobuf:"bytes,1,rep,name=ips,proto3" json:"ips,omitempty"`
	WorkspaceName string   `protobuf:"bytes,2,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
}

func (x *RecyclingNodeRequest) Reset() {
	*x = RecyclingNodeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RecyclingNodeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecyclingNodeRequest) ProtoMessage() {}

func (x *RecyclingNodeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecyclingNodeRequest.ProtoReflect.Descriptor instead.
func (*RecyclingNodeRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_resourcegroup_v1_resourcegroup_proto_rawDescGZIP(), []int{4}
}

func (x *RecyclingNodeRequest) GetIps() []string {
	if x != nil {
		return x.Ips
	}
	return nil
}

func (x *RecyclingNodeRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

// 基础信息 展示
type NodeDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ip                    string      `protobuf:"bytes,1,opt,name=ip,proto3" json:"ip,omitempty"`
	NodeName              string      `protobuf:"bytes,29,opt,name=nodeName,proto3" json:"nodeName,omitempty"`
	Region                string      `protobuf:"bytes,2,opt,name=region,proto3" json:"region,omitempty"`
	Zone                  string      `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	Room                  string      `protobuf:"bytes,4,opt,name=room,proto3" json:"room,omitempty"`
	Rack                  string      `protobuf:"bytes,5,opt,name=rack,proto3" json:"rack,omitempty"`         //机柜
	IdcAlias              string      `protobuf:"bytes,6,opt,name=idcAlias,proto3" json:"idcAlias,omitempty"` //机房简称
	CpuNum                string      `protobuf:"bytes,7,opt,name=cpuNum,proto3" json:"cpuNum,omitempty"`
	Memory                string      `protobuf:"bytes,8,opt,name=memory,proto3" json:"memory,omitempty"`                                                             // GiB
	GpuNum                string      `protobuf:"bytes,9,opt,name=gpuNum,proto3" json:"gpuNum,omitempty"`                                                             // gpu数量
	GpuMemory             string      `protobuf:"bytes,10,opt,name=gpuMemory,proto3" json:"gpuMemory,omitempty"`                                                      // GiB
	DiskInfo              string      `protobuf:"bytes,11,opt,name=diskInfo,proto3" json:"diskInfo,omitempty"`                                                        // 磁盘信息. 例如: nvme,6400,1,0,0, 代表nvme磁盘,容量6400G,数量1,iops性能, 带宽性能
	GpuModel              string      `protobuf:"bytes,12,opt,name=gpuModel,proto3" json:"gpuModel,omitempty"`                                                        // gpu型号
	GpuProductName        string      `protobuf:"bytes,13,opt,name=gpuProductName,proto3" json:"gpuProductName,omitempty"`                                            // gpu产品名称
	CudaDriverVersion     string      `protobuf:"bytes,14,opt,name=cudaDriverVersion,proto3" json:"cudaDriverVersion,omitempty"`                                      // cuda Driver驱动版本
	CudaRuntimeVersion    string      `protobuf:"bytes,15,opt,name=cudaRuntimeVersion,proto3" json:"cudaRuntimeVersion,omitempty"`                                    // cuda Runtime版本
	MachineType           MachineType `protobuf:"varint,16,opt,name=machineType,proto3,enum=apis.aistudio.resourcegroup.v1.MachineType" json:"machineType,omitempty"` // 机器类型
	GpuMachine            string      `protobuf:"bytes,17,opt,name=gpuMachine,proto3" json:"gpuMachine,omitempty"`                                                    // gpu机器型号
	Cluster               string      `protobuf:"bytes,18,opt,name=cluster,proto3" json:"cluster,omitempty"`                                                          // 集群名称
	Status                string      `protobuf:"bytes,19,opt,name=status,proto3" json:"status,omitempty"`                                                            // 节点状态
	DisplaySpec           string      `protobuf:"bytes,20,opt,name=displaySpec,proto3" json:"displaySpec,omitempty"`                                                  // 显示的资源规格
	WorkspaceName         string      `protobuf:"bytes,21,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`                                              // 工作空间名称
	QueueName             string      `protobuf:"bytes,22,opt,name=queueName,proto3" json:"queueName,omitempty"`                                                      // 队列名称
	MachineModel          string      `protobuf:"bytes,23,opt,name=machineModel,proto3" json:"machineModel,omitempty"`                                                // 机器型号,IDC才有
	NodeSpecificationName string      `protobuf:"bytes,24,opt,name=nodeSpecificationName,proto3" json:"nodeSpecificationName,omitempty"`                              // 节点规格
	// 机器的metrics
	CpuMetric         *common.ResourceMetric `protobuf:"bytes,25,opt,name=cpuMetric,proto3" json:"cpuMetric,omitempty"`
	MemoryMetric      *common.ResourceMetric `protobuf:"bytes,26,opt,name=memoryMetric,proto3" json:"memoryMetric,omitempty"`
	GpuMetric         *common.ResourceMetric `protobuf:"bytes,27,opt,name=gpuMetric,proto3" json:"gpuMetric,omitempty"`
	GpuMemMetric      *common.ResourceMetric `protobuf:"bytes,28,opt,name=gpuMemMetric,proto3" json:"gpuMemMetric,omitempty"`
	GpuAllocationMode GpuAllocationMode      `protobuf:"varint,31,opt,name=gpuAllocationMode,proto3,enum=apis.aistudio.resourcegroup.v1.GpuAllocationMode" json:"gpuAllocationMode,omitempty"`
}

func (x *NodeDetail) Reset() {
	*x = NodeDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NodeDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NodeDetail) ProtoMessage() {}

func (x *NodeDetail) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NodeDetail.ProtoReflect.Descriptor instead.
func (*NodeDetail) Descriptor() ([]byte, []int) {
	return file_aistudio_resourcegroup_v1_resourcegroup_proto_rawDescGZIP(), []int{5}
}

func (x *NodeDetail) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *NodeDetail) GetNodeName() string {
	if x != nil {
		return x.NodeName
	}
	return ""
}

func (x *NodeDetail) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *NodeDetail) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *NodeDetail) GetRoom() string {
	if x != nil {
		return x.Room
	}
	return ""
}

func (x *NodeDetail) GetRack() string {
	if x != nil {
		return x.Rack
	}
	return ""
}

func (x *NodeDetail) GetIdcAlias() string {
	if x != nil {
		return x.IdcAlias
	}
	return ""
}

func (x *NodeDetail) GetCpuNum() string {
	if x != nil {
		return x.CpuNum
	}
	return ""
}

func (x *NodeDetail) GetMemory() string {
	if x != nil {
		return x.Memory
	}
	return ""
}

func (x *NodeDetail) GetGpuNum() string {
	if x != nil {
		return x.GpuNum
	}
	return ""
}

func (x *NodeDetail) GetGpuMemory() string {
	if x != nil {
		return x.GpuMemory
	}
	return ""
}

func (x *NodeDetail) GetDiskInfo() string {
	if x != nil {
		return x.DiskInfo
	}
	return ""
}

func (x *NodeDetail) GetGpuModel() string {
	if x != nil {
		return x.GpuModel
	}
	return ""
}

func (x *NodeDetail) GetGpuProductName() string {
	if x != nil {
		return x.GpuProductName
	}
	return ""
}

func (x *NodeDetail) GetCudaDriverVersion() string {
	if x != nil {
		return x.CudaDriverVersion
	}
	return ""
}

func (x *NodeDetail) GetCudaRuntimeVersion() string {
	if x != nil {
		return x.CudaRuntimeVersion
	}
	return ""
}

func (x *NodeDetail) GetMachineType() MachineType {
	if x != nil {
		return x.MachineType
	}
	return MachineType_BareMetal
}

func (x *NodeDetail) GetGpuMachine() string {
	if x != nil {
		return x.GpuMachine
	}
	return ""
}

func (x *NodeDetail) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

func (x *NodeDetail) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *NodeDetail) GetDisplaySpec() string {
	if x != nil {
		return x.DisplaySpec
	}
	return ""
}

func (x *NodeDetail) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *NodeDetail) GetQueueName() string {
	if x != nil {
		return x.QueueName
	}
	return ""
}

func (x *NodeDetail) GetMachineModel() string {
	if x != nil {
		return x.MachineModel
	}
	return ""
}

func (x *NodeDetail) GetNodeSpecificationName() string {
	if x != nil {
		return x.NodeSpecificationName
	}
	return ""
}

func (x *NodeDetail) GetCpuMetric() *common.ResourceMetric {
	if x != nil {
		return x.CpuMetric
	}
	return nil
}

func (x *NodeDetail) GetMemoryMetric() *common.ResourceMetric {
	if x != nil {
		return x.MemoryMetric
	}
	return nil
}

func (x *NodeDetail) GetGpuMetric() *common.ResourceMetric {
	if x != nil {
		return x.GpuMetric
	}
	return nil
}

func (x *NodeDetail) GetGpuMemMetric() *common.ResourceMetric {
	if x != nil {
		return x.GpuMemMetric
	}
	return nil
}

func (x *NodeDetail) GetGpuAllocationMode() GpuAllocationMode {
	if x != nil {
		return x.GpuAllocationMode
	}
	return GpuAllocationMode_None
}

type GetResourceGroupMetricsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName string `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Region        string `protobuf:"bytes,2,opt,name=region,proto3" json:"region,omitempty"`
	Cluster       string `protobuf:"bytes,3,opt,name=cluster,proto3" json:"cluster,omitempty"`
}

func (x *GetResourceGroupMetricsRequest) Reset() {
	*x = GetResourceGroupMetricsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetResourceGroupMetricsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetResourceGroupMetricsRequest) ProtoMessage() {}

func (x *GetResourceGroupMetricsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetResourceGroupMetricsRequest.ProtoReflect.Descriptor instead.
func (*GetResourceGroupMetricsRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_resourcegroup_v1_resourcegroup_proto_rawDescGZIP(), []int{6}
}

func (x *GetResourceGroupMetricsRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *GetResourceGroupMetricsRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *GetResourceGroupMetricsRequest) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

type ResourceGroupMetric struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Cpu    *common.ResourceMetric `protobuf:"bytes,1,opt,name=cpu,proto3" json:"cpu,omitempty"`
	Memory *common.ResourceMetric `protobuf:"bytes,2,opt,name=memory,proto3" json:"memory,omitempty"`
	Gpu    *common.ResourceMetric `protobuf:"bytes,3,opt,name=gpu,proto3" json:"gpu,omitempty"`
	GpuMem *common.ResourceMetric `protobuf:"bytes,4,opt,name=gpuMem,proto3" json:"gpuMem,omitempty"`
}

func (x *ResourceGroupMetric) Reset() {
	*x = ResourceGroupMetric{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResourceGroupMetric) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResourceGroupMetric) ProtoMessage() {}

func (x *ResourceGroupMetric) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResourceGroupMetric.ProtoReflect.Descriptor instead.
func (*ResourceGroupMetric) Descriptor() ([]byte, []int) {
	return file_aistudio_resourcegroup_v1_resourcegroup_proto_rawDescGZIP(), []int{7}
}

func (x *ResourceGroupMetric) GetCpu() *common.ResourceMetric {
	if x != nil {
		return x.Cpu
	}
	return nil
}

func (x *ResourceGroupMetric) GetMemory() *common.ResourceMetric {
	if x != nil {
		return x.Memory
	}
	return nil
}

func (x *ResourceGroupMetric) GetGpu() *common.ResourceMetric {
	if x != nil {
		return x.Gpu
	}
	return nil
}

func (x *ResourceGroupMetric) GetGpuMem() *common.ResourceMetric {
	if x != nil {
		return x.GpuMem
	}
	return nil
}

// 监控信息 展示
type NodeMetric struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ip       string                 `protobuf:"bytes,1,opt,name=ip,proto3" json:"ip,omitempty"`
	Cpu      *common.ResourceMetric `protobuf:"bytes,2,opt,name=cpu,proto3" json:"cpu,omitempty"`
	Memory   *common.ResourceMetric `protobuf:"bytes,3,opt,name=memory,proto3" json:"memory,omitempty"`
	Gpu      *common.ResourceMetric `protobuf:"bytes,4,opt,name=gpu,proto3" json:"gpu,omitempty"`
	Pods     []*PodMetric           `protobuf:"bytes,5,rep,name=pods,proto3" json:"pods,omitempty"`
	PodCount *PodCount              `protobuf:"bytes,6,opt,name=podCount,proto3" json:"podCount,omitempty"`
	GpuMem   *common.ResourceMetric `protobuf:"bytes,7,opt,name=gpuMem,proto3" json:"gpuMem,omitempty"`
}

func (x *NodeMetric) Reset() {
	*x = NodeMetric{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NodeMetric) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NodeMetric) ProtoMessage() {}

func (x *NodeMetric) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NodeMetric.ProtoReflect.Descriptor instead.
func (*NodeMetric) Descriptor() ([]byte, []int) {
	return file_aistudio_resourcegroup_v1_resourcegroup_proto_rawDescGZIP(), []int{8}
}

func (x *NodeMetric) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *NodeMetric) GetCpu() *common.ResourceMetric {
	if x != nil {
		return x.Cpu
	}
	return nil
}

func (x *NodeMetric) GetMemory() *common.ResourceMetric {
	if x != nil {
		return x.Memory
	}
	return nil
}

func (x *NodeMetric) GetGpu() *common.ResourceMetric {
	if x != nil {
		return x.Gpu
	}
	return nil
}

func (x *NodeMetric) GetPods() []*PodMetric {
	if x != nil {
		return x.Pods
	}
	return nil
}

func (x *NodeMetric) GetPodCount() *PodCount {
	if x != nil {
		return x.PodCount
	}
	return nil
}

func (x *NodeMetric) GetGpuMem() *common.ResourceMetric {
	if x != nil {
		return x.GpuMem
	}
	return nil
}

type GetNodeViewRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName string `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Region        string `protobuf:"bytes,2,opt,name=region,proto3" json:"region,omitempty"`
}

func (x *GetNodeViewRequest) Reset() {
	*x = GetNodeViewRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNodeViewRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNodeViewRequest) ProtoMessage() {}

func (x *GetNodeViewRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNodeViewRequest.ProtoReflect.Descriptor instead.
func (*GetNodeViewRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_resourcegroup_v1_resourcegroup_proto_rawDescGZIP(), []int{9}
}

func (x *GetNodeViewRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *GetNodeViewRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

type Cluster struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ClusterName string `protobuf:"bytes,1,opt,name=clusterName,proto3" json:"clusterName,omitempty"`
	DisplayName string `protobuf:"bytes,2,opt,name=displayName,proto3" json:"displayName,omitempty"`
	Region      string `protobuf:"bytes,3,opt,name=region,proto3" json:"region,omitempty"`
	Zone        string `protobuf:"bytes,4,opt,name=zone,proto3" json:"zone,omitempty"`
	Idc         string `protobuf:"bytes,5,opt,name=idc,proto3" json:"idc,omitempty"`
}

func (x *Cluster) Reset() {
	*x = Cluster{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Cluster) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Cluster) ProtoMessage() {}

func (x *Cluster) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Cluster.ProtoReflect.Descriptor instead.
func (*Cluster) Descriptor() ([]byte, []int) {
	return file_aistudio_resourcegroup_v1_resourcegroup_proto_rawDescGZIP(), []int{10}
}

func (x *Cluster) GetClusterName() string {
	if x != nil {
		return x.ClusterName
	}
	return ""
}

func (x *Cluster) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *Cluster) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *Cluster) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *Cluster) GetIdc() string {
	if x != nil {
		return x.Idc
	}
	return ""
}

type ListClusterResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ClusterList []*Cluster `protobuf:"bytes,1,rep,name=clusterList,proto3" json:"clusterList,omitempty"`
}

func (x *ListClusterResponse) Reset() {
	*x = ListClusterResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListClusterResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListClusterResponse) ProtoMessage() {}

func (x *ListClusterResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListClusterResponse.ProtoReflect.Descriptor instead.
func (*ListClusterResponse) Descriptor() ([]byte, []int) {
	return file_aistudio_resourcegroup_v1_resourcegroup_proto_rawDescGZIP(), []int{11}
}

func (x *ListClusterResponse) GetClusterList() []*Cluster {
	if x != nil {
		return x.ClusterList
	}
	return nil
}

type NodeMetricView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NodeMetricView []*NodeMetric `protobuf:"bytes,1,rep,name=nodeMetricView,proto3" json:"nodeMetricView,omitempty"`
}

func (x *NodeMetricView) Reset() {
	*x = NodeMetricView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NodeMetricView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NodeMetricView) ProtoMessage() {}

func (x *NodeMetricView) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NodeMetricView.ProtoReflect.Descriptor instead.
func (*NodeMetricView) Descriptor() ([]byte, []int) {
	return file_aistudio_resourcegroup_v1_resourcegroup_proto_rawDescGZIP(), []int{12}
}

func (x *NodeMetricView) GetNodeMetricView() []*NodeMetric {
	if x != nil {
		return x.NodeMetricView
	}
	return nil
}

type GetNodeStatisticsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName string `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Region        string `protobuf:"bytes,2,opt,name=region,proto3" json:"region,omitempty"`
	Cluster       string `protobuf:"bytes,3,opt,name=cluster,proto3" json:"cluster,omitempty"`
}

func (x *GetNodeStatisticsRequest) Reset() {
	*x = GetNodeStatisticsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNodeStatisticsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNodeStatisticsRequest) ProtoMessage() {}

func (x *GetNodeStatisticsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNodeStatisticsRequest.ProtoReflect.Descriptor instead.
func (*GetNodeStatisticsRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_resourcegroup_v1_resourcegroup_proto_rawDescGZIP(), []int{13}
}

func (x *GetNodeStatisticsRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *GetNodeStatisticsRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *GetNodeStatisticsRequest) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

type NodeStatistics struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total                int32 `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	GpuNodeNum           int32 `protobuf:"varint,2,opt,name=gpuNodeNum,proto3" json:"gpuNodeNum,omitempty"`
	ReadyNodeNum         int32 `protobuf:"varint,3,opt,name=readyNodeNum,proto3" json:"readyNodeNum,omitempty"`
	NotReadyNodeNum      int32 `protobuf:"varint,4,opt,name=notReadyNodeNum,proto3" json:"notReadyNodeNum,omitempty"`
	SchedulingNodeNum    int32 `protobuf:"varint,5,opt,name=schedulingNodeNum,proto3" json:"schedulingNodeNum,omitempty"`
	NotSchedulingNodeNum int32 `protobuf:"varint,6,opt,name=notSchedulingNodeNum,proto3" json:"notSchedulingNodeNum,omitempty"`
}

func (x *NodeStatistics) Reset() {
	*x = NodeStatistics{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NodeStatistics) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NodeStatistics) ProtoMessage() {}

func (x *NodeStatistics) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NodeStatistics.ProtoReflect.Descriptor instead.
func (*NodeStatistics) Descriptor() ([]byte, []int) {
	return file_aistudio_resourcegroup_v1_resourcegroup_proto_rawDescGZIP(), []int{14}
}

func (x *NodeStatistics) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *NodeStatistics) GetGpuNodeNum() int32 {
	if x != nil {
		return x.GpuNodeNum
	}
	return 0
}

func (x *NodeStatistics) GetReadyNodeNum() int32 {
	if x != nil {
		return x.ReadyNodeNum
	}
	return 0
}

func (x *NodeStatistics) GetNotReadyNodeNum() int32 {
	if x != nil {
		return x.NotReadyNodeNum
	}
	return 0
}

func (x *NodeStatistics) GetSchedulingNodeNum() int32 {
	if x != nil {
		return x.SchedulingNodeNum
	}
	return 0
}

func (x *NodeStatistics) GetNotSchedulingNodeNum() int32 {
	if x != nil {
		return x.NotSchedulingNodeNum
	}
	return 0
}

type OwnReference struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Kind Kind   `protobuf:"varint,1,opt,name=kind,proto3,enum=apis.aistudio.resourcegroup.v1.Kind" json:"kind,omitempty"`
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *OwnReference) Reset() {
	*x = OwnReference{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OwnReference) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OwnReference) ProtoMessage() {}

func (x *OwnReference) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OwnReference.ProtoReflect.Descriptor instead.
func (*OwnReference) Descriptor() ([]byte, []int) {
	return file_aistudio_resourcegroup_v1_resourcegroup_proto_rawDescGZIP(), []int{15}
}

func (x *OwnReference) GetKind() Kind {
	if x != nil {
		return x.Kind
	}
	return Kind_Training
}

func (x *OwnReference) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// 不展示
type PodMetric struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name           string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Namespace      string                 `protobuf:"bytes,2,opt,name=namespace,proto3" json:"namespace,omitempty"`
	OwnerReference *OwnReference          `protobuf:"bytes,3,opt,name=ownerReference,proto3" json:"ownerReference,omitempty"`
	Cpu            *common.ResourceMetric `protobuf:"bytes,4,opt,name=cpu,proto3" json:"cpu,omitempty"`
	Memory         *common.ResourceMetric `protobuf:"bytes,5,opt,name=memory,proto3" json:"memory,omitempty"`
	Gpu            *common.ResourceMetric `protobuf:"bytes,6,opt,name=gpu,proto3" json:"gpu,omitempty"`
	Containers     []*ContainerMetric     `protobuf:"bytes,7,rep,name=containers,proto3" json:"containers,omitempty"`
	GpuMem         *common.ResourceMetric `protobuf:"bytes,8,opt,name=gpuMem,proto3" json:"gpuMem,omitempty"`
}

func (x *PodMetric) Reset() {
	*x = PodMetric{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PodMetric) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PodMetric) ProtoMessage() {}

func (x *PodMetric) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PodMetric.ProtoReflect.Descriptor instead.
func (*PodMetric) Descriptor() ([]byte, []int) {
	return file_aistudio_resourcegroup_v1_resourcegroup_proto_rawDescGZIP(), []int{16}
}

func (x *PodMetric) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PodMetric) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *PodMetric) GetOwnerReference() *OwnReference {
	if x != nil {
		return x.OwnerReference
	}
	return nil
}

func (x *PodMetric) GetCpu() *common.ResourceMetric {
	if x != nil {
		return x.Cpu
	}
	return nil
}

func (x *PodMetric) GetMemory() *common.ResourceMetric {
	if x != nil {
		return x.Memory
	}
	return nil
}

func (x *PodMetric) GetGpu() *common.ResourceMetric {
	if x != nil {
		return x.Gpu
	}
	return nil
}

func (x *PodMetric) GetContainers() []*ContainerMetric {
	if x != nil {
		return x.Containers
	}
	return nil
}

func (x *PodMetric) GetGpuMem() *common.ResourceMetric {
	if x != nil {
		return x.GpuMem
	}
	return nil
}

type ContainerMetric struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name   string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Cpu    *common.ResourceMetric `protobuf:"bytes,2,opt,name=cpu,proto3" json:"cpu,omitempty"`
	Memory *common.ResourceMetric `protobuf:"bytes,3,opt,name=memory,proto3" json:"memory,omitempty"`
	Gpu    *common.ResourceMetric `protobuf:"bytes,4,opt,name=gpu,proto3" json:"gpu,omitempty"`
	GpuMem *common.ResourceMetric `protobuf:"bytes,5,opt,name=gpuMem,proto3" json:"gpuMem,omitempty"`
}

func (x *ContainerMetric) Reset() {
	*x = ContainerMetric{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ContainerMetric) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ContainerMetric) ProtoMessage() {}

func (x *ContainerMetric) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ContainerMetric.ProtoReflect.Descriptor instead.
func (*ContainerMetric) Descriptor() ([]byte, []int) {
	return file_aistudio_resourcegroup_v1_resourcegroup_proto_rawDescGZIP(), []int{17}
}

func (x *ContainerMetric) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ContainerMetric) GetCpu() *common.ResourceMetric {
	if x != nil {
		return x.Cpu
	}
	return nil
}

func (x *ContainerMetric) GetMemory() *common.ResourceMetric {
	if x != nil {
		return x.Memory
	}
	return nil
}

func (x *ContainerMetric) GetGpu() *common.ResourceMetric {
	if x != nil {
		return x.Gpu
	}
	return nil
}

func (x *ContainerMetric) GetGpuMem() *common.ResourceMetric {
	if x != nil {
		return x.GpuMem
	}
	return nil
}

type PodCount struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Current     int32 `protobuf:"varint,1,opt,name=current,proto3" json:"current,omitempty"`
	Allocatable int32 `protobuf:"varint,2,opt,name=allocatable,proto3" json:"allocatable,omitempty"`
}

func (x *PodCount) Reset() {
	*x = PodCount{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PodCount) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PodCount) ProtoMessage() {}

func (x *PodCount) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PodCount.ProtoReflect.Descriptor instead.
func (*PodCount) Descriptor() ([]byte, []int) {
	return file_aistudio_resourcegroup_v1_resourcegroup_proto_rawDescGZIP(), []int{18}
}

func (x *PodCount) GetCurrent() int32 {
	if x != nil {
		return x.Current
	}
	return 0
}

func (x *PodCount) GetAllocatable() int32 {
	if x != nil {
		return x.Allocatable
	}
	return 0
}

type GetNodeDetailRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ip                string `protobuf:"bytes,1,opt,name=ip,proto3" json:"ip,omitempty"`
	StatisticsEnabled bool   `protobuf:"varint,2,opt,name=statisticsEnabled,proto3" json:"statisticsEnabled,omitempty"` //是否返回统计信息
	WorkspaceName     string `protobuf:"bytes,3,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
}

func (x *GetNodeDetailRequest) Reset() {
	*x = GetNodeDetailRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNodeDetailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNodeDetailRequest) ProtoMessage() {}

func (x *GetNodeDetailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNodeDetailRequest.ProtoReflect.Descriptor instead.
func (*GetNodeDetailRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_resourcegroup_v1_resourcegroup_proto_rawDescGZIP(), []int{19}
}

func (x *GetNodeDetailRequest) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *GetNodeDetailRequest) GetStatisticsEnabled() bool {
	if x != nil {
		return x.StatisticsEnabled
	}
	return false
}

func (x *GetNodeDetailRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

type ListNodeDetailResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Nodes []*NodeDetail `protobuf:"bytes,1,rep,name=nodes,proto3" json:"nodes,omitempty"`
	Total int32         `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
}

func (x *ListNodeDetailResult) Reset() {
	*x = ListNodeDetailResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListNodeDetailResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListNodeDetailResult) ProtoMessage() {}

func (x *ListNodeDetailResult) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListNodeDetailResult.ProtoReflect.Descriptor instead.
func (*ListNodeDetailResult) Descriptor() ([]byte, []int) {
	return file_aistudio_resourcegroup_v1_resourcegroup_proto_rawDescGZIP(), []int{20}
}

func (x *ListNodeDetailResult) GetNodes() []*NodeDetail {
	if x != nil {
		return x.Nodes
	}
	return nil
}

func (x *ListNodeDetailResult) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

type ListNodeDetailOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ips               []string `protobuf:"bytes,1,rep,name=ips,proto3" json:"ips,omitempty"`
	Region            string   `protobuf:"bytes,2,opt,name=region,proto3" json:"region,omitempty"`
	Zone              string   `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	CpuNum            int64    `protobuf:"varint,4,opt,name=cpuNum,proto3" json:"cpuNum,omitempty"`
	Memory            int64    `protobuf:"varint,5,opt,name=memory,proto3" json:"memory,omitempty"`
	GpuNum            int64    `protobuf:"varint,6,opt,name=gpuNum,proto3" json:"gpuNum,omitempty"`
	GpuMemory         int64    `protobuf:"varint,7,opt,name=gpuMemory,proto3" json:"gpuMemory,omitempty"`
	GpuModel          string   `protobuf:"bytes,8,opt,name=gpuModel,proto3" json:"gpuModel,omitempty"`
	CudaVersion       string   `protobuf:"bytes,9,opt,name=cudaVersion,proto3" json:"cudaVersion,omitempty"`
	WorkspaceName     string   `protobuf:"bytes,10,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Cluster           string   `protobuf:"bytes,11,opt,name=cluster,proto3" json:"cluster,omitempty"`     // 集群名称
	QueueName         string   `protobuf:"bytes,12,opt,name=queueName,proto3" json:"queueName,omitempty"` //队列名称
	EnableStatus      bool     `protobuf:"varint,13,opt,name=enableStatus,proto3" json:"enableStatus,omitempty"`
	Page              int32    `protobuf:"varint,14,opt,name=page,proto3" json:"page,omitempty"`
	PageSize          int32    `protobuf:"varint,15,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
	NodeStatus        []string `protobuf:"bytes,16,rep,name=nodeStatus,proto3" json:"nodeStatus,omitempty"`
	Ip                string   `protobuf:"bytes,17,opt,name=ip,proto3" json:"ip,omitempty"`
	GpuAllocationMode string   `protobuf:"bytes,18,opt,name=gpuAllocationMode,proto3" json:"gpuAllocationMode,omitempty"`
}

func (x *ListNodeDetailOptions) Reset() {
	*x = ListNodeDetailOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListNodeDetailOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListNodeDetailOptions) ProtoMessage() {}

func (x *ListNodeDetailOptions) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListNodeDetailOptions.ProtoReflect.Descriptor instead.
func (*ListNodeDetailOptions) Descriptor() ([]byte, []int) {
	return file_aistudio_resourcegroup_v1_resourcegroup_proto_rawDescGZIP(), []int{21}
}

func (x *ListNodeDetailOptions) GetIps() []string {
	if x != nil {
		return x.Ips
	}
	return nil
}

func (x *ListNodeDetailOptions) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *ListNodeDetailOptions) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *ListNodeDetailOptions) GetCpuNum() int64 {
	if x != nil {
		return x.CpuNum
	}
	return 0
}

func (x *ListNodeDetailOptions) GetMemory() int64 {
	if x != nil {
		return x.Memory
	}
	return 0
}

func (x *ListNodeDetailOptions) GetGpuNum() int64 {
	if x != nil {
		return x.GpuNum
	}
	return 0
}

func (x *ListNodeDetailOptions) GetGpuMemory() int64 {
	if x != nil {
		return x.GpuMemory
	}
	return 0
}

func (x *ListNodeDetailOptions) GetGpuModel() string {
	if x != nil {
		return x.GpuModel
	}
	return ""
}

func (x *ListNodeDetailOptions) GetCudaVersion() string {
	if x != nil {
		return x.CudaVersion
	}
	return ""
}

func (x *ListNodeDetailOptions) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *ListNodeDetailOptions) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

func (x *ListNodeDetailOptions) GetQueueName() string {
	if x != nil {
		return x.QueueName
	}
	return ""
}

func (x *ListNodeDetailOptions) GetEnableStatus() bool {
	if x != nil {
		return x.EnableStatus
	}
	return false
}

func (x *ListNodeDetailOptions) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListNodeDetailOptions) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListNodeDetailOptions) GetNodeStatus() []string {
	if x != nil {
		return x.NodeStatus
	}
	return nil
}

func (x *ListNodeDetailOptions) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *ListNodeDetailOptions) GetGpuAllocationMode() string {
	if x != nil {
		return x.GpuAllocationMode
	}
	return ""
}

type ListNodeDetailForFrontendOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ip            string `protobuf:"bytes,1,opt,name=ip,proto3" json:"ip,omitempty"`                       // 节点ip, 支持模糊搜索
	WorkspaceName string `protobuf:"bytes,2,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"` // 工作空间名称
	QueueName     string `protobuf:"bytes,3,opt,name=queueName,proto3" json:"queueName,omitempty"`         // 队列名称 队列名称不为空的时候，展示队列名称包括该队列的节点，用于编辑
}

func (x *ListNodeDetailForFrontendOptions) Reset() {
	*x = ListNodeDetailForFrontendOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListNodeDetailForFrontendOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListNodeDetailForFrontendOptions) ProtoMessage() {}

func (x *ListNodeDetailForFrontendOptions) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListNodeDetailForFrontendOptions.ProtoReflect.Descriptor instead.
func (*ListNodeDetailForFrontendOptions) Descriptor() ([]byte, []int) {
	return file_aistudio_resourcegroup_v1_resourcegroup_proto_rawDescGZIP(), []int{22}
}

func (x *ListNodeDetailForFrontendOptions) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *ListNodeDetailForFrontendOptions) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *ListNodeDetailForFrontendOptions) GetQueueName() string {
	if x != nil {
		return x.QueueName
	}
	return ""
}

type GetNodeMetricRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ip            string `protobuf:"bytes,1,opt,name=ip,proto3" json:"ip,omitempty"`
	WorkspaceName string `protobuf:"bytes,2,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
}

func (x *GetNodeMetricRequest) Reset() {
	*x = GetNodeMetricRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNodeMetricRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNodeMetricRequest) ProtoMessage() {}

func (x *GetNodeMetricRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNodeMetricRequest.ProtoReflect.Descriptor instead.
func (*GetNodeMetricRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_resourcegroup_v1_resourcegroup_proto_rawDescGZIP(), []int{23}
}

func (x *GetNodeMetricRequest) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *GetNodeMetricRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

type GetNodeSeriesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ip          string `protobuf:"bytes,1,opt,name=ip,proto3" json:"ip,omitempty"`
	MetricsName string `protobuf:"bytes,2,opt,name=metricsName,proto3" json:"metricsName,omitempty"`
	StartTime   string `protobuf:"bytes,3,opt,name=startTime,proto3" json:"startTime,omitempty"`
	EndTime     string `protobuf:"bytes,4,opt,name=endTime,proto3" json:"endTime,omitempty"`
	Step        string `protobuf:"bytes,6,opt,name=step,proto3" json:"step,omitempty"`
}

func (x *GetNodeSeriesRequest) Reset() {
	*x = GetNodeSeriesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNodeSeriesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNodeSeriesRequest) ProtoMessage() {}

func (x *GetNodeSeriesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNodeSeriesRequest.ProtoReflect.Descriptor instead.
func (*GetNodeSeriesRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_resourcegroup_v1_resourcegroup_proto_rawDescGZIP(), []int{24}
}

func (x *GetNodeSeriesRequest) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *GetNodeSeriesRequest) GetMetricsName() string {
	if x != nil {
		return x.MetricsName
	}
	return ""
}

func (x *GetNodeSeriesRequest) GetStartTime() string {
	if x != nil {
		return x.StartTime
	}
	return ""
}

func (x *GetNodeSeriesRequest) GetEndTime() string {
	if x != nil {
		return x.EndTime
	}
	return ""
}

func (x *GetNodeSeriesRequest) GetStep() string {
	if x != nil {
		return x.Step
	}
	return ""
}

type GetNodeSeriesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ip string `protobuf:"bytes,1,opt,name=ip,proto3" json:"ip,omitempty"`
}

func (x *GetNodeSeriesResponse) Reset() {
	*x = GetNodeSeriesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNodeSeriesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNodeSeriesResponse) ProtoMessage() {}

func (x *GetNodeSeriesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNodeSeriesResponse.ProtoReflect.Descriptor instead.
func (*GetNodeSeriesResponse) Descriptor() ([]byte, []int) {
	return file_aistudio_resourcegroup_v1_resourcegroup_proto_rawDescGZIP(), []int{25}
}

func (x *GetNodeSeriesResponse) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

type CascadeNodeDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Label    string                `protobuf:"bytes,1,opt,name=label,proto3" json:"label,omitempty"` // 显示NodeSpecificationName
	Children []*NodeDetailChildren `protobuf:"bytes,2,rep,name=children,proto3" json:"children,omitempty"`
}

func (x *CascadeNodeDetail) Reset() {
	*x = CascadeNodeDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CascadeNodeDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CascadeNodeDetail) ProtoMessage() {}

func (x *CascadeNodeDetail) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CascadeNodeDetail.ProtoReflect.Descriptor instead.
func (*CascadeNodeDetail) Descriptor() ([]byte, []int) {
	return file_aistudio_resourcegroup_v1_resourcegroup_proto_rawDescGZIP(), []int{26}
}

func (x *CascadeNodeDetail) GetLabel() string {
	if x != nil {
		return x.Label
	}
	return ""
}

func (x *CascadeNodeDetail) GetChildren() []*NodeDetailChildren {
	if x != nil {
		return x.Children
	}
	return nil
}

type NodeDetailChildren struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Label string `protobuf:"bytes,1,opt,name=label,proto3" json:"label,omitempty"`
	Value string `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *NodeDetailChildren) Reset() {
	*x = NodeDetailChildren{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NodeDetailChildren) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NodeDetailChildren) ProtoMessage() {}

func (x *NodeDetailChildren) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NodeDetailChildren.ProtoReflect.Descriptor instead.
func (*NodeDetailChildren) Descriptor() ([]byte, []int) {
	return file_aistudio_resourcegroup_v1_resourcegroup_proto_rawDescGZIP(), []int{27}
}

func (x *NodeDetailChildren) GetLabel() string {
	if x != nil {
		return x.Label
	}
	return ""
}

func (x *NodeDetailChildren) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

type CascadeNodeDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Nodes []*CascadeNodeDetail `protobuf:"bytes,1,rep,name=nodes,proto3" json:"nodes,omitempty"`
}

func (x *CascadeNodeDetails) Reset() {
	*x = CascadeNodeDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CascadeNodeDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CascadeNodeDetails) ProtoMessage() {}

func (x *CascadeNodeDetails) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CascadeNodeDetails.ProtoReflect.Descriptor instead.
func (*CascadeNodeDetails) Descriptor() ([]byte, []int) {
	return file_aistudio_resourcegroup_v1_resourcegroup_proto_rawDescGZIP(), []int{28}
}

func (x *CascadeNodeDetails) GetNodes() []*CascadeNodeDetail {
	if x != nil {
		return x.Nodes
	}
	return nil
}

type DisableNodeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ip            string `protobuf:"bytes,1,opt,name=ip,proto3" json:"ip,omitempty"`
	WorkspaceName string `protobuf:"bytes,2,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Disable       bool   `protobuf:"varint,3,opt,name=disable,proto3" json:"disable,omitempty"`
}

func (x *DisableNodeRequest) Reset() {
	*x = DisableNodeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DisableNodeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DisableNodeRequest) ProtoMessage() {}

func (x *DisableNodeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DisableNodeRequest.ProtoReflect.Descriptor instead.
func (*DisableNodeRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_resourcegroup_v1_resourcegroup_proto_rawDescGZIP(), []int{29}
}

func (x *DisableNodeRequest) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *DisableNodeRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *DisableNodeRequest) GetDisable() bool {
	if x != nil {
		return x.Disable
	}
	return false
}

type NodeWorkload struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkloadName        string                `protobuf:"bytes,1,opt,name=workloadName,proto3" json:"workloadName,omitempty"`
	WorkloadType        string                `protobuf:"bytes,2,opt,name=workloadType,proto3" json:"workloadType,omitempty"`
	WorkloadDisplayName string                `protobuf:"bytes,3,opt,name=workloadDisplayName,proto3" json:"workloadDisplayName,omitempty"`
	Creator             string                `protobuf:"bytes,4,opt,name=creator,proto3" json:"creator,omitempty"`
	QueueName           string                `protobuf:"bytes,5,opt,name=queueName,proto3" json:"queueName,omitempty"`
	Region              string                `protobuf:"bytes,6,opt,name=region,proto3" json:"region,omitempty"`
	WorkloadStatus      string                `protobuf:"bytes,7,opt,name=workloadStatus,proto3" json:"workloadStatus,omitempty"`
	AppName             string                `protobuf:"bytes,8,opt,name=appName,proto3" json:"appName,omitempty"`
	AppType             NodeWorkload_AppType  `protobuf:"varint,9,opt,name=appType,proto3,enum=apis.aistudio.resourcegroup.v1.NodeWorkload_AppType" json:"appType,omitempty"` // 0 WebService 1 Inference
	DeploymentGroupName string                `protobuf:"bytes,10,opt,name=deploymentGroupName,proto3" json:"deploymentGroupName,omitempty"`
	DeploymentGroupType string                `protobuf:"bytes,11,opt,name=deploymentGroupType,proto3" json:"deploymentGroupType,omitempty"`
	Specification       *common.Specification `protobuf:"bytes,12,opt,name=specification,proto3" json:"specification,omitempty"`
	InstanceName        string                `protobuf:"bytes,13,opt,name=instanceName,proto3" json:"instanceName,omitempty"`
}

func (x *NodeWorkload) Reset() {
	*x = NodeWorkload{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NodeWorkload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NodeWorkload) ProtoMessage() {}

func (x *NodeWorkload) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NodeWorkload.ProtoReflect.Descriptor instead.
func (*NodeWorkload) Descriptor() ([]byte, []int) {
	return file_aistudio_resourcegroup_v1_resourcegroup_proto_rawDescGZIP(), []int{30}
}

func (x *NodeWorkload) GetWorkloadName() string {
	if x != nil {
		return x.WorkloadName
	}
	return ""
}

func (x *NodeWorkload) GetWorkloadType() string {
	if x != nil {
		return x.WorkloadType
	}
	return ""
}

func (x *NodeWorkload) GetWorkloadDisplayName() string {
	if x != nil {
		return x.WorkloadDisplayName
	}
	return ""
}

func (x *NodeWorkload) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *NodeWorkload) GetQueueName() string {
	if x != nil {
		return x.QueueName
	}
	return ""
}

func (x *NodeWorkload) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *NodeWorkload) GetWorkloadStatus() string {
	if x != nil {
		return x.WorkloadStatus
	}
	return ""
}

func (x *NodeWorkload) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

func (x *NodeWorkload) GetAppType() NodeWorkload_AppType {
	if x != nil {
		return x.AppType
	}
	return NodeWorkload_WebService
}

func (x *NodeWorkload) GetDeploymentGroupName() string {
	if x != nil {
		return x.DeploymentGroupName
	}
	return ""
}

func (x *NodeWorkload) GetDeploymentGroupType() string {
	if x != nil {
		return x.DeploymentGroupType
	}
	return ""
}

func (x *NodeWorkload) GetSpecification() *common.Specification {
	if x != nil {
		return x.Specification
	}
	return nil
}

func (x *NodeWorkload) GetInstanceName() string {
	if x != nil {
		return x.InstanceName
	}
	return ""
}

type GetNodeWorkloadsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NodeWorkloads []*NodeWorkload `protobuf:"bytes,1,rep,name=nodeWorkloads,proto3" json:"nodeWorkloads,omitempty"`
	Total         int32           `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
}

func (x *GetNodeWorkloadsResponse) Reset() {
	*x = GetNodeWorkloadsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNodeWorkloadsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNodeWorkloadsResponse) ProtoMessage() {}

func (x *GetNodeWorkloadsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNodeWorkloadsResponse.ProtoReflect.Descriptor instead.
func (*GetNodeWorkloadsResponse) Descriptor() ([]byte, []int) {
	return file_aistudio_resourcegroup_v1_resourcegroup_proto_rawDescGZIP(), []int{31}
}

func (x *GetNodeWorkloadsResponse) GetNodeWorkloads() []*NodeWorkload {
	if x != nil {
		return x.NodeWorkloads
	}
	return nil
}

func (x *GetNodeWorkloadsResponse) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

type GetNodeWorkloadsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ip            string `protobuf:"bytes,1,opt,name=ip,proto3" json:"ip,omitempty"`
	WorkspaceName string `protobuf:"bytes,2,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
}

func (x *GetNodeWorkloadsRequest) Reset() {
	*x = GetNodeWorkloadsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNodeWorkloadsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNodeWorkloadsRequest) ProtoMessage() {}

func (x *GetNodeWorkloadsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNodeWorkloadsRequest.ProtoReflect.Descriptor instead.
func (*GetNodeWorkloadsRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_resourcegroup_v1_resourcegroup_proto_rawDescGZIP(), []int{32}
}

func (x *GetNodeWorkloadsRequest) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *GetNodeWorkloadsRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

type EvictNodeWorkloadsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ip            string `protobuf:"bytes,1,opt,name=ip,proto3" json:"ip,omitempty"`
	WorkspaceName string `protobuf:"bytes,2,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
}

func (x *EvictNodeWorkloadsRequest) Reset() {
	*x = EvictNodeWorkloadsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EvictNodeWorkloadsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EvictNodeWorkloadsRequest) ProtoMessage() {}

func (x *EvictNodeWorkloadsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EvictNodeWorkloadsRequest.ProtoReflect.Descriptor instead.
func (*EvictNodeWorkloadsRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_resourcegroup_v1_resourcegroup_proto_rawDescGZIP(), []int{33}
}

func (x *EvictNodeWorkloadsRequest) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *EvictNodeWorkloadsRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

type NodeWorkloadsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NodeWorkload []*NodeWorkload `protobuf:"bytes,1,rep,name=nodeWorkload,proto3" json:"nodeWorkload,omitempty"`
}

func (x *NodeWorkloadsResponse) Reset() {
	*x = NodeWorkloadsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NodeWorkloadsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NodeWorkloadsResponse) ProtoMessage() {}

func (x *NodeWorkloadsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NodeWorkloadsResponse.ProtoReflect.Descriptor instead.
func (*NodeWorkloadsResponse) Descriptor() ([]byte, []int) {
	return file_aistudio_resourcegroup_v1_resourcegroup_proto_rawDescGZIP(), []int{34}
}

func (x *NodeWorkloadsResponse) GetNodeWorkload() []*NodeWorkload {
	if x != nil {
		return x.NodeWorkload
	}
	return nil
}

type ChangeNodeGpuAllocationModeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ip                string            `protobuf:"bytes,1,opt,name=ip,proto3" json:"ip,omitempty"`
	WorkspaceName     string            `protobuf:"bytes,2,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	GpuAllocationMode GpuAllocationMode `protobuf:"varint,3,opt,name=gpuAllocationMode,proto3,enum=apis.aistudio.resourcegroup.v1.GpuAllocationMode" json:"gpuAllocationMode,omitempty"` // GPU分配模式 共享模式 独占模式
}

func (x *ChangeNodeGpuAllocationModeRequest) Reset() {
	*x = ChangeNodeGpuAllocationModeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChangeNodeGpuAllocationModeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChangeNodeGpuAllocationModeRequest) ProtoMessage() {}

func (x *ChangeNodeGpuAllocationModeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChangeNodeGpuAllocationModeRequest.ProtoReflect.Descriptor instead.
func (*ChangeNodeGpuAllocationModeRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_resourcegroup_v1_resourcegroup_proto_rawDescGZIP(), []int{35}
}

func (x *ChangeNodeGpuAllocationModeRequest) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *ChangeNodeGpuAllocationModeRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *ChangeNodeGpuAllocationModeRequest) GetGpuAllocationMode() GpuAllocationMode {
	if x != nil {
		return x.GpuAllocationMode
	}
	return GpuAllocationMode_None
}

var File_aistudio_resourcegroup_v1_resourcegroup_proto protoreflect.FileDescriptor

var file_aistudio_resourcegroup_v1_resourcegroup_proto_rawDesc = []byte{
	0x0a, 0x2d, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x2f, 0x76, 0x31, 0x2f, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x1e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x2e, 0x76, 0x31, 0x1a,
	0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f,
	0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65,
	0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x13, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0x3b, 0x0a, 0x13, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77,
	0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x6d, 0x0a, 0x13,
	0x41, 0x70, 0x70, 0x6c, 0x79, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x22, 0xcb, 0x01, 0x0a, 0x14,
	0x41, 0x70, 0x70, 0x6c, 0x79, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72,
	0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65,
	0x67, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69,
	0x6f, 0x6e, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x53, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x73, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x61, 0x70,
	0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70,
	0x6c, 0x79, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x0d, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x5d, 0x0a, 0x0d, 0x41, 0x70, 0x70,
	0x6c, 0x79, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x30, 0x0a, 0x13, 0x6e, 0x6f,
	0x64, 0x65, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x6e, 0x6f, 0x64, 0x65, 0x53, 0x70, 0x65,
	0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08,
	0x61, 0x70, 0x70, 0x6c, 0x79, 0x4e, 0x75, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08,
	0x61, 0x70, 0x70, 0x6c, 0x79, 0x4e, 0x75, 0x6d, 0x22, 0x4e, 0x0a, 0x14, 0x52, 0x65, 0x63, 0x79,
	0x63, 0x6c, 0x69, 0x6e, 0x67, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x10, 0x0a, 0x03, 0x69, 0x70, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x03, 0x69,
	0x70, 0x73, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x86, 0x09, 0x0a, 0x0a, 0x4e, 0x6f, 0x64,
	0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x12, 0x1a, 0x0a, 0x08, 0x6e, 0x6f, 0x64, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x6f, 0x64, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x7a,
	0x6f, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x72, 0x6f, 0x6f, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x72,
	0x6f, 0x6f, 0x6d, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x61, 0x63, 0x6b, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x72, 0x61, 0x63, 0x6b, 0x12, 0x1a, 0x0a, 0x08, 0x69, 0x64, 0x63, 0x41, 0x6c,
	0x69, 0x61, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x69, 0x64, 0x63, 0x41, 0x6c,
	0x69, 0x61, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x63, 0x70, 0x75, 0x4e, 0x75, 0x6d, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x70, 0x75, 0x4e, 0x75, 0x6d, 0x12, 0x16, 0x0a, 0x06, 0x6d,
	0x65, 0x6d, 0x6f, 0x72, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x65, 0x6d,
	0x6f, 0x72, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x67, 0x70, 0x75, 0x4e, 0x75, 0x6d, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x67, 0x70, 0x75, 0x4e, 0x75, 0x6d, 0x12, 0x1c, 0x0a, 0x09, 0x67,
	0x70, 0x75, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x67, 0x70, 0x75, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x69, 0x73,
	0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x69, 0x73,
	0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1a, 0x0a, 0x08, 0x67, 0x70, 0x75, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x67, 0x70, 0x75, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x12, 0x26, 0x0a, 0x0e, 0x67, 0x70, 0x75, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x4e,
	0x61, 0x6d, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x67, 0x70, 0x75, 0x50, 0x72,
	0x6f, 0x64, 0x75, 0x63, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2c, 0x0a, 0x11, 0x63, 0x75, 0x64,
	0x61, 0x44, 0x72, 0x69, 0x76, 0x65, 0x72, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0e,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x63, 0x75, 0x64, 0x61, 0x44, 0x72, 0x69, 0x76, 0x65, 0x72,
	0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x2e, 0x0a, 0x12, 0x63, 0x75, 0x64, 0x61, 0x52,
	0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0f, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x12, 0x63, 0x75, 0x64, 0x61, 0x52, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65,
	0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x4d, 0x0a, 0x0b, 0x6d, 0x61, 0x63, 0x68, 0x69,
	0x6e, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x61,
	0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x61,
	0x63, 0x68, 0x69, 0x6e, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x6d, 0x61, 0x63, 0x68, 0x69,
	0x6e, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x67, 0x70, 0x75, 0x4d, 0x61, 0x63,
	0x68, 0x69, 0x6e, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x67, 0x70, 0x75, 0x4d,
	0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65,
	0x72, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72,
	0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x69, 0x73, 0x70,
	0x6c, 0x61, 0x79, 0x53, 0x70, 0x65, 0x63, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64,
	0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x53, 0x70, 0x65, 0x63, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f,
	0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x15, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x1c, 0x0a, 0x09, 0x71, 0x75, 0x65, 0x75, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x16, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x71, 0x75, 0x65, 0x75, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x22,
	0x0a, 0x0c, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x17,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x12, 0x34, 0x0a, 0x15, 0x6e, 0x6f, 0x64, 0x65, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x18, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x15, 0x6e, 0x6f, 0x64, 0x65, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x39, 0x0a, 0x09, 0x63, 0x70, 0x75, 0x4d,
	0x65, 0x74, 0x72, 0x69, 0x63, 0x18, 0x19, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61, 0x70,
	0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x52, 0x09, 0x63, 0x70, 0x75, 0x4d, 0x65, 0x74,
	0x72, 0x69, 0x63, 0x12, 0x3f, 0x0a, 0x0c, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x4d, 0x65, 0x74,
	0x72, 0x69, 0x63, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x73,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x52, 0x0c, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x4d, 0x65,
	0x74, 0x72, 0x69, 0x63, 0x12, 0x39, 0x0a, 0x09, 0x67, 0x70, 0x75, 0x4d, 0x65, 0x74, 0x72, 0x69,
	0x63, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65,
	0x74, 0x72, 0x69, 0x63, 0x52, 0x09, 0x67, 0x70, 0x75, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x12,
	0x3f, 0x0a, 0x0c, 0x67, 0x70, 0x75, 0x4d, 0x65, 0x6d, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x18,
	0x1c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x72,
	0x69, 0x63, 0x52, 0x0c, 0x67, 0x70, 0x75, 0x4d, 0x65, 0x6d, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63,
	0x12, 0x5f, 0x0a, 0x11, 0x67, 0x70, 0x75, 0x41, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x31, 0x2e, 0x61, 0x70,
	0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x70, 0x75,
	0x41, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x11,
	0x67, 0x70, 0x75, 0x41, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64,
	0x65, 0x22, 0x78, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67,
	0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f,
	0x6e, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x22, 0xdd, 0x01, 0x0a, 0x13,
	0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4d, 0x65, 0x74,
	0x72, 0x69, 0x63, 0x12, 0x2d, 0x0a, 0x03, 0x63, 0x70, 0x75, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x52, 0x03, 0x63,
	0x70, 0x75, 0x12, 0x33, 0x0a, 0x06, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x52,
	0x06, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x12, 0x2d, 0x0a, 0x03, 0x67, 0x70, 0x75, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x72, 0x69,
	0x63, 0x52, 0x03, 0x67, 0x70, 0x75, 0x12, 0x33, 0x0a, 0x06, 0x67, 0x70, 0x75, 0x4d, 0x65, 0x6d,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74,
	0x72, 0x69, 0x63, 0x52, 0x06, 0x67, 0x70, 0x75, 0x4d, 0x65, 0x6d, 0x22, 0xe9, 0x02, 0x0a, 0x0a,
	0x4e, 0x6f, 0x64, 0x65, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x12, 0x2d, 0x0a, 0x03, 0x63, 0x70,
	0x75, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65,
	0x74, 0x72, 0x69, 0x63, 0x52, 0x03, 0x63, 0x70, 0x75, 0x12, 0x33, 0x0a, 0x06, 0x6d, 0x65, 0x6d,
	0x6f, 0x72, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x73,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x52, 0x06, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x12, 0x2d,
	0x0a, 0x03, 0x67, 0x70, 0x75, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61, 0x70,
	0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x52, 0x03, 0x67, 0x70, 0x75, 0x12, 0x3d, 0x0a,
	0x04, 0x70, 0x6f, 0x64, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x61, 0x70,
	0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x6f, 0x64,
	0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x52, 0x04, 0x70, 0x6f, 0x64, 0x73, 0x12, 0x44, 0x0a, 0x08,
	0x70, 0x6f, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28,
	0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x2e, 0x76, 0x31, 0x2e,
	0x50, 0x6f, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x08, 0x70, 0x6f, 0x64, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x33, 0x0a, 0x06, 0x67, 0x70, 0x75, 0x4d, 0x65, 0x6d, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x52,
	0x06, 0x67, 0x70, 0x75, 0x4d, 0x65, 0x6d, 0x22, 0x52, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x4e, 0x6f,
	0x64, 0x65, 0x56, 0x69, 0x65, 0x77, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a,
	0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x22, 0x8b, 0x01, 0x0a, 0x07,
	0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x6c, 0x75, 0x73, 0x74,
	0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6c,
	0x75, 0x73, 0x74, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x69, 0x73,
	0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72,
	0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67,
	0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x64, 0x63, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x69, 0x64, 0x63, 0x22, 0x60, 0x0a, 0x13, 0x4c, 0x69, 0x73,
	0x74, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x49, 0x0a, 0x0b, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73,
	0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x52, 0x0b,
	0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x64, 0x0a, 0x0e, 0x4e,
	0x6f, 0x64, 0x65, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x56, 0x69, 0x65, 0x77, 0x12, 0x52, 0x0a,
	0x0e, 0x6e, 0x6f, 0x64, 0x65, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x56, 0x69, 0x65, 0x77, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73,
	0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x4d, 0x65, 0x74, 0x72, 0x69,
	0x63, 0x52, 0x0e, 0x6e, 0x6f, 0x64, 0x65, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x56, 0x69, 0x65,
	0x77, 0x22, 0x72, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x53, 0x74, 0x61, 0x74,
	0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a,
	0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x63,
	0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6c,
	0x75, 0x73, 0x74, 0x65, 0x72, 0x22, 0xf6, 0x01, 0x0a, 0x0e, 0x4e, 0x6f, 0x64, 0x65, 0x53, 0x74,
	0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x1e,
	0x0a, 0x0a, 0x67, 0x70, 0x75, 0x4e, 0x6f, 0x64, 0x65, 0x4e, 0x75, 0x6d, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0a, 0x67, 0x70, 0x75, 0x4e, 0x6f, 0x64, 0x65, 0x4e, 0x75, 0x6d, 0x12, 0x22,
	0x0a, 0x0c, 0x72, 0x65, 0x61, 0x64, 0x79, 0x4e, 0x6f, 0x64, 0x65, 0x4e, 0x75, 0x6d, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x72, 0x65, 0x61, 0x64, 0x79, 0x4e, 0x6f, 0x64, 0x65, 0x4e,
	0x75, 0x6d, 0x12, 0x28, 0x0a, 0x0f, 0x6e, 0x6f, 0x74, 0x52, 0x65, 0x61, 0x64, 0x79, 0x4e, 0x6f,
	0x64, 0x65, 0x4e, 0x75, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x6e, 0x6f, 0x74,
	0x52, 0x65, 0x61, 0x64, 0x79, 0x4e, 0x6f, 0x64, 0x65, 0x4e, 0x75, 0x6d, 0x12, 0x2c, 0x0a, 0x11,
	0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x69, 0x6e, 0x67, 0x4e, 0x6f, 0x64, 0x65, 0x4e, 0x75,
	0x6d, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x11, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c,
	0x69, 0x6e, 0x67, 0x4e, 0x6f, 0x64, 0x65, 0x4e, 0x75, 0x6d, 0x12, 0x32, 0x0a, 0x14, 0x6e, 0x6f,
	0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x69, 0x6e, 0x67, 0x4e, 0x6f, 0x64, 0x65, 0x4e,
	0x75, 0x6d, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x14, 0x6e, 0x6f, 0x74, 0x53, 0x63, 0x68,
	0x65, 0x64, 0x75, 0x6c, 0x69, 0x6e, 0x67, 0x4e, 0x6f, 0x64, 0x65, 0x4e, 0x75, 0x6d, 0x22, 0x5c,
	0x0a, 0x0c, 0x4f, 0x77, 0x6e, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x38,
	0x0a, 0x04, 0x6b, 0x69, 0x6e, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x61,
	0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x4b, 0x69,
	0x6e, 0x64, 0x52, 0x04, 0x6b, 0x69, 0x6e, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0xac, 0x03, 0x0a,
	0x09, 0x50, 0x6f, 0x64, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1c,
	0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x54, 0x0a, 0x0e,
	0x6f, 0x77, 0x6e, 0x65, 0x72, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74,
	0x75, 0x64, 0x69, 0x6f, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x77, 0x6e, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e,
	0x63, 0x65, 0x52, 0x0e, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e,
	0x63, 0x65, 0x12, 0x2d, 0x0a, 0x03, 0x63, 0x70, 0x75, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1b, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x52, 0x03, 0x63, 0x70,
	0x75, 0x12, 0x33, 0x0a, 0x06, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x52, 0x06,
	0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x12, 0x2d, 0x0a, 0x03, 0x67, 0x70, 0x75, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63,
	0x52, 0x03, 0x67, 0x70, 0x75, 0x12, 0x4f, 0x0a, 0x0a, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e,
	0x65, 0x72, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x61, 0x70, 0x69, 0x73,
	0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x61,
	0x69, 0x6e, 0x65, 0x72, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x52, 0x0a, 0x63, 0x6f, 0x6e, 0x74,
	0x61, 0x69, 0x6e, 0x65, 0x72, 0x73, 0x12, 0x33, 0x0a, 0x06, 0x67, 0x70, 0x75, 0x4d, 0x65, 0x6d,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74,
	0x72, 0x69, 0x63, 0x52, 0x06, 0x67, 0x70, 0x75, 0x4d, 0x65, 0x6d, 0x22, 0xed, 0x01, 0x0a, 0x0f,
	0x43, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x2d, 0x0a, 0x03, 0x63, 0x70, 0x75, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x52, 0x03, 0x63,
	0x70, 0x75, 0x12, 0x33, 0x0a, 0x06, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x52,
	0x06, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x12, 0x2d, 0x0a, 0x03, 0x67, 0x70, 0x75, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x72, 0x69,
	0x63, 0x52, 0x03, 0x67, 0x70, 0x75, 0x12, 0x33, 0x0a, 0x06, 0x67, 0x70, 0x75, 0x4d, 0x65, 0x6d,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74,
	0x72, 0x69, 0x63, 0x52, 0x06, 0x67, 0x70, 0x75, 0x4d, 0x65, 0x6d, 0x22, 0x46, 0x0a, 0x08, 0x50,
	0x6f, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x75, 0x72, 0x72, 0x65,
	0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e,
	0x74, 0x12, 0x20, 0x0a, 0x0b, 0x61, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x61, 0x62, 0x6c, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x61, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x61,
	0x62, 0x6c, 0x65, 0x22, 0x7a, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x12, 0x2c, 0x0a, 0x11, 0x73,
	0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x11, 0x73, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69,
	0x63, 0x73, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72,
	0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x22,
	0x6e, 0x0a, 0x14, 0x4c, 0x69, 0x73, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x40, 0x0a, 0x05, 0x6e, 0x6f, 0x64, 0x65, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69,
	0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x52, 0x05, 0x6e, 0x6f, 0x64, 0x65, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x22,
	0x89, 0x04, 0x0a, 0x15, 0x4c, 0x69, 0x73, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x70, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x03, 0x69, 0x70, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x72,
	0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67,
	0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x63, 0x70, 0x75, 0x4e, 0x75,
	0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x63, 0x70, 0x75, 0x4e, 0x75, 0x6d, 0x12,
	0x16, 0x0a, 0x06, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x06, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x67, 0x70, 0x75, 0x4e, 0x75,
	0x6d, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x67, 0x70, 0x75, 0x4e, 0x75, 0x6d, 0x12,
	0x1c, 0x0a, 0x09, 0x67, 0x70, 0x75, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x09, 0x67, 0x70, 0x75, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x12, 0x1a, 0x0a,
	0x08, 0x67, 0x70, 0x75, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x67, 0x70, 0x75, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x75, 0x64,
	0x61, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x63, 0x75, 0x64, 0x61, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x24, 0x0a, 0x0d, 0x77,
	0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x12, 0x1c, 0x0a, 0x09, 0x71,
	0x75, 0x65, 0x75, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x71, 0x75, 0x65, 0x75, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x65, 0x6e, 0x61,
	0x62, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x0c, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x12, 0x0a,
	0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67,
	0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x0f, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1e, 0x0a,
	0x0a, 0x6e, 0x6f, 0x64, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x10, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x0a, 0x6e, 0x6f, 0x64, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x70, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x12, 0x2c, 0x0a,
	0x11, 0x67, 0x70, 0x75, 0x41, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f,
	0x64, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x67, 0x70, 0x75, 0x41, 0x6c, 0x6c,
	0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x22, 0x76, 0x0a, 0x20, 0x4c,
	0x69, 0x73, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x46, 0x6f, 0x72,
	0x46, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x12,
	0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x71, 0x75, 0x65, 0x75, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x71, 0x75, 0x65, 0x75, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x22, 0x4c, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x4d, 0x65,
	0x74, 0x72, 0x69, 0x63, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x12, 0x24, 0x0a, 0x0d, 0x77,
	0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x22, 0x94, 0x01, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x53, 0x65, 0x72,
	0x69, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x12, 0x20, 0x0a, 0x0b, 0x6d, 0x65,
	0x74, 0x72, 0x69, 0x63, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09,
	0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x6e,
	0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x6e, 0x64,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x74, 0x65, 0x70, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x73, 0x74, 0x65, 0x70, 0x22, 0x27, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x4e,
	0x6f, 0x64, 0x65, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69,
	0x70, 0x22, 0x79, 0x0a, 0x11, 0x43, 0x61, 0x73, 0x63, 0x61, 0x64, 0x65, 0x4e, 0x6f, 0x64, 0x65,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x12, 0x4e, 0x0a, 0x08,
	0x63, 0x68, 0x69, 0x6c, 0x64, 0x72, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x32,
	0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x2e, 0x76, 0x31, 0x2e,
	0x4e, 0x6f, 0x64, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x43, 0x68, 0x69, 0x6c, 0x64, 0x72,
	0x65, 0x6e, 0x52, 0x08, 0x63, 0x68, 0x69, 0x6c, 0x64, 0x72, 0x65, 0x6e, 0x22, 0x40, 0x0a, 0x12,
	0x4e, 0x6f, 0x64, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x43, 0x68, 0x69, 0x6c, 0x64, 0x72,
	0x65, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x5d,
	0x0a, 0x12, 0x43, 0x61, 0x73, 0x63, 0x61, 0x64, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x12, 0x47, 0x0a, 0x05, 0x6e, 0x6f, 0x64, 0x65, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75,
	0x64, 0x69, 0x6f, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x67, 0x72, 0x6f, 0x75,
	0x70, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x73, 0x63, 0x61, 0x64, 0x65, 0x4e, 0x6f, 0x64, 0x65,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x05, 0x6e, 0x6f, 0x64, 0x65, 0x73, 0x22, 0x64, 0x0a,
	0x12, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x02, 0x69, 0x70, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x64, 0x69, 0x73,
	0x61, 0x62, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x64, 0x69, 0x73, 0x61,
	0x62, 0x6c, 0x65, 0x22, 0xeb, 0x04, 0x0a, 0x0c, 0x4e, 0x6f, 0x64, 0x65, 0x57, 0x6f, 0x72, 0x6b,
	0x6c, 0x6f, 0x61, 0x64, 0x12, 0x22, 0x0a, 0x0c, 0x77, 0x6f, 0x72, 0x6b, 0x6c, 0x6f, 0x61, 0x64,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x77, 0x6f, 0x72, 0x6b,
	0x6c, 0x6f, 0x61, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x77, 0x6f, 0x72, 0x6b,
	0x6c, 0x6f, 0x61, 0x64, 0x54, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x77, 0x6f, 0x72, 0x6b, 0x6c, 0x6f, 0x61, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x30, 0x0a, 0x13,
	0x77, 0x6f, 0x72, 0x6b, 0x6c, 0x6f, 0x61, 0x64, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e,
	0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x77, 0x6f, 0x72, 0x6b, 0x6c,
	0x6f, 0x61, 0x64, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18,
	0x0a, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x1c, 0x0a, 0x09, 0x71, 0x75, 0x65, 0x75,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x71, 0x75, 0x65,
	0x75, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x26,
	0x0a, 0x0e, 0x77, 0x6f, 0x72, 0x6b, 0x6c, 0x6f, 0x61, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x77, 0x6f, 0x72, 0x6b, 0x6c, 0x6f, 0x61, 0x64,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x70, 0x70, 0x4e, 0x61, 0x6d,
	0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x4e, 0x0a, 0x07, 0x61, 0x70, 0x70, 0x54, 0x79, 0x70, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x34, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69,
	0x6f, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x2e,
	0x76, 0x31, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x6c, 0x6f, 0x61, 0x64, 0x2e,
	0x41, 0x70, 0x70, 0x54, 0x79, 0x70, 0x65, 0x52, 0x07, 0x61, 0x70, 0x70, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x30, 0x0a, 0x13, 0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x64,
	0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x30, 0x0a, 0x13, 0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x54, 0x79, 0x70, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x13, 0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x40, 0x0a, 0x0d, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x70,
	0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0d, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x22, 0x0a, 0x0c, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e,
	0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x69, 0x6e,
	0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x35, 0x0a, 0x07, 0x41, 0x70,
	0x70, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0e, 0x0a, 0x0a, 0x57, 0x65, 0x62, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x49, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e,
	0x63, 0x65, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10,
	0x02, 0x22, 0x84, 0x01, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x57, 0x6f, 0x72,
	0x6b, 0x6c, 0x6f, 0x61, 0x64, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x52,
	0x0a, 0x0d, 0x6e, 0x6f, 0x64, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x6c, 0x6f, 0x61, 0x64, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73,
	0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x6c,
	0x6f, 0x61, 0x64, 0x52, 0x0d, 0x6e, 0x6f, 0x64, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x6c, 0x6f, 0x61,
	0x64, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x22, 0x4f, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x4e,
	0x6f, 0x64, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x6c, 0x6f, 0x61, 0x64, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x02, 0x69, 0x70, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x51, 0x0a, 0x19, 0x45, 0x76, 0x69,
	0x63, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x6c, 0x6f, 0x61, 0x64, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77,
	0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x69, 0x0a, 0x15,
	0x4e, 0x6f, 0x64, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x6c, 0x6f, 0x61, 0x64, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x50, 0x0a, 0x0c, 0x6e, 0x6f, 0x64, 0x65, 0x57, 0x6f, 0x72,
	0x6b, 0x6c, 0x6f, 0x61, 0x64, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x61, 0x70,
	0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x6f, 0x64,
	0x65, 0x57, 0x6f, 0x72, 0x6b, 0x6c, 0x6f, 0x61, 0x64, 0x52, 0x0c, 0x6e, 0x6f, 0x64, 0x65, 0x57,
	0x6f, 0x72, 0x6b, 0x6c, 0x6f, 0x61, 0x64, 0x22, 0xbb, 0x01, 0x0a, 0x22, 0x43, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x47, 0x70, 0x75, 0x41, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x12, 0x24,
	0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x5f, 0x0a, 0x11, 0x67, 0x70, 0x75, 0x41, 0x6c, 0x6c, 0x6f, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x31, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x70, 0x75, 0x41, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f,
	0x64, 0x65, 0x52, 0x11, 0x67, 0x70, 0x75, 0x41, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x2a, 0x3c, 0x0a, 0x04, 0x4b, 0x69, 0x6e, 0x64, 0x12, 0x0c, 0x0a,
	0x08, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x69, 0x6e, 0x67, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x49,
	0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x10, 0x01, 0x12, 0x07, 0x0a, 0x03, 0x4a, 0x6f,
	0x62, 0x10, 0x02, 0x12, 0x0e, 0x0a, 0x0a, 0x57, 0x65, 0x62, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x10, 0x03, 0x2a, 0x2b, 0x0a, 0x0b, 0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x0d, 0x0a, 0x09, 0x42, 0x61, 0x72, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x6c, 0x10,
	0x00, 0x12, 0x0d, 0x0a, 0x09, 0x43, 0x6c, 0x6f, 0x75, 0x64, 0x48, 0x6f, 0x73, 0x74, 0x10, 0x01,
	0x2a, 0x38, 0x0a, 0x11, 0x47, 0x70, 0x75, 0x41, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x08, 0x0a, 0x04, 0x4e, 0x6f, 0x6e, 0x65, 0x10, 0x00, 0x12,
	0x0d, 0x0a, 0x09, 0x45, 0x78, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x76, 0x65, 0x10, 0x01, 0x12, 0x0a,
	0x0a, 0x06, 0x53, 0x68, 0x61, 0x72, 0x65, 0x64, 0x10, 0x02, 0x32, 0xcf, 0x04, 0x0a, 0x14, 0x52,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x12, 0xaa, 0x01, 0x0a, 0x09, 0x4c, 0x69, 0x73, 0x74, 0x4e, 0x6f, 0x64, 0x65,
	0x73, 0x12, 0x35, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69,
	0x6f, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x1a, 0x34, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e,
	0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4e, 0x6f,
	0x64, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x30,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2a, 0x12, 0x28, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31,
	0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2f, 0x7b, 0x77, 0x6f, 0x72, 0x6b,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x6e, 0x6f, 0x64, 0x65, 0x73,
	0x12, 0xba, 0x01, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x53, 0x74, 0x61, 0x74,
	0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x12, 0x38, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69,
	0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x53,
	0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f,
	0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x2e, 0x76,
	0x31, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73,
	0x22, 0x3b, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x35, 0x12, 0x33, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f,
	0x76, 0x31, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2f, 0x7b, 0x77, 0x6f,
	0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x6e, 0x6f, 0x64,
	0x65, 0x73, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x12, 0xcc, 0x01,
	0x0a, 0x17, 0x47, 0x65, 0x74, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x12, 0x3e, 0x2e, 0x61, 0x70, 0x69, 0x73,
	0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4d, 0x65, 0x74, 0x72, 0x69,
	0x63, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x33, 0x2e, 0x61, 0x70, 0x69, 0x73,
	0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x22, 0x3c,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x36, 0x12, 0x34, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31,
	0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2f, 0x7b, 0x77, 0x6f, 0x72, 0x6b,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x73, 0x2f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x32, 0xc3, 0x0b, 0x0a,
	0x0b, 0x4e, 0x6f, 0x64, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0xae, 0x01, 0x0a,
	0x0d, 0x47, 0x65, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x34,
	0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x2a, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74,
	0x75, 0x64, 0x69, 0x6f, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x22, 0x3b, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x35, 0x12, 0x33, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f,
	0x76, 0x31, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2f, 0x7b, 0x77, 0x6f,
	0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x6e, 0x6f, 0x64,
	0x65, 0x2f, 0x7b, 0x69, 0x70, 0x7d, 0x2f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0xcb, 0x01,
	0x0a, 0x19, 0x4c, 0x69, 0x73, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x46, 0x6f, 0x72, 0x46, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x12, 0x40, 0x2e, 0x61, 0x70,
	0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x4e, 0x6f, 0x64, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x46, 0x6f, 0x72, 0x46, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x1a, 0x32, 0x2e,
	0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x61, 0x73, 0x63, 0x61, 0x64, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x22, 0x38, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x32, 0x12, 0x30, 0x2f, 0x61, 0x70, 0x69, 0x73,
	0x2f, 0x76, 0x31, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2f, 0x7b, 0x77,
	0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x6e, 0x6f,
	0x64, 0x65, 0x73, 0x2f, 0x63, 0x61, 0x73, 0x63, 0x61, 0x64, 0x65, 0x12, 0xb6, 0x01, 0x0a, 0x12,
	0x47, 0x65, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x56, 0x69,
	0x65, 0x77, 0x12, 0x32, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64,
	0x69, 0x6f, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x56, 0x69, 0x65, 0x77, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69,
	0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x4d, 0x65, 0x74, 0x72,
	0x69, 0x63, 0x56, 0x69, 0x65, 0x77, 0x22, 0x3c, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x36, 0x12, 0x34,
	0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x2f, 0x7b, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x7d, 0x2f, 0x6e, 0x6f, 0x64, 0x65, 0x2f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x2f,
	0x76, 0x69, 0x65, 0x77, 0x12, 0xaf, 0x01, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x4e, 0x6f, 0x64, 0x65,
	0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x12, 0x34, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69,
	0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x4d,
	0x65, 0x74, 0x72, 0x69, 0x63, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2a, 0x2e, 0x61,
	0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x6f,
	0x64, 0x65, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x22, 0x3c, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x36,
	0x12, 0x34, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x2f, 0x7b, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x6e, 0x6f, 0x64, 0x65, 0x2f, 0x7b, 0x69, 0x70, 0x7d, 0x2f, 0x6d,
	0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x12, 0x9a, 0x01, 0x0a, 0x0b, 0x44, 0x69, 0x73, 0x61, 0x62,
	0x6c, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x12, 0x32, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69,
	0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x4e,
	0x6f, 0x64, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70,
	0x74, 0x79, 0x22, 0x3f, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x39, 0x3a, 0x01, 0x2a, 0x22, 0x34, 0x2f,
	0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x2f, 0x7b, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x7d, 0x2f, 0x6e, 0x6f, 0x64, 0x65, 0x2f, 0x7b, 0x69, 0x70, 0x7d, 0x2f, 0x64, 0x69, 0x73, 0x61,
	0x62, 0x6c, 0x65, 0x12, 0xc5, 0x01, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x57,
	0x6f, 0x72, 0x6b, 0x6c, 0x6f, 0x61, 0x64, 0x73, 0x12, 0x37, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e,
	0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4e, 0x6f, 0x64,
	0x65, 0x57, 0x6f, 0x72, 0x6b, 0x6c, 0x6f, 0x61, 0x64, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x38, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69,
	0x6f, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x2e,
	0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x6c, 0x6f,
	0x61, 0x64, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x3e, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x38, 0x12, 0x36, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x6f,
	0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2f, 0x7b, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x6e, 0x6f, 0x64, 0x65, 0x2f, 0x7b, 0x69, 0x70,
	0x7d, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x6c, 0x6f, 0x61, 0x64, 0x73, 0x12, 0xa6, 0x01, 0x0a, 0x12,
	0x45, 0x76, 0x69, 0x63, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x6c, 0x6f, 0x61,
	0x64, 0x73, 0x12, 0x39, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64,
	0x69, 0x6f, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x2e, 0x76, 0x31, 0x2e, 0x45, 0x76, 0x69, 0x63, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x57, 0x6f, 0x72,
	0x6b, 0x6c, 0x6f, 0x61, 0x64, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x3d, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x37, 0x3a, 0x01, 0x2a,
	0x22, 0x32, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x2f, 0x7b, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x6e, 0x6f, 0x64, 0x65, 0x2f, 0x7b, 0x69, 0x70, 0x7d, 0x2f, 0x65,
	0x76, 0x69, 0x63, 0x74, 0x12, 0xbb, 0x01, 0x0a, 0x1b, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x4e,
	0x6f, 0x64, 0x65, 0x47, 0x70, 0x75, 0x41, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x4d, 0x6f, 0x64, 0x65, 0x12, 0x42, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74,
	0x75, 0x64, 0x69, 0x6f, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x4e, 0x6f, 0x64, 0x65,
	0x47, 0x70, 0x75, 0x41, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79,
	0x22, 0x40, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x3a, 0x3a, 0x01, 0x2a, 0x22, 0x35, 0x2f, 0x61, 0x70,
	0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2f,
	0x7b, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f,
	0x6e, 0x6f, 0x64, 0x65, 0x2f, 0x7b, 0x69, 0x70, 0x7d, 0x2f, 0x67, 0x70, 0x75, 0x2d, 0x6d, 0x6f,
	0x64, 0x65, 0x42, 0x47, 0x5a, 0x45, 0x67, 0x69, 0x74, 0x2e, 0x6c, 0x69, 0x61, 0x6e, 0x6a, 0x69,
	0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6e, 0x61, 0x74, 0x69, 0x76,
	0x65, 0x2f, 0x6b, 0x69, 0x63, 0x2f, 0x6b, 0x69, 0x63, 0x2d, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f,
	0x72, 0x6d, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x67, 0x72, 0x6f, 0x75, 0x70, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_aistudio_resourcegroup_v1_resourcegroup_proto_rawDescOnce sync.Once
	file_aistudio_resourcegroup_v1_resourcegroup_proto_rawDescData = file_aistudio_resourcegroup_v1_resourcegroup_proto_rawDesc
)

func file_aistudio_resourcegroup_v1_resourcegroup_proto_rawDescGZIP() []byte {
	file_aistudio_resourcegroup_v1_resourcegroup_proto_rawDescOnce.Do(func() {
		file_aistudio_resourcegroup_v1_resourcegroup_proto_rawDescData = protoimpl.X.CompressGZIP(file_aistudio_resourcegroup_v1_resourcegroup_proto_rawDescData)
	})
	return file_aistudio_resourcegroup_v1_resourcegroup_proto_rawDescData
}

var file_aistudio_resourcegroup_v1_resourcegroup_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes = make([]protoimpl.MessageInfo, 36)
var file_aistudio_resourcegroup_v1_resourcegroup_proto_goTypes = []any{
	(Kind)(0),                                  // 0: apis.aistudio.resourcegroup.v1.Kind
	(MachineType)(0),                           // 1: apis.aistudio.resourcegroup.v1.MachineType
	(GpuAllocationMode)(0),                     // 2: apis.aistudio.resourcegroup.v1.GpuAllocationMode
	(NodeWorkload_AppType)(0),                  // 3: apis.aistudio.resourcegroup.v1.NodeWorkload.AppType
	(*ListClustersRequest)(nil),                // 4: apis.aistudio.resourcegroup.v1.ListClustersRequest
	(*ApplyResourceResult)(nil),                // 5: apis.aistudio.resourcegroup.v1.ApplyResourceResult
	(*ApplyResourceRequest)(nil),               // 6: apis.aistudio.resourcegroup.v1.ApplyResourceRequest
	(*ApplyResource)(nil),                      // 7: apis.aistudio.resourcegroup.v1.ApplyResource
	(*RecyclingNodeRequest)(nil),               // 8: apis.aistudio.resourcegroup.v1.RecyclingNodeRequest
	(*NodeDetail)(nil),                         // 9: apis.aistudio.resourcegroup.v1.NodeDetail
	(*GetResourceGroupMetricsRequest)(nil),     // 10: apis.aistudio.resourcegroup.v1.GetResourceGroupMetricsRequest
	(*ResourceGroupMetric)(nil),                // 11: apis.aistudio.resourcegroup.v1.ResourceGroupMetric
	(*NodeMetric)(nil),                         // 12: apis.aistudio.resourcegroup.v1.NodeMetric
	(*GetNodeViewRequest)(nil),                 // 13: apis.aistudio.resourcegroup.v1.GetNodeViewRequest
	(*Cluster)(nil),                            // 14: apis.aistudio.resourcegroup.v1.Cluster
	(*ListClusterResponse)(nil),                // 15: apis.aistudio.resourcegroup.v1.ListClusterResponse
	(*NodeMetricView)(nil),                     // 16: apis.aistudio.resourcegroup.v1.NodeMetricView
	(*GetNodeStatisticsRequest)(nil),           // 17: apis.aistudio.resourcegroup.v1.GetNodeStatisticsRequest
	(*NodeStatistics)(nil),                     // 18: apis.aistudio.resourcegroup.v1.NodeStatistics
	(*OwnReference)(nil),                       // 19: apis.aistudio.resourcegroup.v1.OwnReference
	(*PodMetric)(nil),                          // 20: apis.aistudio.resourcegroup.v1.PodMetric
	(*ContainerMetric)(nil),                    // 21: apis.aistudio.resourcegroup.v1.ContainerMetric
	(*PodCount)(nil),                           // 22: apis.aistudio.resourcegroup.v1.PodCount
	(*GetNodeDetailRequest)(nil),               // 23: apis.aistudio.resourcegroup.v1.GetNodeDetailRequest
	(*ListNodeDetailResult)(nil),               // 24: apis.aistudio.resourcegroup.v1.ListNodeDetailResult
	(*ListNodeDetailOptions)(nil),              // 25: apis.aistudio.resourcegroup.v1.ListNodeDetailOptions
	(*ListNodeDetailForFrontendOptions)(nil),   // 26: apis.aistudio.resourcegroup.v1.ListNodeDetailForFrontendOptions
	(*GetNodeMetricRequest)(nil),               // 27: apis.aistudio.resourcegroup.v1.GetNodeMetricRequest
	(*GetNodeSeriesRequest)(nil),               // 28: apis.aistudio.resourcegroup.v1.GetNodeSeriesRequest
	(*GetNodeSeriesResponse)(nil),              // 29: apis.aistudio.resourcegroup.v1.GetNodeSeriesResponse
	(*CascadeNodeDetail)(nil),                  // 30: apis.aistudio.resourcegroup.v1.CascadeNodeDetail
	(*NodeDetailChildren)(nil),                 // 31: apis.aistudio.resourcegroup.v1.NodeDetailChildren
	(*CascadeNodeDetails)(nil),                 // 32: apis.aistudio.resourcegroup.v1.CascadeNodeDetails
	(*DisableNodeRequest)(nil),                 // 33: apis.aistudio.resourcegroup.v1.DisableNodeRequest
	(*NodeWorkload)(nil),                       // 34: apis.aistudio.resourcegroup.v1.NodeWorkload
	(*GetNodeWorkloadsResponse)(nil),           // 35: apis.aistudio.resourcegroup.v1.GetNodeWorkloadsResponse
	(*GetNodeWorkloadsRequest)(nil),            // 36: apis.aistudio.resourcegroup.v1.GetNodeWorkloadsRequest
	(*EvictNodeWorkloadsRequest)(nil),          // 37: apis.aistudio.resourcegroup.v1.EvictNodeWorkloadsRequest
	(*NodeWorkloadsResponse)(nil),              // 38: apis.aistudio.resourcegroup.v1.NodeWorkloadsResponse
	(*ChangeNodeGpuAllocationModeRequest)(nil), // 39: apis.aistudio.resourcegroup.v1.ChangeNodeGpuAllocationModeRequest
	(*common.ResourceMetric)(nil),              // 40: apis.common.ResourceMetric
	(*common.Specification)(nil),               // 41: apis.common.Specification
	(*emptypb.Empty)(nil),                      // 42: google.protobuf.Empty
}
var file_aistudio_resourcegroup_v1_resourcegroup_proto_depIdxs = []int32{
	7,  // 0: apis.aistudio.resourcegroup.v1.ApplyResourceRequest.resourcesList:type_name -> apis.aistudio.resourcegroup.v1.ApplyResource
	1,  // 1: apis.aistudio.resourcegroup.v1.NodeDetail.machineType:type_name -> apis.aistudio.resourcegroup.v1.MachineType
	40, // 2: apis.aistudio.resourcegroup.v1.NodeDetail.cpuMetric:type_name -> apis.common.ResourceMetric
	40, // 3: apis.aistudio.resourcegroup.v1.NodeDetail.memoryMetric:type_name -> apis.common.ResourceMetric
	40, // 4: apis.aistudio.resourcegroup.v1.NodeDetail.gpuMetric:type_name -> apis.common.ResourceMetric
	40, // 5: apis.aistudio.resourcegroup.v1.NodeDetail.gpuMemMetric:type_name -> apis.common.ResourceMetric
	2,  // 6: apis.aistudio.resourcegroup.v1.NodeDetail.gpuAllocationMode:type_name -> apis.aistudio.resourcegroup.v1.GpuAllocationMode
	40, // 7: apis.aistudio.resourcegroup.v1.ResourceGroupMetric.cpu:type_name -> apis.common.ResourceMetric
	40, // 8: apis.aistudio.resourcegroup.v1.ResourceGroupMetric.memory:type_name -> apis.common.ResourceMetric
	40, // 9: apis.aistudio.resourcegroup.v1.ResourceGroupMetric.gpu:type_name -> apis.common.ResourceMetric
	40, // 10: apis.aistudio.resourcegroup.v1.ResourceGroupMetric.gpuMem:type_name -> apis.common.ResourceMetric
	40, // 11: apis.aistudio.resourcegroup.v1.NodeMetric.cpu:type_name -> apis.common.ResourceMetric
	40, // 12: apis.aistudio.resourcegroup.v1.NodeMetric.memory:type_name -> apis.common.ResourceMetric
	40, // 13: apis.aistudio.resourcegroup.v1.NodeMetric.gpu:type_name -> apis.common.ResourceMetric
	20, // 14: apis.aistudio.resourcegroup.v1.NodeMetric.pods:type_name -> apis.aistudio.resourcegroup.v1.PodMetric
	22, // 15: apis.aistudio.resourcegroup.v1.NodeMetric.podCount:type_name -> apis.aistudio.resourcegroup.v1.PodCount
	40, // 16: apis.aistudio.resourcegroup.v1.NodeMetric.gpuMem:type_name -> apis.common.ResourceMetric
	14, // 17: apis.aistudio.resourcegroup.v1.ListClusterResponse.clusterList:type_name -> apis.aistudio.resourcegroup.v1.Cluster
	12, // 18: apis.aistudio.resourcegroup.v1.NodeMetricView.nodeMetricView:type_name -> apis.aistudio.resourcegroup.v1.NodeMetric
	0,  // 19: apis.aistudio.resourcegroup.v1.OwnReference.kind:type_name -> apis.aistudio.resourcegroup.v1.Kind
	19, // 20: apis.aistudio.resourcegroup.v1.PodMetric.ownerReference:type_name -> apis.aistudio.resourcegroup.v1.OwnReference
	40, // 21: apis.aistudio.resourcegroup.v1.PodMetric.cpu:type_name -> apis.common.ResourceMetric
	40, // 22: apis.aistudio.resourcegroup.v1.PodMetric.memory:type_name -> apis.common.ResourceMetric
	40, // 23: apis.aistudio.resourcegroup.v1.PodMetric.gpu:type_name -> apis.common.ResourceMetric
	21, // 24: apis.aistudio.resourcegroup.v1.PodMetric.containers:type_name -> apis.aistudio.resourcegroup.v1.ContainerMetric
	40, // 25: apis.aistudio.resourcegroup.v1.PodMetric.gpuMem:type_name -> apis.common.ResourceMetric
	40, // 26: apis.aistudio.resourcegroup.v1.ContainerMetric.cpu:type_name -> apis.common.ResourceMetric
	40, // 27: apis.aistudio.resourcegroup.v1.ContainerMetric.memory:type_name -> apis.common.ResourceMetric
	40, // 28: apis.aistudio.resourcegroup.v1.ContainerMetric.gpu:type_name -> apis.common.ResourceMetric
	40, // 29: apis.aistudio.resourcegroup.v1.ContainerMetric.gpuMem:type_name -> apis.common.ResourceMetric
	9,  // 30: apis.aistudio.resourcegroup.v1.ListNodeDetailResult.nodes:type_name -> apis.aistudio.resourcegroup.v1.NodeDetail
	31, // 31: apis.aistudio.resourcegroup.v1.CascadeNodeDetail.children:type_name -> apis.aistudio.resourcegroup.v1.NodeDetailChildren
	30, // 32: apis.aistudio.resourcegroup.v1.CascadeNodeDetails.nodes:type_name -> apis.aistudio.resourcegroup.v1.CascadeNodeDetail
	3,  // 33: apis.aistudio.resourcegroup.v1.NodeWorkload.appType:type_name -> apis.aistudio.resourcegroup.v1.NodeWorkload.AppType
	41, // 34: apis.aistudio.resourcegroup.v1.NodeWorkload.specification:type_name -> apis.common.Specification
	34, // 35: apis.aistudio.resourcegroup.v1.GetNodeWorkloadsResponse.nodeWorkloads:type_name -> apis.aistudio.resourcegroup.v1.NodeWorkload
	34, // 36: apis.aistudio.resourcegroup.v1.NodeWorkloadsResponse.nodeWorkload:type_name -> apis.aistudio.resourcegroup.v1.NodeWorkload
	2,  // 37: apis.aistudio.resourcegroup.v1.ChangeNodeGpuAllocationModeRequest.gpuAllocationMode:type_name -> apis.aistudio.resourcegroup.v1.GpuAllocationMode
	25, // 38: apis.aistudio.resourcegroup.v1.ResourceGroupService.ListNodes:input_type -> apis.aistudio.resourcegroup.v1.ListNodeDetailOptions
	17, // 39: apis.aistudio.resourcegroup.v1.ResourceGroupService.GetNodeStatistics:input_type -> apis.aistudio.resourcegroup.v1.GetNodeStatisticsRequest
	10, // 40: apis.aistudio.resourcegroup.v1.ResourceGroupService.GetResourceGroupMetrics:input_type -> apis.aistudio.resourcegroup.v1.GetResourceGroupMetricsRequest
	23, // 41: apis.aistudio.resourcegroup.v1.NodeService.GetNodeDetail:input_type -> apis.aistudio.resourcegroup.v1.GetNodeDetailRequest
	26, // 42: apis.aistudio.resourcegroup.v1.NodeService.ListNodeDetailForFrontend:input_type -> apis.aistudio.resourcegroup.v1.ListNodeDetailForFrontendOptions
	13, // 43: apis.aistudio.resourcegroup.v1.NodeService.GetNodeMetricsView:input_type -> apis.aistudio.resourcegroup.v1.GetNodeViewRequest
	27, // 44: apis.aistudio.resourcegroup.v1.NodeService.GetNodeMetric:input_type -> apis.aistudio.resourcegroup.v1.GetNodeMetricRequest
	33, // 45: apis.aistudio.resourcegroup.v1.NodeService.DisableNode:input_type -> apis.aistudio.resourcegroup.v1.DisableNodeRequest
	36, // 46: apis.aistudio.resourcegroup.v1.NodeService.GetNodeWorkloads:input_type -> apis.aistudio.resourcegroup.v1.GetNodeWorkloadsRequest
	37, // 47: apis.aistudio.resourcegroup.v1.NodeService.EvictNodeWorkloads:input_type -> apis.aistudio.resourcegroup.v1.EvictNodeWorkloadsRequest
	39, // 48: apis.aistudio.resourcegroup.v1.NodeService.ChangeNodeGpuAllocationMode:input_type -> apis.aistudio.resourcegroup.v1.ChangeNodeGpuAllocationModeRequest
	24, // 49: apis.aistudio.resourcegroup.v1.ResourceGroupService.ListNodes:output_type -> apis.aistudio.resourcegroup.v1.ListNodeDetailResult
	18, // 50: apis.aistudio.resourcegroup.v1.ResourceGroupService.GetNodeStatistics:output_type -> apis.aistudio.resourcegroup.v1.NodeStatistics
	11, // 51: apis.aistudio.resourcegroup.v1.ResourceGroupService.GetResourceGroupMetrics:output_type -> apis.aistudio.resourcegroup.v1.ResourceGroupMetric
	9,  // 52: apis.aistudio.resourcegroup.v1.NodeService.GetNodeDetail:output_type -> apis.aistudio.resourcegroup.v1.NodeDetail
	32, // 53: apis.aistudio.resourcegroup.v1.NodeService.ListNodeDetailForFrontend:output_type -> apis.aistudio.resourcegroup.v1.CascadeNodeDetails
	16, // 54: apis.aistudio.resourcegroup.v1.NodeService.GetNodeMetricsView:output_type -> apis.aistudio.resourcegroup.v1.NodeMetricView
	12, // 55: apis.aistudio.resourcegroup.v1.NodeService.GetNodeMetric:output_type -> apis.aistudio.resourcegroup.v1.NodeMetric
	42, // 56: apis.aistudio.resourcegroup.v1.NodeService.DisableNode:output_type -> google.protobuf.Empty
	35, // 57: apis.aistudio.resourcegroup.v1.NodeService.GetNodeWorkloads:output_type -> apis.aistudio.resourcegroup.v1.GetNodeWorkloadsResponse
	42, // 58: apis.aistudio.resourcegroup.v1.NodeService.EvictNodeWorkloads:output_type -> google.protobuf.Empty
	42, // 59: apis.aistudio.resourcegroup.v1.NodeService.ChangeNodeGpuAllocationMode:output_type -> google.protobuf.Empty
	49, // [49:60] is the sub-list for method output_type
	38, // [38:49] is the sub-list for method input_type
	38, // [38:38] is the sub-list for extension type_name
	38, // [38:38] is the sub-list for extension extendee
	0,  // [0:38] is the sub-list for field type_name
}

func init() { file_aistudio_resourcegroup_v1_resourcegroup_proto_init() }
func file_aistudio_resourcegroup_v1_resourcegroup_proto_init() {
	if File_aistudio_resourcegroup_v1_resourcegroup_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*ListClustersRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*ApplyResourceResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*ApplyResourceRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*ApplyResource); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*RecyclingNodeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*NodeDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[6].Exporter = func(v any, i int) any {
			switch v := v.(*GetResourceGroupMetricsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[7].Exporter = func(v any, i int) any {
			switch v := v.(*ResourceGroupMetric); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[8].Exporter = func(v any, i int) any {
			switch v := v.(*NodeMetric); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[9].Exporter = func(v any, i int) any {
			switch v := v.(*GetNodeViewRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[10].Exporter = func(v any, i int) any {
			switch v := v.(*Cluster); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[11].Exporter = func(v any, i int) any {
			switch v := v.(*ListClusterResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[12].Exporter = func(v any, i int) any {
			switch v := v.(*NodeMetricView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[13].Exporter = func(v any, i int) any {
			switch v := v.(*GetNodeStatisticsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[14].Exporter = func(v any, i int) any {
			switch v := v.(*NodeStatistics); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[15].Exporter = func(v any, i int) any {
			switch v := v.(*OwnReference); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[16].Exporter = func(v any, i int) any {
			switch v := v.(*PodMetric); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[17].Exporter = func(v any, i int) any {
			switch v := v.(*ContainerMetric); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[18].Exporter = func(v any, i int) any {
			switch v := v.(*PodCount); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[19].Exporter = func(v any, i int) any {
			switch v := v.(*GetNodeDetailRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[20].Exporter = func(v any, i int) any {
			switch v := v.(*ListNodeDetailResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[21].Exporter = func(v any, i int) any {
			switch v := v.(*ListNodeDetailOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[22].Exporter = func(v any, i int) any {
			switch v := v.(*ListNodeDetailForFrontendOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[23].Exporter = func(v any, i int) any {
			switch v := v.(*GetNodeMetricRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[24].Exporter = func(v any, i int) any {
			switch v := v.(*GetNodeSeriesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[25].Exporter = func(v any, i int) any {
			switch v := v.(*GetNodeSeriesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[26].Exporter = func(v any, i int) any {
			switch v := v.(*CascadeNodeDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[27].Exporter = func(v any, i int) any {
			switch v := v.(*NodeDetailChildren); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[28].Exporter = func(v any, i int) any {
			switch v := v.(*CascadeNodeDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[29].Exporter = func(v any, i int) any {
			switch v := v.(*DisableNodeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[30].Exporter = func(v any, i int) any {
			switch v := v.(*NodeWorkload); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[31].Exporter = func(v any, i int) any {
			switch v := v.(*GetNodeWorkloadsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[32].Exporter = func(v any, i int) any {
			switch v := v.(*GetNodeWorkloadsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[33].Exporter = func(v any, i int) any {
			switch v := v.(*EvictNodeWorkloadsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[34].Exporter = func(v any, i int) any {
			switch v := v.(*NodeWorkloadsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes[35].Exporter = func(v any, i int) any {
			switch v := v.(*ChangeNodeGpuAllocationModeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_aistudio_resourcegroup_v1_resourcegroup_proto_rawDesc,
			NumEnums:      4,
			NumMessages:   36,
			NumExtensions: 0,
			NumServices:   2,
		},
		GoTypes:           file_aistudio_resourcegroup_v1_resourcegroup_proto_goTypes,
		DependencyIndexes: file_aistudio_resourcegroup_v1_resourcegroup_proto_depIdxs,
		EnumInfos:         file_aistudio_resourcegroup_v1_resourcegroup_proto_enumTypes,
		MessageInfos:      file_aistudio_resourcegroup_v1_resourcegroup_proto_msgTypes,
	}.Build()
	File_aistudio_resourcegroup_v1_resourcegroup_proto = out.File
	file_aistudio_resourcegroup_v1_resourcegroup_proto_rawDesc = nil
	file_aistudio_resourcegroup_v1_resourcegroup_proto_goTypes = nil
	file_aistudio_resourcegroup_v1_resourcegroup_proto_depIdxs = nil
}
