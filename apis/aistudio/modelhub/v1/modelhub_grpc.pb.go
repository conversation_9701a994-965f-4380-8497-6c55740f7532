// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v5.29.3
// source: aistudio/modelhub/v1/modelhub.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	ModelHubService_CreateModel_FullMethodName           = "/apis.aistudio.modelhub.v1.ModelHubService/CreateModel"
	ModelHubService_ListModels_FullMethodName            = "/apis.aistudio.modelhub.v1.ModelHubService/ListModels"
	ModelHubService_UpdateModel_FullMethodName           = "/apis.aistudio.modelhub.v1.ModelHubService/UpdateModel"
	ModelHubService_DeleteModel_FullMethodName           = "/apis.aistudio.modelhub.v1.ModelHubService/DeleteModel"
	ModelHubService_ShowModel_FullMethodName             = "/apis.aistudio.modelhub.v1.ModelHubService/ShowModel"
	ModelHubService_GetGitToken_FullMethodName           = "/apis.aistudio.modelhub.v1.ModelHubService/GetGitToken"
	ModelHubService_CreateModelHub_FullMethodName        = "/apis.aistudio.modelhub.v1.ModelHubService/CreateModelHub"
	ModelHubService_GetModelHub_FullMethodName           = "/apis.aistudio.modelhub.v1.ModelHubService/GetModelHub"
	ModelHubService_GetTree_FullMethodName               = "/apis.aistudio.modelhub.v1.ModelHubService/GetTree"
	ModelHubService_GetLastCommit_FullMethodName         = "/apis.aistudio.modelhub.v1.ModelHubService/GetLastCommit"
	ModelHubService_GetRepoFileContent_FullMethodName    = "/apis.aistudio.modelhub.v1.ModelHubService/GetRepoFileContent"
	ModelHubService_CreateUser_FullMethodName            = "/apis.aistudio.modelhub.v1.ModelHubService/CreateUser"
	ModelHubService_AddCollaborator_FullMethodName       = "/apis.aistudio.modelhub.v1.ModelHubService/AddCollaborator"
	ModelHubService_DeleteCollaborator_FullMethodName    = "/apis.aistudio.modelhub.v1.ModelHubService/DeleteCollaborator"
	ModelHubService_ChangeCollaborator_FullMethodName    = "/apis.aistudio.modelhub.v1.ModelHubService/ChangeCollaborator"
	ModelHubService_ListCollaborator_FullMethodName      = "/apis.aistudio.modelhub.v1.ModelHubService/ListCollaborator"
	ModelHubService_CheckModelIsExist_FullMethodName     = "/apis.aistudio.modelhub.v1.ModelHubService/CheckModelIsExist"
	ModelHubService_CheckCollaboratorAuth_FullMethodName = "/apis.aistudio.modelhub.v1.ModelHubService/CheckCollaboratorAuth"
	ModelHubService_InitModelHubOrg_FullMethodName       = "/apis.aistudio.modelhub.v1.ModelHubService/InitModelHubOrg"
	ModelHubService_GetModelInfo_FullMethodName          = "/apis.aistudio.modelhub.v1.ModelHubService/GetModelInfo"
	ModelHubService_GetTags_FullMethodName               = "/apis.aistudio.modelhub.v1.ModelHubService/GetTags"
	ModelHubService_GetBranches_FullMethodName           = "/apis.aistudio.modelhub.v1.ModelHubService/GetBranches"
	ModelHubService_CreateBranch_FullMethodName          = "/apis.aistudio.modelhub.v1.ModelHubService/CreateBranch"
	ModelHubService_GetAllFiles_FullMethodName           = "/apis.aistudio.modelhub.v1.ModelHubService/GetAllFiles"
	ModelHubService_CheckBranch_FullMethodName           = "/apis.aistudio.modelhub.v1.ModelHubService/CheckBranch"
	ModelHubService_SyncModel_FullMethodName             = "/apis.aistudio.modelhub.v1.ModelHubService/SyncModel"
	ModelHubService_CheckModelForSync_FullMethodName     = "/apis.aistudio.modelhub.v1.ModelHubService/CheckModelForSync"
	ModelHubService_ListSyncModelRecord_FullMethodName   = "/apis.aistudio.modelhub.v1.ModelHubService/ListSyncModelRecord"
	ModelHubService_StopSyncModel_FullMethodName         = "/apis.aistudio.modelhub.v1.ModelHubService/StopSyncModel"
	ModelHubService_UpdateWarmupConfig_FullMethodName    = "/apis.aistudio.modelhub.v1.ModelHubService/UpdateWarmupConfig"
	ModelHubService_StartWarmupJob_FullMethodName        = "/apis.aistudio.modelhub.v1.ModelHubService/StartWarmupJob"
	ModelHubService_StopWarmupJob_FullMethodName         = "/apis.aistudio.modelhub.v1.ModelHubService/StopWarmupJob"
	ModelHubService_ListWarmupRecords_FullMethodName     = "/apis.aistudio.modelhub.v1.ModelHubService/ListWarmupRecords"
	ModelHubService_ListWarmedModels_FullMethodName      = "/apis.aistudio.modelhub.v1.ModelHubService/ListWarmedModels"
	ModelHubService_GitCallback_FullMethodName           = "/apis.aistudio.modelhub.v1.ModelHubService/GitCallback"
	ModelHubService_GetModelWarmupRatio_FullMethodName   = "/apis.aistudio.modelhub.v1.ModelHubService/GetModelWarmupRatio"
	ModelHubService_CreateFork_FullMethodName            = "/apis.aistudio.modelhub.v1.ModelHubService/CreateFork"
	ModelHubService_CheckModelWarmed_FullMethodName      = "/apis.aistudio.modelhub.v1.ModelHubService/CheckModelWarmed"
)

// ModelHubServiceClient is the client API for ModelHubService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ModelHubServiceClient interface {
	// 添加模型
	CreateModel(ctx context.Context, in *CreateModelRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 获取模型列表
	ListModels(ctx context.Context, in *ListModelsOptions, opts ...grpc.CallOption) (*ListModelResponse, error)
	// 更新模型
	UpdateModel(ctx context.Context, in *UpdateModelRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 删除模型
	DeleteModel(ctx context.Context, in *DeleteModelRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 展示模型
	ShowModel(ctx context.Context, in *ShowModelRequest, opts ...grpc.CallOption) (*ShowModelResponse, error)
	// 获取git token
	GetGitToken(ctx context.Context, in *GetGitTokenRequest, opts ...grpc.CallOption) (*GetGitTokenResponse, error)
	// 创建模型仓库
	CreateModelHub(ctx context.Context, in *CreateModelHubRequest, opts ...grpc.CallOption) (*CreateModelHubResponse, error)
	// 获取模型仓库
	GetModelHub(ctx context.Context, in *GetModelHubRequest, opts ...grpc.CallOption) (*GetModelHubResponse, error)
	// 获取repo tree
	GetTree(ctx context.Context, in *GetTreeRequest, opts ...grpc.CallOption) (*GetTreeResponse, error)
	// 获取repo last commit
	GetLastCommit(ctx context.Context, in *GetLastCommitRequest, opts ...grpc.CallOption) (*GetLastCommitResponse, error)
	// 获取repo 文件内容
	GetRepoFileContent(ctx context.Context, in *GetRepoFileContentRequest, opts ...grpc.CallOption) (*GetRepoFileContentResponse, error)
	// 创建模型仓库用户和token
	CreateUser(ctx context.Context, in *CreateUserRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 添加协作用户
	AddCollaborator(ctx context.Context, in *AddCollaboratorRequest, opts ...grpc.CallOption) (*AddCollaboratorResponse, error)
	// 删除协作用户
	DeleteCollaborator(ctx context.Context, in *DeleteCollaboratorRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 修改协作用户权限
	ChangeCollaborator(ctx context.Context, in *ChangeCollaboratorRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 查询协作用户
	ListCollaborator(ctx context.Context, in *ListCollaboratorRequest, opts ...grpc.CallOption) (*ListCollaboratorResponse, error)
	// 检查模型是否存在
	CheckModelIsExist(ctx context.Context, in *CheckModelIsExistRequest, opts ...grpc.CallOption) (*CheckModelIsExistResponse, error)
	// 检查是否拥有协作者权限
	CheckCollaboratorAuth(ctx context.Context, in *CheckCollaboratorAuthRequest, opts ...grpc.CallOption) (*CheckCollaboratorAuthResponse, error)
	// 初始化组织
	InitModelHubOrg(ctx context.Context, in *InitModelHubOrgRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 获取模型信息
	GetModelInfo(ctx context.Context, in *GetModelInfoRequest, opts ...grpc.CallOption) (*GetModelInfoResponse, error)
	// 获取模型tag
	GetTags(ctx context.Context, in *GetTagsRequest, opts ...grpc.CallOption) (*GetTagsResponse, error)
	// 获取模型分支
	GetBranches(ctx context.Context, in *GetBranchesRequest, opts ...grpc.CallOption) (*GetBranchesResponse, error)
	// 创建分支
	CreateBranch(ctx context.Context, in *CreateBranchRequest, opts ...grpc.CallOption) (*CreateBranchResponse, error)
	// 获取全部文件
	GetAllFiles(ctx context.Context, in *GetAllFilesRequest, opts ...grpc.CallOption) (*GetAllFilesResponse, error)
	// 校验分支是否可创建
	CheckBranch(ctx context.Context, in *CheckBranchRequest, opts ...grpc.CallOption) (*CheckBranchResponse, error)
	// 同步模型
	SyncModel(ctx context.Context, in *SyncModelRequest, opts ...grpc.CallOption) (*SyncModelResponse, error)
	// 校验模型是否可同步
	CheckModelForSync(ctx context.Context, in *CheckModelForSyncRequest, opts ...grpc.CallOption) (*CheckModelForSyncResponse, error)
	// 展示同步记录
	ListSyncModelRecord(ctx context.Context, in *ListSyncModelRecordRequest, opts ...grpc.CallOption) (*ListSyncModelRecordResponse, error)
	// 停止同步
	StopSyncModel(ctx context.Context, in *StopSyncModelRequest, opts ...grpc.CallOption) (*StopSyncModelResponse, error)
	// 更新预热配置
	UpdateWarmupConfig(ctx context.Context, in *UpdateWarmupConfigRequest, opts ...grpc.CallOption) (*UpdateWarmupConfigResponse, error)
	// 开始预热
	StartWarmupJob(ctx context.Context, in *StartWarmupJobRequest, opts ...grpc.CallOption) (*StartWarmupJobResponse, error)
	// 停止预热
	StopWarmupJob(ctx context.Context, in *StopWarmupJobRequest, opts ...grpc.CallOption) (*StopWarmupJobResponse, error)
	// 获取预热记录列表
	ListWarmupRecords(ctx context.Context, in *ListWarmupRecordsRequest, opts ...grpc.CallOption) (*ListWarmupRecordsResponse, error)
	// 获取预热模型列表
	ListWarmedModels(ctx context.Context, in *ListWarmedModelsRequest, opts ...grpc.CallOption) (*ListWarmedModelsResponse, error)
	// Git回调
	GitCallback(ctx context.Context, in *GitCallbackRequest, opts ...grpc.CallOption) (*GitCallbackResponse, error)
	// 获取预热比例
	GetModelWarmupRatio(ctx context.Context, in *GetModelWarmupRatioRequest, opts ...grpc.CallOption) (*GetModelWarmupRatioResponse, error)
	// 创建fork
	CreateFork(ctx context.Context, in *CreateForkRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 校验模型是否预热过
	CheckModelWarmed(ctx context.Context, in *CheckModelWarmedRequest, opts ...grpc.CallOption) (*CheckModelWarmedResponse, error)
}

type modelHubServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewModelHubServiceClient(cc grpc.ClientConnInterface) ModelHubServiceClient {
	return &modelHubServiceClient{cc}
}

func (c *modelHubServiceClient) CreateModel(ctx context.Context, in *CreateModelRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, ModelHubService_CreateModel_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelHubServiceClient) ListModels(ctx context.Context, in *ListModelsOptions, opts ...grpc.CallOption) (*ListModelResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListModelResponse)
	err := c.cc.Invoke(ctx, ModelHubService_ListModels_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelHubServiceClient) UpdateModel(ctx context.Context, in *UpdateModelRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, ModelHubService_UpdateModel_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelHubServiceClient) DeleteModel(ctx context.Context, in *DeleteModelRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, ModelHubService_DeleteModel_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelHubServiceClient) ShowModel(ctx context.Context, in *ShowModelRequest, opts ...grpc.CallOption) (*ShowModelResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ShowModelResponse)
	err := c.cc.Invoke(ctx, ModelHubService_ShowModel_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelHubServiceClient) GetGitToken(ctx context.Context, in *GetGitTokenRequest, opts ...grpc.CallOption) (*GetGitTokenResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetGitTokenResponse)
	err := c.cc.Invoke(ctx, ModelHubService_GetGitToken_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelHubServiceClient) CreateModelHub(ctx context.Context, in *CreateModelHubRequest, opts ...grpc.CallOption) (*CreateModelHubResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateModelHubResponse)
	err := c.cc.Invoke(ctx, ModelHubService_CreateModelHub_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelHubServiceClient) GetModelHub(ctx context.Context, in *GetModelHubRequest, opts ...grpc.CallOption) (*GetModelHubResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetModelHubResponse)
	err := c.cc.Invoke(ctx, ModelHubService_GetModelHub_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelHubServiceClient) GetTree(ctx context.Context, in *GetTreeRequest, opts ...grpc.CallOption) (*GetTreeResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetTreeResponse)
	err := c.cc.Invoke(ctx, ModelHubService_GetTree_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelHubServiceClient) GetLastCommit(ctx context.Context, in *GetLastCommitRequest, opts ...grpc.CallOption) (*GetLastCommitResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetLastCommitResponse)
	err := c.cc.Invoke(ctx, ModelHubService_GetLastCommit_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelHubServiceClient) GetRepoFileContent(ctx context.Context, in *GetRepoFileContentRequest, opts ...grpc.CallOption) (*GetRepoFileContentResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetRepoFileContentResponse)
	err := c.cc.Invoke(ctx, ModelHubService_GetRepoFileContent_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelHubServiceClient) CreateUser(ctx context.Context, in *CreateUserRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, ModelHubService_CreateUser_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelHubServiceClient) AddCollaborator(ctx context.Context, in *AddCollaboratorRequest, opts ...grpc.CallOption) (*AddCollaboratorResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AddCollaboratorResponse)
	err := c.cc.Invoke(ctx, ModelHubService_AddCollaborator_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelHubServiceClient) DeleteCollaborator(ctx context.Context, in *DeleteCollaboratorRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, ModelHubService_DeleteCollaborator_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelHubServiceClient) ChangeCollaborator(ctx context.Context, in *ChangeCollaboratorRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, ModelHubService_ChangeCollaborator_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelHubServiceClient) ListCollaborator(ctx context.Context, in *ListCollaboratorRequest, opts ...grpc.CallOption) (*ListCollaboratorResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListCollaboratorResponse)
	err := c.cc.Invoke(ctx, ModelHubService_ListCollaborator_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelHubServiceClient) CheckModelIsExist(ctx context.Context, in *CheckModelIsExistRequest, opts ...grpc.CallOption) (*CheckModelIsExistResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CheckModelIsExistResponse)
	err := c.cc.Invoke(ctx, ModelHubService_CheckModelIsExist_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelHubServiceClient) CheckCollaboratorAuth(ctx context.Context, in *CheckCollaboratorAuthRequest, opts ...grpc.CallOption) (*CheckCollaboratorAuthResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CheckCollaboratorAuthResponse)
	err := c.cc.Invoke(ctx, ModelHubService_CheckCollaboratorAuth_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelHubServiceClient) InitModelHubOrg(ctx context.Context, in *InitModelHubOrgRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, ModelHubService_InitModelHubOrg_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelHubServiceClient) GetModelInfo(ctx context.Context, in *GetModelInfoRequest, opts ...grpc.CallOption) (*GetModelInfoResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetModelInfoResponse)
	err := c.cc.Invoke(ctx, ModelHubService_GetModelInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelHubServiceClient) GetTags(ctx context.Context, in *GetTagsRequest, opts ...grpc.CallOption) (*GetTagsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetTagsResponse)
	err := c.cc.Invoke(ctx, ModelHubService_GetTags_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelHubServiceClient) GetBranches(ctx context.Context, in *GetBranchesRequest, opts ...grpc.CallOption) (*GetBranchesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetBranchesResponse)
	err := c.cc.Invoke(ctx, ModelHubService_GetBranches_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelHubServiceClient) CreateBranch(ctx context.Context, in *CreateBranchRequest, opts ...grpc.CallOption) (*CreateBranchResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateBranchResponse)
	err := c.cc.Invoke(ctx, ModelHubService_CreateBranch_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelHubServiceClient) GetAllFiles(ctx context.Context, in *GetAllFilesRequest, opts ...grpc.CallOption) (*GetAllFilesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetAllFilesResponse)
	err := c.cc.Invoke(ctx, ModelHubService_GetAllFiles_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelHubServiceClient) CheckBranch(ctx context.Context, in *CheckBranchRequest, opts ...grpc.CallOption) (*CheckBranchResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CheckBranchResponse)
	err := c.cc.Invoke(ctx, ModelHubService_CheckBranch_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelHubServiceClient) SyncModel(ctx context.Context, in *SyncModelRequest, opts ...grpc.CallOption) (*SyncModelResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SyncModelResponse)
	err := c.cc.Invoke(ctx, ModelHubService_SyncModel_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelHubServiceClient) CheckModelForSync(ctx context.Context, in *CheckModelForSyncRequest, opts ...grpc.CallOption) (*CheckModelForSyncResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CheckModelForSyncResponse)
	err := c.cc.Invoke(ctx, ModelHubService_CheckModelForSync_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelHubServiceClient) ListSyncModelRecord(ctx context.Context, in *ListSyncModelRecordRequest, opts ...grpc.CallOption) (*ListSyncModelRecordResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListSyncModelRecordResponse)
	err := c.cc.Invoke(ctx, ModelHubService_ListSyncModelRecord_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelHubServiceClient) StopSyncModel(ctx context.Context, in *StopSyncModelRequest, opts ...grpc.CallOption) (*StopSyncModelResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(StopSyncModelResponse)
	err := c.cc.Invoke(ctx, ModelHubService_StopSyncModel_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelHubServiceClient) UpdateWarmupConfig(ctx context.Context, in *UpdateWarmupConfigRequest, opts ...grpc.CallOption) (*UpdateWarmupConfigResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateWarmupConfigResponse)
	err := c.cc.Invoke(ctx, ModelHubService_UpdateWarmupConfig_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelHubServiceClient) StartWarmupJob(ctx context.Context, in *StartWarmupJobRequest, opts ...grpc.CallOption) (*StartWarmupJobResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(StartWarmupJobResponse)
	err := c.cc.Invoke(ctx, ModelHubService_StartWarmupJob_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelHubServiceClient) StopWarmupJob(ctx context.Context, in *StopWarmupJobRequest, opts ...grpc.CallOption) (*StopWarmupJobResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(StopWarmupJobResponse)
	err := c.cc.Invoke(ctx, ModelHubService_StopWarmupJob_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelHubServiceClient) ListWarmupRecords(ctx context.Context, in *ListWarmupRecordsRequest, opts ...grpc.CallOption) (*ListWarmupRecordsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListWarmupRecordsResponse)
	err := c.cc.Invoke(ctx, ModelHubService_ListWarmupRecords_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelHubServiceClient) ListWarmedModels(ctx context.Context, in *ListWarmedModelsRequest, opts ...grpc.CallOption) (*ListWarmedModelsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListWarmedModelsResponse)
	err := c.cc.Invoke(ctx, ModelHubService_ListWarmedModels_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelHubServiceClient) GitCallback(ctx context.Context, in *GitCallbackRequest, opts ...grpc.CallOption) (*GitCallbackResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GitCallbackResponse)
	err := c.cc.Invoke(ctx, ModelHubService_GitCallback_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelHubServiceClient) GetModelWarmupRatio(ctx context.Context, in *GetModelWarmupRatioRequest, opts ...grpc.CallOption) (*GetModelWarmupRatioResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetModelWarmupRatioResponse)
	err := c.cc.Invoke(ctx, ModelHubService_GetModelWarmupRatio_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelHubServiceClient) CreateFork(ctx context.Context, in *CreateForkRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, ModelHubService_CreateFork_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelHubServiceClient) CheckModelWarmed(ctx context.Context, in *CheckModelWarmedRequest, opts ...grpc.CallOption) (*CheckModelWarmedResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CheckModelWarmedResponse)
	err := c.cc.Invoke(ctx, ModelHubService_CheckModelWarmed_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ModelHubServiceServer is the server API for ModelHubService service.
// All implementations must embed UnimplementedModelHubServiceServer
// for forward compatibility
type ModelHubServiceServer interface {
	// 添加模型
	CreateModel(context.Context, *CreateModelRequest) (*emptypb.Empty, error)
	// 获取模型列表
	ListModels(context.Context, *ListModelsOptions) (*ListModelResponse, error)
	// 更新模型
	UpdateModel(context.Context, *UpdateModelRequest) (*emptypb.Empty, error)
	// 删除模型
	DeleteModel(context.Context, *DeleteModelRequest) (*emptypb.Empty, error)
	// 展示模型
	ShowModel(context.Context, *ShowModelRequest) (*ShowModelResponse, error)
	// 获取git token
	GetGitToken(context.Context, *GetGitTokenRequest) (*GetGitTokenResponse, error)
	// 创建模型仓库
	CreateModelHub(context.Context, *CreateModelHubRequest) (*CreateModelHubResponse, error)
	// 获取模型仓库
	GetModelHub(context.Context, *GetModelHubRequest) (*GetModelHubResponse, error)
	// 获取repo tree
	GetTree(context.Context, *GetTreeRequest) (*GetTreeResponse, error)
	// 获取repo last commit
	GetLastCommit(context.Context, *GetLastCommitRequest) (*GetLastCommitResponse, error)
	// 获取repo 文件内容
	GetRepoFileContent(context.Context, *GetRepoFileContentRequest) (*GetRepoFileContentResponse, error)
	// 创建模型仓库用户和token
	CreateUser(context.Context, *CreateUserRequest) (*emptypb.Empty, error)
	// 添加协作用户
	AddCollaborator(context.Context, *AddCollaboratorRequest) (*AddCollaboratorResponse, error)
	// 删除协作用户
	DeleteCollaborator(context.Context, *DeleteCollaboratorRequest) (*emptypb.Empty, error)
	// 修改协作用户权限
	ChangeCollaborator(context.Context, *ChangeCollaboratorRequest) (*emptypb.Empty, error)
	// 查询协作用户
	ListCollaborator(context.Context, *ListCollaboratorRequest) (*ListCollaboratorResponse, error)
	// 检查模型是否存在
	CheckModelIsExist(context.Context, *CheckModelIsExistRequest) (*CheckModelIsExistResponse, error)
	// 检查是否拥有协作者权限
	CheckCollaboratorAuth(context.Context, *CheckCollaboratorAuthRequest) (*CheckCollaboratorAuthResponse, error)
	// 初始化组织
	InitModelHubOrg(context.Context, *InitModelHubOrgRequest) (*emptypb.Empty, error)
	// 获取模型信息
	GetModelInfo(context.Context, *GetModelInfoRequest) (*GetModelInfoResponse, error)
	// 获取模型tag
	GetTags(context.Context, *GetTagsRequest) (*GetTagsResponse, error)
	// 获取模型分支
	GetBranches(context.Context, *GetBranchesRequest) (*GetBranchesResponse, error)
	// 创建分支
	CreateBranch(context.Context, *CreateBranchRequest) (*CreateBranchResponse, error)
	// 获取全部文件
	GetAllFiles(context.Context, *GetAllFilesRequest) (*GetAllFilesResponse, error)
	// 校验分支是否可创建
	CheckBranch(context.Context, *CheckBranchRequest) (*CheckBranchResponse, error)
	// 同步模型
	SyncModel(context.Context, *SyncModelRequest) (*SyncModelResponse, error)
	// 校验模型是否可同步
	CheckModelForSync(context.Context, *CheckModelForSyncRequest) (*CheckModelForSyncResponse, error)
	// 展示同步记录
	ListSyncModelRecord(context.Context, *ListSyncModelRecordRequest) (*ListSyncModelRecordResponse, error)
	// 停止同步
	StopSyncModel(context.Context, *StopSyncModelRequest) (*StopSyncModelResponse, error)
	// 更新预热配置
	UpdateWarmupConfig(context.Context, *UpdateWarmupConfigRequest) (*UpdateWarmupConfigResponse, error)
	// 开始预热
	StartWarmupJob(context.Context, *StartWarmupJobRequest) (*StartWarmupJobResponse, error)
	// 停止预热
	StopWarmupJob(context.Context, *StopWarmupJobRequest) (*StopWarmupJobResponse, error)
	// 获取预热记录列表
	ListWarmupRecords(context.Context, *ListWarmupRecordsRequest) (*ListWarmupRecordsResponse, error)
	// 获取预热模型列表
	ListWarmedModels(context.Context, *ListWarmedModelsRequest) (*ListWarmedModelsResponse, error)
	// Git回调
	GitCallback(context.Context, *GitCallbackRequest) (*GitCallbackResponse, error)
	// 获取预热比例
	GetModelWarmupRatio(context.Context, *GetModelWarmupRatioRequest) (*GetModelWarmupRatioResponse, error)
	// 创建fork
	CreateFork(context.Context, *CreateForkRequest) (*emptypb.Empty, error)
	// 校验模型是否预热过
	CheckModelWarmed(context.Context, *CheckModelWarmedRequest) (*CheckModelWarmedResponse, error)
	mustEmbedUnimplementedModelHubServiceServer()
}

// UnimplementedModelHubServiceServer must be embedded to have forward compatible implementations.
type UnimplementedModelHubServiceServer struct {
}

func (UnimplementedModelHubServiceServer) CreateModel(context.Context, *CreateModelRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateModel not implemented")
}
func (UnimplementedModelHubServiceServer) ListModels(context.Context, *ListModelsOptions) (*ListModelResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListModels not implemented")
}
func (UnimplementedModelHubServiceServer) UpdateModel(context.Context, *UpdateModelRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateModel not implemented")
}
func (UnimplementedModelHubServiceServer) DeleteModel(context.Context, *DeleteModelRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteModel not implemented")
}
func (UnimplementedModelHubServiceServer) ShowModel(context.Context, *ShowModelRequest) (*ShowModelResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ShowModel not implemented")
}
func (UnimplementedModelHubServiceServer) GetGitToken(context.Context, *GetGitTokenRequest) (*GetGitTokenResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetGitToken not implemented")
}
func (UnimplementedModelHubServiceServer) CreateModelHub(context.Context, *CreateModelHubRequest) (*CreateModelHubResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateModelHub not implemented")
}
func (UnimplementedModelHubServiceServer) GetModelHub(context.Context, *GetModelHubRequest) (*GetModelHubResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetModelHub not implemented")
}
func (UnimplementedModelHubServiceServer) GetTree(context.Context, *GetTreeRequest) (*GetTreeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTree not implemented")
}
func (UnimplementedModelHubServiceServer) GetLastCommit(context.Context, *GetLastCommitRequest) (*GetLastCommitResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLastCommit not implemented")
}
func (UnimplementedModelHubServiceServer) GetRepoFileContent(context.Context, *GetRepoFileContentRequest) (*GetRepoFileContentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRepoFileContent not implemented")
}
func (UnimplementedModelHubServiceServer) CreateUser(context.Context, *CreateUserRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateUser not implemented")
}
func (UnimplementedModelHubServiceServer) AddCollaborator(context.Context, *AddCollaboratorRequest) (*AddCollaboratorResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddCollaborator not implemented")
}
func (UnimplementedModelHubServiceServer) DeleteCollaborator(context.Context, *DeleteCollaboratorRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteCollaborator not implemented")
}
func (UnimplementedModelHubServiceServer) ChangeCollaborator(context.Context, *ChangeCollaboratorRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ChangeCollaborator not implemented")
}
func (UnimplementedModelHubServiceServer) ListCollaborator(context.Context, *ListCollaboratorRequest) (*ListCollaboratorResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListCollaborator not implemented")
}
func (UnimplementedModelHubServiceServer) CheckModelIsExist(context.Context, *CheckModelIsExistRequest) (*CheckModelIsExistResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckModelIsExist not implemented")
}
func (UnimplementedModelHubServiceServer) CheckCollaboratorAuth(context.Context, *CheckCollaboratorAuthRequest) (*CheckCollaboratorAuthResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckCollaboratorAuth not implemented")
}
func (UnimplementedModelHubServiceServer) InitModelHubOrg(context.Context, *InitModelHubOrgRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InitModelHubOrg not implemented")
}
func (UnimplementedModelHubServiceServer) GetModelInfo(context.Context, *GetModelInfoRequest) (*GetModelInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetModelInfo not implemented")
}
func (UnimplementedModelHubServiceServer) GetTags(context.Context, *GetTagsRequest) (*GetTagsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTags not implemented")
}
func (UnimplementedModelHubServiceServer) GetBranches(context.Context, *GetBranchesRequest) (*GetBranchesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBranches not implemented")
}
func (UnimplementedModelHubServiceServer) CreateBranch(context.Context, *CreateBranchRequest) (*CreateBranchResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateBranch not implemented")
}
func (UnimplementedModelHubServiceServer) GetAllFiles(context.Context, *GetAllFilesRequest) (*GetAllFilesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAllFiles not implemented")
}
func (UnimplementedModelHubServiceServer) CheckBranch(context.Context, *CheckBranchRequest) (*CheckBranchResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckBranch not implemented")
}
func (UnimplementedModelHubServiceServer) SyncModel(context.Context, *SyncModelRequest) (*SyncModelResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SyncModel not implemented")
}
func (UnimplementedModelHubServiceServer) CheckModelForSync(context.Context, *CheckModelForSyncRequest) (*CheckModelForSyncResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckModelForSync not implemented")
}
func (UnimplementedModelHubServiceServer) ListSyncModelRecord(context.Context, *ListSyncModelRecordRequest) (*ListSyncModelRecordResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListSyncModelRecord not implemented")
}
func (UnimplementedModelHubServiceServer) StopSyncModel(context.Context, *StopSyncModelRequest) (*StopSyncModelResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StopSyncModel not implemented")
}
func (UnimplementedModelHubServiceServer) UpdateWarmupConfig(context.Context, *UpdateWarmupConfigRequest) (*UpdateWarmupConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateWarmupConfig not implemented")
}
func (UnimplementedModelHubServiceServer) StartWarmupJob(context.Context, *StartWarmupJobRequest) (*StartWarmupJobResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StartWarmupJob not implemented")
}
func (UnimplementedModelHubServiceServer) StopWarmupJob(context.Context, *StopWarmupJobRequest) (*StopWarmupJobResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StopWarmupJob not implemented")
}
func (UnimplementedModelHubServiceServer) ListWarmupRecords(context.Context, *ListWarmupRecordsRequest) (*ListWarmupRecordsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListWarmupRecords not implemented")
}
func (UnimplementedModelHubServiceServer) ListWarmedModels(context.Context, *ListWarmedModelsRequest) (*ListWarmedModelsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListWarmedModels not implemented")
}
func (UnimplementedModelHubServiceServer) GitCallback(context.Context, *GitCallbackRequest) (*GitCallbackResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GitCallback not implemented")
}
func (UnimplementedModelHubServiceServer) GetModelWarmupRatio(context.Context, *GetModelWarmupRatioRequest) (*GetModelWarmupRatioResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetModelWarmupRatio not implemented")
}
func (UnimplementedModelHubServiceServer) CreateFork(context.Context, *CreateForkRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateFork not implemented")
}
func (UnimplementedModelHubServiceServer) CheckModelWarmed(context.Context, *CheckModelWarmedRequest) (*CheckModelWarmedResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckModelWarmed not implemented")
}
func (UnimplementedModelHubServiceServer) mustEmbedUnimplementedModelHubServiceServer() {}

// UnsafeModelHubServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ModelHubServiceServer will
// result in compilation errors.
type UnsafeModelHubServiceServer interface {
	mustEmbedUnimplementedModelHubServiceServer()
}

func RegisterModelHubServiceServer(s grpc.ServiceRegistrar, srv ModelHubServiceServer) {
	s.RegisterService(&ModelHubService_ServiceDesc, srv)
}

func _ModelHubService_CreateModel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateModelRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelHubServiceServer).CreateModel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelHubService_CreateModel_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelHubServiceServer).CreateModel(ctx, req.(*CreateModelRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModelHubService_ListModels_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListModelsOptions)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelHubServiceServer).ListModels(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelHubService_ListModels_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelHubServiceServer).ListModels(ctx, req.(*ListModelsOptions))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModelHubService_UpdateModel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateModelRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelHubServiceServer).UpdateModel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelHubService_UpdateModel_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelHubServiceServer).UpdateModel(ctx, req.(*UpdateModelRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModelHubService_DeleteModel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteModelRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelHubServiceServer).DeleteModel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelHubService_DeleteModel_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelHubServiceServer).DeleteModel(ctx, req.(*DeleteModelRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModelHubService_ShowModel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ShowModelRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelHubServiceServer).ShowModel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelHubService_ShowModel_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelHubServiceServer).ShowModel(ctx, req.(*ShowModelRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModelHubService_GetGitToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGitTokenRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelHubServiceServer).GetGitToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelHubService_GetGitToken_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelHubServiceServer).GetGitToken(ctx, req.(*GetGitTokenRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModelHubService_CreateModelHub_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateModelHubRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelHubServiceServer).CreateModelHub(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelHubService_CreateModelHub_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelHubServiceServer).CreateModelHub(ctx, req.(*CreateModelHubRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModelHubService_GetModelHub_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetModelHubRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelHubServiceServer).GetModelHub(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelHubService_GetModelHub_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelHubServiceServer).GetModelHub(ctx, req.(*GetModelHubRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModelHubService_GetTree_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTreeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelHubServiceServer).GetTree(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelHubService_GetTree_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelHubServiceServer).GetTree(ctx, req.(*GetTreeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModelHubService_GetLastCommit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLastCommitRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelHubServiceServer).GetLastCommit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelHubService_GetLastCommit_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelHubServiceServer).GetLastCommit(ctx, req.(*GetLastCommitRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModelHubService_GetRepoFileContent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRepoFileContentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelHubServiceServer).GetRepoFileContent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelHubService_GetRepoFileContent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelHubServiceServer).GetRepoFileContent(ctx, req.(*GetRepoFileContentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModelHubService_CreateUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelHubServiceServer).CreateUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelHubService_CreateUser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelHubServiceServer).CreateUser(ctx, req.(*CreateUserRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModelHubService_AddCollaborator_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddCollaboratorRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelHubServiceServer).AddCollaborator(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelHubService_AddCollaborator_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelHubServiceServer).AddCollaborator(ctx, req.(*AddCollaboratorRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModelHubService_DeleteCollaborator_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteCollaboratorRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelHubServiceServer).DeleteCollaborator(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelHubService_DeleteCollaborator_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelHubServiceServer).DeleteCollaborator(ctx, req.(*DeleteCollaboratorRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModelHubService_ChangeCollaborator_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChangeCollaboratorRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelHubServiceServer).ChangeCollaborator(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelHubService_ChangeCollaborator_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelHubServiceServer).ChangeCollaborator(ctx, req.(*ChangeCollaboratorRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModelHubService_ListCollaborator_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListCollaboratorRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelHubServiceServer).ListCollaborator(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelHubService_ListCollaborator_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelHubServiceServer).ListCollaborator(ctx, req.(*ListCollaboratorRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModelHubService_CheckModelIsExist_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckModelIsExistRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelHubServiceServer).CheckModelIsExist(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelHubService_CheckModelIsExist_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelHubServiceServer).CheckModelIsExist(ctx, req.(*CheckModelIsExistRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModelHubService_CheckCollaboratorAuth_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckCollaboratorAuthRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelHubServiceServer).CheckCollaboratorAuth(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelHubService_CheckCollaboratorAuth_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelHubServiceServer).CheckCollaboratorAuth(ctx, req.(*CheckCollaboratorAuthRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModelHubService_InitModelHubOrg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InitModelHubOrgRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelHubServiceServer).InitModelHubOrg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelHubService_InitModelHubOrg_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelHubServiceServer).InitModelHubOrg(ctx, req.(*InitModelHubOrgRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModelHubService_GetModelInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetModelInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelHubServiceServer).GetModelInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelHubService_GetModelInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelHubServiceServer).GetModelInfo(ctx, req.(*GetModelInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModelHubService_GetTags_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTagsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelHubServiceServer).GetTags(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelHubService_GetTags_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelHubServiceServer).GetTags(ctx, req.(*GetTagsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModelHubService_GetBranches_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBranchesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelHubServiceServer).GetBranches(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelHubService_GetBranches_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelHubServiceServer).GetBranches(ctx, req.(*GetBranchesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModelHubService_CreateBranch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateBranchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelHubServiceServer).CreateBranch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelHubService_CreateBranch_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelHubServiceServer).CreateBranch(ctx, req.(*CreateBranchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModelHubService_GetAllFiles_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllFilesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelHubServiceServer).GetAllFiles(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelHubService_GetAllFiles_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelHubServiceServer).GetAllFiles(ctx, req.(*GetAllFilesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModelHubService_CheckBranch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckBranchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelHubServiceServer).CheckBranch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelHubService_CheckBranch_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelHubServiceServer).CheckBranch(ctx, req.(*CheckBranchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModelHubService_SyncModel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SyncModelRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelHubServiceServer).SyncModel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelHubService_SyncModel_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelHubServiceServer).SyncModel(ctx, req.(*SyncModelRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModelHubService_CheckModelForSync_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckModelForSyncRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelHubServiceServer).CheckModelForSync(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelHubService_CheckModelForSync_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelHubServiceServer).CheckModelForSync(ctx, req.(*CheckModelForSyncRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModelHubService_ListSyncModelRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListSyncModelRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelHubServiceServer).ListSyncModelRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelHubService_ListSyncModelRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelHubServiceServer).ListSyncModelRecord(ctx, req.(*ListSyncModelRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModelHubService_StopSyncModel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StopSyncModelRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelHubServiceServer).StopSyncModel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelHubService_StopSyncModel_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelHubServiceServer).StopSyncModel(ctx, req.(*StopSyncModelRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModelHubService_UpdateWarmupConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateWarmupConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelHubServiceServer).UpdateWarmupConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelHubService_UpdateWarmupConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelHubServiceServer).UpdateWarmupConfig(ctx, req.(*UpdateWarmupConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModelHubService_StartWarmupJob_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StartWarmupJobRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelHubServiceServer).StartWarmupJob(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelHubService_StartWarmupJob_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelHubServiceServer).StartWarmupJob(ctx, req.(*StartWarmupJobRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModelHubService_StopWarmupJob_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StopWarmupJobRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelHubServiceServer).StopWarmupJob(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelHubService_StopWarmupJob_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelHubServiceServer).StopWarmupJob(ctx, req.(*StopWarmupJobRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModelHubService_ListWarmupRecords_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListWarmupRecordsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelHubServiceServer).ListWarmupRecords(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelHubService_ListWarmupRecords_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelHubServiceServer).ListWarmupRecords(ctx, req.(*ListWarmupRecordsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModelHubService_ListWarmedModels_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListWarmedModelsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelHubServiceServer).ListWarmedModels(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelHubService_ListWarmedModels_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelHubServiceServer).ListWarmedModels(ctx, req.(*ListWarmedModelsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModelHubService_GitCallback_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GitCallbackRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelHubServiceServer).GitCallback(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelHubService_GitCallback_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelHubServiceServer).GitCallback(ctx, req.(*GitCallbackRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModelHubService_GetModelWarmupRatio_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetModelWarmupRatioRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelHubServiceServer).GetModelWarmupRatio(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelHubService_GetModelWarmupRatio_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelHubServiceServer).GetModelWarmupRatio(ctx, req.(*GetModelWarmupRatioRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModelHubService_CreateFork_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateForkRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelHubServiceServer).CreateFork(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelHubService_CreateFork_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelHubServiceServer).CreateFork(ctx, req.(*CreateForkRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModelHubService_CheckModelWarmed_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckModelWarmedRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelHubServiceServer).CheckModelWarmed(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelHubService_CheckModelWarmed_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelHubServiceServer).CheckModelWarmed(ctx, req.(*CheckModelWarmedRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// ModelHubService_ServiceDesc is the grpc.ServiceDesc for ModelHubService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ModelHubService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "apis.aistudio.modelhub.v1.ModelHubService",
	HandlerType: (*ModelHubServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateModel",
			Handler:    _ModelHubService_CreateModel_Handler,
		},
		{
			MethodName: "ListModels",
			Handler:    _ModelHubService_ListModels_Handler,
		},
		{
			MethodName: "UpdateModel",
			Handler:    _ModelHubService_UpdateModel_Handler,
		},
		{
			MethodName: "DeleteModel",
			Handler:    _ModelHubService_DeleteModel_Handler,
		},
		{
			MethodName: "ShowModel",
			Handler:    _ModelHubService_ShowModel_Handler,
		},
		{
			MethodName: "GetGitToken",
			Handler:    _ModelHubService_GetGitToken_Handler,
		},
		{
			MethodName: "CreateModelHub",
			Handler:    _ModelHubService_CreateModelHub_Handler,
		},
		{
			MethodName: "GetModelHub",
			Handler:    _ModelHubService_GetModelHub_Handler,
		},
		{
			MethodName: "GetTree",
			Handler:    _ModelHubService_GetTree_Handler,
		},
		{
			MethodName: "GetLastCommit",
			Handler:    _ModelHubService_GetLastCommit_Handler,
		},
		{
			MethodName: "GetRepoFileContent",
			Handler:    _ModelHubService_GetRepoFileContent_Handler,
		},
		{
			MethodName: "CreateUser",
			Handler:    _ModelHubService_CreateUser_Handler,
		},
		{
			MethodName: "AddCollaborator",
			Handler:    _ModelHubService_AddCollaborator_Handler,
		},
		{
			MethodName: "DeleteCollaborator",
			Handler:    _ModelHubService_DeleteCollaborator_Handler,
		},
		{
			MethodName: "ChangeCollaborator",
			Handler:    _ModelHubService_ChangeCollaborator_Handler,
		},
		{
			MethodName: "ListCollaborator",
			Handler:    _ModelHubService_ListCollaborator_Handler,
		},
		{
			MethodName: "CheckModelIsExist",
			Handler:    _ModelHubService_CheckModelIsExist_Handler,
		},
		{
			MethodName: "CheckCollaboratorAuth",
			Handler:    _ModelHubService_CheckCollaboratorAuth_Handler,
		},
		{
			MethodName: "InitModelHubOrg",
			Handler:    _ModelHubService_InitModelHubOrg_Handler,
		},
		{
			MethodName: "GetModelInfo",
			Handler:    _ModelHubService_GetModelInfo_Handler,
		},
		{
			MethodName: "GetTags",
			Handler:    _ModelHubService_GetTags_Handler,
		},
		{
			MethodName: "GetBranches",
			Handler:    _ModelHubService_GetBranches_Handler,
		},
		{
			MethodName: "CreateBranch",
			Handler:    _ModelHubService_CreateBranch_Handler,
		},
		{
			MethodName: "GetAllFiles",
			Handler:    _ModelHubService_GetAllFiles_Handler,
		},
		{
			MethodName: "CheckBranch",
			Handler:    _ModelHubService_CheckBranch_Handler,
		},
		{
			MethodName: "SyncModel",
			Handler:    _ModelHubService_SyncModel_Handler,
		},
		{
			MethodName: "CheckModelForSync",
			Handler:    _ModelHubService_CheckModelForSync_Handler,
		},
		{
			MethodName: "ListSyncModelRecord",
			Handler:    _ModelHubService_ListSyncModelRecord_Handler,
		},
		{
			MethodName: "StopSyncModel",
			Handler:    _ModelHubService_StopSyncModel_Handler,
		},
		{
			MethodName: "UpdateWarmupConfig",
			Handler:    _ModelHubService_UpdateWarmupConfig_Handler,
		},
		{
			MethodName: "StartWarmupJob",
			Handler:    _ModelHubService_StartWarmupJob_Handler,
		},
		{
			MethodName: "StopWarmupJob",
			Handler:    _ModelHubService_StopWarmupJob_Handler,
		},
		{
			MethodName: "ListWarmupRecords",
			Handler:    _ModelHubService_ListWarmupRecords_Handler,
		},
		{
			MethodName: "ListWarmedModels",
			Handler:    _ModelHubService_ListWarmedModels_Handler,
		},
		{
			MethodName: "GitCallback",
			Handler:    _ModelHubService_GitCallback_Handler,
		},
		{
			MethodName: "GetModelWarmupRatio",
			Handler:    _ModelHubService_GetModelWarmupRatio_Handler,
		},
		{
			MethodName: "CreateFork",
			Handler:    _ModelHubService_CreateFork_Handler,
		},
		{
			MethodName: "CheckModelWarmed",
			Handler:    _ModelHubService_CheckModelWarmed_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "aistudio/modelhub/v1/modelhub.proto",
}
