// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v5.29.3
// source: aistudio/modelhub/v1/modelhub.proto

package v1

import (
	_ "git.lianjia.com/cloudnative/kic/kic-platform/apis/common"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	_ "google.golang.org/protobuf/types/known/durationpb"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	_ "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CheckModelWarmedRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName string `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	ModelName     string `protobuf:"bytes,2,opt,name=modelName,proto3" json:"modelName,omitempty"`
}

func (x *CheckModelWarmedRequest) Reset() {
	*x = CheckModelWarmedRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckModelWarmedRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckModelWarmedRequest) ProtoMessage() {}

func (x *CheckModelWarmedRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckModelWarmedRequest.ProtoReflect.Descriptor instead.
func (*CheckModelWarmedRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP(), []int{0}
}

func (x *CheckModelWarmedRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *CheckModelWarmedRequest) GetModelName() string {
	if x != nil {
		return x.ModelName
	}
	return ""
}

type CheckModelWarmedResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status int32  `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"` // 适配 fcn，0 为校验成功， 422 为校验失败
	Errors string `protobuf:"bytes,2,opt,name=errors,proto3" json:"errors,omitempty"`
}

func (x *CheckModelWarmedResponse) Reset() {
	*x = CheckModelWarmedResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckModelWarmedResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckModelWarmedResponse) ProtoMessage() {}

func (x *CheckModelWarmedResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckModelWarmedResponse.ProtoReflect.Descriptor instead.
func (*CheckModelWarmedResponse) Descriptor() ([]byte, []int) {
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP(), []int{1}
}

func (x *CheckModelWarmedResponse) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *CheckModelWarmedResponse) GetErrors() string {
	if x != nil {
		return x.Errors
	}
	return ""
}

type CreateForkRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName     string `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Region            string `protobuf:"bytes,2,opt,name=region,proto3" json:"region,omitempty"`
	Name              string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Nickname          string `protobuf:"bytes,4,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Description       string `protobuf:"bytes,5,opt,name=description,proto3" json:"description,omitempty"`
	FromWorkspaceName string `protobuf:"bytes,6,opt,name=fromWorkspaceName,proto3" json:"fromWorkspaceName,omitempty"`
	FromName          string `protobuf:"bytes,7,opt,name=fromName,proto3" json:"fromName,omitempty"`
}

func (x *CreateForkRequest) Reset() {
	*x = CreateForkRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateForkRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateForkRequest) ProtoMessage() {}

func (x *CreateForkRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateForkRequest.ProtoReflect.Descriptor instead.
func (*CreateForkRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP(), []int{2}
}

func (x *CreateForkRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *CreateForkRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *CreateForkRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateForkRequest) GetNickname() string {
	if x != nil {
		return x.Nickname
	}
	return ""
}

func (x *CreateForkRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CreateForkRequest) GetFromWorkspaceName() string {
	if x != nil {
		return x.FromWorkspaceName
	}
	return ""
}

func (x *CreateForkRequest) GetFromName() string {
	if x != nil {
		return x.FromName
	}
	return ""
}

type SDKModelInfoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName string `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	ModelName     string `protobuf:"bytes,2,opt,name=modelName,proto3" json:"modelName,omitempty"`
	Ref           string `protobuf:"bytes,3,opt,name=ref,proto3" json:"ref,omitempty"`
	Token         string `protobuf:"bytes,4,opt,name=token,proto3" json:"token,omitempty"`
}

func (x *SDKModelInfoRequest) Reset() {
	*x = SDKModelInfoRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SDKModelInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SDKModelInfoRequest) ProtoMessage() {}

func (x *SDKModelInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SDKModelInfoRequest.ProtoReflect.Descriptor instead.
func (*SDKModelInfoRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP(), []int{3}
}

func (x *SDKModelInfoRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *SDKModelInfoRequest) GetModelName() string {
	if x != nil {
		return x.ModelName
	}
	return ""
}

func (x *SDKModelInfoRequest) GetRef() string {
	if x != nil {
		return x.Ref
	}
	return ""
}

func (x *SDKModelInfoRequest) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

type SDKModelInfoResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Sha      string     `protobuf:"bytes,1,opt,name=sha,proto3" json:"sha,omitempty"`
	Siblings []*Sibling `protobuf:"bytes,2,rep,name=siblings,proto3" json:"siblings,omitempty"`
	Id       string     `protobuf:"bytes,3,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *SDKModelInfoResponse) Reset() {
	*x = SDKModelInfoResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SDKModelInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SDKModelInfoResponse) ProtoMessage() {}

func (x *SDKModelInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SDKModelInfoResponse.ProtoReflect.Descriptor instead.
func (*SDKModelInfoResponse) Descriptor() ([]byte, []int) {
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP(), []int{4}
}

func (x *SDKModelInfoResponse) GetSha() string {
	if x != nil {
		return x.Sha
	}
	return ""
}

func (x *SDKModelInfoResponse) GetSiblings() []*Sibling {
	if x != nil {
		return x.Siblings
	}
	return nil
}

func (x *SDKModelInfoResponse) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type Sibling struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Rfilename string `protobuf:"bytes,1,opt,name=rfilename,proto3" json:"rfilename,omitempty"`
}

func (x *Sibling) Reset() {
	*x = Sibling{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Sibling) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Sibling) ProtoMessage() {}

func (x *Sibling) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Sibling.ProtoReflect.Descriptor instead.
func (*Sibling) Descriptor() ([]byte, []int) {
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP(), []int{5}
}

func (x *Sibling) GetRfilename() string {
	if x != nil {
		return x.Rfilename
	}
	return ""
}

type CreateModelRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName string   `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Name          string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Nickname      string   `protobuf:"bytes,3,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Description   string   `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	Private       bool     `protobuf:"varint,5,opt,name=private,proto3" json:"private,omitempty"`
	License       string   `protobuf:"bytes,6,opt,name=license,proto3" json:"license,omitempty"`
	Readme        string   `protobuf:"bytes,7,opt,name=readme,proto3" json:"readme,omitempty"`
	Region        string   `protobuf:"bytes,8,opt,name=region,proto3" json:"region,omitempty"`
	Tags          []string `protobuf:"bytes,9,rep,name=tags,proto3" json:"tags,omitempty"`
	PipelineTag   string   `protobuf:"bytes,10,opt,name=pipelineTag,proto3" json:"pipelineTag,omitempty"`
	LibraryName   string   `protobuf:"bytes,11,opt,name=libraryName,proto3" json:"libraryName,omitempty"`
	Author        string   `protobuf:"bytes,12,opt,name=author,proto3" json:"author,omitempty"`
	LastModified  string   `protobuf:"bytes,13,opt,name=lastModified,proto3" json:"lastModified,omitempty"`
	ModelSource   string   `protobuf:"bytes,15,opt,name=modelSource,proto3" json:"modelSource,omitempty"`
	Downloads     int64    `protobuf:"varint,16,opt,name=downloads,proto3" json:"downloads,omitempty"`
	Likes         int64    `protobuf:"varint,17,opt,name=likes,proto3" json:"likes,omitempty"`
	UsedStorage   int64    `protobuf:"varint,18,opt,name=usedStorage,proto3" json:"usedStorage,omitempty"`
}

func (x *CreateModelRequest) Reset() {
	*x = CreateModelRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateModelRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateModelRequest) ProtoMessage() {}

func (x *CreateModelRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateModelRequest.ProtoReflect.Descriptor instead.
func (*CreateModelRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP(), []int{6}
}

func (x *CreateModelRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *CreateModelRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateModelRequest) GetNickname() string {
	if x != nil {
		return x.Nickname
	}
	return ""
}

func (x *CreateModelRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CreateModelRequest) GetPrivate() bool {
	if x != nil {
		return x.Private
	}
	return false
}

func (x *CreateModelRequest) GetLicense() string {
	if x != nil {
		return x.License
	}
	return ""
}

func (x *CreateModelRequest) GetReadme() string {
	if x != nil {
		return x.Readme
	}
	return ""
}

func (x *CreateModelRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *CreateModelRequest) GetTags() []string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *CreateModelRequest) GetPipelineTag() string {
	if x != nil {
		return x.PipelineTag
	}
	return ""
}

func (x *CreateModelRequest) GetLibraryName() string {
	if x != nil {
		return x.LibraryName
	}
	return ""
}

func (x *CreateModelRequest) GetAuthor() string {
	if x != nil {
		return x.Author
	}
	return ""
}

func (x *CreateModelRequest) GetLastModified() string {
	if x != nil {
		return x.LastModified
	}
	return ""
}

func (x *CreateModelRequest) GetModelSource() string {
	if x != nil {
		return x.ModelSource
	}
	return ""
}

func (x *CreateModelRequest) GetDownloads() int64 {
	if x != nil {
		return x.Downloads
	}
	return 0
}

func (x *CreateModelRequest) GetLikes() int64 {
	if x != nil {
		return x.Likes
	}
	return 0
}

func (x *CreateModelRequest) GetUsedStorage() int64 {
	if x != nil {
		return x.UsedStorage
	}
	return 0
}

type UpdateModelRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName string   `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Name          string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Nickname      string   `protobuf:"bytes,3,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Description   string   `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	Private       bool     `protobuf:"varint,5,opt,name=private,proto3" json:"private,omitempty"`
	License       string   `protobuf:"bytes,6,opt,name=license,proto3" json:"license,omitempty"`
	Readme        string   `protobuf:"bytes,7,opt,name=readme,proto3" json:"readme,omitempty"`
	Region        string   `protobuf:"bytes,8,opt,name=region,proto3" json:"region,omitempty"`
	Tags          []string `protobuf:"bytes,9,rep,name=tags,proto3" json:"tags,omitempty"`
	PipelineTag   string   `protobuf:"bytes,10,opt,name=pipelineTag,proto3" json:"pipelineTag,omitempty"`
	LibraryName   string   `protobuf:"bytes,11,opt,name=libraryName,proto3" json:"libraryName,omitempty"`
	Author        string   `protobuf:"bytes,12,opt,name=author,proto3" json:"author,omitempty"`
	LastModified  string   `protobuf:"bytes,13,opt,name=lastModified,proto3" json:"lastModified,omitempty"`
	ModelSource   string   `protobuf:"bytes,14,opt,name=modelSource,proto3" json:"modelSource,omitempty"`
	Downloads     int64    `protobuf:"varint,15,opt,name=downloads,proto3" json:"downloads,omitempty"`
	Likes         int64    `protobuf:"varint,16,opt,name=likes,proto3" json:"likes,omitempty"`
	UsedStorage   int64    `protobuf:"varint,17,opt,name=usedStorage,proto3" json:"usedStorage,omitempty"`
}

func (x *UpdateModelRequest) Reset() {
	*x = UpdateModelRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateModelRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateModelRequest) ProtoMessage() {}

func (x *UpdateModelRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateModelRequest.ProtoReflect.Descriptor instead.
func (*UpdateModelRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP(), []int{7}
}

func (x *UpdateModelRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *UpdateModelRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UpdateModelRequest) GetNickname() string {
	if x != nil {
		return x.Nickname
	}
	return ""
}

func (x *UpdateModelRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *UpdateModelRequest) GetPrivate() bool {
	if x != nil {
		return x.Private
	}
	return false
}

func (x *UpdateModelRequest) GetLicense() string {
	if x != nil {
		return x.License
	}
	return ""
}

func (x *UpdateModelRequest) GetReadme() string {
	if x != nil {
		return x.Readme
	}
	return ""
}

func (x *UpdateModelRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *UpdateModelRequest) GetTags() []string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *UpdateModelRequest) GetPipelineTag() string {
	if x != nil {
		return x.PipelineTag
	}
	return ""
}

func (x *UpdateModelRequest) GetLibraryName() string {
	if x != nil {
		return x.LibraryName
	}
	return ""
}

func (x *UpdateModelRequest) GetAuthor() string {
	if x != nil {
		return x.Author
	}
	return ""
}

func (x *UpdateModelRequest) GetLastModified() string {
	if x != nil {
		return x.LastModified
	}
	return ""
}

func (x *UpdateModelRequest) GetModelSource() string {
	if x != nil {
		return x.ModelSource
	}
	return ""
}

func (x *UpdateModelRequest) GetDownloads() int64 {
	if x != nil {
		return x.Downloads
	}
	return 0
}

func (x *UpdateModelRequest) GetLikes() int64 {
	if x != nil {
		return x.Likes
	}
	return 0
}

func (x *UpdateModelRequest) GetUsedStorage() int64 {
	if x != nil {
		return x.UsedStorage
	}
	return 0
}

type ListModelsOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 模型名称
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// 模型别名
	Nickname string `protobuf:"bytes,2,opt,name=nickname,proto3" json:"nickname,omitempty"`
	// 工作空间
	WorkspaceName string `protobuf:"bytes,3,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	// 是否来自组织
	FromOrg     bool   `protobuf:"varint,4,opt,name=fromOrg,proto3" json:"fromOrg,omitempty"`
	Creator     string `protobuf:"bytes,5,opt,name=creator,proto3" json:"creator,omitempty"`
	Page        int32  `protobuf:"varint,7,opt,name=page,proto3" json:"page,omitempty"`
	PerPage     int32  `protobuf:"varint,8,opt,name=perPage,proto3" json:"perPage,omitempty"`
	Region      string `protobuf:"bytes,9,opt,name=region,proto3" json:"region,omitempty"`
	PipelineTag string `protobuf:"bytes,10,opt,name=pipelineTag,proto3" json:"pipelineTag,omitempty"`
	LibraryName string `protobuf:"bytes,11,opt,name=libraryName,proto3" json:"libraryName,omitempty"`
}

func (x *ListModelsOptions) Reset() {
	*x = ListModelsOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListModelsOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListModelsOptions) ProtoMessage() {}

func (x *ListModelsOptions) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListModelsOptions.ProtoReflect.Descriptor instead.
func (*ListModelsOptions) Descriptor() ([]byte, []int) {
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP(), []int{8}
}

func (x *ListModelsOptions) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ListModelsOptions) GetNickname() string {
	if x != nil {
		return x.Nickname
	}
	return ""
}

func (x *ListModelsOptions) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *ListModelsOptions) GetFromOrg() bool {
	if x != nil {
		return x.FromOrg
	}
	return false
}

func (x *ListModelsOptions) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *ListModelsOptions) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListModelsOptions) GetPerPage() int32 {
	if x != nil {
		return x.PerPage
	}
	return 0
}

func (x *ListModelsOptions) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *ListModelsOptions) GetPipelineTag() string {
	if x != nil {
		return x.PipelineTag
	}
	return ""
}

func (x *ListModelsOptions) GetLibraryName() string {
	if x != nil {
		return x.LibraryName
	}
	return ""
}

type ListModelResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Count int32    `protobuf:"varint,1,opt,name=count,proto3" json:"count,omitempty"`
	Items []*Model `protobuf:"bytes,2,rep,name=items,proto3" json:"items,omitempty"`
}

func (x *ListModelResponse) Reset() {
	*x = ListModelResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListModelResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListModelResponse) ProtoMessage() {}

func (x *ListModelResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListModelResponse.ProtoReflect.Descriptor instead.
func (*ListModelResponse) Descriptor() ([]byte, []int) {
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP(), []int{9}
}

func (x *ListModelResponse) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *ListModelResponse) GetItems() []*Model {
	if x != nil {
		return x.Items
	}
	return nil
}

type Model struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ID                 int32           `protobuf:"varint,1,opt,name=ID,proto3" json:"ID,omitempty"`
	Name               string          `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Nickname           string          `protobuf:"bytes,3,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Description        string          `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	Private            bool            `protobuf:"varint,5,opt,name=private,proto3" json:"private,omitempty"`
	Creator            string          `protobuf:"bytes,6,opt,name=creator,proto3" json:"creator,omitempty"`
	CreatedAt          string          `protobuf:"bytes,7,opt,name=createdAt,proto3" json:"createdAt,omitempty"`
	UpdatedAt          string          `protobuf:"bytes,8,opt,name=updatedAt,proto3" json:"updatedAt,omitempty"`
	Path               string          `protobuf:"bytes,9,opt,name=path,proto3" json:"path,omitempty"`
	ModelWorkspaceName string          `protobuf:"bytes,10,opt,name=modelWorkspaceName,proto3" json:"modelWorkspaceName,omitempty"`
	Repository         *Repository     `protobuf:"bytes,11,opt,name=repository,proto3" json:"repository,omitempty"`
	AuthCreateBranch   bool            `protobuf:"varint,12,opt,name=authCreateBranch,proto3" json:"authCreateBranch,omitempty"`
	AuthCollaborator   bool            `protobuf:"varint,13,opt,name=authCollaborator,proto3" json:"authCollaborator,omitempty"`
	AuthUpdate         bool            `protobuf:"varint,14,opt,name=authUpdate,proto3" json:"authUpdate,omitempty"`
	AuthDelete         bool            `protobuf:"varint,15,opt,name=authDelete,proto3" json:"authDelete,omitempty"`
	AuthWarmUp         bool            `protobuf:"varint,16,opt,name=authWarmUp,proto3" json:"authWarmUp,omitempty"`
	Tags               []string        `protobuf:"bytes,17,rep,name=tags,proto3" json:"tags,omitempty"`
	PipelineTag        string          `protobuf:"bytes,18,opt,name=pipelineTag,proto3" json:"pipelineTag,omitempty"`
	LibraryName        string          `protobuf:"bytes,19,opt,name=libraryName,proto3" json:"libraryName,omitempty"`
	Author             string          `protobuf:"bytes,20,opt,name=author,proto3" json:"author,omitempty"`
	LastModified       string          `protobuf:"bytes,21,opt,name=lastModified,proto3" json:"lastModified,omitempty"`
	ModelSource        string          `protobuf:"bytes,22,opt,name=modelSource,proto3" json:"modelSource,omitempty"`
	ModelURL           string          `protobuf:"bytes,23,opt,name=modelURL,proto3" json:"modelURL,omitempty"`
	Downloads          int64           `protobuf:"varint,24,opt,name=downloads,proto3" json:"downloads,omitempty"`
	Likes              int64           `protobuf:"varint,25,opt,name=likes,proto3" json:"likes,omitempty"`
	UsedStorage        int64           `protobuf:"varint,26,opt,name=usedStorage,proto3" json:"usedStorage,omitempty"`
	IsNew              bool            `protobuf:"varint,27,opt,name=isNew,proto3" json:"isNew,omitempty"`
	WarmupConfig       *WarmupConfig   `protobuf:"bytes,28,opt,name=warmupConfig,proto3" json:"warmupConfig,omitempty"`
	WarmedRegions      []*WarmupRegion `protobuf:"bytes,29,rep,name=warmedRegions,proto3" json:"warmedRegions,omitempty"`
}

func (x *Model) Reset() {
	*x = Model{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Model) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Model) ProtoMessage() {}

func (x *Model) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Model.ProtoReflect.Descriptor instead.
func (*Model) Descriptor() ([]byte, []int) {
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP(), []int{10}
}

func (x *Model) GetID() int32 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *Model) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Model) GetNickname() string {
	if x != nil {
		return x.Nickname
	}
	return ""
}

func (x *Model) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Model) GetPrivate() bool {
	if x != nil {
		return x.Private
	}
	return false
}

func (x *Model) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *Model) GetCreatedAt() string {
	if x != nil {
		return x.CreatedAt
	}
	return ""
}

func (x *Model) GetUpdatedAt() string {
	if x != nil {
		return x.UpdatedAt
	}
	return ""
}

func (x *Model) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *Model) GetModelWorkspaceName() string {
	if x != nil {
		return x.ModelWorkspaceName
	}
	return ""
}

func (x *Model) GetRepository() *Repository {
	if x != nil {
		return x.Repository
	}
	return nil
}

func (x *Model) GetAuthCreateBranch() bool {
	if x != nil {
		return x.AuthCreateBranch
	}
	return false
}

func (x *Model) GetAuthCollaborator() bool {
	if x != nil {
		return x.AuthCollaborator
	}
	return false
}

func (x *Model) GetAuthUpdate() bool {
	if x != nil {
		return x.AuthUpdate
	}
	return false
}

func (x *Model) GetAuthDelete() bool {
	if x != nil {
		return x.AuthDelete
	}
	return false
}

func (x *Model) GetAuthWarmUp() bool {
	if x != nil {
		return x.AuthWarmUp
	}
	return false
}

func (x *Model) GetTags() []string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *Model) GetPipelineTag() string {
	if x != nil {
		return x.PipelineTag
	}
	return ""
}

func (x *Model) GetLibraryName() string {
	if x != nil {
		return x.LibraryName
	}
	return ""
}

func (x *Model) GetAuthor() string {
	if x != nil {
		return x.Author
	}
	return ""
}

func (x *Model) GetLastModified() string {
	if x != nil {
		return x.LastModified
	}
	return ""
}

func (x *Model) GetModelSource() string {
	if x != nil {
		return x.ModelSource
	}
	return ""
}

func (x *Model) GetModelURL() string {
	if x != nil {
		return x.ModelURL
	}
	return ""
}

func (x *Model) GetDownloads() int64 {
	if x != nil {
		return x.Downloads
	}
	return 0
}

func (x *Model) GetLikes() int64 {
	if x != nil {
		return x.Likes
	}
	return 0
}

func (x *Model) GetUsedStorage() int64 {
	if x != nil {
		return x.UsedStorage
	}
	return 0
}

func (x *Model) GetIsNew() bool {
	if x != nil {
		return x.IsNew
	}
	return false
}

func (x *Model) GetWarmupConfig() *WarmupConfig {
	if x != nil {
		return x.WarmupConfig
	}
	return nil
}

func (x *Model) GetWarmedRegions() []*WarmupRegion {
	if x != nil {
		return x.WarmedRegions
	}
	return nil
}

type Repository struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HTTPCloneURL          string `protobuf:"bytes,1,opt,name=HTTPCloneURL,proto3" json:"HTTPCloneURL,omitempty"`
	HTTPCloneURLWithToken string `protobuf:"bytes,2,opt,name=HTTPCloneURLWithToken,proto3" json:"HTTPCloneURLWithToken,omitempty"`
	SSHCloneURL           string `protobuf:"bytes,3,opt,name=SSHCloneURL,proto3" json:"SSHCloneURL,omitempty"`
}

func (x *Repository) Reset() {
	*x = Repository{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Repository) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Repository) ProtoMessage() {}

func (x *Repository) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Repository.ProtoReflect.Descriptor instead.
func (*Repository) Descriptor() ([]byte, []int) {
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP(), []int{11}
}

func (x *Repository) GetHTTPCloneURL() string {
	if x != nil {
		return x.HTTPCloneURL
	}
	return ""
}

func (x *Repository) GetHTTPCloneURLWithToken() string {
	if x != nil {
		return x.HTTPCloneURLWithToken
	}
	return ""
}

func (x *Repository) GetSSHCloneURL() string {
	if x != nil {
		return x.SSHCloneURL
	}
	return ""
}

type DeleteModelRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName string `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Name          string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Region        string `protobuf:"bytes,3,opt,name=region,proto3" json:"region,omitempty"`
}

func (x *DeleteModelRequest) Reset() {
	*x = DeleteModelRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteModelRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteModelRequest) ProtoMessage() {}

func (x *DeleteModelRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteModelRequest.ProtoReflect.Descriptor instead.
func (*DeleteModelRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP(), []int{12}
}

func (x *DeleteModelRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *DeleteModelRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DeleteModelRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

type ShowModelRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName string `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Name          string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Region        string `protobuf:"bytes,3,opt,name=region,proto3" json:"region,omitempty"`
}

func (x *ShowModelRequest) Reset() {
	*x = ShowModelRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ShowModelRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ShowModelRequest) ProtoMessage() {}

func (x *ShowModelRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ShowModelRequest.ProtoReflect.Descriptor instead.
func (*ShowModelRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP(), []int{13}
}

func (x *ShowModelRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *ShowModelRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ShowModelRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

type ShowModelResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Model    *Model `protobuf:"bytes,1,opt,name=model,proto3" json:"model,omitempty"`
	UserName string `protobuf:"bytes,2,opt,name=userName,proto3" json:"userName,omitempty"`
}

func (x *ShowModelResponse) Reset() {
	*x = ShowModelResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ShowModelResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ShowModelResponse) ProtoMessage() {}

func (x *ShowModelResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ShowModelResponse.ProtoReflect.Descriptor instead.
func (*ShowModelResponse) Descriptor() ([]byte, []int) {
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP(), []int{14}
}

func (x *ShowModelResponse) GetModel() *Model {
	if x != nil {
		return x.Model
	}
	return nil
}

func (x *ShowModelResponse) GetUserName() string {
	if x != nil {
		return x.UserName
	}
	return ""
}

type GetGitTokenRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	App    string `protobuf:"bytes,1,opt,name=app,proto3" json:"app,omitempty"`
	Region string `protobuf:"bytes,2,opt,name=region,proto3" json:"region,omitempty"`
}

func (x *GetGitTokenRequest) Reset() {
	*x = GetGitTokenRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetGitTokenRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGitTokenRequest) ProtoMessage() {}

func (x *GetGitTokenRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGitTokenRequest.ProtoReflect.Descriptor instead.
func (*GetGitTokenRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP(), []int{15}
}

func (x *GetGitTokenRequest) GetApp() string {
	if x != nil {
		return x.App
	}
	return ""
}

func (x *GetGitTokenRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

type GetGitTokenResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Msg      string    `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg,omitempty"`
	GitToken *GitToken `protobuf:"bytes,2,opt,name=gitToken,proto3" json:"gitToken,omitempty"`
}

func (x *GetGitTokenResponse) Reset() {
	*x = GetGitTokenResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetGitTokenResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGitTokenResponse) ProtoMessage() {}

func (x *GetGitTokenResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGitTokenResponse.ProtoReflect.Descriptor instead.
func (*GetGitTokenResponse) Descriptor() ([]byte, []int) {
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP(), []int{16}
}

func (x *GetGitTokenResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *GetGitTokenResponse) GetGitToken() *GitToken {
	if x != nil {
		return x.GitToken
	}
	return nil
}

type GitToken struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name     string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Token    string `protobuf:"bytes,2,opt,name=token,proto3" json:"token,omitempty"`
	UserName string `protobuf:"bytes,3,opt,name=userName,proto3" json:"userName,omitempty"`
}

func (x *GitToken) Reset() {
	*x = GitToken{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GitToken) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GitToken) ProtoMessage() {}

func (x *GitToken) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GitToken.ProtoReflect.Descriptor instead.
func (*GitToken) Descriptor() ([]byte, []int) {
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP(), []int{17}
}

func (x *GitToken) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GitToken) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *GitToken) GetUserName() string {
	if x != nil {
		return x.UserName
	}
	return ""
}

type CreateUserRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserName string `protobuf:"bytes,1,opt,name=userName,proto3" json:"userName,omitempty"`
	Region   string `protobuf:"bytes,2,opt,name=region,proto3" json:"region,omitempty"`
}

func (x *CreateUserRequest) Reset() {
	*x = CreateUserRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateUserRequest) ProtoMessage() {}

func (x *CreateUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateUserRequest.ProtoReflect.Descriptor instead.
func (*CreateUserRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP(), []int{18}
}

func (x *CreateUserRequest) GetUserName() string {
	if x != nil {
		return x.UserName
	}
	return ""
}

func (x *CreateUserRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

type GetTreeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName string `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Name          string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Path          string `protobuf:"bytes,3,opt,name=path,proto3" json:"path,omitempty"`
	Ref           string `protobuf:"bytes,4,opt,name=ref,proto3" json:"ref,omitempty"`
	Region        string `protobuf:"bytes,5,opt,name=region,proto3" json:"region,omitempty"`
	RepoType      string `protobuf:"bytes,6,opt,name=repoType,proto3" json:"repoType,omitempty"`
}

func (x *GetTreeRequest) Reset() {
	*x = GetTreeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTreeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTreeRequest) ProtoMessage() {}

func (x *GetTreeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTreeRequest.ProtoReflect.Descriptor instead.
func (*GetTreeRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP(), []int{19}
}

func (x *GetTreeRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *GetTreeRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GetTreeRequest) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *GetTreeRequest) GetRef() string {
	if x != nil {
		return x.Ref
	}
	return ""
}

func (x *GetTreeRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *GetTreeRequest) GetRepoType() string {
	if x != nil {
		return x.RepoType
	}
	return ""
}

type GetTreeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Msg   string  `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg,omitempty"`
	Files []*File `protobuf:"bytes,2,rep,name=files,proto3" json:"files,omitempty"`
}

func (x *GetTreeResponse) Reset() {
	*x = GetTreeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTreeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTreeResponse) ProtoMessage() {}

func (x *GetTreeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTreeResponse.ProtoReflect.Descriptor instead.
func (*GetTreeResponse) Descriptor() ([]byte, []int) {
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP(), []int{20}
}

func (x *GetTreeResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *GetTreeResponse) GetFiles() []*File {
	if x != nil {
		return x.Files
	}
	return nil
}

type File struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name            string  `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Type            string  `protobuf:"bytes,2,opt,name=type,proto3" json:"type,omitempty"`
	Size            int64   `protobuf:"fixed64,3,opt,name=size,proto3" json:"size,omitempty"`
	Path            string  `protobuf:"bytes,4,opt,name=path,proto3" json:"path,omitempty"`
	Mode            string  `protobuf:"bytes,5,opt,name=mode,proto3" json:"mode,omitempty"`
	Url             string  `protobuf:"bytes,6,opt,name=url,proto3" json:"url,omitempty"`
	Content         string  `protobuf:"bytes,7,opt,name=content,proto3" json:"content,omitempty"`
	Lfs             bool    `protobuf:"varint,8,opt,name=lfs,proto3" json:"lfs,omitempty"`
	LfsPointerSize  int64   `protobuf:"fixed64,9,opt,name=lfsPointerSize,proto3" json:"lfsPointerSize,omitempty"`
	LfsRelativePath string  `protobuf:"bytes,10,opt,name=lfsRelativePath,proto3" json:"lfsRelativePath,omitempty"`
	Commit          *Commit `protobuf:"bytes,11,opt,name=commit,proto3" json:"commit,omitempty"`
}

func (x *File) Reset() {
	*x = File{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *File) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*File) ProtoMessage() {}

func (x *File) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use File.ProtoReflect.Descriptor instead.
func (*File) Descriptor() ([]byte, []int) {
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP(), []int{21}
}

func (x *File) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *File) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *File) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *File) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *File) GetMode() string {
	if x != nil {
		return x.Mode
	}
	return ""
}

func (x *File) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *File) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *File) GetLfs() bool {
	if x != nil {
		return x.Lfs
	}
	return false
}

func (x *File) GetLfsPointerSize() int64 {
	if x != nil {
		return x.LfsPointerSize
	}
	return 0
}

func (x *File) GetLfsRelativePath() string {
	if x != nil {
		return x.LfsRelativePath
	}
	return ""
}

func (x *File) GetCommit() *Commit {
	if x != nil {
		return x.Commit
	}
	return nil
}

type Commit struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CommitterName  string `protobuf:"bytes,1,opt,name=committerName,proto3" json:"committerName,omitempty"`
	CommitterEmail string `protobuf:"bytes,2,opt,name=committerEmail,proto3" json:"committerEmail,omitempty"`
	CommitterDate  string `protobuf:"bytes,3,opt,name=committerDate,proto3" json:"committerDate,omitempty"`
	Message        string `protobuf:"bytes,4,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *Commit) Reset() {
	*x = Commit{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Commit) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Commit) ProtoMessage() {}

func (x *Commit) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Commit.ProtoReflect.Descriptor instead.
func (*Commit) Descriptor() ([]byte, []int) {
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP(), []int{22}
}

func (x *Commit) GetCommitterName() string {
	if x != nil {
		return x.CommitterName
	}
	return ""
}

func (x *Commit) GetCommitterEmail() string {
	if x != nil {
		return x.CommitterEmail
	}
	return ""
}

func (x *Commit) GetCommitterDate() string {
	if x != nil {
		return x.CommitterDate
	}
	return ""
}

func (x *Commit) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type GetLastCommitRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName string `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Name          string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Ref           string `protobuf:"bytes,4,opt,name=ref,proto3" json:"ref,omitempty"`
	Region        string `protobuf:"bytes,5,opt,name=region,proto3" json:"region,omitempty"`
}

func (x *GetLastCommitRequest) Reset() {
	*x = GetLastCommitRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLastCommitRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLastCommitRequest) ProtoMessage() {}

func (x *GetLastCommitRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLastCommitRequest.ProtoReflect.Descriptor instead.
func (*GetLastCommitRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP(), []int{23}
}

func (x *GetLastCommitRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *GetLastCommitRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GetLastCommitRequest) GetRef() string {
	if x != nil {
		return x.Ref
	}
	return ""
}

func (x *GetLastCommitRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

type GetLastCommitResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Msg    string  `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg,omitempty"`
	Commit *Commit `protobuf:"bytes,2,opt,name=commit,proto3" json:"commit,omitempty"`
}

func (x *GetLastCommitResponse) Reset() {
	*x = GetLastCommitResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLastCommitResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLastCommitResponse) ProtoMessage() {}

func (x *GetLastCommitResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLastCommitResponse.ProtoReflect.Descriptor instead.
func (*GetLastCommitResponse) Descriptor() ([]byte, []int) {
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP(), []int{24}
}

func (x *GetLastCommitResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *GetLastCommitResponse) GetCommit() *Commit {
	if x != nil {
		return x.Commit
	}
	return nil
}

type GetRepoFileContentRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName string `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Name          string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Path          string `protobuf:"bytes,3,opt,name=path,proto3" json:"path,omitempty"`
	Ref           string `protobuf:"bytes,4,opt,name=ref,proto3" json:"ref,omitempty"`
	Region        string `protobuf:"bytes,5,opt,name=region,proto3" json:"region,omitempty"`
	RepoType      string `protobuf:"bytes,6,opt,name=repoType,proto3" json:"repoType,omitempty"`
}

func (x *GetRepoFileContentRequest) Reset() {
	*x = GetRepoFileContentRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRepoFileContentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRepoFileContentRequest) ProtoMessage() {}

func (x *GetRepoFileContentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRepoFileContentRequest.ProtoReflect.Descriptor instead.
func (*GetRepoFileContentRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP(), []int{25}
}

func (x *GetRepoFileContentRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *GetRepoFileContentRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GetRepoFileContentRequest) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *GetRepoFileContentRequest) GetRef() string {
	if x != nil {
		return x.Ref
	}
	return ""
}

func (x *GetRepoFileContentRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *GetRepoFileContentRequest) GetRepoType() string {
	if x != nil {
		return x.RepoType
	}
	return ""
}

type GetRepoFileContentResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Msg     string `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg,omitempty"`
	Content string `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`
}

func (x *GetRepoFileContentResponse) Reset() {
	*x = GetRepoFileContentResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRepoFileContentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRepoFileContentResponse) ProtoMessage() {}

func (x *GetRepoFileContentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRepoFileContentResponse.ProtoReflect.Descriptor instead.
func (*GetRepoFileContentResponse) Descriptor() ([]byte, []int) {
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP(), []int{26}
}

func (x *GetRepoFileContentResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *GetRepoFileContentResponse) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

type DownloadFileRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName string `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Name          string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Path          string `protobuf:"bytes,3,opt,name=path,proto3" json:"path,omitempty"`
	Ref           string `protobuf:"bytes,4,opt,name=ref,proto3" json:"ref,omitempty"`
	Lfs           bool   `protobuf:"varint,5,opt,name=lfs,proto3" json:"lfs,omitempty"`
	Region        string `protobuf:"bytes,6,opt,name=region,proto3" json:"region,omitempty"`
}

func (x *DownloadFileRequest) Reset() {
	*x = DownloadFileRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DownloadFileRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DownloadFileRequest) ProtoMessage() {}

func (x *DownloadFileRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DownloadFileRequest.ProtoReflect.Descriptor instead.
func (*DownloadFileRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP(), []int{27}
}

func (x *DownloadFileRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *DownloadFileRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DownloadFileRequest) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *DownloadFileRequest) GetRef() string {
	if x != nil {
		return x.Ref
	}
	return ""
}

func (x *DownloadFileRequest) GetLfs() bool {
	if x != nil {
		return x.Lfs
	}
	return false
}

func (x *DownloadFileRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

type DownloadFileResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Msg  string `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg,omitempty"`
	Url  string `protobuf:"bytes,2,opt,name=url,proto3" json:"url,omitempty"`
	Data []byte `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *DownloadFileResponse) Reset() {
	*x = DownloadFileResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DownloadFileResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DownloadFileResponse) ProtoMessage() {}

func (x *DownloadFileResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DownloadFileResponse.ProtoReflect.Descriptor instead.
func (*DownloadFileResponse) Descriptor() ([]byte, []int) {
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP(), []int{28}
}

func (x *DownloadFileResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *DownloadFileResponse) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *DownloadFileResponse) GetData() []byte {
	if x != nil {
		return x.Data
	}
	return nil
}

type CreateModelHubRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName string `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Region        string `protobuf:"bytes,2,opt,name=region,proto3" json:"region,omitempty"`
}

func (x *CreateModelHubRequest) Reset() {
	*x = CreateModelHubRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateModelHubRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateModelHubRequest) ProtoMessage() {}

func (x *CreateModelHubRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateModelHubRequest.ProtoReflect.Descriptor instead.
func (*CreateModelHubRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP(), []int{29}
}

func (x *CreateModelHubRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *CreateModelHubRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

type CreateModelHubResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Msg string `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg,omitempty"`
}

func (x *CreateModelHubResponse) Reset() {
	*x = CreateModelHubResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateModelHubResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateModelHubResponse) ProtoMessage() {}

func (x *CreateModelHubResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateModelHubResponse.ProtoReflect.Descriptor instead.
func (*CreateModelHubResponse) Descriptor() ([]byte, []int) {
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP(), []int{30}
}

func (x *CreateModelHubResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type GetModelHubRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName string `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Region        string `protobuf:"bytes,2,opt,name=region,proto3" json:"region,omitempty"`
}

func (x *GetModelHubRequest) Reset() {
	*x = GetModelHubRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetModelHubRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetModelHubRequest) ProtoMessage() {}

func (x *GetModelHubRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetModelHubRequest.ProtoReflect.Descriptor instead.
func (*GetModelHubRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP(), []int{31}
}

func (x *GetModelHubRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *GetModelHubRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

type GetModelHubResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Msg  string `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg,omitempty"`
	Open bool   `protobuf:"varint,2,opt,name=open,proto3" json:"open,omitempty"`
}

func (x *GetModelHubResponse) Reset() {
	*x = GetModelHubResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetModelHubResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetModelHubResponse) ProtoMessage() {}

func (x *GetModelHubResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetModelHubResponse.ProtoReflect.Descriptor instead.
func (*GetModelHubResponse) Descriptor() ([]byte, []int) {
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP(), []int{32}
}

func (x *GetModelHubResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *GetModelHubResponse) GetOpen() bool {
	if x != nil {
		return x.Open
	}
	return false
}

type GetModelHubUserRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Username string `protobuf:"bytes,1,opt,name=username,proto3" json:"username,omitempty"`
	Region   string `protobuf:"bytes,2,opt,name=region,proto3" json:"region,omitempty"`
}

func (x *GetModelHubUserRequest) Reset() {
	*x = GetModelHubUserRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetModelHubUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetModelHubUserRequest) ProtoMessage() {}

func (x *GetModelHubUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetModelHubUserRequest.ProtoReflect.Descriptor instead.
func (*GetModelHubUserRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP(), []int{33}
}

func (x *GetModelHubUserRequest) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *GetModelHubUserRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

type GetModelHubUserResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetModelHubUserResponse) Reset() {
	*x = GetModelHubUserResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetModelHubUserResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetModelHubUserResponse) ProtoMessage() {}

func (x *GetModelHubUserResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetModelHubUserResponse.ProtoReflect.Descriptor instead.
func (*GetModelHubUserResponse) Descriptor() ([]byte, []int) {
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP(), []int{34}
}

type AddCollaboratorRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName string `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Repo          string `protobuf:"bytes,2,opt,name=repo,proto3" json:"repo,omitempty"`
	Collaborator  string `protobuf:"bytes,3,opt,name=collaborator,proto3" json:"collaborator,omitempty"`
	AccessMode    string `protobuf:"bytes,4,opt,name=accessMode,proto3" json:"accessMode,omitempty"`
	Region        string `protobuf:"bytes,5,opt,name=region,proto3" json:"region,omitempty"`
	RepoType      string `protobuf:"bytes,6,opt,name=repoType,proto3" json:"repoType,omitempty"`
}

func (x *AddCollaboratorRequest) Reset() {
	*x = AddCollaboratorRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddCollaboratorRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddCollaboratorRequest) ProtoMessage() {}

func (x *AddCollaboratorRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddCollaboratorRequest.ProtoReflect.Descriptor instead.
func (*AddCollaboratorRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP(), []int{35}
}

func (x *AddCollaboratorRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *AddCollaboratorRequest) GetRepo() string {
	if x != nil {
		return x.Repo
	}
	return ""
}

func (x *AddCollaboratorRequest) GetCollaborator() string {
	if x != nil {
		return x.Collaborator
	}
	return ""
}

func (x *AddCollaboratorRequest) GetAccessMode() string {
	if x != nil {
		return x.AccessMode
	}
	return ""
}

func (x *AddCollaboratorRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *AddCollaboratorRequest) GetRepoType() string {
	if x != nil {
		return x.RepoType
	}
	return ""
}

type AddCollaboratorResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status int32  `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	Msg    string `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
}

func (x *AddCollaboratorResponse) Reset() {
	*x = AddCollaboratorResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddCollaboratorResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddCollaboratorResponse) ProtoMessage() {}

func (x *AddCollaboratorResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddCollaboratorResponse.ProtoReflect.Descriptor instead.
func (*AddCollaboratorResponse) Descriptor() ([]byte, []int) {
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP(), []int{36}
}

func (x *AddCollaboratorResponse) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *AddCollaboratorResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type DeleteCollaboratorRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName string `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Repo          string `protobuf:"bytes,2,opt,name=repo,proto3" json:"repo,omitempty"`
	Collaborator  string `protobuf:"bytes,3,opt,name=collaborator,proto3" json:"collaborator,omitempty"`
	Region        string `protobuf:"bytes,4,opt,name=region,proto3" json:"region,omitempty"`
	RepoType      string `protobuf:"bytes,5,opt,name=repoType,proto3" json:"repoType,omitempty"`
}

func (x *DeleteCollaboratorRequest) Reset() {
	*x = DeleteCollaboratorRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteCollaboratorRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteCollaboratorRequest) ProtoMessage() {}

func (x *DeleteCollaboratorRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteCollaboratorRequest.ProtoReflect.Descriptor instead.
func (*DeleteCollaboratorRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP(), []int{37}
}

func (x *DeleteCollaboratorRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *DeleteCollaboratorRequest) GetRepo() string {
	if x != nil {
		return x.Repo
	}
	return ""
}

func (x *DeleteCollaboratorRequest) GetCollaborator() string {
	if x != nil {
		return x.Collaborator
	}
	return ""
}

func (x *DeleteCollaboratorRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *DeleteCollaboratorRequest) GetRepoType() string {
	if x != nil {
		return x.RepoType
	}
	return ""
}

type ChangeCollaboratorRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName string `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Repo          string `protobuf:"bytes,2,opt,name=repo,proto3" json:"repo,omitempty"`
	Collaborator  string `protobuf:"bytes,3,opt,name=collaborator,proto3" json:"collaborator,omitempty"`
	AccessMode    string `protobuf:"bytes,4,opt,name=accessMode,proto3" json:"accessMode,omitempty"`
	Region        string `protobuf:"bytes,5,opt,name=region,proto3" json:"region,omitempty"`
	RepoType      string `protobuf:"bytes,6,opt,name=repoType,proto3" json:"repoType,omitempty"`
}

func (x *ChangeCollaboratorRequest) Reset() {
	*x = ChangeCollaboratorRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChangeCollaboratorRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChangeCollaboratorRequest) ProtoMessage() {}

func (x *ChangeCollaboratorRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChangeCollaboratorRequest.ProtoReflect.Descriptor instead.
func (*ChangeCollaboratorRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP(), []int{38}
}

func (x *ChangeCollaboratorRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *ChangeCollaboratorRequest) GetRepo() string {
	if x != nil {
		return x.Repo
	}
	return ""
}

func (x *ChangeCollaboratorRequest) GetCollaborator() string {
	if x != nil {
		return x.Collaborator
	}
	return ""
}

func (x *ChangeCollaboratorRequest) GetAccessMode() string {
	if x != nil {
		return x.AccessMode
	}
	return ""
}

func (x *ChangeCollaboratorRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *ChangeCollaboratorRequest) GetRepoType() string {
	if x != nil {
		return x.RepoType
	}
	return ""
}

type ListCollaboratorRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName string `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Repo          string `protobuf:"bytes,2,opt,name=repo,proto3" json:"repo,omitempty"`
	Collaborator  string `protobuf:"bytes,3,opt,name=collaborator,proto3" json:"collaborator,omitempty"`
	Page          int32  `protobuf:"varint,4,opt,name=page,proto3" json:"page,omitempty"`
	PerPage       int32  `protobuf:"varint,5,opt,name=perPage,proto3" json:"perPage,omitempty"`
	Region        string `protobuf:"bytes,6,opt,name=region,proto3" json:"region,omitempty"`
	RepoType      string `protobuf:"bytes,7,opt,name=repoType,proto3" json:"repoType,omitempty"`
}

func (x *ListCollaboratorRequest) Reset() {
	*x = ListCollaboratorRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListCollaboratorRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCollaboratorRequest) ProtoMessage() {}

func (x *ListCollaboratorRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCollaboratorRequest.ProtoReflect.Descriptor instead.
func (*ListCollaboratorRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP(), []int{39}
}

func (x *ListCollaboratorRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *ListCollaboratorRequest) GetRepo() string {
	if x != nil {
		return x.Repo
	}
	return ""
}

func (x *ListCollaboratorRequest) GetCollaborator() string {
	if x != nil {
		return x.Collaborator
	}
	return ""
}

func (x *ListCollaboratorRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListCollaboratorRequest) GetPerPage() int32 {
	if x != nil {
		return x.PerPage
	}
	return 0
}

func (x *ListCollaboratorRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *ListCollaboratorRequest) GetRepoType() string {
	if x != nil {
		return x.RepoType
	}
	return ""
}

type ListCollaboratorResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items []*Collaborator `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	Count int32           `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
}

func (x *ListCollaboratorResponse) Reset() {
	*x = ListCollaboratorResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListCollaboratorResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCollaboratorResponse) ProtoMessage() {}

func (x *ListCollaboratorResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCollaboratorResponse.ProtoReflect.Descriptor instead.
func (*ListCollaboratorResponse) Descriptor() ([]byte, []int) {
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP(), []int{40}
}

func (x *ListCollaboratorResponse) GetItems() []*Collaborator {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *ListCollaboratorResponse) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

type Collaborator struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Owner        string `protobuf:"bytes,1,opt,name=owner,proto3" json:"owner,omitempty"`
	Repo         string `protobuf:"bytes,2,opt,name=repo,proto3" json:"repo,omitempty"`
	Collaborator string `protobuf:"bytes,3,opt,name=collaborator,proto3" json:"collaborator,omitempty"`
	AccessMode   string `protobuf:"bytes,4,opt,name=accessMode,proto3" json:"accessMode,omitempty"`
}

func (x *Collaborator) Reset() {
	*x = Collaborator{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Collaborator) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Collaborator) ProtoMessage() {}

func (x *Collaborator) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Collaborator.ProtoReflect.Descriptor instead.
func (*Collaborator) Descriptor() ([]byte, []int) {
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP(), []int{41}
}

func (x *Collaborator) GetOwner() string {
	if x != nil {
		return x.Owner
	}
	return ""
}

func (x *Collaborator) GetRepo() string {
	if x != nil {
		return x.Repo
	}
	return ""
}

func (x *Collaborator) GetCollaborator() string {
	if x != nil {
		return x.Collaborator
	}
	return ""
}

func (x *Collaborator) GetAccessMode() string {
	if x != nil {
		return x.AccessMode
	}
	return ""
}

type CheckModelIsExistRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name          string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	WorkspaceName string `protobuf:"bytes,2,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Region        string `protobuf:"bytes,3,opt,name=region,proto3" json:"region,omitempty"`
}

func (x *CheckModelIsExistRequest) Reset() {
	*x = CheckModelIsExistRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckModelIsExistRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckModelIsExistRequest) ProtoMessage() {}

func (x *CheckModelIsExistRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckModelIsExistRequest.ProtoReflect.Descriptor instead.
func (*CheckModelIsExistRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP(), []int{42}
}

func (x *CheckModelIsExistRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CheckModelIsExistRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *CheckModelIsExistRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

type CheckModelIsExistResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status int32  `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"` // 适配 fcn，0 为校验成功， 422 为校验失败
	Errors string `protobuf:"bytes,2,opt,name=errors,proto3" json:"errors,omitempty"`
}

func (x *CheckModelIsExistResponse) Reset() {
	*x = CheckModelIsExistResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckModelIsExistResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckModelIsExistResponse) ProtoMessage() {}

func (x *CheckModelIsExistResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckModelIsExistResponse.ProtoReflect.Descriptor instead.
func (*CheckModelIsExistResponse) Descriptor() ([]byte, []int) {
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP(), []int{43}
}

func (x *CheckModelIsExistResponse) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *CheckModelIsExistResponse) GetErrors() string {
	if x != nil {
		return x.Errors
	}
	return ""
}

type InitModelHubOrgRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName string `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Region        string `protobuf:"bytes,2,opt,name=region,proto3" json:"region,omitempty"`
}

func (x *InitModelHubOrgRequest) Reset() {
	*x = InitModelHubOrgRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InitModelHubOrgRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitModelHubOrgRequest) ProtoMessage() {}

func (x *InitModelHubOrgRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitModelHubOrgRequest.ProtoReflect.Descriptor instead.
func (*InitModelHubOrgRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP(), []int{44}
}

func (x *InitModelHubOrgRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *InitModelHubOrgRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

type CheckCollaboratorAuthRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName string `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Region        string `protobuf:"bytes,2,opt,name=region,proto3" json:"region,omitempty"`
	Repo          string `protobuf:"bytes,3,opt,name=repo,proto3" json:"repo,omitempty"`
	NeedAuth      string `protobuf:"bytes,4,opt,name=needAuth,proto3" json:"needAuth,omitempty"`
}

func (x *CheckCollaboratorAuthRequest) Reset() {
	*x = CheckCollaboratorAuthRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckCollaboratorAuthRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckCollaboratorAuthRequest) ProtoMessage() {}

func (x *CheckCollaboratorAuthRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckCollaboratorAuthRequest.ProtoReflect.Descriptor instead.
func (*CheckCollaboratorAuthRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP(), []int{45}
}

func (x *CheckCollaboratorAuthRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *CheckCollaboratorAuthRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *CheckCollaboratorAuthRequest) GetRepo() string {
	if x != nil {
		return x.Repo
	}
	return ""
}

func (x *CheckCollaboratorAuthRequest) GetNeedAuth() string {
	if x != nil {
		return x.NeedAuth
	}
	return ""
}

type CheckCollaboratorAuthResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Msg      string `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg,omitempty"`
	HaveAuth bool   `protobuf:"varint,2,opt,name=haveAuth,proto3" json:"haveAuth,omitempty"`
}

func (x *CheckCollaboratorAuthResponse) Reset() {
	*x = CheckCollaboratorAuthResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckCollaboratorAuthResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckCollaboratorAuthResponse) ProtoMessage() {}

func (x *CheckCollaboratorAuthResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckCollaboratorAuthResponse.ProtoReflect.Descriptor instead.
func (*CheckCollaboratorAuthResponse) Descriptor() ([]byte, []int) {
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP(), []int{46}
}

func (x *CheckCollaboratorAuthResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *CheckCollaboratorAuthResponse) GetHaveAuth() bool {
	if x != nil {
		return x.HaveAuth
	}
	return false
}

type GetModelInfoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName string `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Region        string `protobuf:"bytes,2,opt,name=region,proto3" json:"region,omitempty"`
	Name          string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Ref           string `protobuf:"bytes,4,opt,name=ref,proto3" json:"ref,omitempty"`
}

func (x *GetModelInfoRequest) Reset() {
	*x = GetModelInfoRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetModelInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetModelInfoRequest) ProtoMessage() {}

func (x *GetModelInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetModelInfoRequest.ProtoReflect.Descriptor instead.
func (*GetModelInfoRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP(), []int{47}
}

func (x *GetModelInfoRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *GetModelInfoRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *GetModelInfoRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GetModelInfoRequest) GetRef() string {
	if x != nil {
		return x.Ref
	}
	return ""
}

type GetModelInfoResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Msg      string     `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg,omitempty"`
	Siblings []*SDKFile `protobuf:"bytes,2,rep,name=siblings,proto3" json:"siblings,omitempty"`
}

func (x *GetModelInfoResponse) Reset() {
	*x = GetModelInfoResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetModelInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetModelInfoResponse) ProtoMessage() {}

func (x *GetModelInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetModelInfoResponse.ProtoReflect.Descriptor instead.
func (*GetModelInfoResponse) Descriptor() ([]byte, []int) {
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP(), []int{48}
}

func (x *GetModelInfoResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *GetModelInfoResponse) GetSiblings() []*SDKFile {
	if x != nil {
		return x.Siblings
	}
	return nil
}

type SDKFile struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FileName string `protobuf:"bytes,1,opt,name=fileName,proto3" json:"fileName,omitempty"`
}

func (x *SDKFile) Reset() {
	*x = SDKFile{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SDKFile) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SDKFile) ProtoMessage() {}

func (x *SDKFile) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SDKFile.ProtoReflect.Descriptor instead.
func (*SDKFile) Descriptor() ([]byte, []int) {
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP(), []int{49}
}

func (x *SDKFile) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

type GetTagsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName string `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Name          string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Region        string `protobuf:"bytes,3,opt,name=region,proto3" json:"region,omitempty"`
	Page          int32  `protobuf:"varint,4,opt,name=page,proto3" json:"page,omitempty"`
	PerPage       int32  `protobuf:"varint,5,opt,name=perPage,proto3" json:"perPage,omitempty"`
	RepoType      string `protobuf:"bytes,6,opt,name=repoType,proto3" json:"repoType,omitempty"`
}

func (x *GetTagsRequest) Reset() {
	*x = GetTagsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTagsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTagsRequest) ProtoMessage() {}

func (x *GetTagsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTagsRequest.ProtoReflect.Descriptor instead.
func (*GetTagsRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP(), []int{50}
}

func (x *GetTagsRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *GetTagsRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GetTagsRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *GetTagsRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetTagsRequest) GetPerPage() int32 {
	if x != nil {
		return x.PerPage
	}
	return 0
}

func (x *GetTagsRequest) GetRepoType() string {
	if x != nil {
		return x.RepoType
	}
	return ""
}

type GetTagsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Msg  string  `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg,omitempty"`
	Tags []*Tags `protobuf:"bytes,2,rep,name=tags,proto3" json:"tags,omitempty"`
}

func (x *GetTagsResponse) Reset() {
	*x = GetTagsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTagsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTagsResponse) ProtoMessage() {}

func (x *GetTagsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTagsResponse.ProtoReflect.Descriptor instead.
func (*GetTagsResponse) Descriptor() ([]byte, []int) {
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP(), []int{51}
}

func (x *GetTagsResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *GetTagsResponse) GetTags() []*Tags {
	if x != nil {
		return x.Tags
	}
	return nil
}

type Tags struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *Tags) Reset() {
	*x = Tags{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Tags) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Tags) ProtoMessage() {}

func (x *Tags) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Tags.ProtoReflect.Descriptor instead.
func (*Tags) Descriptor() ([]byte, []int) {
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP(), []int{52}
}

func (x *Tags) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type GetBranchesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName string `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Name          string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Region        string `protobuf:"bytes,3,opt,name=region,proto3" json:"region,omitempty"`
	Page          int32  `protobuf:"varint,4,opt,name=page,proto3" json:"page,omitempty"`
	PerPage       int32  `protobuf:"varint,5,opt,name=perPage,proto3" json:"perPage,omitempty"`
	RepoType      string `protobuf:"bytes,6,opt,name=repoType,proto3" json:"repoType,omitempty"`
}

func (x *GetBranchesRequest) Reset() {
	*x = GetBranchesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBranchesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBranchesRequest) ProtoMessage() {}

func (x *GetBranchesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBranchesRequest.ProtoReflect.Descriptor instead.
func (*GetBranchesRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP(), []int{53}
}

func (x *GetBranchesRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *GetBranchesRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GetBranchesRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *GetBranchesRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetBranchesRequest) GetPerPage() int32 {
	if x != nil {
		return x.PerPage
	}
	return 0
}

func (x *GetBranchesRequest) GetRepoType() string {
	if x != nil {
		return x.RepoType
	}
	return ""
}

type GetBranchesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Msg      string    `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg,omitempty"`
	Branches []*Branch `protobuf:"bytes,2,rep,name=branches,proto3" json:"branches,omitempty"`
}

func (x *GetBranchesResponse) Reset() {
	*x = GetBranchesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[54]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBranchesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBranchesResponse) ProtoMessage() {}

func (x *GetBranchesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[54]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBranchesResponse.ProtoReflect.Descriptor instead.
func (*GetBranchesResponse) Descriptor() ([]byte, []int) {
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP(), []int{54}
}

func (x *GetBranchesResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *GetBranchesResponse) GetBranches() []*Branch {
	if x != nil {
		return x.Branches
	}
	return nil
}

type Branch struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name   string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Commit string `protobuf:"bytes,2,opt,name=commit,proto3" json:"commit,omitempty"`
}

func (x *Branch) Reset() {
	*x = Branch{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[55]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Branch) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Branch) ProtoMessage() {}

func (x *Branch) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[55]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Branch.ProtoReflect.Descriptor instead.
func (*Branch) Descriptor() ([]byte, []int) {
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP(), []int{55}
}

func (x *Branch) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Branch) GetCommit() string {
	if x != nil {
		return x.Commit
	}
	return ""
}

type CreateBranchRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName string `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Name          string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Branch        string `protobuf:"bytes,3,opt,name=branch,proto3" json:"branch,omitempty"`
	OldBranch     string `protobuf:"bytes,4,opt,name=oldBranch,proto3" json:"oldBranch,omitempty"`
	RepoType      string `protobuf:"bytes,5,opt,name=repoType,proto3" json:"repoType,omitempty"`
}

func (x *CreateBranchRequest) Reset() {
	*x = CreateBranchRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[56]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateBranchRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateBranchRequest) ProtoMessage() {}

func (x *CreateBranchRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[56]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateBranchRequest.ProtoReflect.Descriptor instead.
func (*CreateBranchRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP(), []int{56}
}

func (x *CreateBranchRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *CreateBranchRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateBranchRequest) GetBranch() string {
	if x != nil {
		return x.Branch
	}
	return ""
}

func (x *CreateBranchRequest) GetOldBranch() string {
	if x != nil {
		return x.OldBranch
	}
	return ""
}

func (x *CreateBranchRequest) GetRepoType() string {
	if x != nil {
		return x.RepoType
	}
	return ""
}

type CreateBranchResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Msg        string `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg,omitempty"`
	CreateSucc bool   `protobuf:"varint,2,opt,name=createSucc,proto3" json:"createSucc,omitempty"`
}

func (x *CreateBranchResponse) Reset() {
	*x = CreateBranchResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[57]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateBranchResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateBranchResponse) ProtoMessage() {}

func (x *CreateBranchResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[57]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateBranchResponse.ProtoReflect.Descriptor instead.
func (*CreateBranchResponse) Descriptor() ([]byte, []int) {
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP(), []int{57}
}

func (x *CreateBranchResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *CreateBranchResponse) GetCreateSucc() bool {
	if x != nil {
		return x.CreateSucc
	}
	return false
}

type GetAllFilesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName string `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Name          string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Ref           string `protobuf:"bytes,3,opt,name=ref,proto3" json:"ref,omitempty"`
	Path          string `protobuf:"bytes,4,opt,name=path,proto3" json:"path,omitempty"`
	Region        string `protobuf:"bytes,5,opt,name=region,proto3" json:"region,omitempty"`
	RepoType      string `protobuf:"bytes,6,opt,name=repoType,proto3" json:"repoType,omitempty"`
}

func (x *GetAllFilesRequest) Reset() {
	*x = GetAllFilesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[58]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAllFilesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllFilesRequest) ProtoMessage() {}

func (x *GetAllFilesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[58]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllFilesRequest.ProtoReflect.Descriptor instead.
func (*GetAllFilesRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP(), []int{58}
}

func (x *GetAllFilesRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *GetAllFilesRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GetAllFilesRequest) GetRef() string {
	if x != nil {
		return x.Ref
	}
	return ""
}

func (x *GetAllFilesRequest) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *GetAllFilesRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *GetAllFilesRequest) GetRepoType() string {
	if x != nil {
		return x.RepoType
	}
	return ""
}

type GetAllFilesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Msg   string  `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg,omitempty"`
	Files []*File `protobuf:"bytes,2,rep,name=files,proto3" json:"files,omitempty"`
}

func (x *GetAllFilesResponse) Reset() {
	*x = GetAllFilesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[59]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAllFilesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllFilesResponse) ProtoMessage() {}

func (x *GetAllFilesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[59]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllFilesResponse.ProtoReflect.Descriptor instead.
func (*GetAllFilesResponse) Descriptor() ([]byte, []int) {
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP(), []int{59}
}

func (x *GetAllFilesResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *GetAllFilesResponse) GetFiles() []*File {
	if x != nil {
		return x.Files
	}
	return nil
}

type CheckBranchRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName string `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Name          string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Branch        string `protobuf:"bytes,3,opt,name=branch,proto3" json:"branch,omitempty"`
	Region        string `protobuf:"bytes,4,opt,name=region,proto3" json:"region,omitempty"`
	RepoType      string `protobuf:"bytes,5,opt,name=repoType,proto3" json:"repoType,omitempty"`
}

func (x *CheckBranchRequest) Reset() {
	*x = CheckBranchRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[60]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckBranchRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckBranchRequest) ProtoMessage() {}

func (x *CheckBranchRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[60]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckBranchRequest.ProtoReflect.Descriptor instead.
func (*CheckBranchRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP(), []int{60}
}

func (x *CheckBranchRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *CheckBranchRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CheckBranchRequest) GetBranch() string {
	if x != nil {
		return x.Branch
	}
	return ""
}

func (x *CheckBranchRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *CheckBranchRequest) GetRepoType() string {
	if x != nil {
		return x.RepoType
	}
	return ""
}

type CheckBranchResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Errors string `protobuf:"bytes,1,opt,name=errors,proto3" json:"errors,omitempty"`
	Status int32  `protobuf:"varint,2,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *CheckBranchResponse) Reset() {
	*x = CheckBranchResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[61]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckBranchResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckBranchResponse) ProtoMessage() {}

func (x *CheckBranchResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[61]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckBranchResponse.ProtoReflect.Descriptor instead.
func (*CheckBranchResponse) Descriptor() ([]byte, []int) {
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP(), []int{61}
}

func (x *CheckBranchResponse) GetErrors() string {
	if x != nil {
		return x.Errors
	}
	return ""
}

func (x *CheckBranchResponse) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

type SyncModelRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 工作空间
	WorkspaceName string `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	// 地域
	Region string `protobuf:"bytes,2,opt,name=region,proto3" json:"region,omitempty"`
	// 模型名称
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// 是否强制同步
	Force bool `protobuf:"varint,4,opt,name=force,proto3" json:"force,omitempty"`
	// 同步源
	Source string `protobuf:"bytes,5,opt,name=source,proto3" json:"source,omitempty"`
	// hf_user
	HfUser string `protobuf:"bytes,6,opt,name=hfUser,proto3" json:"hfUser,omitempty"`
	// hf_token
	HfToken string `protobuf:"bytes,7,opt,name=hfToken,proto3" json:"hfToken,omitempty"`
}

func (x *SyncModelRequest) Reset() {
	*x = SyncModelRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[62]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SyncModelRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncModelRequest) ProtoMessage() {}

func (x *SyncModelRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[62]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncModelRequest.ProtoReflect.Descriptor instead.
func (*SyncModelRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP(), []int{62}
}

func (x *SyncModelRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *SyncModelRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *SyncModelRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SyncModelRequest) GetForce() bool {
	if x != nil {
		return x.Force
	}
	return false
}

func (x *SyncModelRequest) GetSource() string {
	if x != nil {
		return x.Source
	}
	return ""
}

func (x *SyncModelRequest) GetHfUser() string {
	if x != nil {
		return x.HfUser
	}
	return ""
}

func (x *SyncModelRequest) GetHfToken() string {
	if x != nil {
		return x.HfToken
	}
	return ""
}

type SyncModelResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 消息
	Msg string `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg,omitempty"`
	// 状态
	Status int32 `protobuf:"varint,2,opt,name=status,proto3" json:"status,omitempty"`
	// 同步记录
	Data *SyncModelData `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *SyncModelResponse) Reset() {
	*x = SyncModelResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[63]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SyncModelResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncModelResponse) ProtoMessage() {}

func (x *SyncModelResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[63]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncModelResponse.ProtoReflect.Descriptor instead.
func (*SyncModelResponse) Descriptor() ([]byte, []int) {
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP(), []int{63}
}

func (x *SyncModelResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *SyncModelResponse) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *SyncModelResponse) GetData() *SyncModelData {
	if x != nil {
		return x.Data
	}
	return nil
}

type SyncModelData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// job name
	JobName string `protobuf:"bytes,1,opt,name=jobName,proto3" json:"jobName,omitempty"`
}

func (x *SyncModelData) Reset() {
	*x = SyncModelData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[64]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SyncModelData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncModelData) ProtoMessage() {}

func (x *SyncModelData) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[64]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncModelData.ProtoReflect.Descriptor instead.
func (*SyncModelData) Descriptor() ([]byte, []int) {
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP(), []int{64}
}

func (x *SyncModelData) GetJobName() string {
	if x != nil {
		return x.JobName
	}
	return ""
}

type CheckModelForSyncRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 地域
	Region string `protobuf:"bytes,1,opt,name=region,proto3" json:"region,omitempty"`
	// 模型名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *CheckModelForSyncRequest) Reset() {
	*x = CheckModelForSyncRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[65]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckModelForSyncRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckModelForSyncRequest) ProtoMessage() {}

func (x *CheckModelForSyncRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[65]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckModelForSyncRequest.ProtoReflect.Descriptor instead.
func (*CheckModelForSyncRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP(), []int{65}
}

func (x *CheckModelForSyncRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *CheckModelForSyncRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type CheckModelForSyncResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 消息
	Msg string `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg,omitempty"`
	// 状态
	Code int32 `protobuf:"varint,2,opt,name=code,proto3" json:"code,omitempty"`
}

func (x *CheckModelForSyncResponse) Reset() {
	*x = CheckModelForSyncResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[66]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckModelForSyncResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckModelForSyncResponse) ProtoMessage() {}

func (x *CheckModelForSyncResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[66]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckModelForSyncResponse.ProtoReflect.Descriptor instead.
func (*CheckModelForSyncResponse) Descriptor() ([]byte, []int) {
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP(), []int{66}
}

func (x *CheckModelForSyncResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *CheckModelForSyncResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

type ListSyncModelRecordRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 工作空间
	WorkspaceName string `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	// 地域
	Region string `protobuf:"bytes,2,opt,name=region,proto3" json:"region,omitempty"`
	// 模型名称
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// 状态
	Status string `protobuf:"bytes,4,opt,name=status,proto3" json:"status,omitempty"`
	// 状态
	Creator string `protobuf:"bytes,5,opt,name=creator,proto3" json:"creator,omitempty"`
	// 页码
	Page int32 `protobuf:"varint,6,opt,name=page,proto3" json:"page,omitempty"`
	// 每页数量
	PageSize int32 `protobuf:"varint,7,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
	// jobName
	JobName string `protobuf:"bytes,8,opt,name=jobName,proto3" json:"jobName,omitempty"`
	// modelName
	ModelName string `protobuf:"bytes,9,opt,name=modelName,proto3" json:"modelName,omitempty"`
}

func (x *ListSyncModelRecordRequest) Reset() {
	*x = ListSyncModelRecordRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[67]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListSyncModelRecordRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSyncModelRecordRequest) ProtoMessage() {}

func (x *ListSyncModelRecordRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[67]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSyncModelRecordRequest.ProtoReflect.Descriptor instead.
func (*ListSyncModelRecordRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP(), []int{67}
}

func (x *ListSyncModelRecordRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *ListSyncModelRecordRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *ListSyncModelRecordRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ListSyncModelRecordRequest) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *ListSyncModelRecordRequest) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *ListSyncModelRecordRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListSyncModelRecordRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListSyncModelRecordRequest) GetJobName() string {
	if x != nil {
		return x.JobName
	}
	return ""
}

func (x *ListSyncModelRecordRequest) GetModelName() string {
	if x != nil {
		return x.ModelName
	}
	return ""
}

type ListSyncModelRecordResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 消息
	Msg string `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg,omitempty"`
	// 状态
	Status int32 `protobuf:"varint,2,opt,name=status,proto3" json:"status,omitempty"`
	// 同步记录
	Records []*SyncModelRecord `protobuf:"bytes,3,rep,name=records,proto3" json:"records,omitempty"`
	// 总数
	Total int32 `protobuf:"varint,4,opt,name=total,proto3" json:"total,omitempty"`
}

func (x *ListSyncModelRecordResponse) Reset() {
	*x = ListSyncModelRecordResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[68]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListSyncModelRecordResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSyncModelRecordResponse) ProtoMessage() {}

func (x *ListSyncModelRecordResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[68]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSyncModelRecordResponse.ProtoReflect.Descriptor instead.
func (*ListSyncModelRecordResponse) Descriptor() ([]byte, []int) {
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP(), []int{68}
}

func (x *ListSyncModelRecordResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *ListSyncModelRecordResponse) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *ListSyncModelRecordResponse) GetRecords() []*SyncModelRecord {
	if x != nil {
		return x.Records
	}
	return nil
}

func (x *ListSyncModelRecordResponse) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

type SyncModelRecord struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 同步记录ID
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// 工作空间
	WorkspaceName string `protobuf:"bytes,2,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	// 地域
	Region string `protobuf:"bytes,3,opt,name=region,proto3" json:"region,omitempty"`
	// 待同步的模型名称
	Name string `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	// 同步后的模型名称
	ModelName string `protobuf:"bytes,5,opt,name=modelName,proto3" json:"modelName,omitempty"`
	// 状态
	Status string `protobuf:"bytes,6,opt,name=status,proto3" json:"status,omitempty"`
	// 创建时间
	CreateTime string `protobuf:"bytes,7,opt,name=createTime,proto3" json:"createTime,omitempty"`
	// 创建者
	Creator string `protobuf:"bytes,8,opt,name=creator,proto3" json:"creator,omitempty"`
	// pod名称
	PodName string `protobuf:"bytes,9,opt,name=podName,proto3" json:"podName,omitempty"`
	// job名称
	JobName string `protobuf:"bytes,10,opt,name=jobName,proto3" json:"jobName,omitempty"`
	// 是否强制同步
	Force bool `protobuf:"varint,11,opt,name=force,proto3" json:"force,omitempty"`
	// 同步源
	Source string `protobuf:"bytes,12,opt,name=source,proto3" json:"source,omitempty"`
	// hf_user
	HfUser string `protobuf:"bytes,13,opt,name=hfUser,proto3" json:"hfUser,omitempty"`
	// hf_token
	HfToken string `protobuf:"bytes,14,opt,name=hfToken,proto3" json:"hfToken,omitempty"`
	// reason
	Reason string `protobuf:"bytes,15,opt,name=reason,proto3" json:"reason,omitempty"`
	// msg
	Msg string `protobuf:"bytes,16,opt,name=msg,proto3" json:"msg,omitempty"`
	// Condition
	Conditions     []*SyncCondition `protobuf:"bytes,17,rep,name=conditions,proto3" json:"conditions,omitempty"`
	LogFrom        int64            `protobuf:"varint,18,opt,name=logFrom,proto3" json:"logFrom,omitempty"`
	LogTo          int64            `protobuf:"varint,19,opt,name=logTo,proto3" json:"logTo,omitempty"`
	SyncTime       string           `protobuf:"bytes,20,opt,name=syncTime,proto3" json:"syncTime,omitempty"`
	StartTime      string           `protobuf:"bytes,21,opt,name=startTime,proto3" json:"startTime,omitempty"`
	EndTime        string           `protobuf:"bytes,22,opt,name=endTime,proto3" json:"endTime,omitempty"`
	ModelURL       string           `protobuf:"bytes,23,opt,name=modelURL,proto3" json:"modelURL,omitempty"`
	ModelDetailURL string           `protobuf:"bytes,24,opt,name=modelDetailURL,proto3" json:"modelDetailURL,omitempty"`
}

func (x *SyncModelRecord) Reset() {
	*x = SyncModelRecord{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[69]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SyncModelRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncModelRecord) ProtoMessage() {}

func (x *SyncModelRecord) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[69]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncModelRecord.ProtoReflect.Descriptor instead.
func (*SyncModelRecord) Descriptor() ([]byte, []int) {
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP(), []int{69}
}

func (x *SyncModelRecord) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *SyncModelRecord) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *SyncModelRecord) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *SyncModelRecord) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SyncModelRecord) GetModelName() string {
	if x != nil {
		return x.ModelName
	}
	return ""
}

func (x *SyncModelRecord) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *SyncModelRecord) GetCreateTime() string {
	if x != nil {
		return x.CreateTime
	}
	return ""
}

func (x *SyncModelRecord) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *SyncModelRecord) GetPodName() string {
	if x != nil {
		return x.PodName
	}
	return ""
}

func (x *SyncModelRecord) GetJobName() string {
	if x != nil {
		return x.JobName
	}
	return ""
}

func (x *SyncModelRecord) GetForce() bool {
	if x != nil {
		return x.Force
	}
	return false
}

func (x *SyncModelRecord) GetSource() string {
	if x != nil {
		return x.Source
	}
	return ""
}

func (x *SyncModelRecord) GetHfUser() string {
	if x != nil {
		return x.HfUser
	}
	return ""
}

func (x *SyncModelRecord) GetHfToken() string {
	if x != nil {
		return x.HfToken
	}
	return ""
}

func (x *SyncModelRecord) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *SyncModelRecord) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *SyncModelRecord) GetConditions() []*SyncCondition {
	if x != nil {
		return x.Conditions
	}
	return nil
}

func (x *SyncModelRecord) GetLogFrom() int64 {
	if x != nil {
		return x.LogFrom
	}
	return 0
}

func (x *SyncModelRecord) GetLogTo() int64 {
	if x != nil {
		return x.LogTo
	}
	return 0
}

func (x *SyncModelRecord) GetSyncTime() string {
	if x != nil {
		return x.SyncTime
	}
	return ""
}

func (x *SyncModelRecord) GetStartTime() string {
	if x != nil {
		return x.StartTime
	}
	return ""
}

func (x *SyncModelRecord) GetEndTime() string {
	if x != nil {
		return x.EndTime
	}
	return ""
}

func (x *SyncModelRecord) GetModelURL() string {
	if x != nil {
		return x.ModelURL
	}
	return ""
}

func (x *SyncModelRecord) GetModelDetailURL() string {
	if x != nil {
		return x.ModelDetailURL
	}
	return ""
}

type SyncCondition struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type               string `protobuf:"bytes,1,opt,name=type,proto3" json:"type,omitempty"`
	Status             string `protobuf:"bytes,2,opt,name=status,proto3" json:"status,omitempty"`
	LastTransitionTime string `protobuf:"bytes,3,opt,name=lastTransitionTime,proto3" json:"lastTransitionTime,omitempty"` //最后一次状态变更时间
	Reason             string `protobuf:"bytes,4,opt,name=reason,proto3" json:"reason,omitempty"`                         //the reason of the job
	Message            string `protobuf:"bytes,5,opt,name=message,proto3" json:"message,omitempty"`                       //the message of the job
}

func (x *SyncCondition) Reset() {
	*x = SyncCondition{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[70]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SyncCondition) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncCondition) ProtoMessage() {}

func (x *SyncCondition) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[70]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncCondition.ProtoReflect.Descriptor instead.
func (*SyncCondition) Descriptor() ([]byte, []int) {
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP(), []int{70}
}

func (x *SyncCondition) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *SyncCondition) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *SyncCondition) GetLastTransitionTime() string {
	if x != nil {
		return x.LastTransitionTime
	}
	return ""
}

func (x *SyncCondition) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *SyncCondition) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type StopSyncModelRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	JobName       string `protobuf:"bytes,1,opt,name=jobName,proto3" json:"jobName,omitempty"`
	WorkspaceName string `protobuf:"bytes,2,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Region        string `protobuf:"bytes,3,opt,name=region,proto3" json:"region,omitempty"`
}

func (x *StopSyncModelRequest) Reset() {
	*x = StopSyncModelRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[71]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StopSyncModelRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StopSyncModelRequest) ProtoMessage() {}

func (x *StopSyncModelRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[71]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StopSyncModelRequest.ProtoReflect.Descriptor instead.
func (*StopSyncModelRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP(), []int{71}
}

func (x *StopSyncModelRequest) GetJobName() string {
	if x != nil {
		return x.JobName
	}
	return ""
}

func (x *StopSyncModelRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *StopSyncModelRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

type StopSyncModelResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Msg    string `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg,omitempty"`
	Status int32  `protobuf:"varint,2,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *StopSyncModelResponse) Reset() {
	*x = StopSyncModelResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[72]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StopSyncModelResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StopSyncModelResponse) ProtoMessage() {}

func (x *StopSyncModelResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[72]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StopSyncModelResponse.ProtoReflect.Descriptor instead.
func (*StopSyncModelResponse) Descriptor() ([]byte, []int) {
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP(), []int{72}
}

func (x *StopSyncModelResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *StopSyncModelResponse) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

type UpdateWarmupConfigRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName      string        `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Region             string        `protobuf:"bytes,2,opt,name=region,proto3" json:"region,omitempty"`
	ModelWorkspaceName string        `protobuf:"bytes,3,opt,name=modelWorkspaceName,proto3" json:"modelWorkspaceName,omitempty"`
	ModelName          string        `protobuf:"bytes,4,opt,name=modelName,proto3" json:"modelName,omitempty"`
	WarmupConfig       *WarmupConfig `protobuf:"bytes,5,opt,name=warmupConfig,proto3" json:"warmupConfig,omitempty"`
	Creator            string        `protobuf:"bytes,6,opt,name=creator,proto3" json:"creator,omitempty"`
}

func (x *UpdateWarmupConfigRequest) Reset() {
	*x = UpdateWarmupConfigRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[73]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateWarmupConfigRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateWarmupConfigRequest) ProtoMessage() {}

func (x *UpdateWarmupConfigRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[73]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateWarmupConfigRequest.ProtoReflect.Descriptor instead.
func (*UpdateWarmupConfigRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP(), []int{73}
}

func (x *UpdateWarmupConfigRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *UpdateWarmupConfigRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *UpdateWarmupConfigRequest) GetModelWorkspaceName() string {
	if x != nil {
		return x.ModelWorkspaceName
	}
	return ""
}

func (x *UpdateWarmupConfigRequest) GetModelName() string {
	if x != nil {
		return x.ModelName
	}
	return ""
}

func (x *UpdateWarmupConfigRequest) GetWarmupConfig() *WarmupConfig {
	if x != nil {
		return x.WarmupConfig
	}
	return nil
}

func (x *UpdateWarmupConfigRequest) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

type WarmupConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Enabled       bool            `protobuf:"varint,1,opt,name=enabled,proto3" json:"enabled,omitempty"`
	Branch        string          `protobuf:"bytes,2,opt,name=branch,proto3" json:"branch,omitempty"`
	WarmupRegions []*WarmupRegion `protobuf:"bytes,3,rep,name=warmupRegions,proto3" json:"warmupRegions,omitempty"`
}

func (x *WarmupConfig) Reset() {
	*x = WarmupConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[74]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WarmupConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WarmupConfig) ProtoMessage() {}

func (x *WarmupConfig) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[74]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WarmupConfig.ProtoReflect.Descriptor instead.
func (*WarmupConfig) Descriptor() ([]byte, []int) {
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP(), []int{74}
}

func (x *WarmupConfig) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

func (x *WarmupConfig) GetBranch() string {
	if x != nil {
		return x.Branch
	}
	return ""
}

func (x *WarmupConfig) GetWarmupRegions() []*WarmupRegion {
	if x != nil {
		return x.WarmupRegions
	}
	return nil
}

type WarmupRegion struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WarmupRegion     string `protobuf:"bytes,1,opt,name=warmupRegion,proto3" json:"warmupRegion,omitempty"`
	WarmupRegionName string `protobuf:"bytes,4,opt,name=warmupRegionName,proto3" json:"warmupRegionName,omitempty"`
	WarmupZone       string `protobuf:"bytes,2,opt,name=warmupZone,proto3" json:"warmupZone,omitempty"`
	WarmupZoneName   string `protobuf:"bytes,3,opt,name=warmupZoneName,proto3" json:"warmupZoneName,omitempty"`
}

func (x *WarmupRegion) Reset() {
	*x = WarmupRegion{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[75]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WarmupRegion) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WarmupRegion) ProtoMessage() {}

func (x *WarmupRegion) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[75]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WarmupRegion.ProtoReflect.Descriptor instead.
func (*WarmupRegion) Descriptor() ([]byte, []int) {
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP(), []int{75}
}

func (x *WarmupRegion) GetWarmupRegion() string {
	if x != nil {
		return x.WarmupRegion
	}
	return ""
}

func (x *WarmupRegion) GetWarmupRegionName() string {
	if x != nil {
		return x.WarmupRegionName
	}
	return ""
}

func (x *WarmupRegion) GetWarmupZone() string {
	if x != nil {
		return x.WarmupZone
	}
	return ""
}

func (x *WarmupRegion) GetWarmupZoneName() string {
	if x != nil {
		return x.WarmupZoneName
	}
	return ""
}

type UpdateWarmupConfigResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Msg    string `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg,omitempty"`
	Status int32  `protobuf:"varint,2,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *UpdateWarmupConfigResponse) Reset() {
	*x = UpdateWarmupConfigResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[76]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateWarmupConfigResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateWarmupConfigResponse) ProtoMessage() {}

func (x *UpdateWarmupConfigResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[76]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateWarmupConfigResponse.ProtoReflect.Descriptor instead.
func (*UpdateWarmupConfigResponse) Descriptor() ([]byte, []int) {
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP(), []int{76}
}

func (x *UpdateWarmupConfigResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *UpdateWarmupConfigResponse) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

type StartWarmupJobRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName      string          `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Region             string          `protobuf:"bytes,2,opt,name=region,proto3" json:"region,omitempty"`
	ModelWorkspaceName string          `protobuf:"bytes,3,opt,name=modelWorkspaceName,proto3" json:"modelWorkspaceName,omitempty"`
	ModelName          string          `protobuf:"bytes,4,opt,name=modelName,proto3" json:"modelName,omitempty"`
	Branch             string          `protobuf:"bytes,5,opt,name=branch,proto3" json:"branch,omitempty"`
	WarmupRegions      []*WarmupRegion `protobuf:"bytes,6,rep,name=warmupRegions,proto3" json:"warmupRegions,omitempty"`
	Creator            string          `protobuf:"bytes,7,opt,name=creator,proto3" json:"creator,omitempty"`
}

func (x *StartWarmupJobRequest) Reset() {
	*x = StartWarmupJobRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[77]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StartWarmupJobRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartWarmupJobRequest) ProtoMessage() {}

func (x *StartWarmupJobRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[77]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartWarmupJobRequest.ProtoReflect.Descriptor instead.
func (*StartWarmupJobRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP(), []int{77}
}

func (x *StartWarmupJobRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *StartWarmupJobRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *StartWarmupJobRequest) GetModelWorkspaceName() string {
	if x != nil {
		return x.ModelWorkspaceName
	}
	return ""
}

func (x *StartWarmupJobRequest) GetModelName() string {
	if x != nil {
		return x.ModelName
	}
	return ""
}

func (x *StartWarmupJobRequest) GetBranch() string {
	if x != nil {
		return x.Branch
	}
	return ""
}

func (x *StartWarmupJobRequest) GetWarmupRegions() []*WarmupRegion {
	if x != nil {
		return x.WarmupRegions
	}
	return nil
}

func (x *StartWarmupJobRequest) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

type StartWarmupJobResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	JobName string `protobuf:"bytes,3,opt,name=jobName,proto3" json:"jobName,omitempty"`
}

func (x *StartWarmupJobResponse) Reset() {
	*x = StartWarmupJobResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[78]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StartWarmupJobResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartWarmupJobResponse) ProtoMessage() {}

func (x *StartWarmupJobResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[78]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartWarmupJobResponse.ProtoReflect.Descriptor instead.
func (*StartWarmupJobResponse) Descriptor() ([]byte, []int) {
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP(), []int{78}
}

func (x *StartWarmupJobResponse) GetJobName() string {
	if x != nil {
		return x.JobName
	}
	return ""
}

type StopWarmupJobRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName      string `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Region             string `protobuf:"bytes,2,opt,name=region,proto3" json:"region,omitempty"`
	ModelWorkspaceName string `protobuf:"bytes,3,opt,name=modelWorkspaceName,proto3" json:"modelWorkspaceName,omitempty"`
	ModelName          string `protobuf:"bytes,4,opt,name=modelName,proto3" json:"modelName,omitempty"`
	JobName            string `protobuf:"bytes,5,opt,name=jobName,proto3" json:"jobName,omitempty"`
}

func (x *StopWarmupJobRequest) Reset() {
	*x = StopWarmupJobRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[79]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StopWarmupJobRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StopWarmupJobRequest) ProtoMessage() {}

func (x *StopWarmupJobRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[79]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StopWarmupJobRequest.ProtoReflect.Descriptor instead.
func (*StopWarmupJobRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP(), []int{79}
}

func (x *StopWarmupJobRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *StopWarmupJobRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *StopWarmupJobRequest) GetModelWorkspaceName() string {
	if x != nil {
		return x.ModelWorkspaceName
	}
	return ""
}

func (x *StopWarmupJobRequest) GetModelName() string {
	if x != nil {
		return x.ModelName
	}
	return ""
}

func (x *StopWarmupJobRequest) GetJobName() string {
	if x != nil {
		return x.JobName
	}
	return ""
}

type StopWarmupJobResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *StopWarmupJobResponse) Reset() {
	*x = StopWarmupJobResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[80]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StopWarmupJobResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StopWarmupJobResponse) ProtoMessage() {}

func (x *StopWarmupJobResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[80]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StopWarmupJobResponse.ProtoReflect.Descriptor instead.
func (*StopWarmupJobResponse) Descriptor() ([]byte, []int) {
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP(), []int{80}
}

type ListWarmupRecordsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName      string `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Region             string `protobuf:"bytes,2,opt,name=region,proto3" json:"region,omitempty"`
	ModelWorkspaceName string `protobuf:"bytes,3,opt,name=modelWorkspaceName,proto3" json:"modelWorkspaceName,omitempty"`
	ModelName          string `protobuf:"bytes,4,opt,name=modelName,proto3" json:"modelName,omitempty"`
	JobName            string `protobuf:"bytes,5,opt,name=jobName,proto3" json:"jobName,omitempty"`
	Creator            string `protobuf:"bytes,6,opt,name=creator,proto3" json:"creator,omitempty"`
	Page               int32  `protobuf:"varint,7,opt,name=page,proto3" json:"page,omitempty"`
	PageSize           int32  `protobuf:"varint,8,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
}

func (x *ListWarmupRecordsRequest) Reset() {
	*x = ListWarmupRecordsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[81]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListWarmupRecordsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListWarmupRecordsRequest) ProtoMessage() {}

func (x *ListWarmupRecordsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[81]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListWarmupRecordsRequest.ProtoReflect.Descriptor instead.
func (*ListWarmupRecordsRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP(), []int{81}
}

func (x *ListWarmupRecordsRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *ListWarmupRecordsRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *ListWarmupRecordsRequest) GetModelWorkspaceName() string {
	if x != nil {
		return x.ModelWorkspaceName
	}
	return ""
}

func (x *ListWarmupRecordsRequest) GetModelName() string {
	if x != nil {
		return x.ModelName
	}
	return ""
}

func (x *ListWarmupRecordsRequest) GetJobName() string {
	if x != nil {
		return x.JobName
	}
	return ""
}

func (x *ListWarmupRecordsRequest) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *ListWarmupRecordsRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListWarmupRecordsRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

type ListWarmupRecordsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Msg     string          `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg,omitempty"`
	Status  int32           `protobuf:"varint,2,opt,name=status,proto3" json:"status,omitempty"`
	Records []*WarmupRecord `protobuf:"bytes,3,rep,name=records,proto3" json:"records,omitempty"`
	Total   int32           `protobuf:"varint,4,opt,name=total,proto3" json:"total,omitempty"`
}

func (x *ListWarmupRecordsResponse) Reset() {
	*x = ListWarmupRecordsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[82]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListWarmupRecordsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListWarmupRecordsResponse) ProtoMessage() {}

func (x *ListWarmupRecordsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[82]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListWarmupRecordsResponse.ProtoReflect.Descriptor instead.
func (*ListWarmupRecordsResponse) Descriptor() ([]byte, []int) {
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP(), []int{82}
}

func (x *ListWarmupRecordsResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *ListWarmupRecordsResponse) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *ListWarmupRecordsResponse) GetRecords() []*WarmupRecord {
	if x != nil {
		return x.Records
	}
	return nil
}

func (x *ListWarmupRecordsResponse) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

type WarmupRecord struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName      string        `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Region             string        `protobuf:"bytes,2,opt,name=region,proto3" json:"region,omitempty"`
	ModelWorkspaceName string        `protobuf:"bytes,3,opt,name=modelWorkspaceName,proto3" json:"modelWorkspaceName,omitempty"`
	ModelName          string        `protobuf:"bytes,4,opt,name=modelName,proto3" json:"modelName,omitempty"`
	JobName            string        `protobuf:"bytes,5,opt,name=jobName,proto3" json:"jobName,omitempty"`
	WarmupConfig       *WarmupConfig `protobuf:"bytes,6,opt,name=warmupConfig,proto3" json:"warmupConfig,omitempty"`
	Creator            string        `protobuf:"bytes,7,opt,name=creator,proto3" json:"creator,omitempty"`
	CreateTime         string        `protobuf:"bytes,8,opt,name=createTime,proto3" json:"createTime,omitempty"`
	UpdateTime         string        `protobuf:"bytes,9,opt,name=updateTime,proto3" json:"updateTime,omitempty"`
	WarmupStatus       *WarmupStatus `protobuf:"bytes,10,opt,name=warmupStatus,proto3" json:"warmupStatus,omitempty"`
	LogFrom            int64         `protobuf:"varint,11,opt,name=logFrom,proto3" json:"logFrom,omitempty"`
	LogTo              int64         `protobuf:"varint,12,opt,name=logTo,proto3" json:"logTo,omitempty"`
}

func (x *WarmupRecord) Reset() {
	*x = WarmupRecord{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[83]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WarmupRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WarmupRecord) ProtoMessage() {}

func (x *WarmupRecord) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[83]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WarmupRecord.ProtoReflect.Descriptor instead.
func (*WarmupRecord) Descriptor() ([]byte, []int) {
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP(), []int{83}
}

func (x *WarmupRecord) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *WarmupRecord) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *WarmupRecord) GetModelWorkspaceName() string {
	if x != nil {
		return x.ModelWorkspaceName
	}
	return ""
}

func (x *WarmupRecord) GetModelName() string {
	if x != nil {
		return x.ModelName
	}
	return ""
}

func (x *WarmupRecord) GetJobName() string {
	if x != nil {
		return x.JobName
	}
	return ""
}

func (x *WarmupRecord) GetWarmupConfig() *WarmupConfig {
	if x != nil {
		return x.WarmupConfig
	}
	return nil
}

func (x *WarmupRecord) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *WarmupRecord) GetCreateTime() string {
	if x != nil {
		return x.CreateTime
	}
	return ""
}

func (x *WarmupRecord) GetUpdateTime() string {
	if x != nil {
		return x.UpdateTime
	}
	return ""
}

func (x *WarmupRecord) GetWarmupStatus() *WarmupStatus {
	if x != nil {
		return x.WarmupStatus
	}
	return nil
}

func (x *WarmupRecord) GetLogFrom() int64 {
	if x != nil {
		return x.LogFrom
	}
	return 0
}

func (x *WarmupRecord) GetLogTo() int64 {
	if x != nil {
		return x.LogTo
	}
	return 0
}

type WarmupStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status     string              `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Reason     string              `protobuf:"bytes,2,opt,name=reason,proto3" json:"reason,omitempty"`
	Message    string              `protobuf:"bytes,3,opt,name=message,proto3" json:"message,omitempty"`
	Cluster    string              `protobuf:"bytes,4,opt,name=cluster,proto3" json:"cluster,omitempty"`
	PodName    string              `protobuf:"bytes,5,opt,name=podName,proto3" json:"podName,omitempty"`
	StartTime  string              `protobuf:"bytes,6,opt,name=startTime,proto3" json:"startTime,omitempty"`
	EndTime    string              `protobuf:"bytes,7,opt,name=endTime,proto3" json:"endTime,omitempty"`
	LastTime   string              `protobuf:"bytes,8,opt,name=lastTime,proto3" json:"lastTime,omitempty"`
	Conditions []*WarmupConditions `protobuf:"bytes,9,rep,name=conditions,proto3" json:"conditions,omitempty"`
}

func (x *WarmupStatus) Reset() {
	*x = WarmupStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[84]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WarmupStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WarmupStatus) ProtoMessage() {}

func (x *WarmupStatus) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[84]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WarmupStatus.ProtoReflect.Descriptor instead.
func (*WarmupStatus) Descriptor() ([]byte, []int) {
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP(), []int{84}
}

func (x *WarmupStatus) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *WarmupStatus) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *WarmupStatus) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *WarmupStatus) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

func (x *WarmupStatus) GetPodName() string {
	if x != nil {
		return x.PodName
	}
	return ""
}

func (x *WarmupStatus) GetStartTime() string {
	if x != nil {
		return x.StartTime
	}
	return ""
}

func (x *WarmupStatus) GetEndTime() string {
	if x != nil {
		return x.EndTime
	}
	return ""
}

func (x *WarmupStatus) GetLastTime() string {
	if x != nil {
		return x.LastTime
	}
	return ""
}

func (x *WarmupStatus) GetConditions() []*WarmupConditions {
	if x != nil {
		return x.Conditions
	}
	return nil
}

type WarmupConditions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type               string `protobuf:"bytes,1,opt,name=type,proto3" json:"type,omitempty"`
	Status             string `protobuf:"bytes,2,opt,name=status,proto3" json:"status,omitempty"`
	LastTransitionTime string `protobuf:"bytes,3,opt,name=lastTransitionTime,proto3" json:"lastTransitionTime,omitempty"` //最后一次状态变更时间
	Reason             string `protobuf:"bytes,4,opt,name=reason,proto3" json:"reason,omitempty"`                         //the reason of the job
	Message            string `protobuf:"bytes,5,opt,name=message,proto3" json:"message,omitempty"`                       //the message of the job
}

func (x *WarmupConditions) Reset() {
	*x = WarmupConditions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[85]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WarmupConditions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WarmupConditions) ProtoMessage() {}

func (x *WarmupConditions) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[85]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WarmupConditions.ProtoReflect.Descriptor instead.
func (*WarmupConditions) Descriptor() ([]byte, []int) {
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP(), []int{85}
}

func (x *WarmupConditions) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *WarmupConditions) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *WarmupConditions) GetLastTransitionTime() string {
	if x != nil {
		return x.LastTransitionTime
	}
	return ""
}

func (x *WarmupConditions) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *WarmupConditions) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type ListWarmedModelsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName string `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Region        string `protobuf:"bytes,2,opt,name=region,proto3" json:"region,omitempty"`
	OpenType      string `protobuf:"bytes,3,opt,name=openType,proto3" json:"openType,omitempty"`
	Page          int32  `protobuf:"varint,4,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      int32  `protobuf:"varint,5,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
}

func (x *ListWarmedModelsRequest) Reset() {
	*x = ListWarmedModelsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[86]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListWarmedModelsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListWarmedModelsRequest) ProtoMessage() {}

func (x *ListWarmedModelsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[86]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListWarmedModelsRequest.ProtoReflect.Descriptor instead.
func (*ListWarmedModelsRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP(), []int{86}
}

func (x *ListWarmedModelsRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *ListWarmedModelsRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *ListWarmedModelsRequest) GetOpenType() string {
	if x != nil {
		return x.OpenType
	}
	return ""
}

func (x *ListWarmedModelsRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListWarmedModelsRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

type ListWarmedModelsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Models []*WarmedModel `protobuf:"bytes,1,rep,name=models,proto3" json:"models,omitempty"`
	Total  int32          `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
}

func (x *ListWarmedModelsResponse) Reset() {
	*x = ListWarmedModelsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[87]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListWarmedModelsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListWarmedModelsResponse) ProtoMessage() {}

func (x *ListWarmedModelsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[87]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListWarmedModelsResponse.ProtoReflect.Descriptor instead.
func (*ListWarmedModelsResponse) Descriptor() ([]byte, []int) {
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP(), []int{87}
}

func (x *ListWarmedModelsResponse) GetModels() []*WarmedModel {
	if x != nil {
		return x.Models
	}
	return nil
}

func (x *ListWarmedModelsResponse) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

type WarmedModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Region             string        `protobuf:"bytes,1,opt,name=region,proto3" json:"region,omitempty"`
	ModelWorkspaceName string        `protobuf:"bytes,2,opt,name=modelWorkspaceName,proto3" json:"modelWorkspaceName,omitempty"`
	ModelName          string        `protobuf:"bytes,3,opt,name=modelName,proto3" json:"modelName,omitempty"`
	WarmupConfig       *WarmupConfig `protobuf:"bytes,4,opt,name=warmupConfig,proto3" json:"warmupConfig,omitempty"`
	Creator            string        `protobuf:"bytes,5,opt,name=creator,proto3" json:"creator,omitempty"`
	OpenType           string        `protobuf:"bytes,6,opt,name=openType,proto3" json:"openType,omitempty"`
}

func (x *WarmedModel) Reset() {
	*x = WarmedModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[88]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WarmedModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WarmedModel) ProtoMessage() {}

func (x *WarmedModel) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[88]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WarmedModel.ProtoReflect.Descriptor instead.
func (*WarmedModel) Descriptor() ([]byte, []int) {
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP(), []int{88}
}

func (x *WarmedModel) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *WarmedModel) GetModelWorkspaceName() string {
	if x != nil {
		return x.ModelWorkspaceName
	}
	return ""
}

func (x *WarmedModel) GetModelName() string {
	if x != nil {
		return x.ModelName
	}
	return ""
}

func (x *WarmedModel) GetWarmupConfig() *WarmupConfig {
	if x != nil {
		return x.WarmupConfig
	}
	return nil
}

func (x *WarmedModel) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *WarmedModel) GetOpenType() string {
	if x != nil {
		return x.OpenType
	}
	return ""
}

type GitCallbackRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ref        string              `protobuf:"bytes,1,opt,name=ref,proto3" json:"ref,omitempty"` // 分支名
	Repository *CallbackRepository `protobuf:"bytes,2,opt,name=repository,proto3" json:"repository,omitempty"`
	Pusher     *Pusher             `protobuf:"bytes,3,opt,name=pusher,proto3" json:"pusher,omitempty"`
}

func (x *GitCallbackRequest) Reset() {
	*x = GitCallbackRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[89]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GitCallbackRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GitCallbackRequest) ProtoMessage() {}

func (x *GitCallbackRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[89]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GitCallbackRequest.ProtoReflect.Descriptor instead.
func (*GitCallbackRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP(), []int{89}
}

func (x *GitCallbackRequest) GetRef() string {
	if x != nil {
		return x.Ref
	}
	return ""
}

func (x *GitCallbackRequest) GetRepository() *CallbackRepository {
	if x != nil {
		return x.Repository
	}
	return nil
}

func (x *GitCallbackRequest) GetPusher() *Pusher {
	if x != nil {
		return x.Pusher
	}
	return nil
}

type CallbackRepository struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Owner *Owner `protobuf:"bytes,1,opt,name=owner,proto3" json:"owner,omitempty"`
	Name  string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"` // 仓库名
}

func (x *CallbackRepository) Reset() {
	*x = CallbackRepository{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[90]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CallbackRepository) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CallbackRepository) ProtoMessage() {}

func (x *CallbackRepository) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[90]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CallbackRepository.ProtoReflect.Descriptor instead.
func (*CallbackRepository) Descriptor() ([]byte, []int) {
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP(), []int{90}
}

func (x *CallbackRepository) GetOwner() *Owner {
	if x != nil {
		return x.Owner
	}
	return nil
}

func (x *CallbackRepository) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type Owner struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Username string `protobuf:"bytes,1,opt,name=username,proto3" json:"username,omitempty"` // 空间名
}

func (x *Owner) Reset() {
	*x = Owner{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[91]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Owner) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Owner) ProtoMessage() {}

func (x *Owner) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[91]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Owner.ProtoReflect.Descriptor instead.
func (*Owner) Descriptor() ([]byte, []int) {
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP(), []int{91}
}

func (x *Owner) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

type Pusher struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Username string `protobuf:"bytes,1,opt,name=username,proto3" json:"username,omitempty"` // 推送用户名
}

func (x *Pusher) Reset() {
	*x = Pusher{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[92]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Pusher) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Pusher) ProtoMessage() {}

func (x *Pusher) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[92]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Pusher.ProtoReflect.Descriptor instead.
func (*Pusher) Descriptor() ([]byte, []int) {
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP(), []int{92}
}

func (x *Pusher) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

type GitCallbackResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GitCallbackResponse) Reset() {
	*x = GitCallbackResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[93]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GitCallbackResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GitCallbackResponse) ProtoMessage() {}

func (x *GitCallbackResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[93]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GitCallbackResponse.ProtoReflect.Descriptor instead.
func (*GitCallbackResponse) Descriptor() ([]byte, []int) {
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP(), []int{93}
}

type GetModelWarmupRatioRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ModelName string `protobuf:"bytes,1,opt,name=modelName,proto3" json:"modelName,omitempty"`
	Region    string `protobuf:"bytes,2,opt,name=region,proto3" json:"region,omitempty"`
}

func (x *GetModelWarmupRatioRequest) Reset() {
	*x = GetModelWarmupRatioRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[94]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetModelWarmupRatioRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetModelWarmupRatioRequest) ProtoMessage() {}

func (x *GetModelWarmupRatioRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[94]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetModelWarmupRatioRequest.ProtoReflect.Descriptor instead.
func (*GetModelWarmupRatioRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP(), []int{94}
}

func (x *GetModelWarmupRatioRequest) GetModelName() string {
	if x != nil {
		return x.ModelName
	}
	return ""
}

func (x *GetModelWarmupRatioRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

type GetModelWarmupRatioResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WarmupRatios []*WarmupRatio `protobuf:"bytes,1,rep,name=warmupRatios,proto3" json:"warmupRatios,omitempty"`
	WarmupStatus string         `protobuf:"bytes,2,opt,name=warmupStatus,proto3" json:"warmupStatus,omitempty"`
}

func (x *GetModelWarmupRatioResponse) Reset() {
	*x = GetModelWarmupRatioResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[95]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetModelWarmupRatioResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetModelWarmupRatioResponse) ProtoMessage() {}

func (x *GetModelWarmupRatioResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[95]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetModelWarmupRatioResponse.ProtoReflect.Descriptor instead.
func (*GetModelWarmupRatioResponse) Descriptor() ([]byte, []int) {
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP(), []int{95}
}

func (x *GetModelWarmupRatioResponse) GetWarmupRatios() []*WarmupRatio {
	if x != nil {
		return x.WarmupRatios
	}
	return nil
}

func (x *GetModelWarmupRatioResponse) GetWarmupStatus() string {
	if x != nil {
		return x.WarmupStatus
	}
	return ""
}

type WarmupRatio struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WarmupRegion     string `protobuf:"bytes,1,opt,name=warmupRegion,proto3" json:"warmupRegion,omitempty"`
	WarmupZone       string `protobuf:"bytes,2,opt,name=warmupZone,proto3" json:"warmupZone,omitempty"`
	WarmupRegionName string `protobuf:"bytes,3,opt,name=warmupRegionName,proto3" json:"warmupRegionName,omitempty"`
	WarmupZoneName   string `protobuf:"bytes,4,opt,name=warmupZoneName,proto3" json:"warmupZoneName,omitempty"`
	Ratio            int32  `protobuf:"varint,5,opt,name=ratio,proto3" json:"ratio,omitempty"`
}

func (x *WarmupRatio) Reset() {
	*x = WarmupRatio{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[96]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WarmupRatio) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WarmupRatio) ProtoMessage() {}

func (x *WarmupRatio) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_modelhub_v1_modelhub_proto_msgTypes[96]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WarmupRatio.ProtoReflect.Descriptor instead.
func (*WarmupRatio) Descriptor() ([]byte, []int) {
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP(), []int{96}
}

func (x *WarmupRatio) GetWarmupRegion() string {
	if x != nil {
		return x.WarmupRegion
	}
	return ""
}

func (x *WarmupRatio) GetWarmupZone() string {
	if x != nil {
		return x.WarmupZone
	}
	return ""
}

func (x *WarmupRatio) GetWarmupRegionName() string {
	if x != nil {
		return x.WarmupRegionName
	}
	return ""
}

func (x *WarmupRatio) GetWarmupZoneName() string {
	if x != nil {
		return x.WarmupZoneName
	}
	return ""
}

func (x *WarmupRatio) GetRatio() int32 {
	if x != nil {
		return x.Ratio
	}
	return 0
}

var File_aistudio_modelhub_v1_modelhub_proto protoreflect.FileDescriptor

var file_aistudio_modelhub_v1_modelhub_proto_rawDesc = []byte{
	0x0a, 0x23, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x68, 0x75, 0x62, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x68, 0x75, 0x62, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x19, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74,
	0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31,
	0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e,
	0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f,
	0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f,
	0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x13, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65,
	0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x5d, 0x0a, 0x17, 0x43, 0x68,
	0x65, 0x63, 0x6b, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x57, 0x61, 0x72, 0x6d, 0x65, 0x64, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f,
	0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x4a, 0x0a, 0x18, 0x43, 0x68, 0x65,
	0x63, 0x6b, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x57, 0x61, 0x72, 0x6d, 0x65, 0x64, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x16, 0x0a,
	0x06, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x73, 0x22, 0xed, 0x01, 0x0a, 0x11, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x46, 0x6f, 0x72, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x77,
	0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a,
	0x08, 0x6e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x6e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2c, 0x0a, 0x11, 0x66,
	0x72, 0x6f, 0x6d, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x66, 0x72, 0x6f, 0x6d, 0x57, 0x6f, 0x72, 0x6b,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x72, 0x6f,
	0x6d, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x72, 0x6f,
	0x6d, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x81, 0x01, 0x0a, 0x13, 0x53, 0x44, 0x4b, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a,
	0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x10, 0x0a, 0x03, 0x72, 0x65, 0x66, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x72, 0x65, 0x66, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x78, 0x0a, 0x14, 0x53, 0x44, 0x4b,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x10, 0x0a, 0x03, 0x73, 0x68, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x73, 0x68, 0x61, 0x12, 0x3e, 0x0a, 0x08, 0x73, 0x69, 0x62, 0x6c, 0x69, 0x6e, 0x67, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73,
	0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x68, 0x75, 0x62, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x69, 0x62, 0x6c, 0x69, 0x6e, 0x67, 0x52, 0x08, 0x73, 0x69, 0x62, 0x6c, 0x69,
	0x6e, 0x67, 0x73, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x02, 0x69, 0x64, 0x22, 0x27, 0x0a, 0x07, 0x53, 0x69, 0x62, 0x6c, 0x69, 0x6e, 0x67, 0x12, 0x1c,
	0x0a, 0x09, 0x72, 0x66, 0x69, 0x6c, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x72, 0x66, 0x69, 0x6c, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0xfc, 0x03, 0x0a,
	0x12, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a,
	0x08, 0x6e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x6e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x70,
	0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x70, 0x72,
	0x69, 0x76, 0x61, 0x74, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6c, 0x69, 0x63, 0x65, 0x6e, 0x73, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6c, 0x69, 0x63, 0x65, 0x6e, 0x73, 0x65, 0x12,
	0x16, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x64, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x72, 0x65, 0x61, 0x64, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f,
	0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12,
	0x12, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x74,
	0x61, 0x67, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x54,
	0x61, 0x67, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69,
	0x6e, 0x65, 0x54, 0x61, 0x67, 0x12, 0x20, 0x0a, 0x0b, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6c, 0x69, 0x62, 0x72,
	0x61, 0x72, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x75, 0x74, 0x68, 0x6f,
	0x72, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x12,
	0x22, 0x0a, 0x0c, 0x6c, 0x61, 0x73, 0x74, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x69, 0x65, 0x64, 0x18,
	0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6c, 0x61, 0x73, 0x74, 0x4d, 0x6f, 0x64, 0x69, 0x66,
	0x69, 0x65, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x53,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61,
	0x64, 0x73, 0x18, 0x10, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f,
	0x61, 0x64, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x69, 0x6b, 0x65, 0x73, 0x18, 0x11, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x05, 0x6c, 0x69, 0x6b, 0x65, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x75, 0x73, 0x65,
	0x64, 0x53, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b,
	0x75, 0x73, 0x65, 0x64, 0x53, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x22, 0xfc, 0x03, 0x0a, 0x12,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08,
	0x6e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x6e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x72,
	0x69, 0x76, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x70, 0x72, 0x69,
	0x76, 0x61, 0x74, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6c, 0x69, 0x63, 0x65, 0x6e, 0x73, 0x65, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6c, 0x69, 0x63, 0x65, 0x6e, 0x73, 0x65, 0x12, 0x16,
	0x0a, 0x06, 0x72, 0x65, 0x61, 0x64, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x72, 0x65, 0x61, 0x64, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x12,
	0x0a, 0x04, 0x74, 0x61, 0x67, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x74, 0x61,
	0x67, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x54, 0x61,
	0x67, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e,
	0x65, 0x54, 0x61, 0x67, 0x12, 0x20, 0x0a, 0x0b, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x4e,
	0x61, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6c, 0x69, 0x62, 0x72, 0x61,
	0x72, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x12, 0x22,
	0x0a, 0x0c, 0x6c, 0x61, 0x73, 0x74, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x69, 0x65, 0x64, 0x18, 0x0d,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6c, 0x61, 0x73, 0x74, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x69,
	0x65, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64,
	0x73, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61,
	0x64, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x69, 0x6b, 0x65, 0x73, 0x18, 0x10, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x05, 0x6c, 0x69, 0x6b, 0x65, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x75, 0x73, 0x65, 0x64,
	0x53, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x75,
	0x73, 0x65, 0x64, 0x53, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x22, 0xa7, 0x02, 0x0a, 0x11, 0x4c,
	0x69, 0x73, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x66, 0x72, 0x6f, 0x6d, 0x4f, 0x72,
	0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x66, 0x72, 0x6f, 0x6d, 0x4f, 0x72, 0x67,
	0x12, 0x18, 0x0a, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61,
	0x67, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x18,
	0x0a, 0x07, 0x70, 0x65, 0x72, 0x50, 0x61, 0x67, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x07, 0x70, 0x65, 0x72, 0x50, 0x61, 0x67, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69,
	0x6f, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e,
	0x12, 0x20, 0x0a, 0x0b, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x54, 0x61, 0x67, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x54,
	0x61, 0x67, 0x12, 0x20, 0x0a, 0x0b, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x4e, 0x61, 0x6d,
	0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79,
	0x4e, 0x61, 0x6d, 0x65, 0x22, 0x61, 0x0a, 0x11, 0x4c, 0x69, 0x73, 0x74, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x36, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20,
	0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x22, 0xf6, 0x07, 0x0a, 0x05, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x49,
	0x44, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x70, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x12, 0x18, 0x0a,
	0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x41, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64,
	0x41, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x64, 0x41, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x74, 0x68, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68, 0x12, 0x2e, 0x0a, 0x12, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x12, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x45, 0x0a, 0x0a, 0x72, 0x65, 0x70, 0x6f, 0x73,
	0x69, 0x74, 0x6f, 0x72, 0x79, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x61, 0x70,
	0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x6f,
	0x72, 0x79, 0x52, 0x0a, 0x72, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x6f, 0x72, 0x79, 0x12, 0x2a,
	0x0a, 0x10, 0x61, 0x75, 0x74, 0x68, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42, 0x72, 0x61, 0x6e,
	0x63, 0x68, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x61, 0x75, 0x74, 0x68, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x42, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x12, 0x2a, 0x0a, 0x10, 0x61, 0x75,
	0x74, 0x68, 0x43, 0x6f, 0x6c, 0x6c, 0x61, 0x62, 0x6f, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x0d,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x61, 0x75, 0x74, 0x68, 0x43, 0x6f, 0x6c, 0x6c, 0x61, 0x62,
	0x6f, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x1e, 0x0a, 0x0a, 0x61, 0x75, 0x74, 0x68, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x61, 0x75, 0x74, 0x68,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x61, 0x75, 0x74, 0x68, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x61, 0x75, 0x74, 0x68,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x61, 0x75, 0x74, 0x68, 0x57, 0x61,
	0x72, 0x6d, 0x55, 0x70, 0x18, 0x10, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x61, 0x75, 0x74, 0x68,
	0x57, 0x61, 0x72, 0x6d, 0x55, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73, 0x18, 0x11,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x70, 0x69,
	0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x54, 0x61, 0x67, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x54, 0x61, 0x67, 0x12, 0x20, 0x0a, 0x0b,
	0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x13, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16,
	0x0a, 0x06, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x12, 0x22, 0x0a, 0x0c, 0x6c, 0x61, 0x73, 0x74, 0x4d, 0x6f,
	0x64, 0x69, 0x66, 0x69, 0x65, 0x64, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6c, 0x61,
	0x73, 0x74, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x69, 0x65, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x16, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x1a, 0x0a, 0x08,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x55, 0x52, 0x4c, 0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x55, 0x52, 0x4c, 0x12, 0x1c, 0x0a, 0x09, 0x64, 0x6f, 0x77, 0x6e,
	0x6c, 0x6f, 0x61, 0x64, 0x73, 0x18, 0x18, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x64, 0x6f, 0x77,
	0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x69, 0x6b, 0x65, 0x73, 0x18,
	0x19, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x6c, 0x69, 0x6b, 0x65, 0x73, 0x12, 0x20, 0x0a, 0x0b,
	0x75, 0x73, 0x65, 0x64, 0x53, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x18, 0x1a, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0b, 0x75, 0x73, 0x65, 0x64, 0x53, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x12, 0x14,
	0x0a, 0x05, 0x69, 0x73, 0x4e, 0x65, 0x77, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x69,
	0x73, 0x4e, 0x65, 0x77, 0x12, 0x4b, 0x0a, 0x0c, 0x77, 0x61, 0x72, 0x6d, 0x75, 0x70, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x61, 0x70, 0x69,
	0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x61, 0x72, 0x6d, 0x75, 0x70, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x52, 0x0c, 0x77, 0x61, 0x72, 0x6d, 0x75, 0x70, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x12, 0x4d, 0x0a, 0x0d, 0x77, 0x61, 0x72, 0x6d, 0x65, 0x64, 0x52, 0x65, 0x67, 0x69, 0x6f,
	0x6e, 0x73, 0x18, 0x1d, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e,
	0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x68, 0x75,
	0x62, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x61, 0x72, 0x6d, 0x75, 0x70, 0x52, 0x65, 0x67, 0x69, 0x6f,
	0x6e, 0x52, 0x0d, 0x77, 0x61, 0x72, 0x6d, 0x65, 0x64, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x73,
	0x22, 0x88, 0x01, 0x0a, 0x0a, 0x52, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x6f, 0x72, 0x79, 0x12,
	0x22, 0x0a, 0x0c, 0x48, 0x54, 0x54, 0x50, 0x43, 0x6c, 0x6f, 0x6e, 0x65, 0x55, 0x52, 0x4c, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x48, 0x54, 0x54, 0x50, 0x43, 0x6c, 0x6f, 0x6e, 0x65,
	0x55, 0x52, 0x4c, 0x12, 0x34, 0x0a, 0x15, 0x48, 0x54, 0x54, 0x50, 0x43, 0x6c, 0x6f, 0x6e, 0x65,
	0x55, 0x52, 0x4c, 0x57, 0x69, 0x74, 0x68, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x15, 0x48, 0x54, 0x54, 0x50, 0x43, 0x6c, 0x6f, 0x6e, 0x65, 0x55, 0x52, 0x4c,
	0x57, 0x69, 0x74, 0x68, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x20, 0x0a, 0x0b, 0x53, 0x53, 0x48,
	0x43, 0x6c, 0x6f, 0x6e, 0x65, 0x55, 0x52, 0x4c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x53, 0x53, 0x48, 0x43, 0x6c, 0x6f, 0x6e, 0x65, 0x55, 0x52, 0x4c, 0x22, 0x66, 0x0a, 0x12, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72,
	0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67,
	0x69, 0x6f, 0x6e, 0x22, 0x64, 0x0a, 0x10, 0x53, 0x68, 0x6f, 0x77, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x22, 0x67, 0x0a, 0x11, 0x53, 0x68, 0x6f,
	0x77, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x36,
	0x0a, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e,
	0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52,
	0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x4e, 0x61,
	0x6d, 0x65, 0x22, 0x3e, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x47, 0x69, 0x74, 0x54, 0x6f, 0x6b, 0x65,
	0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x61, 0x70, 0x70, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x61, 0x70, 0x70, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65,
	0x67, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69,
	0x6f, 0x6e, 0x22, 0x68, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x47, 0x69, 0x74, 0x54, 0x6f, 0x6b, 0x65,
	0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x3f, 0x0a, 0x08, 0x67,
	0x69, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e,
	0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x69, 0x74, 0x54, 0x6f, 0x6b,
	0x65, 0x6e, 0x52, 0x08, 0x67, 0x69, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x50, 0x0a, 0x08,
	0x47, 0x69, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05,
	0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x6b,
	0x65, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x47,
	0x0a, 0x11, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x22, 0xa4, 0x01, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x54,
	0x72, 0x65, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f,
	0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68, 0x12, 0x10, 0x0a, 0x03, 0x72, 0x65, 0x66, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x72, 0x65, 0x66, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65,
	0x67, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69,
	0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x70, 0x6f, 0x54, 0x79, 0x70, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x70, 0x6f, 0x54, 0x79, 0x70, 0x65, 0x22, 0x5a,
	0x0a, 0x0f, 0x47, 0x65, 0x74, 0x54, 0x72, 0x65, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6d, 0x73, 0x67, 0x12, 0x35, 0x0a, 0x05, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64,
	0x69, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x46,
	0x69, 0x6c, 0x65, 0x52, 0x05, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x22, 0xb5, 0x02, 0x0a, 0x04, 0x46,
	0x69, 0x6c, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73,
	0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x10, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x70, 0x61, 0x74, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70,
	0x61, 0x74, 0x68, 0x12, 0x12, 0x0a, 0x04, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6d, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x6c, 0x66, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x03, 0x6c, 0x66, 0x73, 0x12, 0x26, 0x0a, 0x0e, 0x6c, 0x66, 0x73, 0x50, 0x6f, 0x69, 0x6e,
	0x74, 0x65, 0x72, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x10, 0x52, 0x0e, 0x6c,
	0x66, 0x73, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x28, 0x0a,
	0x0f, 0x6c, 0x66, 0x73, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x76, 0x65, 0x50, 0x61, 0x74, 0x68,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6c, 0x66, 0x73, 0x52, 0x65, 0x6c, 0x61, 0x74,
	0x69, 0x76, 0x65, 0x50, 0x61, 0x74, 0x68, 0x12, 0x39, 0x0a, 0x06, 0x63, 0x6f, 0x6d, 0x6d, 0x69,
	0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61,
	0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x68, 0x75, 0x62,
	0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x52, 0x06, 0x63, 0x6f, 0x6d, 0x6d,
	0x69, 0x74, 0x22, 0x96, 0x01, 0x0a, 0x06, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x12, 0x24, 0x0a,
	0x0d, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x74, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x74, 0x65, 0x72, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x74, 0x65, 0x72,
	0x45, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x63, 0x6f, 0x6d,
	0x6d, 0x69, 0x74, 0x74, 0x65, 0x72, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x24, 0x0a, 0x0d, 0x63,
	0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x74, 0x65, 0x72, 0x44, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x74, 0x65, 0x72, 0x44, 0x61, 0x74,
	0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x7a, 0x0a, 0x14, 0x47,
	0x65, 0x74, 0x4c, 0x61, 0x73, 0x74, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x10, 0x0a,
	0x03, 0x72, 0x65, 0x66, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x72, 0x65, 0x66, 0x12,
	0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x22, 0x64, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x4c, 0x61,
	0x73, 0x74, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d,
	0x73, 0x67, 0x12, 0x39, 0x0a, 0x06, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64,
	0x69, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x52, 0x06, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x22, 0xaf, 0x01,
	0x0a, 0x19, 0x47, 0x65, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x46, 0x69, 0x6c, 0x65, 0x43, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x77,
	0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x74, 0x68, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68, 0x12, 0x10, 0x0a, 0x03, 0x72, 0x65, 0x66,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x72, 0x65, 0x66, 0x12, 0x16, 0x0a, 0x06, 0x72,
	0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67,
	0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x70, 0x6f, 0x54, 0x79, 0x70, 0x65, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x70, 0x6f, 0x54, 0x79, 0x70, 0x65, 0x22,
	0x48, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x46, 0x69, 0x6c, 0x65, 0x43, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x10, 0x0a,
	0x03, 0x6d, 0x73, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12,
	0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x22, 0x9f, 0x01, 0x0a, 0x13, 0x44, 0x6f,
	0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x70,
	0x61, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68, 0x12,
	0x10, 0x0a, 0x03, 0x72, 0x65, 0x66, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x72, 0x65,
	0x66, 0x12, 0x10, 0x0a, 0x03, 0x6c, 0x66, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x03,
	0x6c, 0x66, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x22, 0x4e, 0x0a, 0x14, 0x44,
	0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x55, 0x0a, 0x15, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x48, 0x75, 0x62, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72,
	0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65,
	0x67, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69,
	0x6f, 0x6e, 0x22, 0x2a, 0x0a, 0x16, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x48, 0x75, 0x62, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x10, 0x0a, 0x03,
	0x6d, 0x73, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x22, 0x52,
	0x0a, 0x12, 0x47, 0x65, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x48, 0x75, 0x62, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72,
	0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65,
	0x67, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69,
	0x6f, 0x6e, 0x22, 0x3b, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x48, 0x75,
	0x62, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x12, 0x0a, 0x04, 0x6f,
	0x70, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x04, 0x6f, 0x70, 0x65, 0x6e, 0x22,
	0x4c, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x48, 0x75, 0x62, 0x55, 0x73,
	0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65,
	0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65,
	0x72, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x22, 0x19, 0x0a,
	0x17, 0x47, 0x65, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x48, 0x75, 0x62, 0x55, 0x73, 0x65, 0x72,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0xca, 0x01, 0x0a, 0x16, 0x41, 0x64, 0x64,
	0x43, 0x6f, 0x6c, 0x6c, 0x61, 0x62, 0x6f, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x65, 0x70,
	0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x72, 0x65, 0x70, 0x6f, 0x12, 0x22, 0x0a,
	0x0c, 0x63, 0x6f, 0x6c, 0x6c, 0x61, 0x62, 0x6f, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x6f, 0x6c, 0x6c, 0x61, 0x62, 0x6f, 0x72, 0x61, 0x74, 0x6f,
	0x72, 0x12, 0x1e, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x4d, 0x6f, 0x64, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x4d, 0x6f, 0x64,
	0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x70,
	0x6f, 0x54, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x70,
	0x6f, 0x54, 0x79, 0x70, 0x65, 0x22, 0x43, 0x0a, 0x17, 0x41, 0x64, 0x64, 0x43, 0x6f, 0x6c, 0x6c,
	0x61, 0x62, 0x6f, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x22, 0xad, 0x01, 0x0a, 0x19, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x6f, 0x6c, 0x6c, 0x61, 0x62, 0x6f, 0x72, 0x61, 0x74, 0x6f,
	0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x72, 0x65, 0x70, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x72, 0x65,
	0x70, 0x6f, 0x12, 0x22, 0x0a, 0x0c, 0x63, 0x6f, 0x6c, 0x6c, 0x61, 0x62, 0x6f, 0x72, 0x61, 0x74,
	0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x6f, 0x6c, 0x6c, 0x61, 0x62,
	0x6f, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x1a,
	0x0a, 0x08, 0x72, 0x65, 0x70, 0x6f, 0x54, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x72, 0x65, 0x70, 0x6f, 0x54, 0x79, 0x70, 0x65, 0x22, 0xcd, 0x01, 0x0a, 0x19, 0x43,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x43, 0x6f, 0x6c, 0x6c, 0x61, 0x62, 0x6f, 0x72, 0x61, 0x74, 0x6f,
	0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x72, 0x65, 0x70, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x72, 0x65,
	0x70, 0x6f, 0x12, 0x22, 0x0a, 0x0c, 0x63, 0x6f, 0x6c, 0x6c, 0x61, 0x62, 0x6f, 0x72, 0x61, 0x74,
	0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x6f, 0x6c, 0x6c, 0x61, 0x62,
	0x6f, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x1e, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x4d, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x63, 0x63, 0x65,
	0x73, 0x73, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x1a,
	0x0a, 0x08, 0x72, 0x65, 0x70, 0x6f, 0x54, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x72, 0x65, 0x70, 0x6f, 0x54, 0x79, 0x70, 0x65, 0x22, 0xd9, 0x01, 0x0a, 0x17, 0x4c,
	0x69, 0x73, 0x74, 0x43, 0x6f, 0x6c, 0x6c, 0x61, 0x62, 0x6f, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77,
	0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x72, 0x65, 0x70, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x72, 0x65, 0x70, 0x6f,
	0x12, 0x22, 0x0a, 0x0c, 0x63, 0x6f, 0x6c, 0x6c, 0x61, 0x62, 0x6f, 0x72, 0x61, 0x74, 0x6f, 0x72,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x6f, 0x6c, 0x6c, 0x61, 0x62, 0x6f, 0x72,
	0x61, 0x74, 0x6f, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x65, 0x72, 0x50,
	0x61, 0x67, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x70, 0x65, 0x72, 0x50, 0x61,
	0x67, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65,
	0x70, 0x6f, 0x54, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65,
	0x70, 0x6f, 0x54, 0x79, 0x70, 0x65, 0x22, 0x6f, 0x0a, 0x18, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x6f,
	0x6c, 0x6c, 0x61, 0x62, 0x6f, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x3d, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f,
	0x6c, 0x6c, 0x61, 0x62, 0x6f, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d,
	0x73, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x7c, 0x0a, 0x0c, 0x43, 0x6f, 0x6c, 0x6c, 0x61,
	0x62, 0x6f, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x6f, 0x77, 0x6e, 0x65, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x12, 0x12, 0x0a,
	0x04, 0x72, 0x65, 0x70, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x72, 0x65, 0x70,
	0x6f, 0x12, 0x22, 0x0a, 0x0c, 0x63, 0x6f, 0x6c, 0x6c, 0x61, 0x62, 0x6f, 0x72, 0x61, 0x74, 0x6f,
	0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x6f, 0x6c, 0x6c, 0x61, 0x62, 0x6f,
	0x72, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x1e, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x4d,
	0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x63, 0x63, 0x65, 0x73,
	0x73, 0x4d, 0x6f, 0x64, 0x65, 0x22, 0x6c, 0x0a, 0x18, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x49, 0x73, 0x45, 0x78, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f,
	0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72,
	0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67,
	0x69, 0x6f, 0x6e, 0x22, 0x4b, 0x0a, 0x19, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x49, 0x73, 0x45, 0x78, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73,
	0x22, 0x56, 0x0a, 0x16, 0x49, 0x6e, 0x69, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x48, 0x75, 0x62,
	0x4f, 0x72, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f,
	0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x22, 0x8c, 0x01, 0x0a, 0x1c, 0x43, 0x68, 0x65,
	0x63, 0x6b, 0x43, 0x6f, 0x6c, 0x6c, 0x61, 0x62, 0x6f, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x41, 0x75,
	0x74, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72,
	0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x65, 0x70, 0x6f, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x72, 0x65, 0x70, 0x6f, 0x12, 0x1a, 0x0a, 0x08, 0x6e,
	0x65, 0x65, 0x64, 0x41, 0x75, 0x74, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6e,
	0x65, 0x65, 0x64, 0x41, 0x75, 0x74, 0x68, 0x22, 0x4d, 0x0a, 0x1d, 0x43, 0x68, 0x65, 0x63, 0x6b,
	0x43, 0x6f, 0x6c, 0x6c, 0x61, 0x62, 0x6f, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x41, 0x75, 0x74, 0x68,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x1a, 0x0a, 0x08, 0x68, 0x61,
	0x76, 0x65, 0x41, 0x75, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x68, 0x61,
	0x76, 0x65, 0x41, 0x75, 0x74, 0x68, 0x22, 0x79, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a,
	0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x10, 0x0a, 0x03, 0x72, 0x65, 0x66, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x72, 0x65,
	0x66, 0x22, 0x68, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x3e, 0x0a, 0x08, 0x73,
	0x69, 0x62, 0x6c, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e,
	0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x44, 0x4b, 0x46, 0x69, 0x6c,
	0x65, 0x52, 0x08, 0x73, 0x69, 0x62, 0x6c, 0x69, 0x6e, 0x67, 0x73, 0x22, 0x25, 0x0a, 0x07, 0x53,
	0x44, 0x4b, 0x46, 0x69, 0x6c, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x22, 0xac, 0x01, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x54, 0x61, 0x67, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f,
	0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x70,
	0x65, 0x72, 0x50, 0x61, 0x67, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x70, 0x65,
	0x72, 0x50, 0x61, 0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x70, 0x6f, 0x54, 0x79, 0x70,
	0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x70, 0x6f, 0x54, 0x79, 0x70,
	0x65, 0x22, 0x58, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x54, 0x61, 0x67, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x33, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74,
	0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31,
	0x2e, 0x54, 0x61, 0x67, 0x73, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x22, 0x1a, 0x0a, 0x04, 0x54,
	0x61, 0x67, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0xb0, 0x01, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x42,
	0x72, 0x61, 0x6e, 0x63, 0x68, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24,
	0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69,
	0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e,
	0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04,
	0x70, 0x61, 0x67, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x65, 0x72, 0x50, 0x61, 0x67, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x70, 0x65, 0x72, 0x50, 0x61, 0x67, 0x65, 0x12, 0x1a,
	0x0a, 0x08, 0x72, 0x65, 0x70, 0x6f, 0x54, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x72, 0x65, 0x70, 0x6f, 0x54, 0x79, 0x70, 0x65, 0x22, 0x66, 0x0a, 0x13, 0x47, 0x65,
	0x74, 0x42, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6d, 0x73, 0x67, 0x12, 0x3d, 0x0a, 0x08, 0x62, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x65, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73,
	0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x68, 0x75, 0x62, 0x2e, 0x76,
	0x31, 0x2e, 0x42, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x52, 0x08, 0x62, 0x72, 0x61, 0x6e, 0x63, 0x68,
	0x65, 0x73, 0x22, 0x34, 0x0a, 0x06, 0x42, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x16, 0x0a, 0x06, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x22, 0xa1, 0x01, 0x0a, 0x13, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x42, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x62, 0x72,
	0x61, 0x6e, 0x63, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x62, 0x72, 0x61, 0x6e,
	0x63, 0x68, 0x12, 0x1c, 0x0a, 0x09, 0x6f, 0x6c, 0x64, 0x42, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6f, 0x6c, 0x64, 0x42, 0x72, 0x61, 0x6e, 0x63, 0x68,
	0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x70, 0x6f, 0x54, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x70, 0x6f, 0x54, 0x79, 0x70, 0x65, 0x22, 0x48, 0x0a, 0x14,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x53, 0x75, 0x63, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x53, 0x75, 0x63, 0x63, 0x22, 0xa8, 0x01, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x41, 0x6c,
	0x6c, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a,
	0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x72, 0x65, 0x66, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x72, 0x65, 0x66, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x74,
	0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68, 0x12, 0x16, 0x0a,
	0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72,
	0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x70, 0x6f, 0x54, 0x79, 0x70,
	0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x70, 0x6f, 0x54, 0x79, 0x70,
	0x65, 0x22, 0x5e, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x46, 0x69, 0x6c, 0x65, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x35, 0x0a, 0x05, 0x66, 0x69,
	0x6c, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x73,
	0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x68,
	0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x05, 0x66, 0x69, 0x6c, 0x65,
	0x73, 0x22, 0x9a, 0x01, 0x0a, 0x12, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x42, 0x72, 0x61, 0x6e, 0x63,
	0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x62, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x62, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65,
	0x67, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69,
	0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x70, 0x6f, 0x54, 0x79, 0x70, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x70, 0x6f, 0x54, 0x79, 0x70, 0x65, 0x22, 0x45,
	0x0a, 0x13, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x42, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x12, 0x16, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xc4, 0x01, 0x0a, 0x10, 0x53, 0x79, 0x6e, 0x63, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f,
	0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05,
	0x66, 0x6f, 0x72, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x66, 0x6f, 0x72,
	0x63, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x68, 0x66,
	0x55, 0x73, 0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x68, 0x66, 0x55, 0x73,
	0x65, 0x72, 0x12, 0x18, 0x0a, 0x07, 0x68, 0x66, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x68, 0x66, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x7b, 0x0a, 0x11,
	0x53, 0x79, 0x6e, 0x63, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6d, 0x73, 0x67, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3c, 0x0a, 0x04, 0x64,
	0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x61, 0x70, 0x69, 0x73,
	0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x68,
	0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x79, 0x6e, 0x63, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x44,
	0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x29, 0x0a, 0x0d, 0x53, 0x79, 0x6e,
	0x63, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x44, 0x61, 0x74, 0x61, 0x12, 0x18, 0x0a, 0x07, 0x6a, 0x6f,
	0x62, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6a, 0x6f, 0x62,
	0x4e, 0x61, 0x6d, 0x65, 0x22, 0x46, 0x0a, 0x18, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x46, 0x6f, 0x72, 0x53, 0x79, 0x6e, 0x63, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x41, 0x0a, 0x19,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x46, 0x6f, 0x72, 0x53, 0x79, 0x6e,
	0x63, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x12, 0x0a, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x22,
	0x88, 0x02, 0x0a, 0x1a, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x79, 0x6e, 0x63, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24,
	0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x6f, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x6f, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69,
	0x7a, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69,
	0x7a, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6a, 0x6f, 0x62, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x6a, 0x6f, 0x62, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xa3, 0x01, 0x0a, 0x1b, 0x4c,
	0x69, 0x73, 0x74, 0x53, 0x79, 0x6e, 0x63, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73,
	0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x16, 0x0a, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x44, 0x0a, 0x07, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73,
	0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x68, 0x75, 0x62, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x79, 0x6e, 0x63, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x52, 0x07, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x22, 0xb3, 0x05, 0x0a, 0x0f, 0x53, 0x79, 0x6e, 0x63, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72,
	0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65,
	0x67, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69,
	0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x4e,
	0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1e, 0x0a, 0x0a,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x6f, 0x64, 0x4e, 0x61, 0x6d,
	0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x6f, 0x64, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x18, 0x0a, 0x07, 0x6a, 0x6f, 0x62, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x6a, 0x6f, 0x62, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x66, 0x6f,
	0x72, 0x63, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x66, 0x6f, 0x72, 0x63, 0x65,
	0x12, 0x16, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x68, 0x66, 0x55, 0x73,
	0x65, 0x72, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x68, 0x66, 0x55, 0x73, 0x65, 0x72,
	0x12, 0x18, 0x0a, 0x07, 0x68, 0x66, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x0e, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x68, 0x66, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65,
	0x61, 0x73, 0x6f, 0x6e, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x61, 0x73,
	0x6f, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6d, 0x73, 0x67, 0x12, 0x48, 0x0a, 0x0a, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x18, 0x11, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e,
	0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x68, 0x75,
	0x62, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x79, 0x6e, 0x63, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x0a, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x18,
	0x0a, 0x07, 0x6c, 0x6f, 0x67, 0x46, 0x72, 0x6f, 0x6d, 0x18, 0x12, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x07, 0x6c, 0x6f, 0x67, 0x46, 0x72, 0x6f, 0x6d, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x6f, 0x67, 0x54,
	0x6f, 0x18, 0x13, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x6c, 0x6f, 0x67, 0x54, 0x6f, 0x12, 0x1a,
	0x0a, 0x08, 0x73, 0x79, 0x6e, 0x63, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x73, 0x79, 0x6e, 0x63, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x6e, 0x64, 0x54,
	0x69, 0x6d, 0x65, 0x18, 0x16, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x55, 0x52, 0x4c, 0x18, 0x17,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x55, 0x52, 0x4c, 0x12, 0x26,
	0x0a, 0x0e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x55, 0x52, 0x4c,
	0x18, 0x18, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x55, 0x52, 0x4c, 0x22, 0x9d, 0x01, 0x0a, 0x0d, 0x53, 0x79, 0x6e, 0x63, 0x43,
	0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x2e, 0x0a, 0x12, 0x6c, 0x61, 0x73, 0x74, 0x54, 0x72, 0x61, 0x6e,
	0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x12, 0x6c, 0x61, 0x73, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x18, 0x0a, 0x07,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x6e, 0x0a, 0x14, 0x53, 0x74, 0x6f, 0x70, 0x53, 0x79,
	0x6e, 0x63, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x18,
	0x0a, 0x07, 0x6a, 0x6f, 0x62, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x6a, 0x6f, 0x62, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16,
	0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x22, 0x41, 0x0a, 0x15, 0x53, 0x74, 0x6f, 0x70, 0x53, 0x79,
	0x6e, 0x63, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73,
	0x67, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x8e, 0x02, 0x0a, 0x19, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x57, 0x61, 0x72, 0x6d, 0x75, 0x70, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a,
	0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72,
	0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x2e, 0x0a, 0x12, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x57, 0x6f,
	0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x12, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x4b, 0x0a, 0x0c, 0x77, 0x61, 0x72, 0x6d, 0x75, 0x70, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x73,
	0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x68,
	0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x61, 0x72, 0x6d, 0x75, 0x70, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x52, 0x0c, 0x77, 0x61, 0x72, 0x6d, 0x75, 0x70, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x12, 0x18, 0x0a, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x22, 0x8f, 0x01, 0x0a, 0x0c, 0x57,
	0x61, 0x72, 0x6d, 0x75, 0x70, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x18, 0x0a, 0x07, 0x65,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x65, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x62, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x62, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x12, 0x4d, 0x0a,
	0x0d, 0x77, 0x61, 0x72, 0x6d, 0x75, 0x70, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74,
	0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31,
	0x2e, 0x57, 0x61, 0x72, 0x6d, 0x75, 0x70, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x52, 0x0d, 0x77,
	0x61, 0x72, 0x6d, 0x75, 0x70, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0xa6, 0x01, 0x0a,
	0x0c, 0x57, 0x61, 0x72, 0x6d, 0x75, 0x70, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x22, 0x0a,
	0x0c, 0x77, 0x61, 0x72, 0x6d, 0x75, 0x70, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x77, 0x61, 0x72, 0x6d, 0x75, 0x70, 0x52, 0x65, 0x67, 0x69, 0x6f,
	0x6e, 0x12, 0x2a, 0x0a, 0x10, 0x77, 0x61, 0x72, 0x6d, 0x75, 0x70, 0x52, 0x65, 0x67, 0x69, 0x6f,
	0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x77, 0x61, 0x72,
	0x6d, 0x75, 0x70, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1e, 0x0a,
	0x0a, 0x77, 0x61, 0x72, 0x6d, 0x75, 0x70, 0x5a, 0x6f, 0x6e, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x77, 0x61, 0x72, 0x6d, 0x75, 0x70, 0x5a, 0x6f, 0x6e, 0x65, 0x12, 0x26, 0x0a,
	0x0e, 0x77, 0x61, 0x72, 0x6d, 0x75, 0x70, 0x5a, 0x6f, 0x6e, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x77, 0x61, 0x72, 0x6d, 0x75, 0x70, 0x5a, 0x6f, 0x6e,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x46, 0x0a, 0x1a, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x57,
	0x61, 0x72, 0x6d, 0x75, 0x70, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xa4, 0x02,
	0x0a, 0x15, 0x53, 0x74, 0x61, 0x72, 0x74, 0x57, 0x61, 0x72, 0x6d, 0x75, 0x70, 0x4a, 0x6f, 0x62,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a,
	0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72,
	0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x2e, 0x0a, 0x12, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x57, 0x6f,
	0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x12, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x62, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x62, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x12, 0x4d, 0x0a, 0x0d, 0x77,
	0x61, 0x72, 0x6d, 0x75, 0x70, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x06, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64,
	0x69, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x57,
	0x61, 0x72, 0x6d, 0x75, 0x70, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x52, 0x0d, 0x77, 0x61, 0x72,
	0x6d, 0x75, 0x70, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x6f, 0x72, 0x22, 0x32, 0x0a, 0x16, 0x53, 0x74, 0x61, 0x72, 0x74, 0x57, 0x61, 0x72,
	0x6d, 0x75, 0x70, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18,
	0x0a, 0x07, 0x6a, 0x6f, 0x62, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x6a, 0x6f, 0x62, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xbc, 0x01, 0x0a, 0x14, 0x53, 0x74, 0x6f,
	0x70, 0x57, 0x61, 0x72, 0x6d, 0x75, 0x70, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f,
	0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12,
	0x2e, 0x0a, 0x12, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x1c, 0x0a, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a,
	0x07, 0x6a, 0x6f, 0x62, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x6a, 0x6f, 0x62, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x17, 0x0a, 0x15, 0x53, 0x74, 0x6f, 0x70, 0x57,
	0x61, 0x72, 0x6d, 0x75, 0x70, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x8a, 0x02, 0x0a, 0x18, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x61, 0x72, 0x6d, 0x75, 0x70, 0x52,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a,
	0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x2e, 0x0a, 0x12, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x57, 0x6f,
	0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6a, 0x6f, 0x62,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6a, 0x6f, 0x62, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x12, 0x0a,
	0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67,
	0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x22, 0x9e, 0x01,
	0x0a, 0x19, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x61, 0x72, 0x6d, 0x75, 0x70, 0x52, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d,
	0x73, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x16, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x41, 0x0a, 0x07, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73,
	0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69,
	0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x68, 0x75, 0x62, 0x2e,
	0x76, 0x31, 0x2e, 0x57, 0x61, 0x72, 0x6d, 0x75, 0x70, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52,
	0x07, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x22, 0xd8,
	0x03, 0x0a, 0x0c, 0x57, 0x61, 0x72, 0x6d, 0x75, 0x70, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12,
	0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x2e, 0x0a,
	0x12, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a,
	0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6a,
	0x6f, 0x62, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6a, 0x6f,
	0x62, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x4b, 0x0a, 0x0c, 0x77, 0x61, 0x72, 0x6d, 0x75, 0x70, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x61, 0x70,
	0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x61, 0x72, 0x6d, 0x75, 0x70, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x52, 0x0c, 0x77, 0x61, 0x72, 0x6d, 0x75, 0x70, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x1e, 0x0a, 0x0a,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x4b, 0x0a, 0x0c,
	0x77, 0x61, 0x72, 0x6d, 0x75, 0x70, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64,
	0x69, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x57,
	0x61, 0x72, 0x6d, 0x75, 0x70, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0c, 0x77, 0x61, 0x72,
	0x6d, 0x75, 0x70, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x6c, 0x6f, 0x67,
	0x46, 0x72, 0x6f, 0x6d, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x6c, 0x6f, 0x67, 0x46,
	0x72, 0x6f, 0x6d, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x6f, 0x67, 0x54, 0x6f, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x05, 0x6c, 0x6f, 0x67, 0x54, 0x6f, 0x22, 0xad, 0x02, 0x0a, 0x0c, 0x57, 0x61,
	0x72, 0x6d, 0x75, 0x70, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x12, 0x18,
	0x0a, 0x07, 0x70, 0x6f, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x70, 0x6f, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d,
	0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61, 0x73, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x73, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x4b, 0x0a, 0x0a,
	0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x2b, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x61, 0x72,
	0x6d, 0x75, 0x70, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x0a, 0x63,
	0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0xa0, 0x01, 0x0a, 0x10, 0x57, 0x61,
	0x72, 0x6d, 0x75, 0x70, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x12,
	0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2e, 0x0a, 0x12, 0x6c, 0x61,
	0x73, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x69, 0x6d, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x6c, 0x61, 0x73, 0x74, 0x54, 0x72, 0x61, 0x6e,
	0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65,
	0x61, 0x73, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x61, 0x73,
	0x6f, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0xa3, 0x01, 0x0a,
	0x17, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x61, 0x72, 0x6d, 0x65, 0x64, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16,
	0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x6f, 0x70, 0x65, 0x6e, 0x54, 0x79,
	0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6f, 0x70, 0x65, 0x6e, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69,
	0x7a, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69,
	0x7a, 0x65, 0x22, 0x70, 0x0a, 0x18, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x61, 0x72, 0x6d, 0x65, 0x64,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3e,
	0x0a, 0x06, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26,
	0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x61, 0x72, 0x6d, 0x65,
	0x64, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x06, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x12, 0x14,
	0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x22, 0xf6, 0x01, 0x0a, 0x0b, 0x57, 0x61, 0x72, 0x6d, 0x65, 0x64, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x2e, 0x0a, 0x12,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x57,
	0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x4b, 0x0a, 0x0c, 0x77, 0x61,
	0x72, 0x6d, 0x75, 0x70, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x61, 0x72,
	0x6d, 0x75, 0x70, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0c, 0x77, 0x61, 0x72, 0x6d, 0x75,
	0x70, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x6f, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f,
	0x72, 0x12, 0x1a, 0x0a, 0x08, 0x6f, 0x70, 0x65, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x6f, 0x70, 0x65, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x22, 0xb0, 0x01,
	0x0a, 0x12, 0x47, 0x69, 0x74, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x72, 0x65, 0x66, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x72, 0x65, 0x66, 0x12, 0x4d, 0x0a, 0x0a, 0x72, 0x65, 0x70, 0x6f, 0x73, 0x69,
	0x74, 0x6f, 0x72, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x61, 0x70, 0x69,
	0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x52,
	0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x0a, 0x72, 0x65, 0x70, 0x6f, 0x73,
	0x69, 0x74, 0x6f, 0x72, 0x79, 0x12, 0x39, 0x0a, 0x06, 0x70, 0x75, 0x73, 0x68, 0x65, 0x72, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73,
	0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x68, 0x75, 0x62, 0x2e, 0x76,
	0x31, 0x2e, 0x50, 0x75, 0x73, 0x68, 0x65, 0x72, 0x52, 0x06, 0x70, 0x75, 0x73, 0x68, 0x65, 0x72,
	0x22, 0x60, 0x0a, 0x12, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x70, 0x6f,
	0x73, 0x69, 0x74, 0x6f, 0x72, 0x79, 0x12, 0x36, 0x0a, 0x05, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73,
	0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x68, 0x75, 0x62, 0x2e, 0x76,
	0x31, 0x2e, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x52, 0x05, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x22, 0x23, 0x0a, 0x05, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x12, 0x1a, 0x0a, 0x08, 0x75,
	0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75,
	0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x24, 0x0a, 0x06, 0x50, 0x75, 0x73, 0x68, 0x65,
	0x72, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x15, 0x0a,
	0x13, 0x47, 0x69, 0x74, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x52, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x57, 0x61, 0x72, 0x6d, 0x75, 0x70, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x22, 0x8d, 0x01, 0x0a, 0x1b, 0x47, 0x65, 0x74,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x57, 0x61, 0x72, 0x6d, 0x75, 0x70, 0x52, 0x61, 0x74, 0x69, 0x6f,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4a, 0x0a, 0x0c, 0x77, 0x61, 0x72, 0x6d,
	0x75, 0x70, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26,
	0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x61, 0x72, 0x6d, 0x75,
	0x70, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x52, 0x0c, 0x77, 0x61, 0x72, 0x6d, 0x75, 0x70, 0x52, 0x61,
	0x74, 0x69, 0x6f, 0x73, 0x12, 0x22, 0x0a, 0x0c, 0x77, 0x61, 0x72, 0x6d, 0x75, 0x70, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x77, 0x61, 0x72, 0x6d,
	0x75, 0x70, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xbb, 0x01, 0x0a, 0x0b, 0x57, 0x61, 0x72,
	0x6d, 0x75, 0x70, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x12, 0x22, 0x0a, 0x0c, 0x77, 0x61, 0x72, 0x6d,
	0x75, 0x70, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x77, 0x61, 0x72, 0x6d, 0x75, 0x70, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x1e, 0x0a, 0x0a,
	0x77, 0x61, 0x72, 0x6d, 0x75, 0x70, 0x5a, 0x6f, 0x6e, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x77, 0x61, 0x72, 0x6d, 0x75, 0x70, 0x5a, 0x6f, 0x6e, 0x65, 0x12, 0x2a, 0x0a, 0x10,
	0x77, 0x61, 0x72, 0x6d, 0x75, 0x70, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x77, 0x61, 0x72, 0x6d, 0x75, 0x70, 0x52, 0x65,
	0x67, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x77, 0x61, 0x72, 0x6d,
	0x75, 0x70, 0x5a, 0x6f, 0x6e, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0e, 0x77, 0x61, 0x72, 0x6d, 0x75, 0x70, 0x5a, 0x6f, 0x6e, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x14, 0x0a, 0x05, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x05, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x32, 0x8d, 0x2f, 0x0a, 0x0f, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x48, 0x75, 0x62, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x79, 0x0a, 0x0b, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x2d, 0x2e, 0x61, 0x70, 0x69, 0x73,
	0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x68,
	0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79,
	0x22, 0x23, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1d, 0x3a, 0x01, 0x2a, 0x22, 0x18, 0x2f, 0x61, 0x70,
	0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2d, 0x68, 0x75, 0x62, 0x2f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x8b, 0x01, 0x0a, 0x0a, 0x4c, 0x69, 0x73, 0x74, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x12, 0x2c, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74,
	0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x4f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x1a, 0x2c, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64,
	0x69, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x21, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1b, 0x12, 0x19, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f,
	0x76, 0x31, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2d, 0x68, 0x75, 0x62, 0x2f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x12, 0x79, 0x0a, 0x0b, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x12, 0x2d, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64,
	0x69, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x23, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x1d, 0x3a, 0x01, 0x2a, 0x1a, 0x18, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x2d, 0x68, 0x75, 0x62, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x76,
	0x0a, 0x0b, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x2d, 0x2e,
	0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x22, 0x20, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1a, 0x2a, 0x18, 0x2f, 0x61,
	0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2d, 0x68, 0x75, 0x62,
	0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x88, 0x01, 0x0a, 0x09, 0x53, 0x68, 0x6f, 0x77, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x12, 0x2b, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74,
	0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31,
	0x2e, 0x53, 0x68, 0x6f, 0x77, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x2c, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x68,
	0x6f, 0x77, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x20, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1a, 0x12, 0x18, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76,
	0x31, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2d, 0x68, 0x75, 0x62, 0x2f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x12, 0x8e, 0x01, 0x0a, 0x0b, 0x47, 0x65, 0x74, 0x47, 0x69, 0x74, 0x54, 0x6f, 0x6b, 0x65,
	0x6e, 0x12, 0x2d, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65,
	0x74, 0x47, 0x69, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74,
	0x47, 0x69, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x20, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1a, 0x12, 0x18, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f,
	0x76, 0x31, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2d, 0x68, 0x75, 0x62, 0x2f, 0x74, 0x6f, 0x6b,
	0x65, 0x6e, 0x12, 0xa3, 0x01, 0x0a, 0x0e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x48, 0x75, 0x62, 0x12, 0x30, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73,
	0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x68, 0x75, 0x62, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x48, 0x75, 0x62,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x31, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61,
	0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x68, 0x75, 0x62,
	0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x48,
	0x75, 0x62, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x2c, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x26, 0x3a, 0x01, 0x2a, 0x22, 0x21, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2d, 0x68, 0x75, 0x62, 0x2f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x68, 0x75, 0x62, 0x12, 0x94, 0x01, 0x0a, 0x0b, 0x47, 0x65, 0x74,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x48, 0x75, 0x62, 0x12, 0x2d, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e,
	0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x68, 0x75,
	0x62, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x48, 0x75, 0x62,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61,
	0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x68, 0x75, 0x62,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x48, 0x75, 0x62, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x26, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x20, 0x12,
	0x1e, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2d,
	0x68, 0x75, 0x62, 0x2f, 0x67, 0x65, 0x74, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x68, 0x75, 0x62, 0x12,
	0x81, 0x01, 0x0a, 0x07, 0x47, 0x65, 0x74, 0x54, 0x72, 0x65, 0x65, 0x12, 0x29, 0x2e, 0x61, 0x70,
	0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x72, 0x65, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2a, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69,
	0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x68, 0x75, 0x62, 0x2e,
	0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x72, 0x65, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x1f, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x19, 0x12, 0x17, 0x2f, 0x61, 0x70, 0x69,
	0x73, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2d, 0x68, 0x75, 0x62, 0x2f, 0x74,
	0x72, 0x65, 0x65, 0x12, 0x99, 0x01, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x4c, 0x61, 0x73, 0x74, 0x43,
	0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x12, 0x2f, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73,
	0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x68, 0x75, 0x62, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x61, 0x73, 0x74, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x30, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69,
	0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x68, 0x75, 0x62, 0x2e,
	0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x61, 0x73, 0x74, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x25, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1f,
	0x12, 0x1d, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x2d, 0x68, 0x75, 0x62, 0x2f, 0x6c, 0x61, 0x73, 0x74, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x12,
	0xb0, 0x01, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x46, 0x69, 0x6c, 0x65, 0x43,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x34, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69,
	0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x68, 0x75, 0x62, 0x2e,
	0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x46, 0x69, 0x6c, 0x65, 0x43, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x35, 0x2e, 0x61,
	0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x70, 0x6f,
	0x46, 0x69, 0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x2d, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x27, 0x12, 0x25, 0x2f, 0x61, 0x70,
	0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2d, 0x68, 0x75, 0x62, 0x2f,
	0x67, 0x65, 0x74, 0x72, 0x65, 0x70, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x63, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x12, 0x76, 0x0a, 0x0a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72,
	0x12, 0x2c, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x22, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1c, 0x3a, 0x01,
	0x2a, 0x22, 0x17, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x2d, 0x68, 0x75, 0x62, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x12, 0xa4, 0x01, 0x0a, 0x0f, 0x41,
	0x64, 0x64, 0x43, 0x6f, 0x6c, 0x6c, 0x61, 0x62, 0x6f, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x31,
	0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x64, 0x43, 0x6f,
	0x6c, 0x6c, 0x61, 0x62, 0x6f, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x32, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64,
	0x64, 0x43, 0x6f, 0x6c, 0x6c, 0x61, 0x62, 0x6f, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x2a, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x24, 0x3a, 0x01, 0x2a,
	0x22, 0x1f, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x2d, 0x68, 0x75, 0x62, 0x2f, 0x63, 0x6f, 0x6c, 0x6c, 0x61, 0x62, 0x6f, 0x72, 0x61, 0x74, 0x6f,
	0x72, 0x12, 0x8b, 0x01, 0x0a, 0x12, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x6f, 0x6c, 0x6c,
	0x61, 0x62, 0x6f, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x34, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e,
	0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x68, 0x75,
	0x62, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x6f, 0x6c, 0x6c, 0x61,
	0x62, 0x6f, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x27, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x21, 0x2a, 0x1f,
	0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2d, 0x68,
	0x75, 0x62, 0x2f, 0x63, 0x6f, 0x6c, 0x6c, 0x61, 0x62, 0x6f, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x12,
	0x8e, 0x01, 0x0a, 0x12, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x43, 0x6f, 0x6c, 0x6c, 0x61, 0x62,
	0x6f, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x34, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69,
	0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x68, 0x75, 0x62, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x43, 0x6f, 0x6c, 0x6c, 0x61, 0x62, 0x6f,
	0x72, 0x61, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x22, 0x2a, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x24, 0x3a, 0x01, 0x2a, 0x1a,
	0x1f, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2d,
	0x68, 0x75, 0x62, 0x2f, 0x63, 0x6f, 0x6c, 0x6c, 0x61, 0x62, 0x6f, 0x72, 0x61, 0x74, 0x6f, 0x72,
	0x12, 0xa4, 0x01, 0x0a, 0x10, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x6f, 0x6c, 0x6c, 0x61, 0x62, 0x6f,
	0x72, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x32, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73,
	0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x68, 0x75, 0x62, 0x2e, 0x76,
	0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x6f, 0x6c, 0x6c, 0x61, 0x62, 0x6f, 0x72, 0x61, 0x74,
	0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x33, 0x2e, 0x61, 0x70, 0x69, 0x73,
	0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x68,
	0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x6f, 0x6c, 0x6c, 0x61, 0x62,
	0x6f, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x27,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x21, 0x12, 0x1f, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31,
	0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2d, 0x68, 0x75, 0x62, 0x2f, 0x63, 0x6f, 0x6c, 0x6c, 0x61,
	0x62, 0x6f, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x12, 0xa6, 0x01, 0x0a, 0x11, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x73, 0x45, 0x78, 0x69, 0x73, 0x74, 0x12, 0x33, 0x2e,
	0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x49, 0x73, 0x45, 0x78, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x34, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64,
	0x69, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x73, 0x45, 0x78, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x26, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x20,
	0x12, 0x1e, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x2d, 0x68, 0x75, 0x62, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x65, 0x78, 0x69, 0x73, 0x74,
	0x12, 0xbe, 0x01, 0x0a, 0x15, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x43, 0x6f, 0x6c, 0x6c, 0x61, 0x62,
	0x6f, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x41, 0x75, 0x74, 0x68, 0x12, 0x37, 0x2e, 0x61, 0x70, 0x69,
	0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x43, 0x6f, 0x6c, 0x6c,
	0x61, 0x62, 0x6f, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x41, 0x75, 0x74, 0x68, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x38, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75,
	0x64, 0x69, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x43, 0x6f, 0x6c, 0x6c, 0x61, 0x62, 0x6f, 0x72, 0x61, 0x74, 0x6f,
	0x72, 0x41, 0x75, 0x74, 0x68, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x32, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x2c, 0x12, 0x2a, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2d, 0x68, 0x75, 0x62, 0x2f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x5f,
	0x63, 0x6f, 0x6c, 0x6c, 0x61, 0x62, 0x6f, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x61, 0x75, 0x74,
	0x68, 0x12, 0x8e, 0x01, 0x0a, 0x0f, 0x49, 0x6e, 0x69, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x48,
	0x75, 0x62, 0x4f, 0x72, 0x67, 0x12, 0x31, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73,
	0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x68, 0x75, 0x62, 0x2e, 0x76,
	0x31, 0x2e, 0x49, 0x6e, 0x69, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x48, 0x75, 0x62, 0x4f, 0x72,
	0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79,
	0x22, 0x30, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2a, 0x3a, 0x01, 0x2a, 0x22, 0x25, 0x2f, 0x61, 0x70,
	0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2d, 0x68, 0x75, 0x62, 0x2f,
	0x69, 0x6e, 0x69, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x68, 0x75, 0x62, 0x5f, 0x6f,
	0x72, 0x67, 0x12, 0x9a, 0x01, 0x0a, 0x0c, 0x47, 0x65, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75,
	0x64, 0x69, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x2f, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75,
	0x64, 0x69, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x29, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x23, 0x12, 0x21, 0x2f, 0x61,
	0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2d, 0x68, 0x75, 0x62,
	0x2f, 0x67, 0x65, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x12,
	0x85, 0x01, 0x0a, 0x07, 0x47, 0x65, 0x74, 0x54, 0x61, 0x67, 0x73, 0x12, 0x29, 0x2e, 0x61, 0x70,
	0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x61, 0x67, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2a, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69,
	0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x68, 0x75, 0x62, 0x2e,
	0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x61, 0x67, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x23, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1d, 0x12, 0x1b, 0x2f, 0x61, 0x70, 0x69,
	0x73, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2d, 0x68, 0x75, 0x62, 0x2f, 0x67,
	0x65, 0x74, 0x5f, 0x74, 0x61, 0x67, 0x73, 0x12, 0x95, 0x01, 0x0a, 0x0b, 0x47, 0x65, 0x74, 0x42,
	0x72, 0x61, 0x6e, 0x63, 0x68, 0x65, 0x73, 0x12, 0x2d, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61,
	0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x68, 0x75, 0x62,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x42, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x65, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69,
	0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x68, 0x75, 0x62, 0x2e,
	0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x42, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x65, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x27, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x21, 0x12, 0x1f,
	0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2d, 0x68,
	0x75, 0x62, 0x2f, 0x67, 0x65, 0x74, 0x5f, 0x62, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x65, 0x73, 0x12,
	0x9c, 0x01, 0x0a, 0x0c, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42, 0x72, 0x61, 0x6e, 0x63, 0x68,
	0x12, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x42, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x2f, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x42, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x2b, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x25, 0x3a, 0x01, 0x2a, 0x22, 0x20, 0x2f, 0x61,
	0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2d, 0x68, 0x75, 0x62,
	0x2f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x62, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x12, 0x92,
	0x01, 0x0a, 0x0b, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x12, 0x2d,
	0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x6c,
	0x6c, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2e, 0x2e,
	0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c,
	0x46, 0x69, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x24, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x1e, 0x12, 0x1c, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2d, 0x68, 0x75, 0x62, 0x2f, 0x61, 0x6c, 0x6c, 0x2d, 0x66, 0x69,
	0x6c, 0x65, 0x73, 0x12, 0x95, 0x01, 0x0a, 0x0b, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x42, 0x72, 0x61,
	0x6e, 0x63, 0x68, 0x12, 0x2d, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75,
	0x64, 0x69, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x42, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64,
	0x69, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x42, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x27, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x21, 0x12, 0x1f, 0x2f, 0x61, 0x70, 0x69,
	0x73, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2d, 0x68, 0x75, 0x62, 0x2f, 0x63,
	0x68, 0x65, 0x63, 0x6b, 0x5f, 0x62, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x12, 0x90, 0x01, 0x0a, 0x09,
	0x53, 0x79, 0x6e, 0x63, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x2b, 0x2e, 0x61, 0x70, 0x69, 0x73,
	0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x68,
	0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x79, 0x6e, 0x63, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2c, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69,
	0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x68, 0x75, 0x62, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x79, 0x6e, 0x63, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x28, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x22, 0x3a, 0x01, 0x2a, 0x22,
	0x1d, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2d,
	0x68, 0x75, 0x62, 0x2f, 0x73, 0x79, 0x6e, 0x63, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0xaf,
	0x01, 0x0a, 0x11, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x46, 0x6f, 0x72,
	0x53, 0x79, 0x6e, 0x63, 0x12, 0x33, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74,
	0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x46, 0x6f, 0x72, 0x53, 0x79,
	0x6e, 0x63, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x34, 0x2e, 0x61, 0x70, 0x69, 0x73,
	0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x68,
	0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x46, 0x6f, 0x72, 0x53, 0x79, 0x6e, 0x63, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x2f, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x29, 0x12, 0x27, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76,
	0x31, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2d, 0x68, 0x75, 0x62, 0x2f, 0x63, 0x68, 0x65, 0x63,
	0x6b, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x66, 0x6f, 0x72, 0x5f, 0x73, 0x79, 0x6e, 0x63,
	0x12, 0xb7, 0x01, 0x0a, 0x13, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x79, 0x6e, 0x63, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x35, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e,
	0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x68, 0x75,
	0x62, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x79, 0x6e, 0x63, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x36, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74,
	0x53, 0x79, 0x6e, 0x63, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x31, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2b, 0x12,
	0x29, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2d,
	0x68, 0x75, 0x62, 0x2f, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x73, 0x79, 0x6e, 0x63, 0x5f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x5f, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x9f, 0x01, 0x0a, 0x0d, 0x53,
	0x74, 0x6f, 0x70, 0x53, 0x79, 0x6e, 0x63, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x2f, 0x2e, 0x61,
	0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x6f, 0x70, 0x53, 0x79, 0x6e,
	0x63, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x30, 0x2e,
	0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x6f, 0x70, 0x53, 0x79,
	0x6e, 0x63, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x2b, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x25, 0x3a, 0x01, 0x2a, 0x22, 0x20, 0x2f, 0x61, 0x70, 0x69,
	0x73, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2d, 0x68, 0x75, 0x62, 0x2f, 0x73,
	0x74, 0x6f, 0x70, 0x73, 0x79, 0x6e, 0x63, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0xc9, 0x01, 0x0a,
	0x12, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x57, 0x61, 0x72, 0x6d, 0x75, 0x70, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x12, 0x34, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75,
	0x64, 0x69, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x57, 0x61, 0x72, 0x6d, 0x75, 0x70, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x35, 0x2e, 0x61, 0x70, 0x69, 0x73,
	0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x68,
	0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x57, 0x61, 0x72, 0x6d,
	0x75, 0x70, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x46, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x40, 0x3a, 0x01, 0x2a, 0x1a, 0x3b, 0x2f, 0x61, 0x70,
	0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2d, 0x68, 0x75, 0x62, 0x2f,
	0x7b, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f,
	0x7b, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x77, 0x61, 0x72, 0x6d,
	0x75, 0x70, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0xc0, 0x01, 0x0a, 0x0e, 0x53, 0x74, 0x61,
	0x72, 0x74, 0x57, 0x61, 0x72, 0x6d, 0x75, 0x70, 0x4a, 0x6f, 0x62, 0x12, 0x30, 0x2e, 0x61, 0x70,
	0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x72, 0x74, 0x57, 0x61, 0x72,
	0x6d, 0x75, 0x70, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x31, 0x2e,
	0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x72, 0x74, 0x57,
	0x61, 0x72, 0x6d, 0x75, 0x70, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x49, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x43, 0x3a, 0x01, 0x2a, 0x22, 0x3e, 0x2f, 0x61, 0x70,
	0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2d, 0x68, 0x75, 0x62, 0x2f,
	0x7b, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f,
	0x7b, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x77, 0x61, 0x72, 0x6d,
	0x75, 0x70, 0x6a, 0x6f, 0x62, 0x2f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x12, 0xbc, 0x01, 0x0a, 0x0d,
	0x53, 0x74, 0x6f, 0x70, 0x57, 0x61, 0x72, 0x6d, 0x75, 0x70, 0x4a, 0x6f, 0x62, 0x12, 0x2f, 0x2e,
	0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x6f, 0x70, 0x57, 0x61,
	0x72, 0x6d, 0x75, 0x70, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x30,
	0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x6f, 0x70, 0x57,
	0x61, 0x72, 0x6d, 0x75, 0x70, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x48, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x42, 0x3a, 0x01, 0x2a, 0x22, 0x3d, 0x2f, 0x61, 0x70,
	0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2d, 0x68, 0x75, 0x62, 0x2f,
	0x7b, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f,
	0x7b, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x77, 0x61, 0x72, 0x6d,
	0x75, 0x70, 0x6a, 0x6f, 0x62, 0x2f, 0x73, 0x74, 0x6f, 0x70, 0x12, 0xb8, 0x01, 0x0a, 0x11, 0x4c,
	0x69, 0x73, 0x74, 0x57, 0x61, 0x72, 0x6d, 0x75, 0x70, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73,
	0x12, 0x33, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x57, 0x61, 0x72, 0x6d, 0x75, 0x70, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x34, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73,
	0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x68, 0x75, 0x62, 0x2e, 0x76,
	0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x61, 0x72, 0x6d, 0x75, 0x70, 0x52, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x38, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x32, 0x12, 0x30, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x2d, 0x68, 0x75, 0x62, 0x2f, 0x7b, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x77, 0x61, 0x72, 0x6d, 0x75, 0x70, 0x72, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x73, 0x12, 0xb4, 0x01, 0x0a, 0x10, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x61,
	0x72, 0x6d, 0x65, 0x64, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x12, 0x32, 0x2e, 0x61, 0x70, 0x69,
	0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x61, 0x72, 0x6d, 0x65,
	0x64, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x33,
	0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x57,
	0x61, 0x72, 0x6d, 0x65, 0x64, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x37, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x31, 0x12, 0x2f, 0x2f, 0x61, 0x70,
	0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2d, 0x68, 0x75, 0x62, 0x2f,
	0x7b, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f,
	0x77, 0x61, 0x72, 0x6d, 0x65, 0x64, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x12, 0x98, 0x01, 0x0a,
	0x0b, 0x47, 0x69, 0x74, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x12, 0x2d, 0x2e, 0x61,
	0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x69, 0x74, 0x43, 0x61, 0x6c, 0x6c,
	0x62, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2e, 0x2e, 0x61, 0x70,
	0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x69, 0x74, 0x43, 0x61, 0x6c, 0x6c, 0x62,
	0x61, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x2a, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x24, 0x3a, 0x01, 0x2a, 0x22, 0x1f, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31,
	0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2d, 0x68, 0x75, 0x62, 0x2f, 0x67, 0x69, 0x74, 0x2f, 0x63,
	0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x12, 0xac, 0x01, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x57, 0x61, 0x72, 0x6d, 0x75, 0x70, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x12,
	0x35, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x57, 0x61, 0x72, 0x6d, 0x75, 0x70, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x36, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69,
	0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x68, 0x75, 0x62, 0x2e,
	0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x57, 0x61, 0x72, 0x6d, 0x75,
	0x70, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x26,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x20, 0x12, 0x1e, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31,
	0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2d, 0x68, 0x75, 0x62, 0x2f, 0x77, 0x61, 0x72, 0x6d, 0x75,
	0x70, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x12, 0x76, 0x0a, 0x0a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x46, 0x6f, 0x72, 0x6b, 0x12, 0x2c, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74,
	0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x46, 0x6f, 0x72, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x22, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x1c, 0x3a, 0x01, 0x2a, 0x22, 0x17, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2d, 0x68, 0x75, 0x62, 0x2f, 0x66, 0x6f, 0x72, 0x6b, 0x12, 0xc5,
	0x01, 0x0a, 0x10, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x57, 0x61, 0x72,
	0x6d, 0x65, 0x64, 0x12, 0x32, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75,
	0x64, 0x69, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x57, 0x61, 0x72, 0x6d, 0x65, 0x64,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x33, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61,
	0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x68, 0x75, 0x62,
	0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x57, 0x61,
	0x72, 0x6d, 0x65, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x48, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x42, 0x12, 0x40, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x2d, 0x68, 0x75, 0x62, 0x2f, 0x77, 0x61, 0x72, 0x6d, 0x65, 0x64, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x7b, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7b, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x7d,
	0x2f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x42, 0x4b, 0x5a, 0x49, 0x67, 0x69, 0x74, 0x2e, 0x6c, 0x69,
	0x61, 0x6e, 0x6a, 0x69, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6e,
	0x61, 0x74, 0x69, 0x76, 0x65, 0x2f, 0x6b, 0x69, 0x63, 0x2f, 0x6b, 0x69, 0x63, 0x2d, 0x70, 0x6c,
	0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x61, 0x69, 0x73, 0x74,
	0x75, 0x64, 0x69, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x68, 0x75, 0x62, 0x2f, 0x76, 0x31,
	0x3b, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_aistudio_modelhub_v1_modelhub_proto_rawDescOnce sync.Once
	file_aistudio_modelhub_v1_modelhub_proto_rawDescData = file_aistudio_modelhub_v1_modelhub_proto_rawDesc
)

func file_aistudio_modelhub_v1_modelhub_proto_rawDescGZIP() []byte {
	file_aistudio_modelhub_v1_modelhub_proto_rawDescOnce.Do(func() {
		file_aistudio_modelhub_v1_modelhub_proto_rawDescData = protoimpl.X.CompressGZIP(file_aistudio_modelhub_v1_modelhub_proto_rawDescData)
	})
	return file_aistudio_modelhub_v1_modelhub_proto_rawDescData
}

var file_aistudio_modelhub_v1_modelhub_proto_msgTypes = make([]protoimpl.MessageInfo, 97)
var file_aistudio_modelhub_v1_modelhub_proto_goTypes = []any{
	(*CheckModelWarmedRequest)(nil),       // 0: apis.aistudio.modelhub.v1.CheckModelWarmedRequest
	(*CheckModelWarmedResponse)(nil),      // 1: apis.aistudio.modelhub.v1.CheckModelWarmedResponse
	(*CreateForkRequest)(nil),             // 2: apis.aistudio.modelhub.v1.CreateForkRequest
	(*SDKModelInfoRequest)(nil),           // 3: apis.aistudio.modelhub.v1.SDKModelInfoRequest
	(*SDKModelInfoResponse)(nil),          // 4: apis.aistudio.modelhub.v1.SDKModelInfoResponse
	(*Sibling)(nil),                       // 5: apis.aistudio.modelhub.v1.Sibling
	(*CreateModelRequest)(nil),            // 6: apis.aistudio.modelhub.v1.CreateModelRequest
	(*UpdateModelRequest)(nil),            // 7: apis.aistudio.modelhub.v1.UpdateModelRequest
	(*ListModelsOptions)(nil),             // 8: apis.aistudio.modelhub.v1.ListModelsOptions
	(*ListModelResponse)(nil),             // 9: apis.aistudio.modelhub.v1.ListModelResponse
	(*Model)(nil),                         // 10: apis.aistudio.modelhub.v1.Model
	(*Repository)(nil),                    // 11: apis.aistudio.modelhub.v1.Repository
	(*DeleteModelRequest)(nil),            // 12: apis.aistudio.modelhub.v1.DeleteModelRequest
	(*ShowModelRequest)(nil),              // 13: apis.aistudio.modelhub.v1.ShowModelRequest
	(*ShowModelResponse)(nil),             // 14: apis.aistudio.modelhub.v1.ShowModelResponse
	(*GetGitTokenRequest)(nil),            // 15: apis.aistudio.modelhub.v1.GetGitTokenRequest
	(*GetGitTokenResponse)(nil),           // 16: apis.aistudio.modelhub.v1.GetGitTokenResponse
	(*GitToken)(nil),                      // 17: apis.aistudio.modelhub.v1.GitToken
	(*CreateUserRequest)(nil),             // 18: apis.aistudio.modelhub.v1.CreateUserRequest
	(*GetTreeRequest)(nil),                // 19: apis.aistudio.modelhub.v1.GetTreeRequest
	(*GetTreeResponse)(nil),               // 20: apis.aistudio.modelhub.v1.GetTreeResponse
	(*File)(nil),                          // 21: apis.aistudio.modelhub.v1.File
	(*Commit)(nil),                        // 22: apis.aistudio.modelhub.v1.Commit
	(*GetLastCommitRequest)(nil),          // 23: apis.aistudio.modelhub.v1.GetLastCommitRequest
	(*GetLastCommitResponse)(nil),         // 24: apis.aistudio.modelhub.v1.GetLastCommitResponse
	(*GetRepoFileContentRequest)(nil),     // 25: apis.aistudio.modelhub.v1.GetRepoFileContentRequest
	(*GetRepoFileContentResponse)(nil),    // 26: apis.aistudio.modelhub.v1.GetRepoFileContentResponse
	(*DownloadFileRequest)(nil),           // 27: apis.aistudio.modelhub.v1.DownloadFileRequest
	(*DownloadFileResponse)(nil),          // 28: apis.aistudio.modelhub.v1.DownloadFileResponse
	(*CreateModelHubRequest)(nil),         // 29: apis.aistudio.modelhub.v1.CreateModelHubRequest
	(*CreateModelHubResponse)(nil),        // 30: apis.aistudio.modelhub.v1.CreateModelHubResponse
	(*GetModelHubRequest)(nil),            // 31: apis.aistudio.modelhub.v1.GetModelHubRequest
	(*GetModelHubResponse)(nil),           // 32: apis.aistudio.modelhub.v1.GetModelHubResponse
	(*GetModelHubUserRequest)(nil),        // 33: apis.aistudio.modelhub.v1.GetModelHubUserRequest
	(*GetModelHubUserResponse)(nil),       // 34: apis.aistudio.modelhub.v1.GetModelHubUserResponse
	(*AddCollaboratorRequest)(nil),        // 35: apis.aistudio.modelhub.v1.AddCollaboratorRequest
	(*AddCollaboratorResponse)(nil),       // 36: apis.aistudio.modelhub.v1.AddCollaboratorResponse
	(*DeleteCollaboratorRequest)(nil),     // 37: apis.aistudio.modelhub.v1.DeleteCollaboratorRequest
	(*ChangeCollaboratorRequest)(nil),     // 38: apis.aistudio.modelhub.v1.ChangeCollaboratorRequest
	(*ListCollaboratorRequest)(nil),       // 39: apis.aistudio.modelhub.v1.ListCollaboratorRequest
	(*ListCollaboratorResponse)(nil),      // 40: apis.aistudio.modelhub.v1.ListCollaboratorResponse
	(*Collaborator)(nil),                  // 41: apis.aistudio.modelhub.v1.Collaborator
	(*CheckModelIsExistRequest)(nil),      // 42: apis.aistudio.modelhub.v1.CheckModelIsExistRequest
	(*CheckModelIsExistResponse)(nil),     // 43: apis.aistudio.modelhub.v1.CheckModelIsExistResponse
	(*InitModelHubOrgRequest)(nil),        // 44: apis.aistudio.modelhub.v1.InitModelHubOrgRequest
	(*CheckCollaboratorAuthRequest)(nil),  // 45: apis.aistudio.modelhub.v1.CheckCollaboratorAuthRequest
	(*CheckCollaboratorAuthResponse)(nil), // 46: apis.aistudio.modelhub.v1.CheckCollaboratorAuthResponse
	(*GetModelInfoRequest)(nil),           // 47: apis.aistudio.modelhub.v1.GetModelInfoRequest
	(*GetModelInfoResponse)(nil),          // 48: apis.aistudio.modelhub.v1.GetModelInfoResponse
	(*SDKFile)(nil),                       // 49: apis.aistudio.modelhub.v1.SDKFile
	(*GetTagsRequest)(nil),                // 50: apis.aistudio.modelhub.v1.GetTagsRequest
	(*GetTagsResponse)(nil),               // 51: apis.aistudio.modelhub.v1.GetTagsResponse
	(*Tags)(nil),                          // 52: apis.aistudio.modelhub.v1.Tags
	(*GetBranchesRequest)(nil),            // 53: apis.aistudio.modelhub.v1.GetBranchesRequest
	(*GetBranchesResponse)(nil),           // 54: apis.aistudio.modelhub.v1.GetBranchesResponse
	(*Branch)(nil),                        // 55: apis.aistudio.modelhub.v1.Branch
	(*CreateBranchRequest)(nil),           // 56: apis.aistudio.modelhub.v1.CreateBranchRequest
	(*CreateBranchResponse)(nil),          // 57: apis.aistudio.modelhub.v1.CreateBranchResponse
	(*GetAllFilesRequest)(nil),            // 58: apis.aistudio.modelhub.v1.GetAllFilesRequest
	(*GetAllFilesResponse)(nil),           // 59: apis.aistudio.modelhub.v1.GetAllFilesResponse
	(*CheckBranchRequest)(nil),            // 60: apis.aistudio.modelhub.v1.CheckBranchRequest
	(*CheckBranchResponse)(nil),           // 61: apis.aistudio.modelhub.v1.CheckBranchResponse
	(*SyncModelRequest)(nil),              // 62: apis.aistudio.modelhub.v1.SyncModelRequest
	(*SyncModelResponse)(nil),             // 63: apis.aistudio.modelhub.v1.SyncModelResponse
	(*SyncModelData)(nil),                 // 64: apis.aistudio.modelhub.v1.SyncModelData
	(*CheckModelForSyncRequest)(nil),      // 65: apis.aistudio.modelhub.v1.CheckModelForSyncRequest
	(*CheckModelForSyncResponse)(nil),     // 66: apis.aistudio.modelhub.v1.CheckModelForSyncResponse
	(*ListSyncModelRecordRequest)(nil),    // 67: apis.aistudio.modelhub.v1.ListSyncModelRecordRequest
	(*ListSyncModelRecordResponse)(nil),   // 68: apis.aistudio.modelhub.v1.ListSyncModelRecordResponse
	(*SyncModelRecord)(nil),               // 69: apis.aistudio.modelhub.v1.SyncModelRecord
	(*SyncCondition)(nil),                 // 70: apis.aistudio.modelhub.v1.SyncCondition
	(*StopSyncModelRequest)(nil),          // 71: apis.aistudio.modelhub.v1.StopSyncModelRequest
	(*StopSyncModelResponse)(nil),         // 72: apis.aistudio.modelhub.v1.StopSyncModelResponse
	(*UpdateWarmupConfigRequest)(nil),     // 73: apis.aistudio.modelhub.v1.UpdateWarmupConfigRequest
	(*WarmupConfig)(nil),                  // 74: apis.aistudio.modelhub.v1.WarmupConfig
	(*WarmupRegion)(nil),                  // 75: apis.aistudio.modelhub.v1.WarmupRegion
	(*UpdateWarmupConfigResponse)(nil),    // 76: apis.aistudio.modelhub.v1.UpdateWarmupConfigResponse
	(*StartWarmupJobRequest)(nil),         // 77: apis.aistudio.modelhub.v1.StartWarmupJobRequest
	(*StartWarmupJobResponse)(nil),        // 78: apis.aistudio.modelhub.v1.StartWarmupJobResponse
	(*StopWarmupJobRequest)(nil),          // 79: apis.aistudio.modelhub.v1.StopWarmupJobRequest
	(*StopWarmupJobResponse)(nil),         // 80: apis.aistudio.modelhub.v1.StopWarmupJobResponse
	(*ListWarmupRecordsRequest)(nil),      // 81: apis.aistudio.modelhub.v1.ListWarmupRecordsRequest
	(*ListWarmupRecordsResponse)(nil),     // 82: apis.aistudio.modelhub.v1.ListWarmupRecordsResponse
	(*WarmupRecord)(nil),                  // 83: apis.aistudio.modelhub.v1.WarmupRecord
	(*WarmupStatus)(nil),                  // 84: apis.aistudio.modelhub.v1.WarmupStatus
	(*WarmupConditions)(nil),              // 85: apis.aistudio.modelhub.v1.WarmupConditions
	(*ListWarmedModelsRequest)(nil),       // 86: apis.aistudio.modelhub.v1.ListWarmedModelsRequest
	(*ListWarmedModelsResponse)(nil),      // 87: apis.aistudio.modelhub.v1.ListWarmedModelsResponse
	(*WarmedModel)(nil),                   // 88: apis.aistudio.modelhub.v1.WarmedModel
	(*GitCallbackRequest)(nil),            // 89: apis.aistudio.modelhub.v1.GitCallbackRequest
	(*CallbackRepository)(nil),            // 90: apis.aistudio.modelhub.v1.CallbackRepository
	(*Owner)(nil),                         // 91: apis.aistudio.modelhub.v1.Owner
	(*Pusher)(nil),                        // 92: apis.aistudio.modelhub.v1.Pusher
	(*GitCallbackResponse)(nil),           // 93: apis.aistudio.modelhub.v1.GitCallbackResponse
	(*GetModelWarmupRatioRequest)(nil),    // 94: apis.aistudio.modelhub.v1.GetModelWarmupRatioRequest
	(*GetModelWarmupRatioResponse)(nil),   // 95: apis.aistudio.modelhub.v1.GetModelWarmupRatioResponse
	(*WarmupRatio)(nil),                   // 96: apis.aistudio.modelhub.v1.WarmupRatio
	(*emptypb.Empty)(nil),                 // 97: google.protobuf.Empty
}
var file_aistudio_modelhub_v1_modelhub_proto_depIdxs = []int32{
	5,  // 0: apis.aistudio.modelhub.v1.SDKModelInfoResponse.siblings:type_name -> apis.aistudio.modelhub.v1.Sibling
	10, // 1: apis.aistudio.modelhub.v1.ListModelResponse.items:type_name -> apis.aistudio.modelhub.v1.Model
	11, // 2: apis.aistudio.modelhub.v1.Model.repository:type_name -> apis.aistudio.modelhub.v1.Repository
	74, // 3: apis.aistudio.modelhub.v1.Model.warmupConfig:type_name -> apis.aistudio.modelhub.v1.WarmupConfig
	75, // 4: apis.aistudio.modelhub.v1.Model.warmedRegions:type_name -> apis.aistudio.modelhub.v1.WarmupRegion
	10, // 5: apis.aistudio.modelhub.v1.ShowModelResponse.model:type_name -> apis.aistudio.modelhub.v1.Model
	17, // 6: apis.aistudio.modelhub.v1.GetGitTokenResponse.gitToken:type_name -> apis.aistudio.modelhub.v1.GitToken
	21, // 7: apis.aistudio.modelhub.v1.GetTreeResponse.files:type_name -> apis.aistudio.modelhub.v1.File
	22, // 8: apis.aistudio.modelhub.v1.File.commit:type_name -> apis.aistudio.modelhub.v1.Commit
	22, // 9: apis.aistudio.modelhub.v1.GetLastCommitResponse.commit:type_name -> apis.aistudio.modelhub.v1.Commit
	41, // 10: apis.aistudio.modelhub.v1.ListCollaboratorResponse.items:type_name -> apis.aistudio.modelhub.v1.Collaborator
	49, // 11: apis.aistudio.modelhub.v1.GetModelInfoResponse.siblings:type_name -> apis.aistudio.modelhub.v1.SDKFile
	52, // 12: apis.aistudio.modelhub.v1.GetTagsResponse.tags:type_name -> apis.aistudio.modelhub.v1.Tags
	55, // 13: apis.aistudio.modelhub.v1.GetBranchesResponse.branches:type_name -> apis.aistudio.modelhub.v1.Branch
	21, // 14: apis.aistudio.modelhub.v1.GetAllFilesResponse.files:type_name -> apis.aistudio.modelhub.v1.File
	64, // 15: apis.aistudio.modelhub.v1.SyncModelResponse.data:type_name -> apis.aistudio.modelhub.v1.SyncModelData
	69, // 16: apis.aistudio.modelhub.v1.ListSyncModelRecordResponse.records:type_name -> apis.aistudio.modelhub.v1.SyncModelRecord
	70, // 17: apis.aistudio.modelhub.v1.SyncModelRecord.conditions:type_name -> apis.aistudio.modelhub.v1.SyncCondition
	74, // 18: apis.aistudio.modelhub.v1.UpdateWarmupConfigRequest.warmupConfig:type_name -> apis.aistudio.modelhub.v1.WarmupConfig
	75, // 19: apis.aistudio.modelhub.v1.WarmupConfig.warmupRegions:type_name -> apis.aistudio.modelhub.v1.WarmupRegion
	75, // 20: apis.aistudio.modelhub.v1.StartWarmupJobRequest.warmupRegions:type_name -> apis.aistudio.modelhub.v1.WarmupRegion
	83, // 21: apis.aistudio.modelhub.v1.ListWarmupRecordsResponse.records:type_name -> apis.aistudio.modelhub.v1.WarmupRecord
	74, // 22: apis.aistudio.modelhub.v1.WarmupRecord.warmupConfig:type_name -> apis.aistudio.modelhub.v1.WarmupConfig
	84, // 23: apis.aistudio.modelhub.v1.WarmupRecord.warmupStatus:type_name -> apis.aistudio.modelhub.v1.WarmupStatus
	85, // 24: apis.aistudio.modelhub.v1.WarmupStatus.conditions:type_name -> apis.aistudio.modelhub.v1.WarmupConditions
	88, // 25: apis.aistudio.modelhub.v1.ListWarmedModelsResponse.models:type_name -> apis.aistudio.modelhub.v1.WarmedModel
	74, // 26: apis.aistudio.modelhub.v1.WarmedModel.warmupConfig:type_name -> apis.aistudio.modelhub.v1.WarmupConfig
	90, // 27: apis.aistudio.modelhub.v1.GitCallbackRequest.repository:type_name -> apis.aistudio.modelhub.v1.CallbackRepository
	92, // 28: apis.aistudio.modelhub.v1.GitCallbackRequest.pusher:type_name -> apis.aistudio.modelhub.v1.Pusher
	91, // 29: apis.aistudio.modelhub.v1.CallbackRepository.owner:type_name -> apis.aistudio.modelhub.v1.Owner
	96, // 30: apis.aistudio.modelhub.v1.GetModelWarmupRatioResponse.warmupRatios:type_name -> apis.aistudio.modelhub.v1.WarmupRatio
	6,  // 31: apis.aistudio.modelhub.v1.ModelHubService.CreateModel:input_type -> apis.aistudio.modelhub.v1.CreateModelRequest
	8,  // 32: apis.aistudio.modelhub.v1.ModelHubService.ListModels:input_type -> apis.aistudio.modelhub.v1.ListModelsOptions
	7,  // 33: apis.aistudio.modelhub.v1.ModelHubService.UpdateModel:input_type -> apis.aistudio.modelhub.v1.UpdateModelRequest
	12, // 34: apis.aistudio.modelhub.v1.ModelHubService.DeleteModel:input_type -> apis.aistudio.modelhub.v1.DeleteModelRequest
	13, // 35: apis.aistudio.modelhub.v1.ModelHubService.ShowModel:input_type -> apis.aistudio.modelhub.v1.ShowModelRequest
	15, // 36: apis.aistudio.modelhub.v1.ModelHubService.GetGitToken:input_type -> apis.aistudio.modelhub.v1.GetGitTokenRequest
	29, // 37: apis.aistudio.modelhub.v1.ModelHubService.CreateModelHub:input_type -> apis.aistudio.modelhub.v1.CreateModelHubRequest
	31, // 38: apis.aistudio.modelhub.v1.ModelHubService.GetModelHub:input_type -> apis.aistudio.modelhub.v1.GetModelHubRequest
	19, // 39: apis.aistudio.modelhub.v1.ModelHubService.GetTree:input_type -> apis.aistudio.modelhub.v1.GetTreeRequest
	23, // 40: apis.aistudio.modelhub.v1.ModelHubService.GetLastCommit:input_type -> apis.aistudio.modelhub.v1.GetLastCommitRequest
	25, // 41: apis.aistudio.modelhub.v1.ModelHubService.GetRepoFileContent:input_type -> apis.aistudio.modelhub.v1.GetRepoFileContentRequest
	18, // 42: apis.aistudio.modelhub.v1.ModelHubService.CreateUser:input_type -> apis.aistudio.modelhub.v1.CreateUserRequest
	35, // 43: apis.aistudio.modelhub.v1.ModelHubService.AddCollaborator:input_type -> apis.aistudio.modelhub.v1.AddCollaboratorRequest
	37, // 44: apis.aistudio.modelhub.v1.ModelHubService.DeleteCollaborator:input_type -> apis.aistudio.modelhub.v1.DeleteCollaboratorRequest
	38, // 45: apis.aistudio.modelhub.v1.ModelHubService.ChangeCollaborator:input_type -> apis.aistudio.modelhub.v1.ChangeCollaboratorRequest
	39, // 46: apis.aistudio.modelhub.v1.ModelHubService.ListCollaborator:input_type -> apis.aistudio.modelhub.v1.ListCollaboratorRequest
	42, // 47: apis.aistudio.modelhub.v1.ModelHubService.CheckModelIsExist:input_type -> apis.aistudio.modelhub.v1.CheckModelIsExistRequest
	45, // 48: apis.aistudio.modelhub.v1.ModelHubService.CheckCollaboratorAuth:input_type -> apis.aistudio.modelhub.v1.CheckCollaboratorAuthRequest
	44, // 49: apis.aistudio.modelhub.v1.ModelHubService.InitModelHubOrg:input_type -> apis.aistudio.modelhub.v1.InitModelHubOrgRequest
	47, // 50: apis.aistudio.modelhub.v1.ModelHubService.GetModelInfo:input_type -> apis.aistudio.modelhub.v1.GetModelInfoRequest
	50, // 51: apis.aistudio.modelhub.v1.ModelHubService.GetTags:input_type -> apis.aistudio.modelhub.v1.GetTagsRequest
	53, // 52: apis.aistudio.modelhub.v1.ModelHubService.GetBranches:input_type -> apis.aistudio.modelhub.v1.GetBranchesRequest
	56, // 53: apis.aistudio.modelhub.v1.ModelHubService.CreateBranch:input_type -> apis.aistudio.modelhub.v1.CreateBranchRequest
	58, // 54: apis.aistudio.modelhub.v1.ModelHubService.GetAllFiles:input_type -> apis.aistudio.modelhub.v1.GetAllFilesRequest
	60, // 55: apis.aistudio.modelhub.v1.ModelHubService.CheckBranch:input_type -> apis.aistudio.modelhub.v1.CheckBranchRequest
	62, // 56: apis.aistudio.modelhub.v1.ModelHubService.SyncModel:input_type -> apis.aistudio.modelhub.v1.SyncModelRequest
	65, // 57: apis.aistudio.modelhub.v1.ModelHubService.CheckModelForSync:input_type -> apis.aistudio.modelhub.v1.CheckModelForSyncRequest
	67, // 58: apis.aistudio.modelhub.v1.ModelHubService.ListSyncModelRecord:input_type -> apis.aistudio.modelhub.v1.ListSyncModelRecordRequest
	71, // 59: apis.aistudio.modelhub.v1.ModelHubService.StopSyncModel:input_type -> apis.aistudio.modelhub.v1.StopSyncModelRequest
	73, // 60: apis.aistudio.modelhub.v1.ModelHubService.UpdateWarmupConfig:input_type -> apis.aistudio.modelhub.v1.UpdateWarmupConfigRequest
	77, // 61: apis.aistudio.modelhub.v1.ModelHubService.StartWarmupJob:input_type -> apis.aistudio.modelhub.v1.StartWarmupJobRequest
	79, // 62: apis.aistudio.modelhub.v1.ModelHubService.StopWarmupJob:input_type -> apis.aistudio.modelhub.v1.StopWarmupJobRequest
	81, // 63: apis.aistudio.modelhub.v1.ModelHubService.ListWarmupRecords:input_type -> apis.aistudio.modelhub.v1.ListWarmupRecordsRequest
	86, // 64: apis.aistudio.modelhub.v1.ModelHubService.ListWarmedModels:input_type -> apis.aistudio.modelhub.v1.ListWarmedModelsRequest
	89, // 65: apis.aistudio.modelhub.v1.ModelHubService.GitCallback:input_type -> apis.aistudio.modelhub.v1.GitCallbackRequest
	94, // 66: apis.aistudio.modelhub.v1.ModelHubService.GetModelWarmupRatio:input_type -> apis.aistudio.modelhub.v1.GetModelWarmupRatioRequest
	2,  // 67: apis.aistudio.modelhub.v1.ModelHubService.CreateFork:input_type -> apis.aistudio.modelhub.v1.CreateForkRequest
	0,  // 68: apis.aistudio.modelhub.v1.ModelHubService.CheckModelWarmed:input_type -> apis.aistudio.modelhub.v1.CheckModelWarmedRequest
	97, // 69: apis.aistudio.modelhub.v1.ModelHubService.CreateModel:output_type -> google.protobuf.Empty
	9,  // 70: apis.aistudio.modelhub.v1.ModelHubService.ListModels:output_type -> apis.aistudio.modelhub.v1.ListModelResponse
	97, // 71: apis.aistudio.modelhub.v1.ModelHubService.UpdateModel:output_type -> google.protobuf.Empty
	97, // 72: apis.aistudio.modelhub.v1.ModelHubService.DeleteModel:output_type -> google.protobuf.Empty
	14, // 73: apis.aistudio.modelhub.v1.ModelHubService.ShowModel:output_type -> apis.aistudio.modelhub.v1.ShowModelResponse
	16, // 74: apis.aistudio.modelhub.v1.ModelHubService.GetGitToken:output_type -> apis.aistudio.modelhub.v1.GetGitTokenResponse
	30, // 75: apis.aistudio.modelhub.v1.ModelHubService.CreateModelHub:output_type -> apis.aistudio.modelhub.v1.CreateModelHubResponse
	32, // 76: apis.aistudio.modelhub.v1.ModelHubService.GetModelHub:output_type -> apis.aistudio.modelhub.v1.GetModelHubResponse
	20, // 77: apis.aistudio.modelhub.v1.ModelHubService.GetTree:output_type -> apis.aistudio.modelhub.v1.GetTreeResponse
	24, // 78: apis.aistudio.modelhub.v1.ModelHubService.GetLastCommit:output_type -> apis.aistudio.modelhub.v1.GetLastCommitResponse
	26, // 79: apis.aistudio.modelhub.v1.ModelHubService.GetRepoFileContent:output_type -> apis.aistudio.modelhub.v1.GetRepoFileContentResponse
	97, // 80: apis.aistudio.modelhub.v1.ModelHubService.CreateUser:output_type -> google.protobuf.Empty
	36, // 81: apis.aistudio.modelhub.v1.ModelHubService.AddCollaborator:output_type -> apis.aistudio.modelhub.v1.AddCollaboratorResponse
	97, // 82: apis.aistudio.modelhub.v1.ModelHubService.DeleteCollaborator:output_type -> google.protobuf.Empty
	97, // 83: apis.aistudio.modelhub.v1.ModelHubService.ChangeCollaborator:output_type -> google.protobuf.Empty
	40, // 84: apis.aistudio.modelhub.v1.ModelHubService.ListCollaborator:output_type -> apis.aistudio.modelhub.v1.ListCollaboratorResponse
	43, // 85: apis.aistudio.modelhub.v1.ModelHubService.CheckModelIsExist:output_type -> apis.aistudio.modelhub.v1.CheckModelIsExistResponse
	46, // 86: apis.aistudio.modelhub.v1.ModelHubService.CheckCollaboratorAuth:output_type -> apis.aistudio.modelhub.v1.CheckCollaboratorAuthResponse
	97, // 87: apis.aistudio.modelhub.v1.ModelHubService.InitModelHubOrg:output_type -> google.protobuf.Empty
	48, // 88: apis.aistudio.modelhub.v1.ModelHubService.GetModelInfo:output_type -> apis.aistudio.modelhub.v1.GetModelInfoResponse
	51, // 89: apis.aistudio.modelhub.v1.ModelHubService.GetTags:output_type -> apis.aistudio.modelhub.v1.GetTagsResponse
	54, // 90: apis.aistudio.modelhub.v1.ModelHubService.GetBranches:output_type -> apis.aistudio.modelhub.v1.GetBranchesResponse
	57, // 91: apis.aistudio.modelhub.v1.ModelHubService.CreateBranch:output_type -> apis.aistudio.modelhub.v1.CreateBranchResponse
	59, // 92: apis.aistudio.modelhub.v1.ModelHubService.GetAllFiles:output_type -> apis.aistudio.modelhub.v1.GetAllFilesResponse
	61, // 93: apis.aistudio.modelhub.v1.ModelHubService.CheckBranch:output_type -> apis.aistudio.modelhub.v1.CheckBranchResponse
	63, // 94: apis.aistudio.modelhub.v1.ModelHubService.SyncModel:output_type -> apis.aistudio.modelhub.v1.SyncModelResponse
	66, // 95: apis.aistudio.modelhub.v1.ModelHubService.CheckModelForSync:output_type -> apis.aistudio.modelhub.v1.CheckModelForSyncResponse
	68, // 96: apis.aistudio.modelhub.v1.ModelHubService.ListSyncModelRecord:output_type -> apis.aistudio.modelhub.v1.ListSyncModelRecordResponse
	72, // 97: apis.aistudio.modelhub.v1.ModelHubService.StopSyncModel:output_type -> apis.aistudio.modelhub.v1.StopSyncModelResponse
	76, // 98: apis.aistudio.modelhub.v1.ModelHubService.UpdateWarmupConfig:output_type -> apis.aistudio.modelhub.v1.UpdateWarmupConfigResponse
	78, // 99: apis.aistudio.modelhub.v1.ModelHubService.StartWarmupJob:output_type -> apis.aistudio.modelhub.v1.StartWarmupJobResponse
	80, // 100: apis.aistudio.modelhub.v1.ModelHubService.StopWarmupJob:output_type -> apis.aistudio.modelhub.v1.StopWarmupJobResponse
	82, // 101: apis.aistudio.modelhub.v1.ModelHubService.ListWarmupRecords:output_type -> apis.aistudio.modelhub.v1.ListWarmupRecordsResponse
	87, // 102: apis.aistudio.modelhub.v1.ModelHubService.ListWarmedModels:output_type -> apis.aistudio.modelhub.v1.ListWarmedModelsResponse
	93, // 103: apis.aistudio.modelhub.v1.ModelHubService.GitCallback:output_type -> apis.aistudio.modelhub.v1.GitCallbackResponse
	95, // 104: apis.aistudio.modelhub.v1.ModelHubService.GetModelWarmupRatio:output_type -> apis.aistudio.modelhub.v1.GetModelWarmupRatioResponse
	97, // 105: apis.aistudio.modelhub.v1.ModelHubService.CreateFork:output_type -> google.protobuf.Empty
	1,  // 106: apis.aistudio.modelhub.v1.ModelHubService.CheckModelWarmed:output_type -> apis.aistudio.modelhub.v1.CheckModelWarmedResponse
	69, // [69:107] is the sub-list for method output_type
	31, // [31:69] is the sub-list for method input_type
	31, // [31:31] is the sub-list for extension type_name
	31, // [31:31] is the sub-list for extension extendee
	0,  // [0:31] is the sub-list for field type_name
}

func init() { file_aistudio_modelhub_v1_modelhub_proto_init() }
func file_aistudio_modelhub_v1_modelhub_proto_init() {
	if File_aistudio_modelhub_v1_modelhub_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_aistudio_modelhub_v1_modelhub_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*CheckModelWarmedRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_modelhub_v1_modelhub_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*CheckModelWarmedResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_modelhub_v1_modelhub_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*CreateForkRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_modelhub_v1_modelhub_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*SDKModelInfoRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_modelhub_v1_modelhub_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*SDKModelInfoResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_modelhub_v1_modelhub_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*Sibling); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_modelhub_v1_modelhub_proto_msgTypes[6].Exporter = func(v any, i int) any {
			switch v := v.(*CreateModelRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_modelhub_v1_modelhub_proto_msgTypes[7].Exporter = func(v any, i int) any {
			switch v := v.(*UpdateModelRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_modelhub_v1_modelhub_proto_msgTypes[8].Exporter = func(v any, i int) any {
			switch v := v.(*ListModelsOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_modelhub_v1_modelhub_proto_msgTypes[9].Exporter = func(v any, i int) any {
			switch v := v.(*ListModelResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_modelhub_v1_modelhub_proto_msgTypes[10].Exporter = func(v any, i int) any {
			switch v := v.(*Model); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_modelhub_v1_modelhub_proto_msgTypes[11].Exporter = func(v any, i int) any {
			switch v := v.(*Repository); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_modelhub_v1_modelhub_proto_msgTypes[12].Exporter = func(v any, i int) any {
			switch v := v.(*DeleteModelRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_modelhub_v1_modelhub_proto_msgTypes[13].Exporter = func(v any, i int) any {
			switch v := v.(*ShowModelRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_modelhub_v1_modelhub_proto_msgTypes[14].Exporter = func(v any, i int) any {
			switch v := v.(*ShowModelResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_modelhub_v1_modelhub_proto_msgTypes[15].Exporter = func(v any, i int) any {
			switch v := v.(*GetGitTokenRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_modelhub_v1_modelhub_proto_msgTypes[16].Exporter = func(v any, i int) any {
			switch v := v.(*GetGitTokenResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_modelhub_v1_modelhub_proto_msgTypes[17].Exporter = func(v any, i int) any {
			switch v := v.(*GitToken); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_modelhub_v1_modelhub_proto_msgTypes[18].Exporter = func(v any, i int) any {
			switch v := v.(*CreateUserRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_modelhub_v1_modelhub_proto_msgTypes[19].Exporter = func(v any, i int) any {
			switch v := v.(*GetTreeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_modelhub_v1_modelhub_proto_msgTypes[20].Exporter = func(v any, i int) any {
			switch v := v.(*GetTreeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_modelhub_v1_modelhub_proto_msgTypes[21].Exporter = func(v any, i int) any {
			switch v := v.(*File); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_modelhub_v1_modelhub_proto_msgTypes[22].Exporter = func(v any, i int) any {
			switch v := v.(*Commit); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_modelhub_v1_modelhub_proto_msgTypes[23].Exporter = func(v any, i int) any {
			switch v := v.(*GetLastCommitRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_modelhub_v1_modelhub_proto_msgTypes[24].Exporter = func(v any, i int) any {
			switch v := v.(*GetLastCommitResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_modelhub_v1_modelhub_proto_msgTypes[25].Exporter = func(v any, i int) any {
			switch v := v.(*GetRepoFileContentRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_modelhub_v1_modelhub_proto_msgTypes[26].Exporter = func(v any, i int) any {
			switch v := v.(*GetRepoFileContentResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_modelhub_v1_modelhub_proto_msgTypes[27].Exporter = func(v any, i int) any {
			switch v := v.(*DownloadFileRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_modelhub_v1_modelhub_proto_msgTypes[28].Exporter = func(v any, i int) any {
			switch v := v.(*DownloadFileResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_modelhub_v1_modelhub_proto_msgTypes[29].Exporter = func(v any, i int) any {
			switch v := v.(*CreateModelHubRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_modelhub_v1_modelhub_proto_msgTypes[30].Exporter = func(v any, i int) any {
			switch v := v.(*CreateModelHubResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_modelhub_v1_modelhub_proto_msgTypes[31].Exporter = func(v any, i int) any {
			switch v := v.(*GetModelHubRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_modelhub_v1_modelhub_proto_msgTypes[32].Exporter = func(v any, i int) any {
			switch v := v.(*GetModelHubResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_modelhub_v1_modelhub_proto_msgTypes[33].Exporter = func(v any, i int) any {
			switch v := v.(*GetModelHubUserRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_modelhub_v1_modelhub_proto_msgTypes[34].Exporter = func(v any, i int) any {
			switch v := v.(*GetModelHubUserResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_modelhub_v1_modelhub_proto_msgTypes[35].Exporter = func(v any, i int) any {
			switch v := v.(*AddCollaboratorRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_modelhub_v1_modelhub_proto_msgTypes[36].Exporter = func(v any, i int) any {
			switch v := v.(*AddCollaboratorResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_modelhub_v1_modelhub_proto_msgTypes[37].Exporter = func(v any, i int) any {
			switch v := v.(*DeleteCollaboratorRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_modelhub_v1_modelhub_proto_msgTypes[38].Exporter = func(v any, i int) any {
			switch v := v.(*ChangeCollaboratorRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_modelhub_v1_modelhub_proto_msgTypes[39].Exporter = func(v any, i int) any {
			switch v := v.(*ListCollaboratorRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_modelhub_v1_modelhub_proto_msgTypes[40].Exporter = func(v any, i int) any {
			switch v := v.(*ListCollaboratorResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_modelhub_v1_modelhub_proto_msgTypes[41].Exporter = func(v any, i int) any {
			switch v := v.(*Collaborator); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_modelhub_v1_modelhub_proto_msgTypes[42].Exporter = func(v any, i int) any {
			switch v := v.(*CheckModelIsExistRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_modelhub_v1_modelhub_proto_msgTypes[43].Exporter = func(v any, i int) any {
			switch v := v.(*CheckModelIsExistResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_modelhub_v1_modelhub_proto_msgTypes[44].Exporter = func(v any, i int) any {
			switch v := v.(*InitModelHubOrgRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_modelhub_v1_modelhub_proto_msgTypes[45].Exporter = func(v any, i int) any {
			switch v := v.(*CheckCollaboratorAuthRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_modelhub_v1_modelhub_proto_msgTypes[46].Exporter = func(v any, i int) any {
			switch v := v.(*CheckCollaboratorAuthResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_modelhub_v1_modelhub_proto_msgTypes[47].Exporter = func(v any, i int) any {
			switch v := v.(*GetModelInfoRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_modelhub_v1_modelhub_proto_msgTypes[48].Exporter = func(v any, i int) any {
			switch v := v.(*GetModelInfoResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_modelhub_v1_modelhub_proto_msgTypes[49].Exporter = func(v any, i int) any {
			switch v := v.(*SDKFile); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_modelhub_v1_modelhub_proto_msgTypes[50].Exporter = func(v any, i int) any {
			switch v := v.(*GetTagsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_modelhub_v1_modelhub_proto_msgTypes[51].Exporter = func(v any, i int) any {
			switch v := v.(*GetTagsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_modelhub_v1_modelhub_proto_msgTypes[52].Exporter = func(v any, i int) any {
			switch v := v.(*Tags); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_modelhub_v1_modelhub_proto_msgTypes[53].Exporter = func(v any, i int) any {
			switch v := v.(*GetBranchesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_modelhub_v1_modelhub_proto_msgTypes[54].Exporter = func(v any, i int) any {
			switch v := v.(*GetBranchesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_modelhub_v1_modelhub_proto_msgTypes[55].Exporter = func(v any, i int) any {
			switch v := v.(*Branch); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_modelhub_v1_modelhub_proto_msgTypes[56].Exporter = func(v any, i int) any {
			switch v := v.(*CreateBranchRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_modelhub_v1_modelhub_proto_msgTypes[57].Exporter = func(v any, i int) any {
			switch v := v.(*CreateBranchResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_modelhub_v1_modelhub_proto_msgTypes[58].Exporter = func(v any, i int) any {
			switch v := v.(*GetAllFilesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_modelhub_v1_modelhub_proto_msgTypes[59].Exporter = func(v any, i int) any {
			switch v := v.(*GetAllFilesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_modelhub_v1_modelhub_proto_msgTypes[60].Exporter = func(v any, i int) any {
			switch v := v.(*CheckBranchRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_modelhub_v1_modelhub_proto_msgTypes[61].Exporter = func(v any, i int) any {
			switch v := v.(*CheckBranchResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_modelhub_v1_modelhub_proto_msgTypes[62].Exporter = func(v any, i int) any {
			switch v := v.(*SyncModelRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_modelhub_v1_modelhub_proto_msgTypes[63].Exporter = func(v any, i int) any {
			switch v := v.(*SyncModelResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_modelhub_v1_modelhub_proto_msgTypes[64].Exporter = func(v any, i int) any {
			switch v := v.(*SyncModelData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_modelhub_v1_modelhub_proto_msgTypes[65].Exporter = func(v any, i int) any {
			switch v := v.(*CheckModelForSyncRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_modelhub_v1_modelhub_proto_msgTypes[66].Exporter = func(v any, i int) any {
			switch v := v.(*CheckModelForSyncResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_modelhub_v1_modelhub_proto_msgTypes[67].Exporter = func(v any, i int) any {
			switch v := v.(*ListSyncModelRecordRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_modelhub_v1_modelhub_proto_msgTypes[68].Exporter = func(v any, i int) any {
			switch v := v.(*ListSyncModelRecordResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_modelhub_v1_modelhub_proto_msgTypes[69].Exporter = func(v any, i int) any {
			switch v := v.(*SyncModelRecord); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_modelhub_v1_modelhub_proto_msgTypes[70].Exporter = func(v any, i int) any {
			switch v := v.(*SyncCondition); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_modelhub_v1_modelhub_proto_msgTypes[71].Exporter = func(v any, i int) any {
			switch v := v.(*StopSyncModelRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_modelhub_v1_modelhub_proto_msgTypes[72].Exporter = func(v any, i int) any {
			switch v := v.(*StopSyncModelResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_modelhub_v1_modelhub_proto_msgTypes[73].Exporter = func(v any, i int) any {
			switch v := v.(*UpdateWarmupConfigRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_modelhub_v1_modelhub_proto_msgTypes[74].Exporter = func(v any, i int) any {
			switch v := v.(*WarmupConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_modelhub_v1_modelhub_proto_msgTypes[75].Exporter = func(v any, i int) any {
			switch v := v.(*WarmupRegion); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_modelhub_v1_modelhub_proto_msgTypes[76].Exporter = func(v any, i int) any {
			switch v := v.(*UpdateWarmupConfigResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_modelhub_v1_modelhub_proto_msgTypes[77].Exporter = func(v any, i int) any {
			switch v := v.(*StartWarmupJobRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_modelhub_v1_modelhub_proto_msgTypes[78].Exporter = func(v any, i int) any {
			switch v := v.(*StartWarmupJobResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_modelhub_v1_modelhub_proto_msgTypes[79].Exporter = func(v any, i int) any {
			switch v := v.(*StopWarmupJobRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_modelhub_v1_modelhub_proto_msgTypes[80].Exporter = func(v any, i int) any {
			switch v := v.(*StopWarmupJobResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_modelhub_v1_modelhub_proto_msgTypes[81].Exporter = func(v any, i int) any {
			switch v := v.(*ListWarmupRecordsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_modelhub_v1_modelhub_proto_msgTypes[82].Exporter = func(v any, i int) any {
			switch v := v.(*ListWarmupRecordsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_modelhub_v1_modelhub_proto_msgTypes[83].Exporter = func(v any, i int) any {
			switch v := v.(*WarmupRecord); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_modelhub_v1_modelhub_proto_msgTypes[84].Exporter = func(v any, i int) any {
			switch v := v.(*WarmupStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_modelhub_v1_modelhub_proto_msgTypes[85].Exporter = func(v any, i int) any {
			switch v := v.(*WarmupConditions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_modelhub_v1_modelhub_proto_msgTypes[86].Exporter = func(v any, i int) any {
			switch v := v.(*ListWarmedModelsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_modelhub_v1_modelhub_proto_msgTypes[87].Exporter = func(v any, i int) any {
			switch v := v.(*ListWarmedModelsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_modelhub_v1_modelhub_proto_msgTypes[88].Exporter = func(v any, i int) any {
			switch v := v.(*WarmedModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_modelhub_v1_modelhub_proto_msgTypes[89].Exporter = func(v any, i int) any {
			switch v := v.(*GitCallbackRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_modelhub_v1_modelhub_proto_msgTypes[90].Exporter = func(v any, i int) any {
			switch v := v.(*CallbackRepository); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_modelhub_v1_modelhub_proto_msgTypes[91].Exporter = func(v any, i int) any {
			switch v := v.(*Owner); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_modelhub_v1_modelhub_proto_msgTypes[92].Exporter = func(v any, i int) any {
			switch v := v.(*Pusher); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_modelhub_v1_modelhub_proto_msgTypes[93].Exporter = func(v any, i int) any {
			switch v := v.(*GitCallbackResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_modelhub_v1_modelhub_proto_msgTypes[94].Exporter = func(v any, i int) any {
			switch v := v.(*GetModelWarmupRatioRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_modelhub_v1_modelhub_proto_msgTypes[95].Exporter = func(v any, i int) any {
			switch v := v.(*GetModelWarmupRatioResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_modelhub_v1_modelhub_proto_msgTypes[96].Exporter = func(v any, i int) any {
			switch v := v.(*WarmupRatio); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_aistudio_modelhub_v1_modelhub_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   97,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_aistudio_modelhub_v1_modelhub_proto_goTypes,
		DependencyIndexes: file_aistudio_modelhub_v1_modelhub_proto_depIdxs,
		MessageInfos:      file_aistudio_modelhub_v1_modelhub_proto_msgTypes,
	}.Build()
	File_aistudio_modelhub_v1_modelhub_proto = out.File
	file_aistudio_modelhub_v1_modelhub_proto_rawDesc = nil
	file_aistudio_modelhub_v1_modelhub_proto_goTypes = nil
	file_aistudio_modelhub_v1_modelhub_proto_depIdxs = nil
}
