// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.3
// - protoc             v5.29.3
// source: aistudio/modelhub/v1/modelhub.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationModelHubServiceAddCollaborator = "/apis.aistudio.modelhub.v1.ModelHubService/AddCollaborator"
const OperationModelHubServiceChangeCollaborator = "/apis.aistudio.modelhub.v1.ModelHubService/ChangeCollaborator"
const OperationModelHubServiceCheckBranch = "/apis.aistudio.modelhub.v1.ModelHubService/CheckBranch"
const OperationModelHubServiceCheckCollaboratorAuth = "/apis.aistudio.modelhub.v1.ModelHubService/CheckCollaboratorAuth"
const OperationModelHubServiceCheckModelForSync = "/apis.aistudio.modelhub.v1.ModelHubService/CheckModelForSync"
const OperationModelHubServiceCheckModelIsExist = "/apis.aistudio.modelhub.v1.ModelHubService/CheckModelIsExist"
const OperationModelHubServiceCheckModelWarmed = "/apis.aistudio.modelhub.v1.ModelHubService/CheckModelWarmed"
const OperationModelHubServiceCreateBranch = "/apis.aistudio.modelhub.v1.ModelHubService/CreateBranch"
const OperationModelHubServiceCreateFork = "/apis.aistudio.modelhub.v1.ModelHubService/CreateFork"
const OperationModelHubServiceCreateModel = "/apis.aistudio.modelhub.v1.ModelHubService/CreateModel"
const OperationModelHubServiceCreateModelHub = "/apis.aistudio.modelhub.v1.ModelHubService/CreateModelHub"
const OperationModelHubServiceCreateUser = "/apis.aistudio.modelhub.v1.ModelHubService/CreateUser"
const OperationModelHubServiceDeleteCollaborator = "/apis.aistudio.modelhub.v1.ModelHubService/DeleteCollaborator"
const OperationModelHubServiceDeleteModel = "/apis.aistudio.modelhub.v1.ModelHubService/DeleteModel"
const OperationModelHubServiceGetAllFiles = "/apis.aistudio.modelhub.v1.ModelHubService/GetAllFiles"
const OperationModelHubServiceGetBranches = "/apis.aistudio.modelhub.v1.ModelHubService/GetBranches"
const OperationModelHubServiceGetGitToken = "/apis.aistudio.modelhub.v1.ModelHubService/GetGitToken"
const OperationModelHubServiceGetLastCommit = "/apis.aistudio.modelhub.v1.ModelHubService/GetLastCommit"
const OperationModelHubServiceGetModelHub = "/apis.aistudio.modelhub.v1.ModelHubService/GetModelHub"
const OperationModelHubServiceGetModelInfo = "/apis.aistudio.modelhub.v1.ModelHubService/GetModelInfo"
const OperationModelHubServiceGetModelWarmupRatio = "/apis.aistudio.modelhub.v1.ModelHubService/GetModelWarmupRatio"
const OperationModelHubServiceGetRepoFileContent = "/apis.aistudio.modelhub.v1.ModelHubService/GetRepoFileContent"
const OperationModelHubServiceGetTags = "/apis.aistudio.modelhub.v1.ModelHubService/GetTags"
const OperationModelHubServiceGetTree = "/apis.aistudio.modelhub.v1.ModelHubService/GetTree"
const OperationModelHubServiceGitCallback = "/apis.aistudio.modelhub.v1.ModelHubService/GitCallback"
const OperationModelHubServiceInitModelHubOrg = "/apis.aistudio.modelhub.v1.ModelHubService/InitModelHubOrg"
const OperationModelHubServiceListCollaborator = "/apis.aistudio.modelhub.v1.ModelHubService/ListCollaborator"
const OperationModelHubServiceListModels = "/apis.aistudio.modelhub.v1.ModelHubService/ListModels"
const OperationModelHubServiceListSyncModelRecord = "/apis.aistudio.modelhub.v1.ModelHubService/ListSyncModelRecord"
const OperationModelHubServiceListWarmedModels = "/apis.aistudio.modelhub.v1.ModelHubService/ListWarmedModels"
const OperationModelHubServiceListWarmupRecords = "/apis.aistudio.modelhub.v1.ModelHubService/ListWarmupRecords"
const OperationModelHubServiceShowModel = "/apis.aistudio.modelhub.v1.ModelHubService/ShowModel"
const OperationModelHubServiceStartWarmupJob = "/apis.aistudio.modelhub.v1.ModelHubService/StartWarmupJob"
const OperationModelHubServiceStopSyncModel = "/apis.aistudio.modelhub.v1.ModelHubService/StopSyncModel"
const OperationModelHubServiceStopWarmupJob = "/apis.aistudio.modelhub.v1.ModelHubService/StopWarmupJob"
const OperationModelHubServiceSyncModel = "/apis.aistudio.modelhub.v1.ModelHubService/SyncModel"
const OperationModelHubServiceUpdateModel = "/apis.aistudio.modelhub.v1.ModelHubService/UpdateModel"
const OperationModelHubServiceUpdateWarmupConfig = "/apis.aistudio.modelhub.v1.ModelHubService/UpdateWarmupConfig"

type ModelHubServiceHTTPServer interface {
	// AddCollaborator添加协作用户
	AddCollaborator(context.Context, *AddCollaboratorRequest) (*AddCollaboratorResponse, error)
	// ChangeCollaborator修改协作用户权限
	ChangeCollaborator(context.Context, *ChangeCollaboratorRequest) (*emptypb.Empty, error)
	// CheckBranch 校验分支是否可创建
	CheckBranch(context.Context, *CheckBranchRequest) (*CheckBranchResponse, error)
	// CheckCollaboratorAuth检查是否拥有协作者权限
	CheckCollaboratorAuth(context.Context, *CheckCollaboratorAuthRequest) (*CheckCollaboratorAuthResponse, error)
	// CheckModelForSync校验模型是否可同步
	CheckModelForSync(context.Context, *CheckModelForSyncRequest) (*CheckModelForSyncResponse, error)
	// CheckModelIsExist检查模型是否存在
	CheckModelIsExist(context.Context, *CheckModelIsExistRequest) (*CheckModelIsExistResponse, error)
	// CheckModelWarmed 校验模型是否预热过
	CheckModelWarmed(context.Context, *CheckModelWarmedRequest) (*CheckModelWarmedResponse, error)
	// CreateBranch 创建分支
	CreateBranch(context.Context, *CreateBranchRequest) (*CreateBranchResponse, error)
	// CreateFork 创建fork
	CreateFork(context.Context, *CreateForkRequest) (*emptypb.Empty, error)
	// CreateModel 添加模型
	CreateModel(context.Context, *CreateModelRequest) (*emptypb.Empty, error)
	// CreateModelHub创建模型仓库
	CreateModelHub(context.Context, *CreateModelHubRequest) (*CreateModelHubResponse, error)
	// CreateUser创建模型仓库用户和token
	CreateUser(context.Context, *CreateUserRequest) (*emptypb.Empty, error)
	// DeleteCollaborator删除协作用户
	DeleteCollaborator(context.Context, *DeleteCollaboratorRequest) (*emptypb.Empty, error)
	// DeleteModel删除模型
	DeleteModel(context.Context, *DeleteModelRequest) (*emptypb.Empty, error)
	// GetAllFiles 获取全部文件
	GetAllFiles(context.Context, *GetAllFilesRequest) (*GetAllFilesResponse, error)
	// GetBranches获取模型分支
	GetBranches(context.Context, *GetBranchesRequest) (*GetBranchesResponse, error)
	// GetGitToken获取git token
	GetGitToken(context.Context, *GetGitTokenRequest) (*GetGitTokenResponse, error)
	// GetLastCommit获取repo last commit
	GetLastCommit(context.Context, *GetLastCommitRequest) (*GetLastCommitResponse, error)
	// GetModelHub获取模型仓库
	GetModelHub(context.Context, *GetModelHubRequest) (*GetModelHubResponse, error)
	// GetModelInfo获取模型信息
	GetModelInfo(context.Context, *GetModelInfoRequest) (*GetModelInfoResponse, error)
	// GetModelWarmupRatio获取预热比例
	GetModelWarmupRatio(context.Context, *GetModelWarmupRatioRequest) (*GetModelWarmupRatioResponse, error)
	// GetRepoFileContent获取repo 文件内容
	GetRepoFileContent(context.Context, *GetRepoFileContentRequest) (*GetRepoFileContentResponse, error)
	// GetTags获取模型tag
	GetTags(context.Context, *GetTagsRequest) (*GetTagsResponse, error)
	// GetTree获取repo tree
	GetTree(context.Context, *GetTreeRequest) (*GetTreeResponse, error)
	// GitCallbackGit回调
	GitCallback(context.Context, *GitCallbackRequest) (*GitCallbackResponse, error)
	// InitModelHubOrg初始化组织
	InitModelHubOrg(context.Context, *InitModelHubOrgRequest) (*emptypb.Empty, error)
	// ListCollaborator查询协作用户
	ListCollaborator(context.Context, *ListCollaboratorRequest) (*ListCollaboratorResponse, error)
	// ListModels 获取模型列表
	ListModels(context.Context, *ListModelsOptions) (*ListModelResponse, error)
	// ListSyncModelRecord展示同步记录
	ListSyncModelRecord(context.Context, *ListSyncModelRecordRequest) (*ListSyncModelRecordResponse, error)
	// ListWarmedModels获取预热模型列表
	ListWarmedModels(context.Context, *ListWarmedModelsRequest) (*ListWarmedModelsResponse, error)
	// ListWarmupRecords获取预热记录列表
	ListWarmupRecords(context.Context, *ListWarmupRecordsRequest) (*ListWarmupRecordsResponse, error)
	// ShowModel展示模型
	ShowModel(context.Context, *ShowModelRequest) (*ShowModelResponse, error)
	// StartWarmupJob开始预热
	StartWarmupJob(context.Context, *StartWarmupJobRequest) (*StartWarmupJobResponse, error)
	// StopSyncModel停止同步
	StopSyncModel(context.Context, *StopSyncModelRequest) (*StopSyncModelResponse, error)
	// StopWarmupJob停止预热
	StopWarmupJob(context.Context, *StopWarmupJobRequest) (*StopWarmupJobResponse, error)
	// SyncModel同步模型
	SyncModel(context.Context, *SyncModelRequest) (*SyncModelResponse, error)
	// UpdateModel 更新模型
	UpdateModel(context.Context, *UpdateModelRequest) (*emptypb.Empty, error)
	// UpdateWarmupConfig更新预热配置
	UpdateWarmupConfig(context.Context, *UpdateWarmupConfigRequest) (*UpdateWarmupConfigResponse, error)
}

func RegisterModelHubServiceHTTPServer(s *http.Server, srv ModelHubServiceHTTPServer) {
	r := s.Route("/")
	r.POST("/apis/v1/model-hub/model", _ModelHubService_CreateModel0_HTTP_Handler(srv))
	r.GET("/apis/v1/model-hub/models", _ModelHubService_ListModels0_HTTP_Handler(srv))
	r.PUT("/apis/v1/model-hub/model", _ModelHubService_UpdateModel0_HTTP_Handler(srv))
	r.DELETE("/apis/v1/model-hub/model", _ModelHubService_DeleteModel0_HTTP_Handler(srv))
	r.GET("/apis/v1/model-hub/model", _ModelHubService_ShowModel0_HTTP_Handler(srv))
	r.GET("/apis/v1/model-hub/token", _ModelHubService_GetGitToken0_HTTP_Handler(srv))
	r.POST("/apis/v1/model-hub/createmodelhub", _ModelHubService_CreateModelHub0_HTTP_Handler(srv))
	r.GET("/apis/v1/model-hub/getmodelhub", _ModelHubService_GetModelHub0_HTTP_Handler(srv))
	r.GET("/apis/v1/model-hub/tree", _ModelHubService_GetTree0_HTTP_Handler(srv))
	r.GET("/apis/v1/model-hub/lastcommit", _ModelHubService_GetLastCommit0_HTTP_Handler(srv))
	r.GET("/apis/v1/model-hub/getrepofilecontent", _ModelHubService_GetRepoFileContent0_HTTP_Handler(srv))
	r.POST("/apis/v1/model-hub/user", _ModelHubService_CreateUser0_HTTP_Handler(srv))
	r.POST("/apis/v1/model-hub/collaborator", _ModelHubService_AddCollaborator0_HTTP_Handler(srv))
	r.DELETE("/apis/v1/model-hub/collaborator", _ModelHubService_DeleteCollaborator0_HTTP_Handler(srv))
	r.PUT("/apis/v1/model-hub/collaborator", _ModelHubService_ChangeCollaborator0_HTTP_Handler(srv))
	r.GET("/apis/v1/model-hub/collaborator", _ModelHubService_ListCollaborator0_HTTP_Handler(srv))
	r.GET("/apis/v1/model-hub/model_exist", _ModelHubService_CheckModelIsExist0_HTTP_Handler(srv))
	r.GET("/apis/v1/model-hub/check_collaborator_auth", _ModelHubService_CheckCollaboratorAuth0_HTTP_Handler(srv))
	r.POST("/apis/v1/model-hub/init_model_hub_org", _ModelHubService_InitModelHubOrg0_HTTP_Handler(srv))
	r.GET("/apis/v1/model-hub/get_model_info", _ModelHubService_GetModelInfo0_HTTP_Handler(srv))
	r.GET("/apis/v1/model-hub/get_tags", _ModelHubService_GetTags0_HTTP_Handler(srv))
	r.GET("/apis/v1/model-hub/get_branches", _ModelHubService_GetBranches0_HTTP_Handler(srv))
	r.POST("/apis/v1/model-hub/create_branch", _ModelHubService_CreateBranch0_HTTP_Handler(srv))
	r.GET("/apis/v1/model-hub/all-files", _ModelHubService_GetAllFiles0_HTTP_Handler(srv))
	r.GET("/apis/v1/model-hub/check_branch", _ModelHubService_CheckBranch0_HTTP_Handler(srv))
	r.POST("/apis/v1/model-hub/sync_model", _ModelHubService_SyncModel0_HTTP_Handler(srv))
	r.GET("/apis/v1/model-hub/check_model_for_sync", _ModelHubService_CheckModelForSync0_HTTP_Handler(srv))
	r.GET("/apis/v1/model-hub/list_sync_model_record", _ModelHubService_ListSyncModelRecord0_HTTP_Handler(srv))
	r.POST("/apis/v1/model-hub/stopsyncmodel", _ModelHubService_StopSyncModel0_HTTP_Handler(srv))
	r.PUT("/apis/v1/model-hub/{workspaceName}/{modelName}/warmupconfig", _ModelHubService_UpdateWarmupConfig0_HTTP_Handler(srv))
	r.POST("/apis/v1/model-hub/{workspaceName}/{modelName}/warmupjob/start", _ModelHubService_StartWarmupJob0_HTTP_Handler(srv))
	r.POST("/apis/v1/model-hub/{workspaceName}/{modelName}/warmupjob/stop", _ModelHubService_StopWarmupJob0_HTTP_Handler(srv))
	r.GET("/apis/v1/model-hub/{workspaceName}/warmuprecords", _ModelHubService_ListWarmupRecords0_HTTP_Handler(srv))
	r.GET("/apis/v1/model-hub/{workspaceName}/warmedmodels", _ModelHubService_ListWarmedModels0_HTTP_Handler(srv))
	r.POST("/apis/v1/model-hub/git/callback", _ModelHubService_GitCallback0_HTTP_Handler(srv))
	r.GET("/apis/v1/model-hub/warmupratio", _ModelHubService_GetModelWarmupRatio0_HTTP_Handler(srv))
	r.POST("/apis/v1/model-hub/fork", _ModelHubService_CreateFork0_HTTP_Handler(srv))
	r.GET("/apis/v1/model-hub/warmedmodel/{workspaceName}/{modelName}/check", _ModelHubService_CheckModelWarmed0_HTTP_Handler(srv))
}

func _ModelHubService_CreateModel0_HTTP_Handler(srv ModelHubServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateModelRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationModelHubServiceCreateModel)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateModel(ctx, req.(*CreateModelRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _ModelHubService_ListModels0_HTTP_Handler(srv ModelHubServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListModelsOptions
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationModelHubServiceListModels)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListModels(ctx, req.(*ListModelsOptions))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListModelResponse)
		return ctx.Result(200, reply)
	}
}

func _ModelHubService_UpdateModel0_HTTP_Handler(srv ModelHubServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UpdateModelRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationModelHubServiceUpdateModel)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateModel(ctx, req.(*UpdateModelRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _ModelHubService_DeleteModel0_HTTP_Handler(srv ModelHubServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteModelRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationModelHubServiceDeleteModel)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteModel(ctx, req.(*DeleteModelRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _ModelHubService_ShowModel0_HTTP_Handler(srv ModelHubServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ShowModelRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationModelHubServiceShowModel)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ShowModel(ctx, req.(*ShowModelRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ShowModelResponse)
		return ctx.Result(200, reply)
	}
}

func _ModelHubService_GetGitToken0_HTTP_Handler(srv ModelHubServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetGitTokenRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationModelHubServiceGetGitToken)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetGitToken(ctx, req.(*GetGitTokenRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetGitTokenResponse)
		return ctx.Result(200, reply)
	}
}

func _ModelHubService_CreateModelHub0_HTTP_Handler(srv ModelHubServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateModelHubRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationModelHubServiceCreateModelHub)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateModelHub(ctx, req.(*CreateModelHubRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CreateModelHubResponse)
		return ctx.Result(200, reply)
	}
}

func _ModelHubService_GetModelHub0_HTTP_Handler(srv ModelHubServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetModelHubRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationModelHubServiceGetModelHub)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetModelHub(ctx, req.(*GetModelHubRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetModelHubResponse)
		return ctx.Result(200, reply)
	}
}

func _ModelHubService_GetTree0_HTTP_Handler(srv ModelHubServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetTreeRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationModelHubServiceGetTree)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetTree(ctx, req.(*GetTreeRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetTreeResponse)
		return ctx.Result(200, reply)
	}
}

func _ModelHubService_GetLastCommit0_HTTP_Handler(srv ModelHubServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetLastCommitRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationModelHubServiceGetLastCommit)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetLastCommit(ctx, req.(*GetLastCommitRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetLastCommitResponse)
		return ctx.Result(200, reply)
	}
}

func _ModelHubService_GetRepoFileContent0_HTTP_Handler(srv ModelHubServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetRepoFileContentRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationModelHubServiceGetRepoFileContent)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetRepoFileContent(ctx, req.(*GetRepoFileContentRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetRepoFileContentResponse)
		return ctx.Result(200, reply)
	}
}

func _ModelHubService_CreateUser0_HTTP_Handler(srv ModelHubServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateUserRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationModelHubServiceCreateUser)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateUser(ctx, req.(*CreateUserRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _ModelHubService_AddCollaborator0_HTTP_Handler(srv ModelHubServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in AddCollaboratorRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationModelHubServiceAddCollaborator)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.AddCollaborator(ctx, req.(*AddCollaboratorRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*AddCollaboratorResponse)
		return ctx.Result(200, reply)
	}
}

func _ModelHubService_DeleteCollaborator0_HTTP_Handler(srv ModelHubServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteCollaboratorRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationModelHubServiceDeleteCollaborator)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteCollaborator(ctx, req.(*DeleteCollaboratorRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _ModelHubService_ChangeCollaborator0_HTTP_Handler(srv ModelHubServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ChangeCollaboratorRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationModelHubServiceChangeCollaborator)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ChangeCollaborator(ctx, req.(*ChangeCollaboratorRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _ModelHubService_ListCollaborator0_HTTP_Handler(srv ModelHubServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListCollaboratorRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationModelHubServiceListCollaborator)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListCollaborator(ctx, req.(*ListCollaboratorRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListCollaboratorResponse)
		return ctx.Result(200, reply)
	}
}

func _ModelHubService_CheckModelIsExist0_HTTP_Handler(srv ModelHubServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CheckModelIsExistRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationModelHubServiceCheckModelIsExist)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CheckModelIsExist(ctx, req.(*CheckModelIsExistRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CheckModelIsExistResponse)
		return ctx.Result(200, reply)
	}
}

func _ModelHubService_CheckCollaboratorAuth0_HTTP_Handler(srv ModelHubServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CheckCollaboratorAuthRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationModelHubServiceCheckCollaboratorAuth)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CheckCollaboratorAuth(ctx, req.(*CheckCollaboratorAuthRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CheckCollaboratorAuthResponse)
		return ctx.Result(200, reply)
	}
}

func _ModelHubService_InitModelHubOrg0_HTTP_Handler(srv ModelHubServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in InitModelHubOrgRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationModelHubServiceInitModelHubOrg)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.InitModelHubOrg(ctx, req.(*InitModelHubOrgRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _ModelHubService_GetModelInfo0_HTTP_Handler(srv ModelHubServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetModelInfoRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationModelHubServiceGetModelInfo)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetModelInfo(ctx, req.(*GetModelInfoRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetModelInfoResponse)
		return ctx.Result(200, reply)
	}
}

func _ModelHubService_GetTags0_HTTP_Handler(srv ModelHubServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetTagsRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationModelHubServiceGetTags)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetTags(ctx, req.(*GetTagsRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetTagsResponse)
		return ctx.Result(200, reply)
	}
}

func _ModelHubService_GetBranches0_HTTP_Handler(srv ModelHubServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetBranchesRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationModelHubServiceGetBranches)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetBranches(ctx, req.(*GetBranchesRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetBranchesResponse)
		return ctx.Result(200, reply)
	}
}

func _ModelHubService_CreateBranch0_HTTP_Handler(srv ModelHubServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateBranchRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationModelHubServiceCreateBranch)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateBranch(ctx, req.(*CreateBranchRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CreateBranchResponse)
		return ctx.Result(200, reply)
	}
}

func _ModelHubService_GetAllFiles0_HTTP_Handler(srv ModelHubServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetAllFilesRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationModelHubServiceGetAllFiles)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetAllFiles(ctx, req.(*GetAllFilesRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetAllFilesResponse)
		return ctx.Result(200, reply)
	}
}

func _ModelHubService_CheckBranch0_HTTP_Handler(srv ModelHubServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CheckBranchRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationModelHubServiceCheckBranch)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CheckBranch(ctx, req.(*CheckBranchRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CheckBranchResponse)
		return ctx.Result(200, reply)
	}
}

func _ModelHubService_SyncModel0_HTTP_Handler(srv ModelHubServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in SyncModelRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationModelHubServiceSyncModel)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SyncModel(ctx, req.(*SyncModelRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*SyncModelResponse)
		return ctx.Result(200, reply)
	}
}

func _ModelHubService_CheckModelForSync0_HTTP_Handler(srv ModelHubServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CheckModelForSyncRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationModelHubServiceCheckModelForSync)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CheckModelForSync(ctx, req.(*CheckModelForSyncRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CheckModelForSyncResponse)
		return ctx.Result(200, reply)
	}
}

func _ModelHubService_ListSyncModelRecord0_HTTP_Handler(srv ModelHubServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListSyncModelRecordRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationModelHubServiceListSyncModelRecord)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListSyncModelRecord(ctx, req.(*ListSyncModelRecordRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListSyncModelRecordResponse)
		return ctx.Result(200, reply)
	}
}

func _ModelHubService_StopSyncModel0_HTTP_Handler(srv ModelHubServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in StopSyncModelRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationModelHubServiceStopSyncModel)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.StopSyncModel(ctx, req.(*StopSyncModelRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*StopSyncModelResponse)
		return ctx.Result(200, reply)
	}
}

func _ModelHubService_UpdateWarmupConfig0_HTTP_Handler(srv ModelHubServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UpdateWarmupConfigRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationModelHubServiceUpdateWarmupConfig)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateWarmupConfig(ctx, req.(*UpdateWarmupConfigRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*UpdateWarmupConfigResponse)
		return ctx.Result(200, reply)
	}
}

func _ModelHubService_StartWarmupJob0_HTTP_Handler(srv ModelHubServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in StartWarmupJobRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationModelHubServiceStartWarmupJob)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.StartWarmupJob(ctx, req.(*StartWarmupJobRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*StartWarmupJobResponse)
		return ctx.Result(200, reply)
	}
}

func _ModelHubService_StopWarmupJob0_HTTP_Handler(srv ModelHubServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in StopWarmupJobRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationModelHubServiceStopWarmupJob)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.StopWarmupJob(ctx, req.(*StopWarmupJobRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*StopWarmupJobResponse)
		return ctx.Result(200, reply)
	}
}

func _ModelHubService_ListWarmupRecords0_HTTP_Handler(srv ModelHubServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListWarmupRecordsRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationModelHubServiceListWarmupRecords)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListWarmupRecords(ctx, req.(*ListWarmupRecordsRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListWarmupRecordsResponse)
		return ctx.Result(200, reply)
	}
}

func _ModelHubService_ListWarmedModels0_HTTP_Handler(srv ModelHubServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListWarmedModelsRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationModelHubServiceListWarmedModels)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListWarmedModels(ctx, req.(*ListWarmedModelsRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListWarmedModelsResponse)
		return ctx.Result(200, reply)
	}
}

func _ModelHubService_GitCallback0_HTTP_Handler(srv ModelHubServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GitCallbackRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationModelHubServiceGitCallback)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GitCallback(ctx, req.(*GitCallbackRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GitCallbackResponse)
		return ctx.Result(200, reply)
	}
}

func _ModelHubService_GetModelWarmupRatio0_HTTP_Handler(srv ModelHubServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetModelWarmupRatioRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationModelHubServiceGetModelWarmupRatio)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetModelWarmupRatio(ctx, req.(*GetModelWarmupRatioRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetModelWarmupRatioResponse)
		return ctx.Result(200, reply)
	}
}

func _ModelHubService_CreateFork0_HTTP_Handler(srv ModelHubServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateForkRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationModelHubServiceCreateFork)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateFork(ctx, req.(*CreateForkRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _ModelHubService_CheckModelWarmed0_HTTP_Handler(srv ModelHubServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CheckModelWarmedRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationModelHubServiceCheckModelWarmed)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CheckModelWarmed(ctx, req.(*CheckModelWarmedRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CheckModelWarmedResponse)
		return ctx.Result(200, reply)
	}
}

type ModelHubServiceHTTPClient interface {
	AddCollaborator(ctx context.Context, req *AddCollaboratorRequest, opts ...http.CallOption) (rsp *AddCollaboratorResponse, err error)
	ChangeCollaborator(ctx context.Context, req *ChangeCollaboratorRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	CheckBranch(ctx context.Context, req *CheckBranchRequest, opts ...http.CallOption) (rsp *CheckBranchResponse, err error)
	CheckCollaboratorAuth(ctx context.Context, req *CheckCollaboratorAuthRequest, opts ...http.CallOption) (rsp *CheckCollaboratorAuthResponse, err error)
	CheckModelForSync(ctx context.Context, req *CheckModelForSyncRequest, opts ...http.CallOption) (rsp *CheckModelForSyncResponse, err error)
	CheckModelIsExist(ctx context.Context, req *CheckModelIsExistRequest, opts ...http.CallOption) (rsp *CheckModelIsExistResponse, err error)
	CheckModelWarmed(ctx context.Context, req *CheckModelWarmedRequest, opts ...http.CallOption) (rsp *CheckModelWarmedResponse, err error)
	CreateBranch(ctx context.Context, req *CreateBranchRequest, opts ...http.CallOption) (rsp *CreateBranchResponse, err error)
	CreateFork(ctx context.Context, req *CreateForkRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	CreateModel(ctx context.Context, req *CreateModelRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	CreateModelHub(ctx context.Context, req *CreateModelHubRequest, opts ...http.CallOption) (rsp *CreateModelHubResponse, err error)
	CreateUser(ctx context.Context, req *CreateUserRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	DeleteCollaborator(ctx context.Context, req *DeleteCollaboratorRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	DeleteModel(ctx context.Context, req *DeleteModelRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	GetAllFiles(ctx context.Context, req *GetAllFilesRequest, opts ...http.CallOption) (rsp *GetAllFilesResponse, err error)
	GetBranches(ctx context.Context, req *GetBranchesRequest, opts ...http.CallOption) (rsp *GetBranchesResponse, err error)
	GetGitToken(ctx context.Context, req *GetGitTokenRequest, opts ...http.CallOption) (rsp *GetGitTokenResponse, err error)
	GetLastCommit(ctx context.Context, req *GetLastCommitRequest, opts ...http.CallOption) (rsp *GetLastCommitResponse, err error)
	GetModelHub(ctx context.Context, req *GetModelHubRequest, opts ...http.CallOption) (rsp *GetModelHubResponse, err error)
	GetModelInfo(ctx context.Context, req *GetModelInfoRequest, opts ...http.CallOption) (rsp *GetModelInfoResponse, err error)
	GetModelWarmupRatio(ctx context.Context, req *GetModelWarmupRatioRequest, opts ...http.CallOption) (rsp *GetModelWarmupRatioResponse, err error)
	GetRepoFileContent(ctx context.Context, req *GetRepoFileContentRequest, opts ...http.CallOption) (rsp *GetRepoFileContentResponse, err error)
	GetTags(ctx context.Context, req *GetTagsRequest, opts ...http.CallOption) (rsp *GetTagsResponse, err error)
	GetTree(ctx context.Context, req *GetTreeRequest, opts ...http.CallOption) (rsp *GetTreeResponse, err error)
	GitCallback(ctx context.Context, req *GitCallbackRequest, opts ...http.CallOption) (rsp *GitCallbackResponse, err error)
	InitModelHubOrg(ctx context.Context, req *InitModelHubOrgRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	ListCollaborator(ctx context.Context, req *ListCollaboratorRequest, opts ...http.CallOption) (rsp *ListCollaboratorResponse, err error)
	ListModels(ctx context.Context, req *ListModelsOptions, opts ...http.CallOption) (rsp *ListModelResponse, err error)
	ListSyncModelRecord(ctx context.Context, req *ListSyncModelRecordRequest, opts ...http.CallOption) (rsp *ListSyncModelRecordResponse, err error)
	ListWarmedModels(ctx context.Context, req *ListWarmedModelsRequest, opts ...http.CallOption) (rsp *ListWarmedModelsResponse, err error)
	ListWarmupRecords(ctx context.Context, req *ListWarmupRecordsRequest, opts ...http.CallOption) (rsp *ListWarmupRecordsResponse, err error)
	ShowModel(ctx context.Context, req *ShowModelRequest, opts ...http.CallOption) (rsp *ShowModelResponse, err error)
	StartWarmupJob(ctx context.Context, req *StartWarmupJobRequest, opts ...http.CallOption) (rsp *StartWarmupJobResponse, err error)
	StopSyncModel(ctx context.Context, req *StopSyncModelRequest, opts ...http.CallOption) (rsp *StopSyncModelResponse, err error)
	StopWarmupJob(ctx context.Context, req *StopWarmupJobRequest, opts ...http.CallOption) (rsp *StopWarmupJobResponse, err error)
	SyncModel(ctx context.Context, req *SyncModelRequest, opts ...http.CallOption) (rsp *SyncModelResponse, err error)
	UpdateModel(ctx context.Context, req *UpdateModelRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	UpdateWarmupConfig(ctx context.Context, req *UpdateWarmupConfigRequest, opts ...http.CallOption) (rsp *UpdateWarmupConfigResponse, err error)
}

type ModelHubServiceHTTPClientImpl struct {
	cc *http.Client
}

func NewModelHubServiceHTTPClient(client *http.Client) ModelHubServiceHTTPClient {
	return &ModelHubServiceHTTPClientImpl{client}
}

func (c *ModelHubServiceHTTPClientImpl) AddCollaborator(ctx context.Context, in *AddCollaboratorRequest, opts ...http.CallOption) (*AddCollaboratorResponse, error) {
	var out AddCollaboratorResponse
	pattern := "/apis/v1/model-hub/collaborator"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationModelHubServiceAddCollaborator))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ModelHubServiceHTTPClientImpl) ChangeCollaborator(ctx context.Context, in *ChangeCollaboratorRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/apis/v1/model-hub/collaborator"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationModelHubServiceChangeCollaborator))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ModelHubServiceHTTPClientImpl) CheckBranch(ctx context.Context, in *CheckBranchRequest, opts ...http.CallOption) (*CheckBranchResponse, error) {
	var out CheckBranchResponse
	pattern := "/apis/v1/model-hub/check_branch"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationModelHubServiceCheckBranch))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ModelHubServiceHTTPClientImpl) CheckCollaboratorAuth(ctx context.Context, in *CheckCollaboratorAuthRequest, opts ...http.CallOption) (*CheckCollaboratorAuthResponse, error) {
	var out CheckCollaboratorAuthResponse
	pattern := "/apis/v1/model-hub/check_collaborator_auth"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationModelHubServiceCheckCollaboratorAuth))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ModelHubServiceHTTPClientImpl) CheckModelForSync(ctx context.Context, in *CheckModelForSyncRequest, opts ...http.CallOption) (*CheckModelForSyncResponse, error) {
	var out CheckModelForSyncResponse
	pattern := "/apis/v1/model-hub/check_model_for_sync"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationModelHubServiceCheckModelForSync))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ModelHubServiceHTTPClientImpl) CheckModelIsExist(ctx context.Context, in *CheckModelIsExistRequest, opts ...http.CallOption) (*CheckModelIsExistResponse, error) {
	var out CheckModelIsExistResponse
	pattern := "/apis/v1/model-hub/model_exist"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationModelHubServiceCheckModelIsExist))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ModelHubServiceHTTPClientImpl) CheckModelWarmed(ctx context.Context, in *CheckModelWarmedRequest, opts ...http.CallOption) (*CheckModelWarmedResponse, error) {
	var out CheckModelWarmedResponse
	pattern := "/apis/v1/model-hub/warmedmodel/{workspaceName}/{modelName}/check"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationModelHubServiceCheckModelWarmed))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ModelHubServiceHTTPClientImpl) CreateBranch(ctx context.Context, in *CreateBranchRequest, opts ...http.CallOption) (*CreateBranchResponse, error) {
	var out CreateBranchResponse
	pattern := "/apis/v1/model-hub/create_branch"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationModelHubServiceCreateBranch))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ModelHubServiceHTTPClientImpl) CreateFork(ctx context.Context, in *CreateForkRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/apis/v1/model-hub/fork"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationModelHubServiceCreateFork))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ModelHubServiceHTTPClientImpl) CreateModel(ctx context.Context, in *CreateModelRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/apis/v1/model-hub/model"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationModelHubServiceCreateModel))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ModelHubServiceHTTPClientImpl) CreateModelHub(ctx context.Context, in *CreateModelHubRequest, opts ...http.CallOption) (*CreateModelHubResponse, error) {
	var out CreateModelHubResponse
	pattern := "/apis/v1/model-hub/createmodelhub"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationModelHubServiceCreateModelHub))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ModelHubServiceHTTPClientImpl) CreateUser(ctx context.Context, in *CreateUserRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/apis/v1/model-hub/user"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationModelHubServiceCreateUser))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ModelHubServiceHTTPClientImpl) DeleteCollaborator(ctx context.Context, in *DeleteCollaboratorRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/apis/v1/model-hub/collaborator"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationModelHubServiceDeleteCollaborator))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ModelHubServiceHTTPClientImpl) DeleteModel(ctx context.Context, in *DeleteModelRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/apis/v1/model-hub/model"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationModelHubServiceDeleteModel))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ModelHubServiceHTTPClientImpl) GetAllFiles(ctx context.Context, in *GetAllFilesRequest, opts ...http.CallOption) (*GetAllFilesResponse, error) {
	var out GetAllFilesResponse
	pattern := "/apis/v1/model-hub/all-files"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationModelHubServiceGetAllFiles))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ModelHubServiceHTTPClientImpl) GetBranches(ctx context.Context, in *GetBranchesRequest, opts ...http.CallOption) (*GetBranchesResponse, error) {
	var out GetBranchesResponse
	pattern := "/apis/v1/model-hub/get_branches"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationModelHubServiceGetBranches))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ModelHubServiceHTTPClientImpl) GetGitToken(ctx context.Context, in *GetGitTokenRequest, opts ...http.CallOption) (*GetGitTokenResponse, error) {
	var out GetGitTokenResponse
	pattern := "/apis/v1/model-hub/token"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationModelHubServiceGetGitToken))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ModelHubServiceHTTPClientImpl) GetLastCommit(ctx context.Context, in *GetLastCommitRequest, opts ...http.CallOption) (*GetLastCommitResponse, error) {
	var out GetLastCommitResponse
	pattern := "/apis/v1/model-hub/lastcommit"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationModelHubServiceGetLastCommit))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ModelHubServiceHTTPClientImpl) GetModelHub(ctx context.Context, in *GetModelHubRequest, opts ...http.CallOption) (*GetModelHubResponse, error) {
	var out GetModelHubResponse
	pattern := "/apis/v1/model-hub/getmodelhub"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationModelHubServiceGetModelHub))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ModelHubServiceHTTPClientImpl) GetModelInfo(ctx context.Context, in *GetModelInfoRequest, opts ...http.CallOption) (*GetModelInfoResponse, error) {
	var out GetModelInfoResponse
	pattern := "/apis/v1/model-hub/get_model_info"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationModelHubServiceGetModelInfo))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ModelHubServiceHTTPClientImpl) GetModelWarmupRatio(ctx context.Context, in *GetModelWarmupRatioRequest, opts ...http.CallOption) (*GetModelWarmupRatioResponse, error) {
	var out GetModelWarmupRatioResponse
	pattern := "/apis/v1/model-hub/warmupratio"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationModelHubServiceGetModelWarmupRatio))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ModelHubServiceHTTPClientImpl) GetRepoFileContent(ctx context.Context, in *GetRepoFileContentRequest, opts ...http.CallOption) (*GetRepoFileContentResponse, error) {
	var out GetRepoFileContentResponse
	pattern := "/apis/v1/model-hub/getrepofilecontent"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationModelHubServiceGetRepoFileContent))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ModelHubServiceHTTPClientImpl) GetTags(ctx context.Context, in *GetTagsRequest, opts ...http.CallOption) (*GetTagsResponse, error) {
	var out GetTagsResponse
	pattern := "/apis/v1/model-hub/get_tags"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationModelHubServiceGetTags))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ModelHubServiceHTTPClientImpl) GetTree(ctx context.Context, in *GetTreeRequest, opts ...http.CallOption) (*GetTreeResponse, error) {
	var out GetTreeResponse
	pattern := "/apis/v1/model-hub/tree"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationModelHubServiceGetTree))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ModelHubServiceHTTPClientImpl) GitCallback(ctx context.Context, in *GitCallbackRequest, opts ...http.CallOption) (*GitCallbackResponse, error) {
	var out GitCallbackResponse
	pattern := "/apis/v1/model-hub/git/callback"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationModelHubServiceGitCallback))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ModelHubServiceHTTPClientImpl) InitModelHubOrg(ctx context.Context, in *InitModelHubOrgRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/apis/v1/model-hub/init_model_hub_org"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationModelHubServiceInitModelHubOrg))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ModelHubServiceHTTPClientImpl) ListCollaborator(ctx context.Context, in *ListCollaboratorRequest, opts ...http.CallOption) (*ListCollaboratorResponse, error) {
	var out ListCollaboratorResponse
	pattern := "/apis/v1/model-hub/collaborator"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationModelHubServiceListCollaborator))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ModelHubServiceHTTPClientImpl) ListModels(ctx context.Context, in *ListModelsOptions, opts ...http.CallOption) (*ListModelResponse, error) {
	var out ListModelResponse
	pattern := "/apis/v1/model-hub/models"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationModelHubServiceListModels))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ModelHubServiceHTTPClientImpl) ListSyncModelRecord(ctx context.Context, in *ListSyncModelRecordRequest, opts ...http.CallOption) (*ListSyncModelRecordResponse, error) {
	var out ListSyncModelRecordResponse
	pattern := "/apis/v1/model-hub/list_sync_model_record"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationModelHubServiceListSyncModelRecord))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ModelHubServiceHTTPClientImpl) ListWarmedModels(ctx context.Context, in *ListWarmedModelsRequest, opts ...http.CallOption) (*ListWarmedModelsResponse, error) {
	var out ListWarmedModelsResponse
	pattern := "/apis/v1/model-hub/{workspaceName}/warmedmodels"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationModelHubServiceListWarmedModels))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ModelHubServiceHTTPClientImpl) ListWarmupRecords(ctx context.Context, in *ListWarmupRecordsRequest, opts ...http.CallOption) (*ListWarmupRecordsResponse, error) {
	var out ListWarmupRecordsResponse
	pattern := "/apis/v1/model-hub/{workspaceName}/warmuprecords"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationModelHubServiceListWarmupRecords))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ModelHubServiceHTTPClientImpl) ShowModel(ctx context.Context, in *ShowModelRequest, opts ...http.CallOption) (*ShowModelResponse, error) {
	var out ShowModelResponse
	pattern := "/apis/v1/model-hub/model"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationModelHubServiceShowModel))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ModelHubServiceHTTPClientImpl) StartWarmupJob(ctx context.Context, in *StartWarmupJobRequest, opts ...http.CallOption) (*StartWarmupJobResponse, error) {
	var out StartWarmupJobResponse
	pattern := "/apis/v1/model-hub/{workspaceName}/{modelName}/warmupjob/start"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationModelHubServiceStartWarmupJob))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ModelHubServiceHTTPClientImpl) StopSyncModel(ctx context.Context, in *StopSyncModelRequest, opts ...http.CallOption) (*StopSyncModelResponse, error) {
	var out StopSyncModelResponse
	pattern := "/apis/v1/model-hub/stopsyncmodel"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationModelHubServiceStopSyncModel))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ModelHubServiceHTTPClientImpl) StopWarmupJob(ctx context.Context, in *StopWarmupJobRequest, opts ...http.CallOption) (*StopWarmupJobResponse, error) {
	var out StopWarmupJobResponse
	pattern := "/apis/v1/model-hub/{workspaceName}/{modelName}/warmupjob/stop"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationModelHubServiceStopWarmupJob))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ModelHubServiceHTTPClientImpl) SyncModel(ctx context.Context, in *SyncModelRequest, opts ...http.CallOption) (*SyncModelResponse, error) {
	var out SyncModelResponse
	pattern := "/apis/v1/model-hub/sync_model"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationModelHubServiceSyncModel))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ModelHubServiceHTTPClientImpl) UpdateModel(ctx context.Context, in *UpdateModelRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/apis/v1/model-hub/model"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationModelHubServiceUpdateModel))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ModelHubServiceHTTPClientImpl) UpdateWarmupConfig(ctx context.Context, in *UpdateWarmupConfigRequest, opts ...http.CallOption) (*UpdateWarmupConfigResponse, error) {
	var out UpdateWarmupConfigResponse
	pattern := "/apis/v1/model-hub/{workspaceName}/{modelName}/warmupconfig"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationModelHubServiceUpdateWarmupConfig))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
