// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.3
// - protoc             v5.29.3
// source: aistudio/rbac/v1/rbac.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationRbacServiceChangeUserPlatformRole = "/apis.aistudio.rbac.v1.RbacService/ChangeUserPlatformRole"
const OperationRbacServiceCheckPermission = "/apis.aistudio.rbac.v1.RbacService/CheckPermission"
const OperationRbacServiceCreateOrUpdateUserBinding = "/apis.aistudio.rbac.v1.RbacService/CreateOrUpdateUserBinding"
const OperationRbacServiceCreateOrUpdateWorkspacePermission = "/apis.aistudio.rbac.v1.RbacService/CreateOrUpdateWorkspacePermission"
const OperationRbacServiceCreateOrUpdateWorkspaceRole = "/apis.aistudio.rbac.v1.RbacService/CreateOrUpdateWorkspaceRole"
const OperationRbacServiceCreateOrUpdateWorkspaceRoleBinding = "/apis.aistudio.rbac.v1.RbacService/CreateOrUpdateWorkspaceRoleBinding"
const OperationRbacServiceDeleteUserBinding = "/apis.aistudio.rbac.v1.RbacService/DeleteUserBinding"
const OperationRbacServiceDeleteWorkspacePermission = "/apis.aistudio.rbac.v1.RbacService/DeleteWorkspacePermission"
const OperationRbacServiceDeleteWorkspaceRole = "/apis.aistudio.rbac.v1.RbacService/DeleteWorkspaceRole"
const OperationRbacServiceDeleteWorkspaceRoleBinding = "/apis.aistudio.rbac.v1.RbacService/DeleteWorkspaceRoleBinding"
const OperationRbacServiceListUserBindings = "/apis.aistudio.rbac.v1.RbacService/ListUserBindings"
const OperationRbacServiceListWorkspacePermissions = "/apis.aistudio.rbac.v1.RbacService/ListWorkspacePermissions"
const OperationRbacServiceListWorkspaceRoleBindings = "/apis.aistudio.rbac.v1.RbacService/ListWorkspaceRoleBindings"
const OperationRbacServiceListWorkspaceRoles = "/apis.aistudio.rbac.v1.RbacService/ListWorkspaceRoles"

type RbacServiceHTTPServer interface {
	ChangeUserPlatformRole(context.Context, *ChangeUserPlatformRoleRequest) (*emptypb.Empty, error)
	CheckPermission(context.Context, *CheckPermissionRequest) (*CheckPermissionResponse, error)
	CreateOrUpdateUserBinding(context.Context, *CreateOrUpdateUserBindingRequest) (*UserBinding, error)
	CreateOrUpdateWorkspacePermission(context.Context, *CreateOrUpdateWorkspacePermissionRequest) (*WorkspacePermission, error)
	CreateOrUpdateWorkspaceRole(context.Context, *CreateOrUpdateWorkspaceRoleRequest) (*WorkspaceRole, error)
	CreateOrUpdateWorkspaceRoleBinding(context.Context, *CreateOrUpdateWorkspaceRoleBindingRequest) (*WorkspaceRoleBinding, error)
	DeleteUserBinding(context.Context, *DeleteUserBindingRequest) (*emptypb.Empty, error)
	DeleteWorkspacePermission(context.Context, *DeleteWorkspacePermissionRequest) (*emptypb.Empty, error)
	DeleteWorkspaceRole(context.Context, *DeleteWorkspaceRoleRequest) (*emptypb.Empty, error)
	DeleteWorkspaceRoleBinding(context.Context, *DeleteWorkspaceRoleBindingRequest) (*emptypb.Empty, error)
	ListUserBindings(context.Context, *ListUserBindingsOptions) (*UserBindings, error)
	ListWorkspacePermissions(context.Context, *ListWorkspacePermissionsOptions) (*WorkspacePermissions, error)
	ListWorkspaceRoleBindings(context.Context, *ListWorkspaceRoleBindingsOptions) (*WorkspaceRoleBindings, error)
	ListWorkspaceRoles(context.Context, *ListWorkspaceRolesOptions) (*WorkspaceRoles, error)
}

func RegisterRbacServiceHTTPServer(s *http.Server, srv RbacServiceHTTPServer) {
	r := s.Route("/")
	r.PUT("/apis/v1/workspace_role/{name}", _RbacService_CreateOrUpdateWorkspaceRole0_HTTP_Handler(srv))
	r.POST("/apis/v1/workspace_role", _RbacService_CreateOrUpdateWorkspaceRole1_HTTP_Handler(srv))
	r.DELETE("/apis/v1/workspace_role/{name}", _RbacService_DeleteWorkspaceRole0_HTTP_Handler(srv))
	r.GET("/apis/v1/workspace_roles", _RbacService_ListWorkspaceRoles0_HTTP_Handler(srv))
	r.PUT("/apis/v1/workspace_role_binding/{account}", _RbacService_CreateOrUpdateWorkspaceRoleBinding0_HTTP_Handler(srv))
	r.POST("/apis/v1/workspace_role_binding", _RbacService_CreateOrUpdateWorkspaceRoleBinding1_HTTP_Handler(srv))
	r.DELETE("/apis/v1/workspace_role_binding/{account}", _RbacService_DeleteWorkspaceRoleBinding0_HTTP_Handler(srv))
	r.GET("/apis/v1/workspace_role_bindings", _RbacService_ListWorkspaceRoleBindings0_HTTP_Handler(srv))
	r.PUT("/apis/v1/workspace_permission/{name}", _RbacService_CreateOrUpdateWorkspacePermission0_HTTP_Handler(srv))
	r.POST("/apis/v1/workspace_permission", _RbacService_CreateOrUpdateWorkspacePermission1_HTTP_Handler(srv))
	r.DELETE("/apis/v1/workspace_permission/{name}", _RbacService_DeleteWorkspacePermission0_HTTP_Handler(srv))
	r.GET("/apis/v1/workspace_permissions", _RbacService_ListWorkspacePermissions0_HTTP_Handler(srv))
	r.POST("/apis/v1/check_permission", _RbacService_CheckPermission0_HTTP_Handler(srv))
	r.PUT("/apis/v1/user_binding/{account}", _RbacService_CreateOrUpdateUserBinding0_HTTP_Handler(srv))
	r.POST("/apis/v1/user_binding", _RbacService_CreateOrUpdateUserBinding1_HTTP_Handler(srv))
	r.DELETE("/apis/v1/user_binding/{account}", _RbacService_DeleteUserBinding0_HTTP_Handler(srv))
	r.GET("/apis/v1/user_bindings", _RbacService_ListUserBindings0_HTTP_Handler(srv))
	r.POST("/apis/v1/change_platform_role", _RbacService_ChangeUserPlatformRole0_HTTP_Handler(srv))
}

func _RbacService_CreateOrUpdateWorkspaceRole0_HTTP_Handler(srv RbacServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateOrUpdateWorkspaceRoleRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationRbacServiceCreateOrUpdateWorkspaceRole)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateOrUpdateWorkspaceRole(ctx, req.(*CreateOrUpdateWorkspaceRoleRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*WorkspaceRole)
		return ctx.Result(200, reply)
	}
}

func _RbacService_CreateOrUpdateWorkspaceRole1_HTTP_Handler(srv RbacServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateOrUpdateWorkspaceRoleRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationRbacServiceCreateOrUpdateWorkspaceRole)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateOrUpdateWorkspaceRole(ctx, req.(*CreateOrUpdateWorkspaceRoleRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*WorkspaceRole)
		return ctx.Result(200, reply)
	}
}

func _RbacService_DeleteWorkspaceRole0_HTTP_Handler(srv RbacServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteWorkspaceRoleRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationRbacServiceDeleteWorkspaceRole)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteWorkspaceRole(ctx, req.(*DeleteWorkspaceRoleRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _RbacService_ListWorkspaceRoles0_HTTP_Handler(srv RbacServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListWorkspaceRolesOptions
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationRbacServiceListWorkspaceRoles)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListWorkspaceRoles(ctx, req.(*ListWorkspaceRolesOptions))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*WorkspaceRoles)
		return ctx.Result(200, reply)
	}
}

func _RbacService_CreateOrUpdateWorkspaceRoleBinding0_HTTP_Handler(srv RbacServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateOrUpdateWorkspaceRoleBindingRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationRbacServiceCreateOrUpdateWorkspaceRoleBinding)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateOrUpdateWorkspaceRoleBinding(ctx, req.(*CreateOrUpdateWorkspaceRoleBindingRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*WorkspaceRoleBinding)
		return ctx.Result(200, reply)
	}
}

func _RbacService_CreateOrUpdateWorkspaceRoleBinding1_HTTP_Handler(srv RbacServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateOrUpdateWorkspaceRoleBindingRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationRbacServiceCreateOrUpdateWorkspaceRoleBinding)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateOrUpdateWorkspaceRoleBinding(ctx, req.(*CreateOrUpdateWorkspaceRoleBindingRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*WorkspaceRoleBinding)
		return ctx.Result(200, reply)
	}
}

func _RbacService_DeleteWorkspaceRoleBinding0_HTTP_Handler(srv RbacServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteWorkspaceRoleBindingRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationRbacServiceDeleteWorkspaceRoleBinding)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteWorkspaceRoleBinding(ctx, req.(*DeleteWorkspaceRoleBindingRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _RbacService_ListWorkspaceRoleBindings0_HTTP_Handler(srv RbacServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListWorkspaceRoleBindingsOptions
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationRbacServiceListWorkspaceRoleBindings)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListWorkspaceRoleBindings(ctx, req.(*ListWorkspaceRoleBindingsOptions))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*WorkspaceRoleBindings)
		return ctx.Result(200, reply)
	}
}

func _RbacService_CreateOrUpdateWorkspacePermission0_HTTP_Handler(srv RbacServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateOrUpdateWorkspacePermissionRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationRbacServiceCreateOrUpdateWorkspacePermission)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateOrUpdateWorkspacePermission(ctx, req.(*CreateOrUpdateWorkspacePermissionRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*WorkspacePermission)
		return ctx.Result(200, reply)
	}
}

func _RbacService_CreateOrUpdateWorkspacePermission1_HTTP_Handler(srv RbacServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateOrUpdateWorkspacePermissionRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationRbacServiceCreateOrUpdateWorkspacePermission)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateOrUpdateWorkspacePermission(ctx, req.(*CreateOrUpdateWorkspacePermissionRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*WorkspacePermission)
		return ctx.Result(200, reply)
	}
}

func _RbacService_DeleteWorkspacePermission0_HTTP_Handler(srv RbacServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteWorkspacePermissionRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationRbacServiceDeleteWorkspacePermission)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteWorkspacePermission(ctx, req.(*DeleteWorkspacePermissionRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _RbacService_ListWorkspacePermissions0_HTTP_Handler(srv RbacServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListWorkspacePermissionsOptions
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationRbacServiceListWorkspacePermissions)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListWorkspacePermissions(ctx, req.(*ListWorkspacePermissionsOptions))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*WorkspacePermissions)
		return ctx.Result(200, reply)
	}
}

func _RbacService_CheckPermission0_HTTP_Handler(srv RbacServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CheckPermissionRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationRbacServiceCheckPermission)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CheckPermission(ctx, req.(*CheckPermissionRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CheckPermissionResponse)
		return ctx.Result(200, reply)
	}
}

func _RbacService_CreateOrUpdateUserBinding0_HTTP_Handler(srv RbacServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateOrUpdateUserBindingRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationRbacServiceCreateOrUpdateUserBinding)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateOrUpdateUserBinding(ctx, req.(*CreateOrUpdateUserBindingRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*UserBinding)
		return ctx.Result(200, reply)
	}
}

func _RbacService_CreateOrUpdateUserBinding1_HTTP_Handler(srv RbacServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateOrUpdateUserBindingRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationRbacServiceCreateOrUpdateUserBinding)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateOrUpdateUserBinding(ctx, req.(*CreateOrUpdateUserBindingRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*UserBinding)
		return ctx.Result(200, reply)
	}
}

func _RbacService_DeleteUserBinding0_HTTP_Handler(srv RbacServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteUserBindingRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationRbacServiceDeleteUserBinding)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteUserBinding(ctx, req.(*DeleteUserBindingRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _RbacService_ListUserBindings0_HTTP_Handler(srv RbacServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListUserBindingsOptions
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationRbacServiceListUserBindings)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListUserBindings(ctx, req.(*ListUserBindingsOptions))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*UserBindings)
		return ctx.Result(200, reply)
	}
}

func _RbacService_ChangeUserPlatformRole0_HTTP_Handler(srv RbacServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ChangeUserPlatformRoleRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationRbacServiceChangeUserPlatformRole)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ChangeUserPlatformRole(ctx, req.(*ChangeUserPlatformRoleRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

type RbacServiceHTTPClient interface {
	ChangeUserPlatformRole(ctx context.Context, req *ChangeUserPlatformRoleRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	CheckPermission(ctx context.Context, req *CheckPermissionRequest, opts ...http.CallOption) (rsp *CheckPermissionResponse, err error)
	CreateOrUpdateUserBinding(ctx context.Context, req *CreateOrUpdateUserBindingRequest, opts ...http.CallOption) (rsp *UserBinding, err error)
	CreateOrUpdateWorkspacePermission(ctx context.Context, req *CreateOrUpdateWorkspacePermissionRequest, opts ...http.CallOption) (rsp *WorkspacePermission, err error)
	CreateOrUpdateWorkspaceRole(ctx context.Context, req *CreateOrUpdateWorkspaceRoleRequest, opts ...http.CallOption) (rsp *WorkspaceRole, err error)
	CreateOrUpdateWorkspaceRoleBinding(ctx context.Context, req *CreateOrUpdateWorkspaceRoleBindingRequest, opts ...http.CallOption) (rsp *WorkspaceRoleBinding, err error)
	DeleteUserBinding(ctx context.Context, req *DeleteUserBindingRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	DeleteWorkspacePermission(ctx context.Context, req *DeleteWorkspacePermissionRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	DeleteWorkspaceRole(ctx context.Context, req *DeleteWorkspaceRoleRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	DeleteWorkspaceRoleBinding(ctx context.Context, req *DeleteWorkspaceRoleBindingRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	ListUserBindings(ctx context.Context, req *ListUserBindingsOptions, opts ...http.CallOption) (rsp *UserBindings, err error)
	ListWorkspacePermissions(ctx context.Context, req *ListWorkspacePermissionsOptions, opts ...http.CallOption) (rsp *WorkspacePermissions, err error)
	ListWorkspaceRoleBindings(ctx context.Context, req *ListWorkspaceRoleBindingsOptions, opts ...http.CallOption) (rsp *WorkspaceRoleBindings, err error)
	ListWorkspaceRoles(ctx context.Context, req *ListWorkspaceRolesOptions, opts ...http.CallOption) (rsp *WorkspaceRoles, err error)
}

type RbacServiceHTTPClientImpl struct {
	cc *http.Client
}

func NewRbacServiceHTTPClient(client *http.Client) RbacServiceHTTPClient {
	return &RbacServiceHTTPClientImpl{client}
}

func (c *RbacServiceHTTPClientImpl) ChangeUserPlatformRole(ctx context.Context, in *ChangeUserPlatformRoleRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/apis/v1/change_platform_role"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationRbacServiceChangeUserPlatformRole))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *RbacServiceHTTPClientImpl) CheckPermission(ctx context.Context, in *CheckPermissionRequest, opts ...http.CallOption) (*CheckPermissionResponse, error) {
	var out CheckPermissionResponse
	pattern := "/apis/v1/check_permission"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationRbacServiceCheckPermission))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *RbacServiceHTTPClientImpl) CreateOrUpdateUserBinding(ctx context.Context, in *CreateOrUpdateUserBindingRequest, opts ...http.CallOption) (*UserBinding, error) {
	var out UserBinding
	pattern := "/apis/v1/user_binding"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationRbacServiceCreateOrUpdateUserBinding))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *RbacServiceHTTPClientImpl) CreateOrUpdateWorkspacePermission(ctx context.Context, in *CreateOrUpdateWorkspacePermissionRequest, opts ...http.CallOption) (*WorkspacePermission, error) {
	var out WorkspacePermission
	pattern := "/apis/v1/workspace_permission"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationRbacServiceCreateOrUpdateWorkspacePermission))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *RbacServiceHTTPClientImpl) CreateOrUpdateWorkspaceRole(ctx context.Context, in *CreateOrUpdateWorkspaceRoleRequest, opts ...http.CallOption) (*WorkspaceRole, error) {
	var out WorkspaceRole
	pattern := "/apis/v1/workspace_role"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationRbacServiceCreateOrUpdateWorkspaceRole))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *RbacServiceHTTPClientImpl) CreateOrUpdateWorkspaceRoleBinding(ctx context.Context, in *CreateOrUpdateWorkspaceRoleBindingRequest, opts ...http.CallOption) (*WorkspaceRoleBinding, error) {
	var out WorkspaceRoleBinding
	pattern := "/apis/v1/workspace_role_binding"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationRbacServiceCreateOrUpdateWorkspaceRoleBinding))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *RbacServiceHTTPClientImpl) DeleteUserBinding(ctx context.Context, in *DeleteUserBindingRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/apis/v1/user_binding/{account}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationRbacServiceDeleteUserBinding))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *RbacServiceHTTPClientImpl) DeleteWorkspacePermission(ctx context.Context, in *DeleteWorkspacePermissionRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/apis/v1/workspace_permission/{name}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationRbacServiceDeleteWorkspacePermission))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *RbacServiceHTTPClientImpl) DeleteWorkspaceRole(ctx context.Context, in *DeleteWorkspaceRoleRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/apis/v1/workspace_role/{name}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationRbacServiceDeleteWorkspaceRole))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *RbacServiceHTTPClientImpl) DeleteWorkspaceRoleBinding(ctx context.Context, in *DeleteWorkspaceRoleBindingRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/apis/v1/workspace_role_binding/{account}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationRbacServiceDeleteWorkspaceRoleBinding))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *RbacServiceHTTPClientImpl) ListUserBindings(ctx context.Context, in *ListUserBindingsOptions, opts ...http.CallOption) (*UserBindings, error) {
	var out UserBindings
	pattern := "/apis/v1/user_bindings"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationRbacServiceListUserBindings))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *RbacServiceHTTPClientImpl) ListWorkspacePermissions(ctx context.Context, in *ListWorkspacePermissionsOptions, opts ...http.CallOption) (*WorkspacePermissions, error) {
	var out WorkspacePermissions
	pattern := "/apis/v1/workspace_permissions"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationRbacServiceListWorkspacePermissions))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *RbacServiceHTTPClientImpl) ListWorkspaceRoleBindings(ctx context.Context, in *ListWorkspaceRoleBindingsOptions, opts ...http.CallOption) (*WorkspaceRoleBindings, error) {
	var out WorkspaceRoleBindings
	pattern := "/apis/v1/workspace_role_bindings"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationRbacServiceListWorkspaceRoleBindings))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *RbacServiceHTTPClientImpl) ListWorkspaceRoles(ctx context.Context, in *ListWorkspaceRolesOptions, opts ...http.CallOption) (*WorkspaceRoles, error) {
	var out WorkspaceRoles
	pattern := "/apis/v1/workspace_roles"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationRbacServiceListWorkspaceRoles))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
