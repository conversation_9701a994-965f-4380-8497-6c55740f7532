// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v5.29.3
// source: aistudio/rbac/v1/rbac.proto

package v1

import (
	common "git.lianjia.com/cloudnative/kic/kic-platform/apis/common"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Effect int32

const (
	Effect_ALLOW Effect = 0 // 允许
	Effect_DENY  Effect = 1 // 拒绝
)

// Enum value maps for Effect.
var (
	Effect_name = map[int32]string{
		0: "ALLOW",
		1: "DENY",
	}
	Effect_value = map[string]int32{
		"ALLOW": 0,
		"DENY":  1,
	}
)

func (x Effect) Enum() *Effect {
	p := new(Effect)
	*p = x
	return p
}

func (x Effect) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Effect) Descriptor() protoreflect.EnumDescriptor {
	return file_aistudio_rbac_v1_rbac_proto_enumTypes[0].Descriptor()
}

func (Effect) Type() protoreflect.EnumType {
	return &file_aistudio_rbac_v1_rbac_proto_enumTypes[0]
}

func (x Effect) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Effect.Descriptor instead.
func (Effect) EnumDescriptor() ([]byte, []int) {
	return file_aistudio_rbac_v1_rbac_proto_rawDescGZIP(), []int{0}
}

type ChangeUserPlatformRoleRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Account string `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`  // 账户
	IsAdmin bool   `protobuf:"varint,2,opt,name=isAdmin,proto3" json:"isAdmin,omitempty"` // true 赋予用户管理员 false 取消用户管理员
}

func (x *ChangeUserPlatformRoleRequest) Reset() {
	*x = ChangeUserPlatformRoleRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_rbac_v1_rbac_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChangeUserPlatformRoleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChangeUserPlatformRoleRequest) ProtoMessage() {}

func (x *ChangeUserPlatformRoleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_rbac_v1_rbac_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChangeUserPlatformRoleRequest.ProtoReflect.Descriptor instead.
func (*ChangeUserPlatformRoleRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_rbac_v1_rbac_proto_rawDescGZIP(), []int{0}
}

func (x *ChangeUserPlatformRoleRequest) GetAccount() string {
	if x != nil {
		return x.Account
	}
	return ""
}

func (x *ChangeUserPlatformRoleRequest) GetIsAdmin() bool {
	if x != nil {
		return x.IsAdmin
	}
	return false
}

type WorkspaceRole struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Timestamp   *common.TimestampModel `protobuf:"bytes,1,opt,name=timestamp,proto3" json:"timestamp,omitempty"`     // 时间戳
	Id          string                 `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`                   // 角色ID
	Name        string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`               // 角色名称
	Alias       string                 `protobuf:"bytes,4,opt,name=alias,proto3" json:"alias,omitempty"`             // 角色别名
	Description string                 `protobuf:"bytes,5,opt,name=description,proto3" json:"description,omitempty"` // 角色描述
	Scope       string                 `protobuf:"bytes,6,opt,name=Scope,proto3" json:"Scope,omitempty"`             // 角色范围
	Permissions []string               `protobuf:"bytes,7,rep,name=permissions,proto3" json:"permissions,omitempty"` // 角色权限
}

func (x *WorkspaceRole) Reset() {
	*x = WorkspaceRole{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_rbac_v1_rbac_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WorkspaceRole) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WorkspaceRole) ProtoMessage() {}

func (x *WorkspaceRole) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_rbac_v1_rbac_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WorkspaceRole.ProtoReflect.Descriptor instead.
func (*WorkspaceRole) Descriptor() ([]byte, []int) {
	return file_aistudio_rbac_v1_rbac_proto_rawDescGZIP(), []int{1}
}

func (x *WorkspaceRole) GetTimestamp() *common.TimestampModel {
	if x != nil {
		return x.Timestamp
	}
	return nil
}

func (x *WorkspaceRole) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *WorkspaceRole) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *WorkspaceRole) GetAlias() string {
	if x != nil {
		return x.Alias
	}
	return ""
}

func (x *WorkspaceRole) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *WorkspaceRole) GetScope() string {
	if x != nil {
		return x.Scope
	}
	return ""
}

func (x *WorkspaceRole) GetPermissions() []string {
	if x != nil {
		return x.Permissions
	}
	return nil
}

type CreateOrUpdateWorkspaceRoleRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name        string   `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`               // 角色名称
	Alias       string   `protobuf:"bytes,2,opt,name=alias,proto3" json:"alias,omitempty"`             // 角色别名
	Description string   `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"` // 角色描述
	Scope       string   `protobuf:"bytes,4,opt,name=scope,proto3" json:"scope,omitempty"`             // 角色范围
	Permissions []string `protobuf:"bytes,5,rep,name=permissions,proto3" json:"permissions,omitempty"` // 角色权限
}

func (x *CreateOrUpdateWorkspaceRoleRequest) Reset() {
	*x = CreateOrUpdateWorkspaceRoleRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_rbac_v1_rbac_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateOrUpdateWorkspaceRoleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateOrUpdateWorkspaceRoleRequest) ProtoMessage() {}

func (x *CreateOrUpdateWorkspaceRoleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_rbac_v1_rbac_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateOrUpdateWorkspaceRoleRequest.ProtoReflect.Descriptor instead.
func (*CreateOrUpdateWorkspaceRoleRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_rbac_v1_rbac_proto_rawDescGZIP(), []int{2}
}

func (x *CreateOrUpdateWorkspaceRoleRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateOrUpdateWorkspaceRoleRequest) GetAlias() string {
	if x != nil {
		return x.Alias
	}
	return ""
}

func (x *CreateOrUpdateWorkspaceRoleRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CreateOrUpdateWorkspaceRoleRequest) GetScope() string {
	if x != nil {
		return x.Scope
	}
	return ""
}

func (x *CreateOrUpdateWorkspaceRoleRequest) GetPermissions() []string {
	if x != nil {
		return x.Permissions
	}
	return nil
}

type UpdateWorkspaceRoleRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name        string   `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`               // 角色名称
	Alias       string   `protobuf:"bytes,2,opt,name=alias,proto3" json:"alias,omitempty"`             // 角色别名
	Description string   `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"` // 角色描述
	Scope       string   `protobuf:"bytes,4,opt,name=scope,proto3" json:"scope,omitempty"`             // 角色范围
	Permissions []string `protobuf:"bytes,5,rep,name=permissions,proto3" json:"permissions,omitempty"` // 角色权限
}

func (x *UpdateWorkspaceRoleRequest) Reset() {
	*x = UpdateWorkspaceRoleRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_rbac_v1_rbac_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateWorkspaceRoleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateWorkspaceRoleRequest) ProtoMessage() {}

func (x *UpdateWorkspaceRoleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_rbac_v1_rbac_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateWorkspaceRoleRequest.ProtoReflect.Descriptor instead.
func (*UpdateWorkspaceRoleRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_rbac_v1_rbac_proto_rawDescGZIP(), []int{3}
}

func (x *UpdateWorkspaceRoleRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UpdateWorkspaceRoleRequest) GetAlias() string {
	if x != nil {
		return x.Alias
	}
	return ""
}

func (x *UpdateWorkspaceRoleRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *UpdateWorkspaceRoleRequest) GetScope() string {
	if x != nil {
		return x.Scope
	}
	return ""
}

func (x *UpdateWorkspaceRoleRequest) GetPermissions() []string {
	if x != nil {
		return x.Permissions
	}
	return nil
}

type DeleteWorkspaceRoleRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name  string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`   // 角色名称
	Scope string `protobuf:"bytes,2,opt,name=scope,proto3" json:"scope,omitempty"` // 角色范围
}

func (x *DeleteWorkspaceRoleRequest) Reset() {
	*x = DeleteWorkspaceRoleRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_rbac_v1_rbac_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteWorkspaceRoleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteWorkspaceRoleRequest) ProtoMessage() {}

func (x *DeleteWorkspaceRoleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_rbac_v1_rbac_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteWorkspaceRoleRequest.ProtoReflect.Descriptor instead.
func (*DeleteWorkspaceRoleRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_rbac_v1_rbac_proto_rawDescGZIP(), []int{4}
}

func (x *DeleteWorkspaceRoleRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DeleteWorkspaceRoleRequest) GetScope() string {
	if x != nil {
		return x.Scope
	}
	return ""
}

type WorkspaceRoles struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Roles []*WorkspaceRole `protobuf:"bytes,1,rep,name=roles,proto3" json:"roles,omitempty"` // 角色列表
}

func (x *WorkspaceRoles) Reset() {
	*x = WorkspaceRoles{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_rbac_v1_rbac_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WorkspaceRoles) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WorkspaceRoles) ProtoMessage() {}

func (x *WorkspaceRoles) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_rbac_v1_rbac_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WorkspaceRoles.ProtoReflect.Descriptor instead.
func (*WorkspaceRoles) Descriptor() ([]byte, []int) {
	return file_aistudio_rbac_v1_rbac_proto_rawDescGZIP(), []int{5}
}

func (x *WorkspaceRoles) GetRoles() []*WorkspaceRole {
	if x != nil {
		return x.Roles
	}
	return nil
}

type ListWorkspaceRolesOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Scope string `protobuf:"bytes,1,opt,name=scope,proto3" json:"scope,omitempty"` // 角色范围
}

func (x *ListWorkspaceRolesOptions) Reset() {
	*x = ListWorkspaceRolesOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_rbac_v1_rbac_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListWorkspaceRolesOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListWorkspaceRolesOptions) ProtoMessage() {}

func (x *ListWorkspaceRolesOptions) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_rbac_v1_rbac_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListWorkspaceRolesOptions.ProtoReflect.Descriptor instead.
func (*ListWorkspaceRolesOptions) Descriptor() ([]byte, []int) {
	return file_aistudio_rbac_v1_rbac_proto_rawDescGZIP(), []int{6}
}

func (x *ListWorkspaceRolesOptions) GetScope() string {
	if x != nil {
		return x.Scope
	}
	return ""
}

type WorkspaceRoleBinding struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Timestamp   *common.TimestampModel `protobuf:"bytes,1,opt,name=timestamp,proto3" json:"timestamp,omitempty"`     // 时间戳
	Id          string                 `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`                   // 角色绑定ID
	Account     string                 `protobuf:"bytes,3,opt,name=account,proto3" json:"account,omitempty"`         // 账户
	Scope       string                 `protobuf:"bytes,4,opt,name=scope,proto3" json:"scope,omitempty"`             // 角色范围
	Description string                 `protobuf:"bytes,5,opt,name=description,proto3" json:"description,omitempty"` // 角色绑定描述
	Roles       []string               `protobuf:"bytes,7,rep,name=roles,proto3" json:"roles,omitempty"`             // 绑定角色
}

func (x *WorkspaceRoleBinding) Reset() {
	*x = WorkspaceRoleBinding{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_rbac_v1_rbac_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WorkspaceRoleBinding) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WorkspaceRoleBinding) ProtoMessage() {}

func (x *WorkspaceRoleBinding) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_rbac_v1_rbac_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WorkspaceRoleBinding.ProtoReflect.Descriptor instead.
func (*WorkspaceRoleBinding) Descriptor() ([]byte, []int) {
	return file_aistudio_rbac_v1_rbac_proto_rawDescGZIP(), []int{7}
}

func (x *WorkspaceRoleBinding) GetTimestamp() *common.TimestampModel {
	if x != nil {
		return x.Timestamp
	}
	return nil
}

func (x *WorkspaceRoleBinding) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *WorkspaceRoleBinding) GetAccount() string {
	if x != nil {
		return x.Account
	}
	return ""
}

func (x *WorkspaceRoleBinding) GetScope() string {
	if x != nil {
		return x.Scope
	}
	return ""
}

func (x *WorkspaceRoleBinding) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *WorkspaceRoleBinding) GetRoles() []string {
	if x != nil {
		return x.Roles
	}
	return nil
}

type CreateOrUpdateWorkspaceRoleBindingRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Account     string   `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`         // 账户
	Scope       string   `protobuf:"bytes,2,opt,name=scope,proto3" json:"scope,omitempty"`             // 角色范围
	Description string   `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"` // 角色绑定描述
	Roles       []string `protobuf:"bytes,4,rep,name=roles,proto3" json:"roles,omitempty"`             // 绑定角色
}

func (x *CreateOrUpdateWorkspaceRoleBindingRequest) Reset() {
	*x = CreateOrUpdateWorkspaceRoleBindingRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_rbac_v1_rbac_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateOrUpdateWorkspaceRoleBindingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateOrUpdateWorkspaceRoleBindingRequest) ProtoMessage() {}

func (x *CreateOrUpdateWorkspaceRoleBindingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_rbac_v1_rbac_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateOrUpdateWorkspaceRoleBindingRequest.ProtoReflect.Descriptor instead.
func (*CreateOrUpdateWorkspaceRoleBindingRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_rbac_v1_rbac_proto_rawDescGZIP(), []int{8}
}

func (x *CreateOrUpdateWorkspaceRoleBindingRequest) GetAccount() string {
	if x != nil {
		return x.Account
	}
	return ""
}

func (x *CreateOrUpdateWorkspaceRoleBindingRequest) GetScope() string {
	if x != nil {
		return x.Scope
	}
	return ""
}

func (x *CreateOrUpdateWorkspaceRoleBindingRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CreateOrUpdateWorkspaceRoleBindingRequest) GetRoles() []string {
	if x != nil {
		return x.Roles
	}
	return nil
}

type DeleteWorkspaceRoleBindingRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Account string   `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"` // 账户
	Scope   string   `protobuf:"bytes,2,opt,name=scope,proto3" json:"scope,omitempty"`     // 角色范围
	Roles   []string `protobuf:"bytes,3,rep,name=roles,proto3" json:"roles,omitempty"`     // 绑定角色
}

func (x *DeleteWorkspaceRoleBindingRequest) Reset() {
	*x = DeleteWorkspaceRoleBindingRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_rbac_v1_rbac_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteWorkspaceRoleBindingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteWorkspaceRoleBindingRequest) ProtoMessage() {}

func (x *DeleteWorkspaceRoleBindingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_rbac_v1_rbac_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteWorkspaceRoleBindingRequest.ProtoReflect.Descriptor instead.
func (*DeleteWorkspaceRoleBindingRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_rbac_v1_rbac_proto_rawDescGZIP(), []int{9}
}

func (x *DeleteWorkspaceRoleBindingRequest) GetAccount() string {
	if x != nil {
		return x.Account
	}
	return ""
}

func (x *DeleteWorkspaceRoleBindingRequest) GetScope() string {
	if x != nil {
		return x.Scope
	}
	return ""
}

func (x *DeleteWorkspaceRoleBindingRequest) GetRoles() []string {
	if x != nil {
		return x.Roles
	}
	return nil
}

type WorkspaceRoleBindings struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RoleBindings []*WorkspaceRoleBinding `protobuf:"bytes,1,rep,name=role_bindings,json=roleBindings,proto3" json:"role_bindings,omitempty"` // 角色绑定列表
}

func (x *WorkspaceRoleBindings) Reset() {
	*x = WorkspaceRoleBindings{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_rbac_v1_rbac_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WorkspaceRoleBindings) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WorkspaceRoleBindings) ProtoMessage() {}

func (x *WorkspaceRoleBindings) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_rbac_v1_rbac_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WorkspaceRoleBindings.ProtoReflect.Descriptor instead.
func (*WorkspaceRoleBindings) Descriptor() ([]byte, []int) {
	return file_aistudio_rbac_v1_rbac_proto_rawDescGZIP(), []int{10}
}

func (x *WorkspaceRoleBindings) GetRoleBindings() []*WorkspaceRoleBinding {
	if x != nil {
		return x.RoleBindings
	}
	return nil
}

type ListWorkspaceRoleBindingsOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Account string `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"` // 账户
	Scope   string `protobuf:"bytes,2,opt,name=scope,proto3" json:"scope,omitempty"`     // 角色范围
}

func (x *ListWorkspaceRoleBindingsOptions) Reset() {
	*x = ListWorkspaceRoleBindingsOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_rbac_v1_rbac_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListWorkspaceRoleBindingsOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListWorkspaceRoleBindingsOptions) ProtoMessage() {}

func (x *ListWorkspaceRoleBindingsOptions) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_rbac_v1_rbac_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListWorkspaceRoleBindingsOptions.ProtoReflect.Descriptor instead.
func (*ListWorkspaceRoleBindingsOptions) Descriptor() ([]byte, []int) {
	return file_aistudio_rbac_v1_rbac_proto_rawDescGZIP(), []int{11}
}

func (x *ListWorkspaceRoleBindingsOptions) GetAccount() string {
	if x != nil {
		return x.Account
	}
	return ""
}

func (x *ListWorkspaceRoleBindingsOptions) GetScope() string {
	if x != nil {
		return x.Scope
	}
	return ""
}

type WorkspacePermission struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Timestamp   *common.TimestampModel `protobuf:"bytes,1,opt,name=timestamp,proto3" json:"timestamp,omitempty"`                              // 时间戳
	Id          string                 `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`                                            // 权限ID
	Name        string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`                                        // 权限名称
	Description string                 `protobuf:"bytes,5,opt,name=description,proto3" json:"description,omitempty"`                          // 权限描述
	Scope       string                 `protobuf:"bytes,6,opt,name=scope,proto3" json:"scope,omitempty"`                                      // 权限范围
	Resources   []string               `protobuf:"bytes,7,rep,name=resources,proto3" json:"resources,omitempty"`                              // 权限资源
	Actions     []string               `protobuf:"bytes,8,rep,name=actions,proto3" json:"actions,omitempty"`                                  // 权限操作
	Effect      Effect                 `protobuf:"varint,9,opt,name=effect,proto3,enum=apis.aistudio.rbac.v1.Effect" json:"effect,omitempty"` // 权限效果
}

func (x *WorkspacePermission) Reset() {
	*x = WorkspacePermission{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_rbac_v1_rbac_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WorkspacePermission) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WorkspacePermission) ProtoMessage() {}

func (x *WorkspacePermission) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_rbac_v1_rbac_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WorkspacePermission.ProtoReflect.Descriptor instead.
func (*WorkspacePermission) Descriptor() ([]byte, []int) {
	return file_aistudio_rbac_v1_rbac_proto_rawDescGZIP(), []int{12}
}

func (x *WorkspacePermission) GetTimestamp() *common.TimestampModel {
	if x != nil {
		return x.Timestamp
	}
	return nil
}

func (x *WorkspacePermission) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *WorkspacePermission) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *WorkspacePermission) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *WorkspacePermission) GetScope() string {
	if x != nil {
		return x.Scope
	}
	return ""
}

func (x *WorkspacePermission) GetResources() []string {
	if x != nil {
		return x.Resources
	}
	return nil
}

func (x *WorkspacePermission) GetActions() []string {
	if x != nil {
		return x.Actions
	}
	return nil
}

func (x *WorkspacePermission) GetEffect() Effect {
	if x != nil {
		return x.Effect
	}
	return Effect_ALLOW
}

type CreateOrUpdateWorkspacePermissionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name        string   `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`                                        // 权限名称
	Description string   `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`                          // 权限描述
	Scope       string   `protobuf:"bytes,3,opt,name=scope,proto3" json:"scope,omitempty"`                                      // 权限范围
	Resources   []string `protobuf:"bytes,4,rep,name=resources,proto3" json:"resources,omitempty"`                              // 权限资源
	Actions     []string `protobuf:"bytes,5,rep,name=actions,proto3" json:"actions,omitempty"`                                  // 权限操作
	Effect      Effect   `protobuf:"varint,6,opt,name=effect,proto3,enum=apis.aistudio.rbac.v1.Effect" json:"effect,omitempty"` // 权限效果
}

func (x *CreateOrUpdateWorkspacePermissionRequest) Reset() {
	*x = CreateOrUpdateWorkspacePermissionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_rbac_v1_rbac_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateOrUpdateWorkspacePermissionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateOrUpdateWorkspacePermissionRequest) ProtoMessage() {}

func (x *CreateOrUpdateWorkspacePermissionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_rbac_v1_rbac_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateOrUpdateWorkspacePermissionRequest.ProtoReflect.Descriptor instead.
func (*CreateOrUpdateWorkspacePermissionRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_rbac_v1_rbac_proto_rawDescGZIP(), []int{13}
}

func (x *CreateOrUpdateWorkspacePermissionRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateOrUpdateWorkspacePermissionRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CreateOrUpdateWorkspacePermissionRequest) GetScope() string {
	if x != nil {
		return x.Scope
	}
	return ""
}

func (x *CreateOrUpdateWorkspacePermissionRequest) GetResources() []string {
	if x != nil {
		return x.Resources
	}
	return nil
}

func (x *CreateOrUpdateWorkspacePermissionRequest) GetActions() []string {
	if x != nil {
		return x.Actions
	}
	return nil
}

func (x *CreateOrUpdateWorkspacePermissionRequest) GetEffect() Effect {
	if x != nil {
		return x.Effect
	}
	return Effect_ALLOW
}

type UpdateWorkspacePermissionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name        string   `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`                                        // 权限名称
	Description string   `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`                          // 权限描述
	Scope       string   `protobuf:"bytes,3,opt,name=scope,proto3" json:"scope,omitempty"`                                      // 权限范围
	Resources   []string `protobuf:"bytes,4,rep,name=resources,proto3" json:"resources,omitempty"`                              // 权限资源
	Actions     []string `protobuf:"bytes,5,rep,name=actions,proto3" json:"actions,omitempty"`                                  // 权限操作
	Effect      Effect   `protobuf:"varint,6,opt,name=effect,proto3,enum=apis.aistudio.rbac.v1.Effect" json:"effect,omitempty"` // 权限效果
}

func (x *UpdateWorkspacePermissionRequest) Reset() {
	*x = UpdateWorkspacePermissionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_rbac_v1_rbac_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateWorkspacePermissionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateWorkspacePermissionRequest) ProtoMessage() {}

func (x *UpdateWorkspacePermissionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_rbac_v1_rbac_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateWorkspacePermissionRequest.ProtoReflect.Descriptor instead.
func (*UpdateWorkspacePermissionRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_rbac_v1_rbac_proto_rawDescGZIP(), []int{14}
}

func (x *UpdateWorkspacePermissionRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UpdateWorkspacePermissionRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *UpdateWorkspacePermissionRequest) GetScope() string {
	if x != nil {
		return x.Scope
	}
	return ""
}

func (x *UpdateWorkspacePermissionRequest) GetResources() []string {
	if x != nil {
		return x.Resources
	}
	return nil
}

func (x *UpdateWorkspacePermissionRequest) GetActions() []string {
	if x != nil {
		return x.Actions
	}
	return nil
}

func (x *UpdateWorkspacePermissionRequest) GetEffect() Effect {
	if x != nil {
		return x.Effect
	}
	return Effect_ALLOW
}

type DeleteWorkspacePermissionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name  string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`   // 权限名称
	Scope string `protobuf:"bytes,2,opt,name=scope,proto3" json:"scope,omitempty"` // 权限范围
}

func (x *DeleteWorkspacePermissionRequest) Reset() {
	*x = DeleteWorkspacePermissionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_rbac_v1_rbac_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteWorkspacePermissionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteWorkspacePermissionRequest) ProtoMessage() {}

func (x *DeleteWorkspacePermissionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_rbac_v1_rbac_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteWorkspacePermissionRequest.ProtoReflect.Descriptor instead.
func (*DeleteWorkspacePermissionRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_rbac_v1_rbac_proto_rawDescGZIP(), []int{15}
}

func (x *DeleteWorkspacePermissionRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DeleteWorkspacePermissionRequest) GetScope() string {
	if x != nil {
		return x.Scope
	}
	return ""
}

type WorkspacePermissions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Permissions []*WorkspacePermission `protobuf:"bytes,1,rep,name=permissions,proto3" json:"permissions,omitempty"` // 权限列表
}

func (x *WorkspacePermissions) Reset() {
	*x = WorkspacePermissions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_rbac_v1_rbac_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WorkspacePermissions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WorkspacePermissions) ProtoMessage() {}

func (x *WorkspacePermissions) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_rbac_v1_rbac_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WorkspacePermissions.ProtoReflect.Descriptor instead.
func (*WorkspacePermissions) Descriptor() ([]byte, []int) {
	return file_aistudio_rbac_v1_rbac_proto_rawDescGZIP(), []int{16}
}

func (x *WorkspacePermissions) GetPermissions() []*WorkspacePermission {
	if x != nil {
		return x.Permissions
	}
	return nil
}

type ListWorkspacePermissionsOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Scope string `protobuf:"bytes,1,opt,name=scope,proto3" json:"scope,omitempty"` // 权限范围
}

func (x *ListWorkspacePermissionsOptions) Reset() {
	*x = ListWorkspacePermissionsOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_rbac_v1_rbac_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListWorkspacePermissionsOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListWorkspacePermissionsOptions) ProtoMessage() {}

func (x *ListWorkspacePermissionsOptions) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_rbac_v1_rbac_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListWorkspacePermissionsOptions.ProtoReflect.Descriptor instead.
func (*ListWorkspacePermissionsOptions) Descriptor() ([]byte, []int) {
	return file_aistudio_rbac_v1_rbac_proto_rawDescGZIP(), []int{17}
}

func (x *ListWorkspacePermissionsOptions) GetScope() string {
	if x != nil {
		return x.Scope
	}
	return ""
}

type CheckPermissionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Resource string   `protobuf:"bytes,1,opt,name=resource,proto3" json:"resource,omitempty"`
	Actions  []string `protobuf:"bytes,2,rep,name=actions,proto3" json:"actions,omitempty"`
	Scope    string   `protobuf:"bytes,3,opt,name=scope,proto3" json:"scope,omitempty"`
	Account  string   `protobuf:"bytes,4,opt,name=account,proto3" json:"account,omitempty"`
}

func (x *CheckPermissionRequest) Reset() {
	*x = CheckPermissionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_rbac_v1_rbac_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckPermissionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckPermissionRequest) ProtoMessage() {}

func (x *CheckPermissionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_rbac_v1_rbac_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckPermissionRequest.ProtoReflect.Descriptor instead.
func (*CheckPermissionRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_rbac_v1_rbac_proto_rawDescGZIP(), []int{18}
}

func (x *CheckPermissionRequest) GetResource() string {
	if x != nil {
		return x.Resource
	}
	return ""
}

func (x *CheckPermissionRequest) GetActions() []string {
	if x != nil {
		return x.Actions
	}
	return nil
}

func (x *CheckPermissionRequest) GetScope() string {
	if x != nil {
		return x.Scope
	}
	return ""
}

func (x *CheckPermissionRequest) GetAccount() string {
	if x != nil {
		return x.Account
	}
	return ""
}

type CheckPermissionResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Allowed bool   `protobuf:"varint,1,opt,name=allowed,proto3" json:"allowed,omitempty"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *CheckPermissionResponse) Reset() {
	*x = CheckPermissionResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_rbac_v1_rbac_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckPermissionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckPermissionResponse) ProtoMessage() {}

func (x *CheckPermissionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_rbac_v1_rbac_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckPermissionResponse.ProtoReflect.Descriptor instead.
func (*CheckPermissionResponse) Descriptor() ([]byte, []int) {
	return file_aistudio_rbac_v1_rbac_proto_rawDescGZIP(), []int{19}
}

func (x *CheckPermissionResponse) GetAllowed() bool {
	if x != nil {
		return x.Allowed
	}
	return false
}

func (x *CheckPermissionResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type UserBinding struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Timestamp   *common.TimestampModel `protobuf:"bytes,1,opt,name=timestamp,proto3" json:"timestamp,omitempty"`     // 时间戳
	Id          string                 `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`                   // 用户绑定ID
	Account     string                 `protobuf:"bytes,3,opt,name=account,proto3" json:"account,omitempty"`         // 账户
	Scope       string                 `protobuf:"bytes,4,opt,name=scope,proto3" json:"scope,omitempty"`             // 用户范围
	Permissions []string               `protobuf:"bytes,5,rep,name=permissions,proto3" json:"permissions,omitempty"` // 用户权限
	Description string                 `protobuf:"bytes,6,opt,name=description,proto3" json:"description,omitempty"` // 用户绑定描述
}

func (x *UserBinding) Reset() {
	*x = UserBinding{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_rbac_v1_rbac_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserBinding) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserBinding) ProtoMessage() {}

func (x *UserBinding) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_rbac_v1_rbac_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserBinding.ProtoReflect.Descriptor instead.
func (*UserBinding) Descriptor() ([]byte, []int) {
	return file_aistudio_rbac_v1_rbac_proto_rawDescGZIP(), []int{20}
}

func (x *UserBinding) GetTimestamp() *common.TimestampModel {
	if x != nil {
		return x.Timestamp
	}
	return nil
}

func (x *UserBinding) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *UserBinding) GetAccount() string {
	if x != nil {
		return x.Account
	}
	return ""
}

func (x *UserBinding) GetScope() string {
	if x != nil {
		return x.Scope
	}
	return ""
}

func (x *UserBinding) GetPermissions() []string {
	if x != nil {
		return x.Permissions
	}
	return nil
}

func (x *UserBinding) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

type CreateOrUpdateUserBindingRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Account     string   `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`         // 账户
	Scope       string   `protobuf:"bytes,2,opt,name=scope,proto3" json:"scope,omitempty"`             // 用户范围
	Permissions []string `protobuf:"bytes,3,rep,name=permissions,proto3" json:"permissions,omitempty"` // 用户权限
	Description string   `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"` // 用户绑定描述
}

func (x *CreateOrUpdateUserBindingRequest) Reset() {
	*x = CreateOrUpdateUserBindingRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_rbac_v1_rbac_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateOrUpdateUserBindingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateOrUpdateUserBindingRequest) ProtoMessage() {}

func (x *CreateOrUpdateUserBindingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_rbac_v1_rbac_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateOrUpdateUserBindingRequest.ProtoReflect.Descriptor instead.
func (*CreateOrUpdateUserBindingRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_rbac_v1_rbac_proto_rawDescGZIP(), []int{21}
}

func (x *CreateOrUpdateUserBindingRequest) GetAccount() string {
	if x != nil {
		return x.Account
	}
	return ""
}

func (x *CreateOrUpdateUserBindingRequest) GetScope() string {
	if x != nil {
		return x.Scope
	}
	return ""
}

func (x *CreateOrUpdateUserBindingRequest) GetPermissions() []string {
	if x != nil {
		return x.Permissions
	}
	return nil
}

func (x *CreateOrUpdateUserBindingRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

type DeleteUserBindingRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Account string `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"` // 账户
	Scope   string `protobuf:"bytes,2,opt,name=scope,proto3" json:"scope,omitempty"`     // 用户范围
}

func (x *DeleteUserBindingRequest) Reset() {
	*x = DeleteUserBindingRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_rbac_v1_rbac_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteUserBindingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteUserBindingRequest) ProtoMessage() {}

func (x *DeleteUserBindingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_rbac_v1_rbac_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteUserBindingRequest.ProtoReflect.Descriptor instead.
func (*DeleteUserBindingRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_rbac_v1_rbac_proto_rawDescGZIP(), []int{22}
}

func (x *DeleteUserBindingRequest) GetAccount() string {
	if x != nil {
		return x.Account
	}
	return ""
}

func (x *DeleteUserBindingRequest) GetScope() string {
	if x != nil {
		return x.Scope
	}
	return ""
}

type UserBindings struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserBindings []*UserBinding `protobuf:"bytes,1,rep,name=user_bindings,json=userBindings,proto3" json:"user_bindings,omitempty"` // 用户绑定列表
}

func (x *UserBindings) Reset() {
	*x = UserBindings{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_rbac_v1_rbac_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserBindings) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserBindings) ProtoMessage() {}

func (x *UserBindings) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_rbac_v1_rbac_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserBindings.ProtoReflect.Descriptor instead.
func (*UserBindings) Descriptor() ([]byte, []int) {
	return file_aistudio_rbac_v1_rbac_proto_rawDescGZIP(), []int{23}
}

func (x *UserBindings) GetUserBindings() []*UserBinding {
	if x != nil {
		return x.UserBindings
	}
	return nil
}

type ListUserBindingsOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Account string `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"` // 账户
	Scope   string `protobuf:"bytes,2,opt,name=scope,proto3" json:"scope,omitempty"`     // 用户范围
}

func (x *ListUserBindingsOptions) Reset() {
	*x = ListUserBindingsOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_rbac_v1_rbac_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListUserBindingsOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListUserBindingsOptions) ProtoMessage() {}

func (x *ListUserBindingsOptions) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_rbac_v1_rbac_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListUserBindingsOptions.ProtoReflect.Descriptor instead.
func (*ListUserBindingsOptions) Descriptor() ([]byte, []int) {
	return file_aistudio_rbac_v1_rbac_proto_rawDescGZIP(), []int{24}
}

func (x *ListUserBindingsOptions) GetAccount() string {
	if x != nil {
		return x.Account
	}
	return ""
}

func (x *ListUserBindingsOptions) GetScope() string {
	if x != nil {
		return x.Scope
	}
	return ""
}

var File_aistudio_rbac_v1_rbac_proto protoreflect.FileDescriptor

var file_aistudio_rbac_v1_rbac_proto_rawDesc = []byte{
	0x0a, 0x1b, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2f, 0x72, 0x62, 0x61, 0x63, 0x2f,
	0x76, 0x31, 0x2f, 0x72, 0x62, 0x61, 0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x15, 0x61,
	0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x72, 0x62, 0x61,
	0x63, 0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x13, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0x53, 0x0a, 0x1d, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x55, 0x73,
	0x65, 0x72, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x52, 0x6f, 0x6c, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x18, 0x0a, 0x07, 0x69, 0x73, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x07, 0x69, 0x73, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x22, 0xde, 0x01, 0x0a, 0x0d, 0x57, 0x6f,
	0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x12, 0x39, 0x0a, 0x09, 0x74,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b,
	0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x09, 0x74, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x6c,
	0x69, 0x61, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x6c, 0x69, 0x61, 0x73,
	0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x53, 0x63, 0x6f, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x53, 0x63, 0x6f, 0x70, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x70, 0x65, 0x72, 0x6d,
	0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x70,
	0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0xa8, 0x01, 0x0a, 0x22, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x57, 0x6f, 0x72,
	0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x6c, 0x69, 0x61, 0x73, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x6c, 0x69, 0x61, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x14, 0x0a,
	0x05, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x63,
	0x6f, 0x70, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0xa0, 0x01, 0x0a, 0x1a, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x6c, 0x69, 0x61,
	0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x6c, 0x69, 0x61, 0x73, 0x12, 0x20,
	0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x14, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x65, 0x72,
	0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x46, 0x0a, 0x1a, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x63,
	0x6f, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x70, 0x65,
	0x22, 0x4c, 0x0a, 0x0e, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x52, 0x6f, 0x6c,
	0x65, 0x73, 0x12, 0x3a, 0x0a, 0x05, 0x72, 0x6f, 0x6c, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69,
	0x6f, 0x2e, 0x72, 0x62, 0x61, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x52, 0x05, 0x72, 0x6f, 0x6c, 0x65, 0x73, 0x22, 0x31,
	0x0a, 0x19, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x52,
	0x6f, 0x6c, 0x65, 0x73, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x73,
	0x63, 0x6f, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x70,
	0x65, 0x22, 0xc9, 0x01, 0x0a, 0x14, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x52,
	0x6f, 0x6c, 0x65, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x39, 0x0a, 0x09, 0x74, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e,
	0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x14, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x73, 0x63, 0x6f, 0x70, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x72, 0x6f, 0x6c, 0x65, 0x73,
	0x18, 0x07, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x72, 0x6f, 0x6c, 0x65, 0x73, 0x22, 0x93, 0x01,
	0x0a, 0x29, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x42, 0x69, 0x6e,
	0x64, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x14, 0x0a,
	0x05, 0x72, 0x6f, 0x6c, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x72, 0x6f,
	0x6c, 0x65, 0x73, 0x22, 0x69, 0x0a, 0x21, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x57, 0x6f, 0x72,
	0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x72, 0x6f, 0x6c, 0x65,
	0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x72, 0x6f, 0x6c, 0x65, 0x73, 0x22, 0x69,
	0x0a, 0x15, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x42,
	0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x50, 0x0a, 0x0d, 0x72, 0x6f, 0x6c, 0x65, 0x5f,
	0x62, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b,
	0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x72,
	0x62, 0x61, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x52, 0x6f, 0x6c, 0x65, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x52, 0x0c, 0x72, 0x6f, 0x6c,
	0x65, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x22, 0x52, 0x0a, 0x20, 0x4c, 0x69, 0x73,
	0x74, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x42, 0x69,
	0x6e, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x18, 0x0a,
	0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x70, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x22, 0x9b, 0x02,
	0x0a, 0x13, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x50, 0x65, 0x72, 0x6d, 0x69,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x39, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x12, 0x1c, 0x0a, 0x09,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x09, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x12, 0x35, 0x0a, 0x06, 0x65, 0x66, 0x66, 0x65, 0x63, 0x74, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74,
	0x75, 0x64, 0x69, 0x6f, 0x2e, 0x72, 0x62, 0x61, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x66, 0x66,
	0x65, 0x63, 0x74, 0x52, 0x06, 0x65, 0x66, 0x66, 0x65, 0x63, 0x74, 0x22, 0xe5, 0x01, 0x0a, 0x28,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x57, 0x6f,
	0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b,
	0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x14,
	0x0a, 0x05, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73,
	0x63, 0x6f, 0x70, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x05, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x35, 0x0a, 0x06,
	0x65, 0x66, 0x66, 0x65, 0x63, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x61,
	0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x72, 0x62, 0x61,
	0x63, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x66, 0x66, 0x65, 0x63, 0x74, 0x52, 0x06, 0x65, 0x66, 0x66,
	0x65, 0x63, 0x74, 0x22, 0xdd, 0x01, 0x0a, 0x20, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x57, 0x6f,
	0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b,
	0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x14,
	0x0a, 0x05, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73,
	0x63, 0x6f, 0x70, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x05, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x35, 0x0a, 0x06,
	0x65, 0x66, 0x66, 0x65, 0x63, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x61,
	0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x72, 0x62, 0x61,
	0x63, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x66, 0x66, 0x65, 0x63, 0x74, 0x52, 0x06, 0x65, 0x66, 0x66,
	0x65, 0x63, 0x74, 0x22, 0x4c, 0x0a, 0x20, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x57, 0x6f, 0x72,
	0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x73,
	0x63, 0x6f, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x70,
	0x65, 0x22, 0x64, 0x0a, 0x14, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x50, 0x65,
	0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x4c, 0x0a, 0x0b, 0x70, 0x65, 0x72,
	0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a,
	0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x72,
	0x62, 0x61, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x70, 0x65, 0x72, 0x6d,
	0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x37, 0x0a, 0x1f, 0x4c, 0x69, 0x73, 0x74, 0x57,
	0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x73, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x63,
	0x6f, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x70, 0x65,
	0x22, 0x7e, 0x0a, 0x16, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x12, 0x14, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x22, 0x4d, 0x0a, 0x17, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x61,
	0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x61, 0x6c,
	0x6c, 0x6f, 0x77, 0x65, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22,
	0xcc, 0x01, 0x0a, 0x0b, 0x55, 0x73, 0x65, 0x72, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x12,
	0x39, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52,
	0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x70, 0x65,
	0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x0b, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x20, 0x0a, 0x0b,
	0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x96,
	0x01, 0x0a, 0x20, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x55, 0x73, 0x65, 0x72, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x14, 0x0a,
	0x05, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x63,
	0x6f, 0x70, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x4a, 0x0a, 0x18, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x55, 0x73, 0x65, 0x72, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x14, 0x0a,
	0x05, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x63,
	0x6f, 0x70, 0x65, 0x22, 0x57, 0x0a, 0x0c, 0x55, 0x73, 0x65, 0x72, 0x42, 0x69, 0x6e, 0x64, 0x69,
	0x6e, 0x67, 0x73, 0x12, 0x47, 0x0a, 0x0d, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x62, 0x69, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x61, 0x70, 0x69,
	0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x72, 0x62, 0x61, 0x63, 0x2e,
	0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x52, 0x0c,
	0x75, 0x73, 0x65, 0x72, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x22, 0x49, 0x0a, 0x17,
	0x4c, 0x69, 0x73, 0x74, 0x55, 0x73, 0x65, 0x72, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x73,
	0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x2a, 0x1d, 0x0a, 0x06, 0x45, 0x66, 0x66, 0x65, 0x63,
	0x74, 0x12, 0x09, 0x0a, 0x05, 0x41, 0x4c, 0x4c, 0x4f, 0x57, 0x10, 0x00, 0x12, 0x08, 0x0a, 0x04,
	0x44, 0x45, 0x4e, 0x59, 0x10, 0x01, 0x32, 0xe9, 0x12, 0x0a, 0x0b, 0x52, 0x62, 0x61, 0x63, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0xc7, 0x01, 0x0a, 0x1b, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x4f, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x12, 0x39, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69,
	0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x72, 0x62, 0x61, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x57, 0x6f, 0x72,
	0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69,
	0x6f, 0x2e, 0x72, 0x62, 0x61, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x22, 0x47, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x41, 0x3a,
	0x01, 0x2a, 0x5a, 0x23, 0x3a, 0x01, 0x2a, 0x1a, 0x1e, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76,
	0x31, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x5f, 0x72, 0x6f, 0x6c, 0x65,
	0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x22, 0x17, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76,
	0x31, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x5f, 0x72, 0x6f, 0x6c, 0x65,
	0x12, 0x88, 0x01, 0x0a, 0x13, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x12, 0x31, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e,
	0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x72, 0x62, 0x61, 0x63, 0x2e, 0x76, 0x31,
	0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x52, 0x6f, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x22, 0x26, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x20, 0x2a, 0x1e, 0x2f, 0x61, 0x70,
	0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x5f,
	0x72, 0x6f, 0x6c, 0x65, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x12, 0x8f, 0x01, 0x0a, 0x12,
	0x4c, 0x69, 0x73, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x52, 0x6f, 0x6c,
	0x65, 0x73, 0x12, 0x30, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64,
	0x69, 0x6f, 0x2e, 0x72, 0x62, 0x61, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x57,
	0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x73, 0x4f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x1a, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74,
	0x75, 0x64, 0x69, 0x6f, 0x2e, 0x72, 0x62, 0x61, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x6f, 0x72,
	0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x73, 0x22, 0x20, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x1a, 0x12, 0x18, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x6f,
	0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x5f, 0x72, 0x6f, 0x6c, 0x65, 0x73, 0x12, 0xef, 0x01,
	0x0a, 0x22, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x42, 0x69, 0x6e,
	0x64, 0x69, 0x6e, 0x67, 0x12, 0x40, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74,
	0x75, 0x64, 0x69, 0x6f, 0x2e, 0x72, 0x62, 0x61, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x4f, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69,
	0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x72, 0x62, 0x61, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x57,
	0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x42, 0x69, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x22, 0x5a, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x54, 0x3a, 0x01, 0x2a, 0x5a, 0x2e,
	0x3a, 0x01, 0x2a, 0x1a, 0x29, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x6f,
	0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x5f, 0x72, 0x6f, 0x6c, 0x65, 0x5f, 0x62, 0x69, 0x6e,
	0x64, 0x69, 0x6e, 0x67, 0x2f, 0x7b, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x7d, 0x22, 0x1f,
	0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x5f, 0x72, 0x6f, 0x6c, 0x65, 0x5f, 0x62, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x12,
	0xa1, 0x01, 0x0a, 0x1a, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x38,
	0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x72,
	0x62, 0x61, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x57, 0x6f, 0x72,
	0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79,
	0x22, 0x31, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2b, 0x2a, 0x29, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f,
	0x76, 0x31, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x5f, 0x72, 0x6f, 0x6c,
	0x65, 0x5f, 0x62, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2f, 0x7b, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x7d, 0x12, 0xac, 0x01, 0x0a, 0x19, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x6f, 0x72, 0x6b,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x73, 0x12, 0x37, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69,
	0x6f, 0x2e, 0x72, 0x62, 0x61, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x6f,
	0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x42, 0x69, 0x6e, 0x64, 0x69,
	0x6e, 0x67, 0x73, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x1a, 0x2c, 0x2e, 0x61, 0x70, 0x69,
	0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x72, 0x62, 0x61, 0x63, 0x2e,
	0x76, 0x31, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x52, 0x6f, 0x6c, 0x65,
	0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x22, 0x28, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x22,
	0x12, 0x20, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x5f, 0x72, 0x6f, 0x6c, 0x65, 0x5f, 0x62, 0x69, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x73, 0x12, 0xe5, 0x01, 0x0a, 0x21, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x50, 0x65,
	0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x3f, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e,
	0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x72, 0x62, 0x61, 0x63, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x57,
	0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2a, 0x2e, 0x61, 0x70, 0x69, 0x73,
	0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x72, 0x62, 0x61, 0x63, 0x2e, 0x76,
	0x31, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x50, 0x65, 0x72, 0x6d, 0x69,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x53, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x4d, 0x3a, 0x01, 0x2a,
	0x5a, 0x29, 0x3a, 0x01, 0x2a, 0x1a, 0x24, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f,
	0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x5f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x22, 0x1d, 0x2f, 0x61, 0x70,
	0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x5f,
	0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x9a, 0x01, 0x0a, 0x19, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x50, 0x65,
	0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x37, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e,
	0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x72, 0x62, 0x61, 0x63, 0x2e, 0x76, 0x31,
	0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x2c, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x26, 0x2a, 0x24, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x6f, 0x72, 0x6b,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x5f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x12, 0xa7, 0x01, 0x0a, 0x18, 0x4c, 0x69, 0x73, 0x74,
	0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x73, 0x12, 0x36, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74,
	0x75, 0x64, 0x69, 0x6f, 0x2e, 0x72, 0x62, 0x61, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x73, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x1a, 0x2b, 0x2e, 0x61,
	0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x72, 0x62, 0x61,
	0x63, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x50, 0x65,
	0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x26, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x20, 0x12, 0x1e, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x6f, 0x72, 0x6b,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x5f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x73, 0x12, 0x96, 0x01, 0x0a, 0x0f, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x50, 0x65, 0x72, 0x6d, 0x69,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x2d, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73,
	0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x72, 0x62, 0x61, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68,
	0x65, 0x63, 0x6b, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74,
	0x75, 0x64, 0x69, 0x6f, 0x2e, 0x72, 0x62, 0x61, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x65,
	0x63, 0x6b, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x24, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1e, 0x3a, 0x01, 0x2a, 0x22,
	0x19, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x5f,
	0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0xc0, 0x01, 0x0a, 0x19, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65,
	0x72, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x37, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e,
	0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x72, 0x62, 0x61, 0x63, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x55,
	0x73, 0x65, 0x72, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69,
	0x6f, 0x2e, 0x72, 0x62, 0x61, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x42, 0x69,
	0x6e, 0x64, 0x69, 0x6e, 0x67, 0x22, 0x46, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x40, 0x3a, 0x01, 0x2a,
	0x5a, 0x24, 0x3a, 0x01, 0x2a, 0x1a, 0x1f, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f,
	0x75, 0x73, 0x65, 0x72, 0x5f, 0x62, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2f, 0x7b, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x7d, 0x22, 0x15, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31,
	0x2f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x62, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x85, 0x01,
	0x0a, 0x11, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x42, 0x69, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x12, 0x2f, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75,
	0x64, 0x69, 0x6f, 0x2e, 0x72, 0x62, 0x61, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x27, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x21, 0x2a, 0x1f, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x75,
	0x73, 0x65, 0x72, 0x5f, 0x62, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2f, 0x7b, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x7d, 0x12, 0x87, 0x01, 0x0a, 0x10, 0x4c, 0x69, 0x73, 0x74, 0x55, 0x73,
	0x65, 0x72, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x2e, 0x2e, 0x61, 0x70, 0x69,
	0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x72, 0x62, 0x61, 0x63, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x55, 0x73, 0x65, 0x72, 0x42, 0x69, 0x6e, 0x64, 0x69,
	0x6e, 0x67, 0x73, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x1a, 0x23, 0x2e, 0x61, 0x70, 0x69,
	0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x72, 0x62, 0x61, 0x63, 0x2e,
	0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x22,
	0x1e, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x18, 0x12, 0x16, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76,
	0x31, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x62, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x12,
	0x90, 0x01, 0x0a, 0x16, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x55, 0x73, 0x65, 0x72, 0x50, 0x6c,
	0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x52, 0x6f, 0x6c, 0x65, 0x12, 0x34, 0x2e, 0x61, 0x70, 0x69,
	0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x72, 0x62, 0x61, 0x63, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x55, 0x73, 0x65, 0x72, 0x50, 0x6c, 0x61,
	0x74, 0x66, 0x6f, 0x72, 0x6d, 0x52, 0x6f, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x28, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x22,
	0x3a, 0x01, 0x2a, 0x22, 0x1d, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x5f, 0x72, 0x6f,
	0x6c, 0x65, 0x42, 0x47, 0x5a, 0x45, 0x67, 0x69, 0x74, 0x2e, 0x6c, 0x69, 0x61, 0x6e, 0x6a, 0x69,
	0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6e, 0x61, 0x74, 0x69, 0x76,
	0x65, 0x2f, 0x6b, 0x69, 0x63, 0x2f, 0x6b, 0x69, 0x63, 0x2d, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f,
	0x72, 0x6d, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f,
	0x2f, 0x72, 0x62, 0x61, 0x63, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_aistudio_rbac_v1_rbac_proto_rawDescOnce sync.Once
	file_aistudio_rbac_v1_rbac_proto_rawDescData = file_aistudio_rbac_v1_rbac_proto_rawDesc
)

func file_aistudio_rbac_v1_rbac_proto_rawDescGZIP() []byte {
	file_aistudio_rbac_v1_rbac_proto_rawDescOnce.Do(func() {
		file_aistudio_rbac_v1_rbac_proto_rawDescData = protoimpl.X.CompressGZIP(file_aistudio_rbac_v1_rbac_proto_rawDescData)
	})
	return file_aistudio_rbac_v1_rbac_proto_rawDescData
}

var file_aistudio_rbac_v1_rbac_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_aistudio_rbac_v1_rbac_proto_msgTypes = make([]protoimpl.MessageInfo, 25)
var file_aistudio_rbac_v1_rbac_proto_goTypes = []any{
	(Effect)(0),                                       // 0: apis.aistudio.rbac.v1.Effect
	(*ChangeUserPlatformRoleRequest)(nil),             // 1: apis.aistudio.rbac.v1.ChangeUserPlatformRoleRequest
	(*WorkspaceRole)(nil),                             // 2: apis.aistudio.rbac.v1.WorkspaceRole
	(*CreateOrUpdateWorkspaceRoleRequest)(nil),        // 3: apis.aistudio.rbac.v1.CreateOrUpdateWorkspaceRoleRequest
	(*UpdateWorkspaceRoleRequest)(nil),                // 4: apis.aistudio.rbac.v1.UpdateWorkspaceRoleRequest
	(*DeleteWorkspaceRoleRequest)(nil),                // 5: apis.aistudio.rbac.v1.DeleteWorkspaceRoleRequest
	(*WorkspaceRoles)(nil),                            // 6: apis.aistudio.rbac.v1.WorkspaceRoles
	(*ListWorkspaceRolesOptions)(nil),                 // 7: apis.aistudio.rbac.v1.ListWorkspaceRolesOptions
	(*WorkspaceRoleBinding)(nil),                      // 8: apis.aistudio.rbac.v1.WorkspaceRoleBinding
	(*CreateOrUpdateWorkspaceRoleBindingRequest)(nil), // 9: apis.aistudio.rbac.v1.CreateOrUpdateWorkspaceRoleBindingRequest
	(*DeleteWorkspaceRoleBindingRequest)(nil),         // 10: apis.aistudio.rbac.v1.DeleteWorkspaceRoleBindingRequest
	(*WorkspaceRoleBindings)(nil),                     // 11: apis.aistudio.rbac.v1.WorkspaceRoleBindings
	(*ListWorkspaceRoleBindingsOptions)(nil),          // 12: apis.aistudio.rbac.v1.ListWorkspaceRoleBindingsOptions
	(*WorkspacePermission)(nil),                       // 13: apis.aistudio.rbac.v1.WorkspacePermission
	(*CreateOrUpdateWorkspacePermissionRequest)(nil),  // 14: apis.aistudio.rbac.v1.CreateOrUpdateWorkspacePermissionRequest
	(*UpdateWorkspacePermissionRequest)(nil),          // 15: apis.aistudio.rbac.v1.UpdateWorkspacePermissionRequest
	(*DeleteWorkspacePermissionRequest)(nil),          // 16: apis.aistudio.rbac.v1.DeleteWorkspacePermissionRequest
	(*WorkspacePermissions)(nil),                      // 17: apis.aistudio.rbac.v1.WorkspacePermissions
	(*ListWorkspacePermissionsOptions)(nil),           // 18: apis.aistudio.rbac.v1.ListWorkspacePermissionsOptions
	(*CheckPermissionRequest)(nil),                    // 19: apis.aistudio.rbac.v1.CheckPermissionRequest
	(*CheckPermissionResponse)(nil),                   // 20: apis.aistudio.rbac.v1.CheckPermissionResponse
	(*UserBinding)(nil),                               // 21: apis.aistudio.rbac.v1.UserBinding
	(*CreateOrUpdateUserBindingRequest)(nil),          // 22: apis.aistudio.rbac.v1.CreateOrUpdateUserBindingRequest
	(*DeleteUserBindingRequest)(nil),                  // 23: apis.aistudio.rbac.v1.DeleteUserBindingRequest
	(*UserBindings)(nil),                              // 24: apis.aistudio.rbac.v1.UserBindings
	(*ListUserBindingsOptions)(nil),                   // 25: apis.aistudio.rbac.v1.ListUserBindingsOptions
	(*common.TimestampModel)(nil),                     // 26: apis.common.TimestampModel
	(*emptypb.Empty)(nil),                             // 27: google.protobuf.Empty
}
var file_aistudio_rbac_v1_rbac_proto_depIdxs = []int32{
	26, // 0: apis.aistudio.rbac.v1.WorkspaceRole.timestamp:type_name -> apis.common.TimestampModel
	2,  // 1: apis.aistudio.rbac.v1.WorkspaceRoles.roles:type_name -> apis.aistudio.rbac.v1.WorkspaceRole
	26, // 2: apis.aistudio.rbac.v1.WorkspaceRoleBinding.timestamp:type_name -> apis.common.TimestampModel
	8,  // 3: apis.aistudio.rbac.v1.WorkspaceRoleBindings.role_bindings:type_name -> apis.aistudio.rbac.v1.WorkspaceRoleBinding
	26, // 4: apis.aistudio.rbac.v1.WorkspacePermission.timestamp:type_name -> apis.common.TimestampModel
	0,  // 5: apis.aistudio.rbac.v1.WorkspacePermission.effect:type_name -> apis.aistudio.rbac.v1.Effect
	0,  // 6: apis.aistudio.rbac.v1.CreateOrUpdateWorkspacePermissionRequest.effect:type_name -> apis.aistudio.rbac.v1.Effect
	0,  // 7: apis.aistudio.rbac.v1.UpdateWorkspacePermissionRequest.effect:type_name -> apis.aistudio.rbac.v1.Effect
	13, // 8: apis.aistudio.rbac.v1.WorkspacePermissions.permissions:type_name -> apis.aistudio.rbac.v1.WorkspacePermission
	26, // 9: apis.aistudio.rbac.v1.UserBinding.timestamp:type_name -> apis.common.TimestampModel
	21, // 10: apis.aistudio.rbac.v1.UserBindings.user_bindings:type_name -> apis.aistudio.rbac.v1.UserBinding
	3,  // 11: apis.aistudio.rbac.v1.RbacService.CreateOrUpdateWorkspaceRole:input_type -> apis.aistudio.rbac.v1.CreateOrUpdateWorkspaceRoleRequest
	5,  // 12: apis.aistudio.rbac.v1.RbacService.DeleteWorkspaceRole:input_type -> apis.aistudio.rbac.v1.DeleteWorkspaceRoleRequest
	7,  // 13: apis.aistudio.rbac.v1.RbacService.ListWorkspaceRoles:input_type -> apis.aistudio.rbac.v1.ListWorkspaceRolesOptions
	9,  // 14: apis.aistudio.rbac.v1.RbacService.CreateOrUpdateWorkspaceRoleBinding:input_type -> apis.aistudio.rbac.v1.CreateOrUpdateWorkspaceRoleBindingRequest
	10, // 15: apis.aistudio.rbac.v1.RbacService.DeleteWorkspaceRoleBinding:input_type -> apis.aistudio.rbac.v1.DeleteWorkspaceRoleBindingRequest
	12, // 16: apis.aistudio.rbac.v1.RbacService.ListWorkspaceRoleBindings:input_type -> apis.aistudio.rbac.v1.ListWorkspaceRoleBindingsOptions
	14, // 17: apis.aistudio.rbac.v1.RbacService.CreateOrUpdateWorkspacePermission:input_type -> apis.aistudio.rbac.v1.CreateOrUpdateWorkspacePermissionRequest
	16, // 18: apis.aistudio.rbac.v1.RbacService.DeleteWorkspacePermission:input_type -> apis.aistudio.rbac.v1.DeleteWorkspacePermissionRequest
	18, // 19: apis.aistudio.rbac.v1.RbacService.ListWorkspacePermissions:input_type -> apis.aistudio.rbac.v1.ListWorkspacePermissionsOptions
	19, // 20: apis.aistudio.rbac.v1.RbacService.CheckPermission:input_type -> apis.aistudio.rbac.v1.CheckPermissionRequest
	22, // 21: apis.aistudio.rbac.v1.RbacService.CreateOrUpdateUserBinding:input_type -> apis.aistudio.rbac.v1.CreateOrUpdateUserBindingRequest
	23, // 22: apis.aistudio.rbac.v1.RbacService.DeleteUserBinding:input_type -> apis.aistudio.rbac.v1.DeleteUserBindingRequest
	25, // 23: apis.aistudio.rbac.v1.RbacService.ListUserBindings:input_type -> apis.aistudio.rbac.v1.ListUserBindingsOptions
	1,  // 24: apis.aistudio.rbac.v1.RbacService.ChangeUserPlatformRole:input_type -> apis.aistudio.rbac.v1.ChangeUserPlatformRoleRequest
	2,  // 25: apis.aistudio.rbac.v1.RbacService.CreateOrUpdateWorkspaceRole:output_type -> apis.aistudio.rbac.v1.WorkspaceRole
	27, // 26: apis.aistudio.rbac.v1.RbacService.DeleteWorkspaceRole:output_type -> google.protobuf.Empty
	6,  // 27: apis.aistudio.rbac.v1.RbacService.ListWorkspaceRoles:output_type -> apis.aistudio.rbac.v1.WorkspaceRoles
	8,  // 28: apis.aistudio.rbac.v1.RbacService.CreateOrUpdateWorkspaceRoleBinding:output_type -> apis.aistudio.rbac.v1.WorkspaceRoleBinding
	27, // 29: apis.aistudio.rbac.v1.RbacService.DeleteWorkspaceRoleBinding:output_type -> google.protobuf.Empty
	11, // 30: apis.aistudio.rbac.v1.RbacService.ListWorkspaceRoleBindings:output_type -> apis.aistudio.rbac.v1.WorkspaceRoleBindings
	13, // 31: apis.aistudio.rbac.v1.RbacService.CreateOrUpdateWorkspacePermission:output_type -> apis.aistudio.rbac.v1.WorkspacePermission
	27, // 32: apis.aistudio.rbac.v1.RbacService.DeleteWorkspacePermission:output_type -> google.protobuf.Empty
	17, // 33: apis.aistudio.rbac.v1.RbacService.ListWorkspacePermissions:output_type -> apis.aistudio.rbac.v1.WorkspacePermissions
	20, // 34: apis.aistudio.rbac.v1.RbacService.CheckPermission:output_type -> apis.aistudio.rbac.v1.CheckPermissionResponse
	21, // 35: apis.aistudio.rbac.v1.RbacService.CreateOrUpdateUserBinding:output_type -> apis.aistudio.rbac.v1.UserBinding
	27, // 36: apis.aistudio.rbac.v1.RbacService.DeleteUserBinding:output_type -> google.protobuf.Empty
	24, // 37: apis.aistudio.rbac.v1.RbacService.ListUserBindings:output_type -> apis.aistudio.rbac.v1.UserBindings
	27, // 38: apis.aistudio.rbac.v1.RbacService.ChangeUserPlatformRole:output_type -> google.protobuf.Empty
	25, // [25:39] is the sub-list for method output_type
	11, // [11:25] is the sub-list for method input_type
	11, // [11:11] is the sub-list for extension type_name
	11, // [11:11] is the sub-list for extension extendee
	0,  // [0:11] is the sub-list for field type_name
}

func init() { file_aistudio_rbac_v1_rbac_proto_init() }
func file_aistudio_rbac_v1_rbac_proto_init() {
	if File_aistudio_rbac_v1_rbac_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_aistudio_rbac_v1_rbac_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*ChangeUserPlatformRoleRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_rbac_v1_rbac_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*WorkspaceRole); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_rbac_v1_rbac_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*CreateOrUpdateWorkspaceRoleRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_rbac_v1_rbac_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*UpdateWorkspaceRoleRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_rbac_v1_rbac_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*DeleteWorkspaceRoleRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_rbac_v1_rbac_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*WorkspaceRoles); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_rbac_v1_rbac_proto_msgTypes[6].Exporter = func(v any, i int) any {
			switch v := v.(*ListWorkspaceRolesOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_rbac_v1_rbac_proto_msgTypes[7].Exporter = func(v any, i int) any {
			switch v := v.(*WorkspaceRoleBinding); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_rbac_v1_rbac_proto_msgTypes[8].Exporter = func(v any, i int) any {
			switch v := v.(*CreateOrUpdateWorkspaceRoleBindingRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_rbac_v1_rbac_proto_msgTypes[9].Exporter = func(v any, i int) any {
			switch v := v.(*DeleteWorkspaceRoleBindingRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_rbac_v1_rbac_proto_msgTypes[10].Exporter = func(v any, i int) any {
			switch v := v.(*WorkspaceRoleBindings); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_rbac_v1_rbac_proto_msgTypes[11].Exporter = func(v any, i int) any {
			switch v := v.(*ListWorkspaceRoleBindingsOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_rbac_v1_rbac_proto_msgTypes[12].Exporter = func(v any, i int) any {
			switch v := v.(*WorkspacePermission); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_rbac_v1_rbac_proto_msgTypes[13].Exporter = func(v any, i int) any {
			switch v := v.(*CreateOrUpdateWorkspacePermissionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_rbac_v1_rbac_proto_msgTypes[14].Exporter = func(v any, i int) any {
			switch v := v.(*UpdateWorkspacePermissionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_rbac_v1_rbac_proto_msgTypes[15].Exporter = func(v any, i int) any {
			switch v := v.(*DeleteWorkspacePermissionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_rbac_v1_rbac_proto_msgTypes[16].Exporter = func(v any, i int) any {
			switch v := v.(*WorkspacePermissions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_rbac_v1_rbac_proto_msgTypes[17].Exporter = func(v any, i int) any {
			switch v := v.(*ListWorkspacePermissionsOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_rbac_v1_rbac_proto_msgTypes[18].Exporter = func(v any, i int) any {
			switch v := v.(*CheckPermissionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_rbac_v1_rbac_proto_msgTypes[19].Exporter = func(v any, i int) any {
			switch v := v.(*CheckPermissionResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_rbac_v1_rbac_proto_msgTypes[20].Exporter = func(v any, i int) any {
			switch v := v.(*UserBinding); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_rbac_v1_rbac_proto_msgTypes[21].Exporter = func(v any, i int) any {
			switch v := v.(*CreateOrUpdateUserBindingRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_rbac_v1_rbac_proto_msgTypes[22].Exporter = func(v any, i int) any {
			switch v := v.(*DeleteUserBindingRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_rbac_v1_rbac_proto_msgTypes[23].Exporter = func(v any, i int) any {
			switch v := v.(*UserBindings); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_rbac_v1_rbac_proto_msgTypes[24].Exporter = func(v any, i int) any {
			switch v := v.(*ListUserBindingsOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_aistudio_rbac_v1_rbac_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   25,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_aistudio_rbac_v1_rbac_proto_goTypes,
		DependencyIndexes: file_aistudio_rbac_v1_rbac_proto_depIdxs,
		EnumInfos:         file_aistudio_rbac_v1_rbac_proto_enumTypes,
		MessageInfos:      file_aistudio_rbac_v1_rbac_proto_msgTypes,
	}.Build()
	File_aistudio_rbac_v1_rbac_proto = out.File
	file_aistudio_rbac_v1_rbac_proto_rawDesc = nil
	file_aistudio_rbac_v1_rbac_proto_goTypes = nil
	file_aistudio_rbac_v1_rbac_proto_depIdxs = nil
}
