// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v5.29.3
// source: aistudio/rbac/v1/rbac.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	RbacService_CreateOrUpdateWorkspaceRole_FullMethodName        = "/apis.aistudio.rbac.v1.RbacService/CreateOrUpdateWorkspaceRole"
	RbacService_DeleteWorkspaceRole_FullMethodName                = "/apis.aistudio.rbac.v1.RbacService/DeleteWorkspaceRole"
	RbacService_ListWorkspaceRoles_FullMethodName                 = "/apis.aistudio.rbac.v1.RbacService/ListWorkspaceRoles"
	RbacService_CreateOrUpdateWorkspaceRoleBinding_FullMethodName = "/apis.aistudio.rbac.v1.RbacService/CreateOrUpdateWorkspaceRoleBinding"
	RbacService_DeleteWorkspaceRoleBinding_FullMethodName         = "/apis.aistudio.rbac.v1.RbacService/DeleteWorkspaceRoleBinding"
	RbacService_ListWorkspaceRoleBindings_FullMethodName          = "/apis.aistudio.rbac.v1.RbacService/ListWorkspaceRoleBindings"
	RbacService_CreateOrUpdateWorkspacePermission_FullMethodName  = "/apis.aistudio.rbac.v1.RbacService/CreateOrUpdateWorkspacePermission"
	RbacService_DeleteWorkspacePermission_FullMethodName          = "/apis.aistudio.rbac.v1.RbacService/DeleteWorkspacePermission"
	RbacService_ListWorkspacePermissions_FullMethodName           = "/apis.aistudio.rbac.v1.RbacService/ListWorkspacePermissions"
	RbacService_CheckPermission_FullMethodName                    = "/apis.aistudio.rbac.v1.RbacService/CheckPermission"
	RbacService_CreateOrUpdateUserBinding_FullMethodName          = "/apis.aistudio.rbac.v1.RbacService/CreateOrUpdateUserBinding"
	RbacService_DeleteUserBinding_FullMethodName                  = "/apis.aistudio.rbac.v1.RbacService/DeleteUserBinding"
	RbacService_ListUserBindings_FullMethodName                   = "/apis.aistudio.rbac.v1.RbacService/ListUserBindings"
	RbacService_ChangeUserPlatformRole_FullMethodName             = "/apis.aistudio.rbac.v1.RbacService/ChangeUserPlatformRole"
)

// RbacServiceClient is the client API for RbacService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type RbacServiceClient interface {
	CreateOrUpdateWorkspaceRole(ctx context.Context, in *CreateOrUpdateWorkspaceRoleRequest, opts ...grpc.CallOption) (*WorkspaceRole, error)
	DeleteWorkspaceRole(ctx context.Context, in *DeleteWorkspaceRoleRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	ListWorkspaceRoles(ctx context.Context, in *ListWorkspaceRolesOptions, opts ...grpc.CallOption) (*WorkspaceRoles, error)
	CreateOrUpdateWorkspaceRoleBinding(ctx context.Context, in *CreateOrUpdateWorkspaceRoleBindingRequest, opts ...grpc.CallOption) (*WorkspaceRoleBinding, error)
	DeleteWorkspaceRoleBinding(ctx context.Context, in *DeleteWorkspaceRoleBindingRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	ListWorkspaceRoleBindings(ctx context.Context, in *ListWorkspaceRoleBindingsOptions, opts ...grpc.CallOption) (*WorkspaceRoleBindings, error)
	CreateOrUpdateWorkspacePermission(ctx context.Context, in *CreateOrUpdateWorkspacePermissionRequest, opts ...grpc.CallOption) (*WorkspacePermission, error)
	DeleteWorkspacePermission(ctx context.Context, in *DeleteWorkspacePermissionRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	ListWorkspacePermissions(ctx context.Context, in *ListWorkspacePermissionsOptions, opts ...grpc.CallOption) (*WorkspacePermissions, error)
	CheckPermission(ctx context.Context, in *CheckPermissionRequest, opts ...grpc.CallOption) (*CheckPermissionResponse, error)
	CreateOrUpdateUserBinding(ctx context.Context, in *CreateOrUpdateUserBindingRequest, opts ...grpc.CallOption) (*UserBinding, error)
	DeleteUserBinding(ctx context.Context, in *DeleteUserBindingRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	ListUserBindings(ctx context.Context, in *ListUserBindingsOptions, opts ...grpc.CallOption) (*UserBindings, error)
	ChangeUserPlatformRole(ctx context.Context, in *ChangeUserPlatformRoleRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
}

type rbacServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewRbacServiceClient(cc grpc.ClientConnInterface) RbacServiceClient {
	return &rbacServiceClient{cc}
}

func (c *rbacServiceClient) CreateOrUpdateWorkspaceRole(ctx context.Context, in *CreateOrUpdateWorkspaceRoleRequest, opts ...grpc.CallOption) (*WorkspaceRole, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(WorkspaceRole)
	err := c.cc.Invoke(ctx, RbacService_CreateOrUpdateWorkspaceRole_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rbacServiceClient) DeleteWorkspaceRole(ctx context.Context, in *DeleteWorkspaceRoleRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, RbacService_DeleteWorkspaceRole_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rbacServiceClient) ListWorkspaceRoles(ctx context.Context, in *ListWorkspaceRolesOptions, opts ...grpc.CallOption) (*WorkspaceRoles, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(WorkspaceRoles)
	err := c.cc.Invoke(ctx, RbacService_ListWorkspaceRoles_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rbacServiceClient) CreateOrUpdateWorkspaceRoleBinding(ctx context.Context, in *CreateOrUpdateWorkspaceRoleBindingRequest, opts ...grpc.CallOption) (*WorkspaceRoleBinding, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(WorkspaceRoleBinding)
	err := c.cc.Invoke(ctx, RbacService_CreateOrUpdateWorkspaceRoleBinding_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rbacServiceClient) DeleteWorkspaceRoleBinding(ctx context.Context, in *DeleteWorkspaceRoleBindingRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, RbacService_DeleteWorkspaceRoleBinding_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rbacServiceClient) ListWorkspaceRoleBindings(ctx context.Context, in *ListWorkspaceRoleBindingsOptions, opts ...grpc.CallOption) (*WorkspaceRoleBindings, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(WorkspaceRoleBindings)
	err := c.cc.Invoke(ctx, RbacService_ListWorkspaceRoleBindings_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rbacServiceClient) CreateOrUpdateWorkspacePermission(ctx context.Context, in *CreateOrUpdateWorkspacePermissionRequest, opts ...grpc.CallOption) (*WorkspacePermission, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(WorkspacePermission)
	err := c.cc.Invoke(ctx, RbacService_CreateOrUpdateWorkspacePermission_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rbacServiceClient) DeleteWorkspacePermission(ctx context.Context, in *DeleteWorkspacePermissionRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, RbacService_DeleteWorkspacePermission_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rbacServiceClient) ListWorkspacePermissions(ctx context.Context, in *ListWorkspacePermissionsOptions, opts ...grpc.CallOption) (*WorkspacePermissions, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(WorkspacePermissions)
	err := c.cc.Invoke(ctx, RbacService_ListWorkspacePermissions_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rbacServiceClient) CheckPermission(ctx context.Context, in *CheckPermissionRequest, opts ...grpc.CallOption) (*CheckPermissionResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CheckPermissionResponse)
	err := c.cc.Invoke(ctx, RbacService_CheckPermission_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rbacServiceClient) CreateOrUpdateUserBinding(ctx context.Context, in *CreateOrUpdateUserBindingRequest, opts ...grpc.CallOption) (*UserBinding, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UserBinding)
	err := c.cc.Invoke(ctx, RbacService_CreateOrUpdateUserBinding_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rbacServiceClient) DeleteUserBinding(ctx context.Context, in *DeleteUserBindingRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, RbacService_DeleteUserBinding_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rbacServiceClient) ListUserBindings(ctx context.Context, in *ListUserBindingsOptions, opts ...grpc.CallOption) (*UserBindings, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UserBindings)
	err := c.cc.Invoke(ctx, RbacService_ListUserBindings_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rbacServiceClient) ChangeUserPlatformRole(ctx context.Context, in *ChangeUserPlatformRoleRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, RbacService_ChangeUserPlatformRole_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// RbacServiceServer is the server API for RbacService service.
// All implementations must embed UnimplementedRbacServiceServer
// for forward compatibility
type RbacServiceServer interface {
	CreateOrUpdateWorkspaceRole(context.Context, *CreateOrUpdateWorkspaceRoleRequest) (*WorkspaceRole, error)
	DeleteWorkspaceRole(context.Context, *DeleteWorkspaceRoleRequest) (*emptypb.Empty, error)
	ListWorkspaceRoles(context.Context, *ListWorkspaceRolesOptions) (*WorkspaceRoles, error)
	CreateOrUpdateWorkspaceRoleBinding(context.Context, *CreateOrUpdateWorkspaceRoleBindingRequest) (*WorkspaceRoleBinding, error)
	DeleteWorkspaceRoleBinding(context.Context, *DeleteWorkspaceRoleBindingRequest) (*emptypb.Empty, error)
	ListWorkspaceRoleBindings(context.Context, *ListWorkspaceRoleBindingsOptions) (*WorkspaceRoleBindings, error)
	CreateOrUpdateWorkspacePermission(context.Context, *CreateOrUpdateWorkspacePermissionRequest) (*WorkspacePermission, error)
	DeleteWorkspacePermission(context.Context, *DeleteWorkspacePermissionRequest) (*emptypb.Empty, error)
	ListWorkspacePermissions(context.Context, *ListWorkspacePermissionsOptions) (*WorkspacePermissions, error)
	CheckPermission(context.Context, *CheckPermissionRequest) (*CheckPermissionResponse, error)
	CreateOrUpdateUserBinding(context.Context, *CreateOrUpdateUserBindingRequest) (*UserBinding, error)
	DeleteUserBinding(context.Context, *DeleteUserBindingRequest) (*emptypb.Empty, error)
	ListUserBindings(context.Context, *ListUserBindingsOptions) (*UserBindings, error)
	ChangeUserPlatformRole(context.Context, *ChangeUserPlatformRoleRequest) (*emptypb.Empty, error)
	mustEmbedUnimplementedRbacServiceServer()
}

// UnimplementedRbacServiceServer must be embedded to have forward compatible implementations.
type UnimplementedRbacServiceServer struct {
}

func (UnimplementedRbacServiceServer) CreateOrUpdateWorkspaceRole(context.Context, *CreateOrUpdateWorkspaceRoleRequest) (*WorkspaceRole, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateOrUpdateWorkspaceRole not implemented")
}
func (UnimplementedRbacServiceServer) DeleteWorkspaceRole(context.Context, *DeleteWorkspaceRoleRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteWorkspaceRole not implemented")
}
func (UnimplementedRbacServiceServer) ListWorkspaceRoles(context.Context, *ListWorkspaceRolesOptions) (*WorkspaceRoles, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListWorkspaceRoles not implemented")
}
func (UnimplementedRbacServiceServer) CreateOrUpdateWorkspaceRoleBinding(context.Context, *CreateOrUpdateWorkspaceRoleBindingRequest) (*WorkspaceRoleBinding, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateOrUpdateWorkspaceRoleBinding not implemented")
}
func (UnimplementedRbacServiceServer) DeleteWorkspaceRoleBinding(context.Context, *DeleteWorkspaceRoleBindingRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteWorkspaceRoleBinding not implemented")
}
func (UnimplementedRbacServiceServer) ListWorkspaceRoleBindings(context.Context, *ListWorkspaceRoleBindingsOptions) (*WorkspaceRoleBindings, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListWorkspaceRoleBindings not implemented")
}
func (UnimplementedRbacServiceServer) CreateOrUpdateWorkspacePermission(context.Context, *CreateOrUpdateWorkspacePermissionRequest) (*WorkspacePermission, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateOrUpdateWorkspacePermission not implemented")
}
func (UnimplementedRbacServiceServer) DeleteWorkspacePermission(context.Context, *DeleteWorkspacePermissionRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteWorkspacePermission not implemented")
}
func (UnimplementedRbacServiceServer) ListWorkspacePermissions(context.Context, *ListWorkspacePermissionsOptions) (*WorkspacePermissions, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListWorkspacePermissions not implemented")
}
func (UnimplementedRbacServiceServer) CheckPermission(context.Context, *CheckPermissionRequest) (*CheckPermissionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckPermission not implemented")
}
func (UnimplementedRbacServiceServer) CreateOrUpdateUserBinding(context.Context, *CreateOrUpdateUserBindingRequest) (*UserBinding, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateOrUpdateUserBinding not implemented")
}
func (UnimplementedRbacServiceServer) DeleteUserBinding(context.Context, *DeleteUserBindingRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteUserBinding not implemented")
}
func (UnimplementedRbacServiceServer) ListUserBindings(context.Context, *ListUserBindingsOptions) (*UserBindings, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListUserBindings not implemented")
}
func (UnimplementedRbacServiceServer) ChangeUserPlatformRole(context.Context, *ChangeUserPlatformRoleRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ChangeUserPlatformRole not implemented")
}
func (UnimplementedRbacServiceServer) mustEmbedUnimplementedRbacServiceServer() {}

// UnsafeRbacServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to RbacServiceServer will
// result in compilation errors.
type UnsafeRbacServiceServer interface {
	mustEmbedUnimplementedRbacServiceServer()
}

func RegisterRbacServiceServer(s grpc.ServiceRegistrar, srv RbacServiceServer) {
	s.RegisterService(&RbacService_ServiceDesc, srv)
}

func _RbacService_CreateOrUpdateWorkspaceRole_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateOrUpdateWorkspaceRoleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RbacServiceServer).CreateOrUpdateWorkspaceRole(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RbacService_CreateOrUpdateWorkspaceRole_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RbacServiceServer).CreateOrUpdateWorkspaceRole(ctx, req.(*CreateOrUpdateWorkspaceRoleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RbacService_DeleteWorkspaceRole_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteWorkspaceRoleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RbacServiceServer).DeleteWorkspaceRole(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RbacService_DeleteWorkspaceRole_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RbacServiceServer).DeleteWorkspaceRole(ctx, req.(*DeleteWorkspaceRoleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RbacService_ListWorkspaceRoles_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListWorkspaceRolesOptions)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RbacServiceServer).ListWorkspaceRoles(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RbacService_ListWorkspaceRoles_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RbacServiceServer).ListWorkspaceRoles(ctx, req.(*ListWorkspaceRolesOptions))
	}
	return interceptor(ctx, in, info, handler)
}

func _RbacService_CreateOrUpdateWorkspaceRoleBinding_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateOrUpdateWorkspaceRoleBindingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RbacServiceServer).CreateOrUpdateWorkspaceRoleBinding(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RbacService_CreateOrUpdateWorkspaceRoleBinding_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RbacServiceServer).CreateOrUpdateWorkspaceRoleBinding(ctx, req.(*CreateOrUpdateWorkspaceRoleBindingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RbacService_DeleteWorkspaceRoleBinding_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteWorkspaceRoleBindingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RbacServiceServer).DeleteWorkspaceRoleBinding(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RbacService_DeleteWorkspaceRoleBinding_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RbacServiceServer).DeleteWorkspaceRoleBinding(ctx, req.(*DeleteWorkspaceRoleBindingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RbacService_ListWorkspaceRoleBindings_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListWorkspaceRoleBindingsOptions)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RbacServiceServer).ListWorkspaceRoleBindings(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RbacService_ListWorkspaceRoleBindings_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RbacServiceServer).ListWorkspaceRoleBindings(ctx, req.(*ListWorkspaceRoleBindingsOptions))
	}
	return interceptor(ctx, in, info, handler)
}

func _RbacService_CreateOrUpdateWorkspacePermission_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateOrUpdateWorkspacePermissionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RbacServiceServer).CreateOrUpdateWorkspacePermission(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RbacService_CreateOrUpdateWorkspacePermission_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RbacServiceServer).CreateOrUpdateWorkspacePermission(ctx, req.(*CreateOrUpdateWorkspacePermissionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RbacService_DeleteWorkspacePermission_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteWorkspacePermissionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RbacServiceServer).DeleteWorkspacePermission(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RbacService_DeleteWorkspacePermission_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RbacServiceServer).DeleteWorkspacePermission(ctx, req.(*DeleteWorkspacePermissionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RbacService_ListWorkspacePermissions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListWorkspacePermissionsOptions)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RbacServiceServer).ListWorkspacePermissions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RbacService_ListWorkspacePermissions_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RbacServiceServer).ListWorkspacePermissions(ctx, req.(*ListWorkspacePermissionsOptions))
	}
	return interceptor(ctx, in, info, handler)
}

func _RbacService_CheckPermission_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckPermissionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RbacServiceServer).CheckPermission(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RbacService_CheckPermission_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RbacServiceServer).CheckPermission(ctx, req.(*CheckPermissionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RbacService_CreateOrUpdateUserBinding_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateOrUpdateUserBindingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RbacServiceServer).CreateOrUpdateUserBinding(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RbacService_CreateOrUpdateUserBinding_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RbacServiceServer).CreateOrUpdateUserBinding(ctx, req.(*CreateOrUpdateUserBindingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RbacService_DeleteUserBinding_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteUserBindingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RbacServiceServer).DeleteUserBinding(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RbacService_DeleteUserBinding_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RbacServiceServer).DeleteUserBinding(ctx, req.(*DeleteUserBindingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RbacService_ListUserBindings_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListUserBindingsOptions)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RbacServiceServer).ListUserBindings(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RbacService_ListUserBindings_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RbacServiceServer).ListUserBindings(ctx, req.(*ListUserBindingsOptions))
	}
	return interceptor(ctx, in, info, handler)
}

func _RbacService_ChangeUserPlatformRole_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChangeUserPlatformRoleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RbacServiceServer).ChangeUserPlatformRole(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RbacService_ChangeUserPlatformRole_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RbacServiceServer).ChangeUserPlatformRole(ctx, req.(*ChangeUserPlatformRoleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// RbacService_ServiceDesc is the grpc.ServiceDesc for RbacService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var RbacService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "apis.aistudio.rbac.v1.RbacService",
	HandlerType: (*RbacServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateOrUpdateWorkspaceRole",
			Handler:    _RbacService_CreateOrUpdateWorkspaceRole_Handler,
		},
		{
			MethodName: "DeleteWorkspaceRole",
			Handler:    _RbacService_DeleteWorkspaceRole_Handler,
		},
		{
			MethodName: "ListWorkspaceRoles",
			Handler:    _RbacService_ListWorkspaceRoles_Handler,
		},
		{
			MethodName: "CreateOrUpdateWorkspaceRoleBinding",
			Handler:    _RbacService_CreateOrUpdateWorkspaceRoleBinding_Handler,
		},
		{
			MethodName: "DeleteWorkspaceRoleBinding",
			Handler:    _RbacService_DeleteWorkspaceRoleBinding_Handler,
		},
		{
			MethodName: "ListWorkspaceRoleBindings",
			Handler:    _RbacService_ListWorkspaceRoleBindings_Handler,
		},
		{
			MethodName: "CreateOrUpdateWorkspacePermission",
			Handler:    _RbacService_CreateOrUpdateWorkspacePermission_Handler,
		},
		{
			MethodName: "DeleteWorkspacePermission",
			Handler:    _RbacService_DeleteWorkspacePermission_Handler,
		},
		{
			MethodName: "ListWorkspacePermissions",
			Handler:    _RbacService_ListWorkspacePermissions_Handler,
		},
		{
			MethodName: "CheckPermission",
			Handler:    _RbacService_CheckPermission_Handler,
		},
		{
			MethodName: "CreateOrUpdateUserBinding",
			Handler:    _RbacService_CreateOrUpdateUserBinding_Handler,
		},
		{
			MethodName: "DeleteUserBinding",
			Handler:    _RbacService_DeleteUserBinding_Handler,
		},
		{
			MethodName: "ListUserBindings",
			Handler:    _RbacService_ListUserBindings_Handler,
		},
		{
			MethodName: "ChangeUserPlatformRole",
			Handler:    _RbacService_ChangeUserPlatformRole_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "aistudio/rbac/v1/rbac.proto",
}
