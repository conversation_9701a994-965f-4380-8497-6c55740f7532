// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.3
// - protoc             v5.29.3
// source: aistudio/workflow/v1/workflow.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationWorkflowServiceGetTasksByTopologicalSort = "/apis.workflow.v1.WorkflowService/GetTasksByTopologicalSort"
const OperationWorkflowServiceGetWorkflowDetail = "/apis.workflow.v1.WorkflowService/GetWorkflowDetail"
const OperationWorkflowServiceSubmitWorkflow = "/apis.workflow.v1.WorkflowService/SubmitWorkflow"
const OperationWorkflowServiceWorkflowCancel = "/apis.workflow.v1.WorkflowService/WorkflowCancel"
const OperationWorkflowServiceWorkflowResume = "/apis.workflow.v1.WorkflowService/WorkflowResume"
const OperationWorkflowServiceWorkflowSuspend = "/apis.workflow.v1.WorkflowService/WorkflowSuspend"

type WorkflowServiceHTTPServer interface {
	// GetTasksByTopologicalSort根据拓扑排序获取任务
	GetTasksByTopologicalSort(context.Context, *GetTasksByTopologicalSortRequest) (*GetTasksByTopologicalSortResponse, error)
	// GetWorkflowDetailGetWorkflowDetail查询工作流的详情
	GetWorkflowDetail(context.Context, *GetWorkflowDetailRequest) (*Workflow, error)
	// SubmitWorkflow 提交工作流
	SubmitWorkflow(context.Context, *Workflow) (*Workflow, error)
	// WorkflowCancel取消工作流
	WorkflowCancel(context.Context, *WorkflowCancelRequest) (*emptypb.Empty, error)
	// WorkflowResume恢复工作流
	WorkflowResume(context.Context, *WorkflowResumeRequest) (*emptypb.Empty, error)
	// WorkflowSuspend暂停工作流
	WorkflowSuspend(context.Context, *WorkflowSuspendRequest) (*emptypb.Empty, error)
}

func RegisterWorkflowServiceHTTPServer(s *http.Server, srv WorkflowServiceHTTPServer) {
	r := s.Route("/")
	r.POST("/apis/v1/workflow", _WorkflowService_SubmitWorkflow0_HTTP_Handler(srv))
	r.POST("/apis/v1/workflow/{workflowId}/suspend", _WorkflowService_WorkflowSuspend0_HTTP_Handler(srv))
	r.POST("/apis/v1/workflow/{workflowId}/resume", _WorkflowService_WorkflowResume0_HTTP_Handler(srv))
	r.POST("/apis/v1/workflow/{workflowId}/cancel", _WorkflowService_WorkflowCancel0_HTTP_Handler(srv))
	r.GET("/apis/v1/workflow/{workflowId}", _WorkflowService_GetWorkflowDetail0_HTTP_Handler(srv))
	r.POST("/apis/v1/workflow/{workflowId}/tasks/topologicalSort", _WorkflowService_GetTasksByTopologicalSort0_HTTP_Handler(srv))
}

func _WorkflowService_SubmitWorkflow0_HTTP_Handler(srv WorkflowServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in Workflow
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationWorkflowServiceSubmitWorkflow)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SubmitWorkflow(ctx, req.(*Workflow))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Workflow)
		return ctx.Result(200, reply)
	}
}

func _WorkflowService_WorkflowSuspend0_HTTP_Handler(srv WorkflowServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in WorkflowSuspendRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationWorkflowServiceWorkflowSuspend)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.WorkflowSuspend(ctx, req.(*WorkflowSuspendRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _WorkflowService_WorkflowResume0_HTTP_Handler(srv WorkflowServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in WorkflowResumeRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationWorkflowServiceWorkflowResume)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.WorkflowResume(ctx, req.(*WorkflowResumeRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _WorkflowService_WorkflowCancel0_HTTP_Handler(srv WorkflowServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in WorkflowCancelRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationWorkflowServiceWorkflowCancel)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.WorkflowCancel(ctx, req.(*WorkflowCancelRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _WorkflowService_GetWorkflowDetail0_HTTP_Handler(srv WorkflowServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetWorkflowDetailRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationWorkflowServiceGetWorkflowDetail)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetWorkflowDetail(ctx, req.(*GetWorkflowDetailRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Workflow)
		return ctx.Result(200, reply)
	}
}

func _WorkflowService_GetTasksByTopologicalSort0_HTTP_Handler(srv WorkflowServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetTasksByTopologicalSortRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationWorkflowServiceGetTasksByTopologicalSort)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetTasksByTopologicalSort(ctx, req.(*GetTasksByTopologicalSortRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetTasksByTopologicalSortResponse)
		return ctx.Result(200, reply)
	}
}

type WorkflowServiceHTTPClient interface {
	GetTasksByTopologicalSort(ctx context.Context, req *GetTasksByTopologicalSortRequest, opts ...http.CallOption) (rsp *GetTasksByTopologicalSortResponse, err error)
	GetWorkflowDetail(ctx context.Context, req *GetWorkflowDetailRequest, opts ...http.CallOption) (rsp *Workflow, err error)
	SubmitWorkflow(ctx context.Context, req *Workflow, opts ...http.CallOption) (rsp *Workflow, err error)
	WorkflowCancel(ctx context.Context, req *WorkflowCancelRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	WorkflowResume(ctx context.Context, req *WorkflowResumeRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	WorkflowSuspend(ctx context.Context, req *WorkflowSuspendRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
}

type WorkflowServiceHTTPClientImpl struct {
	cc *http.Client
}

func NewWorkflowServiceHTTPClient(client *http.Client) WorkflowServiceHTTPClient {
	return &WorkflowServiceHTTPClientImpl{client}
}

func (c *WorkflowServiceHTTPClientImpl) GetTasksByTopologicalSort(ctx context.Context, in *GetTasksByTopologicalSortRequest, opts ...http.CallOption) (*GetTasksByTopologicalSortResponse, error) {
	var out GetTasksByTopologicalSortResponse
	pattern := "/apis/v1/workflow/{workflowId}/tasks/topologicalSort"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationWorkflowServiceGetTasksByTopologicalSort))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *WorkflowServiceHTTPClientImpl) GetWorkflowDetail(ctx context.Context, in *GetWorkflowDetailRequest, opts ...http.CallOption) (*Workflow, error) {
	var out Workflow
	pattern := "/apis/v1/workflow/{workflowId}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationWorkflowServiceGetWorkflowDetail))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *WorkflowServiceHTTPClientImpl) SubmitWorkflow(ctx context.Context, in *Workflow, opts ...http.CallOption) (*Workflow, error) {
	var out Workflow
	pattern := "/apis/v1/workflow"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationWorkflowServiceSubmitWorkflow))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *WorkflowServiceHTTPClientImpl) WorkflowCancel(ctx context.Context, in *WorkflowCancelRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/apis/v1/workflow/{workflowId}/cancel"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationWorkflowServiceWorkflowCancel))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *WorkflowServiceHTTPClientImpl) WorkflowResume(ctx context.Context, in *WorkflowResumeRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/apis/v1/workflow/{workflowId}/resume"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationWorkflowServiceWorkflowResume))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *WorkflowServiceHTTPClientImpl) WorkflowSuspend(ctx context.Context, in *WorkflowSuspendRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/apis/v1/workflow/{workflowId}/suspend"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationWorkflowServiceWorkflowSuspend))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
