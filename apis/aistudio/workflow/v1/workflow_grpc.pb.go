// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v5.29.3
// source: aistudio/workflow/v1/workflow.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	WorkflowService_SubmitWorkflow_FullMethodName            = "/apis.workflow.v1.WorkflowService/SubmitWorkflow"
	WorkflowService_WorkflowSuspend_FullMethodName           = "/apis.workflow.v1.WorkflowService/WorkflowSuspend"
	WorkflowService_WorkflowResume_FullMethodName            = "/apis.workflow.v1.WorkflowService/WorkflowResume"
	WorkflowService_WorkflowCancel_FullMethodName            = "/apis.workflow.v1.WorkflowService/WorkflowCancel"
	WorkflowService_GetWorkflowDetail_FullMethodName         = "/apis.workflow.v1.WorkflowService/GetWorkflowDetail"
	WorkflowService_GetTasksByTopologicalSort_FullMethodName = "/apis.workflow.v1.WorkflowService/GetTasksByTopologicalSort"
)

// WorkflowServiceClient is the client API for WorkflowService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type WorkflowServiceClient interface {
	// 提交工作流
	SubmitWorkflow(ctx context.Context, in *Workflow, opts ...grpc.CallOption) (*Workflow, error)
	// 暂停工作流
	WorkflowSuspend(ctx context.Context, in *WorkflowSuspendRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 恢复工作流
	WorkflowResume(ctx context.Context, in *WorkflowResumeRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 取消工作流
	WorkflowCancel(ctx context.Context, in *WorkflowCancelRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// GetWorkflowDetail查询工作流的详情
	GetWorkflowDetail(ctx context.Context, in *GetWorkflowDetailRequest, opts ...grpc.CallOption) (*Workflow, error)
	// 根据拓扑排序获取任务
	GetTasksByTopologicalSort(ctx context.Context, in *GetTasksByTopologicalSortRequest, opts ...grpc.CallOption) (*GetTasksByTopologicalSortResponse, error)
}

type workflowServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewWorkflowServiceClient(cc grpc.ClientConnInterface) WorkflowServiceClient {
	return &workflowServiceClient{cc}
}

func (c *workflowServiceClient) SubmitWorkflow(ctx context.Context, in *Workflow, opts ...grpc.CallOption) (*Workflow, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(Workflow)
	err := c.cc.Invoke(ctx, WorkflowService_SubmitWorkflow_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *workflowServiceClient) WorkflowSuspend(ctx context.Context, in *WorkflowSuspendRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, WorkflowService_WorkflowSuspend_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *workflowServiceClient) WorkflowResume(ctx context.Context, in *WorkflowResumeRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, WorkflowService_WorkflowResume_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *workflowServiceClient) WorkflowCancel(ctx context.Context, in *WorkflowCancelRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, WorkflowService_WorkflowCancel_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *workflowServiceClient) GetWorkflowDetail(ctx context.Context, in *GetWorkflowDetailRequest, opts ...grpc.CallOption) (*Workflow, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(Workflow)
	err := c.cc.Invoke(ctx, WorkflowService_GetWorkflowDetail_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *workflowServiceClient) GetTasksByTopologicalSort(ctx context.Context, in *GetTasksByTopologicalSortRequest, opts ...grpc.CallOption) (*GetTasksByTopologicalSortResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetTasksByTopologicalSortResponse)
	err := c.cc.Invoke(ctx, WorkflowService_GetTasksByTopologicalSort_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// WorkflowServiceServer is the server API for WorkflowService service.
// All implementations must embed UnimplementedWorkflowServiceServer
// for forward compatibility
type WorkflowServiceServer interface {
	// 提交工作流
	SubmitWorkflow(context.Context, *Workflow) (*Workflow, error)
	// 暂停工作流
	WorkflowSuspend(context.Context, *WorkflowSuspendRequest) (*emptypb.Empty, error)
	// 恢复工作流
	WorkflowResume(context.Context, *WorkflowResumeRequest) (*emptypb.Empty, error)
	// 取消工作流
	WorkflowCancel(context.Context, *WorkflowCancelRequest) (*emptypb.Empty, error)
	// GetWorkflowDetail查询工作流的详情
	GetWorkflowDetail(context.Context, *GetWorkflowDetailRequest) (*Workflow, error)
	// 根据拓扑排序获取任务
	GetTasksByTopologicalSort(context.Context, *GetTasksByTopologicalSortRequest) (*GetTasksByTopologicalSortResponse, error)
	mustEmbedUnimplementedWorkflowServiceServer()
}

// UnimplementedWorkflowServiceServer must be embedded to have forward compatible implementations.
type UnimplementedWorkflowServiceServer struct {
}

func (UnimplementedWorkflowServiceServer) SubmitWorkflow(context.Context, *Workflow) (*Workflow, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SubmitWorkflow not implemented")
}
func (UnimplementedWorkflowServiceServer) WorkflowSuspend(context.Context, *WorkflowSuspendRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WorkflowSuspend not implemented")
}
func (UnimplementedWorkflowServiceServer) WorkflowResume(context.Context, *WorkflowResumeRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WorkflowResume not implemented")
}
func (UnimplementedWorkflowServiceServer) WorkflowCancel(context.Context, *WorkflowCancelRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WorkflowCancel not implemented")
}
func (UnimplementedWorkflowServiceServer) GetWorkflowDetail(context.Context, *GetWorkflowDetailRequest) (*Workflow, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetWorkflowDetail not implemented")
}
func (UnimplementedWorkflowServiceServer) GetTasksByTopologicalSort(context.Context, *GetTasksByTopologicalSortRequest) (*GetTasksByTopologicalSortResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTasksByTopologicalSort not implemented")
}
func (UnimplementedWorkflowServiceServer) mustEmbedUnimplementedWorkflowServiceServer() {}

// UnsafeWorkflowServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to WorkflowServiceServer will
// result in compilation errors.
type UnsafeWorkflowServiceServer interface {
	mustEmbedUnimplementedWorkflowServiceServer()
}

func RegisterWorkflowServiceServer(s grpc.ServiceRegistrar, srv WorkflowServiceServer) {
	s.RegisterService(&WorkflowService_ServiceDesc, srv)
}

func _WorkflowService_SubmitWorkflow_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Workflow)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WorkflowServiceServer).SubmitWorkflow(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WorkflowService_SubmitWorkflow_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WorkflowServiceServer).SubmitWorkflow(ctx, req.(*Workflow))
	}
	return interceptor(ctx, in, info, handler)
}

func _WorkflowService_WorkflowSuspend_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WorkflowSuspendRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WorkflowServiceServer).WorkflowSuspend(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WorkflowService_WorkflowSuspend_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WorkflowServiceServer).WorkflowSuspend(ctx, req.(*WorkflowSuspendRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WorkflowService_WorkflowResume_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WorkflowResumeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WorkflowServiceServer).WorkflowResume(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WorkflowService_WorkflowResume_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WorkflowServiceServer).WorkflowResume(ctx, req.(*WorkflowResumeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WorkflowService_WorkflowCancel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WorkflowCancelRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WorkflowServiceServer).WorkflowCancel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WorkflowService_WorkflowCancel_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WorkflowServiceServer).WorkflowCancel(ctx, req.(*WorkflowCancelRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WorkflowService_GetWorkflowDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetWorkflowDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WorkflowServiceServer).GetWorkflowDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WorkflowService_GetWorkflowDetail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WorkflowServiceServer).GetWorkflowDetail(ctx, req.(*GetWorkflowDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WorkflowService_GetTasksByTopologicalSort_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTasksByTopologicalSortRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WorkflowServiceServer).GetTasksByTopologicalSort(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WorkflowService_GetTasksByTopologicalSort_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WorkflowServiceServer).GetTasksByTopologicalSort(ctx, req.(*GetTasksByTopologicalSortRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// WorkflowService_ServiceDesc is the grpc.ServiceDesc for WorkflowService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var WorkflowService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "apis.workflow.v1.WorkflowService",
	HandlerType: (*WorkflowServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SubmitWorkflow",
			Handler:    _WorkflowService_SubmitWorkflow_Handler,
		},
		{
			MethodName: "WorkflowSuspend",
			Handler:    _WorkflowService_WorkflowSuspend_Handler,
		},
		{
			MethodName: "WorkflowResume",
			Handler:    _WorkflowService_WorkflowResume_Handler,
		},
		{
			MethodName: "WorkflowCancel",
			Handler:    _WorkflowService_WorkflowCancel_Handler,
		},
		{
			MethodName: "GetWorkflowDetail",
			Handler:    _WorkflowService_GetWorkflowDetail_Handler,
		},
		{
			MethodName: "GetTasksByTopologicalSort",
			Handler:    _WorkflowService_GetTasksByTopologicalSort_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "aistudio/workflow/v1/workflow.proto",
}

const (
	WorkflowBrokerService_SubmitWorkflow_FullMethodName    = "/apis.workflow.v1.WorkflowBrokerService/SubmitWorkflow"
	WorkflowBrokerService_Exchange_FullMethodName          = "/apis.workflow.v1.WorkflowBrokerService/Exchange"
	WorkflowBrokerService_GetWorkflowDetail_FullMethodName = "/apis.workflow.v1.WorkflowBrokerService/GetWorkflowDetail"
	WorkflowBrokerService_GetTaskStatus_FullMethodName     = "/apis.workflow.v1.WorkflowBrokerService/GetTaskStatus"
)

// WorkflowBrokerServiceClient is the client API for WorkflowBrokerService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// 工作流做了两层拆分,Broker作为内部调用的服务接口,WorkflowService作为外部的调用接口
type WorkflowBrokerServiceClient interface {
	// 提交工作流，提交工作流后只是对工作流做了初始化，不会真正的开始运行工作流
	SubmitWorkflow(ctx context.Context, in *Workflow, opts ...grpc.CallOption) (*Workflow, error)
	// 工作流启动
	Exchange(ctx context.Context, opts ...grpc.CallOption) (WorkflowBrokerService_ExchangeClient, error)
	// GetWorkflowDetail查询工作流的详情
	GetWorkflowDetail(ctx context.Context, in *GetWorkflowDetailRequest, opts ...grpc.CallOption) (*Workflow, error)
	// 查询任务的状态
	GetTaskStatus(ctx context.Context, in *GetTaskStatusRequest, opts ...grpc.CallOption) (*TaskStatus, error)
}

type workflowBrokerServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewWorkflowBrokerServiceClient(cc grpc.ClientConnInterface) WorkflowBrokerServiceClient {
	return &workflowBrokerServiceClient{cc}
}

func (c *workflowBrokerServiceClient) SubmitWorkflow(ctx context.Context, in *Workflow, opts ...grpc.CallOption) (*Workflow, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(Workflow)
	err := c.cc.Invoke(ctx, WorkflowBrokerService_SubmitWorkflow_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *workflowBrokerServiceClient) Exchange(ctx context.Context, opts ...grpc.CallOption) (WorkflowBrokerService_ExchangeClient, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	stream, err := c.cc.NewStream(ctx, &WorkflowBrokerService_ServiceDesc.Streams[0], WorkflowBrokerService_Exchange_FullMethodName, cOpts...)
	if err != nil {
		return nil, err
	}
	x := &workflowBrokerServiceExchangeClient{ClientStream: stream}
	return x, nil
}

type WorkflowBrokerService_ExchangeClient interface {
	Send(*ExchangeRequest) error
	Recv() (*WorkflowRunEvent, error)
	grpc.ClientStream
}

type workflowBrokerServiceExchangeClient struct {
	grpc.ClientStream
}

func (x *workflowBrokerServiceExchangeClient) Send(m *ExchangeRequest) error {
	return x.ClientStream.SendMsg(m)
}

func (x *workflowBrokerServiceExchangeClient) Recv() (*WorkflowRunEvent, error) {
	m := new(WorkflowRunEvent)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *workflowBrokerServiceClient) GetWorkflowDetail(ctx context.Context, in *GetWorkflowDetailRequest, opts ...grpc.CallOption) (*Workflow, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(Workflow)
	err := c.cc.Invoke(ctx, WorkflowBrokerService_GetWorkflowDetail_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *workflowBrokerServiceClient) GetTaskStatus(ctx context.Context, in *GetTaskStatusRequest, opts ...grpc.CallOption) (*TaskStatus, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TaskStatus)
	err := c.cc.Invoke(ctx, WorkflowBrokerService_GetTaskStatus_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// WorkflowBrokerServiceServer is the server API for WorkflowBrokerService service.
// All implementations must embed UnimplementedWorkflowBrokerServiceServer
// for forward compatibility
//
// 工作流做了两层拆分,Broker作为内部调用的服务接口,WorkflowService作为外部的调用接口
type WorkflowBrokerServiceServer interface {
	// 提交工作流，提交工作流后只是对工作流做了初始化，不会真正的开始运行工作流
	SubmitWorkflow(context.Context, *Workflow) (*Workflow, error)
	// 工作流启动
	Exchange(WorkflowBrokerService_ExchangeServer) error
	// GetWorkflowDetail查询工作流的详情
	GetWorkflowDetail(context.Context, *GetWorkflowDetailRequest) (*Workflow, error)
	// 查询任务的状态
	GetTaskStatus(context.Context, *GetTaskStatusRequest) (*TaskStatus, error)
	mustEmbedUnimplementedWorkflowBrokerServiceServer()
}

// UnimplementedWorkflowBrokerServiceServer must be embedded to have forward compatible implementations.
type UnimplementedWorkflowBrokerServiceServer struct {
}

func (UnimplementedWorkflowBrokerServiceServer) SubmitWorkflow(context.Context, *Workflow) (*Workflow, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SubmitWorkflow not implemented")
}
func (UnimplementedWorkflowBrokerServiceServer) Exchange(WorkflowBrokerService_ExchangeServer) error {
	return status.Errorf(codes.Unimplemented, "method Exchange not implemented")
}
func (UnimplementedWorkflowBrokerServiceServer) GetWorkflowDetail(context.Context, *GetWorkflowDetailRequest) (*Workflow, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetWorkflowDetail not implemented")
}
func (UnimplementedWorkflowBrokerServiceServer) GetTaskStatus(context.Context, *GetTaskStatusRequest) (*TaskStatus, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTaskStatus not implemented")
}
func (UnimplementedWorkflowBrokerServiceServer) mustEmbedUnimplementedWorkflowBrokerServiceServer() {}

// UnsafeWorkflowBrokerServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to WorkflowBrokerServiceServer will
// result in compilation errors.
type UnsafeWorkflowBrokerServiceServer interface {
	mustEmbedUnimplementedWorkflowBrokerServiceServer()
}

func RegisterWorkflowBrokerServiceServer(s grpc.ServiceRegistrar, srv WorkflowBrokerServiceServer) {
	s.RegisterService(&WorkflowBrokerService_ServiceDesc, srv)
}

func _WorkflowBrokerService_SubmitWorkflow_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Workflow)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WorkflowBrokerServiceServer).SubmitWorkflow(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WorkflowBrokerService_SubmitWorkflow_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WorkflowBrokerServiceServer).SubmitWorkflow(ctx, req.(*Workflow))
	}
	return interceptor(ctx, in, info, handler)
}

func _WorkflowBrokerService_Exchange_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(WorkflowBrokerServiceServer).Exchange(&workflowBrokerServiceExchangeServer{ServerStream: stream})
}

type WorkflowBrokerService_ExchangeServer interface {
	Send(*WorkflowRunEvent) error
	Recv() (*ExchangeRequest, error)
	grpc.ServerStream
}

type workflowBrokerServiceExchangeServer struct {
	grpc.ServerStream
}

func (x *workflowBrokerServiceExchangeServer) Send(m *WorkflowRunEvent) error {
	return x.ServerStream.SendMsg(m)
}

func (x *workflowBrokerServiceExchangeServer) Recv() (*ExchangeRequest, error) {
	m := new(ExchangeRequest)
	if err := x.ServerStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func _WorkflowBrokerService_GetWorkflowDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetWorkflowDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WorkflowBrokerServiceServer).GetWorkflowDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WorkflowBrokerService_GetWorkflowDetail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WorkflowBrokerServiceServer).GetWorkflowDetail(ctx, req.(*GetWorkflowDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WorkflowBrokerService_GetTaskStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTaskStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WorkflowBrokerServiceServer).GetTaskStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WorkflowBrokerService_GetTaskStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WorkflowBrokerServiceServer).GetTaskStatus(ctx, req.(*GetTaskStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// WorkflowBrokerService_ServiceDesc is the grpc.ServiceDesc for WorkflowBrokerService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var WorkflowBrokerService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "apis.workflow.v1.WorkflowBrokerService",
	HandlerType: (*WorkflowBrokerServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SubmitWorkflow",
			Handler:    _WorkflowBrokerService_SubmitWorkflow_Handler,
		},
		{
			MethodName: "GetWorkflowDetail",
			Handler:    _WorkflowBrokerService_GetWorkflowDetail_Handler,
		},
		{
			MethodName: "GetTaskStatus",
			Handler:    _WorkflowBrokerService_GetTaskStatus_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "Exchange",
			Handler:       _WorkflowBrokerService_Exchange_Handler,
			ServerStreams: true,
			ClientStreams: true,
		},
	},
	Metadata: "aistudio/workflow/v1/workflow.proto",
}
