// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v5.29.3
// source: aistudio/workflow/v1/workflow.proto

package v1

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	durationpb "google.golang.org/protobuf/types/known/durationpb"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type RetryPolicy int32

const (
	RetryPolicy_Always           RetryPolicy = 0
	RetryPolicy_OnFailure        RetryPolicy = 1
	RetryPolicy_OnError          RetryPolicy = 2
	RetryPolicy_OnTransientError RetryPolicy = 3
)

// Enum value maps for RetryPolicy.
var (
	RetryPolicy_name = map[int32]string{
		0: "Always",
		1: "OnFailure",
		2: "OnError",
		3: "OnTransientError",
	}
	RetryPolicy_value = map[string]int32{
		"Always":           0,
		"OnFailure":        1,
		"OnError":          2,
		"OnTransientError": 3,
	}
)

func (x RetryPolicy) Enum() *RetryPolicy {
	p := new(RetryPolicy)
	*p = x
	return p
}

func (x RetryPolicy) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RetryPolicy) Descriptor() protoreflect.EnumDescriptor {
	return file_aistudio_workflow_v1_workflow_proto_enumTypes[0].Descriptor()
}

func (RetryPolicy) Type() protoreflect.EnumType {
	return &file_aistudio_workflow_v1_workflow_proto_enumTypes[0]
}

func (x RetryPolicy) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RetryPolicy.Descriptor instead.
func (RetryPolicy) EnumDescriptor() ([]byte, []int) {
	return file_aistudio_workflow_v1_workflow_proto_rawDescGZIP(), []int{0}
}

type Workflow_WorkflowState int32

const (
	Workflow_Unknown Workflow_WorkflowState = 0
	Workflow_Pending Workflow_WorkflowState = 1
	Workflow_Running Workflow_WorkflowState = 2
	Workflow_Success Workflow_WorkflowState = 3
	Workflow_Failure Workflow_WorkflowState = 4
	Workflow_Error   Workflow_WorkflowState = 5
)

// Enum value maps for Workflow_WorkflowState.
var (
	Workflow_WorkflowState_name = map[int32]string{
		0: "Unknown",
		1: "Pending",
		2: "Running",
		3: "Success",
		4: "Failure",
		5: "Error",
	}
	Workflow_WorkflowState_value = map[string]int32{
		"Unknown": 0,
		"Pending": 1,
		"Running": 2,
		"Success": 3,
		"Failure": 4,
		"Error":   5,
	}
)

func (x Workflow_WorkflowState) Enum() *Workflow_WorkflowState {
	p := new(Workflow_WorkflowState)
	*p = x
	return p
}

func (x Workflow_WorkflowState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Workflow_WorkflowState) Descriptor() protoreflect.EnumDescriptor {
	return file_aistudio_workflow_v1_workflow_proto_enumTypes[1].Descriptor()
}

func (Workflow_WorkflowState) Type() protoreflect.EnumType {
	return &file_aistudio_workflow_v1_workflow_proto_enumTypes[1]
}

func (x Workflow_WorkflowState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Workflow_WorkflowState.Descriptor instead.
func (Workflow_WorkflowState) EnumDescriptor() ([]byte, []int) {
	return file_aistudio_workflow_v1_workflow_proto_rawDescGZIP(), []int{14, 0}
}

type TaskStatus_TaskType int32

const (
	TaskStatus_Step      TaskStatus_TaskType = 0
	TaskStatus_StepGroup TaskStatus_TaskType = 1
)

// Enum value maps for TaskStatus_TaskType.
var (
	TaskStatus_TaskType_name = map[int32]string{
		0: "Step",
		1: "StepGroup",
	}
	TaskStatus_TaskType_value = map[string]int32{
		"Step":      0,
		"StepGroup": 1,
	}
)

func (x TaskStatus_TaskType) Enum() *TaskStatus_TaskType {
	p := new(TaskStatus_TaskType)
	*p = x
	return p
}

func (x TaskStatus_TaskType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TaskStatus_TaskType) Descriptor() protoreflect.EnumDescriptor {
	return file_aistudio_workflow_v1_workflow_proto_enumTypes[2].Descriptor()
}

func (TaskStatus_TaskType) Type() protoreflect.EnumType {
	return &file_aistudio_workflow_v1_workflow_proto_enumTypes[2]
}

func (x TaskStatus_TaskType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TaskStatus_TaskType.Descriptor instead.
func (TaskStatus_TaskType) EnumDescriptor() ([]byte, []int) {
	return file_aistudio_workflow_v1_workflow_proto_rawDescGZIP(), []int{21, 0}
}

type TaskStatus_TaskState int32

const (
	TaskStatus_Pending TaskStatus_TaskState = 0
	TaskStatus_Running TaskStatus_TaskState = 1
	TaskStatus_Success TaskStatus_TaskState = 2
	TaskStatus_Failure TaskStatus_TaskState = 3
	TaskStatus_Error   TaskStatus_TaskState = 4
	TaskStatus_Skip    TaskStatus_TaskState = 5
)

// Enum value maps for TaskStatus_TaskState.
var (
	TaskStatus_TaskState_name = map[int32]string{
		0: "Pending",
		1: "Running",
		2: "Success",
		3: "Failure",
		4: "Error",
		5: "Skip",
	}
	TaskStatus_TaskState_value = map[string]int32{
		"Pending": 0,
		"Running": 1,
		"Success": 2,
		"Failure": 3,
		"Error":   4,
		"Skip":    5,
	}
)

func (x TaskStatus_TaskState) Enum() *TaskStatus_TaskState {
	p := new(TaskStatus_TaskState)
	*p = x
	return p
}

func (x TaskStatus_TaskState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TaskStatus_TaskState) Descriptor() protoreflect.EnumDescriptor {
	return file_aistudio_workflow_v1_workflow_proto_enumTypes[3].Descriptor()
}

func (TaskStatus_TaskState) Type() protoreflect.EnumType {
	return &file_aistudio_workflow_v1_workflow_proto_enumTypes[3]
}

func (x TaskStatus_TaskState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TaskStatus_TaskState.Descriptor instead.
func (TaskStatus_TaskState) EnumDescriptor() ([]byte, []int) {
	return file_aistudio_workflow_v1_workflow_proto_rawDescGZIP(), []int{21, 1}
}

type WorkflowRunEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkflowNamespace string `protobuf:"bytes,1,opt,name=workflowNamespace,proto3" json:"workflowNamespace,omitempty"`
	WorkflowName      string `protobuf:"bytes,2,opt,name=workflowName,proto3" json:"workflowName,omitempty"`
	WorkflowId        string `protobuf:"bytes,3,opt,name=workflowId,proto3" json:"workflowId,omitempty"`
	// Types that are assignable to PlayLoad:
	//
	//	*WorkflowRunEvent_WorkflowEvent
	//	*WorkflowRunEvent_TaskEvent
	//	*WorkflowRunEvent_ExecutableEvent
	PlayLoad isWorkflowRunEvent_PlayLoad `protobuf_oneof:"playLoad"`
}

func (x *WorkflowRunEvent) Reset() {
	*x = WorkflowRunEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_workflow_v1_workflow_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WorkflowRunEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WorkflowRunEvent) ProtoMessage() {}

func (x *WorkflowRunEvent) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_workflow_v1_workflow_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WorkflowRunEvent.ProtoReflect.Descriptor instead.
func (*WorkflowRunEvent) Descriptor() ([]byte, []int) {
	return file_aistudio_workflow_v1_workflow_proto_rawDescGZIP(), []int{0}
}

func (x *WorkflowRunEvent) GetWorkflowNamespace() string {
	if x != nil {
		return x.WorkflowNamespace
	}
	return ""
}

func (x *WorkflowRunEvent) GetWorkflowName() string {
	if x != nil {
		return x.WorkflowName
	}
	return ""
}

func (x *WorkflowRunEvent) GetWorkflowId() string {
	if x != nil {
		return x.WorkflowId
	}
	return ""
}

func (m *WorkflowRunEvent) GetPlayLoad() isWorkflowRunEvent_PlayLoad {
	if m != nil {
		return m.PlayLoad
	}
	return nil
}

func (x *WorkflowRunEvent) GetWorkflowEvent() *WorkflowEvent {
	if x, ok := x.GetPlayLoad().(*WorkflowRunEvent_WorkflowEvent); ok {
		return x.WorkflowEvent
	}
	return nil
}

func (x *WorkflowRunEvent) GetTaskEvent() *TaskEvent {
	if x, ok := x.GetPlayLoad().(*WorkflowRunEvent_TaskEvent); ok {
		return x.TaskEvent
	}
	return nil
}

func (x *WorkflowRunEvent) GetExecutableEvent() *ExecutableEvent {
	if x, ok := x.GetPlayLoad().(*WorkflowRunEvent_ExecutableEvent); ok {
		return x.ExecutableEvent
	}
	return nil
}

type isWorkflowRunEvent_PlayLoad interface {
	isWorkflowRunEvent_PlayLoad()
}

type WorkflowRunEvent_WorkflowEvent struct {
	WorkflowEvent *WorkflowEvent `protobuf:"bytes,4,opt,name=workflowEvent,proto3,oneof"`
}

type WorkflowRunEvent_TaskEvent struct {
	TaskEvent *TaskEvent `protobuf:"bytes,5,opt,name=taskEvent,proto3,oneof"`
}

type WorkflowRunEvent_ExecutableEvent struct {
	ExecutableEvent *ExecutableEvent `protobuf:"bytes,6,opt,name=executableEvent,proto3,oneof"`
}

func (*WorkflowRunEvent_WorkflowEvent) isWorkflowRunEvent_PlayLoad() {}

func (*WorkflowRunEvent_TaskEvent) isWorkflowRunEvent_PlayLoad() {}

func (*WorkflowRunEvent_ExecutableEvent) isWorkflowRunEvent_PlayLoad() {}

type WorkflowEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkflowId    string                 `protobuf:"bytes,1,opt,name=workflowId,proto3" json:"workflowId,omitempty"`
	GlobalContext map[string]string      `protobuf:"bytes,2,rep,name=globalContext,proto3" json:"globalContext,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Timestamp     *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	Reason        string                 `protobuf:"bytes,4,opt,name=reason,proto3" json:"reason,omitempty"`
	Message       string                 `protobuf:"bytes,5,opt,name=message,proto3" json:"message,omitempty"`
	Progress      string                 `protobuf:"bytes,6,opt,name=progress,proto3" json:"progress,omitempty"`
	State         Workflow_WorkflowState `protobuf:"varint,7,opt,name=state,proto3,enum=apis.workflow.v1.Workflow_WorkflowState" json:"state,omitempty"`
	Identifier    string                 `protobuf:"bytes,8,opt,name=identifier,proto3" json:"identifier,omitempty"`
}

func (x *WorkflowEvent) Reset() {
	*x = WorkflowEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_workflow_v1_workflow_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WorkflowEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WorkflowEvent) ProtoMessage() {}

func (x *WorkflowEvent) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_workflow_v1_workflow_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WorkflowEvent.ProtoReflect.Descriptor instead.
func (*WorkflowEvent) Descriptor() ([]byte, []int) {
	return file_aistudio_workflow_v1_workflow_proto_rawDescGZIP(), []int{1}
}

func (x *WorkflowEvent) GetWorkflowId() string {
	if x != nil {
		return x.WorkflowId
	}
	return ""
}

func (x *WorkflowEvent) GetGlobalContext() map[string]string {
	if x != nil {
		return x.GlobalContext
	}
	return nil
}

func (x *WorkflowEvent) GetTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.Timestamp
	}
	return nil
}

func (x *WorkflowEvent) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *WorkflowEvent) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *WorkflowEvent) GetProgress() string {
	if x != nil {
		return x.Progress
	}
	return ""
}

func (x *WorkflowEvent) GetState() Workflow_WorkflowState {
	if x != nil {
		return x.State
	}
	return Workflow_Unknown
}

func (x *WorkflowEvent) GetIdentifier() string {
	if x != nil {
		return x.Identifier
	}
	return ""
}

type TaskEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskId         string                 `protobuf:"bytes,1,opt,name=taskId,proto3" json:"taskId,omitempty"`
	WorkflowId     string                 `protobuf:"bytes,2,opt,name=workflowId,proto3" json:"workflowId,omitempty"`
	Inputs         *Inputs                `protobuf:"bytes,3,opt,name=Inputs,proto3" json:"Inputs,omitempty"`
	GlobalContext  map[string]string      `protobuf:"bytes,4,rep,name=globalContext,proto3" json:"globalContext,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Outputs        *Outputs               `protobuf:"bytes,5,opt,name=outputs,proto3" json:"outputs,omitempty"`
	ExecutableName string                 `protobuf:"bytes,6,opt,name=executableName,proto3" json:"executableName,omitempty"`
	Message        string                 `protobuf:"bytes,7,opt,name=message,proto3" json:"message,omitempty"`
	Reason         string                 `protobuf:"bytes,8,opt,name=reason,proto3" json:"reason,omitempty"`
	Timestamp      *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	Progress       string                 `protobuf:"bytes,10,opt,name=progress,proto3" json:"progress,omitempty"`
	State          TaskStatus_TaskState   `protobuf:"varint,11,opt,name=state,proto3,enum=apis.workflow.v1.TaskStatus_TaskState" json:"state,omitempty"`
	Logs           []*TaskLog             `protobuf:"bytes,12,rep,name=logs,proto3" json:"logs,omitempty"`
	TaskName       string                 `protobuf:"bytes,13,opt,name=taskName,proto3" json:"taskName,omitempty"`
}

func (x *TaskEvent) Reset() {
	*x = TaskEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_workflow_v1_workflow_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskEvent) ProtoMessage() {}

func (x *TaskEvent) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_workflow_v1_workflow_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskEvent.ProtoReflect.Descriptor instead.
func (*TaskEvent) Descriptor() ([]byte, []int) {
	return file_aistudio_workflow_v1_workflow_proto_rawDescGZIP(), []int{2}
}

func (x *TaskEvent) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *TaskEvent) GetWorkflowId() string {
	if x != nil {
		return x.WorkflowId
	}
	return ""
}

func (x *TaskEvent) GetInputs() *Inputs {
	if x != nil {
		return x.Inputs
	}
	return nil
}

func (x *TaskEvent) GetGlobalContext() map[string]string {
	if x != nil {
		return x.GlobalContext
	}
	return nil
}

func (x *TaskEvent) GetOutputs() *Outputs {
	if x != nil {
		return x.Outputs
	}
	return nil
}

func (x *TaskEvent) GetExecutableName() string {
	if x != nil {
		return x.ExecutableName
	}
	return ""
}

func (x *TaskEvent) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *TaskEvent) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *TaskEvent) GetTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.Timestamp
	}
	return nil
}

func (x *TaskEvent) GetProgress() string {
	if x != nil {
		return x.Progress
	}
	return ""
}

func (x *TaskEvent) GetState() TaskStatus_TaskState {
	if x != nil {
		return x.State
	}
	return TaskStatus_Pending
}

func (x *TaskEvent) GetLogs() []*TaskLog {
	if x != nil {
		return x.Logs
	}
	return nil
}

func (x *TaskEvent) GetTaskName() string {
	if x != nil {
		return x.TaskName
	}
	return ""
}

type ExecutableEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Executable *Executable `protobuf:"bytes,1,opt,name=executable,proto3" json:"executable,omitempty"`
}

func (x *ExecutableEvent) Reset() {
	*x = ExecutableEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_workflow_v1_workflow_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExecutableEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExecutableEvent) ProtoMessage() {}

func (x *ExecutableEvent) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_workflow_v1_workflow_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExecutableEvent.ProtoReflect.Descriptor instead.
func (*ExecutableEvent) Descriptor() ([]byte, []int) {
	return file_aistudio_workflow_v1_workflow_proto_rawDescGZIP(), []int{3}
}

func (x *ExecutableEvent) GetExecutable() *Executable {
	if x != nil {
		return x.Executable
	}
	return nil
}

type GetTaskStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskId string `protobuf:"bytes,1,opt,name=taskId,proto3" json:"taskId,omitempty"`
}

func (x *GetTaskStatusRequest) Reset() {
	*x = GetTaskStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_workflow_v1_workflow_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTaskStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTaskStatusRequest) ProtoMessage() {}

func (x *GetTaskStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_workflow_v1_workflow_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTaskStatusRequest.ProtoReflect.Descriptor instead.
func (*GetTaskStatusRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_workflow_v1_workflow_proto_rawDescGZIP(), []int{4}
}

func (x *GetTaskStatusRequest) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

type WorkflowSuspendRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkflowId string `protobuf:"bytes,1,opt,name=workflowId,proto3" json:"workflowId,omitempty"`
}

func (x *WorkflowSuspendRequest) Reset() {
	*x = WorkflowSuspendRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_workflow_v1_workflow_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WorkflowSuspendRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WorkflowSuspendRequest) ProtoMessage() {}

func (x *WorkflowSuspendRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_workflow_v1_workflow_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WorkflowSuspendRequest.ProtoReflect.Descriptor instead.
func (*WorkflowSuspendRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_workflow_v1_workflow_proto_rawDescGZIP(), []int{5}
}

func (x *WorkflowSuspendRequest) GetWorkflowId() string {
	if x != nil {
		return x.WorkflowId
	}
	return ""
}

type WorkflowResumeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkflowId string `protobuf:"bytes,1,opt,name=workflowId,proto3" json:"workflowId,omitempty"`
}

func (x *WorkflowResumeRequest) Reset() {
	*x = WorkflowResumeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_workflow_v1_workflow_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WorkflowResumeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WorkflowResumeRequest) ProtoMessage() {}

func (x *WorkflowResumeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_workflow_v1_workflow_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WorkflowResumeRequest.ProtoReflect.Descriptor instead.
func (*WorkflowResumeRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_workflow_v1_workflow_proto_rawDescGZIP(), []int{6}
}

func (x *WorkflowResumeRequest) GetWorkflowId() string {
	if x != nil {
		return x.WorkflowId
	}
	return ""
}

type WorkflowCancelRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkflowId string `protobuf:"bytes,1,opt,name=workflowId,proto3" json:"workflowId,omitempty"`
}

func (x *WorkflowCancelRequest) Reset() {
	*x = WorkflowCancelRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_workflow_v1_workflow_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WorkflowCancelRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WorkflowCancelRequest) ProtoMessage() {}

func (x *WorkflowCancelRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_workflow_v1_workflow_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WorkflowCancelRequest.ProtoReflect.Descriptor instead.
func (*WorkflowCancelRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_workflow_v1_workflow_proto_rawDescGZIP(), []int{7}
}

func (x *WorkflowCancelRequest) GetWorkflowId() string {
	if x != nil {
		return x.WorkflowId
	}
	return ""
}

type WorkflowStartRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkflowId string `protobuf:"bytes,1,opt,name=workflowId,proto3" json:"workflowId,omitempty"`
}

func (x *WorkflowStartRequest) Reset() {
	*x = WorkflowStartRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_workflow_v1_workflow_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WorkflowStartRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WorkflowStartRequest) ProtoMessage() {}

func (x *WorkflowStartRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_workflow_v1_workflow_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WorkflowStartRequest.ProtoReflect.Descriptor instead.
func (*WorkflowStartRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_workflow_v1_workflow_proto_rawDescGZIP(), []int{8}
}

func (x *WorkflowStartRequest) GetWorkflowId() string {
	if x != nil {
		return x.WorkflowId
	}
	return ""
}

type GetWorkflowDetailRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkflowId string `protobuf:"bytes,1,opt,name=workflowId,proto3" json:"workflowId,omitempty"`
}

func (x *GetWorkflowDetailRequest) Reset() {
	*x = GetWorkflowDetailRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_workflow_v1_workflow_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetWorkflowDetailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWorkflowDetailRequest) ProtoMessage() {}

func (x *GetWorkflowDetailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_workflow_v1_workflow_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWorkflowDetailRequest.ProtoReflect.Descriptor instead.
func (*GetWorkflowDetailRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_workflow_v1_workflow_proto_rawDescGZIP(), []int{9}
}

func (x *GetWorkflowDetailRequest) GetWorkflowId() string {
	if x != nil {
		return x.WorkflowId
	}
	return ""
}

type ExchangeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to PayLoad:
	//
	//	*ExchangeRequest_WorkflowStartRequest
	//	*ExchangeRequest_WorkflowCancelRequest
	//	*ExchangeRequest_WorkflowResumeRequest
	//	*ExchangeRequest_WorkflowSuspendRequest
	PayLoad isExchangeRequest_PayLoad `protobuf_oneof:"payLoad"`
}

func (x *ExchangeRequest) Reset() {
	*x = ExchangeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_workflow_v1_workflow_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExchangeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExchangeRequest) ProtoMessage() {}

func (x *ExchangeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_workflow_v1_workflow_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExchangeRequest.ProtoReflect.Descriptor instead.
func (*ExchangeRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_workflow_v1_workflow_proto_rawDescGZIP(), []int{10}
}

func (m *ExchangeRequest) GetPayLoad() isExchangeRequest_PayLoad {
	if m != nil {
		return m.PayLoad
	}
	return nil
}

func (x *ExchangeRequest) GetWorkflowStartRequest() *WorkflowStartRequest {
	if x, ok := x.GetPayLoad().(*ExchangeRequest_WorkflowStartRequest); ok {
		return x.WorkflowStartRequest
	}
	return nil
}

func (x *ExchangeRequest) GetWorkflowCancelRequest() *WorkflowCancelRequest {
	if x, ok := x.GetPayLoad().(*ExchangeRequest_WorkflowCancelRequest); ok {
		return x.WorkflowCancelRequest
	}
	return nil
}

func (x *ExchangeRequest) GetWorkflowResumeRequest() *WorkflowResumeRequest {
	if x, ok := x.GetPayLoad().(*ExchangeRequest_WorkflowResumeRequest); ok {
		return x.WorkflowResumeRequest
	}
	return nil
}

func (x *ExchangeRequest) GetWorkflowSuspendRequest() *WorkflowSuspendRequest {
	if x, ok := x.GetPayLoad().(*ExchangeRequest_WorkflowSuspendRequest); ok {
		return x.WorkflowSuspendRequest
	}
	return nil
}

type isExchangeRequest_PayLoad interface {
	isExchangeRequest_PayLoad()
}

type ExchangeRequest_WorkflowStartRequest struct {
	WorkflowStartRequest *WorkflowStartRequest `protobuf:"bytes,1,opt,name=workflowStartRequest,proto3,oneof"`
}

type ExchangeRequest_WorkflowCancelRequest struct {
	WorkflowCancelRequest *WorkflowCancelRequest `protobuf:"bytes,2,opt,name=workflowCancelRequest,proto3,oneof"`
}

type ExchangeRequest_WorkflowResumeRequest struct {
	WorkflowResumeRequest *WorkflowResumeRequest `protobuf:"bytes,3,opt,name=workflowResumeRequest,proto3,oneof"`
}

type ExchangeRequest_WorkflowSuspendRequest struct {
	WorkflowSuspendRequest *WorkflowSuspendRequest `protobuf:"bytes,4,opt,name=workflowSuspendRequest,proto3,oneof"`
}

func (*ExchangeRequest_WorkflowStartRequest) isExchangeRequest_PayLoad() {}

func (*ExchangeRequest_WorkflowCancelRequest) isExchangeRequest_PayLoad() {}

func (*ExchangeRequest_WorkflowResumeRequest) isExchangeRequest_PayLoad() {}

func (*ExchangeRequest_WorkflowSuspendRequest) isExchangeRequest_PayLoad() {}

type Executable struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskId            string            `protobuf:"bytes,1,opt,name=taskId,proto3" json:"taskId,omitempty"`
	GlobalContext     map[string]string `protobuf:"bytes,2,rep,name=globalContext,proto3" json:"globalContext,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Inputs            *Inputs           `protobuf:"bytes,3,opt,name=inputs,proto3" json:"inputs,omitempty"`
	ExecutableName    string            `protobuf:"bytes,4,opt,name=executableName,proto3" json:"executableName,omitempty"` //执行任务的名称
	WorkerName        string            `protobuf:"bytes,5,opt,name=workerName,proto3" json:"workerName,omitempty"`         //队列名称
	WorkflowNamespace string            `protobuf:"bytes,6,opt,name=workflowNamespace,proto3" json:"workflowNamespace,omitempty"`
	WorkflowName      string            `protobuf:"bytes,7,opt,name=workflowName,proto3" json:"workflowName,omitempty"`
	TaskName          string            `protobuf:"bytes,8,opt,name=taskName,proto3" json:"taskName,omitempty"`
	WorkflowId        string            `protobuf:"bytes,9,opt,name=workflowId,proto3" json:"workflowId,omitempty"`
}

func (x *Executable) Reset() {
	*x = Executable{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_workflow_v1_workflow_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Executable) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Executable) ProtoMessage() {}

func (x *Executable) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_workflow_v1_workflow_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Executable.ProtoReflect.Descriptor instead.
func (*Executable) Descriptor() ([]byte, []int) {
	return file_aistudio_workflow_v1_workflow_proto_rawDescGZIP(), []int{11}
}

func (x *Executable) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *Executable) GetGlobalContext() map[string]string {
	if x != nil {
		return x.GlobalContext
	}
	return nil
}

func (x *Executable) GetInputs() *Inputs {
	if x != nil {
		return x.Inputs
	}
	return nil
}

func (x *Executable) GetExecutableName() string {
	if x != nil {
		return x.ExecutableName
	}
	return ""
}

func (x *Executable) GetWorkerName() string {
	if x != nil {
		return x.WorkerName
	}
	return ""
}

func (x *Executable) GetWorkflowNamespace() string {
	if x != nil {
		return x.WorkflowNamespace
	}
	return ""
}

func (x *Executable) GetWorkflowName() string {
	if x != nil {
		return x.WorkflowName
	}
	return ""
}

func (x *Executable) GetTaskName() string {
	if x != nil {
		return x.TaskName
	}
	return ""
}

func (x *Executable) GetWorkflowId() string {
	if x != nil {
		return x.WorkflowId
	}
	return ""
}

type ExecuteResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskId            string                 `protobuf:"bytes,1,opt,name=taskId,proto3" json:"taskId,omitempty"`
	Outputs           *Outputs               `protobuf:"bytes,2,opt,name=outputs,proto3" json:"outputs,omitempty"`
	GlobalContext     map[string]string      `protobuf:"bytes,3,rep,name=globalContext,proto3" json:"globalContext,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	State             TaskStatus_TaskState   `protobuf:"varint,4,opt,name=state,proto3,enum=apis.workflow.v1.TaskStatus_TaskState" json:"state,omitempty"`
	Reason            string                 `protobuf:"bytes,5,opt,name=reason,proto3" json:"reason,omitempty"`
	Message           string                 `protobuf:"bytes,6,opt,name=message,proto3" json:"message,omitempty"`
	Logs              []*TaskLog             `protobuf:"bytes,7,rep,name=logs,proto3" json:"logs,omitempty"`
	FinishAt          *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=finishAt,proto3" json:"finishAt,omitempty"`
	WorkflowNamespace string                 `protobuf:"bytes,9,opt,name=workflowNamespace,proto3" json:"workflowNamespace,omitempty"`
	WorkflowName      string                 `protobuf:"bytes,10,opt,name=workflowName,proto3" json:"workflowName,omitempty"`
	TaskName          string                 `protobuf:"bytes,11,opt,name=taskName,proto3" json:"taskName,omitempty"`
	ExecutableName    string                 `protobuf:"bytes,12,opt,name=executableName,proto3" json:"executableName,omitempty"`
}

func (x *ExecuteResult) Reset() {
	*x = ExecuteResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_workflow_v1_workflow_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExecuteResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExecuteResult) ProtoMessage() {}

func (x *ExecuteResult) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_workflow_v1_workflow_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExecuteResult.ProtoReflect.Descriptor instead.
func (*ExecuteResult) Descriptor() ([]byte, []int) {
	return file_aistudio_workflow_v1_workflow_proto_rawDescGZIP(), []int{12}
}

func (x *ExecuteResult) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *ExecuteResult) GetOutputs() *Outputs {
	if x != nil {
		return x.Outputs
	}
	return nil
}

func (x *ExecuteResult) GetGlobalContext() map[string]string {
	if x != nil {
		return x.GlobalContext
	}
	return nil
}

func (x *ExecuteResult) GetState() TaskStatus_TaskState {
	if x != nil {
		return x.State
	}
	return TaskStatus_Pending
}

func (x *ExecuteResult) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *ExecuteResult) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ExecuteResult) GetLogs() []*TaskLog {
	if x != nil {
		return x.Logs
	}
	return nil
}

func (x *ExecuteResult) GetFinishAt() *timestamppb.Timestamp {
	if x != nil {
		return x.FinishAt
	}
	return nil
}

func (x *ExecuteResult) GetWorkflowNamespace() string {
	if x != nil {
		return x.WorkflowNamespace
	}
	return ""
}

func (x *ExecuteResult) GetWorkflowName() string {
	if x != nil {
		return x.WorkflowName
	}
	return ""
}

func (x *ExecuteResult) GetTaskName() string {
	if x != nil {
		return x.TaskName
	}
	return ""
}

func (x *ExecuteResult) GetExecutableName() string {
	if x != nil {
		return x.ExecutableName
	}
	return ""
}

type TaskLog struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Level     string                 `protobuf:"bytes,1,opt,name=level,proto3" json:"level,omitempty"`
	Timestamp *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	Log       string                 `protobuf:"bytes,3,opt,name=log,proto3" json:"log,omitempty"`
}

func (x *TaskLog) Reset() {
	*x = TaskLog{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_workflow_v1_workflow_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskLog) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskLog) ProtoMessage() {}

func (x *TaskLog) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_workflow_v1_workflow_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskLog.ProtoReflect.Descriptor instead.
func (*TaskLog) Descriptor() ([]byte, []int) {
	return file_aistudio_workflow_v1_workflow_proto_rawDescGZIP(), []int{13}
}

func (x *TaskLog) GetLevel() string {
	if x != nil {
		return x.Level
	}
	return ""
}

func (x *TaskLog) GetTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.Timestamp
	}
	return nil
}

func (x *TaskLog) GetLog() string {
	if x != nil {
		return x.Log
	}
	return ""
}

type Workflow struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name      string           `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Namespace string           `protobuf:"bytes,2,opt,name=namespace,proto3" json:"namespace,omitempty"`
	Spec      *Workflow_Spec   `protobuf:"bytes,3,opt,name=spec,proto3" json:"spec,omitempty"`
	Status    *Workflow_Status `protobuf:"bytes,4,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *Workflow) Reset() {
	*x = Workflow{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_workflow_v1_workflow_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Workflow) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Workflow) ProtoMessage() {}

func (x *Workflow) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_workflow_v1_workflow_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Workflow.ProtoReflect.Descriptor instead.
func (*Workflow) Descriptor() ([]byte, []int) {
	return file_aistudio_workflow_v1_workflow_proto_rawDescGZIP(), []int{14}
}

func (x *Workflow) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Workflow) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *Workflow) GetSpec() *Workflow_Spec {
	if x != nil {
		return x.Spec
	}
	return nil
}

func (x *Workflow) GetStatus() *Workflow_Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type Input struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key   string `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	Value string `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *Input) Reset() {
	*x = Input{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_workflow_v1_workflow_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Input) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Input) ProtoMessage() {}

func (x *Input) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_workflow_v1_workflow_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Input.ProtoReflect.Descriptor instead.
func (*Input) Descriptor() ([]byte, []int) {
	return file_aistudio_workflow_v1_workflow_proto_rawDescGZIP(), []int{15}
}

func (x *Input) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *Input) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

type Output struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key   string `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	Value string `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *Output) Reset() {
	*x = Output{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_workflow_v1_workflow_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Output) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Output) ProtoMessage() {}

func (x *Output) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_workflow_v1_workflow_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Output.ProtoReflect.Descriptor instead.
func (*Output) Descriptor() ([]byte, []int) {
	return file_aistudio_workflow_v1_workflow_proto_rawDescGZIP(), []int{16}
}

func (x *Output) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *Output) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

type Inputs struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items []*Input `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
}

func (x *Inputs) Reset() {
	*x = Inputs{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_workflow_v1_workflow_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Inputs) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Inputs) ProtoMessage() {}

func (x *Inputs) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_workflow_v1_workflow_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Inputs.ProtoReflect.Descriptor instead.
func (*Inputs) Descriptor() ([]byte, []int) {
	return file_aistudio_workflow_v1_workflow_proto_rawDescGZIP(), []int{17}
}

func (x *Inputs) GetItems() []*Input {
	if x != nil {
		return x.Items
	}
	return nil
}

type Outputs struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items []*Output `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
}

func (x *Outputs) Reset() {
	*x = Outputs{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_workflow_v1_workflow_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Outputs) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Outputs) ProtoMessage() {}

func (x *Outputs) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_workflow_v1_workflow_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Outputs.ProtoReflect.Descriptor instead.
func (*Outputs) Descriptor() ([]byte, []int) {
	return file_aistudio_workflow_v1_workflow_proto_rawDescGZIP(), []int{18}
}

func (x *Outputs) GetItems() []*Output {
	if x != nil {
		return x.Items
	}
	return nil
}

type InputsLoop struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items []*Inputs `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
}

func (x *InputsLoop) Reset() {
	*x = InputsLoop{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_workflow_v1_workflow_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InputsLoop) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InputsLoop) ProtoMessage() {}

func (x *InputsLoop) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_workflow_v1_workflow_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InputsLoop.ProtoReflect.Descriptor instead.
func (*InputsLoop) Descriptor() ([]byte, []int) {
	return file_aistudio_workflow_v1_workflow_proto_rawDescGZIP(), []int{19}
}

func (x *InputsLoop) GetItems() []*Inputs {
	if x != nil {
		return x.Items
	}
	return nil
}

type TaskSpec struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name           string               `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	DisplayName    string               `protobuf:"bytes,3,opt,name=displayName,proto3" json:"displayName,omitempty"`
	Description    string               `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	Dependencies   []string             `protobuf:"bytes,5,rep,name=dependencies,proto3" json:"dependencies,omitempty"` //依赖的任务
	ExecutableName string               `protobuf:"bytes,6,opt,name=executableName,proto3" json:"executableName,omitempty"`
	Worker         string               `protobuf:"bytes,7,opt,name=worker,proto3" json:"worker,omitempty"`
	Timeout        *durationpb.Duration `protobuf:"bytes,8,opt,name=timeout,proto3" json:"timeout,omitempty"`
	RetryStrategy  *RetryStrategy       `protobuf:"bytes,9,opt,name=retryStrategy,proto3" json:"retryStrategy,omitempty"`
	When           string               `protobuf:"bytes,10,opt,name=when,proto3" json:"when,omitempty"`
	Inputs         *Inputs              `protobuf:"bytes,11,opt,name=inputs,proto3" json:"inputs,omitempty"`
	Skip           bool                 `protobuf:"varint,12,opt,name=skip,proto3" json:"skip,omitempty"`
	InputsLoop     *InputsLoop          `protobuf:"bytes,13,opt,name=inputsLoop,proto3" json:"inputsLoop,omitempty"` //迭代任务,当和arguments同时存在时，argumentsItems优先
}

func (x *TaskSpec) Reset() {
	*x = TaskSpec{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_workflow_v1_workflow_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskSpec) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskSpec) ProtoMessage() {}

func (x *TaskSpec) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_workflow_v1_workflow_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskSpec.ProtoReflect.Descriptor instead.
func (*TaskSpec) Descriptor() ([]byte, []int) {
	return file_aistudio_workflow_v1_workflow_proto_rawDescGZIP(), []int{20}
}

func (x *TaskSpec) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *TaskSpec) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *TaskSpec) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *TaskSpec) GetDependencies() []string {
	if x != nil {
		return x.Dependencies
	}
	return nil
}

func (x *TaskSpec) GetExecutableName() string {
	if x != nil {
		return x.ExecutableName
	}
	return ""
}

func (x *TaskSpec) GetWorker() string {
	if x != nil {
		return x.Worker
	}
	return ""
}

func (x *TaskSpec) GetTimeout() *durationpb.Duration {
	if x != nil {
		return x.Timeout
	}
	return nil
}

func (x *TaskSpec) GetRetryStrategy() *RetryStrategy {
	if x != nil {
		return x.RetryStrategy
	}
	return nil
}

func (x *TaskSpec) GetWhen() string {
	if x != nil {
		return x.When
	}
	return ""
}

func (x *TaskSpec) GetInputs() *Inputs {
	if x != nil {
		return x.Inputs
	}
	return nil
}

func (x *TaskSpec) GetSkip() bool {
	if x != nil {
		return x.Skip
	}
	return false
}

func (x *TaskSpec) GetInputsLoop() *InputsLoop {
	if x != nil {
		return x.InputsLoop
	}
	return nil
}

type TaskStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Spec          *TaskSpec              `protobuf:"bytes,1,opt,name=spec,proto3" json:"spec,omitempty"`
	Id            string                 `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`
	State         TaskStatus_TaskState   `protobuf:"varint,3,opt,name=state,proto3,enum=apis.workflow.v1.TaskStatus_TaskState" json:"state,omitempty"`
	Message       string                 `protobuf:"bytes,4,opt,name=message,proto3" json:"message,omitempty"`
	StartAt       *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=startAt,proto3" json:"startAt,omitempty"`
	ModifyAt      *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=modifyAt,proto3" json:"modifyAt,omitempty"`
	Inputs        *Inputs                `protobuf:"bytes,7,opt,name=inputs,proto3" json:"inputs,omitempty"`
	Outputs       *Outputs               `protobuf:"bytes,8,opt,name=outputs,proto3" json:"outputs,omitempty"`
	Reason        string                 `protobuf:"bytes,9,opt,name=reason,proto3" json:"reason,omitempty"`
	Children      []string               `protobuf:"bytes,10,rep,name=children,proto3" json:"children,omitempty"` //子任务
	Progress      string                 `protobuf:"bytes,11,opt,name=progress,proto3" json:"progress,omitempty"`
	Type          TaskStatus_TaskType    `protobuf:"varint,12,opt,name=type,proto3,enum=apis.workflow.v1.TaskStatus_TaskType" json:"type,omitempty"`
	GlobalContext map[string]string      `protobuf:"bytes,13,rep,name=globalContext,proto3" json:"globalContext,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Dependencies  []string               `protobuf:"bytes,14,rep,name=dependencies,proto3" json:"dependencies,omitempty"` //依赖的任务ID
}

func (x *TaskStatus) Reset() {
	*x = TaskStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_workflow_v1_workflow_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskStatus) ProtoMessage() {}

func (x *TaskStatus) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_workflow_v1_workflow_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskStatus.ProtoReflect.Descriptor instead.
func (*TaskStatus) Descriptor() ([]byte, []int) {
	return file_aistudio_workflow_v1_workflow_proto_rawDescGZIP(), []int{21}
}

func (x *TaskStatus) GetSpec() *TaskSpec {
	if x != nil {
		return x.Spec
	}
	return nil
}

func (x *TaskStatus) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *TaskStatus) GetState() TaskStatus_TaskState {
	if x != nil {
		return x.State
	}
	return TaskStatus_Pending
}

func (x *TaskStatus) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *TaskStatus) GetStartAt() *timestamppb.Timestamp {
	if x != nil {
		return x.StartAt
	}
	return nil
}

func (x *TaskStatus) GetModifyAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ModifyAt
	}
	return nil
}

func (x *TaskStatus) GetInputs() *Inputs {
	if x != nil {
		return x.Inputs
	}
	return nil
}

func (x *TaskStatus) GetOutputs() *Outputs {
	if x != nil {
		return x.Outputs
	}
	return nil
}

func (x *TaskStatus) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *TaskStatus) GetChildren() []string {
	if x != nil {
		return x.Children
	}
	return nil
}

func (x *TaskStatus) GetProgress() string {
	if x != nil {
		return x.Progress
	}
	return ""
}

func (x *TaskStatus) GetType() TaskStatus_TaskType {
	if x != nil {
		return x.Type
	}
	return TaskStatus_Step
}

func (x *TaskStatus) GetGlobalContext() map[string]string {
	if x != nil {
		return x.GlobalContext
	}
	return nil
}

func (x *TaskStatus) GetDependencies() []string {
	if x != nil {
		return x.Dependencies
	}
	return nil
}

type RetryStrategy struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Backoff *BackOff    `protobuf:"bytes,1,opt,name=backoff,proto3" json:"backoff,omitempty"`
	Policy  RetryPolicy `protobuf:"varint,2,opt,name=policy,proto3,enum=apis.workflow.v1.RetryPolicy" json:"policy,omitempty"`
}

func (x *RetryStrategy) Reset() {
	*x = RetryStrategy{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_workflow_v1_workflow_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RetryStrategy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RetryStrategy) ProtoMessage() {}

func (x *RetryStrategy) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_workflow_v1_workflow_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RetryStrategy.ProtoReflect.Descriptor instead.
func (*RetryStrategy) Descriptor() ([]byte, []int) {
	return file_aistudio_workflow_v1_workflow_proto_rawDescGZIP(), []int{22}
}

func (x *RetryStrategy) GetBackoff() *BackOff {
	if x != nil {
		return x.Backoff
	}
	return nil
}

func (x *RetryStrategy) GetPolicy() RetryPolicy {
	if x != nil {
		return x.Policy
	}
	return RetryPolicy_Always
}

type BackOff struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Steps    int32                `protobuf:"varint,1,opt,name=steps,proto3" json:"steps,omitempty"`      // 指数退避的步数,可以看做程序的最大重试次数
	Duration *durationpb.Duration `protobuf:"bytes,2,opt,name=duration,proto3" json:"duration,omitempty"` //初始延迟的时间
	Factor   int32                `protobuf:"varint,3,opt,name=factor,proto3" json:"factor,omitempty"`    //指数退避的因子
	Jitter   int32                `protobuf:"varint,4,opt,name=jitter,proto3" json:"jitter,omitempty"`    //可以看作是偏差因子，该值越大，每次重试的延时的可选区间越大
}

func (x *BackOff) Reset() {
	*x = BackOff{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_workflow_v1_workflow_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BackOff) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackOff) ProtoMessage() {}

func (x *BackOff) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_workflow_v1_workflow_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackOff.ProtoReflect.Descriptor instead.
func (*BackOff) Descriptor() ([]byte, []int) {
	return file_aistudio_workflow_v1_workflow_proto_rawDescGZIP(), []int{23}
}

func (x *BackOff) GetSteps() int32 {
	if x != nil {
		return x.Steps
	}
	return 0
}

func (x *BackOff) GetDuration() *durationpb.Duration {
	if x != nil {
		return x.Duration
	}
	return nil
}

func (x *BackOff) GetFactor() int32 {
	if x != nil {
		return x.Factor
	}
	return 0
}

func (x *BackOff) GetJitter() int32 {
	if x != nil {
		return x.Jitter
	}
	return 0
}

type GetTasksByTopologicalSortRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkflowId string `protobuf:"bytes,1,opt,name=workflowId,proto3" json:"workflowId,omitempty"`
}

func (x *GetTasksByTopologicalSortRequest) Reset() {
	*x = GetTasksByTopologicalSortRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_workflow_v1_workflow_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTasksByTopologicalSortRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTasksByTopologicalSortRequest) ProtoMessage() {}

func (x *GetTasksByTopologicalSortRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_workflow_v1_workflow_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTasksByTopologicalSortRequest.ProtoReflect.Descriptor instead.
func (*GetTasksByTopologicalSortRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_workflow_v1_workflow_proto_rawDescGZIP(), []int{24}
}

func (x *GetTasksByTopologicalSortRequest) GetWorkflowId() string {
	if x != nil {
		return x.WorkflowId
	}
	return ""
}

type GetTasksByTopologicalSortResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Tasks []*TaskStatus `protobuf:"bytes,1,rep,name=tasks,proto3" json:"tasks,omitempty"`
}

func (x *GetTasksByTopologicalSortResponse) Reset() {
	*x = GetTasksByTopologicalSortResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_workflow_v1_workflow_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTasksByTopologicalSortResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTasksByTopologicalSortResponse) ProtoMessage() {}

func (x *GetTasksByTopologicalSortResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_workflow_v1_workflow_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTasksByTopologicalSortResponse.ProtoReflect.Descriptor instead.
func (*GetTasksByTopologicalSortResponse) Descriptor() ([]byte, []int) {
	return file_aistudio_workflow_v1_workflow_proto_rawDescGZIP(), []int{25}
}

func (x *GetTasksByTopologicalSortResponse) GetTasks() []*TaskStatus {
	if x != nil {
		return x.Tasks
	}
	return nil
}

type Workflow_Spec struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Description string               `protobuf:"bytes,1,opt,name=description,proto3" json:"description,omitempty"`
	Tasks       []*TaskSpec          `protobuf:"bytes,2,rep,name=tasks,proto3" json:"tasks,omitempty"`
	EntryPoint  string               `protobuf:"bytes,3,opt,name=entryPoint,proto3" json:"entryPoint,omitempty"`
	Arguments   map[string]string    `protobuf:"bytes,4,rep,name=arguments,proto3" json:"arguments,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Timeout     *durationpb.Duration `protobuf:"bytes,5,opt,name=timeout,proto3" json:"timeout,omitempty"`
}

func (x *Workflow_Spec) Reset() {
	*x = Workflow_Spec{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_workflow_v1_workflow_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Workflow_Spec) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Workflow_Spec) ProtoMessage() {}

func (x *Workflow_Spec) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_workflow_v1_workflow_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Workflow_Spec.ProtoReflect.Descriptor instead.
func (*Workflow_Spec) Descriptor() ([]byte, []int) {
	return file_aistudio_workflow_v1_workflow_proto_rawDescGZIP(), []int{14, 0}
}

func (x *Workflow_Spec) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Workflow_Spec) GetTasks() []*TaskSpec {
	if x != nil {
		return x.Tasks
	}
	return nil
}

func (x *Workflow_Spec) GetEntryPoint() string {
	if x != nil {
		return x.EntryPoint
	}
	return ""
}

func (x *Workflow_Spec) GetArguments() map[string]string {
	if x != nil {
		return x.Arguments
	}
	return nil
}

func (x *Workflow_Spec) GetTimeout() *durationpb.Duration {
	if x != nil {
		return x.Timeout
	}
	return nil
}

type Workflow_Status struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id               string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	State            Workflow_WorkflowState `protobuf:"varint,2,opt,name=state,proto3,enum=apis.workflow.v1.Workflow_WorkflowState" json:"state,omitempty"`
	StartAt          *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=startAt,proto3" json:"startAt,omitempty"`
	FinishAt         *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=finishAt,proto3" json:"finishAt,omitempty"`
	Progress         string                 `protobuf:"bytes,5,opt,name=progress,proto3" json:"progress,omitempty"`
	Message          string                 `protobuf:"bytes,6,opt,name=message,proto3" json:"message,omitempty"`
	Reason           string                 `protobuf:"bytes,7,opt,name=reason,proto3" json:"reason,omitempty"`
	Tasks            map[string]*TaskStatus `protobuf:"bytes,8,rep,name=tasks,proto3" json:"tasks,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` //任务的状态
	EntryPointTaskId string                 `protobuf:"bytes,9,opt,name=entryPointTaskId,proto3" json:"entryPointTaskId,omitempty"`
	Identifier       string                 `protobuf:"bytes,10,opt,name=identifier,proto3" json:"identifier,omitempty"` //broker的标识
}

func (x *Workflow_Status) Reset() {
	*x = Workflow_Status{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_workflow_v1_workflow_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Workflow_Status) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Workflow_Status) ProtoMessage() {}

func (x *Workflow_Status) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_workflow_v1_workflow_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Workflow_Status.ProtoReflect.Descriptor instead.
func (*Workflow_Status) Descriptor() ([]byte, []int) {
	return file_aistudio_workflow_v1_workflow_proto_rawDescGZIP(), []int{14, 1}
}

func (x *Workflow_Status) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Workflow_Status) GetState() Workflow_WorkflowState {
	if x != nil {
		return x.State
	}
	return Workflow_Unknown
}

func (x *Workflow_Status) GetStartAt() *timestamppb.Timestamp {
	if x != nil {
		return x.StartAt
	}
	return nil
}

func (x *Workflow_Status) GetFinishAt() *timestamppb.Timestamp {
	if x != nil {
		return x.FinishAt
	}
	return nil
}

func (x *Workflow_Status) GetProgress() string {
	if x != nil {
		return x.Progress
	}
	return ""
}

func (x *Workflow_Status) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *Workflow_Status) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *Workflow_Status) GetTasks() map[string]*TaskStatus {
	if x != nil {
		return x.Tasks
	}
	return nil
}

func (x *Workflow_Status) GetEntryPointTaskId() string {
	if x != nil {
		return x.EntryPointTaskId
	}
	return ""
}

func (x *Workflow_Status) GetIdentifier() string {
	if x != nil {
		return x.Identifier
	}
	return ""
}

var File_aistudio_workflow_v1_workflow_proto protoreflect.FileDescriptor

var file_aistudio_workflow_v1_workflow_proto_rawDesc = []byte{
	0x0a, 0x23, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x66,
	0x6c, 0x6f, 0x77, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x10, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x77, 0x6f, 0x72, 0x6b,
	0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x76, 0x31, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70,
	0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0xe5, 0x02, 0x0a, 0x10, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77,
	0x52, 0x75, 0x6e, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x2c, 0x0a, 0x11, 0x77, 0x6f, 0x72, 0x6b,
	0x66, 0x6c, 0x6f, 0x77, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x11, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x4e, 0x61, 0x6d,
	0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c,
	0x6f, 0x77, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x77, 0x6f,
	0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x77, 0x6f,
	0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x49, 0x64, 0x12, 0x47, 0x0a, 0x0d, 0x77, 0x6f,
	0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f,
	0x77, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x45, 0x76, 0x65,
	0x6e, 0x74, 0x48, 0x00, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x45, 0x76,
	0x65, 0x6e, 0x74, 0x12, 0x3b, 0x0a, 0x09, 0x74, 0x61, 0x73, 0x6b, 0x45, 0x76, 0x65, 0x6e, 0x74,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x77, 0x6f,
	0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x45, 0x76,
	0x65, 0x6e, 0x74, 0x48, 0x00, 0x52, 0x09, 0x74, 0x61, 0x73, 0x6b, 0x45, 0x76, 0x65, 0x6e, 0x74,
	0x12, 0x4d, 0x0a, 0x0f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x45, 0x76,
	0x65, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x73,
	0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x65,
	0x63, 0x75, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x48, 0x00, 0x52, 0x0f,
	0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x42,
	0x0a, 0x0a, 0x08, 0x70, 0x6c, 0x61, 0x79, 0x4c, 0x6f, 0x61, 0x64, 0x22, 0xb3, 0x03, 0x0a, 0x0d,
	0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x1e, 0x0a,
	0x0a, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x49, 0x64, 0x12, 0x58, 0x0a,
	0x0d, 0x67, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x77, 0x6f, 0x72, 0x6b,
	0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77,
	0x45, 0x76, 0x65, 0x6e, 0x74, 0x2e, 0x47, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x74,
	0x65, 0x78, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0d, 0x67, 0x6c, 0x6f, 0x62, 0x61, 0x6c,
	0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x12, 0x38, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x12,
	0x3e, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x28,
	0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x76,
	0x31, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x66,
	0x6c, 0x6f, 0x77, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12,
	0x1e, 0x0a, 0x0a, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x1a,
	0x40, 0x0a, 0x12, 0x47, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38,
	0x01, 0x22, 0xfb, 0x04, 0x0a, 0x09, 0x54, 0x61, 0x73, 0x6b, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12,
	0x16, 0x0a, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x77, 0x6f, 0x72, 0x6b, 0x66,
	0x6c, 0x6f, 0x77, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x77, 0x6f, 0x72,
	0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x49, 0x64, 0x12, 0x30, 0x0a, 0x06, 0x49, 0x6e, 0x70, 0x75, 0x74,
	0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x77,
	0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6e, 0x70, 0x75, 0x74,
	0x73, 0x52, 0x06, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x73, 0x12, 0x54, 0x0a, 0x0d, 0x67, 0x6c, 0x6f,
	0x62, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77,
	0x2e, 0x76, 0x31, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x2e, 0x47, 0x6c,
	0x6f, 0x62, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x52, 0x0d, 0x67, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x12,
	0x33, 0x0a, 0x07, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77,
	0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x73, 0x52, 0x07, 0x6f, 0x75, 0x74,
	0x70, 0x75, 0x74, 0x73, 0x12, 0x26, 0x0a, 0x0e, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x61, 0x62,
	0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x65, 0x78,
	0x65, 0x63, 0x75, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x38,
	0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x74,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x67,
	0x72, 0x65, 0x73, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x67,
	0x72, 0x65, 0x73, 0x73, 0x12, 0x3c, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66,
	0x6c, 0x6f, 0x77, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61,
	0x74, 0x65, 0x12, 0x2d, 0x0a, 0x04, 0x6c, 0x6f, 0x67, 0x73, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77,
	0x2e, 0x76, 0x31, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x4c, 0x6f, 0x67, 0x52, 0x04, 0x6c, 0x6f, 0x67,
	0x73, 0x12, 0x1a, 0x0a, 0x08, 0x74, 0x61, 0x73, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x0d, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x61, 0x73, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x1a, 0x40, 0x0a,
	0x12, 0x47, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22,
	0x4f, 0x0a, 0x0f, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x45, 0x76, 0x65,
	0x6e, 0x74, 0x12, 0x3c, 0x0a, 0x0a, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x61, 0x62, 0x6c, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x77, 0x6f,
	0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74,
	0x61, 0x62, 0x6c, 0x65, 0x52, 0x0a, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x61, 0x62, 0x6c, 0x65,
	0x22, 0x2e, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x74, 0x61, 0x73, 0x6b,
	0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64,
	0x22, 0x38, 0x0a, 0x16, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x53, 0x75, 0x73, 0x70,
	0x65, 0x6e, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x77, 0x6f,
	0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x49, 0x64, 0x22, 0x37, 0x0a, 0x15, 0x57, 0x6f,
	0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x65, 0x73, 0x75, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x49,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f,
	0x77, 0x49, 0x64, 0x22, 0x37, 0x0a, 0x15, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x43,
	0x61, 0x6e, 0x63, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1e, 0x0a, 0x0a,
	0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x49, 0x64, 0x22, 0x36, 0x0a, 0x14,
	0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x53, 0x74, 0x61, 0x72, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77,
	0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c,
	0x6f, 0x77, 0x49, 0x64, 0x22, 0x3a, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66,
	0x6c, 0x6f, 0x77, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x1e, 0x0a, 0x0a, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x49, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x49, 0x64,
	0x22, 0xa0, 0x03, 0x0a, 0x0f, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x5c, 0x0a, 0x14, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77,
	0x53, 0x74, 0x61, 0x72, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c,
	0x6f, 0x77, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x53, 0x74,
	0x61, 0x72, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x14, 0x77, 0x6f,
	0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x53, 0x74, 0x61, 0x72, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x5f, 0x0a, 0x15, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x43, 0x61,
	0x6e, 0x63, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f,
	0x77, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x43, 0x61, 0x6e,
	0x63, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x15, 0x77, 0x6f,
	0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x5f, 0x0a, 0x15, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52,
	0x65, 0x73, 0x75, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c,
	0x6f, 0x77, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x65,
	0x73, 0x75, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x15, 0x77,
	0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x65, 0x73, 0x75, 0x6d, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x62, 0x0a, 0x16, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77,
	0x53, 0x75, 0x73, 0x70, 0x65, 0x6e, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x77, 0x6f, 0x72, 0x6b,
	0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77,
	0x53, 0x75, 0x73, 0x70, 0x65, 0x6e, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00,
	0x52, 0x16, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x53, 0x75, 0x73, 0x70, 0x65, 0x6e,
	0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x42, 0x09, 0x0a, 0x07, 0x70, 0x61, 0x79, 0x4c,
	0x6f, 0x61, 0x64, 0x22, 0xc5, 0x03, 0x0a, 0x0a, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x61, 0x62,
	0x6c, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x55, 0x0a, 0x0d, 0x67, 0x6c,
	0x6f, 0x62, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x2f, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f,
	0x77, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x2e,
	0x47, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x52, 0x0d, 0x67, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78,
	0x74, 0x12, 0x30, 0x0a, 0x06, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f,
	0x77, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x73, 0x52, 0x06, 0x69, 0x6e, 0x70,
	0x75, 0x74, 0x73, 0x12, 0x26, 0x0a, 0x0e, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x61, 0x62, 0x6c,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x65, 0x78, 0x65,
	0x63, 0x75, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x77,
	0x6f, 0x72, 0x6b, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x77, 0x6f, 0x72, 0x6b, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2c, 0x0a, 0x11, 0x77,
	0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77,
	0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x77, 0x6f, 0x72,
	0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a,
	0x08, 0x74, 0x61, 0x73, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x74, 0x61, 0x73, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x77, 0x6f, 0x72,
	0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x49, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x77,
	0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x49, 0x64, 0x1a, 0x40, 0x0a, 0x12, 0x47, 0x6c, 0x6f,
	0x62, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xe5, 0x04, 0x0a, 0x0d,
	0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x16, 0x0a,
	0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74,
	0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x33, 0x0a, 0x07, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x73,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x77, 0x6f,
	0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74,
	0x73, 0x52, 0x07, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x73, 0x12, 0x58, 0x0a, 0x0d, 0x67, 0x6c,
	0x6f, 0x62, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x32, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f,
	0x77, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x2e, 0x47, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0d, 0x67, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x43, 0x6f, 0x6e,
	0x74, 0x65, 0x78, 0x74, 0x12, 0x3c, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66,
	0x6c, 0x6f, 0x77, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61,
	0x74, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x12, 0x2d, 0x0a, 0x04, 0x6c, 0x6f, 0x67, 0x73, 0x18, 0x07, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c,
	0x6f, 0x77, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x4c, 0x6f, 0x67, 0x52, 0x04, 0x6c,
	0x6f, 0x67, 0x73, 0x12, 0x36, 0x0a, 0x08, 0x66, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x41, 0x74, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x52, 0x08, 0x66, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x41, 0x74, 0x12, 0x2c, 0x0a, 0x11, 0x77,
	0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77,
	0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x77, 0x6f, 0x72,
	0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a,
	0x08, 0x74, 0x61, 0x73, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x74, 0x61, 0x73, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x65, 0x78, 0x65,
	0x63, 0x75, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0e, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x1a, 0x40, 0x0a, 0x12, 0x47, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x74, 0x65,
	0x78, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x22, 0x6b, 0x0a, 0x07, 0x54, 0x61, 0x73, 0x6b, 0x4c, 0x6f, 0x67, 0x12, 0x14,
	0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6c,
	0x65, 0x76, 0x65, 0x6c, 0x12, 0x38, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x10,
	0x0a, 0x03, 0x6c, 0x6f, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6c, 0x6f, 0x67,
	0x22, 0xc6, 0x08, 0x0a, 0x08, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x1c, 0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12,
	0x33, 0x0a, 0x04, 0x73, 0x70, 0x65, 0x63, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e,
	0x61, 0x70, 0x69, 0x73, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x76, 0x31,
	0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x53, 0x70, 0x65, 0x63, 0x52, 0x04,
	0x73, 0x70, 0x65, 0x63, 0x12, 0x39, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x77, 0x6f, 0x72, 0x6b,
	0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77,
	0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x1a,
	0xbb, 0x02, 0x0a, 0x04, 0x53, 0x70, 0x65, 0x63, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x30, 0x0a, 0x05, 0x74, 0x61,
	0x73, 0x6b, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x73,
	0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x61, 0x73,
	0x6b, 0x53, 0x70, 0x65, 0x63, 0x52, 0x05, 0x74, 0x61, 0x73, 0x6b, 0x73, 0x12, 0x1e, 0x0a, 0x0a,
	0x65, 0x6e, 0x74, 0x72, 0x79, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x12, 0x4c, 0x0a, 0x09,
	0x61, 0x72, 0x67, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x2e, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e,
	0x76, 0x31, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x53, 0x70, 0x65, 0x63,
	0x2e, 0x41, 0x72, 0x67, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52,
	0x09, 0x61, 0x72, 0x67, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x33, 0x0a, 0x07, 0x74, 0x69,
	0x6d, 0x65, 0x6f, 0x75, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x07, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x1a,
	0x3c, 0x0a, 0x0e, 0x41, 0x72, 0x67, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0xfc, 0x03,
	0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x3e, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x28, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x77,
	0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x66,
	0x6c, 0x6f, 0x77, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x34, 0x0a, 0x07, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x41, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x07, 0x73, 0x74, 0x61, 0x72, 0x74, 0x41, 0x74, 0x12, 0x36,
	0x0a, 0x08, 0x66, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x41, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x08, 0x66, 0x69,
	0x6e, 0x69, 0x73, 0x68, 0x41, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x65,
	0x73, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x65,
	0x73, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x16, 0x0a, 0x06,
	0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65,
	0x61, 0x73, 0x6f, 0x6e, 0x12, 0x42, 0x0a, 0x05, 0x74, 0x61, 0x73, 0x6b, 0x73, 0x18, 0x08, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66,
	0x6c, 0x6f, 0x77, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x73, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x52, 0x05, 0x74, 0x61, 0x73, 0x6b, 0x73, 0x12, 0x2a, 0x0a, 0x10, 0x65, 0x6e, 0x74, 0x72,
	0x79, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x10, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x54, 0x61,
	0x73, 0x6b, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69,
	0x65, 0x72, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69,
	0x66, 0x69, 0x65, 0x72, 0x1a, 0x56, 0x0a, 0x0a, 0x54, 0x61, 0x73, 0x6b, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x32, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66,
	0x6c, 0x6f, 0x77, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x5b, 0x0a, 0x0d,
	0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x0b, 0x0a,
	0x07, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x50, 0x65,
	0x6e, 0x64, 0x69, 0x6e, 0x67, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x52, 0x75, 0x6e, 0x6e, 0x69,
	0x6e, 0x67, 0x10, 0x02, 0x12, 0x0b, 0x0a, 0x07, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x10,
	0x03, 0x12, 0x0b, 0x0a, 0x07, 0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x10, 0x04, 0x12, 0x09,
	0x0a, 0x05, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x10, 0x05, 0x22, 0x2f, 0x0a, 0x05, 0x49, 0x6e, 0x70,
	0x75, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x30, 0x0a, 0x06, 0x4f, 0x75,
	0x74, 0x70, 0x75, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x37, 0x0a, 0x06,
	0x49, 0x6e, 0x70, 0x75, 0x74, 0x73, 0x12, 0x2d, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x77, 0x6f, 0x72,
	0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x52, 0x05,
	0x69, 0x74, 0x65, 0x6d, 0x73, 0x22, 0x39, 0x0a, 0x07, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x73,
	0x12, 0x2e, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x18, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e,
	0x76, 0x31, 0x2e, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73,
	0x22, 0x3c, 0x0a, 0x0a, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x73, 0x4c, 0x6f, 0x6f, 0x70, 0x12, 0x2e,
	0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e,
	0x61, 0x70, 0x69, 0x73, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x76, 0x31,
	0x2e, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x73, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x22, 0xda,
	0x03, 0x0a, 0x08, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x70, 0x65, 0x63, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x20, 0x0a, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x22, 0x0a, 0x0c, 0x64, 0x65, 0x70, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x63,
	0x69, 0x65, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x64, 0x65, 0x70, 0x65, 0x6e,
	0x64, 0x65, 0x6e, 0x63, 0x69, 0x65, 0x73, 0x12, 0x26, 0x0a, 0x0e, 0x65, 0x78, 0x65, 0x63, 0x75,
	0x74, 0x61, 0x62, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0e, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x16, 0x0a, 0x06, 0x77, 0x6f, 0x72, 0x6b, 0x65, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x77, 0x6f, 0x72, 0x6b, 0x65, 0x72, 0x12, 0x33, 0x0a, 0x07, 0x74, 0x69, 0x6d, 0x65, 0x6f,
	0x75, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x07, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x12, 0x45, 0x0a, 0x0d,
	0x72, 0x65, 0x74, 0x72, 0x79, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66,
	0x6c, 0x6f, 0x77, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x74, 0x72, 0x79, 0x53, 0x74, 0x72, 0x61,
	0x74, 0x65, 0x67, 0x79, 0x52, 0x0d, 0x72, 0x65, 0x74, 0x72, 0x79, 0x53, 0x74, 0x72, 0x61, 0x74,
	0x65, 0x67, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x77, 0x68, 0x65, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x77, 0x68, 0x65, 0x6e, 0x12, 0x30, 0x0a, 0x06, 0x69, 0x6e, 0x70, 0x75, 0x74,
	0x73, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x77,
	0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6e, 0x70, 0x75, 0x74,
	0x73, 0x52, 0x06, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x6b, 0x69,
	0x70, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x08, 0x52, 0x04, 0x73, 0x6b, 0x69, 0x70, 0x12, 0x3c, 0x0a,
	0x0a, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x73, 0x4c, 0x6f, 0x6f, 0x70, 0x18, 0x0d, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f,
	0x77, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x73, 0x4c, 0x6f, 0x6f, 0x70, 0x52,
	0x0a, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x73, 0x4c, 0x6f, 0x6f, 0x70, 0x22, 0xbc, 0x06, 0x0a, 0x0a,
	0x54, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2e, 0x0a, 0x04, 0x73, 0x70,
	0x65, 0x63, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e,
	0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x61, 0x73, 0x6b,
	0x53, 0x70, 0x65, 0x63, 0x52, 0x04, 0x73, 0x70, 0x65, 0x63, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x3c, 0x0a, 0x05, 0x73, 0x74,
	0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x73,
	0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x61, 0x73,
	0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x12, 0x34, 0x0a, 0x07, 0x73, 0x74, 0x61, 0x72, 0x74, 0x41, 0x74, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x07, 0x73, 0x74, 0x61, 0x72, 0x74, 0x41, 0x74, 0x12, 0x36, 0x0a, 0x08, 0x6d, 0x6f, 0x64, 0x69,
	0x66, 0x79, 0x41, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x08, 0x6d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x41, 0x74,
	0x12, 0x30, 0x0a, 0x06, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77,
	0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x73, 0x52, 0x06, 0x69, 0x6e, 0x70, 0x75,
	0x74, 0x73, 0x12, 0x33, 0x0a, 0x07, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x73, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66,
	0x6c, 0x6f, 0x77, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x73, 0x52, 0x07,
	0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f,
	0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12,
	0x1a, 0x0a, 0x08, 0x63, 0x68, 0x69, 0x6c, 0x64, 0x72, 0x65, 0x6e, 0x18, 0x0a, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x08, 0x63, 0x68, 0x69, 0x6c, 0x64, 0x72, 0x65, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x70,
	0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70,
	0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x12, 0x39, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x77, 0x6f, 0x72,
	0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x12, 0x55, 0x0a, 0x0d, 0x67, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x74,
	0x65, 0x78, 0x74, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x61, 0x70, 0x69, 0x73,
	0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x61, 0x73,
	0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x47, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x43, 0x6f,
	0x6e, 0x74, 0x65, 0x78, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0d, 0x67, 0x6c, 0x6f, 0x62,
	0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x12, 0x22, 0x0a, 0x0c, 0x64, 0x65, 0x70,
	0x65, 0x6e, 0x64, 0x65, 0x6e, 0x63, 0x69, 0x65, 0x73, 0x18, 0x0e, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x0c, 0x64, 0x65, 0x70, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x63, 0x69, 0x65, 0x73, 0x1a, 0x40, 0x0a,
	0x12, 0x47, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22,
	0x23, 0x0a, 0x08, 0x54, 0x61, 0x73, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x12, 0x08, 0x0a, 0x04, 0x53,
	0x74, 0x65, 0x70, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x53, 0x74, 0x65, 0x70, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x10, 0x01, 0x22, 0x54, 0x0a, 0x09, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x12, 0x0b, 0x0a, 0x07, 0x50, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x10, 0x00, 0x12, 0x0b,
	0x0a, 0x07, 0x52, 0x75, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x53,
	0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x10, 0x02, 0x12, 0x0b, 0x0a, 0x07, 0x46, 0x61, 0x69, 0x6c,
	0x75, 0x72, 0x65, 0x10, 0x03, 0x12, 0x09, 0x0a, 0x05, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x10, 0x04,
	0x12, 0x08, 0x0a, 0x04, 0x53, 0x6b, 0x69, 0x70, 0x10, 0x05, 0x22, 0x7b, 0x0a, 0x0d, 0x52, 0x65,
	0x74, 0x72, 0x79, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x12, 0x33, 0x0a, 0x07, 0x62,
	0x61, 0x63, 0x6b, 0x6f, 0x66, 0x66, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61,
	0x70, 0x69, 0x73, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x76, 0x31, 0x2e,
	0x42, 0x61, 0x63, 0x6b, 0x4f, 0x66, 0x66, 0x52, 0x07, 0x62, 0x61, 0x63, 0x6b, 0x6f, 0x66, 0x66,
	0x12, 0x35, 0x0a, 0x06, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77,
	0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x74, 0x72, 0x79, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52,
	0x06, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x22, 0x86, 0x01, 0x0a, 0x07, 0x42, 0x61, 0x63, 0x6b,
	0x4f, 0x66, 0x66, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x65, 0x70, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x05, 0x73, 0x74, 0x65, 0x70, 0x73, 0x12, 0x35, 0x0a, 0x08, 0x64, 0x75, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x16, 0x0a, 0x06, 0x66, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x06, 0x66, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x6a, 0x69, 0x74, 0x74,
	0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x6a, 0x69, 0x74, 0x74, 0x65, 0x72,
	0x22, 0x42, 0x0a, 0x20, 0x47, 0x65, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x73, 0x42, 0x79, 0x54, 0x6f,
	0x70, 0x6f, 0x6c, 0x6f, 0x67, 0x69, 0x63, 0x61, 0x6c, 0x53, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77,
	0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c,
	0x6f, 0x77, 0x49, 0x64, 0x22, 0x57, 0x0a, 0x21, 0x47, 0x65, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x73,
	0x42, 0x79, 0x54, 0x6f, 0x70, 0x6f, 0x6c, 0x6f, 0x67, 0x69, 0x63, 0x61, 0x6c, 0x53, 0x6f, 0x72,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x32, 0x0a, 0x05, 0x74, 0x61, 0x73,
	0x6b, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e,
	0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x61, 0x73, 0x6b,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x05, 0x74, 0x61, 0x73, 0x6b, 0x73, 0x2a, 0x4b, 0x0a,
	0x0b, 0x52, 0x65, 0x74, 0x72, 0x79, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12, 0x0a, 0x0a, 0x06,
	0x41, 0x6c, 0x77, 0x61, 0x79, 0x73, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x4f, 0x6e, 0x46, 0x61,
	0x69, 0x6c, 0x75, 0x72, 0x65, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x4f, 0x6e, 0x45, 0x72, 0x72,
	0x6f, 0x72, 0x10, 0x02, 0x12, 0x14, 0x0a, 0x10, 0x4f, 0x6e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x69,
	0x65, 0x6e, 0x74, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x10, 0x03, 0x32, 0xdc, 0x06, 0x0a, 0x0f, 0x57,
	0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x66,
	0x0a, 0x0e, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77,
	0x12, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77,
	0x2e, 0x76, 0x31, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x1a, 0x1a, 0x2e, 0x61,
	0x70, 0x69, 0x73, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x76, 0x31, 0x2e,
	0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x22, 0x1c, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x16,
	0x3a, 0x01, 0x2a, 0x22, 0x11, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x6f,
	0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x12, 0x86, 0x01, 0x0a, 0x0f, 0x57, 0x6f, 0x72, 0x6b, 0x66,
	0x6c, 0x6f, 0x77, 0x53, 0x75, 0x73, 0x70, 0x65, 0x6e, 0x64, 0x12, 0x28, 0x2e, 0x61, 0x70, 0x69,
	0x73, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x6f,
	0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x53, 0x75, 0x73, 0x70, 0x65, 0x6e, 0x64, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x31, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x2b, 0x3a, 0x01, 0x2a, 0x22, 0x26, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76,
	0x31, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x7b, 0x77, 0x6f, 0x72, 0x6b,
	0x66, 0x6c, 0x6f, 0x77, 0x49, 0x64, 0x7d, 0x2f, 0x73, 0x75, 0x73, 0x70, 0x65, 0x6e, 0x64, 0x12,
	0x83, 0x01, 0x0a, 0x0e, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x65, 0x73, 0x75,
	0x6d, 0x65, 0x12, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c,
	0x6f, 0x77, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x65,
	0x73, 0x75, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x22, 0x30, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2a, 0x3a, 0x01, 0x2a, 0x22, 0x25,
	0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f,
	0x77, 0x2f, 0x7b, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x49, 0x64, 0x7d, 0x2f, 0x72,
	0x65, 0x73, 0x75, 0x6d, 0x65, 0x12, 0x83, 0x01, 0x0a, 0x0e, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c,
	0x6f, 0x77, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x12, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e,
	0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x6f, 0x72, 0x6b,
	0x66, 0x6c, 0x6f, 0x77, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x30, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x2a, 0x3a, 0x01, 0x2a, 0x22, 0x25, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x77,
	0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x7b, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f,
	0x77, 0x49, 0x64, 0x7d, 0x2f, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x12, 0x83, 0x01, 0x0a, 0x11,
	0x47, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x12, 0x2a, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f,
	0x77, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1a, 0x2e,
	0x61, 0x70, 0x69, 0x73, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x76, 0x31,
	0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x22, 0x26, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x20, 0x12, 0x1e, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x6f, 0x72, 0x6b,
	0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x7b, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x49, 0x64,
	0x7d, 0x12, 0xc5, 0x01, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x73, 0x42, 0x79,
	0x54, 0x6f, 0x70, 0x6f, 0x6c, 0x6f, 0x67, 0x69, 0x63, 0x61, 0x6c, 0x53, 0x6f, 0x72, 0x74, 0x12,
	0x32, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e,
	0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x73, 0x42, 0x79, 0x54, 0x6f, 0x70,
	0x6f, 0x6c, 0x6f, 0x67, 0x69, 0x63, 0x61, 0x6c, 0x53, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x33, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66,
	0x6c, 0x6f, 0x77, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x73, 0x42,
	0x79, 0x54, 0x6f, 0x70, 0x6f, 0x6c, 0x6f, 0x67, 0x69, 0x63, 0x61, 0x6c, 0x53, 0x6f, 0x72, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x3f, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x39,
	0x3a, 0x01, 0x2a, 0x22, 0x34, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x6f,
	0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x7b, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77,
	0x49, 0x64, 0x7d, 0x2f, 0x74, 0x61, 0x73, 0x6b, 0x73, 0x2f, 0x74, 0x6f, 0x70, 0x6f, 0x6c, 0x6f,
	0x67, 0x69, 0x63, 0x61, 0x6c, 0x53, 0x6f, 0x72, 0x74, 0x32, 0xf4, 0x02, 0x0a, 0x15, 0x57, 0x6f,
	0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x42, 0x72, 0x6f, 0x6b, 0x65, 0x72, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x12, 0x4a, 0x0a, 0x0e, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x57, 0x6f, 0x72,
	0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x12, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x77, 0x6f, 0x72,
	0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f,
	0x77, 0x1a, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f,
	0x77, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x22, 0x00, 0x12,
	0x57, 0x0a, 0x08, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x21, 0x2e, 0x61, 0x70,
	0x69, 0x73, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x76, 0x31, 0x2e, 0x45,
	0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x22,
	0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x76,
	0x31, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x75, 0x6e, 0x45, 0x76, 0x65,
	0x6e, 0x74, 0x22, 0x00, 0x28, 0x01, 0x30, 0x01, 0x12, 0x5d, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x57,
	0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x2a, 0x2e,
	0x61, 0x70, 0x69, 0x73, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x73,
	0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x6f, 0x72,
	0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x22, 0x00, 0x12, 0x57, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x54, 0x61,
	0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e,
	0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x54,
	0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77,
	0x2e, 0x76, 0x31, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x00,
	0x42, 0x4b, 0x5a, 0x49, 0x67, 0x69, 0x74, 0x2e, 0x6c, 0x69, 0x61, 0x6e, 0x6a, 0x69, 0x61, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x2f,
	0x6b, 0x69, 0x63, 0x2f, 0x6b, 0x69, 0x63, 0x2d, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d,
	0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2f, 0x77,
	0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_aistudio_workflow_v1_workflow_proto_rawDescOnce sync.Once
	file_aistudio_workflow_v1_workflow_proto_rawDescData = file_aistudio_workflow_v1_workflow_proto_rawDesc
)

func file_aistudio_workflow_v1_workflow_proto_rawDescGZIP() []byte {
	file_aistudio_workflow_v1_workflow_proto_rawDescOnce.Do(func() {
		file_aistudio_workflow_v1_workflow_proto_rawDescData = protoimpl.X.CompressGZIP(file_aistudio_workflow_v1_workflow_proto_rawDescData)
	})
	return file_aistudio_workflow_v1_workflow_proto_rawDescData
}

var file_aistudio_workflow_v1_workflow_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_aistudio_workflow_v1_workflow_proto_msgTypes = make([]protoimpl.MessageInfo, 35)
var file_aistudio_workflow_v1_workflow_proto_goTypes = []any{
	(RetryPolicy)(0),                          // 0: apis.workflow.v1.RetryPolicy
	(Workflow_WorkflowState)(0),               // 1: apis.workflow.v1.Workflow.WorkflowState
	(TaskStatus_TaskType)(0),                  // 2: apis.workflow.v1.TaskStatus.TaskType
	(TaskStatus_TaskState)(0),                 // 3: apis.workflow.v1.TaskStatus.TaskState
	(*WorkflowRunEvent)(nil),                  // 4: apis.workflow.v1.WorkflowRunEvent
	(*WorkflowEvent)(nil),                     // 5: apis.workflow.v1.WorkflowEvent
	(*TaskEvent)(nil),                         // 6: apis.workflow.v1.TaskEvent
	(*ExecutableEvent)(nil),                   // 7: apis.workflow.v1.ExecutableEvent
	(*GetTaskStatusRequest)(nil),              // 8: apis.workflow.v1.GetTaskStatusRequest
	(*WorkflowSuspendRequest)(nil),            // 9: apis.workflow.v1.WorkflowSuspendRequest
	(*WorkflowResumeRequest)(nil),             // 10: apis.workflow.v1.WorkflowResumeRequest
	(*WorkflowCancelRequest)(nil),             // 11: apis.workflow.v1.WorkflowCancelRequest
	(*WorkflowStartRequest)(nil),              // 12: apis.workflow.v1.WorkflowStartRequest
	(*GetWorkflowDetailRequest)(nil),          // 13: apis.workflow.v1.GetWorkflowDetailRequest
	(*ExchangeRequest)(nil),                   // 14: apis.workflow.v1.ExchangeRequest
	(*Executable)(nil),                        // 15: apis.workflow.v1.Executable
	(*ExecuteResult)(nil),                     // 16: apis.workflow.v1.ExecuteResult
	(*TaskLog)(nil),                           // 17: apis.workflow.v1.TaskLog
	(*Workflow)(nil),                          // 18: apis.workflow.v1.Workflow
	(*Input)(nil),                             // 19: apis.workflow.v1.Input
	(*Output)(nil),                            // 20: apis.workflow.v1.Output
	(*Inputs)(nil),                            // 21: apis.workflow.v1.Inputs
	(*Outputs)(nil),                           // 22: apis.workflow.v1.Outputs
	(*InputsLoop)(nil),                        // 23: apis.workflow.v1.InputsLoop
	(*TaskSpec)(nil),                          // 24: apis.workflow.v1.TaskSpec
	(*TaskStatus)(nil),                        // 25: apis.workflow.v1.TaskStatus
	(*RetryStrategy)(nil),                     // 26: apis.workflow.v1.RetryStrategy
	(*BackOff)(nil),                           // 27: apis.workflow.v1.BackOff
	(*GetTasksByTopologicalSortRequest)(nil),  // 28: apis.workflow.v1.GetTasksByTopologicalSortRequest
	(*GetTasksByTopologicalSortResponse)(nil), // 29: apis.workflow.v1.GetTasksByTopologicalSortResponse
	nil,                           // 30: apis.workflow.v1.WorkflowEvent.GlobalContextEntry
	nil,                           // 31: apis.workflow.v1.TaskEvent.GlobalContextEntry
	nil,                           // 32: apis.workflow.v1.Executable.GlobalContextEntry
	nil,                           // 33: apis.workflow.v1.ExecuteResult.GlobalContextEntry
	(*Workflow_Spec)(nil),         // 34: apis.workflow.v1.Workflow.Spec
	(*Workflow_Status)(nil),       // 35: apis.workflow.v1.Workflow.Status
	nil,                           // 36: apis.workflow.v1.Workflow.Spec.ArgumentsEntry
	nil,                           // 37: apis.workflow.v1.Workflow.Status.TasksEntry
	nil,                           // 38: apis.workflow.v1.TaskStatus.GlobalContextEntry
	(*timestamppb.Timestamp)(nil), // 39: google.protobuf.Timestamp
	(*durationpb.Duration)(nil),   // 40: google.protobuf.Duration
	(*emptypb.Empty)(nil),         // 41: google.protobuf.Empty
}
var file_aistudio_workflow_v1_workflow_proto_depIdxs = []int32{
	5,  // 0: apis.workflow.v1.WorkflowRunEvent.workflowEvent:type_name -> apis.workflow.v1.WorkflowEvent
	6,  // 1: apis.workflow.v1.WorkflowRunEvent.taskEvent:type_name -> apis.workflow.v1.TaskEvent
	7,  // 2: apis.workflow.v1.WorkflowRunEvent.executableEvent:type_name -> apis.workflow.v1.ExecutableEvent
	30, // 3: apis.workflow.v1.WorkflowEvent.globalContext:type_name -> apis.workflow.v1.WorkflowEvent.GlobalContextEntry
	39, // 4: apis.workflow.v1.WorkflowEvent.timestamp:type_name -> google.protobuf.Timestamp
	1,  // 5: apis.workflow.v1.WorkflowEvent.state:type_name -> apis.workflow.v1.Workflow.WorkflowState
	21, // 6: apis.workflow.v1.TaskEvent.Inputs:type_name -> apis.workflow.v1.Inputs
	31, // 7: apis.workflow.v1.TaskEvent.globalContext:type_name -> apis.workflow.v1.TaskEvent.GlobalContextEntry
	22, // 8: apis.workflow.v1.TaskEvent.outputs:type_name -> apis.workflow.v1.Outputs
	39, // 9: apis.workflow.v1.TaskEvent.timestamp:type_name -> google.protobuf.Timestamp
	3,  // 10: apis.workflow.v1.TaskEvent.state:type_name -> apis.workflow.v1.TaskStatus.TaskState
	17, // 11: apis.workflow.v1.TaskEvent.logs:type_name -> apis.workflow.v1.TaskLog
	15, // 12: apis.workflow.v1.ExecutableEvent.executable:type_name -> apis.workflow.v1.Executable
	12, // 13: apis.workflow.v1.ExchangeRequest.workflowStartRequest:type_name -> apis.workflow.v1.WorkflowStartRequest
	11, // 14: apis.workflow.v1.ExchangeRequest.workflowCancelRequest:type_name -> apis.workflow.v1.WorkflowCancelRequest
	10, // 15: apis.workflow.v1.ExchangeRequest.workflowResumeRequest:type_name -> apis.workflow.v1.WorkflowResumeRequest
	9,  // 16: apis.workflow.v1.ExchangeRequest.workflowSuspendRequest:type_name -> apis.workflow.v1.WorkflowSuspendRequest
	32, // 17: apis.workflow.v1.Executable.globalContext:type_name -> apis.workflow.v1.Executable.GlobalContextEntry
	21, // 18: apis.workflow.v1.Executable.inputs:type_name -> apis.workflow.v1.Inputs
	22, // 19: apis.workflow.v1.ExecuteResult.outputs:type_name -> apis.workflow.v1.Outputs
	33, // 20: apis.workflow.v1.ExecuteResult.globalContext:type_name -> apis.workflow.v1.ExecuteResult.GlobalContextEntry
	3,  // 21: apis.workflow.v1.ExecuteResult.state:type_name -> apis.workflow.v1.TaskStatus.TaskState
	17, // 22: apis.workflow.v1.ExecuteResult.logs:type_name -> apis.workflow.v1.TaskLog
	39, // 23: apis.workflow.v1.ExecuteResult.finishAt:type_name -> google.protobuf.Timestamp
	39, // 24: apis.workflow.v1.TaskLog.timestamp:type_name -> google.protobuf.Timestamp
	34, // 25: apis.workflow.v1.Workflow.spec:type_name -> apis.workflow.v1.Workflow.Spec
	35, // 26: apis.workflow.v1.Workflow.status:type_name -> apis.workflow.v1.Workflow.Status
	19, // 27: apis.workflow.v1.Inputs.items:type_name -> apis.workflow.v1.Input
	20, // 28: apis.workflow.v1.Outputs.items:type_name -> apis.workflow.v1.Output
	21, // 29: apis.workflow.v1.InputsLoop.items:type_name -> apis.workflow.v1.Inputs
	40, // 30: apis.workflow.v1.TaskSpec.timeout:type_name -> google.protobuf.Duration
	26, // 31: apis.workflow.v1.TaskSpec.retryStrategy:type_name -> apis.workflow.v1.RetryStrategy
	21, // 32: apis.workflow.v1.TaskSpec.inputs:type_name -> apis.workflow.v1.Inputs
	23, // 33: apis.workflow.v1.TaskSpec.inputsLoop:type_name -> apis.workflow.v1.InputsLoop
	24, // 34: apis.workflow.v1.TaskStatus.spec:type_name -> apis.workflow.v1.TaskSpec
	3,  // 35: apis.workflow.v1.TaskStatus.state:type_name -> apis.workflow.v1.TaskStatus.TaskState
	39, // 36: apis.workflow.v1.TaskStatus.startAt:type_name -> google.protobuf.Timestamp
	39, // 37: apis.workflow.v1.TaskStatus.modifyAt:type_name -> google.protobuf.Timestamp
	21, // 38: apis.workflow.v1.TaskStatus.inputs:type_name -> apis.workflow.v1.Inputs
	22, // 39: apis.workflow.v1.TaskStatus.outputs:type_name -> apis.workflow.v1.Outputs
	2,  // 40: apis.workflow.v1.TaskStatus.type:type_name -> apis.workflow.v1.TaskStatus.TaskType
	38, // 41: apis.workflow.v1.TaskStatus.globalContext:type_name -> apis.workflow.v1.TaskStatus.GlobalContextEntry
	27, // 42: apis.workflow.v1.RetryStrategy.backoff:type_name -> apis.workflow.v1.BackOff
	0,  // 43: apis.workflow.v1.RetryStrategy.policy:type_name -> apis.workflow.v1.RetryPolicy
	40, // 44: apis.workflow.v1.BackOff.duration:type_name -> google.protobuf.Duration
	25, // 45: apis.workflow.v1.GetTasksByTopologicalSortResponse.tasks:type_name -> apis.workflow.v1.TaskStatus
	24, // 46: apis.workflow.v1.Workflow.Spec.tasks:type_name -> apis.workflow.v1.TaskSpec
	36, // 47: apis.workflow.v1.Workflow.Spec.arguments:type_name -> apis.workflow.v1.Workflow.Spec.ArgumentsEntry
	40, // 48: apis.workflow.v1.Workflow.Spec.timeout:type_name -> google.protobuf.Duration
	1,  // 49: apis.workflow.v1.Workflow.Status.state:type_name -> apis.workflow.v1.Workflow.WorkflowState
	39, // 50: apis.workflow.v1.Workflow.Status.startAt:type_name -> google.protobuf.Timestamp
	39, // 51: apis.workflow.v1.Workflow.Status.finishAt:type_name -> google.protobuf.Timestamp
	37, // 52: apis.workflow.v1.Workflow.Status.tasks:type_name -> apis.workflow.v1.Workflow.Status.TasksEntry
	25, // 53: apis.workflow.v1.Workflow.Status.TasksEntry.value:type_name -> apis.workflow.v1.TaskStatus
	18, // 54: apis.workflow.v1.WorkflowService.SubmitWorkflow:input_type -> apis.workflow.v1.Workflow
	9,  // 55: apis.workflow.v1.WorkflowService.WorkflowSuspend:input_type -> apis.workflow.v1.WorkflowSuspendRequest
	10, // 56: apis.workflow.v1.WorkflowService.WorkflowResume:input_type -> apis.workflow.v1.WorkflowResumeRequest
	11, // 57: apis.workflow.v1.WorkflowService.WorkflowCancel:input_type -> apis.workflow.v1.WorkflowCancelRequest
	13, // 58: apis.workflow.v1.WorkflowService.GetWorkflowDetail:input_type -> apis.workflow.v1.GetWorkflowDetailRequest
	28, // 59: apis.workflow.v1.WorkflowService.GetTasksByTopologicalSort:input_type -> apis.workflow.v1.GetTasksByTopologicalSortRequest
	18, // 60: apis.workflow.v1.WorkflowBrokerService.SubmitWorkflow:input_type -> apis.workflow.v1.Workflow
	14, // 61: apis.workflow.v1.WorkflowBrokerService.Exchange:input_type -> apis.workflow.v1.ExchangeRequest
	13, // 62: apis.workflow.v1.WorkflowBrokerService.GetWorkflowDetail:input_type -> apis.workflow.v1.GetWorkflowDetailRequest
	8,  // 63: apis.workflow.v1.WorkflowBrokerService.GetTaskStatus:input_type -> apis.workflow.v1.GetTaskStatusRequest
	18, // 64: apis.workflow.v1.WorkflowService.SubmitWorkflow:output_type -> apis.workflow.v1.Workflow
	41, // 65: apis.workflow.v1.WorkflowService.WorkflowSuspend:output_type -> google.protobuf.Empty
	41, // 66: apis.workflow.v1.WorkflowService.WorkflowResume:output_type -> google.protobuf.Empty
	41, // 67: apis.workflow.v1.WorkflowService.WorkflowCancel:output_type -> google.protobuf.Empty
	18, // 68: apis.workflow.v1.WorkflowService.GetWorkflowDetail:output_type -> apis.workflow.v1.Workflow
	29, // 69: apis.workflow.v1.WorkflowService.GetTasksByTopologicalSort:output_type -> apis.workflow.v1.GetTasksByTopologicalSortResponse
	18, // 70: apis.workflow.v1.WorkflowBrokerService.SubmitWorkflow:output_type -> apis.workflow.v1.Workflow
	4,  // 71: apis.workflow.v1.WorkflowBrokerService.Exchange:output_type -> apis.workflow.v1.WorkflowRunEvent
	18, // 72: apis.workflow.v1.WorkflowBrokerService.GetWorkflowDetail:output_type -> apis.workflow.v1.Workflow
	25, // 73: apis.workflow.v1.WorkflowBrokerService.GetTaskStatus:output_type -> apis.workflow.v1.TaskStatus
	64, // [64:74] is the sub-list for method output_type
	54, // [54:64] is the sub-list for method input_type
	54, // [54:54] is the sub-list for extension type_name
	54, // [54:54] is the sub-list for extension extendee
	0,  // [0:54] is the sub-list for field type_name
}

func init() { file_aistudio_workflow_v1_workflow_proto_init() }
func file_aistudio_workflow_v1_workflow_proto_init() {
	if File_aistudio_workflow_v1_workflow_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_aistudio_workflow_v1_workflow_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*WorkflowRunEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_workflow_v1_workflow_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*WorkflowEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_workflow_v1_workflow_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*TaskEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_workflow_v1_workflow_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*ExecutableEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_workflow_v1_workflow_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*GetTaskStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_workflow_v1_workflow_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*WorkflowSuspendRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_workflow_v1_workflow_proto_msgTypes[6].Exporter = func(v any, i int) any {
			switch v := v.(*WorkflowResumeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_workflow_v1_workflow_proto_msgTypes[7].Exporter = func(v any, i int) any {
			switch v := v.(*WorkflowCancelRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_workflow_v1_workflow_proto_msgTypes[8].Exporter = func(v any, i int) any {
			switch v := v.(*WorkflowStartRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_workflow_v1_workflow_proto_msgTypes[9].Exporter = func(v any, i int) any {
			switch v := v.(*GetWorkflowDetailRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_workflow_v1_workflow_proto_msgTypes[10].Exporter = func(v any, i int) any {
			switch v := v.(*ExchangeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_workflow_v1_workflow_proto_msgTypes[11].Exporter = func(v any, i int) any {
			switch v := v.(*Executable); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_workflow_v1_workflow_proto_msgTypes[12].Exporter = func(v any, i int) any {
			switch v := v.(*ExecuteResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_workflow_v1_workflow_proto_msgTypes[13].Exporter = func(v any, i int) any {
			switch v := v.(*TaskLog); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_workflow_v1_workflow_proto_msgTypes[14].Exporter = func(v any, i int) any {
			switch v := v.(*Workflow); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_workflow_v1_workflow_proto_msgTypes[15].Exporter = func(v any, i int) any {
			switch v := v.(*Input); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_workflow_v1_workflow_proto_msgTypes[16].Exporter = func(v any, i int) any {
			switch v := v.(*Output); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_workflow_v1_workflow_proto_msgTypes[17].Exporter = func(v any, i int) any {
			switch v := v.(*Inputs); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_workflow_v1_workflow_proto_msgTypes[18].Exporter = func(v any, i int) any {
			switch v := v.(*Outputs); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_workflow_v1_workflow_proto_msgTypes[19].Exporter = func(v any, i int) any {
			switch v := v.(*InputsLoop); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_workflow_v1_workflow_proto_msgTypes[20].Exporter = func(v any, i int) any {
			switch v := v.(*TaskSpec); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_workflow_v1_workflow_proto_msgTypes[21].Exporter = func(v any, i int) any {
			switch v := v.(*TaskStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_workflow_v1_workflow_proto_msgTypes[22].Exporter = func(v any, i int) any {
			switch v := v.(*RetryStrategy); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_workflow_v1_workflow_proto_msgTypes[23].Exporter = func(v any, i int) any {
			switch v := v.(*BackOff); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_workflow_v1_workflow_proto_msgTypes[24].Exporter = func(v any, i int) any {
			switch v := v.(*GetTasksByTopologicalSortRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_workflow_v1_workflow_proto_msgTypes[25].Exporter = func(v any, i int) any {
			switch v := v.(*GetTasksByTopologicalSortResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_workflow_v1_workflow_proto_msgTypes[30].Exporter = func(v any, i int) any {
			switch v := v.(*Workflow_Spec); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_workflow_v1_workflow_proto_msgTypes[31].Exporter = func(v any, i int) any {
			switch v := v.(*Workflow_Status); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_aistudio_workflow_v1_workflow_proto_msgTypes[0].OneofWrappers = []any{
		(*WorkflowRunEvent_WorkflowEvent)(nil),
		(*WorkflowRunEvent_TaskEvent)(nil),
		(*WorkflowRunEvent_ExecutableEvent)(nil),
	}
	file_aistudio_workflow_v1_workflow_proto_msgTypes[10].OneofWrappers = []any{
		(*ExchangeRequest_WorkflowStartRequest)(nil),
		(*ExchangeRequest_WorkflowCancelRequest)(nil),
		(*ExchangeRequest_WorkflowResumeRequest)(nil),
		(*ExchangeRequest_WorkflowSuspendRequest)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_aistudio_workflow_v1_workflow_proto_rawDesc,
			NumEnums:      4,
			NumMessages:   35,
			NumExtensions: 0,
			NumServices:   2,
		},
		GoTypes:           file_aistudio_workflow_v1_workflow_proto_goTypes,
		DependencyIndexes: file_aistudio_workflow_v1_workflow_proto_depIdxs,
		EnumInfos:         file_aistudio_workflow_v1_workflow_proto_enumTypes,
		MessageInfos:      file_aistudio_workflow_v1_workflow_proto_msgTypes,
	}.Build()
	File_aistudio_workflow_v1_workflow_proto = out.File
	file_aistudio_workflow_v1_workflow_proto_rawDesc = nil
	file_aistudio_workflow_v1_workflow_proto_goTypes = nil
	file_aistudio_workflow_v1_workflow_proto_depIdxs = nil
}
