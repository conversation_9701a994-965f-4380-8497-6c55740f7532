// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v5.29.3
// source: aistudio/dataset/v1/dataset.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	DatasetService_CreateOrUpdateDataset_FullMethodName = "/apis.aistudio.dataset.v1.DatasetService/CreateOrUpdateDataset"
	DatasetService_UpdateDataset_FullMethodName         = "/apis.aistudio.dataset.v1.DatasetService/UpdateDataset"
	DatasetService_GetDataset_FullMethodName            = "/apis.aistudio.dataset.v1.DatasetService/GetDataset"
	DatasetService_ListDatasets_FullMethodName          = "/apis.aistudio.dataset.v1.DatasetService/ListDatasets"
	DatasetService_DeleteDataset_FullMethodName         = "/apis.aistudio.dataset.v1.DatasetService/DeleteDataset"
)

// DatasetServiceClient is the client API for DatasetService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type DatasetServiceClient interface {
	CreateOrUpdateDataset(ctx context.Context, in *CreateDatasetRequest, opts ...grpc.CallOption) (*Dataset, error)
	UpdateDataset(ctx context.Context, in *CreateDatasetRequest, opts ...grpc.CallOption) (*Dataset, error)
	GetDataset(ctx context.Context, in *GetDatasetRequest, opts ...grpc.CallOption) (*Dataset, error)
	ListDatasets(ctx context.Context, in *ListDatasetOptions, opts ...grpc.CallOption) (*ListDatasetsResponse, error)
	DeleteDataset(ctx context.Context, in *DeleteDatasetRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
}

type datasetServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewDatasetServiceClient(cc grpc.ClientConnInterface) DatasetServiceClient {
	return &datasetServiceClient{cc}
}

func (c *datasetServiceClient) CreateOrUpdateDataset(ctx context.Context, in *CreateDatasetRequest, opts ...grpc.CallOption) (*Dataset, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(Dataset)
	err := c.cc.Invoke(ctx, DatasetService_CreateOrUpdateDataset_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *datasetServiceClient) UpdateDataset(ctx context.Context, in *CreateDatasetRequest, opts ...grpc.CallOption) (*Dataset, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(Dataset)
	err := c.cc.Invoke(ctx, DatasetService_UpdateDataset_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *datasetServiceClient) GetDataset(ctx context.Context, in *GetDatasetRequest, opts ...grpc.CallOption) (*Dataset, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(Dataset)
	err := c.cc.Invoke(ctx, DatasetService_GetDataset_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *datasetServiceClient) ListDatasets(ctx context.Context, in *ListDatasetOptions, opts ...grpc.CallOption) (*ListDatasetsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListDatasetsResponse)
	err := c.cc.Invoke(ctx, DatasetService_ListDatasets_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *datasetServiceClient) DeleteDataset(ctx context.Context, in *DeleteDatasetRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, DatasetService_DeleteDataset_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// DatasetServiceServer is the server API for DatasetService service.
// All implementations must embed UnimplementedDatasetServiceServer
// for forward compatibility
type DatasetServiceServer interface {
	CreateOrUpdateDataset(context.Context, *CreateDatasetRequest) (*Dataset, error)
	UpdateDataset(context.Context, *CreateDatasetRequest) (*Dataset, error)
	GetDataset(context.Context, *GetDatasetRequest) (*Dataset, error)
	ListDatasets(context.Context, *ListDatasetOptions) (*ListDatasetsResponse, error)
	DeleteDataset(context.Context, *DeleteDatasetRequest) (*emptypb.Empty, error)
	mustEmbedUnimplementedDatasetServiceServer()
}

// UnimplementedDatasetServiceServer must be embedded to have forward compatible implementations.
type UnimplementedDatasetServiceServer struct {
}

func (UnimplementedDatasetServiceServer) CreateOrUpdateDataset(context.Context, *CreateDatasetRequest) (*Dataset, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateOrUpdateDataset not implemented")
}
func (UnimplementedDatasetServiceServer) UpdateDataset(context.Context, *CreateDatasetRequest) (*Dataset, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateDataset not implemented")
}
func (UnimplementedDatasetServiceServer) GetDataset(context.Context, *GetDatasetRequest) (*Dataset, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDataset not implemented")
}
func (UnimplementedDatasetServiceServer) ListDatasets(context.Context, *ListDatasetOptions) (*ListDatasetsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListDatasets not implemented")
}
func (UnimplementedDatasetServiceServer) DeleteDataset(context.Context, *DeleteDatasetRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteDataset not implemented")
}
func (UnimplementedDatasetServiceServer) mustEmbedUnimplementedDatasetServiceServer() {}

// UnsafeDatasetServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to DatasetServiceServer will
// result in compilation errors.
type UnsafeDatasetServiceServer interface {
	mustEmbedUnimplementedDatasetServiceServer()
}

func RegisterDatasetServiceServer(s grpc.ServiceRegistrar, srv DatasetServiceServer) {
	s.RegisterService(&DatasetService_ServiceDesc, srv)
}

func _DatasetService_CreateOrUpdateDataset_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateDatasetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DatasetServiceServer).CreateOrUpdateDataset(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DatasetService_CreateOrUpdateDataset_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DatasetServiceServer).CreateOrUpdateDataset(ctx, req.(*CreateDatasetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DatasetService_UpdateDataset_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateDatasetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DatasetServiceServer).UpdateDataset(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DatasetService_UpdateDataset_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DatasetServiceServer).UpdateDataset(ctx, req.(*CreateDatasetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DatasetService_GetDataset_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDatasetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DatasetServiceServer).GetDataset(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DatasetService_GetDataset_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DatasetServiceServer).GetDataset(ctx, req.(*GetDatasetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DatasetService_ListDatasets_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListDatasetOptions)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DatasetServiceServer).ListDatasets(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DatasetService_ListDatasets_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DatasetServiceServer).ListDatasets(ctx, req.(*ListDatasetOptions))
	}
	return interceptor(ctx, in, info, handler)
}

func _DatasetService_DeleteDataset_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteDatasetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DatasetServiceServer).DeleteDataset(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DatasetService_DeleteDataset_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DatasetServiceServer).DeleteDataset(ctx, req.(*DeleteDatasetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// DatasetService_ServiceDesc is the grpc.ServiceDesc for DatasetService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var DatasetService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "apis.aistudio.dataset.v1.DatasetService",
	HandlerType: (*DatasetServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateOrUpdateDataset",
			Handler:    _DatasetService_CreateOrUpdateDataset_Handler,
		},
		{
			MethodName: "UpdateDataset",
			Handler:    _DatasetService_UpdateDataset_Handler,
		},
		{
			MethodName: "GetDataset",
			Handler:    _DatasetService_GetDataset_Handler,
		},
		{
			MethodName: "ListDatasets",
			Handler:    _DatasetService_ListDatasets_Handler,
		},
		{
			MethodName: "DeleteDataset",
			Handler:    _DatasetService_DeleteDataset_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "aistudio/dataset/v1/dataset.proto",
}
