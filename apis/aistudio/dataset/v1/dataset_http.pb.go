// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.3
// - protoc             v5.29.3
// source: aistudio/dataset/v1/dataset.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationDatasetServiceCreateOrUpdateDataset = "/apis.aistudio.dataset.v1.DatasetService/CreateOrUpdateDataset"
const OperationDatasetServiceDeleteDataset = "/apis.aistudio.dataset.v1.DatasetService/DeleteDataset"
const OperationDatasetServiceGetDataset = "/apis.aistudio.dataset.v1.DatasetService/GetDataset"
const OperationDatasetServiceListDatasets = "/apis.aistudio.dataset.v1.DatasetService/ListDatasets"
const OperationDatasetServiceUpdateDataset = "/apis.aistudio.dataset.v1.DatasetService/UpdateDataset"

type DatasetServiceHTTPServer interface {
	CreateOrUpdateDataset(context.Context, *CreateDatasetRequest) (*Dataset, error)
	DeleteDataset(context.Context, *DeleteDatasetRequest) (*emptypb.Empty, error)
	GetDataset(context.Context, *GetDatasetRequest) (*Dataset, error)
	ListDatasets(context.Context, *ListDatasetOptions) (*ListDatasetsResponse, error)
	UpdateDataset(context.Context, *CreateDatasetRequest) (*Dataset, error)
}

func RegisterDatasetServiceHTTPServer(s *http.Server, srv DatasetServiceHTTPServer) {
	r := s.Route("/")
	r.POST("/apis/v1/workspace/{workspaceName}/dataset", _DatasetService_CreateOrUpdateDataset0_HTTP_Handler(srv))
	r.PUT("/apis/v1/workspace/{workspaceName}/datasets/{name}", _DatasetService_UpdateDataset0_HTTP_Handler(srv))
	r.GET("/apis/v1/workspace/{workspaceName}/datasets/{name}", _DatasetService_GetDataset0_HTTP_Handler(srv))
	r.GET("/apis/v1/workspace/{workspaceName}/datasets", _DatasetService_ListDatasets0_HTTP_Handler(srv))
	r.DELETE("/apis/v1/workspace/{workspaceName}/datasets/{name}", _DatasetService_DeleteDataset0_HTTP_Handler(srv))
}

func _DatasetService_CreateOrUpdateDataset0_HTTP_Handler(srv DatasetServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateDatasetRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationDatasetServiceCreateOrUpdateDataset)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateOrUpdateDataset(ctx, req.(*CreateDatasetRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Dataset)
		return ctx.Result(200, reply)
	}
}

func _DatasetService_UpdateDataset0_HTTP_Handler(srv DatasetServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateDatasetRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationDatasetServiceUpdateDataset)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateDataset(ctx, req.(*CreateDatasetRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Dataset)
		return ctx.Result(200, reply)
	}
}

func _DatasetService_GetDataset0_HTTP_Handler(srv DatasetServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetDatasetRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationDatasetServiceGetDataset)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetDataset(ctx, req.(*GetDatasetRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Dataset)
		return ctx.Result(200, reply)
	}
}

func _DatasetService_ListDatasets0_HTTP_Handler(srv DatasetServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListDatasetOptions
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationDatasetServiceListDatasets)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListDatasets(ctx, req.(*ListDatasetOptions))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListDatasetsResponse)
		return ctx.Result(200, reply)
	}
}

func _DatasetService_DeleteDataset0_HTTP_Handler(srv DatasetServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteDatasetRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationDatasetServiceDeleteDataset)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteDataset(ctx, req.(*DeleteDatasetRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

type DatasetServiceHTTPClient interface {
	CreateOrUpdateDataset(ctx context.Context, req *CreateDatasetRequest, opts ...http.CallOption) (rsp *Dataset, err error)
	DeleteDataset(ctx context.Context, req *DeleteDatasetRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	GetDataset(ctx context.Context, req *GetDatasetRequest, opts ...http.CallOption) (rsp *Dataset, err error)
	ListDatasets(ctx context.Context, req *ListDatasetOptions, opts ...http.CallOption) (rsp *ListDatasetsResponse, err error)
	UpdateDataset(ctx context.Context, req *CreateDatasetRequest, opts ...http.CallOption) (rsp *Dataset, err error)
}

type DatasetServiceHTTPClientImpl struct {
	cc *http.Client
}

func NewDatasetServiceHTTPClient(client *http.Client) DatasetServiceHTTPClient {
	return &DatasetServiceHTTPClientImpl{client}
}

func (c *DatasetServiceHTTPClientImpl) CreateOrUpdateDataset(ctx context.Context, in *CreateDatasetRequest, opts ...http.CallOption) (*Dataset, error) {
	var out Dataset
	pattern := "/apis/v1/workspace/{workspaceName}/dataset"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationDatasetServiceCreateOrUpdateDataset))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *DatasetServiceHTTPClientImpl) DeleteDataset(ctx context.Context, in *DeleteDatasetRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/apis/v1/workspace/{workspaceName}/datasets/{name}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationDatasetServiceDeleteDataset))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *DatasetServiceHTTPClientImpl) GetDataset(ctx context.Context, in *GetDatasetRequest, opts ...http.CallOption) (*Dataset, error) {
	var out Dataset
	pattern := "/apis/v1/workspace/{workspaceName}/datasets/{name}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationDatasetServiceGetDataset))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *DatasetServiceHTTPClientImpl) ListDatasets(ctx context.Context, in *ListDatasetOptions, opts ...http.CallOption) (*ListDatasetsResponse, error) {
	var out ListDatasetsResponse
	pattern := "/apis/v1/workspace/{workspaceName}/datasets"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationDatasetServiceListDatasets))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *DatasetServiceHTTPClientImpl) UpdateDataset(ctx context.Context, in *CreateDatasetRequest, opts ...http.CallOption) (*Dataset, error) {
	var out Dataset
	pattern := "/apis/v1/workspace/{workspaceName}/datasets/{name}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationDatasetServiceUpdateDataset))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
