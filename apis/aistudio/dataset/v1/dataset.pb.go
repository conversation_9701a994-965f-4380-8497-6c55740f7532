// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v5.29.3
// source: aistudio/dataset/v1/dataset.proto

package v1

import (
	common "git.lianjia.com/cloudnative/kic/kic-platform/apis/common"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	_ "google.golang.org/protobuf/types/known/durationpb"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type FileSystemType int32

const (
	FileSystemType_CloudFs FileSystemType = 0
	FileSystemType_CubeFs  FileSystemType = 1
)

// Enum value maps for FileSystemType.
var (
	FileSystemType_name = map[int32]string{
		0: "CloudFs",
		1: "CubeFs",
	}
	FileSystemType_value = map[string]int32{
		"CloudFs": 0,
		"CubeFs":  1,
	}
)

func (x FileSystemType) Enum() *FileSystemType {
	p := new(FileSystemType)
	*p = x
	return p
}

func (x FileSystemType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FileSystemType) Descriptor() protoreflect.EnumDescriptor {
	return file_aistudio_dataset_v1_dataset_proto_enumTypes[0].Descriptor()
}

func (FileSystemType) Type() protoreflect.EnumType {
	return &file_aistudio_dataset_v1_dataset_proto_enumTypes[0]
}

func (x FileSystemType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FileSystemType.Descriptor instead.
func (FileSystemType) EnumDescriptor() ([]byte, []int) {
	return file_aistudio_dataset_v1_dataset_proto_rawDescGZIP(), []int{0}
}

// fluid dataset 状态
type DatasetStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UfsTotal         string `protobuf:"bytes,1,opt,name=ufsTotal,proto3" json:"ufsTotal,omitempty"`
	Cached           string `protobuf:"bytes,2,opt,name=cached,proto3" json:"cached,omitempty"`
	CacheCapacity    string `protobuf:"bytes,3,opt,name=cacheCapacity,proto3" json:"cacheCapacity,omitempty"`
	CachedPercentage string `protobuf:"bytes,4,opt,name=cachedPercentage,proto3" json:"cachedPercentage,omitempty"`
	Phase            string `protobuf:"bytes,5,opt,name=phase,proto3" json:"phase,omitempty"`
}

func (x *DatasetStatus) Reset() {
	*x = DatasetStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_dataset_v1_dataset_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DatasetStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DatasetStatus) ProtoMessage() {}

func (x *DatasetStatus) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_dataset_v1_dataset_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DatasetStatus.ProtoReflect.Descriptor instead.
func (*DatasetStatus) Descriptor() ([]byte, []int) {
	return file_aistudio_dataset_v1_dataset_proto_rawDescGZIP(), []int{0}
}

func (x *DatasetStatus) GetUfsTotal() string {
	if x != nil {
		return x.UfsTotal
	}
	return ""
}

func (x *DatasetStatus) GetCached() string {
	if x != nil {
		return x.Cached
	}
	return ""
}

func (x *DatasetStatus) GetCacheCapacity() string {
	if x != nil {
		return x.CacheCapacity
	}
	return ""
}

func (x *DatasetStatus) GetCachedPercentage() string {
	if x != nil {
		return x.CachedPercentage
	}
	return ""
}

func (x *DatasetStatus) GetPhase() string {
	if x != nil {
		return x.Phase
	}
	return ""
}

type Dataset struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name        string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	DisplayName string                 `protobuf:"bytes,2,opt,name=displayName,proto3" json:"displayName,omitempty"`
	Description string                 `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	FsType      FileSystemType         `protobuf:"varint,4,opt,name=fsType,proto3,enum=apis.aistudio.dataset.v1.FileSystemType" json:"fsType,omitempty"`
	FsConfig    *FileSystemConfig      `protobuf:"bytes,5,opt,name=fsConfig,proto3" json:"fsConfig,omitempty"`
	MountPath   string                 `protobuf:"bytes,6,opt,name=mountPath,proto3" json:"mountPath,omitempty"`
	Creator     string                 `protobuf:"bytes,7,opt,name=creator,proto3" json:"creator,omitempty"`
	Status      *DatasetStatus         `protobuf:"bytes,8,opt,name=status,proto3" json:"status,omitempty"`
	Timestamp   *common.TimestampModel `protobuf:"bytes,9,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
}

func (x *Dataset) Reset() {
	*x = Dataset{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_dataset_v1_dataset_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Dataset) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Dataset) ProtoMessage() {}

func (x *Dataset) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_dataset_v1_dataset_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Dataset.ProtoReflect.Descriptor instead.
func (*Dataset) Descriptor() ([]byte, []int) {
	return file_aistudio_dataset_v1_dataset_proto_rawDescGZIP(), []int{1}
}

func (x *Dataset) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Dataset) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *Dataset) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Dataset) GetFsType() FileSystemType {
	if x != nil {
		return x.FsType
	}
	return FileSystemType_CloudFs
}

func (x *Dataset) GetFsConfig() *FileSystemConfig {
	if x != nil {
		return x.FsConfig
	}
	return nil
}

func (x *Dataset) GetMountPath() string {
	if x != nil {
		return x.MountPath
	}
	return ""
}

func (x *Dataset) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *Dataset) GetStatus() *DatasetStatus {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *Dataset) GetTimestamp() *common.TimestampModel {
	if x != nil {
		return x.Timestamp
	}
	return nil
}

type FileSystemConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	VolumeName   string `protobuf:"bytes,1,opt,name=volumeName,proto3" json:"volumeName,omitempty"`     //卷名称
	VolumeSubdir string `protobuf:"bytes,2,opt,name=volumeSubdir,proto3" json:"volumeSubdir,omitempty"` //卷子目录
	Path         string `protobuf:"bytes,3,opt,name=path,proto3" json:"path,omitempty"`                 //路径
}

func (x *FileSystemConfig) Reset() {
	*x = FileSystemConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_dataset_v1_dataset_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FileSystemConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FileSystemConfig) ProtoMessage() {}

func (x *FileSystemConfig) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_dataset_v1_dataset_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FileSystemConfig.ProtoReflect.Descriptor instead.
func (*FileSystemConfig) Descriptor() ([]byte, []int) {
	return file_aistudio_dataset_v1_dataset_proto_rawDescGZIP(), []int{2}
}

func (x *FileSystemConfig) GetVolumeName() string {
	if x != nil {
		return x.VolumeName
	}
	return ""
}

func (x *FileSystemConfig) GetVolumeSubdir() string {
	if x != nil {
		return x.VolumeSubdir
	}
	return ""
}

func (x *FileSystemConfig) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

type CreateDatasetRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Region        string            `protobuf:"bytes,1,opt,name=region,proto3" json:"region,omitempty"`
	WorkspaceName string            `protobuf:"bytes,2,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Name          string            `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	DisplayName   string            `protobuf:"bytes,4,opt,name=displayName,proto3" json:"displayName,omitempty"`
	Description   string            `protobuf:"bytes,5,opt,name=description,proto3" json:"description,omitempty"`
	FsType        FileSystemType    `protobuf:"varint,6,opt,name=fsType,proto3,enum=apis.aistudio.dataset.v1.FileSystemType" json:"fsType,omitempty"`
	FsConfig      *FileSystemConfig `protobuf:"bytes,7,opt,name=fsConfig,proto3" json:"fsConfig,omitempty"`
	MountPath     string            `protobuf:"bytes,8,opt,name=mountPath,proto3" json:"mountPath,omitempty"` // 本地挂载目录
	ReadOnly      bool              `protobuf:"varint,9,opt,name=readOnly,proto3" json:"readOnly,omitempty"`  // 是否只读
}

func (x *CreateDatasetRequest) Reset() {
	*x = CreateDatasetRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_dataset_v1_dataset_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateDatasetRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateDatasetRequest) ProtoMessage() {}

func (x *CreateDatasetRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_dataset_v1_dataset_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateDatasetRequest.ProtoReflect.Descriptor instead.
func (*CreateDatasetRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_dataset_v1_dataset_proto_rawDescGZIP(), []int{3}
}

func (x *CreateDatasetRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *CreateDatasetRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *CreateDatasetRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateDatasetRequest) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *CreateDatasetRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CreateDatasetRequest) GetFsType() FileSystemType {
	if x != nil {
		return x.FsType
	}
	return FileSystemType_CloudFs
}

func (x *CreateDatasetRequest) GetFsConfig() *FileSystemConfig {
	if x != nil {
		return x.FsConfig
	}
	return nil
}

func (x *CreateDatasetRequest) GetMountPath() string {
	if x != nil {
		return x.MountPath
	}
	return ""
}

func (x *CreateDatasetRequest) GetReadOnly() bool {
	if x != nil {
		return x.ReadOnly
	}
	return false
}

type UpdateDatasetRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Region        string `protobuf:"bytes,1,opt,name=region,proto3" json:"region,omitempty"`
	WorkspaceName string `protobuf:"bytes,2,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	DisplayName   string `protobuf:"bytes,4,opt,name=displayName,proto3" json:"displayName,omitempty"`
	Description   string `protobuf:"bytes,5,opt,name=description,proto3" json:"description,omitempty"`
	MountPath     string `protobuf:"bytes,8,opt,name=mountPath,proto3" json:"mountPath,omitempty"` // 本地挂载目录
}

func (x *UpdateDatasetRequest) Reset() {
	*x = UpdateDatasetRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_dataset_v1_dataset_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateDatasetRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateDatasetRequest) ProtoMessage() {}

func (x *UpdateDatasetRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_dataset_v1_dataset_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateDatasetRequest.ProtoReflect.Descriptor instead.
func (*UpdateDatasetRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_dataset_v1_dataset_proto_rawDescGZIP(), []int{4}
}

func (x *UpdateDatasetRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *UpdateDatasetRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *UpdateDatasetRequest) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *UpdateDatasetRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *UpdateDatasetRequest) GetMountPath() string {
	if x != nil {
		return x.MountPath
	}
	return ""
}

type GetDatasetRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Region        string `protobuf:"bytes,1,opt,name=region,proto3" json:"region,omitempty"`
	WorkspaceName string `protobuf:"bytes,2,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Name          string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *GetDatasetRequest) Reset() {
	*x = GetDatasetRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_dataset_v1_dataset_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDatasetRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDatasetRequest) ProtoMessage() {}

func (x *GetDatasetRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_dataset_v1_dataset_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDatasetRequest.ProtoReflect.Descriptor instead.
func (*GetDatasetRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_dataset_v1_dataset_proto_rawDescGZIP(), []int{5}
}

func (x *GetDatasetRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *GetDatasetRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *GetDatasetRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type DeleteDatasetRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Region        string `protobuf:"bytes,1,opt,name=region,proto3" json:"region,omitempty"`
	WorkspaceName string `protobuf:"bytes,2,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Name          string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *DeleteDatasetRequest) Reset() {
	*x = DeleteDatasetRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_dataset_v1_dataset_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteDatasetRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteDatasetRequest) ProtoMessage() {}

func (x *DeleteDatasetRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_dataset_v1_dataset_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteDatasetRequest.ProtoReflect.Descriptor instead.
func (*DeleteDatasetRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_dataset_v1_dataset_proto_rawDescGZIP(), []int{6}
}

func (x *DeleteDatasetRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *DeleteDatasetRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *DeleteDatasetRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type ListDatasetOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Region        string         `protobuf:"bytes,1,opt,name=region,proto3" json:"region,omitempty"`
	WorkspaceName string         `protobuf:"bytes,2,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Creator       string         `protobuf:"bytes,3,opt,name=creator,proto3" json:"creator,omitempty"`
	FsType        FileSystemType `protobuf:"varint,4,opt,name=fsType,proto3,enum=apis.aistudio.dataset.v1.FileSystemType" json:"fsType,omitempty"`
}

func (x *ListDatasetOptions) Reset() {
	*x = ListDatasetOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_dataset_v1_dataset_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListDatasetOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDatasetOptions) ProtoMessage() {}

func (x *ListDatasetOptions) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_dataset_v1_dataset_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDatasetOptions.ProtoReflect.Descriptor instead.
func (*ListDatasetOptions) Descriptor() ([]byte, []int) {
	return file_aistudio_dataset_v1_dataset_proto_rawDescGZIP(), []int{7}
}

func (x *ListDatasetOptions) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *ListDatasetOptions) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *ListDatasetOptions) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *ListDatasetOptions) GetFsType() FileSystemType {
	if x != nil {
		return x.FsType
	}
	return FileSystemType_CloudFs
}

type ListDatasetsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Datasets []*Dataset `protobuf:"bytes,1,rep,name=datasets,proto3" json:"datasets,omitempty"`
}

func (x *ListDatasetsResponse) Reset() {
	*x = ListDatasetsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_dataset_v1_dataset_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListDatasetsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDatasetsResponse) ProtoMessage() {}

func (x *ListDatasetsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_dataset_v1_dataset_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDatasetsResponse.ProtoReflect.Descriptor instead.
func (*ListDatasetsResponse) Descriptor() ([]byte, []int) {
	return file_aistudio_dataset_v1_dataset_proto_rawDescGZIP(), []int{8}
}

func (x *ListDatasetsResponse) GetDatasets() []*Dataset {
	if x != nil {
		return x.Datasets
	}
	return nil
}

var File_aistudio_dataset_v1_dataset_proto protoreflect.FileDescriptor

var file_aistudio_dataset_v1_dataset_proto_rawDesc = []byte{
	0x0a, 0x21, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x73,
	0x65, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x18, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64,
	0x69, 0x6f, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x64, 0x75, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x13, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xab, 0x01,
	0x0a, 0x0d, 0x44, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x1a, 0x0a, 0x08, 0x75, 0x66, 0x73, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x75, 0x66, 0x73, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x63,
	0x61, 0x63, 0x68, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x63,
	0x68, 0x65, 0x64, 0x12, 0x24, 0x0a, 0x0d, 0x63, 0x61, 0x63, 0x68, 0x65, 0x43, 0x61, 0x70, 0x61,
	0x63, 0x69, 0x74, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x61, 0x63, 0x68,
	0x65, 0x43, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x12, 0x2a, 0x0a, 0x10, 0x63, 0x61, 0x63,
	0x68, 0x65, 0x64, 0x50, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x10, 0x63, 0x61, 0x63, 0x68, 0x65, 0x64, 0x50, 0x65, 0x72, 0x63, 0x65,
	0x6e, 0x74, 0x61, 0x67, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x68, 0x61, 0x73, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x70, 0x68, 0x61, 0x73, 0x65, 0x22, 0x9f, 0x03, 0x0a, 0x07,
	0x44, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64,
	0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a,
	0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x40, 0x0a, 0x06, 0x66, 0x73, 0x54, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x28, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e,
	0x64, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x53,
	0x79, 0x73, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x52, 0x06, 0x66, 0x73, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x46, 0x0a, 0x08, 0x66, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75,
	0x64, 0x69, 0x6f, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x46,
	0x69, 0x6c, 0x65, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52,
	0x08, 0x66, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x1c, 0x0a, 0x09, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x50, 0x61, 0x74, 0x68, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x50, 0x61, 0x74, 0x68, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x6f, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f,
	0x72, 0x12, 0x3f, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69,
	0x6f, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x61, 0x74,
	0x61, 0x73, 0x65, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x39, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x22, 0x6a, 0x0a,
	0x10, 0x46, 0x69, 0x6c, 0x65, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x12, 0x1e, 0x0a, 0x0a, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x22, 0x0a, 0x0c, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x53, 0x75, 0x62, 0x64, 0x69,
	0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x53,
	0x75, 0x62, 0x64, 0x69, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x74, 0x68, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68, 0x22, 0xf0, 0x02, 0x0a, 0x14, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f,
	0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e,
	0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c,
	0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x40, 0x0a, 0x06, 0x66, 0x73, 0x54, 0x79,
	0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x28, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e,
	0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x06, 0x66, 0x73, 0x54, 0x79, 0x70, 0x65, 0x12, 0x46, 0x0a, 0x08, 0x66, 0x73,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x61,
	0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x64, 0x61, 0x74,
	0x61, 0x73, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x53, 0x79, 0x73, 0x74,
	0x65, 0x6d, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x08, 0x66, 0x73, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x12, 0x1c, 0x0a, 0x09, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x61, 0x74, 0x68, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x61, 0x74, 0x68,
	0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x61, 0x64, 0x4f, 0x6e, 0x6c, 0x79, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x08, 0x72, 0x65, 0x61, 0x64, 0x4f, 0x6e, 0x6c, 0x79, 0x22, 0xb6, 0x01, 0x0a,
	0x14, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x24, 0x0a,
	0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61,
	0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1c, 0x0a, 0x09, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x50, 0x61, 0x74, 0x68, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x50, 0x61, 0x74, 0x68, 0x22, 0x65, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61,
	0x73, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65,
	0x67, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69,
	0x6f, 0x6e, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x68, 0x0a, 0x14,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x44, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x24, 0x0a, 0x0d,
	0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0xae, 0x01, 0x0a, 0x12, 0x4c, 0x69, 0x73, 0x74, 0x44,
	0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x16, 0x0a,
	0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72,
	0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f,
	0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x40, 0x0a, 0x06, 0x66, 0x73, 0x54, 0x79, 0x70, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x28, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73,
	0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x46, 0x69, 0x6c, 0x65, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x06, 0x66, 0x73, 0x54, 0x79, 0x70, 0x65, 0x22, 0x55, 0x0a, 0x14, 0x4c, 0x69, 0x73, 0x74, 0x44,
	0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x3d, 0x0a, 0x08, 0x64, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69,
	0x6f, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x61, 0x74,
	0x61, 0x73, 0x65, 0x74, 0x52, 0x08, 0x64, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x73, 0x2a, 0x29,
	0x0a, 0x0e, 0x46, 0x69, 0x6c, 0x65, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x0b, 0x0a, 0x07, 0x43, 0x6c, 0x6f, 0x75, 0x64, 0x46, 0x73, 0x10, 0x00, 0x12, 0x0a, 0x0a,
	0x06, 0x43, 0x75, 0x62, 0x65, 0x46, 0x73, 0x10, 0x01, 0x32, 0xad, 0x06, 0x0a, 0x0e, 0x44, 0x61,
	0x74, 0x61, 0x73, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0xa1, 0x01, 0x0a,
	0x15, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44,
	0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x12, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69,
	0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69,
	0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x22, 0x35, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x2f, 0x3a, 0x01, 0x2a, 0x22, 0x2a, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x77,
	0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2f, 0x7b, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74,
	0x12, 0xa1, 0x01, 0x0a, 0x0d, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x61, 0x73,
	0x65, 0x74, 0x12, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64,
	0x69, 0x6f, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64,
	0x69, 0x6f, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x61,
	0x74, 0x61, 0x73, 0x65, 0x74, 0x22, 0x3d, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x37, 0x3a, 0x01, 0x2a,
	0x1a, 0x32, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x2f, 0x7b, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x73, 0x2f, 0x7b, 0x6e,
	0x61, 0x6d, 0x65, 0x7d, 0x12, 0x98, 0x01, 0x0a, 0x0a, 0x47, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61,
	0x73, 0x65, 0x74, 0x12, 0x2b, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75,
	0x64, 0x69, 0x6f, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47,
	0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f,
	0x2e, 0x64, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x61, 0x74, 0x61,
	0x73, 0x65, 0x74, 0x22, 0x3a, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x34, 0x12, 0x32, 0x2f, 0x61, 0x70,
	0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2f,
	0x7b, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f,
	0x64, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x73, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x12,
	0xa1, 0x01, 0x0a, 0x0c, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x73,
	0x12, 0x2c, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f,
	0x2e, 0x64, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74,
	0x44, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x1a, 0x2e,
	0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x64,
	0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x61,
	0x74, 0x61, 0x73, 0x65, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x33,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2d, 0x12, 0x2b, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31,
	0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2f, 0x7b, 0x77, 0x6f, 0x72, 0x6b,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x73,
	0x65, 0x74, 0x73, 0x12, 0x93, 0x01, 0x0a, 0x0d, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x44, 0x61,
	0x74, 0x61, 0x73, 0x65, 0x74, 0x12, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73,
	0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x44, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x3a, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x34, 0x2a, 0x32, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f,
	0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2f, 0x7b, 0x77, 0x6f, 0x72, 0x6b, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x73, 0x65,
	0x74, 0x73, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x42, 0x4a, 0x5a, 0x48, 0x67, 0x69, 0x74,
	0x2e, 0x6c, 0x69, 0x61, 0x6e, 0x6a, 0x69, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x63, 0x6c, 0x6f,
	0x75, 0x64, 0x6e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x2f, 0x6b, 0x69, 0x63, 0x2f, 0x6b, 0x69, 0x63,
	0x2d, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x61,
	0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x2f,
	0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_aistudio_dataset_v1_dataset_proto_rawDescOnce sync.Once
	file_aistudio_dataset_v1_dataset_proto_rawDescData = file_aistudio_dataset_v1_dataset_proto_rawDesc
)

func file_aistudio_dataset_v1_dataset_proto_rawDescGZIP() []byte {
	file_aistudio_dataset_v1_dataset_proto_rawDescOnce.Do(func() {
		file_aistudio_dataset_v1_dataset_proto_rawDescData = protoimpl.X.CompressGZIP(file_aistudio_dataset_v1_dataset_proto_rawDescData)
	})
	return file_aistudio_dataset_v1_dataset_proto_rawDescData
}

var file_aistudio_dataset_v1_dataset_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_aistudio_dataset_v1_dataset_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_aistudio_dataset_v1_dataset_proto_goTypes = []any{
	(FileSystemType)(0),           // 0: apis.aistudio.dataset.v1.FileSystemType
	(*DatasetStatus)(nil),         // 1: apis.aistudio.dataset.v1.DatasetStatus
	(*Dataset)(nil),               // 2: apis.aistudio.dataset.v1.Dataset
	(*FileSystemConfig)(nil),      // 3: apis.aistudio.dataset.v1.FileSystemConfig
	(*CreateDatasetRequest)(nil),  // 4: apis.aistudio.dataset.v1.CreateDatasetRequest
	(*UpdateDatasetRequest)(nil),  // 5: apis.aistudio.dataset.v1.UpdateDatasetRequest
	(*GetDatasetRequest)(nil),     // 6: apis.aistudio.dataset.v1.GetDatasetRequest
	(*DeleteDatasetRequest)(nil),  // 7: apis.aistudio.dataset.v1.DeleteDatasetRequest
	(*ListDatasetOptions)(nil),    // 8: apis.aistudio.dataset.v1.ListDatasetOptions
	(*ListDatasetsResponse)(nil),  // 9: apis.aistudio.dataset.v1.ListDatasetsResponse
	(*common.TimestampModel)(nil), // 10: apis.common.TimestampModel
	(*emptypb.Empty)(nil),         // 11: google.protobuf.Empty
}
var file_aistudio_dataset_v1_dataset_proto_depIdxs = []int32{
	0,  // 0: apis.aistudio.dataset.v1.Dataset.fsType:type_name -> apis.aistudio.dataset.v1.FileSystemType
	3,  // 1: apis.aistudio.dataset.v1.Dataset.fsConfig:type_name -> apis.aistudio.dataset.v1.FileSystemConfig
	1,  // 2: apis.aistudio.dataset.v1.Dataset.status:type_name -> apis.aistudio.dataset.v1.DatasetStatus
	10, // 3: apis.aistudio.dataset.v1.Dataset.timestamp:type_name -> apis.common.TimestampModel
	0,  // 4: apis.aistudio.dataset.v1.CreateDatasetRequest.fsType:type_name -> apis.aistudio.dataset.v1.FileSystemType
	3,  // 5: apis.aistudio.dataset.v1.CreateDatasetRequest.fsConfig:type_name -> apis.aistudio.dataset.v1.FileSystemConfig
	0,  // 6: apis.aistudio.dataset.v1.ListDatasetOptions.fsType:type_name -> apis.aistudio.dataset.v1.FileSystemType
	2,  // 7: apis.aistudio.dataset.v1.ListDatasetsResponse.datasets:type_name -> apis.aistudio.dataset.v1.Dataset
	4,  // 8: apis.aistudio.dataset.v1.DatasetService.CreateOrUpdateDataset:input_type -> apis.aistudio.dataset.v1.CreateDatasetRequest
	4,  // 9: apis.aistudio.dataset.v1.DatasetService.UpdateDataset:input_type -> apis.aistudio.dataset.v1.CreateDatasetRequest
	6,  // 10: apis.aistudio.dataset.v1.DatasetService.GetDataset:input_type -> apis.aistudio.dataset.v1.GetDatasetRequest
	8,  // 11: apis.aistudio.dataset.v1.DatasetService.ListDatasets:input_type -> apis.aistudio.dataset.v1.ListDatasetOptions
	7,  // 12: apis.aistudio.dataset.v1.DatasetService.DeleteDataset:input_type -> apis.aistudio.dataset.v1.DeleteDatasetRequest
	2,  // 13: apis.aistudio.dataset.v1.DatasetService.CreateOrUpdateDataset:output_type -> apis.aistudio.dataset.v1.Dataset
	2,  // 14: apis.aistudio.dataset.v1.DatasetService.UpdateDataset:output_type -> apis.aistudio.dataset.v1.Dataset
	2,  // 15: apis.aistudio.dataset.v1.DatasetService.GetDataset:output_type -> apis.aistudio.dataset.v1.Dataset
	9,  // 16: apis.aistudio.dataset.v1.DatasetService.ListDatasets:output_type -> apis.aistudio.dataset.v1.ListDatasetsResponse
	11, // 17: apis.aistudio.dataset.v1.DatasetService.DeleteDataset:output_type -> google.protobuf.Empty
	13, // [13:18] is the sub-list for method output_type
	8,  // [8:13] is the sub-list for method input_type
	8,  // [8:8] is the sub-list for extension type_name
	8,  // [8:8] is the sub-list for extension extendee
	0,  // [0:8] is the sub-list for field type_name
}

func init() { file_aistudio_dataset_v1_dataset_proto_init() }
func file_aistudio_dataset_v1_dataset_proto_init() {
	if File_aistudio_dataset_v1_dataset_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_aistudio_dataset_v1_dataset_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*DatasetStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_dataset_v1_dataset_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*Dataset); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_dataset_v1_dataset_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*FileSystemConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_dataset_v1_dataset_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*CreateDatasetRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_dataset_v1_dataset_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*UpdateDatasetRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_dataset_v1_dataset_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*GetDatasetRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_dataset_v1_dataset_proto_msgTypes[6].Exporter = func(v any, i int) any {
			switch v := v.(*DeleteDatasetRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_dataset_v1_dataset_proto_msgTypes[7].Exporter = func(v any, i int) any {
			switch v := v.(*ListDatasetOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_dataset_v1_dataset_proto_msgTypes[8].Exporter = func(v any, i int) any {
			switch v := v.(*ListDatasetsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_aistudio_dataset_v1_dataset_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_aistudio_dataset_v1_dataset_proto_goTypes,
		DependencyIndexes: file_aistudio_dataset_v1_dataset_proto_depIdxs,
		EnumInfos:         file_aistudio_dataset_v1_dataset_proto_enumTypes,
		MessageInfos:      file_aistudio_dataset_v1_dataset_proto_msgTypes,
	}.Build()
	File_aistudio_dataset_v1_dataset_proto = out.File
	file_aistudio_dataset_v1_dataset_proto_rawDesc = nil
	file_aistudio_dataset_v1_dataset_proto_goTypes = nil
	file_aistudio_dataset_v1_dataset_proto_depIdxs = nil
}
