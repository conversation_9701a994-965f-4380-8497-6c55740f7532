// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.3
// - protoc             v5.29.3
// source: aistudio/metrics/v1/metrics.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationMetricsServiceQuery = "/apis.aistudio.metrics.v1.MetricsService/Query"
const OperationMetricsServiceQueryNodeCPUUsage = "/apis.aistudio.metrics.v1.MetricsService/QueryNodeCPUUsage"
const OperationMetricsServiceQueryNodeGPUUsage = "/apis.aistudio.metrics.v1.MetricsService/QueryNodeGPUUsage"
const OperationMetricsServiceQueryNodeMemoryUsage = "/apis.aistudio.metrics.v1.MetricsService/QueryNodeMemoryUsage"
const OperationMetricsServiceQueryQueueCPUUsage = "/apis.aistudio.metrics.v1.MetricsService/QueryQueueCPUUsage"
const OperationMetricsServiceQueryQueueGPUUsage = "/apis.aistudio.metrics.v1.MetricsService/QueryQueueGPUUsage"
const OperationMetricsServiceQueryQueueMemoryUsage = "/apis.aistudio.metrics.v1.MetricsService/QueryQueueMemoryUsage"
const OperationMetricsServiceQueryRange = "/apis.aistudio.metrics.v1.MetricsService/QueryRange"
const OperationMetricsServiceQueryWorkspaceCPUUsage = "/apis.aistudio.metrics.v1.MetricsService/QueryWorkspaceCPUUsage"
const OperationMetricsServiceQueryWorkspaceGPUUsage = "/apis.aistudio.metrics.v1.MetricsService/QueryWorkspaceGPUUsage"
const OperationMetricsServiceQueryWorkspaceMemoryUsage = "/apis.aistudio.metrics.v1.MetricsService/QueryWorkspaceMemoryUsage"

type MetricsServiceHTTPServer interface {
	Query(context.Context, *QueryRequest) (*QueryResult, error)
	QueryNodeCPUUsage(context.Context, *QueryRangeNodeCPUUsageRequest) (*QueryRangeResult, error)
	QueryNodeGPUUsage(context.Context, *QueryRangeNodeGPUUsageRequest) (*QueryRangeResult, error)
	QueryNodeMemoryUsage(context.Context, *QueryRangeNodeMemoryUsageRequest) (*QueryRangeResult, error)
	QueryQueueCPUUsage(context.Context, *QueryRangeQueueCPUUsageRequest) (*QueryRangeResult, error)
	QueryQueueGPUUsage(context.Context, *QueryRangeQueueGPUUsageRequest) (*QueryRangeResult, error)
	QueryQueueMemoryUsage(context.Context, *QueryRangeQueueMemoryUsageRequest) (*QueryRangeResult, error)
	QueryRange(context.Context, *QueryRangeRequest) (*QueryRangeResult, error)
	QueryWorkspaceCPUUsage(context.Context, *QueryRangeWorkspaceCPUUsageRequest) (*QueryRangeResult, error)
	QueryWorkspaceGPUUsage(context.Context, *QueryRangeWorkspaceGPUUsageRequest) (*QueryRangeResult, error)
	QueryWorkspaceMemoryUsage(context.Context, *QueryRangeWorkspaceMemoryUsageRequest) (*QueryRangeResult, error)
}

func RegisterMetricsServiceHTTPServer(s *http.Server, srv MetricsServiceHTTPServer) {
	r := s.Route("/")
	r.GET("/apis/v1/metrics/query-range", _MetricsService_QueryRange0_HTTP_Handler(srv))
	r.GET("/apis/v1/metrics/query", _MetricsService_Query0_HTTP_Handler(srv))
	r.GET("/apis/v1/workspace/{workspaceName}/metrics/cpu-usage", _MetricsService_QueryWorkspaceCPUUsage0_HTTP_Handler(srv))
	r.GET("/apis/v1/node/{nodeIP}/metrics/cpu-usage", _MetricsService_QueryNodeCPUUsage0_HTTP_Handler(srv))
	r.GET("/apis/v1/workspace/{workspaceName}/queue/{queueName}/metrics/cpu-usage", _MetricsService_QueryQueueCPUUsage0_HTTP_Handler(srv))
	r.GET("/apis/v1/workspace/{workspaceName}/metrics/memory-usage", _MetricsService_QueryWorkspaceMemoryUsage0_HTTP_Handler(srv))
	r.GET("/apis/v1/node/{nodeIP}/metrics/memory-usage", _MetricsService_QueryNodeMemoryUsage0_HTTP_Handler(srv))
	r.GET("/apis/v1/workspace/{workspaceName}/queue/{queueName}/metrics/memory-usage", _MetricsService_QueryQueueMemoryUsage0_HTTP_Handler(srv))
	r.GET("/apis/v1/workspace/{workspaceName}/metrics/gpu-usage", _MetricsService_QueryWorkspaceGPUUsage0_HTTP_Handler(srv))
	r.GET("/apis/v1/node/{nodeIP}/metrics/gpu-usage", _MetricsService_QueryNodeGPUUsage0_HTTP_Handler(srv))
	r.GET("/apis/v1/workspace/{workspaceName}/queue/{queueName}/metrics/gpu-usage", _MetricsService_QueryQueueGPUUsage0_HTTP_Handler(srv))
}

func _MetricsService_QueryRange0_HTTP_Handler(srv MetricsServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in QueryRangeRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationMetricsServiceQueryRange)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.QueryRange(ctx, req.(*QueryRangeRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*QueryRangeResult)
		return ctx.Result(200, reply)
	}
}

func _MetricsService_Query0_HTTP_Handler(srv MetricsServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in QueryRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationMetricsServiceQuery)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Query(ctx, req.(*QueryRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*QueryResult)
		return ctx.Result(200, reply)
	}
}

func _MetricsService_QueryWorkspaceCPUUsage0_HTTP_Handler(srv MetricsServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in QueryRangeWorkspaceCPUUsageRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationMetricsServiceQueryWorkspaceCPUUsage)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.QueryWorkspaceCPUUsage(ctx, req.(*QueryRangeWorkspaceCPUUsageRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*QueryRangeResult)
		return ctx.Result(200, reply)
	}
}

func _MetricsService_QueryNodeCPUUsage0_HTTP_Handler(srv MetricsServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in QueryRangeNodeCPUUsageRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationMetricsServiceQueryNodeCPUUsage)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.QueryNodeCPUUsage(ctx, req.(*QueryRangeNodeCPUUsageRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*QueryRangeResult)
		return ctx.Result(200, reply)
	}
}

func _MetricsService_QueryQueueCPUUsage0_HTTP_Handler(srv MetricsServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in QueryRangeQueueCPUUsageRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationMetricsServiceQueryQueueCPUUsage)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.QueryQueueCPUUsage(ctx, req.(*QueryRangeQueueCPUUsageRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*QueryRangeResult)
		return ctx.Result(200, reply)
	}
}

func _MetricsService_QueryWorkspaceMemoryUsage0_HTTP_Handler(srv MetricsServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in QueryRangeWorkspaceMemoryUsageRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationMetricsServiceQueryWorkspaceMemoryUsage)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.QueryWorkspaceMemoryUsage(ctx, req.(*QueryRangeWorkspaceMemoryUsageRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*QueryRangeResult)
		return ctx.Result(200, reply)
	}
}

func _MetricsService_QueryNodeMemoryUsage0_HTTP_Handler(srv MetricsServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in QueryRangeNodeMemoryUsageRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationMetricsServiceQueryNodeMemoryUsage)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.QueryNodeMemoryUsage(ctx, req.(*QueryRangeNodeMemoryUsageRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*QueryRangeResult)
		return ctx.Result(200, reply)
	}
}

func _MetricsService_QueryQueueMemoryUsage0_HTTP_Handler(srv MetricsServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in QueryRangeQueueMemoryUsageRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationMetricsServiceQueryQueueMemoryUsage)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.QueryQueueMemoryUsage(ctx, req.(*QueryRangeQueueMemoryUsageRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*QueryRangeResult)
		return ctx.Result(200, reply)
	}
}

func _MetricsService_QueryWorkspaceGPUUsage0_HTTP_Handler(srv MetricsServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in QueryRangeWorkspaceGPUUsageRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationMetricsServiceQueryWorkspaceGPUUsage)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.QueryWorkspaceGPUUsage(ctx, req.(*QueryRangeWorkspaceGPUUsageRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*QueryRangeResult)
		return ctx.Result(200, reply)
	}
}

func _MetricsService_QueryNodeGPUUsage0_HTTP_Handler(srv MetricsServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in QueryRangeNodeGPUUsageRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationMetricsServiceQueryNodeGPUUsage)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.QueryNodeGPUUsage(ctx, req.(*QueryRangeNodeGPUUsageRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*QueryRangeResult)
		return ctx.Result(200, reply)
	}
}

func _MetricsService_QueryQueueGPUUsage0_HTTP_Handler(srv MetricsServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in QueryRangeQueueGPUUsageRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationMetricsServiceQueryQueueGPUUsage)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.QueryQueueGPUUsage(ctx, req.(*QueryRangeQueueGPUUsageRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*QueryRangeResult)
		return ctx.Result(200, reply)
	}
}

type MetricsServiceHTTPClient interface {
	Query(ctx context.Context, req *QueryRequest, opts ...http.CallOption) (rsp *QueryResult, err error)
	QueryNodeCPUUsage(ctx context.Context, req *QueryRangeNodeCPUUsageRequest, opts ...http.CallOption) (rsp *QueryRangeResult, err error)
	QueryNodeGPUUsage(ctx context.Context, req *QueryRangeNodeGPUUsageRequest, opts ...http.CallOption) (rsp *QueryRangeResult, err error)
	QueryNodeMemoryUsage(ctx context.Context, req *QueryRangeNodeMemoryUsageRequest, opts ...http.CallOption) (rsp *QueryRangeResult, err error)
	QueryQueueCPUUsage(ctx context.Context, req *QueryRangeQueueCPUUsageRequest, opts ...http.CallOption) (rsp *QueryRangeResult, err error)
	QueryQueueGPUUsage(ctx context.Context, req *QueryRangeQueueGPUUsageRequest, opts ...http.CallOption) (rsp *QueryRangeResult, err error)
	QueryQueueMemoryUsage(ctx context.Context, req *QueryRangeQueueMemoryUsageRequest, opts ...http.CallOption) (rsp *QueryRangeResult, err error)
	QueryRange(ctx context.Context, req *QueryRangeRequest, opts ...http.CallOption) (rsp *QueryRangeResult, err error)
	QueryWorkspaceCPUUsage(ctx context.Context, req *QueryRangeWorkspaceCPUUsageRequest, opts ...http.CallOption) (rsp *QueryRangeResult, err error)
	QueryWorkspaceGPUUsage(ctx context.Context, req *QueryRangeWorkspaceGPUUsageRequest, opts ...http.CallOption) (rsp *QueryRangeResult, err error)
	QueryWorkspaceMemoryUsage(ctx context.Context, req *QueryRangeWorkspaceMemoryUsageRequest, opts ...http.CallOption) (rsp *QueryRangeResult, err error)
}

type MetricsServiceHTTPClientImpl struct {
	cc *http.Client
}

func NewMetricsServiceHTTPClient(client *http.Client) MetricsServiceHTTPClient {
	return &MetricsServiceHTTPClientImpl{client}
}

func (c *MetricsServiceHTTPClientImpl) Query(ctx context.Context, in *QueryRequest, opts ...http.CallOption) (*QueryResult, error) {
	var out QueryResult
	pattern := "/apis/v1/metrics/query"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationMetricsServiceQuery))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *MetricsServiceHTTPClientImpl) QueryNodeCPUUsage(ctx context.Context, in *QueryRangeNodeCPUUsageRequest, opts ...http.CallOption) (*QueryRangeResult, error) {
	var out QueryRangeResult
	pattern := "/apis/v1/node/{nodeIP}/metrics/cpu-usage"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationMetricsServiceQueryNodeCPUUsage))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *MetricsServiceHTTPClientImpl) QueryNodeGPUUsage(ctx context.Context, in *QueryRangeNodeGPUUsageRequest, opts ...http.CallOption) (*QueryRangeResult, error) {
	var out QueryRangeResult
	pattern := "/apis/v1/node/{nodeIP}/metrics/gpu-usage"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationMetricsServiceQueryNodeGPUUsage))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *MetricsServiceHTTPClientImpl) QueryNodeMemoryUsage(ctx context.Context, in *QueryRangeNodeMemoryUsageRequest, opts ...http.CallOption) (*QueryRangeResult, error) {
	var out QueryRangeResult
	pattern := "/apis/v1/node/{nodeIP}/metrics/memory-usage"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationMetricsServiceQueryNodeMemoryUsage))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *MetricsServiceHTTPClientImpl) QueryQueueCPUUsage(ctx context.Context, in *QueryRangeQueueCPUUsageRequest, opts ...http.CallOption) (*QueryRangeResult, error) {
	var out QueryRangeResult
	pattern := "/apis/v1/workspace/{workspaceName}/queue/{queueName}/metrics/cpu-usage"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationMetricsServiceQueryQueueCPUUsage))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *MetricsServiceHTTPClientImpl) QueryQueueGPUUsage(ctx context.Context, in *QueryRangeQueueGPUUsageRequest, opts ...http.CallOption) (*QueryRangeResult, error) {
	var out QueryRangeResult
	pattern := "/apis/v1/workspace/{workspaceName}/queue/{queueName}/metrics/gpu-usage"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationMetricsServiceQueryQueueGPUUsage))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *MetricsServiceHTTPClientImpl) QueryQueueMemoryUsage(ctx context.Context, in *QueryRangeQueueMemoryUsageRequest, opts ...http.CallOption) (*QueryRangeResult, error) {
	var out QueryRangeResult
	pattern := "/apis/v1/workspace/{workspaceName}/queue/{queueName}/metrics/memory-usage"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationMetricsServiceQueryQueueMemoryUsage))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *MetricsServiceHTTPClientImpl) QueryRange(ctx context.Context, in *QueryRangeRequest, opts ...http.CallOption) (*QueryRangeResult, error) {
	var out QueryRangeResult
	pattern := "/apis/v1/metrics/query-range"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationMetricsServiceQueryRange))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *MetricsServiceHTTPClientImpl) QueryWorkspaceCPUUsage(ctx context.Context, in *QueryRangeWorkspaceCPUUsageRequest, opts ...http.CallOption) (*QueryRangeResult, error) {
	var out QueryRangeResult
	pattern := "/apis/v1/workspace/{workspaceName}/metrics/cpu-usage"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationMetricsServiceQueryWorkspaceCPUUsage))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *MetricsServiceHTTPClientImpl) QueryWorkspaceGPUUsage(ctx context.Context, in *QueryRangeWorkspaceGPUUsageRequest, opts ...http.CallOption) (*QueryRangeResult, error) {
	var out QueryRangeResult
	pattern := "/apis/v1/workspace/{workspaceName}/metrics/gpu-usage"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationMetricsServiceQueryWorkspaceGPUUsage))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *MetricsServiceHTTPClientImpl) QueryWorkspaceMemoryUsage(ctx context.Context, in *QueryRangeWorkspaceMemoryUsageRequest, opts ...http.CallOption) (*QueryRangeResult, error) {
	var out QueryRangeResult
	pattern := "/apis/v1/workspace/{workspaceName}/metrics/memory-usage"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationMetricsServiceQueryWorkspaceMemoryUsage))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
