// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v5.29.3
// source: aistudio/metrics/v1/metrics.proto

package v1

import (
	_ "git.lianjia.com/cloudnative/kic/kic-platform/apis/common"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	_ "google.golang.org/protobuf/types/known/durationpb"
	_ "google.golang.org/protobuf/types/known/emptypb"
	_ "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type QueryRangeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MetricsQuery string `protobuf:"bytes,1,opt,name=metricsQuery,proto3" json:"metricsQuery,omitempty"`
	Start        int64  `protobuf:"varint,2,opt,name=start,proto3" json:"start,omitempty"`
	End          int64  `protobuf:"varint,3,opt,name=end,proto3" json:"end,omitempty"`
	DataPoints   int64  `protobuf:"varint,4,opt,name=dataPoints,proto3" json:"dataPoints,omitempty"`
}

func (x *QueryRangeRequest) Reset() {
	*x = QueryRangeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_metrics_v1_metrics_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryRangeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryRangeRequest) ProtoMessage() {}

func (x *QueryRangeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_metrics_v1_metrics_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryRangeRequest.ProtoReflect.Descriptor instead.
func (*QueryRangeRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_metrics_v1_metrics_proto_rawDescGZIP(), []int{0}
}

func (x *QueryRangeRequest) GetMetricsQuery() string {
	if x != nil {
		return x.MetricsQuery
	}
	return ""
}

func (x *QueryRangeRequest) GetStart() int64 {
	if x != nil {
		return x.Start
	}
	return 0
}

func (x *QueryRangeRequest) GetEnd() int64 {
	if x != nil {
		return x.End
	}
	return 0
}

func (x *QueryRangeRequest) GetDataPoints() int64 {
	if x != nil {
		return x.DataPoints
	}
	return 0
}

type QueryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MetricsQuery string `protobuf:"bytes,1,opt,name=metricsQuery,proto3" json:"metricsQuery,omitempty"`
}

func (x *QueryRequest) Reset() {
	*x = QueryRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_metrics_v1_metrics_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryRequest) ProtoMessage() {}

func (x *QueryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_metrics_v1_metrics_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryRequest.ProtoReflect.Descriptor instead.
func (*QueryRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_metrics_v1_metrics_proto_rawDescGZIP(), []int{1}
}

func (x *QueryRequest) GetMetricsQuery() string {
	if x != nil {
		return x.MetricsQuery
	}
	return ""
}

type QueryResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Labels map[string]string `protobuf:"bytes,1,rep,name=labels,proto3" json:"labels,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Value  float64           `protobuf:"fixed64,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *QueryResult) Reset() {
	*x = QueryResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_metrics_v1_metrics_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryResult) ProtoMessage() {}

func (x *QueryResult) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_metrics_v1_metrics_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryResult.ProtoReflect.Descriptor instead.
func (*QueryResult) Descriptor() ([]byte, []int) {
	return file_aistudio_metrics_v1_metrics_proto_rawDescGZIP(), []int{2}
}

func (x *QueryResult) GetLabels() map[string]string {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *QueryResult) GetValue() float64 {
	if x != nil {
		return x.Value
	}
	return 0
}

type QueryRangeResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Series []*Series `protobuf:"bytes,1,rep,name=series,proto3" json:"series,omitempty"`
}

func (x *QueryRangeResult) Reset() {
	*x = QueryRangeResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_metrics_v1_metrics_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryRangeResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryRangeResult) ProtoMessage() {}

func (x *QueryRangeResult) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_metrics_v1_metrics_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryRangeResult.ProtoReflect.Descriptor instead.
func (*QueryRangeResult) Descriptor() ([]byte, []int) {
	return file_aistudio_metrics_v1_metrics_proto_rawDescGZIP(), []int{3}
}

func (x *QueryRangeResult) GetSeries() []*Series {
	if x != nil {
		return x.Series
	}
	return nil
}

type Series struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Labels map[string]string `protobuf:"bytes,1,rep,name=labels,proto3" json:"labels,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Values []float64         `protobuf:"fixed64,2,rep,packed,name=values,proto3" json:"values,omitempty"`
	Xline  []string          `protobuf:"bytes,3,rep,name=xline,proto3" json:"xline,omitempty"`
}

func (x *Series) Reset() {
	*x = Series{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_metrics_v1_metrics_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Series) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Series) ProtoMessage() {}

func (x *Series) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_metrics_v1_metrics_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Series.ProtoReflect.Descriptor instead.
func (*Series) Descriptor() ([]byte, []int) {
	return file_aistudio_metrics_v1_metrics_proto_rawDescGZIP(), []int{4}
}

func (x *Series) GetLabels() map[string]string {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *Series) GetValues() []float64 {
	if x != nil {
		return x.Values
	}
	return nil
}

func (x *Series) GetXline() []string {
	if x != nil {
		return x.Xline
	}
	return nil
}

type QueryRangeWorkspaceCPUUsageRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName string `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Start         int64  `protobuf:"varint,2,opt,name=start,proto3" json:"start,omitempty"`
	End           int64  `protobuf:"varint,3,opt,name=end,proto3" json:"end,omitempty"`
	DataPoints    int64  `protobuf:"varint,4,opt,name=dataPoints,proto3" json:"dataPoints,omitempty"`
	Region        string `protobuf:"bytes,5,opt,name=region,proto3" json:"region,omitempty"`
	Cluster       string `protobuf:"bytes,6,opt,name=cluster,proto3" json:"cluster,omitempty"`
}

func (x *QueryRangeWorkspaceCPUUsageRequest) Reset() {
	*x = QueryRangeWorkspaceCPUUsageRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_metrics_v1_metrics_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryRangeWorkspaceCPUUsageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryRangeWorkspaceCPUUsageRequest) ProtoMessage() {}

func (x *QueryRangeWorkspaceCPUUsageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_metrics_v1_metrics_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryRangeWorkspaceCPUUsageRequest.ProtoReflect.Descriptor instead.
func (*QueryRangeWorkspaceCPUUsageRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_metrics_v1_metrics_proto_rawDescGZIP(), []int{5}
}

func (x *QueryRangeWorkspaceCPUUsageRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *QueryRangeWorkspaceCPUUsageRequest) GetStart() int64 {
	if x != nil {
		return x.Start
	}
	return 0
}

func (x *QueryRangeWorkspaceCPUUsageRequest) GetEnd() int64 {
	if x != nil {
		return x.End
	}
	return 0
}

func (x *QueryRangeWorkspaceCPUUsageRequest) GetDataPoints() int64 {
	if x != nil {
		return x.DataPoints
	}
	return 0
}

func (x *QueryRangeWorkspaceCPUUsageRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *QueryRangeWorkspaceCPUUsageRequest) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

type QueryRangeNodeCPUUsageRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NodeIP     string `protobuf:"bytes,1,opt,name=nodeIP,proto3" json:"nodeIP,omitempty"`
	Start      int64  `protobuf:"varint,2,opt,name=start,proto3" json:"start,omitempty"`
	End        int64  `protobuf:"varint,3,opt,name=end,proto3" json:"end,omitempty"`
	DataPoints int64  `protobuf:"varint,4,opt,name=dataPoints,proto3" json:"dataPoints,omitempty"`
}

func (x *QueryRangeNodeCPUUsageRequest) Reset() {
	*x = QueryRangeNodeCPUUsageRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_metrics_v1_metrics_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryRangeNodeCPUUsageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryRangeNodeCPUUsageRequest) ProtoMessage() {}

func (x *QueryRangeNodeCPUUsageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_metrics_v1_metrics_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryRangeNodeCPUUsageRequest.ProtoReflect.Descriptor instead.
func (*QueryRangeNodeCPUUsageRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_metrics_v1_metrics_proto_rawDescGZIP(), []int{6}
}

func (x *QueryRangeNodeCPUUsageRequest) GetNodeIP() string {
	if x != nil {
		return x.NodeIP
	}
	return ""
}

func (x *QueryRangeNodeCPUUsageRequest) GetStart() int64 {
	if x != nil {
		return x.Start
	}
	return 0
}

func (x *QueryRangeNodeCPUUsageRequest) GetEnd() int64 {
	if x != nil {
		return x.End
	}
	return 0
}

func (x *QueryRangeNodeCPUUsageRequest) GetDataPoints() int64 {
	if x != nil {
		return x.DataPoints
	}
	return 0
}

type QueryRangeQueueCPUUsageRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName string `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	QueueName     string `protobuf:"bytes,2,opt,name=queueName,proto3" json:"queueName,omitempty"`
	Start         int64  `protobuf:"varint,3,opt,name=start,proto3" json:"start,omitempty"`
	End           int64  `protobuf:"varint,4,opt,name=end,proto3" json:"end,omitempty"`
	DataPoints    int64  `protobuf:"varint,5,opt,name=dataPoints,proto3" json:"dataPoints,omitempty"`
}

func (x *QueryRangeQueueCPUUsageRequest) Reset() {
	*x = QueryRangeQueueCPUUsageRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_metrics_v1_metrics_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryRangeQueueCPUUsageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryRangeQueueCPUUsageRequest) ProtoMessage() {}

func (x *QueryRangeQueueCPUUsageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_metrics_v1_metrics_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryRangeQueueCPUUsageRequest.ProtoReflect.Descriptor instead.
func (*QueryRangeQueueCPUUsageRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_metrics_v1_metrics_proto_rawDescGZIP(), []int{7}
}

func (x *QueryRangeQueueCPUUsageRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *QueryRangeQueueCPUUsageRequest) GetQueueName() string {
	if x != nil {
		return x.QueueName
	}
	return ""
}

func (x *QueryRangeQueueCPUUsageRequest) GetStart() int64 {
	if x != nil {
		return x.Start
	}
	return 0
}

func (x *QueryRangeQueueCPUUsageRequest) GetEnd() int64 {
	if x != nil {
		return x.End
	}
	return 0
}

func (x *QueryRangeQueueCPUUsageRequest) GetDataPoints() int64 {
	if x != nil {
		return x.DataPoints
	}
	return 0
}

type QueryRangeWorkspaceMemoryUsageRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName string `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Start         int64  `protobuf:"varint,2,opt,name=start,proto3" json:"start,omitempty"`
	End           int64  `protobuf:"varint,3,opt,name=end,proto3" json:"end,omitempty"`
	DataPoints    int64  `protobuf:"varint,4,opt,name=dataPoints,proto3" json:"dataPoints,omitempty"`
	Region        string `protobuf:"bytes,5,opt,name=region,proto3" json:"region,omitempty"`
	Cluster       string `protobuf:"bytes,6,opt,name=cluster,proto3" json:"cluster,omitempty"`
}

func (x *QueryRangeWorkspaceMemoryUsageRequest) Reset() {
	*x = QueryRangeWorkspaceMemoryUsageRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_metrics_v1_metrics_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryRangeWorkspaceMemoryUsageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryRangeWorkspaceMemoryUsageRequest) ProtoMessage() {}

func (x *QueryRangeWorkspaceMemoryUsageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_metrics_v1_metrics_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryRangeWorkspaceMemoryUsageRequest.ProtoReflect.Descriptor instead.
func (*QueryRangeWorkspaceMemoryUsageRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_metrics_v1_metrics_proto_rawDescGZIP(), []int{8}
}

func (x *QueryRangeWorkspaceMemoryUsageRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *QueryRangeWorkspaceMemoryUsageRequest) GetStart() int64 {
	if x != nil {
		return x.Start
	}
	return 0
}

func (x *QueryRangeWorkspaceMemoryUsageRequest) GetEnd() int64 {
	if x != nil {
		return x.End
	}
	return 0
}

func (x *QueryRangeWorkspaceMemoryUsageRequest) GetDataPoints() int64 {
	if x != nil {
		return x.DataPoints
	}
	return 0
}

func (x *QueryRangeWorkspaceMemoryUsageRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *QueryRangeWorkspaceMemoryUsageRequest) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

type QueryRangeNodeMemoryUsageRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NodeIP     string `protobuf:"bytes,1,opt,name=nodeIP,proto3" json:"nodeIP,omitempty"`
	Start      int64  `protobuf:"varint,2,opt,name=start,proto3" json:"start,omitempty"`
	End        int64  `protobuf:"varint,3,opt,name=end,proto3" json:"end,omitempty"`
	DataPoints int64  `protobuf:"varint,4,opt,name=dataPoints,proto3" json:"dataPoints,omitempty"`
}

func (x *QueryRangeNodeMemoryUsageRequest) Reset() {
	*x = QueryRangeNodeMemoryUsageRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_metrics_v1_metrics_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryRangeNodeMemoryUsageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryRangeNodeMemoryUsageRequest) ProtoMessage() {}

func (x *QueryRangeNodeMemoryUsageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_metrics_v1_metrics_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryRangeNodeMemoryUsageRequest.ProtoReflect.Descriptor instead.
func (*QueryRangeNodeMemoryUsageRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_metrics_v1_metrics_proto_rawDescGZIP(), []int{9}
}

func (x *QueryRangeNodeMemoryUsageRequest) GetNodeIP() string {
	if x != nil {
		return x.NodeIP
	}
	return ""
}

func (x *QueryRangeNodeMemoryUsageRequest) GetStart() int64 {
	if x != nil {
		return x.Start
	}
	return 0
}

func (x *QueryRangeNodeMemoryUsageRequest) GetEnd() int64 {
	if x != nil {
		return x.End
	}
	return 0
}

func (x *QueryRangeNodeMemoryUsageRequest) GetDataPoints() int64 {
	if x != nil {
		return x.DataPoints
	}
	return 0
}

type QueryRangeQueueMemoryUsageRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName string `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	QueueName     string `protobuf:"bytes,2,opt,name=queueName,proto3" json:"queueName,omitempty"`
	Start         int64  `protobuf:"varint,3,opt,name=start,proto3" json:"start,omitempty"`
	End           int64  `protobuf:"varint,4,opt,name=end,proto3" json:"end,omitempty"`
	DataPoints    int64  `protobuf:"varint,5,opt,name=dataPoints,proto3" json:"dataPoints,omitempty"`
}

func (x *QueryRangeQueueMemoryUsageRequest) Reset() {
	*x = QueryRangeQueueMemoryUsageRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_metrics_v1_metrics_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryRangeQueueMemoryUsageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryRangeQueueMemoryUsageRequest) ProtoMessage() {}

func (x *QueryRangeQueueMemoryUsageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_metrics_v1_metrics_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryRangeQueueMemoryUsageRequest.ProtoReflect.Descriptor instead.
func (*QueryRangeQueueMemoryUsageRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_metrics_v1_metrics_proto_rawDescGZIP(), []int{10}
}

func (x *QueryRangeQueueMemoryUsageRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *QueryRangeQueueMemoryUsageRequest) GetQueueName() string {
	if x != nil {
		return x.QueueName
	}
	return ""
}

func (x *QueryRangeQueueMemoryUsageRequest) GetStart() int64 {
	if x != nil {
		return x.Start
	}
	return 0
}

func (x *QueryRangeQueueMemoryUsageRequest) GetEnd() int64 {
	if x != nil {
		return x.End
	}
	return 0
}

func (x *QueryRangeQueueMemoryUsageRequest) GetDataPoints() int64 {
	if x != nil {
		return x.DataPoints
	}
	return 0
}

type QueryRangeWorkspaceGPUUsageRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName string `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Start         int64  `protobuf:"varint,2,opt,name=start,proto3" json:"start,omitempty"`
	End           int64  `protobuf:"varint,3,opt,name=end,proto3" json:"end,omitempty"`
	DataPoints    int64  `protobuf:"varint,4,opt,name=dataPoints,proto3" json:"dataPoints,omitempty"`
	Region        string `protobuf:"bytes,5,opt,name=region,proto3" json:"region,omitempty"`
	Cluster       string `protobuf:"bytes,6,opt,name=cluster,proto3" json:"cluster,omitempty"`
}

func (x *QueryRangeWorkspaceGPUUsageRequest) Reset() {
	*x = QueryRangeWorkspaceGPUUsageRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_metrics_v1_metrics_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryRangeWorkspaceGPUUsageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryRangeWorkspaceGPUUsageRequest) ProtoMessage() {}

func (x *QueryRangeWorkspaceGPUUsageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_metrics_v1_metrics_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryRangeWorkspaceGPUUsageRequest.ProtoReflect.Descriptor instead.
func (*QueryRangeWorkspaceGPUUsageRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_metrics_v1_metrics_proto_rawDescGZIP(), []int{11}
}

func (x *QueryRangeWorkspaceGPUUsageRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *QueryRangeWorkspaceGPUUsageRequest) GetStart() int64 {
	if x != nil {
		return x.Start
	}
	return 0
}

func (x *QueryRangeWorkspaceGPUUsageRequest) GetEnd() int64 {
	if x != nil {
		return x.End
	}
	return 0
}

func (x *QueryRangeWorkspaceGPUUsageRequest) GetDataPoints() int64 {
	if x != nil {
		return x.DataPoints
	}
	return 0
}

func (x *QueryRangeWorkspaceGPUUsageRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *QueryRangeWorkspaceGPUUsageRequest) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

type QueryRangeNodeGPUUsageRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NodeIP     string `protobuf:"bytes,1,opt,name=nodeIP,proto3" json:"nodeIP,omitempty"`
	Start      int64  `protobuf:"varint,2,opt,name=start,proto3" json:"start,omitempty"`
	End        int64  `protobuf:"varint,3,opt,name=end,proto3" json:"end,omitempty"`
	DataPoints int64  `protobuf:"varint,4,opt,name=dataPoints,proto3" json:"dataPoints,omitempty"`
	Region     string `protobuf:"bytes,5,opt,name=region,proto3" json:"region,omitempty"`
}

func (x *QueryRangeNodeGPUUsageRequest) Reset() {
	*x = QueryRangeNodeGPUUsageRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_metrics_v1_metrics_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryRangeNodeGPUUsageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryRangeNodeGPUUsageRequest) ProtoMessage() {}

func (x *QueryRangeNodeGPUUsageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_metrics_v1_metrics_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryRangeNodeGPUUsageRequest.ProtoReflect.Descriptor instead.
func (*QueryRangeNodeGPUUsageRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_metrics_v1_metrics_proto_rawDescGZIP(), []int{12}
}

func (x *QueryRangeNodeGPUUsageRequest) GetNodeIP() string {
	if x != nil {
		return x.NodeIP
	}
	return ""
}

func (x *QueryRangeNodeGPUUsageRequest) GetStart() int64 {
	if x != nil {
		return x.Start
	}
	return 0
}

func (x *QueryRangeNodeGPUUsageRequest) GetEnd() int64 {
	if x != nil {
		return x.End
	}
	return 0
}

func (x *QueryRangeNodeGPUUsageRequest) GetDataPoints() int64 {
	if x != nil {
		return x.DataPoints
	}
	return 0
}

func (x *QueryRangeNodeGPUUsageRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

type QueryRangeQueueGPUUsageRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName string `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	QueueName     string `protobuf:"bytes,2,opt,name=queueName,proto3" json:"queueName,omitempty"`
	Start         int64  `protobuf:"varint,3,opt,name=start,proto3" json:"start,omitempty"`
	End           int64  `protobuf:"varint,4,opt,name=end,proto3" json:"end,omitempty"`
	DataPoints    int64  `protobuf:"varint,5,opt,name=dataPoints,proto3" json:"dataPoints,omitempty"`
	Region        string `protobuf:"bytes,6,opt,name=region,proto3" json:"region,omitempty"`
}

func (x *QueryRangeQueueGPUUsageRequest) Reset() {
	*x = QueryRangeQueueGPUUsageRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_metrics_v1_metrics_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryRangeQueueGPUUsageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryRangeQueueGPUUsageRequest) ProtoMessage() {}

func (x *QueryRangeQueueGPUUsageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_metrics_v1_metrics_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryRangeQueueGPUUsageRequest.ProtoReflect.Descriptor instead.
func (*QueryRangeQueueGPUUsageRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_metrics_v1_metrics_proto_rawDescGZIP(), []int{13}
}

func (x *QueryRangeQueueGPUUsageRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *QueryRangeQueueGPUUsageRequest) GetQueueName() string {
	if x != nil {
		return x.QueueName
	}
	return ""
}

func (x *QueryRangeQueueGPUUsageRequest) GetStart() int64 {
	if x != nil {
		return x.Start
	}
	return 0
}

func (x *QueryRangeQueueGPUUsageRequest) GetEnd() int64 {
	if x != nil {
		return x.End
	}
	return 0
}

func (x *QueryRangeQueueGPUUsageRequest) GetDataPoints() int64 {
	if x != nil {
		return x.DataPoints
	}
	return 0
}

func (x *QueryRangeQueueGPUUsageRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

var File_aistudio_metrics_v1_metrics_proto protoreflect.FileDescriptor

var file_aistudio_metrics_v1_metrics_proto_rawDesc = []byte{
	0x0a, 0x21, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2f, 0x6d, 0x65, 0x74, 0x72, 0x69,
	0x63, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x18, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64,
	0x69, 0x6f, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x2e, 0x76, 0x31, 0x1a, 0x13, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61,
	0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f,
	0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x7f,
	0x0a, 0x11, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x0c, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x51, 0x75,
	0x65, 0x72, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6d, 0x65, 0x74, 0x72, 0x69,
	0x63, 0x73, 0x51, 0x75, 0x65, 0x72, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x73, 0x74, 0x61, 0x72, 0x74, 0x12, 0x10, 0x0a,
	0x03, 0x65, 0x6e, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x65, 0x6e, 0x64, 0x12,
	0x1e, 0x0a, 0x0a, 0x64, 0x61, 0x74, 0x61, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0a, 0x64, 0x61, 0x74, 0x61, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x22,
	0x32, 0x0a, 0x0c, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x22, 0x0a, 0x0c, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x51, 0x75, 0x65, 0x72, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x51, 0x75,
	0x65, 0x72, 0x79, 0x22, 0xa9, 0x01, 0x0a, 0x0b, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x12, 0x49, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75,
	0x64, 0x69, 0x6f, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x51,
	0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c,
	0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x12, 0x14,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x1a, 0x39, 0x0a, 0x0b, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22,
	0x4c, 0x0a, 0x10, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x12, 0x38, 0x0a, 0x06, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75,
	0x64, 0x69, 0x6f, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x65, 0x72, 0x69, 0x65, 0x73, 0x52, 0x06, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x22, 0xb7, 0x01,
	0x0a, 0x06, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x12, 0x44, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65,
	0x6c, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e,
	0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c,
	0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x12, 0x16,
	0x0a, 0x06, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x01, 0x52, 0x06,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x78, 0x6c, 0x69, 0x6e, 0x65, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x78, 0x6c, 0x69, 0x6e, 0x65, 0x1a, 0x39, 0x0a, 0x0b,
	0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xc4, 0x01, 0x0a, 0x22, 0x51, 0x75, 0x65, 0x72,
	0x79, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x43,
	0x50, 0x55, 0x55, 0x73, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24,
	0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x72, 0x74, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x05, 0x73, 0x74, 0x61, 0x72, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x65, 0x6e,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x65, 0x6e, 0x64, 0x12, 0x1e, 0x0a, 0x0a,
	0x64, 0x61, 0x74, 0x61, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0a, 0x64, 0x61, 0x74, 0x61, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x12, 0x16, 0x0a, 0x06,
	0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65,
	0x67, 0x69, 0x6f, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x22, 0x7f,
	0x0a, 0x1d, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x4e, 0x6f, 0x64, 0x65,
	0x43, 0x50, 0x55, 0x55, 0x73, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x16, 0x0a, 0x06, 0x6e, 0x6f, 0x64, 0x65, 0x49, 0x50, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x6e, 0x6f, 0x64, 0x65, 0x49, 0x50, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x73, 0x74, 0x61, 0x72, 0x74, 0x12, 0x10, 0x0a,
	0x03, 0x65, 0x6e, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x65, 0x6e, 0x64, 0x12,
	0x1e, 0x0a, 0x0a, 0x64, 0x61, 0x74, 0x61, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0a, 0x64, 0x61, 0x74, 0x61, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x22,
	0xac, 0x01, 0x0a, 0x1e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x51, 0x75,
	0x65, 0x75, 0x65, 0x43, 0x50, 0x55, 0x55, 0x73, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x71, 0x75, 0x65, 0x75,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x71, 0x75, 0x65,
	0x75, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x72, 0x74, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x73, 0x74, 0x61, 0x72, 0x74, 0x12, 0x10, 0x0a, 0x03,
	0x65, 0x6e, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x65, 0x6e, 0x64, 0x12, 0x1e,
	0x0a, 0x0a, 0x64, 0x61, 0x74, 0x61, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0a, 0x64, 0x61, 0x74, 0x61, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x22, 0xc7,
	0x01, 0x0a, 0x25, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x57, 0x6f, 0x72,
	0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x55, 0x73, 0x61, 0x67,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x14,
	0x0a, 0x05, 0x73, 0x74, 0x61, 0x72, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x65, 0x6e, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x03, 0x65, 0x6e, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x64, 0x61, 0x74, 0x61, 0x50, 0x6f,
	0x69, 0x6e, 0x74, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x64, 0x61, 0x74, 0x61,
	0x50, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x18,
	0x0a, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x22, 0x82, 0x01, 0x0a, 0x20, 0x51, 0x75, 0x65,
	0x72, 0x79, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x4d, 0x65, 0x6d, 0x6f, 0x72,
	0x79, 0x55, 0x73, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a,
	0x06, 0x6e, 0x6f, 0x64, 0x65, 0x49, 0x50, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6e,
	0x6f, 0x64, 0x65, 0x49, 0x50, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x72, 0x74, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x73, 0x74, 0x61, 0x72, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x65,
	0x6e, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x65, 0x6e, 0x64, 0x12, 0x1e, 0x0a,
	0x0a, 0x64, 0x61, 0x74, 0x61, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0a, 0x64, 0x61, 0x74, 0x61, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x22, 0xaf, 0x01,
	0x0a, 0x21, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x51, 0x75, 0x65, 0x75,
	0x65, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x55, 0x73, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x71, 0x75, 0x65,
	0x75, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x71, 0x75,
	0x65, 0x75, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x73, 0x74, 0x61, 0x72, 0x74, 0x12, 0x10, 0x0a,
	0x03, 0x65, 0x6e, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x65, 0x6e, 0x64, 0x12,
	0x1e, 0x0a, 0x0a, 0x64, 0x61, 0x74, 0x61, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0a, 0x64, 0x61, 0x74, 0x61, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x22,
	0xc4, 0x01, 0x0a, 0x22, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x57, 0x6f,
	0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x47, 0x50, 0x55, 0x55, 0x73, 0x61, 0x67, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77,
	0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05,
	0x73, 0x74, 0x61, 0x72, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x65, 0x6e, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x03, 0x65, 0x6e, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x64, 0x61, 0x74, 0x61, 0x50, 0x6f, 0x69, 0x6e,
	0x74, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x64, 0x61, 0x74, 0x61, 0x50, 0x6f,
	0x69, 0x6e, 0x74, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x18, 0x0a, 0x07,
	0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63,
	0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x22, 0x97, 0x01, 0x0a, 0x1d, 0x51, 0x75, 0x65, 0x72, 0x79,
	0x52, 0x61, 0x6e, 0x67, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x47, 0x50, 0x55, 0x55, 0x73, 0x61, 0x67,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x6e, 0x6f, 0x64, 0x65,
	0x49, 0x50, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6e, 0x6f, 0x64, 0x65, 0x49, 0x50,
	0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x72, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x05, 0x73, 0x74, 0x61, 0x72, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x65, 0x6e, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x03, 0x65, 0x6e, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x64, 0x61, 0x74, 0x61,
	0x50, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x64, 0x61,
	0x74, 0x61, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69,
	0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e,
	0x22, 0xc4, 0x01, 0x0a, 0x1e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x51,
	0x75, 0x65, 0x75, 0x65, 0x47, 0x50, 0x55, 0x55, 0x73, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x71, 0x75, 0x65,
	0x75, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x71, 0x75,
	0x65, 0x75, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x73, 0x74, 0x61, 0x72, 0x74, 0x12, 0x10, 0x0a,
	0x03, 0x65, 0x6e, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x65, 0x6e, 0x64, 0x12,
	0x1e, 0x0a, 0x0a, 0x64, 0x61, 0x74, 0x61, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0a, 0x64, 0x61, 0x74, 0x61, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x12,
	0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x32, 0xe8, 0x0f, 0x0a, 0x0e, 0x4d, 0x65, 0x74, 0x72,
	0x69, 0x63, 0x73, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x8b, 0x01, 0x0a, 0x0a, 0x51,
	0x75, 0x65, 0x72, 0x79, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x2b, 0x2e, 0x61, 0x70, 0x69, 0x73,
	0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2a, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69,
	0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x22, 0x24, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1e, 0x12, 0x1c, 0x2f, 0x61, 0x70, 0x69,
	0x73, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x2f, 0x71, 0x75, 0x65,
	0x72, 0x79, 0x2d, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x76, 0x0a, 0x05, 0x51, 0x75, 0x65, 0x72,
	0x79, 0x12, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69,
	0x6f, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75, 0x65,
	0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x73,
	0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x22, 0x1e, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x18, 0x12, 0x16, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f,
	0x76, 0x31, 0x2f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x2f, 0x71, 0x75, 0x65, 0x72, 0x79,
	0x12, 0xc0, 0x01, 0x0a, 0x16, 0x51, 0x75, 0x65, 0x72, 0x79, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x43, 0x50, 0x55, 0x55, 0x73, 0x61, 0x67, 0x65, 0x12, 0x3c, 0x2e, 0x61, 0x70,
	0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6d, 0x65, 0x74, 0x72,
	0x69, 0x63, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x61, 0x6e, 0x67,
	0x65, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x43, 0x50, 0x55, 0x55, 0x73, 0x61,
	0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2a, 0x2e, 0x61, 0x70, 0x69, 0x73,
	0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x3c, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x36, 0x12, 0x34, 0x2f,
	0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x2f, 0x7b, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x7d, 0x2f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x2f, 0x63, 0x70, 0x75, 0x2d, 0x75, 0x73,
	0x61, 0x67, 0x65, 0x12, 0xaa, 0x01, 0x0a, 0x11, 0x51, 0x75, 0x65, 0x72, 0x79, 0x4e, 0x6f, 0x64,
	0x65, 0x43, 0x50, 0x55, 0x55, 0x73, 0x61, 0x67, 0x65, 0x12, 0x37, 0x2e, 0x61, 0x70, 0x69, 0x73,
	0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x4e,
	0x6f, 0x64, 0x65, 0x43, 0x50, 0x55, 0x55, 0x73, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x2a, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64,
	0x69, 0x6f, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75,
	0x65, 0x72, 0x79, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x30,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2a, 0x12, 0x28, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31,
	0x2f, 0x6e, 0x6f, 0x64, 0x65, 0x2f, 0x7b, 0x6e, 0x6f, 0x64, 0x65, 0x49, 0x50, 0x7d, 0x2f, 0x6d,
	0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x2f, 0x63, 0x70, 0x75, 0x2d, 0x75, 0x73, 0x61, 0x67, 0x65,
	0x12, 0xca, 0x01, 0x0a, 0x12, 0x51, 0x75, 0x65, 0x72, 0x79, 0x51, 0x75, 0x65, 0x75, 0x65, 0x43,
	0x50, 0x55, 0x55, 0x73, 0x61, 0x67, 0x65, 0x12, 0x38, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61,
	0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x2e,
	0x76, 0x31, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x51, 0x75, 0x65,
	0x75, 0x65, 0x43, 0x50, 0x55, 0x55, 0x73, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x2a, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69,
	0x6f, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75, 0x65,
	0x72, 0x79, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x4e, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x48, 0x12, 0x46, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f,
	0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2f, 0x7b, 0x77, 0x6f, 0x72, 0x6b, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2f,
	0x7b, 0x71, 0x75, 0x65, 0x75, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x6d, 0x65, 0x74, 0x72,
	0x69, 0x63, 0x73, 0x2f, 0x63, 0x70, 0x75, 0x2d, 0x75, 0x73, 0x61, 0x67, 0x65, 0x12, 0xc9, 0x01,
	0x0a, 0x19, 0x51, 0x75, 0x65, 0x72, 0x79, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x55, 0x73, 0x61, 0x67, 0x65, 0x12, 0x3f, 0x2e, 0x61, 0x70,
	0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6d, 0x65, 0x74, 0x72,
	0x69, 0x63, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x61, 0x6e, 0x67,
	0x65, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79,
	0x55, 0x73, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2a, 0x2e, 0x61,
	0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6d, 0x65, 0x74,
	0x72, 0x69, 0x63, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x61, 0x6e,
	0x67, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x3f, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x39,
	0x12, 0x37, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x2f, 0x7b, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x2f, 0x6d, 0x65, 0x6d,
	0x6f, 0x72, 0x79, 0x2d, 0x75, 0x73, 0x61, 0x67, 0x65, 0x12, 0xb3, 0x01, 0x0a, 0x14, 0x51, 0x75,
	0x65, 0x72, 0x79, 0x4e, 0x6f, 0x64, 0x65, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x55, 0x73, 0x61,
	0x67, 0x65, 0x12, 0x3a, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64,
	0x69, 0x6f, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75,
	0x65, 0x72, 0x79, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x4d, 0x65, 0x6d, 0x6f,
	0x72, 0x79, 0x55, 0x73, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2a,
	0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6d,
	0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52,
	0x61, 0x6e, 0x67, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x33, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x2d, 0x12, 0x2b, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x6e, 0x6f, 0x64,
	0x65, 0x2f, 0x7b, 0x6e, 0x6f, 0x64, 0x65, 0x49, 0x50, 0x7d, 0x2f, 0x6d, 0x65, 0x74, 0x72, 0x69,
	0x63, 0x73, 0x2f, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x2d, 0x75, 0x73, 0x61, 0x67, 0x65, 0x12,
	0xd3, 0x01, 0x0a, 0x15, 0x51, 0x75, 0x65, 0x72, 0x79, 0x51, 0x75, 0x65, 0x75, 0x65, 0x4d, 0x65,
	0x6d, 0x6f, 0x72, 0x79, 0x55, 0x73, 0x61, 0x67, 0x65, 0x12, 0x3b, 0x2e, 0x61, 0x70, 0x69, 0x73,
	0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x51,
	0x75, 0x65, 0x75, 0x65, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x55, 0x73, 0x61, 0x67, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2a, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69,
	0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x22, 0x51, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x4b, 0x12, 0x49, 0x2f, 0x61, 0x70, 0x69,
	0x73, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2f, 0x7b,
	0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x71,
	0x75, 0x65, 0x75, 0x65, 0x2f, 0x7b, 0x71, 0x75, 0x65, 0x75, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d,
	0x2f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x2f, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x2d,
	0x75, 0x73, 0x61, 0x67, 0x65, 0x12, 0xc0, 0x01, 0x0a, 0x16, 0x51, 0x75, 0x65, 0x72, 0x79, 0x57,
	0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x47, 0x50, 0x55, 0x55, 0x73, 0x61, 0x67, 0x65,
	0x12, 0x3c, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f,
	0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75, 0x65, 0x72,
	0x79, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x47,
	0x50, 0x55, 0x55, 0x73, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2a,
	0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6d,
	0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52,
	0x61, 0x6e, 0x67, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x3c, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x36, 0x12, 0x34, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x6f, 0x72,
	0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2f, 0x7b, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x2f, 0x67,
	0x70, 0x75, 0x2d, 0x75, 0x73, 0x61, 0x67, 0x65, 0x12, 0xaa, 0x01, 0x0a, 0x11, 0x51, 0x75, 0x65,
	0x72, 0x79, 0x4e, 0x6f, 0x64, 0x65, 0x47, 0x50, 0x55, 0x55, 0x73, 0x61, 0x67, 0x65, 0x12, 0x37,
	0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6d,
	0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52,
	0x61, 0x6e, 0x67, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x47, 0x50, 0x55, 0x55, 0x73, 0x61, 0x67, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2a, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61,
	0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x2e,
	0x76, 0x31, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x22, 0x30, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2a, 0x12, 0x28, 0x2f, 0x61, 0x70,
	0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x6e, 0x6f, 0x64, 0x65, 0x2f, 0x7b, 0x6e, 0x6f, 0x64, 0x65,
	0x49, 0x50, 0x7d, 0x2f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x2f, 0x67, 0x70, 0x75, 0x2d,
	0x75, 0x73, 0x61, 0x67, 0x65, 0x12, 0xca, 0x01, 0x0a, 0x12, 0x51, 0x75, 0x65, 0x72, 0x79, 0x51,
	0x75, 0x65, 0x75, 0x65, 0x47, 0x50, 0x55, 0x55, 0x73, 0x61, 0x67, 0x65, 0x12, 0x38, 0x2e, 0x61,
	0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6d, 0x65, 0x74,
	0x72, 0x69, 0x63, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x61, 0x6e,
	0x67, 0x65, 0x51, 0x75, 0x65, 0x75, 0x65, 0x47, 0x50, 0x55, 0x55, 0x73, 0x61, 0x67, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2a, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69,
	0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x22, 0x4e, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x48, 0x12, 0x46, 0x2f, 0x61, 0x70, 0x69,
	0x73, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2f, 0x7b,
	0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x71,
	0x75, 0x65, 0x75, 0x65, 0x2f, 0x7b, 0x71, 0x75, 0x65, 0x75, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d,
	0x2f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x2f, 0x67, 0x70, 0x75, 0x2d, 0x75, 0x73, 0x61,
	0x67, 0x65, 0x42, 0x4a, 0x5a, 0x48, 0x67, 0x69, 0x74, 0x2e, 0x6c, 0x69, 0x61, 0x6e, 0x6a, 0x69,
	0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6e, 0x61, 0x74, 0x69, 0x76,
	0x65, 0x2f, 0x6b, 0x69, 0x63, 0x2f, 0x6b, 0x69, 0x63, 0x2d, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f,
	0x72, 0x6d, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f,
	0x2f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_aistudio_metrics_v1_metrics_proto_rawDescOnce sync.Once
	file_aistudio_metrics_v1_metrics_proto_rawDescData = file_aistudio_metrics_v1_metrics_proto_rawDesc
)

func file_aistudio_metrics_v1_metrics_proto_rawDescGZIP() []byte {
	file_aistudio_metrics_v1_metrics_proto_rawDescOnce.Do(func() {
		file_aistudio_metrics_v1_metrics_proto_rawDescData = protoimpl.X.CompressGZIP(file_aistudio_metrics_v1_metrics_proto_rawDescData)
	})
	return file_aistudio_metrics_v1_metrics_proto_rawDescData
}

var file_aistudio_metrics_v1_metrics_proto_msgTypes = make([]protoimpl.MessageInfo, 16)
var file_aistudio_metrics_v1_metrics_proto_goTypes = []any{
	(*QueryRangeRequest)(nil),                     // 0: apis.aistudio.metrics.v1.QueryRangeRequest
	(*QueryRequest)(nil),                          // 1: apis.aistudio.metrics.v1.QueryRequest
	(*QueryResult)(nil),                           // 2: apis.aistudio.metrics.v1.QueryResult
	(*QueryRangeResult)(nil),                      // 3: apis.aistudio.metrics.v1.QueryRangeResult
	(*Series)(nil),                                // 4: apis.aistudio.metrics.v1.Series
	(*QueryRangeWorkspaceCPUUsageRequest)(nil),    // 5: apis.aistudio.metrics.v1.QueryRangeWorkspaceCPUUsageRequest
	(*QueryRangeNodeCPUUsageRequest)(nil),         // 6: apis.aistudio.metrics.v1.QueryRangeNodeCPUUsageRequest
	(*QueryRangeQueueCPUUsageRequest)(nil),        // 7: apis.aistudio.metrics.v1.QueryRangeQueueCPUUsageRequest
	(*QueryRangeWorkspaceMemoryUsageRequest)(nil), // 8: apis.aistudio.metrics.v1.QueryRangeWorkspaceMemoryUsageRequest
	(*QueryRangeNodeMemoryUsageRequest)(nil),      // 9: apis.aistudio.metrics.v1.QueryRangeNodeMemoryUsageRequest
	(*QueryRangeQueueMemoryUsageRequest)(nil),     // 10: apis.aistudio.metrics.v1.QueryRangeQueueMemoryUsageRequest
	(*QueryRangeWorkspaceGPUUsageRequest)(nil),    // 11: apis.aistudio.metrics.v1.QueryRangeWorkspaceGPUUsageRequest
	(*QueryRangeNodeGPUUsageRequest)(nil),         // 12: apis.aistudio.metrics.v1.QueryRangeNodeGPUUsageRequest
	(*QueryRangeQueueGPUUsageRequest)(nil),        // 13: apis.aistudio.metrics.v1.QueryRangeQueueGPUUsageRequest
	nil,                                           // 14: apis.aistudio.metrics.v1.QueryResult.LabelsEntry
	nil,                                           // 15: apis.aistudio.metrics.v1.Series.LabelsEntry
}
var file_aistudio_metrics_v1_metrics_proto_depIdxs = []int32{
	14, // 0: apis.aistudio.metrics.v1.QueryResult.labels:type_name -> apis.aistudio.metrics.v1.QueryResult.LabelsEntry
	4,  // 1: apis.aistudio.metrics.v1.QueryRangeResult.series:type_name -> apis.aistudio.metrics.v1.Series
	15, // 2: apis.aistudio.metrics.v1.Series.labels:type_name -> apis.aistudio.metrics.v1.Series.LabelsEntry
	0,  // 3: apis.aistudio.metrics.v1.MetricsService.QueryRange:input_type -> apis.aistudio.metrics.v1.QueryRangeRequest
	1,  // 4: apis.aistudio.metrics.v1.MetricsService.Query:input_type -> apis.aistudio.metrics.v1.QueryRequest
	5,  // 5: apis.aistudio.metrics.v1.MetricsService.QueryWorkspaceCPUUsage:input_type -> apis.aistudio.metrics.v1.QueryRangeWorkspaceCPUUsageRequest
	6,  // 6: apis.aistudio.metrics.v1.MetricsService.QueryNodeCPUUsage:input_type -> apis.aistudio.metrics.v1.QueryRangeNodeCPUUsageRequest
	7,  // 7: apis.aistudio.metrics.v1.MetricsService.QueryQueueCPUUsage:input_type -> apis.aistudio.metrics.v1.QueryRangeQueueCPUUsageRequest
	8,  // 8: apis.aistudio.metrics.v1.MetricsService.QueryWorkspaceMemoryUsage:input_type -> apis.aistudio.metrics.v1.QueryRangeWorkspaceMemoryUsageRequest
	9,  // 9: apis.aistudio.metrics.v1.MetricsService.QueryNodeMemoryUsage:input_type -> apis.aistudio.metrics.v1.QueryRangeNodeMemoryUsageRequest
	10, // 10: apis.aistudio.metrics.v1.MetricsService.QueryQueueMemoryUsage:input_type -> apis.aistudio.metrics.v1.QueryRangeQueueMemoryUsageRequest
	11, // 11: apis.aistudio.metrics.v1.MetricsService.QueryWorkspaceGPUUsage:input_type -> apis.aistudio.metrics.v1.QueryRangeWorkspaceGPUUsageRequest
	12, // 12: apis.aistudio.metrics.v1.MetricsService.QueryNodeGPUUsage:input_type -> apis.aistudio.metrics.v1.QueryRangeNodeGPUUsageRequest
	13, // 13: apis.aistudio.metrics.v1.MetricsService.QueryQueueGPUUsage:input_type -> apis.aistudio.metrics.v1.QueryRangeQueueGPUUsageRequest
	3,  // 14: apis.aistudio.metrics.v1.MetricsService.QueryRange:output_type -> apis.aistudio.metrics.v1.QueryRangeResult
	2,  // 15: apis.aistudio.metrics.v1.MetricsService.Query:output_type -> apis.aistudio.metrics.v1.QueryResult
	3,  // 16: apis.aistudio.metrics.v1.MetricsService.QueryWorkspaceCPUUsage:output_type -> apis.aistudio.metrics.v1.QueryRangeResult
	3,  // 17: apis.aistudio.metrics.v1.MetricsService.QueryNodeCPUUsage:output_type -> apis.aistudio.metrics.v1.QueryRangeResult
	3,  // 18: apis.aistudio.metrics.v1.MetricsService.QueryQueueCPUUsage:output_type -> apis.aistudio.metrics.v1.QueryRangeResult
	3,  // 19: apis.aistudio.metrics.v1.MetricsService.QueryWorkspaceMemoryUsage:output_type -> apis.aistudio.metrics.v1.QueryRangeResult
	3,  // 20: apis.aistudio.metrics.v1.MetricsService.QueryNodeMemoryUsage:output_type -> apis.aistudio.metrics.v1.QueryRangeResult
	3,  // 21: apis.aistudio.metrics.v1.MetricsService.QueryQueueMemoryUsage:output_type -> apis.aistudio.metrics.v1.QueryRangeResult
	3,  // 22: apis.aistudio.metrics.v1.MetricsService.QueryWorkspaceGPUUsage:output_type -> apis.aistudio.metrics.v1.QueryRangeResult
	3,  // 23: apis.aistudio.metrics.v1.MetricsService.QueryNodeGPUUsage:output_type -> apis.aistudio.metrics.v1.QueryRangeResult
	3,  // 24: apis.aistudio.metrics.v1.MetricsService.QueryQueueGPUUsage:output_type -> apis.aistudio.metrics.v1.QueryRangeResult
	14, // [14:25] is the sub-list for method output_type
	3,  // [3:14] is the sub-list for method input_type
	3,  // [3:3] is the sub-list for extension type_name
	3,  // [3:3] is the sub-list for extension extendee
	0,  // [0:3] is the sub-list for field type_name
}

func init() { file_aistudio_metrics_v1_metrics_proto_init() }
func file_aistudio_metrics_v1_metrics_proto_init() {
	if File_aistudio_metrics_v1_metrics_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_aistudio_metrics_v1_metrics_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*QueryRangeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_metrics_v1_metrics_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*QueryRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_metrics_v1_metrics_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*QueryResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_metrics_v1_metrics_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*QueryRangeResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_metrics_v1_metrics_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*Series); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_metrics_v1_metrics_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*QueryRangeWorkspaceCPUUsageRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_metrics_v1_metrics_proto_msgTypes[6].Exporter = func(v any, i int) any {
			switch v := v.(*QueryRangeNodeCPUUsageRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_metrics_v1_metrics_proto_msgTypes[7].Exporter = func(v any, i int) any {
			switch v := v.(*QueryRangeQueueCPUUsageRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_metrics_v1_metrics_proto_msgTypes[8].Exporter = func(v any, i int) any {
			switch v := v.(*QueryRangeWorkspaceMemoryUsageRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_metrics_v1_metrics_proto_msgTypes[9].Exporter = func(v any, i int) any {
			switch v := v.(*QueryRangeNodeMemoryUsageRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_metrics_v1_metrics_proto_msgTypes[10].Exporter = func(v any, i int) any {
			switch v := v.(*QueryRangeQueueMemoryUsageRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_metrics_v1_metrics_proto_msgTypes[11].Exporter = func(v any, i int) any {
			switch v := v.(*QueryRangeWorkspaceGPUUsageRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_metrics_v1_metrics_proto_msgTypes[12].Exporter = func(v any, i int) any {
			switch v := v.(*QueryRangeNodeGPUUsageRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_metrics_v1_metrics_proto_msgTypes[13].Exporter = func(v any, i int) any {
			switch v := v.(*QueryRangeQueueGPUUsageRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_aistudio_metrics_v1_metrics_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   16,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_aistudio_metrics_v1_metrics_proto_goTypes,
		DependencyIndexes: file_aistudio_metrics_v1_metrics_proto_depIdxs,
		MessageInfos:      file_aistudio_metrics_v1_metrics_proto_msgTypes,
	}.Build()
	File_aistudio_metrics_v1_metrics_proto = out.File
	file_aistudio_metrics_v1_metrics_proto_rawDesc = nil
	file_aistudio_metrics_v1_metrics_proto_goTypes = nil
	file_aistudio_metrics_v1_metrics_proto_depIdxs = nil
}
