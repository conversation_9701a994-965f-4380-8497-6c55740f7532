// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v5.29.3
// source: aistudio/metrics/v1/metrics.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	MetricsService_QueryRange_FullMethodName                = "/apis.aistudio.metrics.v1.MetricsService/QueryRange"
	MetricsService_Query_FullMethodName                     = "/apis.aistudio.metrics.v1.MetricsService/Query"
	MetricsService_QueryWorkspaceCPUUsage_FullMethodName    = "/apis.aistudio.metrics.v1.MetricsService/QueryWorkspaceCPUUsage"
	MetricsService_QueryNodeCPUUsage_FullMethodName         = "/apis.aistudio.metrics.v1.MetricsService/QueryNodeCPUUsage"
	MetricsService_QueryQueueCPUUsage_FullMethodName        = "/apis.aistudio.metrics.v1.MetricsService/QueryQueueCPUUsage"
	MetricsService_QueryWorkspaceMemoryUsage_FullMethodName = "/apis.aistudio.metrics.v1.MetricsService/QueryWorkspaceMemoryUsage"
	MetricsService_QueryNodeMemoryUsage_FullMethodName      = "/apis.aistudio.metrics.v1.MetricsService/QueryNodeMemoryUsage"
	MetricsService_QueryQueueMemoryUsage_FullMethodName     = "/apis.aistudio.metrics.v1.MetricsService/QueryQueueMemoryUsage"
	MetricsService_QueryWorkspaceGPUUsage_FullMethodName    = "/apis.aistudio.metrics.v1.MetricsService/QueryWorkspaceGPUUsage"
	MetricsService_QueryNodeGPUUsage_FullMethodName         = "/apis.aistudio.metrics.v1.MetricsService/QueryNodeGPUUsage"
	MetricsService_QueryQueueGPUUsage_FullMethodName        = "/apis.aistudio.metrics.v1.MetricsService/QueryQueueGPUUsage"
)

// MetricsServiceClient is the client API for MetricsService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type MetricsServiceClient interface {
	QueryRange(ctx context.Context, in *QueryRangeRequest, opts ...grpc.CallOption) (*QueryRangeResult, error)
	Query(ctx context.Context, in *QueryRequest, opts ...grpc.CallOption) (*QueryResult, error)
	QueryWorkspaceCPUUsage(ctx context.Context, in *QueryRangeWorkspaceCPUUsageRequest, opts ...grpc.CallOption) (*QueryRangeResult, error)
	QueryNodeCPUUsage(ctx context.Context, in *QueryRangeNodeCPUUsageRequest, opts ...grpc.CallOption) (*QueryRangeResult, error)
	QueryQueueCPUUsage(ctx context.Context, in *QueryRangeQueueCPUUsageRequest, opts ...grpc.CallOption) (*QueryRangeResult, error)
	QueryWorkspaceMemoryUsage(ctx context.Context, in *QueryRangeWorkspaceMemoryUsageRequest, opts ...grpc.CallOption) (*QueryRangeResult, error)
	QueryNodeMemoryUsage(ctx context.Context, in *QueryRangeNodeMemoryUsageRequest, opts ...grpc.CallOption) (*QueryRangeResult, error)
	QueryQueueMemoryUsage(ctx context.Context, in *QueryRangeQueueMemoryUsageRequest, opts ...grpc.CallOption) (*QueryRangeResult, error)
	QueryWorkspaceGPUUsage(ctx context.Context, in *QueryRangeWorkspaceGPUUsageRequest, opts ...grpc.CallOption) (*QueryRangeResult, error)
	QueryNodeGPUUsage(ctx context.Context, in *QueryRangeNodeGPUUsageRequest, opts ...grpc.CallOption) (*QueryRangeResult, error)
	QueryQueueGPUUsage(ctx context.Context, in *QueryRangeQueueGPUUsageRequest, opts ...grpc.CallOption) (*QueryRangeResult, error)
}

type metricsServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewMetricsServiceClient(cc grpc.ClientConnInterface) MetricsServiceClient {
	return &metricsServiceClient{cc}
}

func (c *metricsServiceClient) QueryRange(ctx context.Context, in *QueryRangeRequest, opts ...grpc.CallOption) (*QueryRangeResult, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(QueryRangeResult)
	err := c.cc.Invoke(ctx, MetricsService_QueryRange_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *metricsServiceClient) Query(ctx context.Context, in *QueryRequest, opts ...grpc.CallOption) (*QueryResult, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(QueryResult)
	err := c.cc.Invoke(ctx, MetricsService_Query_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *metricsServiceClient) QueryWorkspaceCPUUsage(ctx context.Context, in *QueryRangeWorkspaceCPUUsageRequest, opts ...grpc.CallOption) (*QueryRangeResult, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(QueryRangeResult)
	err := c.cc.Invoke(ctx, MetricsService_QueryWorkspaceCPUUsage_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *metricsServiceClient) QueryNodeCPUUsage(ctx context.Context, in *QueryRangeNodeCPUUsageRequest, opts ...grpc.CallOption) (*QueryRangeResult, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(QueryRangeResult)
	err := c.cc.Invoke(ctx, MetricsService_QueryNodeCPUUsage_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *metricsServiceClient) QueryQueueCPUUsage(ctx context.Context, in *QueryRangeQueueCPUUsageRequest, opts ...grpc.CallOption) (*QueryRangeResult, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(QueryRangeResult)
	err := c.cc.Invoke(ctx, MetricsService_QueryQueueCPUUsage_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *metricsServiceClient) QueryWorkspaceMemoryUsage(ctx context.Context, in *QueryRangeWorkspaceMemoryUsageRequest, opts ...grpc.CallOption) (*QueryRangeResult, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(QueryRangeResult)
	err := c.cc.Invoke(ctx, MetricsService_QueryWorkspaceMemoryUsage_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *metricsServiceClient) QueryNodeMemoryUsage(ctx context.Context, in *QueryRangeNodeMemoryUsageRequest, opts ...grpc.CallOption) (*QueryRangeResult, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(QueryRangeResult)
	err := c.cc.Invoke(ctx, MetricsService_QueryNodeMemoryUsage_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *metricsServiceClient) QueryQueueMemoryUsage(ctx context.Context, in *QueryRangeQueueMemoryUsageRequest, opts ...grpc.CallOption) (*QueryRangeResult, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(QueryRangeResult)
	err := c.cc.Invoke(ctx, MetricsService_QueryQueueMemoryUsage_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *metricsServiceClient) QueryWorkspaceGPUUsage(ctx context.Context, in *QueryRangeWorkspaceGPUUsageRequest, opts ...grpc.CallOption) (*QueryRangeResult, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(QueryRangeResult)
	err := c.cc.Invoke(ctx, MetricsService_QueryWorkspaceGPUUsage_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *metricsServiceClient) QueryNodeGPUUsage(ctx context.Context, in *QueryRangeNodeGPUUsageRequest, opts ...grpc.CallOption) (*QueryRangeResult, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(QueryRangeResult)
	err := c.cc.Invoke(ctx, MetricsService_QueryNodeGPUUsage_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *metricsServiceClient) QueryQueueGPUUsage(ctx context.Context, in *QueryRangeQueueGPUUsageRequest, opts ...grpc.CallOption) (*QueryRangeResult, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(QueryRangeResult)
	err := c.cc.Invoke(ctx, MetricsService_QueryQueueGPUUsage_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MetricsServiceServer is the server API for MetricsService service.
// All implementations must embed UnimplementedMetricsServiceServer
// for forward compatibility
type MetricsServiceServer interface {
	QueryRange(context.Context, *QueryRangeRequest) (*QueryRangeResult, error)
	Query(context.Context, *QueryRequest) (*QueryResult, error)
	QueryWorkspaceCPUUsage(context.Context, *QueryRangeWorkspaceCPUUsageRequest) (*QueryRangeResult, error)
	QueryNodeCPUUsage(context.Context, *QueryRangeNodeCPUUsageRequest) (*QueryRangeResult, error)
	QueryQueueCPUUsage(context.Context, *QueryRangeQueueCPUUsageRequest) (*QueryRangeResult, error)
	QueryWorkspaceMemoryUsage(context.Context, *QueryRangeWorkspaceMemoryUsageRequest) (*QueryRangeResult, error)
	QueryNodeMemoryUsage(context.Context, *QueryRangeNodeMemoryUsageRequest) (*QueryRangeResult, error)
	QueryQueueMemoryUsage(context.Context, *QueryRangeQueueMemoryUsageRequest) (*QueryRangeResult, error)
	QueryWorkspaceGPUUsage(context.Context, *QueryRangeWorkspaceGPUUsageRequest) (*QueryRangeResult, error)
	QueryNodeGPUUsage(context.Context, *QueryRangeNodeGPUUsageRequest) (*QueryRangeResult, error)
	QueryQueueGPUUsage(context.Context, *QueryRangeQueueGPUUsageRequest) (*QueryRangeResult, error)
	mustEmbedUnimplementedMetricsServiceServer()
}

// UnimplementedMetricsServiceServer must be embedded to have forward compatible implementations.
type UnimplementedMetricsServiceServer struct {
}

func (UnimplementedMetricsServiceServer) QueryRange(context.Context, *QueryRangeRequest) (*QueryRangeResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryRange not implemented")
}
func (UnimplementedMetricsServiceServer) Query(context.Context, *QueryRequest) (*QueryResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Query not implemented")
}
func (UnimplementedMetricsServiceServer) QueryWorkspaceCPUUsage(context.Context, *QueryRangeWorkspaceCPUUsageRequest) (*QueryRangeResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryWorkspaceCPUUsage not implemented")
}
func (UnimplementedMetricsServiceServer) QueryNodeCPUUsage(context.Context, *QueryRangeNodeCPUUsageRequest) (*QueryRangeResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryNodeCPUUsage not implemented")
}
func (UnimplementedMetricsServiceServer) QueryQueueCPUUsage(context.Context, *QueryRangeQueueCPUUsageRequest) (*QueryRangeResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryQueueCPUUsage not implemented")
}
func (UnimplementedMetricsServiceServer) QueryWorkspaceMemoryUsage(context.Context, *QueryRangeWorkspaceMemoryUsageRequest) (*QueryRangeResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryWorkspaceMemoryUsage not implemented")
}
func (UnimplementedMetricsServiceServer) QueryNodeMemoryUsage(context.Context, *QueryRangeNodeMemoryUsageRequest) (*QueryRangeResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryNodeMemoryUsage not implemented")
}
func (UnimplementedMetricsServiceServer) QueryQueueMemoryUsage(context.Context, *QueryRangeQueueMemoryUsageRequest) (*QueryRangeResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryQueueMemoryUsage not implemented")
}
func (UnimplementedMetricsServiceServer) QueryWorkspaceGPUUsage(context.Context, *QueryRangeWorkspaceGPUUsageRequest) (*QueryRangeResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryWorkspaceGPUUsage not implemented")
}
func (UnimplementedMetricsServiceServer) QueryNodeGPUUsage(context.Context, *QueryRangeNodeGPUUsageRequest) (*QueryRangeResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryNodeGPUUsage not implemented")
}
func (UnimplementedMetricsServiceServer) QueryQueueGPUUsage(context.Context, *QueryRangeQueueGPUUsageRequest) (*QueryRangeResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryQueueGPUUsage not implemented")
}
func (UnimplementedMetricsServiceServer) mustEmbedUnimplementedMetricsServiceServer() {}

// UnsafeMetricsServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to MetricsServiceServer will
// result in compilation errors.
type UnsafeMetricsServiceServer interface {
	mustEmbedUnimplementedMetricsServiceServer()
}

func RegisterMetricsServiceServer(s grpc.ServiceRegistrar, srv MetricsServiceServer) {
	s.RegisterService(&MetricsService_ServiceDesc, srv)
}

func _MetricsService_QueryRange_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryRangeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MetricsServiceServer).QueryRange(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MetricsService_QueryRange_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MetricsServiceServer).QueryRange(ctx, req.(*QueryRangeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MetricsService_Query_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MetricsServiceServer).Query(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MetricsService_Query_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MetricsServiceServer).Query(ctx, req.(*QueryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MetricsService_QueryWorkspaceCPUUsage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryRangeWorkspaceCPUUsageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MetricsServiceServer).QueryWorkspaceCPUUsage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MetricsService_QueryWorkspaceCPUUsage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MetricsServiceServer).QueryWorkspaceCPUUsage(ctx, req.(*QueryRangeWorkspaceCPUUsageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MetricsService_QueryNodeCPUUsage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryRangeNodeCPUUsageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MetricsServiceServer).QueryNodeCPUUsage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MetricsService_QueryNodeCPUUsage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MetricsServiceServer).QueryNodeCPUUsage(ctx, req.(*QueryRangeNodeCPUUsageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MetricsService_QueryQueueCPUUsage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryRangeQueueCPUUsageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MetricsServiceServer).QueryQueueCPUUsage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MetricsService_QueryQueueCPUUsage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MetricsServiceServer).QueryQueueCPUUsage(ctx, req.(*QueryRangeQueueCPUUsageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MetricsService_QueryWorkspaceMemoryUsage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryRangeWorkspaceMemoryUsageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MetricsServiceServer).QueryWorkspaceMemoryUsage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MetricsService_QueryWorkspaceMemoryUsage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MetricsServiceServer).QueryWorkspaceMemoryUsage(ctx, req.(*QueryRangeWorkspaceMemoryUsageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MetricsService_QueryNodeMemoryUsage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryRangeNodeMemoryUsageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MetricsServiceServer).QueryNodeMemoryUsage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MetricsService_QueryNodeMemoryUsage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MetricsServiceServer).QueryNodeMemoryUsage(ctx, req.(*QueryRangeNodeMemoryUsageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MetricsService_QueryQueueMemoryUsage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryRangeQueueMemoryUsageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MetricsServiceServer).QueryQueueMemoryUsage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MetricsService_QueryQueueMemoryUsage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MetricsServiceServer).QueryQueueMemoryUsage(ctx, req.(*QueryRangeQueueMemoryUsageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MetricsService_QueryWorkspaceGPUUsage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryRangeWorkspaceGPUUsageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MetricsServiceServer).QueryWorkspaceGPUUsage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MetricsService_QueryWorkspaceGPUUsage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MetricsServiceServer).QueryWorkspaceGPUUsage(ctx, req.(*QueryRangeWorkspaceGPUUsageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MetricsService_QueryNodeGPUUsage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryRangeNodeGPUUsageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MetricsServiceServer).QueryNodeGPUUsage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MetricsService_QueryNodeGPUUsage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MetricsServiceServer).QueryNodeGPUUsage(ctx, req.(*QueryRangeNodeGPUUsageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MetricsService_QueryQueueGPUUsage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryRangeQueueGPUUsageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MetricsServiceServer).QueryQueueGPUUsage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MetricsService_QueryQueueGPUUsage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MetricsServiceServer).QueryQueueGPUUsage(ctx, req.(*QueryRangeQueueGPUUsageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// MetricsService_ServiceDesc is the grpc.ServiceDesc for MetricsService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var MetricsService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "apis.aistudio.metrics.v1.MetricsService",
	HandlerType: (*MetricsServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "QueryRange",
			Handler:    _MetricsService_QueryRange_Handler,
		},
		{
			MethodName: "Query",
			Handler:    _MetricsService_Query_Handler,
		},
		{
			MethodName: "QueryWorkspaceCPUUsage",
			Handler:    _MetricsService_QueryWorkspaceCPUUsage_Handler,
		},
		{
			MethodName: "QueryNodeCPUUsage",
			Handler:    _MetricsService_QueryNodeCPUUsage_Handler,
		},
		{
			MethodName: "QueryQueueCPUUsage",
			Handler:    _MetricsService_QueryQueueCPUUsage_Handler,
		},
		{
			MethodName: "QueryWorkspaceMemoryUsage",
			Handler:    _MetricsService_QueryWorkspaceMemoryUsage_Handler,
		},
		{
			MethodName: "QueryNodeMemoryUsage",
			Handler:    _MetricsService_QueryNodeMemoryUsage_Handler,
		},
		{
			MethodName: "QueryQueueMemoryUsage",
			Handler:    _MetricsService_QueryQueueMemoryUsage_Handler,
		},
		{
			MethodName: "QueryWorkspaceGPUUsage",
			Handler:    _MetricsService_QueryWorkspaceGPUUsage_Handler,
		},
		{
			MethodName: "QueryNodeGPUUsage",
			Handler:    _MetricsService_QueryNodeGPUUsage_Handler,
		},
		{
			MethodName: "QueryQueueGPUUsage",
			Handler:    _MetricsService_QueryQueueGPUUsage_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "aistudio/metrics/v1/metrics.proto",
}
