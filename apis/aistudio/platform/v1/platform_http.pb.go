// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.3
// - protoc             v5.29.3
// source: aistudio/platform/v1/platform.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationPlatformServiceAddDragonfly = "/apis.aistudio.platform.v1.PlatformService/AddDragonfly"
const OperationPlatformServiceAddOrUpdateCubeFSCluster = "/apis.aistudio.platform.v1.PlatformService/AddOrUpdateCubeFSCluster"
const OperationPlatformServiceAddOrUpdateKubernetesCluster = "/apis.aistudio.platform.v1.PlatformService/AddOrUpdateKubernetesCluster"
const OperationPlatformServiceAddOrUpdateNodeSpecification = "/apis.aistudio.platform.v1.PlatformService/AddOrUpdateNodeSpecification"
const OperationPlatformServiceAssignNode = "/apis.aistudio.platform.v1.PlatformService/AssignNode"
const OperationPlatformServiceCreateDistributedCacheRegistration = "/apis.aistudio.platform.v1.PlatformService/CreateDistributedCacheRegistration"
const OperationPlatformServiceCubeFSClusterUpdateStatus = "/apis.aistudio.platform.v1.PlatformService/CubeFSClusterUpdateStatus"
const OperationPlatformServiceDeleteCubeFSCluster = "/apis.aistudio.platform.v1.PlatformService/DeleteCubeFSCluster"
const OperationPlatformServiceDeleteDistributedCacheRegistration = "/apis.aistudio.platform.v1.PlatformService/DeleteDistributedCacheRegistration"
const OperationPlatformServiceDeleteDragonfly = "/apis.aistudio.platform.v1.PlatformService/DeleteDragonfly"
const OperationPlatformServiceDeleteNodeSpecification = "/apis.aistudio.platform.v1.PlatformService/DeleteNodeSpecification"
const OperationPlatformServiceGetKubernetesCluster = "/apis.aistudio.platform.v1.PlatformService/GetKubernetesCluster"
const OperationPlatformServiceGetNodesInfoStatistics = "/apis.aistudio.platform.v1.PlatformService/GetNodesInfoStatistics"
const OperationPlatformServiceListDistributedCacheRegistrationClusters = "/apis.aistudio.platform.v1.PlatformService/ListDistributedCacheRegistrationClusters"
const OperationPlatformServiceListDistributedCacheRegistrationRegions = "/apis.aistudio.platform.v1.PlatformService/ListDistributedCacheRegistrationRegions"
const OperationPlatformServiceListDistributedCacheRegistrationZones = "/apis.aistudio.platform.v1.PlatformService/ListDistributedCacheRegistrationZones"
const OperationPlatformServiceListDistributedCacheRegistrations = "/apis.aistudio.platform.v1.PlatformService/ListDistributedCacheRegistrations"
const OperationPlatformServiceListDragonfly = "/apis.aistudio.platform.v1.PlatformService/ListDragonfly"
const OperationPlatformServiceListKubernetesClusters = "/apis.aistudio.platform.v1.PlatformService/ListKubernetesClusters"
const OperationPlatformServiceListNodeSpecifications = "/apis.aistudio.platform.v1.PlatformService/ListNodeSpecifications"
const OperationPlatformServiceListNodes = "/apis.aistudio.platform.v1.PlatformService/ListNodes"
const OperationPlatformServiceRecycleNode = "/apis.aistudio.platform.v1.PlatformService/RecycleNode"
const OperationPlatformServiceRemoveKubernetesCluster = "/apis.aistudio.platform.v1.PlatformService/RemoveKubernetesCluster"
const OperationPlatformServiceSetCubeFSDashboard = "/apis.aistudio.platform.v1.PlatformService/SetCubeFSDashboard"
const OperationPlatformServiceSetDragonfly = "/apis.aistudio.platform.v1.PlatformService/SetDragonfly"
const OperationPlatformServiceUpdateDistributedCacheRegistration = "/apis.aistudio.platform.v1.PlatformService/UpdateDistributedCacheRegistration"

type PlatformServiceHTTPServer interface {
	// AddDragonfly添加Dragonfly配置
	AddDragonfly(context.Context, *AddDragonflyRequest) (*emptypb.Empty, error)
	AddOrUpdateCubeFSCluster(context.Context, *AddOrUpdateCubeFSClusterRequest) (*emptypb.Empty, error)
	// AddOrUpdateKubernetesCluster 新增或者更新Kubernetes的集群信息
	AddOrUpdateKubernetesCluster(context.Context, *CreateKubernetesClusterRequest) (*KubernetesCluster, error)
	// AddOrUpdateNodeSpecification 新增节点规格
	AddOrUpdateNodeSpecification(context.Context, *NodeSpecification) (*NodeSpecification, error)
	// AssignNode 节点分配和回收
	AssignNode(context.Context, *AssignNodeRequest) (*emptypb.Empty, error)
	// CreateDistributedCacheRegistration 创建分布式缓存注册表
	CreateDistributedCacheRegistration(context.Context, *CreateDistributedCacheRegistrationRequest) (*CreateDistributedCacheRegistrationResponse, error)
	CubeFSClusterUpdateStatus(context.Context, *CubeFSClusterSetStatusRequest) (*emptypb.Empty, error)
	DeleteCubeFSCluster(context.Context, *DeleteCubeFSClusterRequest) (*emptypb.Empty, error)
	// DeleteDistributedCacheRegistration 删除分布式缓存注册表
	DeleteDistributedCacheRegistration(context.Context, *DeleteDistributedCacheRegistrationRequest) (*DeleteDistributedCacheRegistrationResponse, error)
	// DeleteDragonfly删除Dragonfly配置
	DeleteDragonfly(context.Context, *DeleteDragonflyRequest) (*emptypb.Empty, error)
	// DeleteNodeSpecification 删除节点规格
	DeleteNodeSpecification(context.Context, *DeleteNodeSpecificationRequest) (*emptypb.Empty, error)
	GetKubernetesCluster(context.Context, *GetKubernetesClusterRequest) (*KubernetesCluster, error)
	// GetNodesInfoStatistics 节点信息统计
	GetNodesInfoStatistics(context.Context, *GetNodesInfoStatisticsRequest) (*NodesInfoStatisticsResponse, error)
	// ListDistributedCacheRegistrationClusters 获取分布式缓存注册集群
	ListDistributedCacheRegistrationClusters(context.Context, *ListDistributedCacheRegistrationClustersRequest) (*ListDistributedCacheRegistrationClustersResponse, error)
	// ListDistributedCacheRegistrationRegions 获取存储框架地区
	ListDistributedCacheRegistrationRegions(context.Context, *ListDistributedCacheRegistrationRegionsRequest) (*ListDistributedCacheRegistrationRegionsResponse, error)
	// ListDistributedCacheRegistrationZones 获取分布式缓存注册可用区
	ListDistributedCacheRegistrationZones(context.Context, *ListDistributedCacheRegistrationZonesRequest) (*ListDistributedCacheRegistrationZonesResponse, error)
	// ListDistributedCacheRegistrations 获取分布式缓存注册表列表
	ListDistributedCacheRegistrations(context.Context, *ListDistributedCacheRegistrationsRequest) (*ListDistributedCacheRegistrationsResponse, error)
	// ListDragonfly获取Dragonfly配置
	ListDragonfly(context.Context, *ListDragonflyRequest) (*ListDragonflyResponse, error)
	ListKubernetesClusters(context.Context, *ListKubernetesClustersOptions) (*ListKubernetesClustersResponse, error)
	// ListNodeSpecifications 查询所有的节点规格
	ListNodeSpecifications(context.Context, *ListNodeSpecificationOptions) (*ListNodeSpecificationsResult, error)
	// ListNodes 展示所有节点
	ListNodes(context.Context, *ListNodesOptions) (*ListNodesResult, error)
	RecycleNode(context.Context, *RecycleNodeRequest) (*emptypb.Empty, error)
	RemoveKubernetesCluster(context.Context, *RemoveKubernetesClusterRequest) (*KubernetesCluster, error)
	SetCubeFSDashboard(context.Context, *SetCubeFSDashboardRequest) (*emptypb.Empty, error)
	// SetDragonfly更新Dragonfly配置
	SetDragonfly(context.Context, *SetDragonflyRequest) (*emptypb.Empty, error)
	// UpdateDistributedCacheRegistration 更新分布式缓存注册表
	UpdateDistributedCacheRegistration(context.Context, *UpdateDistributedCacheRegistrationRequest) (*UpdateDistributedCacheRegistrationResponse, error)
}

func RegisterPlatformServiceHTTPServer(s *http.Server, srv PlatformServiceHTTPServer) {
	r := s.Route("/")
	r.POST("/apis/v1/k8s-cluster/{name}", _PlatformService_AddOrUpdateKubernetesCluster0_HTTP_Handler(srv))
	r.PUT("/apis/v1/platform/settings/k8s-cluster", _PlatformService_AddOrUpdateKubernetesCluster1_HTTP_Handler(srv))
	r.DELETE("/apis/v1/platform/settings/k8s-cluster/{name}", _PlatformService_RemoveKubernetesCluster0_HTTP_Handler(srv))
	r.GET("/apis/v1/platform/settings/k8s-cluster", _PlatformService_ListKubernetesClusters0_HTTP_Handler(srv))
	r.GET("/apis/v1/platform/settings/k8s-cluster/{name}", _PlatformService_GetKubernetesCluster0_HTTP_Handler(srv))
	r.PUT("/apis/v1/platform/settings/node-specifications/{name}", _PlatformService_AddOrUpdateNodeSpecification0_HTTP_Handler(srv))
	r.PUT("/apis/v1/platform/settings/node-specifications", _PlatformService_AddOrUpdateNodeSpecification1_HTTP_Handler(srv))
	r.DELETE("/apis/v1/platform/settings/node-specifications/{name}", _PlatformService_DeleteNodeSpecification0_HTTP_Handler(srv))
	r.GET("/apis/v1/platform/settings/node-specifications", _PlatformService_ListNodeSpecifications0_HTTP_Handler(srv))
	r.POST("/apis/v1/platform/settings/cubefs-cluster/{name}", _PlatformService_AddOrUpdateCubeFSCluster0_HTTP_Handler(srv))
	r.PUT("/apis/v1/platform/settings/cubefs-cluster", _PlatformService_AddOrUpdateCubeFSCluster1_HTTP_Handler(srv))
	r.DELETE("/apis/v1/platform/settings/cubefs-cluster/{name}", _PlatformService_DeleteCubeFSCluster0_HTTP_Handler(srv))
	r.POST("/apis/v1/platform/settings/cubefs-cluster/{name}/status/{status}", _PlatformService_CubeFSClusterUpdateStatus0_HTTP_Handler(srv))
	r.POST("/apis/v1/platform/settings/cubefs-dashboard", _PlatformService_SetCubeFSDashboard0_HTTP_Handler(srv))
	r.PUT("/apis/v1/platform/settings/cubefs-dashboard", _PlatformService_SetCubeFSDashboard1_HTTP_Handler(srv))
	r.GET("/apis/v1/platform/settings/nodes", _PlatformService_ListNodes1_HTTP_Handler(srv))
	r.GET("/apis/v1/platform/setting/nodes/statistics", _PlatformService_GetNodesInfoStatistics0_HTTP_Handler(srv))
	r.POST("/apis/v1/platform/settings/nodes/assign", _PlatformService_AssignNode0_HTTP_Handler(srv))
	r.POST("/apis/v1/platform/settings/nodes/recycle", _PlatformService_RecycleNode0_HTTP_Handler(srv))
	r.POST("/apis/v1/platform/settings/dragonfly", _PlatformService_AddDragonfly0_HTTP_Handler(srv))
	r.PUT("/apis/v1/platform/settings/dragonfly", _PlatformService_SetDragonfly0_HTTP_Handler(srv))
	r.DELETE("/apis/v1/platform/settings/dragonfly", _PlatformService_DeleteDragonfly0_HTTP_Handler(srv))
	r.GET("/apis/v1/platform/settings/dragonfly", _PlatformService_ListDragonfly0_HTTP_Handler(srv))
	r.GET("/apis/v1/platform/settings/distributedcacheregistrations", _PlatformService_ListDistributedCacheRegistrations0_HTTP_Handler(srv))
	r.POST("/apis/v1/platform/settings/distributedcacheregistration", _PlatformService_CreateDistributedCacheRegistration0_HTTP_Handler(srv))
	r.DELETE("/apis/v1/platform/settings/distributedcacheregistration/{id}", _PlatformService_DeleteDistributedCacheRegistration0_HTTP_Handler(srv))
	r.PUT("/apis/v1/platform/settings/distributedcacheregistration/{id}", _PlatformService_UpdateDistributedCacheRegistration0_HTTP_Handler(srv))
	r.GET("/apis/v1/platform/settings/distributedcacheregistration/regions", _PlatformService_ListDistributedCacheRegistrationRegions0_HTTP_Handler(srv))
	r.GET("/apis/v1/platform/settings/distributedcacheregistration/zones", _PlatformService_ListDistributedCacheRegistrationZones0_HTTP_Handler(srv))
	r.GET("/apis/v1/platform/settings/distributedcacheregistration/clusters", _PlatformService_ListDistributedCacheRegistrationClusters0_HTTP_Handler(srv))
}

func _PlatformService_AddOrUpdateKubernetesCluster0_HTTP_Handler(srv PlatformServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateKubernetesClusterRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationPlatformServiceAddOrUpdateKubernetesCluster)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.AddOrUpdateKubernetesCluster(ctx, req.(*CreateKubernetesClusterRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*KubernetesCluster)
		return ctx.Result(200, reply)
	}
}

func _PlatformService_AddOrUpdateKubernetesCluster1_HTTP_Handler(srv PlatformServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateKubernetesClusterRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationPlatformServiceAddOrUpdateKubernetesCluster)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.AddOrUpdateKubernetesCluster(ctx, req.(*CreateKubernetesClusterRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*KubernetesCluster)
		return ctx.Result(200, reply)
	}
}

func _PlatformService_RemoveKubernetesCluster0_HTTP_Handler(srv PlatformServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in RemoveKubernetesClusterRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationPlatformServiceRemoveKubernetesCluster)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.RemoveKubernetesCluster(ctx, req.(*RemoveKubernetesClusterRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*KubernetesCluster)
		return ctx.Result(200, reply)
	}
}

func _PlatformService_ListKubernetesClusters0_HTTP_Handler(srv PlatformServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListKubernetesClustersOptions
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationPlatformServiceListKubernetesClusters)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListKubernetesClusters(ctx, req.(*ListKubernetesClustersOptions))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListKubernetesClustersResponse)
		return ctx.Result(200, reply)
	}
}

func _PlatformService_GetKubernetesCluster0_HTTP_Handler(srv PlatformServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetKubernetesClusterRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationPlatformServiceGetKubernetesCluster)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetKubernetesCluster(ctx, req.(*GetKubernetesClusterRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*KubernetesCluster)
		return ctx.Result(200, reply)
	}
}

func _PlatformService_AddOrUpdateNodeSpecification0_HTTP_Handler(srv PlatformServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in NodeSpecification
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationPlatformServiceAddOrUpdateNodeSpecification)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.AddOrUpdateNodeSpecification(ctx, req.(*NodeSpecification))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*NodeSpecification)
		return ctx.Result(200, reply)
	}
}

func _PlatformService_AddOrUpdateNodeSpecification1_HTTP_Handler(srv PlatformServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in NodeSpecification
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationPlatformServiceAddOrUpdateNodeSpecification)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.AddOrUpdateNodeSpecification(ctx, req.(*NodeSpecification))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*NodeSpecification)
		return ctx.Result(200, reply)
	}
}

func _PlatformService_DeleteNodeSpecification0_HTTP_Handler(srv PlatformServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteNodeSpecificationRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationPlatformServiceDeleteNodeSpecification)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteNodeSpecification(ctx, req.(*DeleteNodeSpecificationRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _PlatformService_ListNodeSpecifications0_HTTP_Handler(srv PlatformServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListNodeSpecificationOptions
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationPlatformServiceListNodeSpecifications)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListNodeSpecifications(ctx, req.(*ListNodeSpecificationOptions))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListNodeSpecificationsResult)
		return ctx.Result(200, reply)
	}
}

func _PlatformService_AddOrUpdateCubeFSCluster0_HTTP_Handler(srv PlatformServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in AddOrUpdateCubeFSClusterRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationPlatformServiceAddOrUpdateCubeFSCluster)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.AddOrUpdateCubeFSCluster(ctx, req.(*AddOrUpdateCubeFSClusterRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _PlatformService_AddOrUpdateCubeFSCluster1_HTTP_Handler(srv PlatformServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in AddOrUpdateCubeFSClusterRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationPlatformServiceAddOrUpdateCubeFSCluster)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.AddOrUpdateCubeFSCluster(ctx, req.(*AddOrUpdateCubeFSClusterRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _PlatformService_DeleteCubeFSCluster0_HTTP_Handler(srv PlatformServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteCubeFSClusterRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationPlatformServiceDeleteCubeFSCluster)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteCubeFSCluster(ctx, req.(*DeleteCubeFSClusterRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _PlatformService_CubeFSClusterUpdateStatus0_HTTP_Handler(srv PlatformServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CubeFSClusterSetStatusRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationPlatformServiceCubeFSClusterUpdateStatus)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CubeFSClusterUpdateStatus(ctx, req.(*CubeFSClusterSetStatusRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _PlatformService_SetCubeFSDashboard0_HTTP_Handler(srv PlatformServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in SetCubeFSDashboardRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationPlatformServiceSetCubeFSDashboard)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SetCubeFSDashboard(ctx, req.(*SetCubeFSDashboardRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _PlatformService_SetCubeFSDashboard1_HTTP_Handler(srv PlatformServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in SetCubeFSDashboardRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationPlatformServiceSetCubeFSDashboard)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SetCubeFSDashboard(ctx, req.(*SetCubeFSDashboardRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _PlatformService_ListNodes1_HTTP_Handler(srv PlatformServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListNodesOptions
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationPlatformServiceListNodes)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListNodes(ctx, req.(*ListNodesOptions))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListNodesResult)
		return ctx.Result(200, reply)
	}
}

func _PlatformService_GetNodesInfoStatistics0_HTTP_Handler(srv PlatformServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetNodesInfoStatisticsRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationPlatformServiceGetNodesInfoStatistics)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetNodesInfoStatistics(ctx, req.(*GetNodesInfoStatisticsRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*NodesInfoStatisticsResponse)
		return ctx.Result(200, reply)
	}
}

func _PlatformService_AssignNode0_HTTP_Handler(srv PlatformServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in AssignNodeRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationPlatformServiceAssignNode)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.AssignNode(ctx, req.(*AssignNodeRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _PlatformService_RecycleNode0_HTTP_Handler(srv PlatformServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in RecycleNodeRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationPlatformServiceRecycleNode)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.RecycleNode(ctx, req.(*RecycleNodeRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _PlatformService_AddDragonfly0_HTTP_Handler(srv PlatformServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in AddDragonflyRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationPlatformServiceAddDragonfly)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.AddDragonfly(ctx, req.(*AddDragonflyRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _PlatformService_SetDragonfly0_HTTP_Handler(srv PlatformServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in SetDragonflyRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationPlatformServiceSetDragonfly)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SetDragonfly(ctx, req.(*SetDragonflyRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _PlatformService_DeleteDragonfly0_HTTP_Handler(srv PlatformServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteDragonflyRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationPlatformServiceDeleteDragonfly)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteDragonfly(ctx, req.(*DeleteDragonflyRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _PlatformService_ListDragonfly0_HTTP_Handler(srv PlatformServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListDragonflyRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationPlatformServiceListDragonfly)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListDragonfly(ctx, req.(*ListDragonflyRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListDragonflyResponse)
		return ctx.Result(200, reply)
	}
}

func _PlatformService_ListDistributedCacheRegistrations0_HTTP_Handler(srv PlatformServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListDistributedCacheRegistrationsRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationPlatformServiceListDistributedCacheRegistrations)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListDistributedCacheRegistrations(ctx, req.(*ListDistributedCacheRegistrationsRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListDistributedCacheRegistrationsResponse)
		return ctx.Result(200, reply)
	}
}

func _PlatformService_CreateDistributedCacheRegistration0_HTTP_Handler(srv PlatformServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateDistributedCacheRegistrationRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationPlatformServiceCreateDistributedCacheRegistration)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateDistributedCacheRegistration(ctx, req.(*CreateDistributedCacheRegistrationRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CreateDistributedCacheRegistrationResponse)
		return ctx.Result(200, reply)
	}
}

func _PlatformService_DeleteDistributedCacheRegistration0_HTTP_Handler(srv PlatformServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteDistributedCacheRegistrationRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationPlatformServiceDeleteDistributedCacheRegistration)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteDistributedCacheRegistration(ctx, req.(*DeleteDistributedCacheRegistrationRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DeleteDistributedCacheRegistrationResponse)
		return ctx.Result(200, reply)
	}
}

func _PlatformService_UpdateDistributedCacheRegistration0_HTTP_Handler(srv PlatformServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UpdateDistributedCacheRegistrationRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationPlatformServiceUpdateDistributedCacheRegistration)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateDistributedCacheRegistration(ctx, req.(*UpdateDistributedCacheRegistrationRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*UpdateDistributedCacheRegistrationResponse)
		return ctx.Result(200, reply)
	}
}

func _PlatformService_ListDistributedCacheRegistrationRegions0_HTTP_Handler(srv PlatformServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListDistributedCacheRegistrationRegionsRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationPlatformServiceListDistributedCacheRegistrationRegions)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListDistributedCacheRegistrationRegions(ctx, req.(*ListDistributedCacheRegistrationRegionsRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListDistributedCacheRegistrationRegionsResponse)
		return ctx.Result(200, reply)
	}
}

func _PlatformService_ListDistributedCacheRegistrationZones0_HTTP_Handler(srv PlatformServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListDistributedCacheRegistrationZonesRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationPlatformServiceListDistributedCacheRegistrationZones)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListDistributedCacheRegistrationZones(ctx, req.(*ListDistributedCacheRegistrationZonesRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListDistributedCacheRegistrationZonesResponse)
		return ctx.Result(200, reply)
	}
}

func _PlatformService_ListDistributedCacheRegistrationClusters0_HTTP_Handler(srv PlatformServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListDistributedCacheRegistrationClustersRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationPlatformServiceListDistributedCacheRegistrationClusters)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListDistributedCacheRegistrationClusters(ctx, req.(*ListDistributedCacheRegistrationClustersRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListDistributedCacheRegistrationClustersResponse)
		return ctx.Result(200, reply)
	}
}

type PlatformServiceHTTPClient interface {
	AddDragonfly(ctx context.Context, req *AddDragonflyRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	AddOrUpdateCubeFSCluster(ctx context.Context, req *AddOrUpdateCubeFSClusterRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	AddOrUpdateKubernetesCluster(ctx context.Context, req *CreateKubernetesClusterRequest, opts ...http.CallOption) (rsp *KubernetesCluster, err error)
	AddOrUpdateNodeSpecification(ctx context.Context, req *NodeSpecification, opts ...http.CallOption) (rsp *NodeSpecification, err error)
	AssignNode(ctx context.Context, req *AssignNodeRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	CreateDistributedCacheRegistration(ctx context.Context, req *CreateDistributedCacheRegistrationRequest, opts ...http.CallOption) (rsp *CreateDistributedCacheRegistrationResponse, err error)
	CubeFSClusterUpdateStatus(ctx context.Context, req *CubeFSClusterSetStatusRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	DeleteCubeFSCluster(ctx context.Context, req *DeleteCubeFSClusterRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	DeleteDistributedCacheRegistration(ctx context.Context, req *DeleteDistributedCacheRegistrationRequest, opts ...http.CallOption) (rsp *DeleteDistributedCacheRegistrationResponse, err error)
	DeleteDragonfly(ctx context.Context, req *DeleteDragonflyRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	DeleteNodeSpecification(ctx context.Context, req *DeleteNodeSpecificationRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	GetKubernetesCluster(ctx context.Context, req *GetKubernetesClusterRequest, opts ...http.CallOption) (rsp *KubernetesCluster, err error)
	GetNodesInfoStatistics(ctx context.Context, req *GetNodesInfoStatisticsRequest, opts ...http.CallOption) (rsp *NodesInfoStatisticsResponse, err error)
	ListDistributedCacheRegistrationClusters(ctx context.Context, req *ListDistributedCacheRegistrationClustersRequest, opts ...http.CallOption) (rsp *ListDistributedCacheRegistrationClustersResponse, err error)
	ListDistributedCacheRegistrationRegions(ctx context.Context, req *ListDistributedCacheRegistrationRegionsRequest, opts ...http.CallOption) (rsp *ListDistributedCacheRegistrationRegionsResponse, err error)
	ListDistributedCacheRegistrationZones(ctx context.Context, req *ListDistributedCacheRegistrationZonesRequest, opts ...http.CallOption) (rsp *ListDistributedCacheRegistrationZonesResponse, err error)
	ListDistributedCacheRegistrations(ctx context.Context, req *ListDistributedCacheRegistrationsRequest, opts ...http.CallOption) (rsp *ListDistributedCacheRegistrationsResponse, err error)
	ListDragonfly(ctx context.Context, req *ListDragonflyRequest, opts ...http.CallOption) (rsp *ListDragonflyResponse, err error)
	ListKubernetesClusters(ctx context.Context, req *ListKubernetesClustersOptions, opts ...http.CallOption) (rsp *ListKubernetesClustersResponse, err error)
	ListNodeSpecifications(ctx context.Context, req *ListNodeSpecificationOptions, opts ...http.CallOption) (rsp *ListNodeSpecificationsResult, err error)
	ListNodes(ctx context.Context, req *ListNodesOptions, opts ...http.CallOption) (rsp *ListNodesResult, err error)
	RecycleNode(ctx context.Context, req *RecycleNodeRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	RemoveKubernetesCluster(ctx context.Context, req *RemoveKubernetesClusterRequest, opts ...http.CallOption) (rsp *KubernetesCluster, err error)
	SetCubeFSDashboard(ctx context.Context, req *SetCubeFSDashboardRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	SetDragonfly(ctx context.Context, req *SetDragonflyRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	UpdateDistributedCacheRegistration(ctx context.Context, req *UpdateDistributedCacheRegistrationRequest, opts ...http.CallOption) (rsp *UpdateDistributedCacheRegistrationResponse, err error)
}

type PlatformServiceHTTPClientImpl struct {
	cc *http.Client
}

func NewPlatformServiceHTTPClient(client *http.Client) PlatformServiceHTTPClient {
	return &PlatformServiceHTTPClientImpl{client}
}

func (c *PlatformServiceHTTPClientImpl) AddDragonfly(ctx context.Context, in *AddDragonflyRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/apis/v1/platform/settings/dragonfly"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationPlatformServiceAddDragonfly))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *PlatformServiceHTTPClientImpl) AddOrUpdateCubeFSCluster(ctx context.Context, in *AddOrUpdateCubeFSClusterRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/apis/v1/platform/settings/cubefs-cluster"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationPlatformServiceAddOrUpdateCubeFSCluster))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *PlatformServiceHTTPClientImpl) AddOrUpdateKubernetesCluster(ctx context.Context, in *CreateKubernetesClusterRequest, opts ...http.CallOption) (*KubernetesCluster, error) {
	var out KubernetesCluster
	pattern := "/apis/v1/platform/settings/k8s-cluster"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationPlatformServiceAddOrUpdateKubernetesCluster))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *PlatformServiceHTTPClientImpl) AddOrUpdateNodeSpecification(ctx context.Context, in *NodeSpecification, opts ...http.CallOption) (*NodeSpecification, error) {
	var out NodeSpecification
	pattern := "/apis/v1/platform/settings/node-specifications"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationPlatformServiceAddOrUpdateNodeSpecification))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *PlatformServiceHTTPClientImpl) AssignNode(ctx context.Context, in *AssignNodeRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/apis/v1/platform/settings/nodes/assign"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationPlatformServiceAssignNode))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *PlatformServiceHTTPClientImpl) CreateDistributedCacheRegistration(ctx context.Context, in *CreateDistributedCacheRegistrationRequest, opts ...http.CallOption) (*CreateDistributedCacheRegistrationResponse, error) {
	var out CreateDistributedCacheRegistrationResponse
	pattern := "/apis/v1/platform/settings/distributedcacheregistration"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationPlatformServiceCreateDistributedCacheRegistration))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *PlatformServiceHTTPClientImpl) CubeFSClusterUpdateStatus(ctx context.Context, in *CubeFSClusterSetStatusRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/apis/v1/platform/settings/cubefs-cluster/{name}/status/{status}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationPlatformServiceCubeFSClusterUpdateStatus))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *PlatformServiceHTTPClientImpl) DeleteCubeFSCluster(ctx context.Context, in *DeleteCubeFSClusterRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/apis/v1/platform/settings/cubefs-cluster/{name}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationPlatformServiceDeleteCubeFSCluster))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *PlatformServiceHTTPClientImpl) DeleteDistributedCacheRegistration(ctx context.Context, in *DeleteDistributedCacheRegistrationRequest, opts ...http.CallOption) (*DeleteDistributedCacheRegistrationResponse, error) {
	var out DeleteDistributedCacheRegistrationResponse
	pattern := "/apis/v1/platform/settings/distributedcacheregistration/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationPlatformServiceDeleteDistributedCacheRegistration))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *PlatformServiceHTTPClientImpl) DeleteDragonfly(ctx context.Context, in *DeleteDragonflyRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/apis/v1/platform/settings/dragonfly"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationPlatformServiceDeleteDragonfly))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *PlatformServiceHTTPClientImpl) DeleteNodeSpecification(ctx context.Context, in *DeleteNodeSpecificationRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/apis/v1/platform/settings/node-specifications/{name}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationPlatformServiceDeleteNodeSpecification))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *PlatformServiceHTTPClientImpl) GetKubernetesCluster(ctx context.Context, in *GetKubernetesClusterRequest, opts ...http.CallOption) (*KubernetesCluster, error) {
	var out KubernetesCluster
	pattern := "/apis/v1/platform/settings/k8s-cluster/{name}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationPlatformServiceGetKubernetesCluster))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *PlatformServiceHTTPClientImpl) GetNodesInfoStatistics(ctx context.Context, in *GetNodesInfoStatisticsRequest, opts ...http.CallOption) (*NodesInfoStatisticsResponse, error) {
	var out NodesInfoStatisticsResponse
	pattern := "/apis/v1/platform/setting/nodes/statistics"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationPlatformServiceGetNodesInfoStatistics))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *PlatformServiceHTTPClientImpl) ListDistributedCacheRegistrationClusters(ctx context.Context, in *ListDistributedCacheRegistrationClustersRequest, opts ...http.CallOption) (*ListDistributedCacheRegistrationClustersResponse, error) {
	var out ListDistributedCacheRegistrationClustersResponse
	pattern := "/apis/v1/platform/settings/distributedcacheregistration/clusters"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationPlatformServiceListDistributedCacheRegistrationClusters))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *PlatformServiceHTTPClientImpl) ListDistributedCacheRegistrationRegions(ctx context.Context, in *ListDistributedCacheRegistrationRegionsRequest, opts ...http.CallOption) (*ListDistributedCacheRegistrationRegionsResponse, error) {
	var out ListDistributedCacheRegistrationRegionsResponse
	pattern := "/apis/v1/platform/settings/distributedcacheregistration/regions"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationPlatformServiceListDistributedCacheRegistrationRegions))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *PlatformServiceHTTPClientImpl) ListDistributedCacheRegistrationZones(ctx context.Context, in *ListDistributedCacheRegistrationZonesRequest, opts ...http.CallOption) (*ListDistributedCacheRegistrationZonesResponse, error) {
	var out ListDistributedCacheRegistrationZonesResponse
	pattern := "/apis/v1/platform/settings/distributedcacheregistration/zones"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationPlatformServiceListDistributedCacheRegistrationZones))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *PlatformServiceHTTPClientImpl) ListDistributedCacheRegistrations(ctx context.Context, in *ListDistributedCacheRegistrationsRequest, opts ...http.CallOption) (*ListDistributedCacheRegistrationsResponse, error) {
	var out ListDistributedCacheRegistrationsResponse
	pattern := "/apis/v1/platform/settings/distributedcacheregistrations"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationPlatformServiceListDistributedCacheRegistrations))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *PlatformServiceHTTPClientImpl) ListDragonfly(ctx context.Context, in *ListDragonflyRequest, opts ...http.CallOption) (*ListDragonflyResponse, error) {
	var out ListDragonflyResponse
	pattern := "/apis/v1/platform/settings/dragonfly"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationPlatformServiceListDragonfly))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *PlatformServiceHTTPClientImpl) ListKubernetesClusters(ctx context.Context, in *ListKubernetesClustersOptions, opts ...http.CallOption) (*ListKubernetesClustersResponse, error) {
	var out ListKubernetesClustersResponse
	pattern := "/apis/v1/platform/settings/k8s-cluster"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationPlatformServiceListKubernetesClusters))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *PlatformServiceHTTPClientImpl) ListNodeSpecifications(ctx context.Context, in *ListNodeSpecificationOptions, opts ...http.CallOption) (*ListNodeSpecificationsResult, error) {
	var out ListNodeSpecificationsResult
	pattern := "/apis/v1/platform/settings/node-specifications"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationPlatformServiceListNodeSpecifications))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *PlatformServiceHTTPClientImpl) ListNodes(ctx context.Context, in *ListNodesOptions, opts ...http.CallOption) (*ListNodesResult, error) {
	var out ListNodesResult
	pattern := "/apis/v1/platform/settings/nodes"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationPlatformServiceListNodes))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *PlatformServiceHTTPClientImpl) RecycleNode(ctx context.Context, in *RecycleNodeRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/apis/v1/platform/settings/nodes/recycle"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationPlatformServiceRecycleNode))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *PlatformServiceHTTPClientImpl) RemoveKubernetesCluster(ctx context.Context, in *RemoveKubernetesClusterRequest, opts ...http.CallOption) (*KubernetesCluster, error) {
	var out KubernetesCluster
	pattern := "/apis/v1/platform/settings/k8s-cluster/{name}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationPlatformServiceRemoveKubernetesCluster))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *PlatformServiceHTTPClientImpl) SetCubeFSDashboard(ctx context.Context, in *SetCubeFSDashboardRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/apis/v1/platform/settings/cubefs-dashboard"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationPlatformServiceSetCubeFSDashboard))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *PlatformServiceHTTPClientImpl) SetDragonfly(ctx context.Context, in *SetDragonflyRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/apis/v1/platform/settings/dragonfly"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationPlatformServiceSetDragonfly))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *PlatformServiceHTTPClientImpl) UpdateDistributedCacheRegistration(ctx context.Context, in *UpdateDistributedCacheRegistrationRequest, opts ...http.CallOption) (*UpdateDistributedCacheRegistrationResponse, error) {
	var out UpdateDistributedCacheRegistrationResponse
	pattern := "/apis/v1/platform/settings/distributedcacheregistration/{id}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationPlatformServiceUpdateDistributedCacheRegistration))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
