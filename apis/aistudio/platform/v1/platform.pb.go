// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v5.29.3
// source: aistudio/platform/v1/platform.proto

package v1

import (
	common "git.lianjia.com/cloudnative/kic/kic-platform/apis/common"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ClusterUsage int32

const (
	ClusterUsage_Training  ClusterUsage = 0
	ClusterUsage_Inference ClusterUsage = 1
)

// Enum value maps for ClusterUsage.
var (
	ClusterUsage_name = map[int32]string{
		0: "Training",
		1: "Inference",
	}
	ClusterUsage_value = map[string]int32{
		"Training":  0,
		"Inference": 1,
	}
)

func (x ClusterUsage) Enum() *ClusterUsage {
	p := new(ClusterUsage)
	*p = x
	return p
}

func (x ClusterUsage) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ClusterUsage) Descriptor() protoreflect.EnumDescriptor {
	return file_aistudio_platform_v1_platform_proto_enumTypes[0].Descriptor()
}

func (ClusterUsage) Type() protoreflect.EnumType {
	return &file_aistudio_platform_v1_platform_proto_enumTypes[0]
}

func (x ClusterUsage) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ClusterUsage.Descriptor instead.
func (ClusterUsage) EnumDescriptor() ([]byte, []int) {
	return file_aistudio_platform_v1_platform_proto_rawDescGZIP(), []int{0}
}

type AddOrUpdateCubeFSClusterRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name          string   `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Region        string   `protobuf:"bytes,2,opt,name=region,proto3" json:"region,omitempty"`
	Zones         []string `protobuf:"bytes,3,rep,name=zones,proto3" json:"zones,omitempty"` // 可用区
	Description   string   `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	DisplayName   string   `protobuf:"bytes,5,opt,name=displayName,proto3" json:"displayName,omitempty"`
	MasterAddress string   `protobuf:"bytes,6,opt,name=masterAddress,proto3" json:"masterAddress,omitempty"`
	Enabled       bool     `protobuf:"varint,7,opt,name=enabled,proto3" json:"enabled,omitempty"`
	Specification string   `protobuf:"bytes,8,opt,name=specification,proto3" json:"specification,omitempty"`
}

func (x *AddOrUpdateCubeFSClusterRequest) Reset() {
	*x = AddOrUpdateCubeFSClusterRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_platform_v1_platform_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddOrUpdateCubeFSClusterRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddOrUpdateCubeFSClusterRequest) ProtoMessage() {}

func (x *AddOrUpdateCubeFSClusterRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_platform_v1_platform_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddOrUpdateCubeFSClusterRequest.ProtoReflect.Descriptor instead.
func (*AddOrUpdateCubeFSClusterRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_platform_v1_platform_proto_rawDescGZIP(), []int{0}
}

func (x *AddOrUpdateCubeFSClusterRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *AddOrUpdateCubeFSClusterRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *AddOrUpdateCubeFSClusterRequest) GetZones() []string {
	if x != nil {
		return x.Zones
	}
	return nil
}

func (x *AddOrUpdateCubeFSClusterRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *AddOrUpdateCubeFSClusterRequest) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *AddOrUpdateCubeFSClusterRequest) GetMasterAddress() string {
	if x != nil {
		return x.MasterAddress
	}
	return ""
}

func (x *AddOrUpdateCubeFSClusterRequest) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

func (x *AddOrUpdateCubeFSClusterRequest) GetSpecification() string {
	if x != nil {
		return x.Specification
	}
	return ""
}

type DeleteCubeFSClusterRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *DeleteCubeFSClusterRequest) Reset() {
	*x = DeleteCubeFSClusterRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_platform_v1_platform_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteCubeFSClusterRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteCubeFSClusterRequest) ProtoMessage() {}

func (x *DeleteCubeFSClusterRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_platform_v1_platform_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteCubeFSClusterRequest.ProtoReflect.Descriptor instead.
func (*DeleteCubeFSClusterRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_platform_v1_platform_proto_rawDescGZIP(), []int{1}
}

func (x *DeleteCubeFSClusterRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type CubeFSClusterSetStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name   string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Status string `protobuf:"bytes,2,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *CubeFSClusterSetStatusRequest) Reset() {
	*x = CubeFSClusterSetStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_platform_v1_platform_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CubeFSClusterSetStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CubeFSClusterSetStatusRequest) ProtoMessage() {}

func (x *CubeFSClusterSetStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_platform_v1_platform_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CubeFSClusterSetStatusRequest.ProtoReflect.Descriptor instead.
func (*CubeFSClusterSetStatusRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_platform_v1_platform_proto_rawDescGZIP(), []int{2}
}

func (x *CubeFSClusterSetStatusRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CubeFSClusterSetStatusRequest) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

type SetCubeFSDashboardRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url      string `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	User     string `protobuf:"bytes,2,opt,name=user,proto3" json:"user,omitempty"`
	Password string `protobuf:"bytes,3,opt,name=password,proto3" json:"password,omitempty"`
}

func (x *SetCubeFSDashboardRequest) Reset() {
	*x = SetCubeFSDashboardRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_platform_v1_platform_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetCubeFSDashboardRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetCubeFSDashboardRequest) ProtoMessage() {}

func (x *SetCubeFSDashboardRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_platform_v1_platform_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetCubeFSDashboardRequest.ProtoReflect.Descriptor instead.
func (*SetCubeFSDashboardRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_platform_v1_platform_proto_rawDescGZIP(), []int{3}
}

func (x *SetCubeFSDashboardRequest) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *SetCubeFSDashboardRequest) GetUser() string {
	if x != nil {
		return x.User
	}
	return ""
}

func (x *SetCubeFSDashboardRequest) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

type ListNodeSpecificationOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GpuEnabled bool   `protobuf:"varint,1,opt,name=gpuEnabled,proto3" json:"gpuEnabled,omitempty"`
	GpuProduct string `protobuf:"bytes,2,opt,name=gpuProduct,proto3" json:"gpuProduct,omitempty"`
}

func (x *ListNodeSpecificationOptions) Reset() {
	*x = ListNodeSpecificationOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_platform_v1_platform_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListNodeSpecificationOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListNodeSpecificationOptions) ProtoMessage() {}

func (x *ListNodeSpecificationOptions) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_platform_v1_platform_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListNodeSpecificationOptions.ProtoReflect.Descriptor instead.
func (*ListNodeSpecificationOptions) Descriptor() ([]byte, []int) {
	return file_aistudio_platform_v1_platform_proto_rawDescGZIP(), []int{4}
}

func (x *ListNodeSpecificationOptions) GetGpuEnabled() bool {
	if x != nil {
		return x.GpuEnabled
	}
	return false
}

func (x *ListNodeSpecificationOptions) GetGpuProduct() string {
	if x != nil {
		return x.GpuProduct
	}
	return ""
}

type ListNodeSpecificationsResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NodeSpecifications []*NodeSpecification `protobuf:"bytes,1,rep,name=nodeSpecifications,proto3" json:"nodeSpecifications,omitempty"`
}

func (x *ListNodeSpecificationsResult) Reset() {
	*x = ListNodeSpecificationsResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_platform_v1_platform_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListNodeSpecificationsResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListNodeSpecificationsResult) ProtoMessage() {}

func (x *ListNodeSpecificationsResult) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_platform_v1_platform_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListNodeSpecificationsResult.ProtoReflect.Descriptor instead.
func (*ListNodeSpecificationsResult) Descriptor() ([]byte, []int) {
	return file_aistudio_platform_v1_platform_proto_rawDescGZIP(), []int{5}
}

func (x *ListNodeSpecificationsResult) GetNodeSpecifications() []*NodeSpecification {
	if x != nil {
		return x.NodeSpecifications
	}
	return nil
}

type DeleteNodeSpecificationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *DeleteNodeSpecificationRequest) Reset() {
	*x = DeleteNodeSpecificationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_platform_v1_platform_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteNodeSpecificationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteNodeSpecificationRequest) ProtoMessage() {}

func (x *DeleteNodeSpecificationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_platform_v1_platform_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteNodeSpecificationRequest.ProtoReflect.Descriptor instead.
func (*DeleteNodeSpecificationRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_platform_v1_platform_proto_rawDescGZIP(), []int{6}
}

func (x *DeleteNodeSpecificationRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// 注册Kubernetes的集群信息
type KubernetesCluster struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name         string            `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Region       string            `protobuf:"bytes,2,opt,name=region,proto3" json:"region,omitempty"`
	Zone         string            `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	Idc          string            `protobuf:"bytes,4,opt,name=idc,proto3" json:"idc,omitempty"`
	DisplayName  string            `protobuf:"bytes,5,opt,name=displayName,proto3" json:"displayName,omitempty"`
	Description  string            `protobuf:"bytes,6,opt,name=description,proto3" json:"description,omitempty"`                                                                                           //集群的描述信息
	Usages       []ClusterUsage    `protobuf:"varint,7,rep,packed,name=usages,proto3,enum=apis.aistudio.platform.v1.ClusterUsage" json:"usages,omitempty"`                                                 //集群用途
	NodeSelector map[string]string `protobuf:"bytes,8,rep,name=nodeSelector,proto3" json:"nodeSelector,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` //集群的nodeSelector, 不设置表示全量节点
}

func (x *KubernetesCluster) Reset() {
	*x = KubernetesCluster{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_platform_v1_platform_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *KubernetesCluster) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KubernetesCluster) ProtoMessage() {}

func (x *KubernetesCluster) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_platform_v1_platform_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KubernetesCluster.ProtoReflect.Descriptor instead.
func (*KubernetesCluster) Descriptor() ([]byte, []int) {
	return file_aistudio_platform_v1_platform_proto_rawDescGZIP(), []int{7}
}

func (x *KubernetesCluster) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *KubernetesCluster) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *KubernetesCluster) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *KubernetesCluster) GetIdc() string {
	if x != nil {
		return x.Idc
	}
	return ""
}

func (x *KubernetesCluster) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *KubernetesCluster) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *KubernetesCluster) GetUsages() []ClusterUsage {
	if x != nil {
		return x.Usages
	}
	return nil
}

func (x *KubernetesCluster) GetNodeSelector() map[string]string {
	if x != nil {
		return x.NodeSelector
	}
	return nil
}

// NodeSpecification is a specification for a node with gpu or cpu
type NodeSpecification struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name         string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	DisplayName  string                 `protobuf:"bytes,2,opt,name=displayName,proto3" json:"displayName,omitempty"`
	Description  string                 `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	CpuNum       int32                  `protobuf:"varint,4,opt,name=cpuNum,proto3" json:"cpuNum,omitempty"`
	Memory       int32                  `protobuf:"varint,5,opt,name=memory,proto3" json:"memory,omitempty"` // GiB
	GpuNum       int32                  `protobuf:"varint,6,opt,name=gpuNum,proto3" json:"gpuNum,omitempty"`
	GpuMemory    int32                  `protobuf:"varint,7,opt,name=gpuMemory,proto3" json:"gpuMemory,omitempty"`       // GiB
	DiskCapacity int32                  `protobuf:"varint,8,opt,name=diskCapacity,proto3" json:"diskCapacity,omitempty"` // GiB 磁盘容量
	GpuProduct   string                 `protobuf:"bytes,9,opt,name=gpuProduct,proto3" json:"gpuProduct,omitempty"`      // gpu产品名称
	Timestamp    *common.TimestampModel `protobuf:"bytes,10,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
}

func (x *NodeSpecification) Reset() {
	*x = NodeSpecification{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_platform_v1_platform_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NodeSpecification) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NodeSpecification) ProtoMessage() {}

func (x *NodeSpecification) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_platform_v1_platform_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NodeSpecification.ProtoReflect.Descriptor instead.
func (*NodeSpecification) Descriptor() ([]byte, []int) {
	return file_aistudio_platform_v1_platform_proto_rawDescGZIP(), []int{8}
}

func (x *NodeSpecification) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *NodeSpecification) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *NodeSpecification) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *NodeSpecification) GetCpuNum() int32 {
	if x != nil {
		return x.CpuNum
	}
	return 0
}

func (x *NodeSpecification) GetMemory() int32 {
	if x != nil {
		return x.Memory
	}
	return 0
}

func (x *NodeSpecification) GetGpuNum() int32 {
	if x != nil {
		return x.GpuNum
	}
	return 0
}

func (x *NodeSpecification) GetGpuMemory() int32 {
	if x != nil {
		return x.GpuMemory
	}
	return 0
}

func (x *NodeSpecification) GetDiskCapacity() int32 {
	if x != nil {
		return x.DiskCapacity
	}
	return 0
}

func (x *NodeSpecification) GetGpuProduct() string {
	if x != nil {
		return x.GpuProduct
	}
	return ""
}

func (x *NodeSpecification) GetTimestamp() *common.TimestampModel {
	if x != nil {
		return x.Timestamp
	}
	return nil
}

type CubeFSCluster struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name        string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Region      string `protobuf:"bytes,2,opt,name=region,proto3" json:"region,omitempty"`
	Zone        string `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	Description string `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	Idc         string `protobuf:"bytes,5,opt,name=idc,proto3" json:"idc,omitempty"`
	DisplayName string `protobuf:"bytes,6,opt,name=displayName,proto3" json:"displayName,omitempty"`
}

func (x *CubeFSCluster) Reset() {
	*x = CubeFSCluster{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_platform_v1_platform_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CubeFSCluster) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CubeFSCluster) ProtoMessage() {}

func (x *CubeFSCluster) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_platform_v1_platform_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CubeFSCluster.ProtoReflect.Descriptor instead.
func (*CubeFSCluster) Descriptor() ([]byte, []int) {
	return file_aistudio_platform_v1_platform_proto_rawDescGZIP(), []int{9}
}

func (x *CubeFSCluster) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CubeFSCluster) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *CubeFSCluster) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *CubeFSCluster) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CubeFSCluster) GetIdc() string {
	if x != nil {
		return x.Idc
	}
	return ""
}

func (x *CubeFSCluster) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

type CreateKubernetesClusterRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name         string            `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Region       string            `protobuf:"bytes,2,opt,name=region,proto3" json:"region,omitempty"`
	Zone         string            `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	Idc          string            `protobuf:"bytes,4,opt,name=idc,proto3" json:"idc,omitempty"`
	DisplayName  string            `protobuf:"bytes,5,opt,name=displayName,proto3" json:"displayName,omitempty"`
	Description  string            `protobuf:"bytes,6,opt,name=description,proto3" json:"description,omitempty"`                                                                                           //集群的描述信息
	Usages       []ClusterUsage    `protobuf:"varint,7,rep,packed,name=usages,proto3,enum=apis.aistudio.platform.v1.ClusterUsage" json:"usages,omitempty"`                                                 //集群用途
	NodeSelector map[string]string `protobuf:"bytes,8,rep,name=nodeSelector,proto3" json:"nodeSelector,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` //集群的nodeSelector, 不设置表示全量节点
}

func (x *CreateKubernetesClusterRequest) Reset() {
	*x = CreateKubernetesClusterRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_platform_v1_platform_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateKubernetesClusterRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateKubernetesClusterRequest) ProtoMessage() {}

func (x *CreateKubernetesClusterRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_platform_v1_platform_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateKubernetesClusterRequest.ProtoReflect.Descriptor instead.
func (*CreateKubernetesClusterRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_platform_v1_platform_proto_rawDescGZIP(), []int{10}
}

func (x *CreateKubernetesClusterRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateKubernetesClusterRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *CreateKubernetesClusterRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *CreateKubernetesClusterRequest) GetIdc() string {
	if x != nil {
		return x.Idc
	}
	return ""
}

func (x *CreateKubernetesClusterRequest) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *CreateKubernetesClusterRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CreateKubernetesClusterRequest) GetUsages() []ClusterUsage {
	if x != nil {
		return x.Usages
	}
	return nil
}

func (x *CreateKubernetesClusterRequest) GetNodeSelector() map[string]string {
	if x != nil {
		return x.NodeSelector
	}
	return nil
}

type RemoveKubernetesClusterRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *RemoveKubernetesClusterRequest) Reset() {
	*x = RemoveKubernetesClusterRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_platform_v1_platform_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RemoveKubernetesClusterRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoveKubernetesClusterRequest) ProtoMessage() {}

func (x *RemoveKubernetesClusterRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_platform_v1_platform_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoveKubernetesClusterRequest.ProtoReflect.Descriptor instead.
func (*RemoveKubernetesClusterRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_platform_v1_platform_proto_rawDescGZIP(), []int{11}
}

func (x *RemoveKubernetesClusterRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type ListKubernetesClustersResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Clusters []*KubernetesCluster `protobuf:"bytes,1,rep,name=clusters,proto3" json:"clusters,omitempty"`
}

func (x *ListKubernetesClustersResponse) Reset() {
	*x = ListKubernetesClustersResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_platform_v1_platform_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListKubernetesClustersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListKubernetesClustersResponse) ProtoMessage() {}

func (x *ListKubernetesClustersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_platform_v1_platform_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListKubernetesClustersResponse.ProtoReflect.Descriptor instead.
func (*ListKubernetesClustersResponse) Descriptor() ([]byte, []int) {
	return file_aistudio_platform_v1_platform_proto_rawDescGZIP(), []int{12}
}

func (x *ListKubernetesClustersResponse) GetClusters() []*KubernetesCluster {
	if x != nil {
		return x.Clusters
	}
	return nil
}

type GetKubernetesClusterRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *GetKubernetesClusterRequest) Reset() {
	*x = GetKubernetesClusterRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_platform_v1_platform_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetKubernetesClusterRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetKubernetesClusterRequest) ProtoMessage() {}

func (x *GetKubernetesClusterRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_platform_v1_platform_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetKubernetesClusterRequest.ProtoReflect.Descriptor instead.
func (*GetKubernetesClusterRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_platform_v1_platform_proto_rawDescGZIP(), []int{13}
}

func (x *GetKubernetesClusterRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type ListKubernetesClustersOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ListKubernetesClustersOptions) Reset() {
	*x = ListKubernetesClustersOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_platform_v1_platform_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListKubernetesClustersOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListKubernetesClustersOptions) ProtoMessage() {}

func (x *ListKubernetesClustersOptions) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_platform_v1_platform_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListKubernetesClustersOptions.ProtoReflect.Descriptor instead.
func (*ListKubernetesClustersOptions) Descriptor() ([]byte, []int) {
	return file_aistudio_platform_v1_platform_proto_rawDescGZIP(), []int{14}
}

type ListNodesOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ip            string `protobuf:"bytes,1,opt,name=ip,proto3" json:"ip,omitempty"`                       // 用于模糊搜索
	Page          int32  `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`                  // 页码
	PageSize      int32  `protobuf:"varint,3,opt,name=pageSize,proto3" json:"pageSize,omitempty"`          // 每页数量
	WorkspaceName string `protobuf:"bytes,4,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"` // 所属空间
	Assigned      bool   `protobuf:"varint,5,opt,name=assigned,proto3" json:"assigned,omitempty"`          // 是否已分配
	Cluster       string `protobuf:"bytes,6,opt,name=cluster,proto3" json:"cluster,omitempty"`             // 所属集群
	Status        string `protobuf:"bytes,7,opt,name=status,proto3" json:"status,omitempty"`               // 节点状态
	QueueName     string `protobuf:"bytes,8,opt,name=queueName,proto3" json:"queueName,omitempty"`         // 队列名称
}

func (x *ListNodesOptions) Reset() {
	*x = ListNodesOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_platform_v1_platform_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListNodesOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListNodesOptions) ProtoMessage() {}

func (x *ListNodesOptions) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_platform_v1_platform_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListNodesOptions.ProtoReflect.Descriptor instead.
func (*ListNodesOptions) Descriptor() ([]byte, []int) {
	return file_aistudio_platform_v1_platform_proto_rawDescGZIP(), []int{15}
}

func (x *ListNodesOptions) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *ListNodesOptions) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListNodesOptions) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListNodesOptions) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *ListNodesOptions) GetAssigned() bool {
	if x != nil {
		return x.Assigned
	}
	return false
}

func (x *ListNodesOptions) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

func (x *ListNodesOptions) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *ListNodesOptions) GetQueueName() string {
	if x != nil {
		return x.QueueName
	}
	return ""
}

type Node struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ip            string            `protobuf:"bytes,1,opt,name=ip,proto3" json:"ip,omitempty"`
	Cluster       string            `protobuf:"bytes,2,opt,name=cluster,proto3" json:"cluster,omitempty"`
	Status        string            `protobuf:"bytes,3,opt,name=status,proto3" json:"status,omitempty"`
	GpuProduct    string            `protobuf:"bytes,4,opt,name=gpuProduct,proto3" json:"gpuProduct,omitempty"`
	Specification string            `protobuf:"bytes,5,opt,name=specification,proto3" json:"specification,omitempty"`
	Labels        map[string]string `protobuf:"bytes,6,rep,name=labels,proto3" json:"labels,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Annotations   map[string]string `protobuf:"bytes,7,rep,name=annotations,proto3" json:"annotations,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	WorkspaceName string            `protobuf:"bytes,8,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	NodeName      string            `protobuf:"bytes,9,opt,name=nodeName,proto3" json:"nodeName,omitempty"`
	QueueName     string            `protobuf:"bytes,10,opt,name=queueName,proto3" json:"queueName,omitempty"`
}

func (x *Node) Reset() {
	*x = Node{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_platform_v1_platform_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Node) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Node) ProtoMessage() {}

func (x *Node) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_platform_v1_platform_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Node.ProtoReflect.Descriptor instead.
func (*Node) Descriptor() ([]byte, []int) {
	return file_aistudio_platform_v1_platform_proto_rawDescGZIP(), []int{16}
}

func (x *Node) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *Node) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

func (x *Node) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *Node) GetGpuProduct() string {
	if x != nil {
		return x.GpuProduct
	}
	return ""
}

func (x *Node) GetSpecification() string {
	if x != nil {
		return x.Specification
	}
	return ""
}

func (x *Node) GetLabels() map[string]string {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *Node) GetAnnotations() map[string]string {
	if x != nil {
		return x.Annotations
	}
	return nil
}

func (x *Node) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *Node) GetNodeName() string {
	if x != nil {
		return x.NodeName
	}
	return ""
}

func (x *Node) GetQueueName() string {
	if x != nil {
		return x.QueueName
	}
	return ""
}

type ListNodesResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Nodes []*Node `protobuf:"bytes,1,rep,name=nodes,proto3" json:"nodes,omitempty"`
	Total int32   `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
}

func (x *ListNodesResult) Reset() {
	*x = ListNodesResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_platform_v1_platform_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListNodesResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListNodesResult) ProtoMessage() {}

func (x *ListNodesResult) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_platform_v1_platform_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListNodesResult.ProtoReflect.Descriptor instead.
func (*ListNodesResult) Descriptor() ([]byte, []int) {
	return file_aistudio_platform_v1_platform_proto_rawDescGZIP(), []int{17}
}

func (x *ListNodesResult) GetNodes() []*Node {
	if x != nil {
		return x.Nodes
	}
	return nil
}

func (x *ListNodesResult) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

type AssignNodeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ips           []string `protobuf:"bytes,1,rep,name=ips,proto3" json:"ips,omitempty"`
	WorkspaceName string   `protobuf:"bytes,2,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
}

func (x *AssignNodeRequest) Reset() {
	*x = AssignNodeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_platform_v1_platform_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AssignNodeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssignNodeRequest) ProtoMessage() {}

func (x *AssignNodeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_platform_v1_platform_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssignNodeRequest.ProtoReflect.Descriptor instead.
func (*AssignNodeRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_platform_v1_platform_proto_rawDescGZIP(), []int{18}
}

func (x *AssignNodeRequest) GetIps() []string {
	if x != nil {
		return x.Ips
	}
	return nil
}

func (x *AssignNodeRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

type RecycleNodeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ips []string `protobuf:"bytes,1,rep,name=ips,proto3" json:"ips,omitempty"`
}

func (x *RecycleNodeRequest) Reset() {
	*x = RecycleNodeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_platform_v1_platform_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RecycleNodeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecycleNodeRequest) ProtoMessage() {}

func (x *RecycleNodeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_platform_v1_platform_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecycleNodeRequest.ProtoReflect.Descriptor instead.
func (*RecycleNodeRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_platform_v1_platform_proto_rawDescGZIP(), []int{19}
}

func (x *RecycleNodeRequest) GetIps() []string {
	if x != nil {
		return x.Ips
	}
	return nil
}

type GetNodesInfoStatisticsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetNodesInfoStatisticsRequest) Reset() {
	*x = GetNodesInfoStatisticsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_platform_v1_platform_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNodesInfoStatisticsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNodesInfoStatisticsRequest) ProtoMessage() {}

func (x *GetNodesInfoStatisticsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_platform_v1_platform_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNodesInfoStatisticsRequest.ProtoReflect.Descriptor instead.
func (*GetNodesInfoStatisticsRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_platform_v1_platform_proto_rawDescGZIP(), []int{20}
}

type NodesInfoStatisticsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NodeTotal          int32            `protobuf:"varint,1,opt,name=nodeTotal,proto3" json:"nodeTotal,omitempty"`                                                                                   // 节点总数
	GpuNodeTotal       int32            `protobuf:"varint,2,opt,name=gpuNodeTotal,proto3" json:"gpuNodeTotal,omitempty"`                                                                             // gpu节点总数
	GpuMap             map[string]int32 `protobuf:"bytes,3,rep,name=gpuMap,proto3" json:"gpuMap,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"` // 统计不同的 gpu规格 节点数量
	ReadyNodeNum       int32            `protobuf:"varint,4,opt,name=readyNodeNum,proto3" json:"readyNodeNum,omitempty"`                                                                             // 正常节点
	NotReadyNodeNum    int32            `protobuf:"varint,5,opt,name=notReadyNodeNum,proto3" json:"notReadyNodeNum,omitempty"`                                                                       // 异常节点
	AssignedNodeNum    int32            `protobuf:"varint,6,opt,name=assignedNodeNum,proto3" json:"assignedNodeNum,omitempty"`                                                                       // 已分配节点
	NotAssignedNodeNum int32            `protobuf:"varint,7,opt,name=notAssignedNodeNum,proto3" json:"notAssignedNodeNum,omitempty"`                                                                 // 未分配节点
}

func (x *NodesInfoStatisticsResponse) Reset() {
	*x = NodesInfoStatisticsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_platform_v1_platform_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NodesInfoStatisticsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NodesInfoStatisticsResponse) ProtoMessage() {}

func (x *NodesInfoStatisticsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_platform_v1_platform_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NodesInfoStatisticsResponse.ProtoReflect.Descriptor instead.
func (*NodesInfoStatisticsResponse) Descriptor() ([]byte, []int) {
	return file_aistudio_platform_v1_platform_proto_rawDescGZIP(), []int{21}
}

func (x *NodesInfoStatisticsResponse) GetNodeTotal() int32 {
	if x != nil {
		return x.NodeTotal
	}
	return 0
}

func (x *NodesInfoStatisticsResponse) GetGpuNodeTotal() int32 {
	if x != nil {
		return x.GpuNodeTotal
	}
	return 0
}

func (x *NodesInfoStatisticsResponse) GetGpuMap() map[string]int32 {
	if x != nil {
		return x.GpuMap
	}
	return nil
}

func (x *NodesInfoStatisticsResponse) GetReadyNodeNum() int32 {
	if x != nil {
		return x.ReadyNodeNum
	}
	return 0
}

func (x *NodesInfoStatisticsResponse) GetNotReadyNodeNum() int32 {
	if x != nil {
		return x.NotReadyNodeNum
	}
	return 0
}

func (x *NodesInfoStatisticsResponse) GetAssignedNodeNum() int32 {
	if x != nil {
		return x.AssignedNodeNum
	}
	return 0
}

func (x *NodesInfoStatisticsResponse) GetNotAssignedNodeNum() int32 {
	if x != nil {
		return x.NotAssignedNodeNum
	}
	return 0
}

type AddDragonflyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 集群名称
	Cluster string `protobuf:"bytes,1,opt,name=cluster,proto3" json:"cluster,omitempty"`
	// 地区
	Region string `protobuf:"bytes,2,opt,name=region,proto3" json:"region,omitempty"`
	// 分区
	Zone string `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	// 描述
	Description string `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	// 地址
	Address string `protobuf:"bytes,5,opt,name=address,proto3" json:"address,omitempty"`
}

func (x *AddDragonflyRequest) Reset() {
	*x = AddDragonflyRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_platform_v1_platform_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddDragonflyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddDragonflyRequest) ProtoMessage() {}

func (x *AddDragonflyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_platform_v1_platform_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddDragonflyRequest.ProtoReflect.Descriptor instead.
func (*AddDragonflyRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_platform_v1_platform_proto_rawDescGZIP(), []int{22}
}

func (x *AddDragonflyRequest) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

func (x *AddDragonflyRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *AddDragonflyRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *AddDragonflyRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *AddDragonflyRequest) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

type SetDragonflyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// 集群名称
	Cluster string `protobuf:"bytes,2,opt,name=cluster,proto3" json:"cluster,omitempty"`
	// 地区
	Region string `protobuf:"bytes,3,opt,name=region,proto3" json:"region,omitempty"`
	// 分区
	Zone string `protobuf:"bytes,4,opt,name=zone,proto3" json:"zone,omitempty"`
	// 描述
	Description string `protobuf:"bytes,5,opt,name=description,proto3" json:"description,omitempty"`
	// 地址
	Address string `protobuf:"bytes,6,opt,name=address,proto3" json:"address,omitempty"`
}

func (x *SetDragonflyRequest) Reset() {
	*x = SetDragonflyRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_platform_v1_platform_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetDragonflyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetDragonflyRequest) ProtoMessage() {}

func (x *SetDragonflyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_platform_v1_platform_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetDragonflyRequest.ProtoReflect.Descriptor instead.
func (*SetDragonflyRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_platform_v1_platform_proto_rawDescGZIP(), []int{23}
}

func (x *SetDragonflyRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *SetDragonflyRequest) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

func (x *SetDragonflyRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *SetDragonflyRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *SetDragonflyRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *SetDragonflyRequest) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

type DeleteDragonflyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *DeleteDragonflyRequest) Reset() {
	*x = DeleteDragonflyRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_platform_v1_platform_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteDragonflyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteDragonflyRequest) ProtoMessage() {}

func (x *DeleteDragonflyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_platform_v1_platform_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteDragonflyRequest.ProtoReflect.Descriptor instead.
func (*DeleteDragonflyRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_platform_v1_platform_proto_rawDescGZIP(), []int{24}
}

func (x *DeleteDragonflyRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type ListDragonflyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 集群名称
	Cluster string `protobuf:"bytes,1,opt,name=cluster,proto3" json:"cluster,omitempty"`
	// 创建人
	Creator string `protobuf:"bytes,2,opt,name=creator,proto3" json:"creator,omitempty"`
	// page
	Page int32 `protobuf:"varint,3,opt,name=page,proto3" json:"page,omitempty"`
	// pageSize
	PageSize int32 `protobuf:"varint,4,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
}

func (x *ListDragonflyRequest) Reset() {
	*x = ListDragonflyRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_platform_v1_platform_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListDragonflyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDragonflyRequest) ProtoMessage() {}

func (x *ListDragonflyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_platform_v1_platform_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDragonflyRequest.ProtoReflect.Descriptor instead.
func (*ListDragonflyRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_platform_v1_platform_proto_rawDescGZIP(), []int{25}
}

func (x *ListDragonflyRequest) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

func (x *ListDragonflyRequest) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *ListDragonflyRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListDragonflyRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

type ListDragonflyResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Dragonflies []*Dragonfly `protobuf:"bytes,1,rep,name=dragonflies,proto3" json:"dragonflies,omitempty"`
	Total       int32        `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
}

func (x *ListDragonflyResponse) Reset() {
	*x = ListDragonflyResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_platform_v1_platform_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListDragonflyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDragonflyResponse) ProtoMessage() {}

func (x *ListDragonflyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_platform_v1_platform_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDragonflyResponse.ProtoReflect.Descriptor instead.
func (*ListDragonflyResponse) Descriptor() ([]byte, []int) {
	return file_aistudio_platform_v1_platform_proto_rawDescGZIP(), []int{26}
}

func (x *ListDragonflyResponse) GetDragonflies() []*Dragonfly {
	if x != nil {
		return x.Dragonflies
	}
	return nil
}

func (x *ListDragonflyResponse) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

type Dragonfly struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// 集群名称
	Cluster string `protobuf:"bytes,2,opt,name=cluster,proto3" json:"cluster,omitempty"`
	// 地区
	Region string `protobuf:"bytes,3,opt,name=region,proto3" json:"region,omitempty"`
	// 分区
	Zone string `protobuf:"bytes,4,opt,name=zone,proto3" json:"zone,omitempty"`
	// 描述
	Description string `protobuf:"bytes,5,opt,name=description,proto3" json:"description,omitempty"`
	// 地址
	Address string `protobuf:"bytes,6,opt,name=address,proto3" json:"address,omitempty"`
	// 创建人
	Creator string `protobuf:"bytes,7,opt,name=creator,proto3" json:"creator,omitempty"`
	// 创建时间
	CreateTime string `protobuf:"bytes,8,opt,name=createTime,proto3" json:"createTime,omitempty"`
	// 更新时间
	UpdateTime string `protobuf:"bytes,9,opt,name=updateTime,proto3" json:"updateTime,omitempty"`
}

func (x *Dragonfly) Reset() {
	*x = Dragonfly{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_platform_v1_platform_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Dragonfly) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Dragonfly) ProtoMessage() {}

func (x *Dragonfly) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_platform_v1_platform_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Dragonfly.ProtoReflect.Descriptor instead.
func (*Dragonfly) Descriptor() ([]byte, []int) {
	return file_aistudio_platform_v1_platform_proto_rawDescGZIP(), []int{27}
}

func (x *Dragonfly) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Dragonfly) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

func (x *Dragonfly) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *Dragonfly) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *Dragonfly) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Dragonfly) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *Dragonfly) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *Dragonfly) GetCreateTime() string {
	if x != nil {
		return x.CreateTime
	}
	return ""
}

func (x *Dragonfly) GetUpdateTime() string {
	if x != nil {
		return x.UpdateTime
	}
	return ""
}

type ListDistributedCacheRegistrationsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Creator  string `protobuf:"bytes,1,opt,name=creator,proto3" json:"creator,omitempty"`
	Page     int32  `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	PageSize int32  `protobuf:"varint,3,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
}

func (x *ListDistributedCacheRegistrationsRequest) Reset() {
	*x = ListDistributedCacheRegistrationsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_platform_v1_platform_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListDistributedCacheRegistrationsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDistributedCacheRegistrationsRequest) ProtoMessage() {}

func (x *ListDistributedCacheRegistrationsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_platform_v1_platform_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDistributedCacheRegistrationsRequest.ProtoReflect.Descriptor instead.
func (*ListDistributedCacheRegistrationsRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_platform_v1_platform_proto_rawDescGZIP(), []int{28}
}

func (x *ListDistributedCacheRegistrationsRequest) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *ListDistributedCacheRegistrationsRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListDistributedCacheRegistrationsRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

type ListDistributedCacheRegistrationsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DistributedCacheRegistrations []*DistributedCacheRegistration `protobuf:"bytes,3,rep,name=distributedCacheRegistrations,proto3" json:"distributedCacheRegistrations,omitempty"`
	Total                         int32                           `protobuf:"varint,4,opt,name=total,proto3" json:"total,omitempty"`
}

func (x *ListDistributedCacheRegistrationsResponse) Reset() {
	*x = ListDistributedCacheRegistrationsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_platform_v1_platform_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListDistributedCacheRegistrationsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDistributedCacheRegistrationsResponse) ProtoMessage() {}

func (x *ListDistributedCacheRegistrationsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_platform_v1_platform_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDistributedCacheRegistrationsResponse.ProtoReflect.Descriptor instead.
func (*ListDistributedCacheRegistrationsResponse) Descriptor() ([]byte, []int) {
	return file_aistudio_platform_v1_platform_proto_rawDescGZIP(), []int{29}
}

func (x *ListDistributedCacheRegistrationsResponse) GetDistributedCacheRegistrations() []*DistributedCacheRegistration {
	if x != nil {
		return x.DistributedCacheRegistrations
	}
	return nil
}

func (x *ListDistributedCacheRegistrationsResponse) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

type DistributedCacheRegistration struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id              string                              `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	CacheRegion     string                              `protobuf:"bytes,2,opt,name=cacheRegion,proto3" json:"cacheRegion,omitempty"`
	CacheRegionName string                              `protobuf:"bytes,3,opt,name=cacheRegionName,proto3" json:"cacheRegionName,omitempty"`
	CacheZone       string                              `protobuf:"bytes,4,opt,name=cacheZone,proto3" json:"cacheZone,omitempty"`
	CacheZoneName   string                              `protobuf:"bytes,5,opt,name=cacheZoneName,proto3" json:"cacheZoneName,omitempty"`
	Creator         string                              `protobuf:"bytes,6,opt,name=creator,proto3" json:"creator,omitempty"`
	CacheFramework  string                              `protobuf:"bytes,7,opt,name=cacheFramework,proto3" json:"cacheFramework,omitempty"`
	Cluster         string                              `protobuf:"bytes,8,opt,name=cluster,proto3" json:"cluster,omitempty"`
	ClusterName     string                              `protobuf:"bytes,9,opt,name=clusterName,proto3" json:"clusterName,omitempty"`
	Status          *DistributedCacheRegistrationStatus `protobuf:"bytes,10,opt,name=status,proto3" json:"status,omitempty"`
	Enabled         bool                                `protobuf:"varint,11,opt,name=enabled,proto3" json:"enabled,omitempty"`
}

func (x *DistributedCacheRegistration) Reset() {
	*x = DistributedCacheRegistration{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_platform_v1_platform_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DistributedCacheRegistration) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DistributedCacheRegistration) ProtoMessage() {}

func (x *DistributedCacheRegistration) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_platform_v1_platform_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DistributedCacheRegistration.ProtoReflect.Descriptor instead.
func (*DistributedCacheRegistration) Descriptor() ([]byte, []int) {
	return file_aistudio_platform_v1_platform_proto_rawDescGZIP(), []int{30}
}

func (x *DistributedCacheRegistration) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *DistributedCacheRegistration) GetCacheRegion() string {
	if x != nil {
		return x.CacheRegion
	}
	return ""
}

func (x *DistributedCacheRegistration) GetCacheRegionName() string {
	if x != nil {
		return x.CacheRegionName
	}
	return ""
}

func (x *DistributedCacheRegistration) GetCacheZone() string {
	if x != nil {
		return x.CacheZone
	}
	return ""
}

func (x *DistributedCacheRegistration) GetCacheZoneName() string {
	if x != nil {
		return x.CacheZoneName
	}
	return ""
}

func (x *DistributedCacheRegistration) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *DistributedCacheRegistration) GetCacheFramework() string {
	if x != nil {
		return x.CacheFramework
	}
	return ""
}

func (x *DistributedCacheRegistration) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

func (x *DistributedCacheRegistration) GetClusterName() string {
	if x != nil {
		return x.ClusterName
	}
	return ""
}

func (x *DistributedCacheRegistration) GetStatus() *DistributedCacheRegistrationStatus {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *DistributedCacheRegistration) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

type DistributedCacheRegistrationStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Capacity int64 `protobuf:"varint,1,opt,name=capacity,proto3" json:"capacity,omitempty"`
	Used     int64 `protobuf:"varint,2,opt,name=used,proto3" json:"used,omitempty"`
}

func (x *DistributedCacheRegistrationStatus) Reset() {
	*x = DistributedCacheRegistrationStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_platform_v1_platform_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DistributedCacheRegistrationStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DistributedCacheRegistrationStatus) ProtoMessage() {}

func (x *DistributedCacheRegistrationStatus) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_platform_v1_platform_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DistributedCacheRegistrationStatus.ProtoReflect.Descriptor instead.
func (*DistributedCacheRegistrationStatus) Descriptor() ([]byte, []int) {
	return file_aistudio_platform_v1_platform_proto_rawDescGZIP(), []int{31}
}

func (x *DistributedCacheRegistrationStatus) GetCapacity() int64 {
	if x != nil {
		return x.Capacity
	}
	return 0
}

func (x *DistributedCacheRegistrationStatus) GetUsed() int64 {
	if x != nil {
		return x.Used
	}
	return 0
}

type CreateDistributedCacheRegistrationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CacheRegion     string `protobuf:"bytes,1,opt,name=cacheRegion,proto3" json:"cacheRegion,omitempty"`
	CacheRegionName string `protobuf:"bytes,2,opt,name=cacheRegionName,proto3" json:"cacheRegionName,omitempty"`
	CacheZone       string `protobuf:"bytes,3,opt,name=cacheZone,proto3" json:"cacheZone,omitempty"`
	CacheZoneName   string `protobuf:"bytes,4,opt,name=cacheZoneName,proto3" json:"cacheZoneName,omitempty"`
	CacheFramework  string `protobuf:"bytes,5,opt,name=cacheFramework,proto3" json:"cacheFramework,omitempty"`
	Cluster         string `protobuf:"bytes,6,opt,name=cluster,proto3" json:"cluster,omitempty"`
	ClusterName     string `protobuf:"bytes,7,opt,name=clusterName,proto3" json:"clusterName,omitempty"`
	Enabled         bool   `protobuf:"varint,8,opt,name=enabled,proto3" json:"enabled,omitempty"`
}

func (x *CreateDistributedCacheRegistrationRequest) Reset() {
	*x = CreateDistributedCacheRegistrationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_platform_v1_platform_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateDistributedCacheRegistrationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateDistributedCacheRegistrationRequest) ProtoMessage() {}

func (x *CreateDistributedCacheRegistrationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_platform_v1_platform_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateDistributedCacheRegistrationRequest.ProtoReflect.Descriptor instead.
func (*CreateDistributedCacheRegistrationRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_platform_v1_platform_proto_rawDescGZIP(), []int{32}
}

func (x *CreateDistributedCacheRegistrationRequest) GetCacheRegion() string {
	if x != nil {
		return x.CacheRegion
	}
	return ""
}

func (x *CreateDistributedCacheRegistrationRequest) GetCacheRegionName() string {
	if x != nil {
		return x.CacheRegionName
	}
	return ""
}

func (x *CreateDistributedCacheRegistrationRequest) GetCacheZone() string {
	if x != nil {
		return x.CacheZone
	}
	return ""
}

func (x *CreateDistributedCacheRegistrationRequest) GetCacheZoneName() string {
	if x != nil {
		return x.CacheZoneName
	}
	return ""
}

func (x *CreateDistributedCacheRegistrationRequest) GetCacheFramework() string {
	if x != nil {
		return x.CacheFramework
	}
	return ""
}

func (x *CreateDistributedCacheRegistrationRequest) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

func (x *CreateDistributedCacheRegistrationRequest) GetClusterName() string {
	if x != nil {
		return x.ClusterName
	}
	return ""
}

func (x *CreateDistributedCacheRegistrationRequest) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

type CreateDistributedCacheRegistrationResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CreateDistributedCacheRegistrationResponse) Reset() {
	*x = CreateDistributedCacheRegistrationResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_platform_v1_platform_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateDistributedCacheRegistrationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateDistributedCacheRegistrationResponse) ProtoMessage() {}

func (x *CreateDistributedCacheRegistrationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_platform_v1_platform_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateDistributedCacheRegistrationResponse.ProtoReflect.Descriptor instead.
func (*CreateDistributedCacheRegistrationResponse) Descriptor() ([]byte, []int) {
	return file_aistudio_platform_v1_platform_proto_rawDescGZIP(), []int{33}
}

type DeleteDistributedCacheRegistrationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *DeleteDistributedCacheRegistrationRequest) Reset() {
	*x = DeleteDistributedCacheRegistrationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_platform_v1_platform_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteDistributedCacheRegistrationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteDistributedCacheRegistrationRequest) ProtoMessage() {}

func (x *DeleteDistributedCacheRegistrationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_platform_v1_platform_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteDistributedCacheRegistrationRequest.ProtoReflect.Descriptor instead.
func (*DeleteDistributedCacheRegistrationRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_platform_v1_platform_proto_rawDescGZIP(), []int{34}
}

func (x *DeleteDistributedCacheRegistrationRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type DeleteDistributedCacheRegistrationResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeleteDistributedCacheRegistrationResponse) Reset() {
	*x = DeleteDistributedCacheRegistrationResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_platform_v1_platform_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteDistributedCacheRegistrationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteDistributedCacheRegistrationResponse) ProtoMessage() {}

func (x *DeleteDistributedCacheRegistrationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_platform_v1_platform_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteDistributedCacheRegistrationResponse.ProtoReflect.Descriptor instead.
func (*DeleteDistributedCacheRegistrationResponse) Descriptor() ([]byte, []int) {
	return file_aistudio_platform_v1_platform_proto_rawDescGZIP(), []int{35}
}

type UpdateDistributedCacheRegistrationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id              string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	CacheRegion     string `protobuf:"bytes,2,opt,name=cacheRegion,proto3" json:"cacheRegion,omitempty"`
	CacheRegionName string `protobuf:"bytes,3,opt,name=cacheRegionName,proto3" json:"cacheRegionName,omitempty"`
	CacheZone       string `protobuf:"bytes,4,opt,name=cacheZone,proto3" json:"cacheZone,omitempty"`
	CacheZoneName   string `protobuf:"bytes,5,opt,name=cacheZoneName,proto3" json:"cacheZoneName,omitempty"`
	CacheFramework  string `protobuf:"bytes,6,opt,name=cacheFramework,proto3" json:"cacheFramework,omitempty"`
	Cluster         string `protobuf:"bytes,7,opt,name=cluster,proto3" json:"cluster,omitempty"`
	ClusterName     string `protobuf:"bytes,8,opt,name=clusterName,proto3" json:"clusterName,omitempty"`
	Enabled         bool   `protobuf:"varint,9,opt,name=enabled,proto3" json:"enabled,omitempty"`
}

func (x *UpdateDistributedCacheRegistrationRequest) Reset() {
	*x = UpdateDistributedCacheRegistrationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_platform_v1_platform_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateDistributedCacheRegistrationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateDistributedCacheRegistrationRequest) ProtoMessage() {}

func (x *UpdateDistributedCacheRegistrationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_platform_v1_platform_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateDistributedCacheRegistrationRequest.ProtoReflect.Descriptor instead.
func (*UpdateDistributedCacheRegistrationRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_platform_v1_platform_proto_rawDescGZIP(), []int{36}
}

func (x *UpdateDistributedCacheRegistrationRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *UpdateDistributedCacheRegistrationRequest) GetCacheRegion() string {
	if x != nil {
		return x.CacheRegion
	}
	return ""
}

func (x *UpdateDistributedCacheRegistrationRequest) GetCacheRegionName() string {
	if x != nil {
		return x.CacheRegionName
	}
	return ""
}

func (x *UpdateDistributedCacheRegistrationRequest) GetCacheZone() string {
	if x != nil {
		return x.CacheZone
	}
	return ""
}

func (x *UpdateDistributedCacheRegistrationRequest) GetCacheZoneName() string {
	if x != nil {
		return x.CacheZoneName
	}
	return ""
}

func (x *UpdateDistributedCacheRegistrationRequest) GetCacheFramework() string {
	if x != nil {
		return x.CacheFramework
	}
	return ""
}

func (x *UpdateDistributedCacheRegistrationRequest) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

func (x *UpdateDistributedCacheRegistrationRequest) GetClusterName() string {
	if x != nil {
		return x.ClusterName
	}
	return ""
}

func (x *UpdateDistributedCacheRegistrationRequest) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

type UpdateDistributedCacheRegistrationResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdateDistributedCacheRegistrationResponse) Reset() {
	*x = UpdateDistributedCacheRegistrationResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_platform_v1_platform_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateDistributedCacheRegistrationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateDistributedCacheRegistrationResponse) ProtoMessage() {}

func (x *UpdateDistributedCacheRegistrationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_platform_v1_platform_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateDistributedCacheRegistrationResponse.ProtoReflect.Descriptor instead.
func (*UpdateDistributedCacheRegistrationResponse) Descriptor() ([]byte, []int) {
	return file_aistudio_platform_v1_platform_proto_rawDescGZIP(), []int{37}
}

type ListDistributedCacheRegistrationRegionsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ListDistributedCacheRegistrationRegionsRequest) Reset() {
	*x = ListDistributedCacheRegistrationRegionsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_platform_v1_platform_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListDistributedCacheRegistrationRegionsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDistributedCacheRegistrationRegionsRequest) ProtoMessage() {}

func (x *ListDistributedCacheRegistrationRegionsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_platform_v1_platform_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDistributedCacheRegistrationRegionsRequest.ProtoReflect.Descriptor instead.
func (*ListDistributedCacheRegistrationRegionsRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_platform_v1_platform_proto_rawDescGZIP(), []int{38}
}

type ListDistributedCacheRegistrationRegionsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DistributedCacheRegistrationRegions []*DistributedCacheRegistrationRegion `protobuf:"bytes,1,rep,name=distributedCacheRegistrationRegions,proto3" json:"distributedCacheRegistrationRegions,omitempty"`
}

func (x *ListDistributedCacheRegistrationRegionsResponse) Reset() {
	*x = ListDistributedCacheRegistrationRegionsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_platform_v1_platform_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListDistributedCacheRegistrationRegionsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDistributedCacheRegistrationRegionsResponse) ProtoMessage() {}

func (x *ListDistributedCacheRegistrationRegionsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_platform_v1_platform_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDistributedCacheRegistrationRegionsResponse.ProtoReflect.Descriptor instead.
func (*ListDistributedCacheRegistrationRegionsResponse) Descriptor() ([]byte, []int) {
	return file_aistudio_platform_v1_platform_proto_rawDescGZIP(), []int{39}
}

func (x *ListDistributedCacheRegistrationRegionsResponse) GetDistributedCacheRegistrationRegions() []*DistributedCacheRegistrationRegion {
	if x != nil {
		return x.DistributedCacheRegistrationRegions
	}
	return nil
}

type DistributedCacheRegistrationRegion struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CacheRegion     string `protobuf:"bytes,1,opt,name=cacheRegion,proto3" json:"cacheRegion,omitempty"`
	CacheRegionName string `protobuf:"bytes,2,opt,name=cacheRegionName,proto3" json:"cacheRegionName,omitempty"`
}

func (x *DistributedCacheRegistrationRegion) Reset() {
	*x = DistributedCacheRegistrationRegion{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_platform_v1_platform_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DistributedCacheRegistrationRegion) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DistributedCacheRegistrationRegion) ProtoMessage() {}

func (x *DistributedCacheRegistrationRegion) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_platform_v1_platform_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DistributedCacheRegistrationRegion.ProtoReflect.Descriptor instead.
func (*DistributedCacheRegistrationRegion) Descriptor() ([]byte, []int) {
	return file_aistudio_platform_v1_platform_proto_rawDescGZIP(), []int{40}
}

func (x *DistributedCacheRegistrationRegion) GetCacheRegion() string {
	if x != nil {
		return x.CacheRegion
	}
	return ""
}

func (x *DistributedCacheRegistrationRegion) GetCacheRegionName() string {
	if x != nil {
		return x.CacheRegionName
	}
	return ""
}

type ListDistributedCacheRegistrationZonesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CacheRegion string `protobuf:"bytes,1,opt,name=cacheRegion,proto3" json:"cacheRegion,omitempty"`
}

func (x *ListDistributedCacheRegistrationZonesRequest) Reset() {
	*x = ListDistributedCacheRegistrationZonesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_platform_v1_platform_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListDistributedCacheRegistrationZonesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDistributedCacheRegistrationZonesRequest) ProtoMessage() {}

func (x *ListDistributedCacheRegistrationZonesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_platform_v1_platform_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDistributedCacheRegistrationZonesRequest.ProtoReflect.Descriptor instead.
func (*ListDistributedCacheRegistrationZonesRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_platform_v1_platform_proto_rawDescGZIP(), []int{41}
}

func (x *ListDistributedCacheRegistrationZonesRequest) GetCacheRegion() string {
	if x != nil {
		return x.CacheRegion
	}
	return ""
}

type ListDistributedCacheRegistrationZonesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DistributedCacheRegistrationZones []*DistributedCacheRegistrationZone `protobuf:"bytes,1,rep,name=distributedCacheRegistrationZones,proto3" json:"distributedCacheRegistrationZones,omitempty"`
}

func (x *ListDistributedCacheRegistrationZonesResponse) Reset() {
	*x = ListDistributedCacheRegistrationZonesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_platform_v1_platform_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListDistributedCacheRegistrationZonesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDistributedCacheRegistrationZonesResponse) ProtoMessage() {}

func (x *ListDistributedCacheRegistrationZonesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_platform_v1_platform_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDistributedCacheRegistrationZonesResponse.ProtoReflect.Descriptor instead.
func (*ListDistributedCacheRegistrationZonesResponse) Descriptor() ([]byte, []int) {
	return file_aistudio_platform_v1_platform_proto_rawDescGZIP(), []int{42}
}

func (x *ListDistributedCacheRegistrationZonesResponse) GetDistributedCacheRegistrationZones() []*DistributedCacheRegistrationZone {
	if x != nil {
		return x.DistributedCacheRegistrationZones
	}
	return nil
}

type DistributedCacheRegistrationZone struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CacheZone     string `protobuf:"bytes,1,opt,name=cacheZone,proto3" json:"cacheZone,omitempty"`
	CacheZoneName string `protobuf:"bytes,2,opt,name=cacheZoneName,proto3" json:"cacheZoneName,omitempty"`
}

func (x *DistributedCacheRegistrationZone) Reset() {
	*x = DistributedCacheRegistrationZone{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_platform_v1_platform_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DistributedCacheRegistrationZone) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DistributedCacheRegistrationZone) ProtoMessage() {}

func (x *DistributedCacheRegistrationZone) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_platform_v1_platform_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DistributedCacheRegistrationZone.ProtoReflect.Descriptor instead.
func (*DistributedCacheRegistrationZone) Descriptor() ([]byte, []int) {
	return file_aistudio_platform_v1_platform_proto_rawDescGZIP(), []int{43}
}

func (x *DistributedCacheRegistrationZone) GetCacheZone() string {
	if x != nil {
		return x.CacheZone
	}
	return ""
}

func (x *DistributedCacheRegistrationZone) GetCacheZoneName() string {
	if x != nil {
		return x.CacheZoneName
	}
	return ""
}

type ListDistributedCacheRegistrationClustersRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CacheRegion string `protobuf:"bytes,1,opt,name=cacheRegion,proto3" json:"cacheRegion,omitempty"`
	CacheZone   string `protobuf:"bytes,2,opt,name=cacheZone,proto3" json:"cacheZone,omitempty"`
}

func (x *ListDistributedCacheRegistrationClustersRequest) Reset() {
	*x = ListDistributedCacheRegistrationClustersRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_platform_v1_platform_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListDistributedCacheRegistrationClustersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDistributedCacheRegistrationClustersRequest) ProtoMessage() {}

func (x *ListDistributedCacheRegistrationClustersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_platform_v1_platform_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDistributedCacheRegistrationClustersRequest.ProtoReflect.Descriptor instead.
func (*ListDistributedCacheRegistrationClustersRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_platform_v1_platform_proto_rawDescGZIP(), []int{44}
}

func (x *ListDistributedCacheRegistrationClustersRequest) GetCacheRegion() string {
	if x != nil {
		return x.CacheRegion
	}
	return ""
}

func (x *ListDistributedCacheRegistrationClustersRequest) GetCacheZone() string {
	if x != nil {
		return x.CacheZone
	}
	return ""
}

type ListDistributedCacheRegistrationClustersResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DistributedCacheRegistrationClusters []*DistributedCacheRegistrationCluster `protobuf:"bytes,1,rep,name=distributedCacheRegistrationClusters,proto3" json:"distributedCacheRegistrationClusters,omitempty"`
}

func (x *ListDistributedCacheRegistrationClustersResponse) Reset() {
	*x = ListDistributedCacheRegistrationClustersResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_platform_v1_platform_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListDistributedCacheRegistrationClustersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDistributedCacheRegistrationClustersResponse) ProtoMessage() {}

func (x *ListDistributedCacheRegistrationClustersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_platform_v1_platform_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDistributedCacheRegistrationClustersResponse.ProtoReflect.Descriptor instead.
func (*ListDistributedCacheRegistrationClustersResponse) Descriptor() ([]byte, []int) {
	return file_aistudio_platform_v1_platform_proto_rawDescGZIP(), []int{45}
}

func (x *ListDistributedCacheRegistrationClustersResponse) GetDistributedCacheRegistrationClusters() []*DistributedCacheRegistrationCluster {
	if x != nil {
		return x.DistributedCacheRegistrationClusters
	}
	return nil
}

type DistributedCacheRegistrationCluster struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Cluster     string `protobuf:"bytes,1,opt,name=cluster,proto3" json:"cluster,omitempty"`
	ClusterName string `protobuf:"bytes,2,opt,name=clusterName,proto3" json:"clusterName,omitempty"`
}

func (x *DistributedCacheRegistrationCluster) Reset() {
	*x = DistributedCacheRegistrationCluster{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_platform_v1_platform_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DistributedCacheRegistrationCluster) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DistributedCacheRegistrationCluster) ProtoMessage() {}

func (x *DistributedCacheRegistrationCluster) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_platform_v1_platform_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DistributedCacheRegistrationCluster.ProtoReflect.Descriptor instead.
func (*DistributedCacheRegistrationCluster) Descriptor() ([]byte, []int) {
	return file_aistudio_platform_v1_platform_proto_rawDescGZIP(), []int{46}
}

func (x *DistributedCacheRegistrationCluster) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

func (x *DistributedCacheRegistrationCluster) GetClusterName() string {
	if x != nil {
		return x.ClusterName
	}
	return ""
}

var File_aistudio_platform_v1_platform_proto protoreflect.FileDescriptor

var file_aistudio_platform_v1_platform_proto_rawDesc = []byte{
	0x0a, 0x23, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2f, 0x70, 0x6c, 0x61, 0x74, 0x66,
	0x6f, 0x72, 0x6d, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x19, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74,
	0x75, 0x64, 0x69, 0x6f, 0x2e, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31,
	0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e,
	0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f,
	0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x13, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xe8, 0x02, 0x0a, 0x1f, 0x41, 0x64,
	0x64, 0x4f, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x75, 0x62, 0x65, 0x46, 0x53, 0x43,
	0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06,
	0x72, 0x04, 0x10, 0x01, 0x18, 0x1f, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x33, 0x0a, 0x06,
	0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1b, 0xfa, 0x42,
	0x18, 0x72, 0x16, 0x52, 0x09, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x2d, 0x62, 0x6a, 0x52, 0x09,
	0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x2d, 0x73, 0x68, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f,
	0x6e, 0x12, 0x24, 0x0a, 0x05, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09,
	0x42, 0x0e, 0xfa, 0x42, 0x0b, 0x92, 0x01, 0x08, 0x22, 0x06, 0x72, 0x04, 0x52, 0x02, 0x77, 0x71,
	0x52, 0x05, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x12, 0x2c, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42,
	0x07, 0x72, 0x05, 0x10, 0x01, 0x18, 0xff, 0x01, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2b, 0x0a, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72,
	0x04, 0x10, 0x01, 0x18, 0x1f, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x30, 0x0a, 0x0d, 0x6d, 0x61, 0x73, 0x74, 0x65, 0x72, 0x41, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05,
	0x10, 0x01, 0x18, 0xff, 0x01, 0x52, 0x0d, 0x6d, 0x61, 0x73, 0x74, 0x65, 0x72, 0x41, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x24,
	0x0a, 0x0d, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x22, 0x3b, 0x0a, 0x1a, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x75,
	0x62, 0x65, 0x46, 0x53, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x1d, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x1f, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x22, 0x70, 0x0a, 0x1d, 0x43, 0x75, 0x62, 0x65, 0x46, 0x53, 0x43, 0x6c, 0x75, 0x73, 0x74,
	0x65, 0x72, 0x53, 0x65, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x1d, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x1f, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x30, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x18, 0xfa, 0x42, 0x15, 0x72, 0x13, 0x52, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65,
	0x64, 0x52, 0x08, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x22, 0x7f, 0x0a, 0x19, 0x53, 0x65, 0x74, 0x43, 0x75, 0x62, 0x65, 0x46, 0x53,
	0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x1c, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa,
	0x42, 0x07, 0x72, 0x05, 0x10, 0x01, 0x18, 0xff, 0x01, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x1d,
	0x0a, 0x04, 0x75, 0x73, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42,
	0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x1f, 0x52, 0x04, 0x75, 0x73, 0x65, 0x72, 0x12, 0x25, 0x0a,
	0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x40, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73,
	0x77, 0x6f, 0x72, 0x64, 0x22, 0x5e, 0x0a, 0x1c, 0x4c, 0x69, 0x73, 0x74, 0x4e, 0x6f, 0x64, 0x65,
	0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x67, 0x70, 0x75, 0x45, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x67, 0x70, 0x75, 0x45, 0x6e, 0x61,
	0x62, 0x6c, 0x65, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x67, 0x70, 0x75, 0x50, 0x72, 0x6f, 0x64, 0x75,
	0x63, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x67, 0x70, 0x75, 0x50, 0x72, 0x6f,
	0x64, 0x75, 0x63, 0x74, 0x22, 0x7c, 0x0a, 0x1c, 0x4c, 0x69, 0x73, 0x74, 0x4e, 0x6f, 0x64, 0x65,
	0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0x5c, 0x0a, 0x12, 0x6e, 0x6f, 0x64, 0x65, 0x53, 0x70, 0x65, 0x63,
	0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x2c, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f,
	0x2e, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x6f, 0x64,
	0x65, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x12,
	0x6e, 0x6f, 0x64, 0x65, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x22, 0x34, 0x0a, 0x1e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4e, 0x6f, 0x64, 0x65,
	0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x8f, 0x03, 0x0a, 0x11, 0x4b, 0x75, 0x62,
	0x65, 0x72, 0x6e, 0x65, 0x74, 0x65, 0x73, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f,
	0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x10,
	0x0a, 0x03, 0x69, 0x64, 0x63, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x69, 0x64, 0x63,
	0x12, 0x20, 0x0a, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3f, 0x0a, 0x06, 0x75, 0x73, 0x61, 0x67, 0x65, 0x73, 0x18, 0x07,
	0x20, 0x03, 0x28, 0x0e, 0x32, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74,
	0x75, 0x64, 0x69, 0x6f, 0x2e, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x55, 0x73, 0x61, 0x67, 0x65, 0x52, 0x06, 0x75,
	0x73, 0x61, 0x67, 0x65, 0x73, 0x12, 0x62, 0x0a, 0x0c, 0x6e, 0x6f, 0x64, 0x65, 0x53, 0x65, 0x6c,
	0x65, 0x63, 0x74, 0x6f, 0x72, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3e, 0x2e, 0x61, 0x70,
	0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x70, 0x6c, 0x61, 0x74,
	0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x4b, 0x75, 0x62, 0x65, 0x72, 0x6e, 0x65, 0x74,
	0x65, 0x73, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x53, 0x65,
	0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0c, 0x6e, 0x6f, 0x64,
	0x65, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x1a, 0x3f, 0x0a, 0x11, 0x4e, 0x6f, 0x64,
	0x65, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xd0, 0x02, 0x0a, 0x11, 0x4e,
	0x6f, 0x64, 0x65, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c,
	0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x63, 0x70, 0x75, 0x4e,
	0x75, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x63, 0x70, 0x75, 0x4e, 0x75, 0x6d,
	0x12, 0x16, 0x0a, 0x06, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x06, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x67, 0x70, 0x75, 0x4e,
	0x75, 0x6d, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x67, 0x70, 0x75, 0x4e, 0x75, 0x6d,
	0x12, 0x1c, 0x0a, 0x09, 0x67, 0x70, 0x75, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x09, 0x67, 0x70, 0x75, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x12, 0x22,
	0x0a, 0x0c, 0x64, 0x69, 0x73, 0x6b, 0x43, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x64, 0x69, 0x73, 0x6b, 0x43, 0x61, 0x70, 0x61, 0x63, 0x69,
	0x74, 0x79, 0x12, 0x1e, 0x0a, 0x0a, 0x67, 0x70, 0x75, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x67, 0x70, 0x75, 0x50, 0x72, 0x6f, 0x64, 0x75,
	0x63, 0x74, 0x12, 0x39, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x22, 0xa5, 0x01,
	0x0a, 0x0d, 0x43, 0x75, 0x62, 0x65, 0x46, 0x53, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x7a,
	0x6f, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12,
	0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x64, 0x63, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x69, 0x64, 0x63, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61,
	0x79, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xa9, 0x03, 0x0a, 0x1e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x4b, 0x75, 0x62, 0x65, 0x72, 0x6e, 0x65, 0x74, 0x65, 0x73, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65,
	0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06,
	0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65,
	0x67, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x64, 0x63, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x69, 0x64, 0x63, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x69,
	0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b,
	0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3f,
	0x0a, 0x06, 0x75, 0x73, 0x61, 0x67, 0x65, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x27,
	0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x70,
	0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6c, 0x75, 0x73, 0x74,
	0x65, 0x72, 0x55, 0x73, 0x61, 0x67, 0x65, 0x52, 0x06, 0x75, 0x73, 0x61, 0x67, 0x65, 0x73, 0x12,
	0x6f, 0x0a, 0x0c, 0x6e, 0x6f, 0x64, 0x65, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x18,
	0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x4b, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73,
	0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4b, 0x75, 0x62, 0x65, 0x72, 0x6e, 0x65, 0x74,
	0x65, 0x73, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x52, 0x0c, 0x6e, 0x6f, 0x64, 0x65, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72,
	0x1a, 0x3f, 0x0a, 0x11, 0x4e, 0x6f, 0x64, 0x65, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38,
	0x01, 0x22, 0x34, 0x0a, 0x1e, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x4b, 0x75, 0x62, 0x65, 0x72,
	0x6e, 0x65, 0x74, 0x65, 0x73, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x6a, 0x0a, 0x1e, 0x4c, 0x69, 0x73, 0x74, 0x4b,
	0x75, 0x62, 0x65, 0x72, 0x6e, 0x65, 0x74, 0x65, 0x73, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x48, 0x0a, 0x08, 0x63, 0x6c, 0x75,
	0x73, 0x74, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x61, 0x70,
	0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x70, 0x6c, 0x61, 0x74,
	0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x4b, 0x75, 0x62, 0x65, 0x72, 0x6e, 0x65, 0x74,
	0x65, 0x73, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x52, 0x08, 0x63, 0x6c, 0x75, 0x73, 0x74,
	0x65, 0x72, 0x73, 0x22, 0x31, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x4b, 0x75, 0x62, 0x65, 0x72, 0x6e,
	0x65, 0x74, 0x65, 0x73, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x1f, 0x0a, 0x1d, 0x4c, 0x69, 0x73, 0x74, 0x4b, 0x75,
	0x62, 0x65, 0x72, 0x6e, 0x65, 0x74, 0x65, 0x73, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x73,
	0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0xe4, 0x01, 0x0a, 0x10, 0x4c, 0x69, 0x73, 0x74,
	0x4e, 0x6f, 0x64, 0x65, 0x73, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x12, 0x12, 0x0a, 0x04,
	0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65,
	0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x24, 0x0a, 0x0d,
	0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x61, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x61, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x12, 0x18,
	0x0a, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x1c, 0x0a, 0x09, 0x71, 0x75, 0x65, 0x75, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x71, 0x75, 0x65, 0x75, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x82,
	0x04, 0x0a, 0x04, 0x4e, 0x6f, 0x64, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74,
	0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65,
	0x72, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x67, 0x70, 0x75,
	0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x67,
	0x70, 0x75, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x73, 0x70, 0x65,
	0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x43, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x2b, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e,
	0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x6f, 0x64, 0x65,
	0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x6c, 0x61,
	0x62, 0x65, 0x6c, 0x73, 0x12, 0x52, 0x0a, 0x0b, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x61, 0x70, 0x69, 0x73,
	0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f,
	0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x2e, 0x41, 0x6e, 0x6e, 0x6f, 0x74,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0b, 0x61, 0x6e, 0x6e,
	0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a,
	0x0a, 0x08, 0x6e, 0x6f, 0x64, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x6e, 0x6f, 0x64, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x71, 0x75,
	0x65, 0x75, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x71,
	0x75, 0x65, 0x75, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x1a, 0x39, 0x0a, 0x0b, 0x4c, 0x61, 0x62, 0x65,
	0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x1a, 0x3e, 0x0a, 0x10, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x22, 0x5e, 0x0a, 0x0f, 0x4c, 0x69, 0x73, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x73,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x35, 0x0a, 0x05, 0x6e, 0x6f, 0x64, 0x65, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73,
	0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76,
	0x31, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x05, 0x6e, 0x6f, 0x64, 0x65, 0x73, 0x12, 0x14, 0x0a,
	0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x22, 0x4b, 0x0a, 0x11, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x4e, 0x6f, 0x64,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x70, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x03, 0x69, 0x70, 0x73, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f,
	0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x22, 0x26, 0x0a, 0x12, 0x52, 0x65, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x70, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x03, 0x69, 0x70, 0x73, 0x22, 0x1f, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x4e,
	0x6f, 0x64, 0x65, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69,
	0x63, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x9e, 0x03, 0x0a, 0x1b, 0x4e, 0x6f,
	0x64, 0x65, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x6e, 0x6f, 0x64,
	0x65, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x6e, 0x6f,
	0x64, 0x65, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x22, 0x0a, 0x0c, 0x67, 0x70, 0x75, 0x4e, 0x6f,
	0x64, 0x65, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x67,
	0x70, 0x75, 0x4e, 0x6f, 0x64, 0x65, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x5a, 0x0a, 0x06, 0x67,
	0x70, 0x75, 0x4d, 0x61, 0x70, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x42, 0x2e, 0x61, 0x70,
	0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x70, 0x6c, 0x61, 0x74,
	0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x73, 0x49, 0x6e, 0x66,
	0x6f, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x2e, 0x47, 0x70, 0x75, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52,
	0x06, 0x67, 0x70, 0x75, 0x4d, 0x61, 0x70, 0x12, 0x22, 0x0a, 0x0c, 0x72, 0x65, 0x61, 0x64, 0x79,
	0x4e, 0x6f, 0x64, 0x65, 0x4e, 0x75, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x72,
	0x65, 0x61, 0x64, 0x79, 0x4e, 0x6f, 0x64, 0x65, 0x4e, 0x75, 0x6d, 0x12, 0x28, 0x0a, 0x0f, 0x6e,
	0x6f, 0x74, 0x52, 0x65, 0x61, 0x64, 0x79, 0x4e, 0x6f, 0x64, 0x65, 0x4e, 0x75, 0x6d, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x6e, 0x6f, 0x74, 0x52, 0x65, 0x61, 0x64, 0x79, 0x4e, 0x6f,
	0x64, 0x65, 0x4e, 0x75, 0x6d, 0x12, 0x28, 0x0a, 0x0f, 0x61, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x65,
	0x64, 0x4e, 0x6f, 0x64, 0x65, 0x4e, 0x75, 0x6d, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f,
	0x61, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x4e, 0x6f, 0x64, 0x65, 0x4e, 0x75, 0x6d, 0x12,
	0x2e, 0x0a, 0x12, 0x6e, 0x6f, 0x74, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x4e, 0x6f,
	0x64, 0x65, 0x4e, 0x75, 0x6d, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x12, 0x6e, 0x6f, 0x74,
	0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x4e, 0x6f, 0x64, 0x65, 0x4e, 0x75, 0x6d, 0x1a,
	0x39, 0x0a, 0x0b, 0x47, 0x70, 0x75, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x97, 0x01, 0x0a, 0x13, 0x41,
	0x64, 0x64, 0x44, 0x72, 0x61, 0x67, 0x6f, 0x6e, 0x66, 0x6c, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x12, 0x16, 0x0a, 0x06,
	0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65,
	0x67, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x22, 0xa7, 0x01, 0x0a, 0x13, 0x53, 0x65, 0x74, 0x44, 0x72, 0x61, 0x67,
	0x6f, 0x6e, 0x66, 0x6c, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x18, 0x0a, 0x07,
	0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63,
	0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x12,
	0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f,
	0x6e, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x22, 0x28,
	0x0a, 0x16, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x44, 0x72, 0x61, 0x67, 0x6f, 0x6e, 0x66, 0x6c,
	0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0x7a, 0x0a, 0x14, 0x4c, 0x69, 0x73, 0x74,
	0x44, 0x72, 0x61, 0x67, 0x6f, 0x6e, 0x66, 0x6c, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x18, 0x0a, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x6f, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65,
	0x53, 0x69, 0x7a, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65,
	0x53, 0x69, 0x7a, 0x65, 0x22, 0x75, 0x0a, 0x15, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x72, 0x61, 0x67,
	0x6f, 0x6e, 0x66, 0x6c, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x46, 0x0a,
	0x0b, 0x64, 0x72, 0x61, 0x67, 0x6f, 0x6e, 0x66, 0x6c, 0x69, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64,
	0x69, 0x6f, 0x2e, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x44,
	0x72, 0x61, 0x67, 0x6f, 0x6e, 0x66, 0x6c, 0x79, 0x52, 0x0b, 0x64, 0x72, 0x61, 0x67, 0x6f, 0x6e,
	0x66, 0x6c, 0x69, 0x65, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x22, 0xf7, 0x01, 0x0a, 0x09,
	0x44, 0x72, 0x61, 0x67, 0x6f, 0x6e, 0x66, 0x6c, 0x79, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6c, 0x75,
	0x73, 0x74, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6c, 0x75, 0x73,
	0x74, 0x65, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x7a,
	0x6f, 0x6e, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12,
	0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x74, 0x0a, 0x28, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x69, 0x73,
	0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x64, 0x43, 0x61, 0x63, 0x68, 0x65, 0x52, 0x65, 0x67,
	0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x70,
	0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12,
	0x1a, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x22, 0xc0, 0x01, 0x0a, 0x29,
	0x4c, 0x69, 0x73, 0x74, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x64, 0x43,
	0x61, 0x63, 0x68, 0x65, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7d, 0x0a, 0x1d, 0x64, 0x69, 0x73,
	0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x64, 0x43, 0x61, 0x63, 0x68, 0x65, 0x52, 0x65, 0x67,
	0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x37, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f,
	0x2e, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x69, 0x73,
	0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x64, 0x43, 0x61, 0x63, 0x68, 0x65, 0x52, 0x65, 0x67,
	0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x1d, 0x64, 0x69, 0x73, 0x74, 0x72,
	0x69, 0x62, 0x75, 0x74, 0x65, 0x64, 0x43, 0x61, 0x63, 0x68, 0x65, 0x52, 0x65, 0x67, 0x69, 0x73,
	0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x22, 0xad,
	0x03, 0x0a, 0x1c, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x64, 0x43, 0x61,
	0x63, 0x68, 0x65, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x20, 0x0a, 0x0b, 0x63, 0x61, 0x63, 0x68, 0x65, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x61, 0x63, 0x68, 0x65, 0x52, 0x65, 0x67, 0x69, 0x6f,
	0x6e, 0x12, 0x28, 0x0a, 0x0f, 0x63, 0x61, 0x63, 0x68, 0x65, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x63, 0x61, 0x63, 0x68,
	0x65, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x63,
	0x61, 0x63, 0x68, 0x65, 0x5a, 0x6f, 0x6e, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x63, 0x61, 0x63, 0x68, 0x65, 0x5a, 0x6f, 0x6e, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x63, 0x61, 0x63,
	0x68, 0x65, 0x5a, 0x6f, 0x6e, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x63, 0x61, 0x63, 0x68, 0x65, 0x5a, 0x6f, 0x6e, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x18, 0x0a, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x26, 0x0a, 0x0e, 0x63, 0x61, 0x63,
	0x68, 0x65, 0x46, 0x72, 0x61, 0x6d, 0x65, 0x77, 0x6f, 0x72, 0x6b, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0e, 0x63, 0x61, 0x63, 0x68, 0x65, 0x46, 0x72, 0x61, 0x6d, 0x65, 0x77, 0x6f, 0x72,
	0x6b, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x12, 0x20, 0x0a, 0x0b, 0x63,
	0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x55, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3d, 0x2e,
	0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x70, 0x6c,
	0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69,
	0x62, 0x75, 0x74, 0x65, 0x64, 0x43, 0x61, 0x63, 0x68, 0x65, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x22, 0x54,
	0x0a, 0x22, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x64, 0x43, 0x61, 0x63,
	0x68, 0x65, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x63, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79,
	0x12, 0x12, 0x0a, 0x04, 0x75, 0x73, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04,
	0x75, 0x73, 0x65, 0x64, 0x22, 0xb9, 0x02, 0x0a, 0x29, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44,
	0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x64, 0x43, 0x61, 0x63, 0x68, 0x65, 0x52,
	0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x61, 0x63, 0x68, 0x65, 0x52, 0x65, 0x67, 0x69, 0x6f,
	0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x61, 0x63, 0x68, 0x65, 0x52, 0x65,
	0x67, 0x69, 0x6f, 0x6e, 0x12, 0x28, 0x0a, 0x0f, 0x63, 0x61, 0x63, 0x68, 0x65, 0x52, 0x65, 0x67,
	0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x63,
	0x61, 0x63, 0x68, 0x65, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1c,
	0x0a, 0x09, 0x63, 0x61, 0x63, 0x68, 0x65, 0x5a, 0x6f, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x63, 0x61, 0x63, 0x68, 0x65, 0x5a, 0x6f, 0x6e, 0x65, 0x12, 0x24, 0x0a, 0x0d,
	0x63, 0x61, 0x63, 0x68, 0x65, 0x5a, 0x6f, 0x6e, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x61, 0x63, 0x68, 0x65, 0x5a, 0x6f, 0x6e, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x63, 0x61, 0x63, 0x68, 0x65, 0x46, 0x72, 0x61, 0x6d, 0x65,
	0x77, 0x6f, 0x72, 0x6b, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x63, 0x61, 0x63, 0x68,
	0x65, 0x46, 0x72, 0x61, 0x6d, 0x65, 0x77, 0x6f, 0x72, 0x6b, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6c,
	0x75, 0x73, 0x74, 0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6c, 0x75,
	0x73, 0x74, 0x65, 0x72, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x4e,
	0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6c, 0x75, 0x73, 0x74,
	0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65,
	0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64,
	0x22, 0x2c, 0x0a, 0x2a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69,
	0x62, 0x75, 0x74, 0x65, 0x64, 0x43, 0x61, 0x63, 0x68, 0x65, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x3b,
	0x0a, 0x29, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75,
	0x74, 0x65, 0x64, 0x43, 0x61, 0x63, 0x68, 0x65, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0x2c, 0x0a, 0x2a, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x64,
	0x43, 0x61, 0x63, 0x68, 0x65, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0xc9, 0x02, 0x0a, 0x29, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x64, 0x43,
	0x61, 0x63, 0x68, 0x65, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x61, 0x63, 0x68, 0x65,
	0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x61,
	0x63, 0x68, 0x65, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x28, 0x0a, 0x0f, 0x63, 0x61, 0x63,
	0x68, 0x65, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0f, 0x63, 0x61, 0x63, 0x68, 0x65, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x61, 0x63, 0x68, 0x65, 0x5a, 0x6f, 0x6e, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x61, 0x63, 0x68, 0x65, 0x5a, 0x6f, 0x6e,
	0x65, 0x12, 0x24, 0x0a, 0x0d, 0x63, 0x61, 0x63, 0x68, 0x65, 0x5a, 0x6f, 0x6e, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x61, 0x63, 0x68, 0x65, 0x5a,
	0x6f, 0x6e, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x63, 0x61, 0x63, 0x68, 0x65,
	0x46, 0x72, 0x61, 0x6d, 0x65, 0x77, 0x6f, 0x72, 0x6b, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0e, 0x63, 0x61, 0x63, 0x68, 0x65, 0x46, 0x72, 0x61, 0x6d, 0x65, 0x77, 0x6f, 0x72, 0x6b, 0x12,
	0x18, 0x0a, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x6c, 0x75,
	0x73, 0x74, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x65,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x65, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x64, 0x22, 0x2c, 0x0a, 0x2a, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44,
	0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x64, 0x43, 0x61, 0x63, 0x68, 0x65, 0x52,
	0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x30, 0x0a, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x69, 0x73, 0x74, 0x72,
	0x69, 0x62, 0x75, 0x74, 0x65, 0x64, 0x43, 0x61, 0x63, 0x68, 0x65, 0x52, 0x65, 0x67, 0x69, 0x73,
	0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0xc3, 0x01, 0x0a, 0x2f, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x69,
	0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x64, 0x43, 0x61, 0x63, 0x68, 0x65, 0x52, 0x65,
	0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x8f, 0x01, 0x0a, 0x23, 0x64, 0x69,
	0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x64, 0x43, 0x61, 0x63, 0x68, 0x65, 0x52, 0x65,
	0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61,
	0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d,
	0x2e, 0x76, 0x31, 0x2e, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x64, 0x43,
	0x61, 0x63, 0x68, 0x65, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x52, 0x23, 0x64, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75,
	0x74, 0x65, 0x64, 0x43, 0x61, 0x63, 0x68, 0x65, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x70, 0x0a, 0x22, 0x44,
	0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x64, 0x43, 0x61, 0x63, 0x68, 0x65, 0x52,
	0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x67, 0x69, 0x6f,
	0x6e, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x61, 0x63, 0x68, 0x65, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x61, 0x63, 0x68, 0x65, 0x52, 0x65, 0x67,
	0x69, 0x6f, 0x6e, 0x12, 0x28, 0x0a, 0x0f, 0x63, 0x61, 0x63, 0x68, 0x65, 0x52, 0x65, 0x67, 0x69,
	0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x63, 0x61,
	0x63, 0x68, 0x65, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x50, 0x0a,
	0x2c, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x64,
	0x43, 0x61, 0x63, 0x68, 0x65, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5a, 0x6f, 0x6e, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x20, 0x0a,
	0x0b, 0x63, 0x61, 0x63, 0x68, 0x65, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x63, 0x61, 0x63, 0x68, 0x65, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x22,
	0xbb, 0x01, 0x0a, 0x2d, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75,
	0x74, 0x65, 0x64, 0x43, 0x61, 0x63, 0x68, 0x65, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5a, 0x6f, 0x6e, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x89, 0x01, 0x0a, 0x21, 0x64, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65,
	0x64, 0x43, 0x61, 0x63, 0x68, 0x65, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5a, 0x6f, 0x6e, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3b, 0x2e,
	0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x70, 0x6c,
	0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69,
	0x62, 0x75, 0x74, 0x65, 0x64, 0x43, 0x61, 0x63, 0x68, 0x65, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5a, 0x6f, 0x6e, 0x65, 0x52, 0x21, 0x64, 0x69, 0x73, 0x74,
	0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x64, 0x43, 0x61, 0x63, 0x68, 0x65, 0x52, 0x65, 0x67, 0x69,
	0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5a, 0x6f, 0x6e, 0x65, 0x73, 0x22, 0x66, 0x0a,
	0x20, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x64, 0x43, 0x61, 0x63, 0x68,
	0x65, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5a, 0x6f, 0x6e,
	0x65, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x61, 0x63, 0x68, 0x65, 0x5a, 0x6f, 0x6e, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x61, 0x63, 0x68, 0x65, 0x5a, 0x6f, 0x6e, 0x65, 0x12,
	0x24, 0x0a, 0x0d, 0x63, 0x61, 0x63, 0x68, 0x65, 0x5a, 0x6f, 0x6e, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x61, 0x63, 0x68, 0x65, 0x5a, 0x6f, 0x6e,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x71, 0x0a, 0x2f, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x69, 0x73,
	0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x64, 0x43, 0x61, 0x63, 0x68, 0x65, 0x52, 0x65, 0x67,
	0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x61, 0x63, 0x68,
	0x65, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63,
	0x61, 0x63, 0x68, 0x65, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x61,
	0x63, 0x68, 0x65, 0x5a, 0x6f, 0x6e, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63,
	0x61, 0x63, 0x68, 0x65, 0x5a, 0x6f, 0x6e, 0x65, 0x22, 0xc7, 0x01, 0x0a, 0x30, 0x4c, 0x69, 0x73,
	0x74, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x64, 0x43, 0x61, 0x63, 0x68,
	0x65, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6c, 0x75,
	0x73, 0x74, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x92, 0x01,
	0x0a, 0x24, 0x64, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x64, 0x43, 0x61, 0x63,
	0x68, 0x65, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6c,
	0x75, 0x73, 0x74, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3e, 0x2e, 0x61,
	0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x70, 0x6c, 0x61,
	0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62,
	0x75, 0x74, 0x65, 0x64, 0x43, 0x61, 0x63, 0x68, 0x65, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x52, 0x24, 0x64, 0x69,
	0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x64, 0x43, 0x61, 0x63, 0x68, 0x65, 0x52, 0x65,
	0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65,
	0x72, 0x73, 0x22, 0x61, 0x0a, 0x23, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65,
	0x64, 0x43, 0x61, 0x63, 0x68, 0x65, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6c, 0x75,
	0x73, 0x74, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6c, 0x75, 0x73,
	0x74, 0x65, 0x72, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65,
	0x72, 0x4e, 0x61, 0x6d, 0x65, 0x2a, 0x2b, 0x0a, 0x0c, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72,
	0x55, 0x73, 0x61, 0x67, 0x65, 0x12, 0x0c, 0x0a, 0x08, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x69, 0x6e,
	0x67, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x49, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65,
	0x10, 0x01, 0x32, 0xc1, 0x28, 0x0a, 0x0f, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0xdc, 0x01, 0x0a, 0x1c, 0x41, 0x64, 0x64, 0x4f, 0x72,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4b, 0x75, 0x62, 0x65, 0x72, 0x6e, 0x65, 0x74, 0x65, 0x73,
	0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x12, 0x39, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61,
	0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d,
	0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4b, 0x75, 0x62, 0x65, 0x72, 0x6e,
	0x65, 0x74, 0x65, 0x73, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x2c, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64,
	0x69, 0x6f, 0x2e, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x4b,
	0x75, 0x62, 0x65, 0x72, 0x6e, 0x65, 0x74, 0x65, 0x73, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72,
	0x22, 0x53, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x4d, 0x3a, 0x01, 0x2a, 0x5a, 0x20, 0x3a, 0x01, 0x2a,
	0x22, 0x1b, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x6b, 0x38, 0x73, 0x2d, 0x63,
	0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x1a, 0x26, 0x2f,
	0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d,
	0x2f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x2f, 0x6b, 0x38, 0x73, 0x2d, 0x63, 0x6c,
	0x75, 0x73, 0x74, 0x65, 0x72, 0x12, 0xb9, 0x01, 0x0a, 0x17, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65,
	0x4b, 0x75, 0x62, 0x65, 0x72, 0x6e, 0x65, 0x74, 0x65, 0x73, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65,
	0x72, 0x12, 0x39, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69,
	0x6f, 0x2e, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65,
	0x6d, 0x6f, 0x76, 0x65, 0x4b, 0x75, 0x62, 0x65, 0x72, 0x6e, 0x65, 0x74, 0x65, 0x73, 0x43, 0x6c,
	0x75, 0x73, 0x74, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2c, 0x2e, 0x61,
	0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x70, 0x6c, 0x61,
	0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x4b, 0x75, 0x62, 0x65, 0x72, 0x6e, 0x65,
	0x74, 0x65, 0x73, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x22, 0x35, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x2f, 0x2a, 0x2d, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x6c, 0x61,
	0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x2f, 0x6b,
	0x38, 0x73, 0x2d, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65,
	0x7d, 0x12, 0xbd, 0x01, 0x0a, 0x16, 0x4c, 0x69, 0x73, 0x74, 0x4b, 0x75, 0x62, 0x65, 0x72, 0x6e,
	0x65, 0x74, 0x65, 0x73, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x73, 0x12, 0x38, 0x2e, 0x61,
	0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x70, 0x6c, 0x61,
	0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4b, 0x75, 0x62,
	0x65, 0x72, 0x6e, 0x65, 0x74, 0x65, 0x73, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x73, 0x4f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x1a, 0x39, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69,
	0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4b, 0x75, 0x62, 0x65, 0x72, 0x6e, 0x65, 0x74, 0x65,
	0x73, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x2e, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x28, 0x12, 0x26, 0x2f, 0x61, 0x70, 0x69, 0x73,
	0x2f, 0x76, 0x31, 0x2f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x73, 0x65, 0x74,
	0x74, 0x69, 0x6e, 0x67, 0x73, 0x2f, 0x6b, 0x38, 0x73, 0x2d, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65,
	0x72, 0x12, 0xb3, 0x01, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x4b, 0x75, 0x62, 0x65, 0x72, 0x6e, 0x65,
	0x74, 0x65, 0x73, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x12, 0x36, 0x2e, 0x61, 0x70, 0x69,
	0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x70, 0x6c, 0x61, 0x74, 0x66,
	0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4b, 0x75, 0x62, 0x65, 0x72, 0x6e,
	0x65, 0x74, 0x65, 0x73, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x2c, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64,
	0x69, 0x6f, 0x2e, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x4b,
	0x75, 0x62, 0x65, 0x72, 0x6e, 0x65, 0x74, 0x65, 0x73, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72,
	0x22, 0x35, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2f, 0x12, 0x2d, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f,
	0x76, 0x31, 0x2f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x73, 0x65, 0x74, 0x74,
	0x69, 0x6e, 0x67, 0x73, 0x2f, 0x6b, 0x38, 0x73, 0x2d, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72,
	0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x12, 0xf1, 0x01, 0x0a, 0x1c, 0x41, 0x64, 0x64, 0x4f,
	0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x53, 0x70, 0x65, 0x63, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2c, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e,
	0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72,
	0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0x2c, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69,
	0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e,
	0x76, 0x31, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x22, 0x75, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x6f, 0x3a, 0x01, 0x2a, 0x5a,
	0x3a, 0x3a, 0x01, 0x2a, 0x1a, 0x35, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x70,
	0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73,
	0x2f, 0x6e, 0x6f, 0x64, 0x65, 0x2d, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x1a, 0x2e, 0x2f, 0x61, 0x70,
	0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x73,
	0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x2f, 0x6e, 0x6f, 0x64, 0x65, 0x2d, 0x73, 0x70, 0x65,
	0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0xab, 0x01, 0x0a, 0x17,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x39, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61,
	0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d,
	0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x53, 0x70,
	0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x3d, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x37, 0x2a, 0x35, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x6c, 0x61,
	0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x2f, 0x6e,
	0x6f, 0x64, 0x65, 0x2d, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x12, 0xc2, 0x01, 0x0a, 0x16, 0x4c, 0x69,
	0x73, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x12, 0x37, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74,
	0x75, 0x64, 0x69, 0x6f, 0x2e, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x1a, 0x37, 0x2e,
	0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x70, 0x6c,
	0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4e, 0x6f,
	0x64, 0x65, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x36, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x30, 0x12, 0x2e,
	0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72,
	0x6d, 0x2f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x2f, 0x6e, 0x6f, 0x64, 0x65, 0x2d,
	0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0xdb,
	0x01, 0x0a, 0x18, 0x41, 0x64, 0x64, 0x4f, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x75,
	0x62, 0x65, 0x46, 0x53, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x12, 0x3a, 0x2e, 0x61, 0x70,
	0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x70, 0x6c, 0x61, 0x74,
	0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x64, 0x4f, 0x72, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x43, 0x75, 0x62, 0x65, 0x46, 0x53, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22,
	0x6b, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x65, 0x3a, 0x01, 0x2a, 0x5a, 0x35, 0x3a, 0x01, 0x2a, 0x22,
	0x30, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f,
	0x72, 0x6d, 0x2f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x2f, 0x63, 0x75, 0x62, 0x65,
	0x66, 0x73, 0x2d, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65,
	0x7d, 0x1a, 0x29, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x6c, 0x61, 0x74,
	0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x2f, 0x63, 0x75,
	0x62, 0x65, 0x66, 0x73, 0x2d, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x12, 0x9e, 0x01, 0x0a,
	0x13, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x75, 0x62, 0x65, 0x46, 0x53, 0x43, 0x6c, 0x75,
	0x73, 0x74, 0x65, 0x72, 0x12, 0x35, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74,
	0x75, 0x64, 0x69, 0x6f, 0x2e, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31,
	0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x75, 0x62, 0x65, 0x46, 0x53, 0x43, 0x6c, 0x75,
	0x73, 0x74, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x22, 0x38, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x32, 0x2a, 0x30, 0x2f, 0x61, 0x70,
	0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x73,
	0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x2f, 0x63, 0x75, 0x62, 0x65, 0x66, 0x73, 0x2d, 0x63,
	0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x12, 0xba, 0x01,
	0x0a, 0x19, 0x43, 0x75, 0x62, 0x65, 0x46, 0x53, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x38, 0x2e, 0x61, 0x70,
	0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x70, 0x6c, 0x61, 0x74,
	0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x75, 0x62, 0x65, 0x46, 0x53, 0x43, 0x6c,
	0x75, 0x73, 0x74, 0x65, 0x72, 0x53, 0x65, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x4b, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x45, 0x3a, 0x01, 0x2a, 0x22, 0x40, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f,
	0x76, 0x31, 0x2f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x73, 0x65, 0x74, 0x74,
	0x69, 0x6e, 0x67, 0x73, 0x2f, 0x63, 0x75, 0x62, 0x65, 0x66, 0x73, 0x2d, 0x63, 0x6c, 0x75, 0x73,
	0x74, 0x65, 0x72, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x2f, 0x7b, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x7d, 0x12, 0xcc, 0x01, 0x0a, 0x12, 0x53,
	0x65, 0x74, 0x43, 0x75, 0x62, 0x65, 0x46, 0x53, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72,
	0x64, 0x12, 0x34, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69,
	0x6f, 0x2e, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65,
	0x74, 0x43, 0x75, 0x62, 0x65, 0x46, 0x53, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22,
	0x68, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x62, 0x3a, 0x01, 0x2a, 0x5a, 0x30, 0x3a, 0x01, 0x2a, 0x22,
	0x2b, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f,
	0x72, 0x6d, 0x2f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x2f, 0x63, 0x75, 0x62, 0x65,
	0x66, 0x73, 0x2d, 0x64, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x1a, 0x2b, 0x2f, 0x61,
	0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f,
	0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x2f, 0x63, 0x75, 0x62, 0x65, 0x66, 0x73, 0x2d,
	0x64, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x12, 0x8e, 0x01, 0x0a, 0x09, 0x4c, 0x69,
	0x73, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x73, 0x12, 0x2b, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61,
	0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d,
	0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x73, 0x4f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x1a, 0x2a, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74,
	0x75, 0x64, 0x69, 0x6f, 0x2e, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x22, 0x28, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x22, 0x12, 0x20, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f,
	0x76, 0x31, 0x2f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x73, 0x65, 0x74, 0x74,
	0x69, 0x6e, 0x67, 0x73, 0x2f, 0x6e, 0x6f, 0x64, 0x65, 0x73, 0x12, 0xbe, 0x01, 0x0a, 0x16, 0x47,
	0x65, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x53, 0x74, 0x61, 0x74, 0x69,
	0x73, 0x74, 0x69, 0x63, 0x73, 0x12, 0x38, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73,
	0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x65, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x53, 0x74,
	0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x36, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e,
	0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x6f, 0x64, 0x65,
	0x73, 0x49, 0x6e, 0x66, 0x6f, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x32, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2c, 0x12,
	0x2a, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f,
	0x72, 0x6d, 0x2f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x2f, 0x6e, 0x6f, 0x64, 0x65, 0x73,
	0x2f, 0x73, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x12, 0x86, 0x01, 0x0a, 0x0a,
	0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x4e, 0x6f, 0x64, 0x65, 0x12, 0x2c, 0x2e, 0x61, 0x70, 0x69,
	0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x70, 0x6c, 0x61, 0x74, 0x66,
	0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x4e, 0x6f, 0x64,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79,
	0x22, 0x32, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2c, 0x3a, 0x01, 0x2a, 0x22, 0x27, 0x2f, 0x61, 0x70,
	0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x73,
	0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x2f, 0x6e, 0x6f, 0x64, 0x65, 0x73, 0x2f, 0x61, 0x73,
	0x73, 0x69, 0x67, 0x6e, 0x12, 0x89, 0x01, 0x0a, 0x0b, 0x52, 0x65, 0x63, 0x79, 0x63, 0x6c, 0x65,
	0x4e, 0x6f, 0x64, 0x65, 0x12, 0x2d, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74,
	0x75, 0x64, 0x69, 0x6f, 0x2e, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31,
	0x2e, 0x52, 0x65, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x33, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x2d, 0x3a, 0x01, 0x2a, 0x22, 0x28, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31,
	0x2f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e,
	0x67, 0x73, 0x2f, 0x6e, 0x6f, 0x64, 0x65, 0x73, 0x2f, 0x72, 0x65, 0x63, 0x79, 0x63, 0x6c, 0x65,
	0x12, 0x87, 0x01, 0x0a, 0x0c, 0x41, 0x64, 0x64, 0x44, 0x72, 0x61, 0x67, 0x6f, 0x6e, 0x66, 0x6c,
	0x79, 0x12, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69,
	0x6f, 0x2e, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64,
	0x64, 0x44, 0x72, 0x61, 0x67, 0x6f, 0x6e, 0x66, 0x6c, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x2f, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x29, 0x3a, 0x01, 0x2a, 0x22, 0x24, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x70,
	0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73,
	0x2f, 0x64, 0x72, 0x61, 0x67, 0x6f, 0x6e, 0x66, 0x6c, 0x79, 0x12, 0x87, 0x01, 0x0a, 0x0c, 0x53,
	0x65, 0x74, 0x44, 0x72, 0x61, 0x67, 0x6f, 0x6e, 0x66, 0x6c, 0x79, 0x12, 0x2e, 0x2e, 0x61, 0x70,
	0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x70, 0x6c, 0x61, 0x74,
	0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x74, 0x44, 0x72, 0x61, 0x67, 0x6f,
	0x6e, 0x66, 0x6c, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x22, 0x2f, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x29, 0x3a, 0x01, 0x2a, 0x1a, 0x24,
	0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72,
	0x6d, 0x2f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x2f, 0x64, 0x72, 0x61, 0x67, 0x6f,
	0x6e, 0x66, 0x6c, 0x79, 0x12, 0x8a, 0x01, 0x0a, 0x0f, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x44,
	0x72, 0x61, 0x67, 0x6f, 0x6e, 0x66, 0x6c, 0x79, 0x12, 0x31, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e,
	0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72,
	0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x44, 0x72, 0x61, 0x67, 0x6f,
	0x6e, 0x66, 0x6c, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x22, 0x2c, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x26, 0x2a, 0x24, 0x2f, 0x61, 0x70,
	0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x73,
	0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x2f, 0x64, 0x72, 0x61, 0x67, 0x6f, 0x6e, 0x66, 0x6c,
	0x79, 0x12, 0xa0, 0x01, 0x0a, 0x0d, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x72, 0x61, 0x67, 0x6f, 0x6e,
	0x66, 0x6c, 0x79, 0x12, 0x2f, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75,
	0x64, 0x69, 0x6f, 0x2e, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e,
	0x4c, 0x69, 0x73, 0x74, 0x44, 0x72, 0x61, 0x67, 0x6f, 0x6e, 0x66, 0x6c, 0x79, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x30, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74,
	0x75, 0x64, 0x69, 0x6f, 0x2e, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x72, 0x61, 0x67, 0x6f, 0x6e, 0x66, 0x6c, 0x79, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x2c, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x26, 0x12, 0x24,
	0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72,
	0x6d, 0x2f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x2f, 0x64, 0x72, 0x61, 0x67, 0x6f,
	0x6e, 0x66, 0x6c, 0x79, 0x12, 0xf0, 0x01, 0x0a, 0x21, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x69, 0x73,
	0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x64, 0x43, 0x61, 0x63, 0x68, 0x65, 0x52, 0x65, 0x67,
	0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x43, 0x2e, 0x61, 0x70, 0x69,
	0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x70, 0x6c, 0x61, 0x74, 0x66,
	0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x69, 0x73, 0x74, 0x72,
	0x69, 0x62, 0x75, 0x74, 0x65, 0x64, 0x43, 0x61, 0x63, 0x68, 0x65, 0x52, 0x65, 0x67, 0x69, 0x73,
	0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x44, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e,
	0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74,
	0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x64, 0x43, 0x61, 0x63, 0x68, 0x65,
	0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x40, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x3a, 0x12, 0x38, 0x2f,
	0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d,
	0x2f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x2f, 0x64, 0x69, 0x73, 0x74, 0x72, 0x69,
	0x62, 0x75, 0x74, 0x65, 0x64, 0x63, 0x61, 0x63, 0x68, 0x65, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0xf5, 0x01, 0x0a, 0x22, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x64, 0x43, 0x61, 0x63,
	0x68, 0x65, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x44,
	0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x70,
	0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x64, 0x43, 0x61, 0x63, 0x68,
	0x65, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x45, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74,
	0x75, 0x64, 0x69, 0x6f, 0x2e, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74,
	0x65, 0x64, 0x43, 0x61, 0x63, 0x68, 0x65, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x42, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x3c, 0x3a, 0x01, 0x2a, 0x22, 0x37, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31,
	0x2f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e,
	0x67, 0x73, 0x2f, 0x64, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x64, 0x63, 0x61,
	0x63, 0x68, 0x65, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0xf7, 0x01, 0x0a, 0x22, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69,
	0x62, 0x75, 0x74, 0x65, 0x64, 0x43, 0x61, 0x63, 0x68, 0x65, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x44, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69,
	0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e,
	0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62,
	0x75, 0x74, 0x65, 0x64, 0x43, 0x61, 0x63, 0x68, 0x65, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x45, 0x2e, 0x61,
	0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x70, 0x6c, 0x61,
	0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x44,
	0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x64, 0x43, 0x61, 0x63, 0x68, 0x65, 0x52,
	0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x44, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x3e, 0x2a, 0x3c, 0x2f, 0x61, 0x70,
	0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x73,
	0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x2f, 0x64, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75,
	0x74, 0x65, 0x64, 0x63, 0x61, 0x63, 0x68, 0x65, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x12, 0xfa, 0x01, 0x0a, 0x22, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x64, 0x43,
	0x61, 0x63, 0x68, 0x65, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x44, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f,
	0x2e, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x64, 0x43, 0x61,
	0x63, 0x68, 0x65, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x45, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69,
	0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e,
	0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62,
	0x75, 0x74, 0x65, 0x64, 0x43, 0x61, 0x63, 0x68, 0x65, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x47, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x41, 0x3a, 0x01, 0x2a, 0x1a, 0x3c, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f,
	0x76, 0x31, 0x2f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x73, 0x65, 0x74, 0x74,
	0x69, 0x6e, 0x67, 0x73, 0x2f, 0x64, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x64,
	0x63, 0x61, 0x63, 0x68, 0x65, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x12, 0x89, 0x02, 0x0a, 0x27, 0x4c, 0x69, 0x73, 0x74, 0x44,
	0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x64, 0x43, 0x61, 0x63, 0x68, 0x65, 0x52,
	0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x67, 0x69, 0x6f,
	0x6e, 0x73, 0x12, 0x49, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64,
	0x69, 0x6f, 0x2e, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x64, 0x43, 0x61,
	0x63, 0x68, 0x65, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x67, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x4a, 0x2e,
	0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x70, 0x6c,
	0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x69,
	0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x64, 0x43, 0x61, 0x63, 0x68, 0x65, 0x52, 0x65,
	0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x47, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x41, 0x12, 0x3f, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x6c, 0x61, 0x74,
	0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x2f, 0x64, 0x69,
	0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x64, 0x63, 0x61, 0x63, 0x68, 0x65, 0x72, 0x65,
	0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x72, 0x65, 0x67, 0x69, 0x6f,
	0x6e, 0x73, 0x12, 0x81, 0x02, 0x0a, 0x25, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x69, 0x73, 0x74, 0x72,
	0x69, 0x62, 0x75, 0x74, 0x65, 0x64, 0x43, 0x61, 0x63, 0x68, 0x65, 0x52, 0x65, 0x67, 0x69, 0x73,
	0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5a, 0x6f, 0x6e, 0x65, 0x73, 0x12, 0x47, 0x2e, 0x61,
	0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x70, 0x6c, 0x61,
	0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x69, 0x73,
	0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x64, 0x43, 0x61, 0x63, 0x68, 0x65, 0x52, 0x65, 0x67,
	0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5a, 0x6f, 0x6e, 0x65, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x48, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73,
	0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76,
	0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65,
	0x64, 0x43, 0x61, 0x63, 0x68, 0x65, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5a, 0x6f, 0x6e, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x45, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x3f, 0x12, 0x3d, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76,
	0x31, 0x2f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x73, 0x65, 0x74, 0x74, 0x69,
	0x6e, 0x67, 0x73, 0x2f, 0x64, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x64, 0x63,
	0x61, 0x63, 0x68, 0x65, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x12, 0x8d, 0x02, 0x0a, 0x28, 0x4c, 0x69, 0x73, 0x74, 0x44,
	0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x64, 0x43, 0x61, 0x63, 0x68, 0x65, 0x52,
	0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6c, 0x75, 0x73, 0x74,
	0x65, 0x72, 0x73, 0x12, 0x4a, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75,
	0x64, 0x69, 0x6f, 0x2e, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e,
	0x4c, 0x69, 0x73, 0x74, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x64, 0x43,
	0x61, 0x63, 0x68, 0x65, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x4b, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e,
	0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74,
	0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x64, 0x43, 0x61, 0x63, 0x68, 0x65,
	0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6c, 0x75, 0x73,
	0x74, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x48, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x42, 0x12, 0x40, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x70,
	0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73,
	0x2f, 0x64, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x64, 0x63, 0x61, 0x63, 0x68,
	0x65, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x63, 0x6c,
	0x75, 0x73, 0x74, 0x65, 0x72, 0x73, 0x42, 0x4b, 0x5a, 0x49, 0x67, 0x69, 0x74, 0x2e, 0x6c, 0x69,
	0x61, 0x6e, 0x6a, 0x69, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6e,
	0x61, 0x74, 0x69, 0x76, 0x65, 0x2f, 0x6b, 0x69, 0x63, 0x2f, 0x6b, 0x69, 0x63, 0x2d, 0x70, 0x6c,
	0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x61, 0x69, 0x73, 0x74,
	0x75, 0x64, 0x69, 0x6f, 0x2f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x76, 0x31,
	0x3b, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_aistudio_platform_v1_platform_proto_rawDescOnce sync.Once
	file_aistudio_platform_v1_platform_proto_rawDescData = file_aistudio_platform_v1_platform_proto_rawDesc
)

func file_aistudio_platform_v1_platform_proto_rawDescGZIP() []byte {
	file_aistudio_platform_v1_platform_proto_rawDescOnce.Do(func() {
		file_aistudio_platform_v1_platform_proto_rawDescData = protoimpl.X.CompressGZIP(file_aistudio_platform_v1_platform_proto_rawDescData)
	})
	return file_aistudio_platform_v1_platform_proto_rawDescData
}

var file_aistudio_platform_v1_platform_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_aistudio_platform_v1_platform_proto_msgTypes = make([]protoimpl.MessageInfo, 52)
var file_aistudio_platform_v1_platform_proto_goTypes = []any{
	(ClusterUsage)(0),                                        // 0: apis.aistudio.platform.v1.ClusterUsage
	(*AddOrUpdateCubeFSClusterRequest)(nil),                  // 1: apis.aistudio.platform.v1.AddOrUpdateCubeFSClusterRequest
	(*DeleteCubeFSClusterRequest)(nil),                       // 2: apis.aistudio.platform.v1.DeleteCubeFSClusterRequest
	(*CubeFSClusterSetStatusRequest)(nil),                    // 3: apis.aistudio.platform.v1.CubeFSClusterSetStatusRequest
	(*SetCubeFSDashboardRequest)(nil),                        // 4: apis.aistudio.platform.v1.SetCubeFSDashboardRequest
	(*ListNodeSpecificationOptions)(nil),                     // 5: apis.aistudio.platform.v1.ListNodeSpecificationOptions
	(*ListNodeSpecificationsResult)(nil),                     // 6: apis.aistudio.platform.v1.ListNodeSpecificationsResult
	(*DeleteNodeSpecificationRequest)(nil),                   // 7: apis.aistudio.platform.v1.DeleteNodeSpecificationRequest
	(*KubernetesCluster)(nil),                                // 8: apis.aistudio.platform.v1.KubernetesCluster
	(*NodeSpecification)(nil),                                // 9: apis.aistudio.platform.v1.NodeSpecification
	(*CubeFSCluster)(nil),                                    // 10: apis.aistudio.platform.v1.CubeFSCluster
	(*CreateKubernetesClusterRequest)(nil),                   // 11: apis.aistudio.platform.v1.CreateKubernetesClusterRequest
	(*RemoveKubernetesClusterRequest)(nil),                   // 12: apis.aistudio.platform.v1.RemoveKubernetesClusterRequest
	(*ListKubernetesClustersResponse)(nil),                   // 13: apis.aistudio.platform.v1.ListKubernetesClustersResponse
	(*GetKubernetesClusterRequest)(nil),                      // 14: apis.aistudio.platform.v1.GetKubernetesClusterRequest
	(*ListKubernetesClustersOptions)(nil),                    // 15: apis.aistudio.platform.v1.ListKubernetesClustersOptions
	(*ListNodesOptions)(nil),                                 // 16: apis.aistudio.platform.v1.ListNodesOptions
	(*Node)(nil),                                             // 17: apis.aistudio.platform.v1.Node
	(*ListNodesResult)(nil),                                  // 18: apis.aistudio.platform.v1.ListNodesResult
	(*AssignNodeRequest)(nil),                                // 19: apis.aistudio.platform.v1.AssignNodeRequest
	(*RecycleNodeRequest)(nil),                               // 20: apis.aistudio.platform.v1.RecycleNodeRequest
	(*GetNodesInfoStatisticsRequest)(nil),                    // 21: apis.aistudio.platform.v1.GetNodesInfoStatisticsRequest
	(*NodesInfoStatisticsResponse)(nil),                      // 22: apis.aistudio.platform.v1.NodesInfoStatisticsResponse
	(*AddDragonflyRequest)(nil),                              // 23: apis.aistudio.platform.v1.AddDragonflyRequest
	(*SetDragonflyRequest)(nil),                              // 24: apis.aistudio.platform.v1.SetDragonflyRequest
	(*DeleteDragonflyRequest)(nil),                           // 25: apis.aistudio.platform.v1.DeleteDragonflyRequest
	(*ListDragonflyRequest)(nil),                             // 26: apis.aistudio.platform.v1.ListDragonflyRequest
	(*ListDragonflyResponse)(nil),                            // 27: apis.aistudio.platform.v1.ListDragonflyResponse
	(*Dragonfly)(nil),                                        // 28: apis.aistudio.platform.v1.Dragonfly
	(*ListDistributedCacheRegistrationsRequest)(nil),         // 29: apis.aistudio.platform.v1.ListDistributedCacheRegistrationsRequest
	(*ListDistributedCacheRegistrationsResponse)(nil),        // 30: apis.aistudio.platform.v1.ListDistributedCacheRegistrationsResponse
	(*DistributedCacheRegistration)(nil),                     // 31: apis.aistudio.platform.v1.DistributedCacheRegistration
	(*DistributedCacheRegistrationStatus)(nil),               // 32: apis.aistudio.platform.v1.DistributedCacheRegistrationStatus
	(*CreateDistributedCacheRegistrationRequest)(nil),        // 33: apis.aistudio.platform.v1.CreateDistributedCacheRegistrationRequest
	(*CreateDistributedCacheRegistrationResponse)(nil),       // 34: apis.aistudio.platform.v1.CreateDistributedCacheRegistrationResponse
	(*DeleteDistributedCacheRegistrationRequest)(nil),        // 35: apis.aistudio.platform.v1.DeleteDistributedCacheRegistrationRequest
	(*DeleteDistributedCacheRegistrationResponse)(nil),       // 36: apis.aistudio.platform.v1.DeleteDistributedCacheRegistrationResponse
	(*UpdateDistributedCacheRegistrationRequest)(nil),        // 37: apis.aistudio.platform.v1.UpdateDistributedCacheRegistrationRequest
	(*UpdateDistributedCacheRegistrationResponse)(nil),       // 38: apis.aistudio.platform.v1.UpdateDistributedCacheRegistrationResponse
	(*ListDistributedCacheRegistrationRegionsRequest)(nil),   // 39: apis.aistudio.platform.v1.ListDistributedCacheRegistrationRegionsRequest
	(*ListDistributedCacheRegistrationRegionsResponse)(nil),  // 40: apis.aistudio.platform.v1.ListDistributedCacheRegistrationRegionsResponse
	(*DistributedCacheRegistrationRegion)(nil),               // 41: apis.aistudio.platform.v1.DistributedCacheRegistrationRegion
	(*ListDistributedCacheRegistrationZonesRequest)(nil),     // 42: apis.aistudio.platform.v1.ListDistributedCacheRegistrationZonesRequest
	(*ListDistributedCacheRegistrationZonesResponse)(nil),    // 43: apis.aistudio.platform.v1.ListDistributedCacheRegistrationZonesResponse
	(*DistributedCacheRegistrationZone)(nil),                 // 44: apis.aistudio.platform.v1.DistributedCacheRegistrationZone
	(*ListDistributedCacheRegistrationClustersRequest)(nil),  // 45: apis.aistudio.platform.v1.ListDistributedCacheRegistrationClustersRequest
	(*ListDistributedCacheRegistrationClustersResponse)(nil), // 46: apis.aistudio.platform.v1.ListDistributedCacheRegistrationClustersResponse
	(*DistributedCacheRegistrationCluster)(nil),              // 47: apis.aistudio.platform.v1.DistributedCacheRegistrationCluster
	nil,                           // 48: apis.aistudio.platform.v1.KubernetesCluster.NodeSelectorEntry
	nil,                           // 49: apis.aistudio.platform.v1.CreateKubernetesClusterRequest.NodeSelectorEntry
	nil,                           // 50: apis.aistudio.platform.v1.Node.LabelsEntry
	nil,                           // 51: apis.aistudio.platform.v1.Node.AnnotationsEntry
	nil,                           // 52: apis.aistudio.platform.v1.NodesInfoStatisticsResponse.GpuMapEntry
	(*common.TimestampModel)(nil), // 53: apis.common.TimestampModel
	(*emptypb.Empty)(nil),         // 54: google.protobuf.Empty
}
var file_aistudio_platform_v1_platform_proto_depIdxs = []int32{
	9,  // 0: apis.aistudio.platform.v1.ListNodeSpecificationsResult.nodeSpecifications:type_name -> apis.aistudio.platform.v1.NodeSpecification
	0,  // 1: apis.aistudio.platform.v1.KubernetesCluster.usages:type_name -> apis.aistudio.platform.v1.ClusterUsage
	48, // 2: apis.aistudio.platform.v1.KubernetesCluster.nodeSelector:type_name -> apis.aistudio.platform.v1.KubernetesCluster.NodeSelectorEntry
	53, // 3: apis.aistudio.platform.v1.NodeSpecification.timestamp:type_name -> apis.common.TimestampModel
	0,  // 4: apis.aistudio.platform.v1.CreateKubernetesClusterRequest.usages:type_name -> apis.aistudio.platform.v1.ClusterUsage
	49, // 5: apis.aistudio.platform.v1.CreateKubernetesClusterRequest.nodeSelector:type_name -> apis.aistudio.platform.v1.CreateKubernetesClusterRequest.NodeSelectorEntry
	8,  // 6: apis.aistudio.platform.v1.ListKubernetesClustersResponse.clusters:type_name -> apis.aistudio.platform.v1.KubernetesCluster
	50, // 7: apis.aistudio.platform.v1.Node.labels:type_name -> apis.aistudio.platform.v1.Node.LabelsEntry
	51, // 8: apis.aistudio.platform.v1.Node.annotations:type_name -> apis.aistudio.platform.v1.Node.AnnotationsEntry
	17, // 9: apis.aistudio.platform.v1.ListNodesResult.nodes:type_name -> apis.aistudio.platform.v1.Node
	52, // 10: apis.aistudio.platform.v1.NodesInfoStatisticsResponse.gpuMap:type_name -> apis.aistudio.platform.v1.NodesInfoStatisticsResponse.GpuMapEntry
	28, // 11: apis.aistudio.platform.v1.ListDragonflyResponse.dragonflies:type_name -> apis.aistudio.platform.v1.Dragonfly
	31, // 12: apis.aistudio.platform.v1.ListDistributedCacheRegistrationsResponse.distributedCacheRegistrations:type_name -> apis.aistudio.platform.v1.DistributedCacheRegistration
	32, // 13: apis.aistudio.platform.v1.DistributedCacheRegistration.status:type_name -> apis.aistudio.platform.v1.DistributedCacheRegistrationStatus
	41, // 14: apis.aistudio.platform.v1.ListDistributedCacheRegistrationRegionsResponse.distributedCacheRegistrationRegions:type_name -> apis.aistudio.platform.v1.DistributedCacheRegistrationRegion
	44, // 15: apis.aistudio.platform.v1.ListDistributedCacheRegistrationZonesResponse.distributedCacheRegistrationZones:type_name -> apis.aistudio.platform.v1.DistributedCacheRegistrationZone
	47, // 16: apis.aistudio.platform.v1.ListDistributedCacheRegistrationClustersResponse.distributedCacheRegistrationClusters:type_name -> apis.aistudio.platform.v1.DistributedCacheRegistrationCluster
	11, // 17: apis.aistudio.platform.v1.PlatformService.AddOrUpdateKubernetesCluster:input_type -> apis.aistudio.platform.v1.CreateKubernetesClusterRequest
	12, // 18: apis.aistudio.platform.v1.PlatformService.RemoveKubernetesCluster:input_type -> apis.aistudio.platform.v1.RemoveKubernetesClusterRequest
	15, // 19: apis.aistudio.platform.v1.PlatformService.ListKubernetesClusters:input_type -> apis.aistudio.platform.v1.ListKubernetesClustersOptions
	14, // 20: apis.aistudio.platform.v1.PlatformService.GetKubernetesCluster:input_type -> apis.aistudio.platform.v1.GetKubernetesClusterRequest
	9,  // 21: apis.aistudio.platform.v1.PlatformService.AddOrUpdateNodeSpecification:input_type -> apis.aistudio.platform.v1.NodeSpecification
	7,  // 22: apis.aistudio.platform.v1.PlatformService.DeleteNodeSpecification:input_type -> apis.aistudio.platform.v1.DeleteNodeSpecificationRequest
	5,  // 23: apis.aistudio.platform.v1.PlatformService.ListNodeSpecifications:input_type -> apis.aistudio.platform.v1.ListNodeSpecificationOptions
	1,  // 24: apis.aistudio.platform.v1.PlatformService.AddOrUpdateCubeFSCluster:input_type -> apis.aistudio.platform.v1.AddOrUpdateCubeFSClusterRequest
	2,  // 25: apis.aistudio.platform.v1.PlatformService.DeleteCubeFSCluster:input_type -> apis.aistudio.platform.v1.DeleteCubeFSClusterRequest
	3,  // 26: apis.aistudio.platform.v1.PlatformService.CubeFSClusterUpdateStatus:input_type -> apis.aistudio.platform.v1.CubeFSClusterSetStatusRequest
	4,  // 27: apis.aistudio.platform.v1.PlatformService.SetCubeFSDashboard:input_type -> apis.aistudio.platform.v1.SetCubeFSDashboardRequest
	16, // 28: apis.aistudio.platform.v1.PlatformService.ListNodes:input_type -> apis.aistudio.platform.v1.ListNodesOptions
	21, // 29: apis.aistudio.platform.v1.PlatformService.GetNodesInfoStatistics:input_type -> apis.aistudio.platform.v1.GetNodesInfoStatisticsRequest
	19, // 30: apis.aistudio.platform.v1.PlatformService.AssignNode:input_type -> apis.aistudio.platform.v1.AssignNodeRequest
	20, // 31: apis.aistudio.platform.v1.PlatformService.RecycleNode:input_type -> apis.aistudio.platform.v1.RecycleNodeRequest
	23, // 32: apis.aistudio.platform.v1.PlatformService.AddDragonfly:input_type -> apis.aistudio.platform.v1.AddDragonflyRequest
	24, // 33: apis.aistudio.platform.v1.PlatformService.SetDragonfly:input_type -> apis.aistudio.platform.v1.SetDragonflyRequest
	25, // 34: apis.aistudio.platform.v1.PlatformService.DeleteDragonfly:input_type -> apis.aistudio.platform.v1.DeleteDragonflyRequest
	26, // 35: apis.aistudio.platform.v1.PlatformService.ListDragonfly:input_type -> apis.aistudio.platform.v1.ListDragonflyRequest
	29, // 36: apis.aistudio.platform.v1.PlatformService.ListDistributedCacheRegistrations:input_type -> apis.aistudio.platform.v1.ListDistributedCacheRegistrationsRequest
	33, // 37: apis.aistudio.platform.v1.PlatformService.CreateDistributedCacheRegistration:input_type -> apis.aistudio.platform.v1.CreateDistributedCacheRegistrationRequest
	35, // 38: apis.aistudio.platform.v1.PlatformService.DeleteDistributedCacheRegistration:input_type -> apis.aistudio.platform.v1.DeleteDistributedCacheRegistrationRequest
	37, // 39: apis.aistudio.platform.v1.PlatformService.UpdateDistributedCacheRegistration:input_type -> apis.aistudio.platform.v1.UpdateDistributedCacheRegistrationRequest
	39, // 40: apis.aistudio.platform.v1.PlatformService.ListDistributedCacheRegistrationRegions:input_type -> apis.aistudio.platform.v1.ListDistributedCacheRegistrationRegionsRequest
	42, // 41: apis.aistudio.platform.v1.PlatformService.ListDistributedCacheRegistrationZones:input_type -> apis.aistudio.platform.v1.ListDistributedCacheRegistrationZonesRequest
	45, // 42: apis.aistudio.platform.v1.PlatformService.ListDistributedCacheRegistrationClusters:input_type -> apis.aistudio.platform.v1.ListDistributedCacheRegistrationClustersRequest
	8,  // 43: apis.aistudio.platform.v1.PlatformService.AddOrUpdateKubernetesCluster:output_type -> apis.aistudio.platform.v1.KubernetesCluster
	8,  // 44: apis.aistudio.platform.v1.PlatformService.RemoveKubernetesCluster:output_type -> apis.aistudio.platform.v1.KubernetesCluster
	13, // 45: apis.aistudio.platform.v1.PlatformService.ListKubernetesClusters:output_type -> apis.aistudio.platform.v1.ListKubernetesClustersResponse
	8,  // 46: apis.aistudio.platform.v1.PlatformService.GetKubernetesCluster:output_type -> apis.aistudio.platform.v1.KubernetesCluster
	9,  // 47: apis.aistudio.platform.v1.PlatformService.AddOrUpdateNodeSpecification:output_type -> apis.aistudio.platform.v1.NodeSpecification
	54, // 48: apis.aistudio.platform.v1.PlatformService.DeleteNodeSpecification:output_type -> google.protobuf.Empty
	6,  // 49: apis.aistudio.platform.v1.PlatformService.ListNodeSpecifications:output_type -> apis.aistudio.platform.v1.ListNodeSpecificationsResult
	54, // 50: apis.aistudio.platform.v1.PlatformService.AddOrUpdateCubeFSCluster:output_type -> google.protobuf.Empty
	54, // 51: apis.aistudio.platform.v1.PlatformService.DeleteCubeFSCluster:output_type -> google.protobuf.Empty
	54, // 52: apis.aistudio.platform.v1.PlatformService.CubeFSClusterUpdateStatus:output_type -> google.protobuf.Empty
	54, // 53: apis.aistudio.platform.v1.PlatformService.SetCubeFSDashboard:output_type -> google.protobuf.Empty
	18, // 54: apis.aistudio.platform.v1.PlatformService.ListNodes:output_type -> apis.aistudio.platform.v1.ListNodesResult
	22, // 55: apis.aistudio.platform.v1.PlatformService.GetNodesInfoStatistics:output_type -> apis.aistudio.platform.v1.NodesInfoStatisticsResponse
	54, // 56: apis.aistudio.platform.v1.PlatformService.AssignNode:output_type -> google.protobuf.Empty
	54, // 57: apis.aistudio.platform.v1.PlatformService.RecycleNode:output_type -> google.protobuf.Empty
	54, // 58: apis.aistudio.platform.v1.PlatformService.AddDragonfly:output_type -> google.protobuf.Empty
	54, // 59: apis.aistudio.platform.v1.PlatformService.SetDragonfly:output_type -> google.protobuf.Empty
	54, // 60: apis.aistudio.platform.v1.PlatformService.DeleteDragonfly:output_type -> google.protobuf.Empty
	27, // 61: apis.aistudio.platform.v1.PlatformService.ListDragonfly:output_type -> apis.aistudio.platform.v1.ListDragonflyResponse
	30, // 62: apis.aistudio.platform.v1.PlatformService.ListDistributedCacheRegistrations:output_type -> apis.aistudio.platform.v1.ListDistributedCacheRegistrationsResponse
	34, // 63: apis.aistudio.platform.v1.PlatformService.CreateDistributedCacheRegistration:output_type -> apis.aistudio.platform.v1.CreateDistributedCacheRegistrationResponse
	36, // 64: apis.aistudio.platform.v1.PlatformService.DeleteDistributedCacheRegistration:output_type -> apis.aistudio.platform.v1.DeleteDistributedCacheRegistrationResponse
	38, // 65: apis.aistudio.platform.v1.PlatformService.UpdateDistributedCacheRegistration:output_type -> apis.aistudio.platform.v1.UpdateDistributedCacheRegistrationResponse
	40, // 66: apis.aistudio.platform.v1.PlatformService.ListDistributedCacheRegistrationRegions:output_type -> apis.aistudio.platform.v1.ListDistributedCacheRegistrationRegionsResponse
	43, // 67: apis.aistudio.platform.v1.PlatformService.ListDistributedCacheRegistrationZones:output_type -> apis.aistudio.platform.v1.ListDistributedCacheRegistrationZonesResponse
	46, // 68: apis.aistudio.platform.v1.PlatformService.ListDistributedCacheRegistrationClusters:output_type -> apis.aistudio.platform.v1.ListDistributedCacheRegistrationClustersResponse
	43, // [43:69] is the sub-list for method output_type
	17, // [17:43] is the sub-list for method input_type
	17, // [17:17] is the sub-list for extension type_name
	17, // [17:17] is the sub-list for extension extendee
	0,  // [0:17] is the sub-list for field type_name
}

func init() { file_aistudio_platform_v1_platform_proto_init() }
func file_aistudio_platform_v1_platform_proto_init() {
	if File_aistudio_platform_v1_platform_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_aistudio_platform_v1_platform_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*AddOrUpdateCubeFSClusterRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_platform_v1_platform_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*DeleteCubeFSClusterRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_platform_v1_platform_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*CubeFSClusterSetStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_platform_v1_platform_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*SetCubeFSDashboardRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_platform_v1_platform_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*ListNodeSpecificationOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_platform_v1_platform_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*ListNodeSpecificationsResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_platform_v1_platform_proto_msgTypes[6].Exporter = func(v any, i int) any {
			switch v := v.(*DeleteNodeSpecificationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_platform_v1_platform_proto_msgTypes[7].Exporter = func(v any, i int) any {
			switch v := v.(*KubernetesCluster); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_platform_v1_platform_proto_msgTypes[8].Exporter = func(v any, i int) any {
			switch v := v.(*NodeSpecification); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_platform_v1_platform_proto_msgTypes[9].Exporter = func(v any, i int) any {
			switch v := v.(*CubeFSCluster); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_platform_v1_platform_proto_msgTypes[10].Exporter = func(v any, i int) any {
			switch v := v.(*CreateKubernetesClusterRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_platform_v1_platform_proto_msgTypes[11].Exporter = func(v any, i int) any {
			switch v := v.(*RemoveKubernetesClusterRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_platform_v1_platform_proto_msgTypes[12].Exporter = func(v any, i int) any {
			switch v := v.(*ListKubernetesClustersResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_platform_v1_platform_proto_msgTypes[13].Exporter = func(v any, i int) any {
			switch v := v.(*GetKubernetesClusterRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_platform_v1_platform_proto_msgTypes[14].Exporter = func(v any, i int) any {
			switch v := v.(*ListKubernetesClustersOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_platform_v1_platform_proto_msgTypes[15].Exporter = func(v any, i int) any {
			switch v := v.(*ListNodesOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_platform_v1_platform_proto_msgTypes[16].Exporter = func(v any, i int) any {
			switch v := v.(*Node); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_platform_v1_platform_proto_msgTypes[17].Exporter = func(v any, i int) any {
			switch v := v.(*ListNodesResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_platform_v1_platform_proto_msgTypes[18].Exporter = func(v any, i int) any {
			switch v := v.(*AssignNodeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_platform_v1_platform_proto_msgTypes[19].Exporter = func(v any, i int) any {
			switch v := v.(*RecycleNodeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_platform_v1_platform_proto_msgTypes[20].Exporter = func(v any, i int) any {
			switch v := v.(*GetNodesInfoStatisticsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_platform_v1_platform_proto_msgTypes[21].Exporter = func(v any, i int) any {
			switch v := v.(*NodesInfoStatisticsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_platform_v1_platform_proto_msgTypes[22].Exporter = func(v any, i int) any {
			switch v := v.(*AddDragonflyRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_platform_v1_platform_proto_msgTypes[23].Exporter = func(v any, i int) any {
			switch v := v.(*SetDragonflyRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_platform_v1_platform_proto_msgTypes[24].Exporter = func(v any, i int) any {
			switch v := v.(*DeleteDragonflyRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_platform_v1_platform_proto_msgTypes[25].Exporter = func(v any, i int) any {
			switch v := v.(*ListDragonflyRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_platform_v1_platform_proto_msgTypes[26].Exporter = func(v any, i int) any {
			switch v := v.(*ListDragonflyResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_platform_v1_platform_proto_msgTypes[27].Exporter = func(v any, i int) any {
			switch v := v.(*Dragonfly); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_platform_v1_platform_proto_msgTypes[28].Exporter = func(v any, i int) any {
			switch v := v.(*ListDistributedCacheRegistrationsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_platform_v1_platform_proto_msgTypes[29].Exporter = func(v any, i int) any {
			switch v := v.(*ListDistributedCacheRegistrationsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_platform_v1_platform_proto_msgTypes[30].Exporter = func(v any, i int) any {
			switch v := v.(*DistributedCacheRegistration); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_platform_v1_platform_proto_msgTypes[31].Exporter = func(v any, i int) any {
			switch v := v.(*DistributedCacheRegistrationStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_platform_v1_platform_proto_msgTypes[32].Exporter = func(v any, i int) any {
			switch v := v.(*CreateDistributedCacheRegistrationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_platform_v1_platform_proto_msgTypes[33].Exporter = func(v any, i int) any {
			switch v := v.(*CreateDistributedCacheRegistrationResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_platform_v1_platform_proto_msgTypes[34].Exporter = func(v any, i int) any {
			switch v := v.(*DeleteDistributedCacheRegistrationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_platform_v1_platform_proto_msgTypes[35].Exporter = func(v any, i int) any {
			switch v := v.(*DeleteDistributedCacheRegistrationResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_platform_v1_platform_proto_msgTypes[36].Exporter = func(v any, i int) any {
			switch v := v.(*UpdateDistributedCacheRegistrationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_platform_v1_platform_proto_msgTypes[37].Exporter = func(v any, i int) any {
			switch v := v.(*UpdateDistributedCacheRegistrationResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_platform_v1_platform_proto_msgTypes[38].Exporter = func(v any, i int) any {
			switch v := v.(*ListDistributedCacheRegistrationRegionsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_platform_v1_platform_proto_msgTypes[39].Exporter = func(v any, i int) any {
			switch v := v.(*ListDistributedCacheRegistrationRegionsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_platform_v1_platform_proto_msgTypes[40].Exporter = func(v any, i int) any {
			switch v := v.(*DistributedCacheRegistrationRegion); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_platform_v1_platform_proto_msgTypes[41].Exporter = func(v any, i int) any {
			switch v := v.(*ListDistributedCacheRegistrationZonesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_platform_v1_platform_proto_msgTypes[42].Exporter = func(v any, i int) any {
			switch v := v.(*ListDistributedCacheRegistrationZonesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_platform_v1_platform_proto_msgTypes[43].Exporter = func(v any, i int) any {
			switch v := v.(*DistributedCacheRegistrationZone); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_platform_v1_platform_proto_msgTypes[44].Exporter = func(v any, i int) any {
			switch v := v.(*ListDistributedCacheRegistrationClustersRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_platform_v1_platform_proto_msgTypes[45].Exporter = func(v any, i int) any {
			switch v := v.(*ListDistributedCacheRegistrationClustersResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_platform_v1_platform_proto_msgTypes[46].Exporter = func(v any, i int) any {
			switch v := v.(*DistributedCacheRegistrationCluster); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_aistudio_platform_v1_platform_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   52,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_aistudio_platform_v1_platform_proto_goTypes,
		DependencyIndexes: file_aistudio_platform_v1_platform_proto_depIdxs,
		EnumInfos:         file_aistudio_platform_v1_platform_proto_enumTypes,
		MessageInfos:      file_aistudio_platform_v1_platform_proto_msgTypes,
	}.Build()
	File_aistudio_platform_v1_platform_proto = out.File
	file_aistudio_platform_v1_platform_proto_rawDesc = nil
	file_aistudio_platform_v1_platform_proto_goTypes = nil
	file_aistudio_platform_v1_platform_proto_depIdxs = nil
}
