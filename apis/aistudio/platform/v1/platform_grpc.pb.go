// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v5.29.3
// source: aistudio/platform/v1/platform.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	PlatformService_AddOrUpdateKubernetesCluster_FullMethodName             = "/apis.aistudio.platform.v1.PlatformService/AddOrUpdateKubernetesCluster"
	PlatformService_RemoveKubernetesCluster_FullMethodName                  = "/apis.aistudio.platform.v1.PlatformService/RemoveKubernetesCluster"
	PlatformService_ListKubernetesClusters_FullMethodName                   = "/apis.aistudio.platform.v1.PlatformService/ListKubernetesClusters"
	PlatformService_GetKubernetesCluster_FullMethodName                     = "/apis.aistudio.platform.v1.PlatformService/GetKubernetesCluster"
	PlatformService_AddOrUpdateNodeSpecification_FullMethodName             = "/apis.aistudio.platform.v1.PlatformService/AddOrUpdateNodeSpecification"
	PlatformService_DeleteNodeSpecification_FullMethodName                  = "/apis.aistudio.platform.v1.PlatformService/DeleteNodeSpecification"
	PlatformService_ListNodeSpecifications_FullMethodName                   = "/apis.aistudio.platform.v1.PlatformService/ListNodeSpecifications"
	PlatformService_AddOrUpdateCubeFSCluster_FullMethodName                 = "/apis.aistudio.platform.v1.PlatformService/AddOrUpdateCubeFSCluster"
	PlatformService_DeleteCubeFSCluster_FullMethodName                      = "/apis.aistudio.platform.v1.PlatformService/DeleteCubeFSCluster"
	PlatformService_CubeFSClusterUpdateStatus_FullMethodName                = "/apis.aistudio.platform.v1.PlatformService/CubeFSClusterUpdateStatus"
	PlatformService_SetCubeFSDashboard_FullMethodName                       = "/apis.aistudio.platform.v1.PlatformService/SetCubeFSDashboard"
	PlatformService_ListNodes_FullMethodName                                = "/apis.aistudio.platform.v1.PlatformService/ListNodes"
	PlatformService_GetNodesInfoStatistics_FullMethodName                   = "/apis.aistudio.platform.v1.PlatformService/GetNodesInfoStatistics"
	PlatformService_AssignNode_FullMethodName                               = "/apis.aistudio.platform.v1.PlatformService/AssignNode"
	PlatformService_RecycleNode_FullMethodName                              = "/apis.aistudio.platform.v1.PlatformService/RecycleNode"
	PlatformService_AddDragonfly_FullMethodName                             = "/apis.aistudio.platform.v1.PlatformService/AddDragonfly"
	PlatformService_SetDragonfly_FullMethodName                             = "/apis.aistudio.platform.v1.PlatformService/SetDragonfly"
	PlatformService_DeleteDragonfly_FullMethodName                          = "/apis.aistudio.platform.v1.PlatformService/DeleteDragonfly"
	PlatformService_ListDragonfly_FullMethodName                            = "/apis.aistudio.platform.v1.PlatformService/ListDragonfly"
	PlatformService_ListDistributedCacheRegistrations_FullMethodName        = "/apis.aistudio.platform.v1.PlatformService/ListDistributedCacheRegistrations"
	PlatformService_CreateDistributedCacheRegistration_FullMethodName       = "/apis.aistudio.platform.v1.PlatformService/CreateDistributedCacheRegistration"
	PlatformService_DeleteDistributedCacheRegistration_FullMethodName       = "/apis.aistudio.platform.v1.PlatformService/DeleteDistributedCacheRegistration"
	PlatformService_UpdateDistributedCacheRegistration_FullMethodName       = "/apis.aistudio.platform.v1.PlatformService/UpdateDistributedCacheRegistration"
	PlatformService_ListDistributedCacheRegistrationRegions_FullMethodName  = "/apis.aistudio.platform.v1.PlatformService/ListDistributedCacheRegistrationRegions"
	PlatformService_ListDistributedCacheRegistrationZones_FullMethodName    = "/apis.aistudio.platform.v1.PlatformService/ListDistributedCacheRegistrationZones"
	PlatformService_ListDistributedCacheRegistrationClusters_FullMethodName = "/apis.aistudio.platform.v1.PlatformService/ListDistributedCacheRegistrationClusters"
)

// PlatformServiceClient is the client API for PlatformService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// 平台设置接口
type PlatformServiceClient interface {
	// 新增或者更新Kubernetes的集群信息
	AddOrUpdateKubernetesCluster(ctx context.Context, in *CreateKubernetesClusterRequest, opts ...grpc.CallOption) (*KubernetesCluster, error)
	RemoveKubernetesCluster(ctx context.Context, in *RemoveKubernetesClusterRequest, opts ...grpc.CallOption) (*KubernetesCluster, error)
	ListKubernetesClusters(ctx context.Context, in *ListKubernetesClustersOptions, opts ...grpc.CallOption) (*ListKubernetesClustersResponse, error)
	GetKubernetesCluster(ctx context.Context, in *GetKubernetesClusterRequest, opts ...grpc.CallOption) (*KubernetesCluster, error)
	// 新增节点规格
	AddOrUpdateNodeSpecification(ctx context.Context, in *NodeSpecification, opts ...grpc.CallOption) (*NodeSpecification, error)
	// 删除节点规格
	DeleteNodeSpecification(ctx context.Context, in *DeleteNodeSpecificationRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 查询所有的节点规格
	ListNodeSpecifications(ctx context.Context, in *ListNodeSpecificationOptions, opts ...grpc.CallOption) (*ListNodeSpecificationsResult, error)
	AddOrUpdateCubeFSCluster(ctx context.Context, in *AddOrUpdateCubeFSClusterRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	DeleteCubeFSCluster(ctx context.Context, in *DeleteCubeFSClusterRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	CubeFSClusterUpdateStatus(ctx context.Context, in *CubeFSClusterSetStatusRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	SetCubeFSDashboard(ctx context.Context, in *SetCubeFSDashboardRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 展示所有节点
	ListNodes(ctx context.Context, in *ListNodesOptions, opts ...grpc.CallOption) (*ListNodesResult, error)
	// 节点信息统计
	GetNodesInfoStatistics(ctx context.Context, in *GetNodesInfoStatisticsRequest, opts ...grpc.CallOption) (*NodesInfoStatisticsResponse, error)
	// 节点分配和回收
	AssignNode(ctx context.Context, in *AssignNodeRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	RecycleNode(ctx context.Context, in *RecycleNodeRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 添加Dragonfly配置
	AddDragonfly(ctx context.Context, in *AddDragonflyRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 更新Dragonfly配置
	SetDragonfly(ctx context.Context, in *SetDragonflyRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 删除Dragonfly配置
	DeleteDragonfly(ctx context.Context, in *DeleteDragonflyRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 获取Dragonfly配置
	ListDragonfly(ctx context.Context, in *ListDragonflyRequest, opts ...grpc.CallOption) (*ListDragonflyResponse, error)
	// 获取分布式缓存注册表列表
	ListDistributedCacheRegistrations(ctx context.Context, in *ListDistributedCacheRegistrationsRequest, opts ...grpc.CallOption) (*ListDistributedCacheRegistrationsResponse, error)
	// 创建分布式缓存注册表
	CreateDistributedCacheRegistration(ctx context.Context, in *CreateDistributedCacheRegistrationRequest, opts ...grpc.CallOption) (*CreateDistributedCacheRegistrationResponse, error)
	// 删除分布式缓存注册表
	DeleteDistributedCacheRegistration(ctx context.Context, in *DeleteDistributedCacheRegistrationRequest, opts ...grpc.CallOption) (*DeleteDistributedCacheRegistrationResponse, error)
	// 更新分布式缓存注册表
	UpdateDistributedCacheRegistration(ctx context.Context, in *UpdateDistributedCacheRegistrationRequest, opts ...grpc.CallOption) (*UpdateDistributedCacheRegistrationResponse, error)
	// 获取存储框架地区
	ListDistributedCacheRegistrationRegions(ctx context.Context, in *ListDistributedCacheRegistrationRegionsRequest, opts ...grpc.CallOption) (*ListDistributedCacheRegistrationRegionsResponse, error)
	// 获取分布式缓存注册可用区
	ListDistributedCacheRegistrationZones(ctx context.Context, in *ListDistributedCacheRegistrationZonesRequest, opts ...grpc.CallOption) (*ListDistributedCacheRegistrationZonesResponse, error)
	// 获取分布式缓存注册集群
	ListDistributedCacheRegistrationClusters(ctx context.Context, in *ListDistributedCacheRegistrationClustersRequest, opts ...grpc.CallOption) (*ListDistributedCacheRegistrationClustersResponse, error)
}

type platformServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewPlatformServiceClient(cc grpc.ClientConnInterface) PlatformServiceClient {
	return &platformServiceClient{cc}
}

func (c *platformServiceClient) AddOrUpdateKubernetesCluster(ctx context.Context, in *CreateKubernetesClusterRequest, opts ...grpc.CallOption) (*KubernetesCluster, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(KubernetesCluster)
	err := c.cc.Invoke(ctx, PlatformService_AddOrUpdateKubernetesCluster_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *platformServiceClient) RemoveKubernetesCluster(ctx context.Context, in *RemoveKubernetesClusterRequest, opts ...grpc.CallOption) (*KubernetesCluster, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(KubernetesCluster)
	err := c.cc.Invoke(ctx, PlatformService_RemoveKubernetesCluster_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *platformServiceClient) ListKubernetesClusters(ctx context.Context, in *ListKubernetesClustersOptions, opts ...grpc.CallOption) (*ListKubernetesClustersResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListKubernetesClustersResponse)
	err := c.cc.Invoke(ctx, PlatformService_ListKubernetesClusters_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *platformServiceClient) GetKubernetesCluster(ctx context.Context, in *GetKubernetesClusterRequest, opts ...grpc.CallOption) (*KubernetesCluster, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(KubernetesCluster)
	err := c.cc.Invoke(ctx, PlatformService_GetKubernetesCluster_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *platformServiceClient) AddOrUpdateNodeSpecification(ctx context.Context, in *NodeSpecification, opts ...grpc.CallOption) (*NodeSpecification, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(NodeSpecification)
	err := c.cc.Invoke(ctx, PlatformService_AddOrUpdateNodeSpecification_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *platformServiceClient) DeleteNodeSpecification(ctx context.Context, in *DeleteNodeSpecificationRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, PlatformService_DeleteNodeSpecification_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *platformServiceClient) ListNodeSpecifications(ctx context.Context, in *ListNodeSpecificationOptions, opts ...grpc.CallOption) (*ListNodeSpecificationsResult, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListNodeSpecificationsResult)
	err := c.cc.Invoke(ctx, PlatformService_ListNodeSpecifications_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *platformServiceClient) AddOrUpdateCubeFSCluster(ctx context.Context, in *AddOrUpdateCubeFSClusterRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, PlatformService_AddOrUpdateCubeFSCluster_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *platformServiceClient) DeleteCubeFSCluster(ctx context.Context, in *DeleteCubeFSClusterRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, PlatformService_DeleteCubeFSCluster_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *platformServiceClient) CubeFSClusterUpdateStatus(ctx context.Context, in *CubeFSClusterSetStatusRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, PlatformService_CubeFSClusterUpdateStatus_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *platformServiceClient) SetCubeFSDashboard(ctx context.Context, in *SetCubeFSDashboardRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, PlatformService_SetCubeFSDashboard_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *platformServiceClient) ListNodes(ctx context.Context, in *ListNodesOptions, opts ...grpc.CallOption) (*ListNodesResult, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListNodesResult)
	err := c.cc.Invoke(ctx, PlatformService_ListNodes_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *platformServiceClient) GetNodesInfoStatistics(ctx context.Context, in *GetNodesInfoStatisticsRequest, opts ...grpc.CallOption) (*NodesInfoStatisticsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(NodesInfoStatisticsResponse)
	err := c.cc.Invoke(ctx, PlatformService_GetNodesInfoStatistics_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *platformServiceClient) AssignNode(ctx context.Context, in *AssignNodeRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, PlatformService_AssignNode_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *platformServiceClient) RecycleNode(ctx context.Context, in *RecycleNodeRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, PlatformService_RecycleNode_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *platformServiceClient) AddDragonfly(ctx context.Context, in *AddDragonflyRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, PlatformService_AddDragonfly_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *platformServiceClient) SetDragonfly(ctx context.Context, in *SetDragonflyRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, PlatformService_SetDragonfly_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *platformServiceClient) DeleteDragonfly(ctx context.Context, in *DeleteDragonflyRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, PlatformService_DeleteDragonfly_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *platformServiceClient) ListDragonfly(ctx context.Context, in *ListDragonflyRequest, opts ...grpc.CallOption) (*ListDragonflyResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListDragonflyResponse)
	err := c.cc.Invoke(ctx, PlatformService_ListDragonfly_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *platformServiceClient) ListDistributedCacheRegistrations(ctx context.Context, in *ListDistributedCacheRegistrationsRequest, opts ...grpc.CallOption) (*ListDistributedCacheRegistrationsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListDistributedCacheRegistrationsResponse)
	err := c.cc.Invoke(ctx, PlatformService_ListDistributedCacheRegistrations_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *platformServiceClient) CreateDistributedCacheRegistration(ctx context.Context, in *CreateDistributedCacheRegistrationRequest, opts ...grpc.CallOption) (*CreateDistributedCacheRegistrationResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateDistributedCacheRegistrationResponse)
	err := c.cc.Invoke(ctx, PlatformService_CreateDistributedCacheRegistration_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *platformServiceClient) DeleteDistributedCacheRegistration(ctx context.Context, in *DeleteDistributedCacheRegistrationRequest, opts ...grpc.CallOption) (*DeleteDistributedCacheRegistrationResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteDistributedCacheRegistrationResponse)
	err := c.cc.Invoke(ctx, PlatformService_DeleteDistributedCacheRegistration_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *platformServiceClient) UpdateDistributedCacheRegistration(ctx context.Context, in *UpdateDistributedCacheRegistrationRequest, opts ...grpc.CallOption) (*UpdateDistributedCacheRegistrationResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateDistributedCacheRegistrationResponse)
	err := c.cc.Invoke(ctx, PlatformService_UpdateDistributedCacheRegistration_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *platformServiceClient) ListDistributedCacheRegistrationRegions(ctx context.Context, in *ListDistributedCacheRegistrationRegionsRequest, opts ...grpc.CallOption) (*ListDistributedCacheRegistrationRegionsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListDistributedCacheRegistrationRegionsResponse)
	err := c.cc.Invoke(ctx, PlatformService_ListDistributedCacheRegistrationRegions_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *platformServiceClient) ListDistributedCacheRegistrationZones(ctx context.Context, in *ListDistributedCacheRegistrationZonesRequest, opts ...grpc.CallOption) (*ListDistributedCacheRegistrationZonesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListDistributedCacheRegistrationZonesResponse)
	err := c.cc.Invoke(ctx, PlatformService_ListDistributedCacheRegistrationZones_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *platformServiceClient) ListDistributedCacheRegistrationClusters(ctx context.Context, in *ListDistributedCacheRegistrationClustersRequest, opts ...grpc.CallOption) (*ListDistributedCacheRegistrationClustersResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListDistributedCacheRegistrationClustersResponse)
	err := c.cc.Invoke(ctx, PlatformService_ListDistributedCacheRegistrationClusters_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PlatformServiceServer is the server API for PlatformService service.
// All implementations must embed UnimplementedPlatformServiceServer
// for forward compatibility
//
// 平台设置接口
type PlatformServiceServer interface {
	// 新增或者更新Kubernetes的集群信息
	AddOrUpdateKubernetesCluster(context.Context, *CreateKubernetesClusterRequest) (*KubernetesCluster, error)
	RemoveKubernetesCluster(context.Context, *RemoveKubernetesClusterRequest) (*KubernetesCluster, error)
	ListKubernetesClusters(context.Context, *ListKubernetesClustersOptions) (*ListKubernetesClustersResponse, error)
	GetKubernetesCluster(context.Context, *GetKubernetesClusterRequest) (*KubernetesCluster, error)
	// 新增节点规格
	AddOrUpdateNodeSpecification(context.Context, *NodeSpecification) (*NodeSpecification, error)
	// 删除节点规格
	DeleteNodeSpecification(context.Context, *DeleteNodeSpecificationRequest) (*emptypb.Empty, error)
	// 查询所有的节点规格
	ListNodeSpecifications(context.Context, *ListNodeSpecificationOptions) (*ListNodeSpecificationsResult, error)
	AddOrUpdateCubeFSCluster(context.Context, *AddOrUpdateCubeFSClusterRequest) (*emptypb.Empty, error)
	DeleteCubeFSCluster(context.Context, *DeleteCubeFSClusterRequest) (*emptypb.Empty, error)
	CubeFSClusterUpdateStatus(context.Context, *CubeFSClusterSetStatusRequest) (*emptypb.Empty, error)
	SetCubeFSDashboard(context.Context, *SetCubeFSDashboardRequest) (*emptypb.Empty, error)
	// 展示所有节点
	ListNodes(context.Context, *ListNodesOptions) (*ListNodesResult, error)
	// 节点信息统计
	GetNodesInfoStatistics(context.Context, *GetNodesInfoStatisticsRequest) (*NodesInfoStatisticsResponse, error)
	// 节点分配和回收
	AssignNode(context.Context, *AssignNodeRequest) (*emptypb.Empty, error)
	RecycleNode(context.Context, *RecycleNodeRequest) (*emptypb.Empty, error)
	// 添加Dragonfly配置
	AddDragonfly(context.Context, *AddDragonflyRequest) (*emptypb.Empty, error)
	// 更新Dragonfly配置
	SetDragonfly(context.Context, *SetDragonflyRequest) (*emptypb.Empty, error)
	// 删除Dragonfly配置
	DeleteDragonfly(context.Context, *DeleteDragonflyRequest) (*emptypb.Empty, error)
	// 获取Dragonfly配置
	ListDragonfly(context.Context, *ListDragonflyRequest) (*ListDragonflyResponse, error)
	// 获取分布式缓存注册表列表
	ListDistributedCacheRegistrations(context.Context, *ListDistributedCacheRegistrationsRequest) (*ListDistributedCacheRegistrationsResponse, error)
	// 创建分布式缓存注册表
	CreateDistributedCacheRegistration(context.Context, *CreateDistributedCacheRegistrationRequest) (*CreateDistributedCacheRegistrationResponse, error)
	// 删除分布式缓存注册表
	DeleteDistributedCacheRegistration(context.Context, *DeleteDistributedCacheRegistrationRequest) (*DeleteDistributedCacheRegistrationResponse, error)
	// 更新分布式缓存注册表
	UpdateDistributedCacheRegistration(context.Context, *UpdateDistributedCacheRegistrationRequest) (*UpdateDistributedCacheRegistrationResponse, error)
	// 获取存储框架地区
	ListDistributedCacheRegistrationRegions(context.Context, *ListDistributedCacheRegistrationRegionsRequest) (*ListDistributedCacheRegistrationRegionsResponse, error)
	// 获取分布式缓存注册可用区
	ListDistributedCacheRegistrationZones(context.Context, *ListDistributedCacheRegistrationZonesRequest) (*ListDistributedCacheRegistrationZonesResponse, error)
	// 获取分布式缓存注册集群
	ListDistributedCacheRegistrationClusters(context.Context, *ListDistributedCacheRegistrationClustersRequest) (*ListDistributedCacheRegistrationClustersResponse, error)
	mustEmbedUnimplementedPlatformServiceServer()
}

// UnimplementedPlatformServiceServer must be embedded to have forward compatible implementations.
type UnimplementedPlatformServiceServer struct {
}

func (UnimplementedPlatformServiceServer) AddOrUpdateKubernetesCluster(context.Context, *CreateKubernetesClusterRequest) (*KubernetesCluster, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddOrUpdateKubernetesCluster not implemented")
}
func (UnimplementedPlatformServiceServer) RemoveKubernetesCluster(context.Context, *RemoveKubernetesClusterRequest) (*KubernetesCluster, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemoveKubernetesCluster not implemented")
}
func (UnimplementedPlatformServiceServer) ListKubernetesClusters(context.Context, *ListKubernetesClustersOptions) (*ListKubernetesClustersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListKubernetesClusters not implemented")
}
func (UnimplementedPlatformServiceServer) GetKubernetesCluster(context.Context, *GetKubernetesClusterRequest) (*KubernetesCluster, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetKubernetesCluster not implemented")
}
func (UnimplementedPlatformServiceServer) AddOrUpdateNodeSpecification(context.Context, *NodeSpecification) (*NodeSpecification, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddOrUpdateNodeSpecification not implemented")
}
func (UnimplementedPlatformServiceServer) DeleteNodeSpecification(context.Context, *DeleteNodeSpecificationRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteNodeSpecification not implemented")
}
func (UnimplementedPlatformServiceServer) ListNodeSpecifications(context.Context, *ListNodeSpecificationOptions) (*ListNodeSpecificationsResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListNodeSpecifications not implemented")
}
func (UnimplementedPlatformServiceServer) AddOrUpdateCubeFSCluster(context.Context, *AddOrUpdateCubeFSClusterRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddOrUpdateCubeFSCluster not implemented")
}
func (UnimplementedPlatformServiceServer) DeleteCubeFSCluster(context.Context, *DeleteCubeFSClusterRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteCubeFSCluster not implemented")
}
func (UnimplementedPlatformServiceServer) CubeFSClusterUpdateStatus(context.Context, *CubeFSClusterSetStatusRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CubeFSClusterUpdateStatus not implemented")
}
func (UnimplementedPlatformServiceServer) SetCubeFSDashboard(context.Context, *SetCubeFSDashboardRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetCubeFSDashboard not implemented")
}
func (UnimplementedPlatformServiceServer) ListNodes(context.Context, *ListNodesOptions) (*ListNodesResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListNodes not implemented")
}
func (UnimplementedPlatformServiceServer) GetNodesInfoStatistics(context.Context, *GetNodesInfoStatisticsRequest) (*NodesInfoStatisticsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNodesInfoStatistics not implemented")
}
func (UnimplementedPlatformServiceServer) AssignNode(context.Context, *AssignNodeRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AssignNode not implemented")
}
func (UnimplementedPlatformServiceServer) RecycleNode(context.Context, *RecycleNodeRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RecycleNode not implemented")
}
func (UnimplementedPlatformServiceServer) AddDragonfly(context.Context, *AddDragonflyRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddDragonfly not implemented")
}
func (UnimplementedPlatformServiceServer) SetDragonfly(context.Context, *SetDragonflyRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetDragonfly not implemented")
}
func (UnimplementedPlatformServiceServer) DeleteDragonfly(context.Context, *DeleteDragonflyRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteDragonfly not implemented")
}
func (UnimplementedPlatformServiceServer) ListDragonfly(context.Context, *ListDragonflyRequest) (*ListDragonflyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListDragonfly not implemented")
}
func (UnimplementedPlatformServiceServer) ListDistributedCacheRegistrations(context.Context, *ListDistributedCacheRegistrationsRequest) (*ListDistributedCacheRegistrationsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListDistributedCacheRegistrations not implemented")
}
func (UnimplementedPlatformServiceServer) CreateDistributedCacheRegistration(context.Context, *CreateDistributedCacheRegistrationRequest) (*CreateDistributedCacheRegistrationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateDistributedCacheRegistration not implemented")
}
func (UnimplementedPlatformServiceServer) DeleteDistributedCacheRegistration(context.Context, *DeleteDistributedCacheRegistrationRequest) (*DeleteDistributedCacheRegistrationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteDistributedCacheRegistration not implemented")
}
func (UnimplementedPlatformServiceServer) UpdateDistributedCacheRegistration(context.Context, *UpdateDistributedCacheRegistrationRequest) (*UpdateDistributedCacheRegistrationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateDistributedCacheRegistration not implemented")
}
func (UnimplementedPlatformServiceServer) ListDistributedCacheRegistrationRegions(context.Context, *ListDistributedCacheRegistrationRegionsRequest) (*ListDistributedCacheRegistrationRegionsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListDistributedCacheRegistrationRegions not implemented")
}
func (UnimplementedPlatformServiceServer) ListDistributedCacheRegistrationZones(context.Context, *ListDistributedCacheRegistrationZonesRequest) (*ListDistributedCacheRegistrationZonesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListDistributedCacheRegistrationZones not implemented")
}
func (UnimplementedPlatformServiceServer) ListDistributedCacheRegistrationClusters(context.Context, *ListDistributedCacheRegistrationClustersRequest) (*ListDistributedCacheRegistrationClustersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListDistributedCacheRegistrationClusters not implemented")
}
func (UnimplementedPlatformServiceServer) mustEmbedUnimplementedPlatformServiceServer() {}

// UnsafePlatformServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PlatformServiceServer will
// result in compilation errors.
type UnsafePlatformServiceServer interface {
	mustEmbedUnimplementedPlatformServiceServer()
}

func RegisterPlatformServiceServer(s grpc.ServiceRegistrar, srv PlatformServiceServer) {
	s.RegisterService(&PlatformService_ServiceDesc, srv)
}

func _PlatformService_AddOrUpdateKubernetesCluster_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateKubernetesClusterRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PlatformServiceServer).AddOrUpdateKubernetesCluster(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PlatformService_AddOrUpdateKubernetesCluster_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PlatformServiceServer).AddOrUpdateKubernetesCluster(ctx, req.(*CreateKubernetesClusterRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PlatformService_RemoveKubernetesCluster_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveKubernetesClusterRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PlatformServiceServer).RemoveKubernetesCluster(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PlatformService_RemoveKubernetesCluster_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PlatformServiceServer).RemoveKubernetesCluster(ctx, req.(*RemoveKubernetesClusterRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PlatformService_ListKubernetesClusters_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListKubernetesClustersOptions)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PlatformServiceServer).ListKubernetesClusters(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PlatformService_ListKubernetesClusters_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PlatformServiceServer).ListKubernetesClusters(ctx, req.(*ListKubernetesClustersOptions))
	}
	return interceptor(ctx, in, info, handler)
}

func _PlatformService_GetKubernetesCluster_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetKubernetesClusterRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PlatformServiceServer).GetKubernetesCluster(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PlatformService_GetKubernetesCluster_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PlatformServiceServer).GetKubernetesCluster(ctx, req.(*GetKubernetesClusterRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PlatformService_AddOrUpdateNodeSpecification_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NodeSpecification)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PlatformServiceServer).AddOrUpdateNodeSpecification(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PlatformService_AddOrUpdateNodeSpecification_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PlatformServiceServer).AddOrUpdateNodeSpecification(ctx, req.(*NodeSpecification))
	}
	return interceptor(ctx, in, info, handler)
}

func _PlatformService_DeleteNodeSpecification_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteNodeSpecificationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PlatformServiceServer).DeleteNodeSpecification(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PlatformService_DeleteNodeSpecification_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PlatformServiceServer).DeleteNodeSpecification(ctx, req.(*DeleteNodeSpecificationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PlatformService_ListNodeSpecifications_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListNodeSpecificationOptions)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PlatformServiceServer).ListNodeSpecifications(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PlatformService_ListNodeSpecifications_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PlatformServiceServer).ListNodeSpecifications(ctx, req.(*ListNodeSpecificationOptions))
	}
	return interceptor(ctx, in, info, handler)
}

func _PlatformService_AddOrUpdateCubeFSCluster_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddOrUpdateCubeFSClusterRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PlatformServiceServer).AddOrUpdateCubeFSCluster(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PlatformService_AddOrUpdateCubeFSCluster_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PlatformServiceServer).AddOrUpdateCubeFSCluster(ctx, req.(*AddOrUpdateCubeFSClusterRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PlatformService_DeleteCubeFSCluster_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteCubeFSClusterRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PlatformServiceServer).DeleteCubeFSCluster(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PlatformService_DeleteCubeFSCluster_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PlatformServiceServer).DeleteCubeFSCluster(ctx, req.(*DeleteCubeFSClusterRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PlatformService_CubeFSClusterUpdateStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CubeFSClusterSetStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PlatformServiceServer).CubeFSClusterUpdateStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PlatformService_CubeFSClusterUpdateStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PlatformServiceServer).CubeFSClusterUpdateStatus(ctx, req.(*CubeFSClusterSetStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PlatformService_SetCubeFSDashboard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetCubeFSDashboardRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PlatformServiceServer).SetCubeFSDashboard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PlatformService_SetCubeFSDashboard_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PlatformServiceServer).SetCubeFSDashboard(ctx, req.(*SetCubeFSDashboardRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PlatformService_ListNodes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListNodesOptions)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PlatformServiceServer).ListNodes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PlatformService_ListNodes_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PlatformServiceServer).ListNodes(ctx, req.(*ListNodesOptions))
	}
	return interceptor(ctx, in, info, handler)
}

func _PlatformService_GetNodesInfoStatistics_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNodesInfoStatisticsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PlatformServiceServer).GetNodesInfoStatistics(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PlatformService_GetNodesInfoStatistics_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PlatformServiceServer).GetNodesInfoStatistics(ctx, req.(*GetNodesInfoStatisticsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PlatformService_AssignNode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AssignNodeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PlatformServiceServer).AssignNode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PlatformService_AssignNode_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PlatformServiceServer).AssignNode(ctx, req.(*AssignNodeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PlatformService_RecycleNode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecycleNodeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PlatformServiceServer).RecycleNode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PlatformService_RecycleNode_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PlatformServiceServer).RecycleNode(ctx, req.(*RecycleNodeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PlatformService_AddDragonfly_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddDragonflyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PlatformServiceServer).AddDragonfly(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PlatformService_AddDragonfly_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PlatformServiceServer).AddDragonfly(ctx, req.(*AddDragonflyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PlatformService_SetDragonfly_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetDragonflyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PlatformServiceServer).SetDragonfly(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PlatformService_SetDragonfly_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PlatformServiceServer).SetDragonfly(ctx, req.(*SetDragonflyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PlatformService_DeleteDragonfly_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteDragonflyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PlatformServiceServer).DeleteDragonfly(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PlatformService_DeleteDragonfly_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PlatformServiceServer).DeleteDragonfly(ctx, req.(*DeleteDragonflyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PlatformService_ListDragonfly_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListDragonflyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PlatformServiceServer).ListDragonfly(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PlatformService_ListDragonfly_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PlatformServiceServer).ListDragonfly(ctx, req.(*ListDragonflyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PlatformService_ListDistributedCacheRegistrations_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListDistributedCacheRegistrationsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PlatformServiceServer).ListDistributedCacheRegistrations(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PlatformService_ListDistributedCacheRegistrations_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PlatformServiceServer).ListDistributedCacheRegistrations(ctx, req.(*ListDistributedCacheRegistrationsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PlatformService_CreateDistributedCacheRegistration_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateDistributedCacheRegistrationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PlatformServiceServer).CreateDistributedCacheRegistration(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PlatformService_CreateDistributedCacheRegistration_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PlatformServiceServer).CreateDistributedCacheRegistration(ctx, req.(*CreateDistributedCacheRegistrationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PlatformService_DeleteDistributedCacheRegistration_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteDistributedCacheRegistrationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PlatformServiceServer).DeleteDistributedCacheRegistration(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PlatformService_DeleteDistributedCacheRegistration_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PlatformServiceServer).DeleteDistributedCacheRegistration(ctx, req.(*DeleteDistributedCacheRegistrationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PlatformService_UpdateDistributedCacheRegistration_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateDistributedCacheRegistrationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PlatformServiceServer).UpdateDistributedCacheRegistration(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PlatformService_UpdateDistributedCacheRegistration_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PlatformServiceServer).UpdateDistributedCacheRegistration(ctx, req.(*UpdateDistributedCacheRegistrationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PlatformService_ListDistributedCacheRegistrationRegions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListDistributedCacheRegistrationRegionsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PlatformServiceServer).ListDistributedCacheRegistrationRegions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PlatformService_ListDistributedCacheRegistrationRegions_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PlatformServiceServer).ListDistributedCacheRegistrationRegions(ctx, req.(*ListDistributedCacheRegistrationRegionsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PlatformService_ListDistributedCacheRegistrationZones_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListDistributedCacheRegistrationZonesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PlatformServiceServer).ListDistributedCacheRegistrationZones(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PlatformService_ListDistributedCacheRegistrationZones_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PlatformServiceServer).ListDistributedCacheRegistrationZones(ctx, req.(*ListDistributedCacheRegistrationZonesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PlatformService_ListDistributedCacheRegistrationClusters_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListDistributedCacheRegistrationClustersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PlatformServiceServer).ListDistributedCacheRegistrationClusters(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PlatformService_ListDistributedCacheRegistrationClusters_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PlatformServiceServer).ListDistributedCacheRegistrationClusters(ctx, req.(*ListDistributedCacheRegistrationClustersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// PlatformService_ServiceDesc is the grpc.ServiceDesc for PlatformService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var PlatformService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "apis.aistudio.platform.v1.PlatformService",
	HandlerType: (*PlatformServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AddOrUpdateKubernetesCluster",
			Handler:    _PlatformService_AddOrUpdateKubernetesCluster_Handler,
		},
		{
			MethodName: "RemoveKubernetesCluster",
			Handler:    _PlatformService_RemoveKubernetesCluster_Handler,
		},
		{
			MethodName: "ListKubernetesClusters",
			Handler:    _PlatformService_ListKubernetesClusters_Handler,
		},
		{
			MethodName: "GetKubernetesCluster",
			Handler:    _PlatformService_GetKubernetesCluster_Handler,
		},
		{
			MethodName: "AddOrUpdateNodeSpecification",
			Handler:    _PlatformService_AddOrUpdateNodeSpecification_Handler,
		},
		{
			MethodName: "DeleteNodeSpecification",
			Handler:    _PlatformService_DeleteNodeSpecification_Handler,
		},
		{
			MethodName: "ListNodeSpecifications",
			Handler:    _PlatformService_ListNodeSpecifications_Handler,
		},
		{
			MethodName: "AddOrUpdateCubeFSCluster",
			Handler:    _PlatformService_AddOrUpdateCubeFSCluster_Handler,
		},
		{
			MethodName: "DeleteCubeFSCluster",
			Handler:    _PlatformService_DeleteCubeFSCluster_Handler,
		},
		{
			MethodName: "CubeFSClusterUpdateStatus",
			Handler:    _PlatformService_CubeFSClusterUpdateStatus_Handler,
		},
		{
			MethodName: "SetCubeFSDashboard",
			Handler:    _PlatformService_SetCubeFSDashboard_Handler,
		},
		{
			MethodName: "ListNodes",
			Handler:    _PlatformService_ListNodes_Handler,
		},
		{
			MethodName: "GetNodesInfoStatistics",
			Handler:    _PlatformService_GetNodesInfoStatistics_Handler,
		},
		{
			MethodName: "AssignNode",
			Handler:    _PlatformService_AssignNode_Handler,
		},
		{
			MethodName: "RecycleNode",
			Handler:    _PlatformService_RecycleNode_Handler,
		},
		{
			MethodName: "AddDragonfly",
			Handler:    _PlatformService_AddDragonfly_Handler,
		},
		{
			MethodName: "SetDragonfly",
			Handler:    _PlatformService_SetDragonfly_Handler,
		},
		{
			MethodName: "DeleteDragonfly",
			Handler:    _PlatformService_DeleteDragonfly_Handler,
		},
		{
			MethodName: "ListDragonfly",
			Handler:    _PlatformService_ListDragonfly_Handler,
		},
		{
			MethodName: "ListDistributedCacheRegistrations",
			Handler:    _PlatformService_ListDistributedCacheRegistrations_Handler,
		},
		{
			MethodName: "CreateDistributedCacheRegistration",
			Handler:    _PlatformService_CreateDistributedCacheRegistration_Handler,
		},
		{
			MethodName: "DeleteDistributedCacheRegistration",
			Handler:    _PlatformService_DeleteDistributedCacheRegistration_Handler,
		},
		{
			MethodName: "UpdateDistributedCacheRegistration",
			Handler:    _PlatformService_UpdateDistributedCacheRegistration_Handler,
		},
		{
			MethodName: "ListDistributedCacheRegistrationRegions",
			Handler:    _PlatformService_ListDistributedCacheRegistrationRegions_Handler,
		},
		{
			MethodName: "ListDistributedCacheRegistrationZones",
			Handler:    _PlatformService_ListDistributedCacheRegistrationZones_Handler,
		},
		{
			MethodName: "ListDistributedCacheRegistrationClusters",
			Handler:    _PlatformService_ListDistributedCacheRegistrationClusters_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "aistudio/platform/v1/platform.proto",
}
