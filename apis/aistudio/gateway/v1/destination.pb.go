// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v5.29.3
// source: aistudio/gateway/v1/destination.proto

package v1

import (
	common "git.lianjia.com/cloudnative/kic/kic-platform/apis/common"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	_ "google.golang.org/protobuf/types/known/durationpb"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	_ "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type TopologySpreadConstraints_LocalityReferenceScope int32

const (
	TopologySpreadConstraints_Region TopologySpreadConstraints_LocalityReferenceScope = 0
	TopologySpreadConstraints_Zone   TopologySpreadConstraints_LocalityReferenceScope = 1
	TopologySpreadConstraints_IDC    TopologySpreadConstraints_LocalityReferenceScope = 2
)

// Enum value maps for TopologySpreadConstraints_LocalityReferenceScope.
var (
	TopologySpreadConstraints_LocalityReferenceScope_name = map[int32]string{
		0: "Region",
		1: "Zone",
		2: "IDC",
	}
	TopologySpreadConstraints_LocalityReferenceScope_value = map[string]int32{
		"Region": 0,
		"Zone":   1,
		"IDC":    2,
	}
)

func (x TopologySpreadConstraints_LocalityReferenceScope) Enum() *TopologySpreadConstraints_LocalityReferenceScope {
	p := new(TopologySpreadConstraints_LocalityReferenceScope)
	*p = x
	return p
}

func (x TopologySpreadConstraints_LocalityReferenceScope) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TopologySpreadConstraints_LocalityReferenceScope) Descriptor() protoreflect.EnumDescriptor {
	return file_aistudio_gateway_v1_destination_proto_enumTypes[0].Descriptor()
}

func (TopologySpreadConstraints_LocalityReferenceScope) Type() protoreflect.EnumType {
	return &file_aistudio_gateway_v1_destination_proto_enumTypes[0]
}

func (x TopologySpreadConstraints_LocalityReferenceScope) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TopologySpreadConstraints_LocalityReferenceScope.Descriptor instead.
func (TopologySpreadConstraints_LocalityReferenceScope) EnumDescriptor() ([]byte, []int) {
	return file_aistudio_gateway_v1_destination_proto_rawDescGZIP(), []int{26, 0}
}

type LoadBalanceStrategy_SchedulingAlgorithm int32

const (
	LoadBalanceStrategy_ROUND_ROBIN      LoadBalanceStrategy_SchedulingAlgorithm = 0 //轮询
	LoadBalanceStrategy_LEAST_CONNECTION LoadBalanceStrategy_SchedulingAlgorithm = 1 //最少连接数
)

// Enum value maps for LoadBalanceStrategy_SchedulingAlgorithm.
var (
	LoadBalanceStrategy_SchedulingAlgorithm_name = map[int32]string{
		0: "ROUND_ROBIN",
		1: "LEAST_CONNECTION",
	}
	LoadBalanceStrategy_SchedulingAlgorithm_value = map[string]int32{
		"ROUND_ROBIN":      0,
		"LEAST_CONNECTION": 1,
	}
)

func (x LoadBalanceStrategy_SchedulingAlgorithm) Enum() *LoadBalanceStrategy_SchedulingAlgorithm {
	p := new(LoadBalanceStrategy_SchedulingAlgorithm)
	*p = x
	return p
}

func (x LoadBalanceStrategy_SchedulingAlgorithm) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LoadBalanceStrategy_SchedulingAlgorithm) Descriptor() protoreflect.EnumDescriptor {
	return file_aistudio_gateway_v1_destination_proto_enumTypes[1].Descriptor()
}

func (LoadBalanceStrategy_SchedulingAlgorithm) Type() protoreflect.EnumType {
	return &file_aistudio_gateway_v1_destination_proto_enumTypes[1]
}

func (x LoadBalanceStrategy_SchedulingAlgorithm) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LoadBalanceStrategy_SchedulingAlgorithm.Descriptor instead.
func (LoadBalanceStrategy_SchedulingAlgorithm) EnumDescriptor() ([]byte, []int) {
	return file_aistudio_gateway_v1_destination_proto_rawDescGZIP(), []int{27, 0}
}

type CreateServiceEntryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Cluster                   string                     `protobuf:"bytes,1,opt,name=cluster,proto3" json:"cluster,omitempty"`
	ServiceName               string                     `protobuf:"bytes,2,opt,name=serviceName,proto3" json:"serviceName,omitempty"`
	WorkspaceName             string                     `protobuf:"bytes,3,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	TopologySpreadConstraints *TopologySpreadConstraints `protobuf:"bytes,4,opt,name=topologySpreadConstraints,proto3" json:"topologySpreadConstraints,omitempty"`
	LoadBalanceStrategy       *LoadBalanceStrategy       `protobuf:"bytes,6,opt,name=loadBalanceStrategy,proto3" json:"loadBalanceStrategy,omitempty"` //负载均衡策略
	Warmup                    *Warmup                    `protobuf:"bytes,7,opt,name=warmup,proto3" json:"warmup,omitempty"`                           //预热时间
	Labels                    map[string]string          `protobuf:"bytes,5,rep,name=labels,proto3" json:"labels,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Description               string                     `protobuf:"bytes,8,opt,name=description,proto3" json:"description,omitempty"`
}

func (x *CreateServiceEntryRequest) Reset() {
	*x = CreateServiceEntryRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_gateway_v1_destination_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateServiceEntryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateServiceEntryRequest) ProtoMessage() {}

func (x *CreateServiceEntryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_gateway_v1_destination_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateServiceEntryRequest.ProtoReflect.Descriptor instead.
func (*CreateServiceEntryRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_gateway_v1_destination_proto_rawDescGZIP(), []int{0}
}

func (x *CreateServiceEntryRequest) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

func (x *CreateServiceEntryRequest) GetServiceName() string {
	if x != nil {
		return x.ServiceName
	}
	return ""
}

func (x *CreateServiceEntryRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *CreateServiceEntryRequest) GetTopologySpreadConstraints() *TopologySpreadConstraints {
	if x != nil {
		return x.TopologySpreadConstraints
	}
	return nil
}

func (x *CreateServiceEntryRequest) GetLoadBalanceStrategy() *LoadBalanceStrategy {
	if x != nil {
		return x.LoadBalanceStrategy
	}
	return nil
}

func (x *CreateServiceEntryRequest) GetWarmup() *Warmup {
	if x != nil {
		return x.Warmup
	}
	return nil
}

func (x *CreateServiceEntryRequest) GetLabels() map[string]string {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *CreateServiceEntryRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

type GetDestinationServiceRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName string `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Name          string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *GetDestinationServiceRequest) Reset() {
	*x = GetDestinationServiceRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_gateway_v1_destination_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDestinationServiceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDestinationServiceRequest) ProtoMessage() {}

func (x *GetDestinationServiceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_gateway_v1_destination_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDestinationServiceRequest.ProtoReflect.Descriptor instead.
func (*GetDestinationServiceRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_gateway_v1_destination_proto_rawDescGZIP(), []int{1}
}

func (x *GetDestinationServiceRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *GetDestinationServiceRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type CreateServiceEntryResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ServiceEntryId string `protobuf:"bytes,1,opt,name=serviceEntryId,proto3" json:"serviceEntryId,omitempty"`
}

func (x *CreateServiceEntryResponse) Reset() {
	*x = CreateServiceEntryResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_gateway_v1_destination_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateServiceEntryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateServiceEntryResponse) ProtoMessage() {}

func (x *CreateServiceEntryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_gateway_v1_destination_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateServiceEntryResponse.ProtoReflect.Descriptor instead.
func (*CreateServiceEntryResponse) Descriptor() ([]byte, []int) {
	return file_aistudio_gateway_v1_destination_proto_rawDescGZIP(), []int{2}
}

func (x *CreateServiceEntryResponse) GetServiceEntryId() string {
	if x != nil {
		return x.ServiceEntryId
	}
	return ""
}

type UpdateServiceEntryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ServiceEntryId            string                     `protobuf:"bytes,1,opt,name=serviceEntryId,proto3" json:"serviceEntryId,omitempty"`
	TopologySpreadConstraints *TopologySpreadConstraints `protobuf:"bytes,2,opt,name=topologySpreadConstraints,proto3" json:"topologySpreadConstraints,omitempty"`
	LoadBalanceStrategy       *LoadBalanceStrategy       `protobuf:"bytes,4,opt,name=loadBalanceStrategy,proto3" json:"loadBalanceStrategy,omitempty"` //负载均衡策略
	Warmup                    *Warmup                    `protobuf:"bytes,5,opt,name=warmup,proto3" json:"warmup,omitempty"`                           //预热时间
	Labels                    map[string]string          `protobuf:"bytes,6,rep,name=labels,proto3" json:"labels,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *UpdateServiceEntryRequest) Reset() {
	*x = UpdateServiceEntryRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_gateway_v1_destination_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateServiceEntryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateServiceEntryRequest) ProtoMessage() {}

func (x *UpdateServiceEntryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_gateway_v1_destination_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateServiceEntryRequest.ProtoReflect.Descriptor instead.
func (*UpdateServiceEntryRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_gateway_v1_destination_proto_rawDescGZIP(), []int{3}
}

func (x *UpdateServiceEntryRequest) GetServiceEntryId() string {
	if x != nil {
		return x.ServiceEntryId
	}
	return ""
}

func (x *UpdateServiceEntryRequest) GetTopologySpreadConstraints() *TopologySpreadConstraints {
	if x != nil {
		return x.TopologySpreadConstraints
	}
	return nil
}

func (x *UpdateServiceEntryRequest) GetLoadBalanceStrategy() *LoadBalanceStrategy {
	if x != nil {
		return x.LoadBalanceStrategy
	}
	return nil
}

func (x *UpdateServiceEntryRequest) GetWarmup() *Warmup {
	if x != nil {
		return x.Warmup
	}
	return nil
}

func (x *UpdateServiceEntryRequest) GetLabels() map[string]string {
	if x != nil {
		return x.Labels
	}
	return nil
}

type GetServiceEntryDetailRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ServiceEntryId string `protobuf:"bytes,1,opt,name=serviceEntryId,proto3" json:"serviceEntryId,omitempty"`
}

func (x *GetServiceEntryDetailRequest) Reset() {
	*x = GetServiceEntryDetailRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_gateway_v1_destination_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetServiceEntryDetailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetServiceEntryDetailRequest) ProtoMessage() {}

func (x *GetServiceEntryDetailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_gateway_v1_destination_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetServiceEntryDetailRequest.ProtoReflect.Descriptor instead.
func (*GetServiceEntryDetailRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_gateway_v1_destination_proto_rawDescGZIP(), []int{4}
}

func (x *GetServiceEntryDetailRequest) GetServiceEntryId() string {
	if x != nil {
		return x.ServiceEntryId
	}
	return ""
}

type DeleteServiceEntryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ServiceEntryId string `protobuf:"bytes,1,opt,name=serviceEntryId,proto3" json:"serviceEntryId,omitempty"`
}

func (x *DeleteServiceEntryRequest) Reset() {
	*x = DeleteServiceEntryRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_gateway_v1_destination_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteServiceEntryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteServiceEntryRequest) ProtoMessage() {}

func (x *DeleteServiceEntryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_gateway_v1_destination_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteServiceEntryRequest.ProtoReflect.Descriptor instead.
func (*DeleteServiceEntryRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_gateway_v1_destination_proto_rawDescGZIP(), []int{5}
}

func (x *DeleteServiceEntryRequest) GetServiceEntryId() string {
	if x != nil {
		return x.ServiceEntryId
	}
	return ""
}

type ListServiceEntriesOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName   string            `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	DestinationName string            `protobuf:"bytes,2,opt,name=destinationName,proto3" json:"destinationName,omitempty"`
	Labels          map[string]string `protobuf:"bytes,3,rep,name=labels,proto3" json:"labels,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *ListServiceEntriesOptions) Reset() {
	*x = ListServiceEntriesOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_gateway_v1_destination_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListServiceEntriesOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListServiceEntriesOptions) ProtoMessage() {}

func (x *ListServiceEntriesOptions) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_gateway_v1_destination_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListServiceEntriesOptions.ProtoReflect.Descriptor instead.
func (*ListServiceEntriesOptions) Descriptor() ([]byte, []int) {
	return file_aistudio_gateway_v1_destination_proto_rawDescGZIP(), []int{6}
}

func (x *ListServiceEntriesOptions) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *ListServiceEntriesOptions) GetDestinationName() string {
	if x != nil {
		return x.DestinationName
	}
	return ""
}

func (x *ListServiceEntriesOptions) GetLabels() map[string]string {
	if x != nil {
		return x.Labels
	}
	return nil
}

type ListServiceEntriesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ServiceEntries []*ServiceEntry `protobuf:"bytes,1,rep,name=serviceEntries,proto3" json:"serviceEntries,omitempty"`
	Total          int32           `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
}

func (x *ListServiceEntriesResponse) Reset() {
	*x = ListServiceEntriesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_gateway_v1_destination_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListServiceEntriesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListServiceEntriesResponse) ProtoMessage() {}

func (x *ListServiceEntriesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_gateway_v1_destination_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListServiceEntriesResponse.ProtoReflect.Descriptor instead.
func (*ListServiceEntriesResponse) Descriptor() ([]byte, []int) {
	return file_aistudio_gateway_v1_destination_proto_rawDescGZIP(), []int{7}
}

func (x *ListServiceEntriesResponse) GetServiceEntries() []*ServiceEntry {
	if x != nil {
		return x.ServiceEntries
	}
	return nil
}

func (x *ListServiceEntriesResponse) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

type ServiceEndpoint struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Addresses      []string                  `protobuf:"bytes,1,rep,name=addresses,proto3" json:"addresses,omitempty"`
	Hostname       string                    `protobuf:"bytes,2,opt,name=hostname,proto3" json:"hostname,omitempty"`
	NodeName       string                    `protobuf:"bytes,3,opt,name=nodeName,proto3" json:"nodeName,omitempty"`
	Zone           string                    `protobuf:"bytes,4,opt,name=zone,proto3" json:"zone,omitempty"`
	Condition      *ServiceEndpointCondition `protobuf:"bytes,5,opt,name=condition,proto3" json:"condition,omitempty"`
	TrafficRemoval bool                      `protobuf:"varint,6,opt,name=trafficRemoval,proto3" json:"trafficRemoval,omitempty"` //是否主动摘流中
}

func (x *ServiceEndpoint) Reset() {
	*x = ServiceEndpoint{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_gateway_v1_destination_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceEndpoint) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceEndpoint) ProtoMessage() {}

func (x *ServiceEndpoint) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_gateway_v1_destination_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceEndpoint.ProtoReflect.Descriptor instead.
func (*ServiceEndpoint) Descriptor() ([]byte, []int) {
	return file_aistudio_gateway_v1_destination_proto_rawDescGZIP(), []int{8}
}

func (x *ServiceEndpoint) GetAddresses() []string {
	if x != nil {
		return x.Addresses
	}
	return nil
}

func (x *ServiceEndpoint) GetHostname() string {
	if x != nil {
		return x.Hostname
	}
	return ""
}

func (x *ServiceEndpoint) GetNodeName() string {
	if x != nil {
		return x.NodeName
	}
	return ""
}

func (x *ServiceEndpoint) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *ServiceEndpoint) GetCondition() *ServiceEndpointCondition {
	if x != nil {
		return x.Condition
	}
	return nil
}

func (x *ServiceEndpoint) GetTrafficRemoval() bool {
	if x != nil {
		return x.TrafficRemoval
	}
	return false
}

type ServiceEndpointCondition struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ready       bool `protobuf:"varint,1,opt,name=ready,proto3" json:"ready,omitempty"`
	Serving     bool `protobuf:"varint,2,opt,name=serving,proto3" json:"serving,omitempty"`
	Terminating bool `protobuf:"varint,3,opt,name=terminating,proto3" json:"terminating,omitempty"`
}

func (x *ServiceEndpointCondition) Reset() {
	*x = ServiceEndpointCondition{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_gateway_v1_destination_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceEndpointCondition) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceEndpointCondition) ProtoMessage() {}

func (x *ServiceEndpointCondition) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_gateway_v1_destination_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceEndpointCondition.ProtoReflect.Descriptor instead.
func (*ServiceEndpointCondition) Descriptor() ([]byte, []int) {
	return file_aistudio_gateway_v1_destination_proto_rawDescGZIP(), []int{9}
}

func (x *ServiceEndpointCondition) GetReady() bool {
	if x != nil {
		return x.Ready
	}
	return false
}

func (x *ServiceEndpointCondition) GetServing() bool {
	if x != nil {
		return x.Serving
	}
	return false
}

func (x *ServiceEndpointCondition) GetTerminating() bool {
	if x != nil {
		return x.Terminating
	}
	return false
}

type ListServiceEntryEndpointResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ServiceEndpoints []*ServiceEndpoint `protobuf:"bytes,1,rep,name=serviceEndpoints,proto3" json:"serviceEndpoints,omitempty"`
}

func (x *ListServiceEntryEndpointResponse) Reset() {
	*x = ListServiceEntryEndpointResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_gateway_v1_destination_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListServiceEntryEndpointResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListServiceEntryEndpointResponse) ProtoMessage() {}

func (x *ListServiceEntryEndpointResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_gateway_v1_destination_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListServiceEntryEndpointResponse.ProtoReflect.Descriptor instead.
func (*ListServiceEntryEndpointResponse) Descriptor() ([]byte, []int) {
	return file_aistudio_gateway_v1_destination_proto_rawDescGZIP(), []int{10}
}

func (x *ListServiceEntryEndpointResponse) GetServiceEndpoints() []*ServiceEndpoint {
	if x != nil {
		return x.ServiceEndpoints
	}
	return nil
}

type ListServiceEntryEndpointRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ServiceEntryId string `protobuf:"bytes,1,opt,name=serviceEntryId,proto3" json:"serviceEntryId,omitempty"`
	WorkspaceName  string `protobuf:"bytes,2,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
}

func (x *ListServiceEntryEndpointRequest) Reset() {
	*x = ListServiceEntryEndpointRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_gateway_v1_destination_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListServiceEntryEndpointRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListServiceEntryEndpointRequest) ProtoMessage() {}

func (x *ListServiceEntryEndpointRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_gateway_v1_destination_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListServiceEntryEndpointRequest.ProtoReflect.Descriptor instead.
func (*ListServiceEntryEndpointRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_gateway_v1_destination_proto_rawDescGZIP(), []int{11}
}

func (x *ListServiceEntryEndpointRequest) GetServiceEntryId() string {
	if x != nil {
		return x.ServiceEntryId
	}
	return ""
}

func (x *ListServiceEntryEndpointRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

type ServiceEndpointTrafficRemovalRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ServiceEntryId string `protobuf:"bytes,1,opt,name=serviceEntryId,proto3" json:"serviceEntryId,omitempty"`
	WorkspaceName  string `protobuf:"bytes,2,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Endpoint       string `protobuf:"bytes,3,opt,name=endpoint,proto3" json:"endpoint,omitempty"`
}

func (x *ServiceEndpointTrafficRemovalRequest) Reset() {
	*x = ServiceEndpointTrafficRemovalRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_gateway_v1_destination_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceEndpointTrafficRemovalRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceEndpointTrafficRemovalRequest) ProtoMessage() {}

func (x *ServiceEndpointTrafficRemovalRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_gateway_v1_destination_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceEndpointTrafficRemovalRequest.ProtoReflect.Descriptor instead.
func (*ServiceEndpointTrafficRemovalRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_gateway_v1_destination_proto_rawDescGZIP(), []int{12}
}

func (x *ServiceEndpointTrafficRemovalRequest) GetServiceEntryId() string {
	if x != nil {
		return x.ServiceEntryId
	}
	return ""
}

func (x *ServiceEndpointTrafficRemovalRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *ServiceEndpointTrafficRemovalRequest) GetEndpoint() string {
	if x != nil {
		return x.Endpoint
	}
	return ""
}

type ServiceEndpointTrafficRecoveryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ServiceEntryId string `protobuf:"bytes,1,opt,name=serviceEntryId,proto3" json:"serviceEntryId,omitempty"`
	WorkspaceName  string `protobuf:"bytes,2,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Endpoint       string `protobuf:"bytes,3,opt,name=endpoint,proto3" json:"endpoint,omitempty"`
}

func (x *ServiceEndpointTrafficRecoveryRequest) Reset() {
	*x = ServiceEndpointTrafficRecoveryRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_gateway_v1_destination_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceEndpointTrafficRecoveryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceEndpointTrafficRecoveryRequest) ProtoMessage() {}

func (x *ServiceEndpointTrafficRecoveryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_gateway_v1_destination_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceEndpointTrafficRecoveryRequest.ProtoReflect.Descriptor instead.
func (*ServiceEndpointTrafficRecoveryRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_gateway_v1_destination_proto_rawDescGZIP(), []int{13}
}

func (x *ServiceEndpointTrafficRecoveryRequest) GetServiceEntryId() string {
	if x != nil {
		return x.ServiceEntryId
	}
	return ""
}

func (x *ServiceEndpointTrafficRecoveryRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *ServiceEndpointTrafficRecoveryRequest) GetEndpoint() string {
	if x != nil {
		return x.Endpoint
	}
	return ""
}

type CreateOrUpdateDestinationServiceRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name            string         `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Description     string         `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
	DisplayName     string         `protobuf:"bytes,3,opt,name=displayName,proto3" json:"displayName,omitempty"`
	WorkspaceName   string         `protobuf:"bytes,4,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Managers        []string       `protobuf:"bytes,5,rep,name=managers,proto3" json:"managers,omitempty"`
	Members         []string       `protobuf:"bytes,6,rep,name=members,proto3" json:"members,omitempty"`
	Ports           []*common.Port `protobuf:"bytes,7,rep,name=ports,proto3" json:"ports,omitempty"`
	ApplicationName string         `protobuf:"bytes,8,opt,name=applicationName,proto3" json:"applicationName,omitempty"` //关联的应用名称
	IsDefault       bool           `protobuf:"varint,9,opt,name=isDefault,proto3" json:"isDefault,omitempty"`            //是否默认创建
}

func (x *CreateOrUpdateDestinationServiceRequest) Reset() {
	*x = CreateOrUpdateDestinationServiceRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_gateway_v1_destination_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateOrUpdateDestinationServiceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateOrUpdateDestinationServiceRequest) ProtoMessage() {}

func (x *CreateOrUpdateDestinationServiceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_gateway_v1_destination_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateOrUpdateDestinationServiceRequest.ProtoReflect.Descriptor instead.
func (*CreateOrUpdateDestinationServiceRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_gateway_v1_destination_proto_rawDescGZIP(), []int{14}
}

func (x *CreateOrUpdateDestinationServiceRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateOrUpdateDestinationServiceRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CreateOrUpdateDestinationServiceRequest) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *CreateOrUpdateDestinationServiceRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *CreateOrUpdateDestinationServiceRequest) GetManagers() []string {
	if x != nil {
		return x.Managers
	}
	return nil
}

func (x *CreateOrUpdateDestinationServiceRequest) GetMembers() []string {
	if x != nil {
		return x.Members
	}
	return nil
}

func (x *CreateOrUpdateDestinationServiceRequest) GetPorts() []*common.Port {
	if x != nil {
		return x.Ports
	}
	return nil
}

func (x *CreateOrUpdateDestinationServiceRequest) GetApplicationName() string {
	if x != nil {
		return x.ApplicationName
	}
	return ""
}

func (x *CreateOrUpdateDestinationServiceRequest) GetIsDefault() bool {
	if x != nil {
		return x.IsDefault
	}
	return false
}

type ListDestinationServiceRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name              string `protobuf:"bytes,6,opt,name=name,proto3" json:"name,omitempty"`
	WorkspaceName     string `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	User              string `protobuf:"bytes,2,opt,name=user,proto3" json:"user,omitempty"`
	Region            string `protobuf:"bytes,3,opt,name=region,proto3" json:"region,omitempty"`
	ApplicationName   string `protobuf:"bytes,4,opt,name=applicationName,proto3" json:"applicationName,omitempty"` //应用名称
	IsDefault         string `protobuf:"bytes,5,opt,name=isDefault,proto3" json:"isDefault,omitempty"`
	FilterAppNotExist bool   `protobuf:"varint,8,opt,name=filterAppNotExist,proto3" json:"filterAppNotExist,omitempty"` // 仅返回关联应用不存在的服务（脏数据清理）
}

func (x *ListDestinationServiceRequest) Reset() {
	*x = ListDestinationServiceRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_gateway_v1_destination_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListDestinationServiceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDestinationServiceRequest) ProtoMessage() {}

func (x *ListDestinationServiceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_gateway_v1_destination_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDestinationServiceRequest.ProtoReflect.Descriptor instead.
func (*ListDestinationServiceRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_gateway_v1_destination_proto_rawDescGZIP(), []int{15}
}

func (x *ListDestinationServiceRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ListDestinationServiceRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *ListDestinationServiceRequest) GetUser() string {
	if x != nil {
		return x.User
	}
	return ""
}

func (x *ListDestinationServiceRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *ListDestinationServiceRequest) GetApplicationName() string {
	if x != nil {
		return x.ApplicationName
	}
	return ""
}

func (x *ListDestinationServiceRequest) GetIsDefault() string {
	if x != nil {
		return x.IsDefault
	}
	return ""
}

func (x *ListDestinationServiceRequest) GetFilterAppNotExist() bool {
	if x != nil {
		return x.FilterAppNotExist
	}
	return false
}

type ListDestinationServiceResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Services []*DestinationService `protobuf:"bytes,1,rep,name=services,proto3" json:"services,omitempty"`
	Total    int32                 `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
}

func (x *ListDestinationServiceResponse) Reset() {
	*x = ListDestinationServiceResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_gateway_v1_destination_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListDestinationServiceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDestinationServiceResponse) ProtoMessage() {}

func (x *ListDestinationServiceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_gateway_v1_destination_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDestinationServiceResponse.ProtoReflect.Descriptor instead.
func (*ListDestinationServiceResponse) Descriptor() ([]byte, []int) {
	return file_aistudio_gateway_v1_destination_proto_rawDescGZIP(), []int{16}
}

func (x *ListDestinationServiceResponse) GetServices() []*DestinationService {
	if x != nil {
		return x.Services
	}
	return nil
}

func (x *ListDestinationServiceResponse) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

type ListServiceEntryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName string `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Gateway       string `protobuf:"bytes,2,opt,name=gateway,proto3" json:"gateway,omitempty"`
}

func (x *ListServiceEntryRequest) Reset() {
	*x = ListServiceEntryRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_gateway_v1_destination_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListServiceEntryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListServiceEntryRequest) ProtoMessage() {}

func (x *ListServiceEntryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_gateway_v1_destination_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListServiceEntryRequest.ProtoReflect.Descriptor instead.
func (*ListServiceEntryRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_gateway_v1_destination_proto_rawDescGZIP(), []int{17}
}

func (x *ListServiceEntryRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *ListServiceEntryRequest) GetGateway() string {
	if x != nil {
		return x.Gateway
	}
	return ""
}

type ListServiceEntryResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ServiceEntries []*ServiceEntry `protobuf:"bytes,1,rep,name=serviceEntries,proto3" json:"serviceEntries,omitempty"`
}

func (x *ListServiceEntryResponse) Reset() {
	*x = ListServiceEntryResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_gateway_v1_destination_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListServiceEntryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListServiceEntryResponse) ProtoMessage() {}

func (x *ListServiceEntryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_gateway_v1_destination_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListServiceEntryResponse.ProtoReflect.Descriptor instead.
func (*ListServiceEntryResponse) Descriptor() ([]byte, []int) {
	return file_aistudio_gateway_v1_destination_proto_rawDescGZIP(), []int{18}
}

func (x *ListServiceEntryResponse) GetServiceEntries() []*ServiceEntry {
	if x != nil {
		return x.ServiceEntries
	}
	return nil
}

type DeleteDestinationServiceRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name          string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	WorkspaceName string `protobuf:"bytes,2,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
}

func (x *DeleteDestinationServiceRequest) Reset() {
	*x = DeleteDestinationServiceRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_gateway_v1_destination_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteDestinationServiceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteDestinationServiceRequest) ProtoMessage() {}

func (x *DeleteDestinationServiceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_gateway_v1_destination_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteDestinationServiceRequest.ProtoReflect.Descriptor instead.
func (*DeleteDestinationServiceRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_gateway_v1_destination_proto_rawDescGZIP(), []int{19}
}

func (x *DeleteDestinationServiceRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DeleteDestinationServiceRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

type GatewayBindingRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name                      string                     `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	WorkspaceName             string                     `protobuf:"bytes,2,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Gateway                   string                     `protobuf:"bytes,3,opt,name=gateway,proto3" json:"gateway,omitempty"`
	TopologySpreadConstraints *TopologySpreadConstraints `protobuf:"bytes,4,opt,name=topologySpreadConstraints,proto3" json:"topologySpreadConstraints,omitempty"` //拓扑约束
	LoadBalanceStrategy       *LoadBalanceStrategy       `protobuf:"bytes,5,opt,name=loadBalanceStrategy,proto3" json:"loadBalanceStrategy,omitempty"`             //负载均衡策略
}

func (x *GatewayBindingRequest) Reset() {
	*x = GatewayBindingRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_gateway_v1_destination_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GatewayBindingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GatewayBindingRequest) ProtoMessage() {}

func (x *GatewayBindingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_gateway_v1_destination_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GatewayBindingRequest.ProtoReflect.Descriptor instead.
func (*GatewayBindingRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_gateway_v1_destination_proto_rawDescGZIP(), []int{20}
}

func (x *GatewayBindingRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GatewayBindingRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *GatewayBindingRequest) GetGateway() string {
	if x != nil {
		return x.Gateway
	}
	return ""
}

func (x *GatewayBindingRequest) GetTopologySpreadConstraints() *TopologySpreadConstraints {
	if x != nil {
		return x.TopologySpreadConstraints
	}
	return nil
}

func (x *GatewayBindingRequest) GetLoadBalanceStrategy() *LoadBalanceStrategy {
	if x != nil {
		return x.LoadBalanceStrategy
	}
	return nil
}

type ServiceEntry struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                        string                     `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	ServiceName               string                     `protobuf:"bytes,2,opt,name=serviceName,proto3" json:"serviceName,omitempty"`
	ClusterName               string                     `protobuf:"bytes,3,opt,name=clusterName,proto3" json:"clusterName,omitempty"`
	WorkspaceName             string                     `protobuf:"bytes,4,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	ServiceEntityStatus       *ServiceEntryStatus        `protobuf:"bytes,5,opt,name=serviceEntityStatus,proto3" json:"serviceEntityStatus,omitempty"`             //服务导入状态
	Ports                     []*common.Port             `protobuf:"bytes,6,rep,name=ports,proto3" json:"ports,omitempty"`                                         //端口信息
	TopologySpreadConstraints *TopologySpreadConstraints `protobuf:"bytes,7,opt,name=topologySpreadConstraints,proto3" json:"topologySpreadConstraints,omitempty"` //拓扑约束
	Creator                   string                     `protobuf:"bytes,8,opt,name=creator,proto3" json:"creator,omitempty"`                                     //创建者
	LoadBalanceStrategy       *LoadBalanceStrategy       `protobuf:"bytes,9,opt,name=loadBalanceStrategy,proto3" json:"loadBalanceStrategy,omitempty"`             //负载均衡策略
	Warmup                    *Warmup                    `protobuf:"bytes,10,opt,name=warmup,proto3" json:"warmup,omitempty"`
}

func (x *ServiceEntry) Reset() {
	*x = ServiceEntry{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_gateway_v1_destination_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceEntry) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceEntry) ProtoMessage() {}

func (x *ServiceEntry) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_gateway_v1_destination_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceEntry.ProtoReflect.Descriptor instead.
func (*ServiceEntry) Descriptor() ([]byte, []int) {
	return file_aistudio_gateway_v1_destination_proto_rawDescGZIP(), []int{21}
}

func (x *ServiceEntry) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ServiceEntry) GetServiceName() string {
	if x != nil {
		return x.ServiceName
	}
	return ""
}

func (x *ServiceEntry) GetClusterName() string {
	if x != nil {
		return x.ClusterName
	}
	return ""
}

func (x *ServiceEntry) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *ServiceEntry) GetServiceEntityStatus() *ServiceEntryStatus {
	if x != nil {
		return x.ServiceEntityStatus
	}
	return nil
}

func (x *ServiceEntry) GetPorts() []*common.Port {
	if x != nil {
		return x.Ports
	}
	return nil
}

func (x *ServiceEntry) GetTopologySpreadConstraints() *TopologySpreadConstraints {
	if x != nil {
		return x.TopologySpreadConstraints
	}
	return nil
}

func (x *ServiceEntry) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *ServiceEntry) GetLoadBalanceStrategy() *LoadBalanceStrategy {
	if x != nil {
		return x.LoadBalanceStrategy
	}
	return nil
}

func (x *ServiceEntry) GetWarmup() *Warmup {
	if x != nil {
		return x.Warmup
	}
	return nil
}

// ServiceImportStatus 服务导入状态
type ServiceEntryStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	State               string                `protobuf:"bytes,1,opt,name=state,proto3" json:"state,omitempty"`
	Message             string                `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	UpdateTime          string                `protobuf:"bytes,3,opt,name=updateTime,proto3" json:"updateTime,omitempty"`
	StartTime           string                `protobuf:"bytes,4,opt,name=startTime,proto3" json:"startTime,omitempty"`
	ServiceRef          *ServiceRef           `protobuf:"bytes,6,opt,name=serviceRef,proto3" json:"serviceRef,omitempty"`                   //多集群服务
	DeploymentGroupRefs []*DeploymentGroupRef `protobuf:"bytes,7,rep,name=deploymentGroupRefs,proto3" json:"deploymentGroupRefs,omitempty"` //部署组
	ApplicationRefs     []string              `protobuf:"bytes,8,rep,name=applicationRefs,proto3" json:"applicationRefs,omitempty"`         //关联的应用
}

func (x *ServiceEntryStatus) Reset() {
	*x = ServiceEntryStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_gateway_v1_destination_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceEntryStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceEntryStatus) ProtoMessage() {}

func (x *ServiceEntryStatus) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_gateway_v1_destination_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceEntryStatus.ProtoReflect.Descriptor instead.
func (*ServiceEntryStatus) Descriptor() ([]byte, []int) {
	return file_aistudio_gateway_v1_destination_proto_rawDescGZIP(), []int{22}
}

func (x *ServiceEntryStatus) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

func (x *ServiceEntryStatus) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ServiceEntryStatus) GetUpdateTime() string {
	if x != nil {
		return x.UpdateTime
	}
	return ""
}

func (x *ServiceEntryStatus) GetStartTime() string {
	if x != nil {
		return x.StartTime
	}
	return ""
}

func (x *ServiceEntryStatus) GetServiceRef() *ServiceRef {
	if x != nil {
		return x.ServiceRef
	}
	return nil
}

func (x *ServiceEntryStatus) GetDeploymentGroupRefs() []*DeploymentGroupRef {
	if x != nil {
		return x.DeploymentGroupRefs
	}
	return nil
}

func (x *ServiceEntryStatus) GetApplicationRefs() []string {
	if x != nil {
		return x.ApplicationRefs
	}
	return nil
}

type DeploymentGroupRef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppName                    string `protobuf:"bytes,1,opt,name=appName,proto3" json:"appName,omitempty"`
	DeploymentGroupName        string `protobuf:"bytes,2,opt,name=deploymentGroupName,proto3" json:"deploymentGroupName,omitempty"`
	DeploymentGroupDescription string `protobuf:"bytes,3,opt,name=deploymentGroupDescription,proto3" json:"deploymentGroupDescription,omitempty"` //部署描述
	DeploymentGroupId          string `protobuf:"bytes,4,opt,name=deploymentGroupId,proto3" json:"deploymentGroupId,omitempty"`
}

func (x *DeploymentGroupRef) Reset() {
	*x = DeploymentGroupRef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_gateway_v1_destination_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeploymentGroupRef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeploymentGroupRef) ProtoMessage() {}

func (x *DeploymentGroupRef) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_gateway_v1_destination_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeploymentGroupRef.ProtoReflect.Descriptor instead.
func (*DeploymentGroupRef) Descriptor() ([]byte, []int) {
	return file_aistudio_gateway_v1_destination_proto_rawDescGZIP(), []int{23}
}

func (x *DeploymentGroupRef) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

func (x *DeploymentGroupRef) GetDeploymentGroupName() string {
	if x != nil {
		return x.DeploymentGroupName
	}
	return ""
}

func (x *DeploymentGroupRef) GetDeploymentGroupDescription() string {
	if x != nil {
		return x.DeploymentGroupDescription
	}
	return ""
}

func (x *DeploymentGroupRef) GetDeploymentGroupId() string {
	if x != nil {
		return x.DeploymentGroupId
	}
	return ""
}

type ServiceRef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ServiceName      string `protobuf:"bytes,1,opt,name=serviceName,proto3" json:"serviceName,omitempty"`
	ServiceNamespace string `protobuf:"bytes,2,opt,name=serviceNamespace,proto3" json:"serviceNamespace,omitempty"`
}

func (x *ServiceRef) Reset() {
	*x = ServiceRef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_gateway_v1_destination_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceRef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceRef) ProtoMessage() {}

func (x *ServiceRef) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_gateway_v1_destination_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceRef.ProtoReflect.Descriptor instead.
func (*ServiceRef) Descriptor() ([]byte, []int) {
	return file_aistudio_gateway_v1_destination_proto_rawDescGZIP(), []int{24}
}

func (x *ServiceRef) GetServiceName() string {
	if x != nil {
		return x.ServiceName
	}
	return ""
}

func (x *ServiceRef) GetServiceNamespace() string {
	if x != nil {
		return x.ServiceNamespace
	}
	return ""
}

type DestinationService struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name            string         `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Description     string         `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
	Creator         string         `protobuf:"bytes,3,opt,name=creator,proto3" json:"creator,omitempty"`
	WorkspaceName   string         `protobuf:"bytes,4,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Managers        []string       `protobuf:"bytes,5,rep,name=managers,proto3" json:"managers,omitempty"`
	Members         []string       `protobuf:"bytes,6,rep,name=members,proto3" json:"members,omitempty"`
	Ports           []*common.Port `protobuf:"bytes,7,rep,name=ports,proto3" json:"ports,omitempty"`
	ApplicationName string         `protobuf:"bytes,8,opt,name=applicationName,proto3" json:"applicationName,omitempty"`
	DisplayName     string         `protobuf:"bytes,9,opt,name=displayName,proto3" json:"displayName,omitempty"`
	IsDefault       bool           `protobuf:"varint,10,opt,name=isDefault,proto3" json:"isDefault,omitempty"` //是否默认创建
	CreateTime      string         `protobuf:"bytes,11,opt,name=createTime,proto3" json:"createTime,omitempty"`
	UpdateTime      string         `protobuf:"bytes,12,opt,name=updateTime,proto3" json:"updateTime,omitempty"`
}

func (x *DestinationService) Reset() {
	*x = DestinationService{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_gateway_v1_destination_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DestinationService) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DestinationService) ProtoMessage() {}

func (x *DestinationService) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_gateway_v1_destination_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DestinationService.ProtoReflect.Descriptor instead.
func (*DestinationService) Descriptor() ([]byte, []int) {
	return file_aistudio_gateway_v1_destination_proto_rawDescGZIP(), []int{25}
}

func (x *DestinationService) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DestinationService) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *DestinationService) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *DestinationService) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *DestinationService) GetManagers() []string {
	if x != nil {
		return x.Managers
	}
	return nil
}

func (x *DestinationService) GetMembers() []string {
	if x != nil {
		return x.Members
	}
	return nil
}

func (x *DestinationService) GetPorts() []*common.Port {
	if x != nil {
		return x.Ports
	}
	return nil
}

func (x *DestinationService) GetApplicationName() string {
	if x != nil {
		return x.ApplicationName
	}
	return ""
}

func (x *DestinationService) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *DestinationService) GetIsDefault() bool {
	if x != nil {
		return x.IsDefault
	}
	return false
}

func (x *DestinationService) GetCreateTime() string {
	if x != nil {
		return x.CreateTime
	}
	return ""
}

func (x *DestinationService) GetUpdateTime() string {
	if x != nil {
		return x.UpdateTime
	}
	return ""
}

// 流量拓扑关系
type TopologySpreadConstraints struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Scope       TopologySpreadConstraints_LocalityReferenceScope `protobuf:"varint,1,opt,name=scope,proto3,enum=apis.aistudio.gateway.v1.TopologySpreadConstraints_LocalityReferenceScope" json:"scope,omitempty"` //就近访问作用域
	MinReplicas int32                                            `protobuf:"varint,2,opt,name=minReplicas,proto3" json:"minReplicas,omitempty"`                                                                    //最小副本数
	Enabled     bool                                             `protobuf:"varint,3,opt,name=enabled,proto3" json:"enabled,omitempty"`                                                                            //是否启用
}

func (x *TopologySpreadConstraints) Reset() {
	*x = TopologySpreadConstraints{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_gateway_v1_destination_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TopologySpreadConstraints) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TopologySpreadConstraints) ProtoMessage() {}

func (x *TopologySpreadConstraints) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_gateway_v1_destination_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TopologySpreadConstraints.ProtoReflect.Descriptor instead.
func (*TopologySpreadConstraints) Descriptor() ([]byte, []int) {
	return file_aistudio_gateway_v1_destination_proto_rawDescGZIP(), []int{26}
}

func (x *TopologySpreadConstraints) GetScope() TopologySpreadConstraints_LocalityReferenceScope {
	if x != nil {
		return x.Scope
	}
	return TopologySpreadConstraints_Region
}

func (x *TopologySpreadConstraints) GetMinReplicas() int32 {
	if x != nil {
		return x.MinReplicas
	}
	return 0
}

func (x *TopologySpreadConstraints) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

// 负载均衡策略
type LoadBalanceStrategy struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SchedulingAlgorithm LoadBalanceStrategy_SchedulingAlgorithm `protobuf:"varint,1,opt,name=schedulingAlgorithm,proto3,enum=apis.aistudio.gateway.v1.LoadBalanceStrategy_SchedulingAlgorithm" json:"schedulingAlgorithm,omitempty"` //调度算法
}

func (x *LoadBalanceStrategy) Reset() {
	*x = LoadBalanceStrategy{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_gateway_v1_destination_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoadBalanceStrategy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoadBalanceStrategy) ProtoMessage() {}

func (x *LoadBalanceStrategy) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_gateway_v1_destination_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoadBalanceStrategy.ProtoReflect.Descriptor instead.
func (*LoadBalanceStrategy) Descriptor() ([]byte, []int) {
	return file_aistudio_gateway_v1_destination_proto_rawDescGZIP(), []int{27}
}

func (x *LoadBalanceStrategy) GetSchedulingAlgorithm() LoadBalanceStrategy_SchedulingAlgorithm {
	if x != nil {
		return x.SchedulingAlgorithm
	}
	return LoadBalanceStrategy_ROUND_ROBIN
}

// 服务预热
type Warmup struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Enabled  bool  `protobuf:"varint,1,opt,name=enabled,proto3" json:"enabled,omitempty"`   //是否启用
	Duration int32 `protobuf:"varint,2,opt,name=duration,proto3" json:"duration,omitempty"` //预热时间
}

func (x *Warmup) Reset() {
	*x = Warmup{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_gateway_v1_destination_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Warmup) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Warmup) ProtoMessage() {}

func (x *Warmup) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_gateway_v1_destination_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Warmup.ProtoReflect.Descriptor instead.
func (*Warmup) Descriptor() ([]byte, []int) {
	return file_aistudio_gateway_v1_destination_proto_rawDescGZIP(), []int{28}
}

func (x *Warmup) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

func (x *Warmup) GetDuration() int32 {
	if x != nil {
		return x.Duration
	}
	return 0
}

var File_aistudio_gateway_v1_destination_proto protoreflect.FileDescriptor

var file_aistudio_gateway_v1_destination_proto_rawDesc = []byte{
	0x0a, 0x25, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2f, 0x67, 0x61, 0x74, 0x65, 0x77,
	0x61, 0x79, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x18, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69,
	0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x76,
	0x31, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e,
	0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x13, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f,
	0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xc1, 0x04, 0x0a, 0x19,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6c, 0x75,
	0x73, 0x74, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6c, 0x75, 0x73,
	0x74, 0x65, 0x72, 0x12, 0x20, 0x0a, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f,
	0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x71, 0x0a, 0x19, 0x74,
	0x6f, 0x70, 0x6f, 0x6c, 0x6f, 0x67, 0x79, 0x53, 0x70, 0x72, 0x65, 0x61, 0x64, 0x43, 0x6f, 0x6e,
	0x73, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x74, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33,
	0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x67,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x6f, 0x70, 0x6f, 0x6c, 0x6f,
	0x67, 0x79, 0x53, 0x70, 0x72, 0x65, 0x61, 0x64, 0x43, 0x6f, 0x6e, 0x73, 0x74, 0x72, 0x61, 0x69,
	0x6e, 0x74, 0x73, 0x52, 0x19, 0x74, 0x6f, 0x70, 0x6f, 0x6c, 0x6f, 0x67, 0x79, 0x53, 0x70, 0x72,
	0x65, 0x61, 0x64, 0x43, 0x6f, 0x6e, 0x73, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x74, 0x73, 0x12, 0x5f,
	0x0a, 0x13, 0x6c, 0x6f, 0x61, 0x64, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x53, 0x74, 0x72,
	0x61, 0x74, 0x65, 0x67, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x61, 0x70,
	0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x61, 0x64, 0x42, 0x61, 0x6c, 0x61, 0x6e,
	0x63, 0x65, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x52, 0x13, 0x6c, 0x6f, 0x61, 0x64,
	0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x12,
	0x38, 0x0a, 0x06, 0x77, 0x61, 0x72, 0x6d, 0x75, 0x70, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x20, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e,
	0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x61, 0x72, 0x6d, 0x75,
	0x70, 0x52, 0x06, 0x77, 0x61, 0x72, 0x6d, 0x75, 0x70, 0x12, 0x57, 0x0a, 0x06, 0x6c, 0x61, 0x62,
	0x65, 0x6c, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3f, 0x2e, 0x61, 0x70, 0x69, 0x73,
	0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x4c,
	0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65,
	0x6c, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x1a, 0x39, 0x0a, 0x0b, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22,
	0x58, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x44, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x44, 0x0a, 0x1a, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x49, 0x64, 0x22,
	0xe5, 0x03, 0x0a, 0x19, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a,
	0x0e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x49, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x49, 0x64, 0x12, 0x71, 0x0a, 0x19, 0x74, 0x6f, 0x70, 0x6f, 0x6c, 0x6f, 0x67,
	0x79, 0x53, 0x70, 0x72, 0x65, 0x61, 0x64, 0x43, 0x6f, 0x6e, 0x73, 0x74, 0x72, 0x61, 0x69, 0x6e,
	0x74, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e,
	0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79,
	0x2e, 0x76, 0x31, 0x2e, 0x54, 0x6f, 0x70, 0x6f, 0x6c, 0x6f, 0x67, 0x79, 0x53, 0x70, 0x72, 0x65,
	0x61, 0x64, 0x43, 0x6f, 0x6e, 0x73, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x74, 0x73, 0x52, 0x19, 0x74,
	0x6f, 0x70, 0x6f, 0x6c, 0x6f, 0x67, 0x79, 0x53, 0x70, 0x72, 0x65, 0x61, 0x64, 0x43, 0x6f, 0x6e,
	0x73, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x74, 0x73, 0x12, 0x5f, 0x0a, 0x13, 0x6c, 0x6f, 0x61, 0x64,
	0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73,
	0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x6f, 0x61, 0x64, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x53, 0x74, 0x72, 0x61,
	0x74, 0x65, 0x67, 0x79, 0x52, 0x13, 0x6c, 0x6f, 0x61, 0x64, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63,
	0x65, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x12, 0x38, 0x0a, 0x06, 0x77, 0x61, 0x72,
	0x6d, 0x75, 0x70, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x73,
	0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x61, 0x72, 0x6d, 0x75, 0x70, 0x52, 0x06, 0x77, 0x61, 0x72,
	0x6d, 0x75, 0x70, 0x12, 0x57, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x06, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x3f, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75,
	0x64, 0x69, 0x6f, 0x2e, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x1a, 0x39, 0x0a, 0x0b,
	0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x46, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x49, 0x64, 0x22,
	0x43, 0x0a, 0x19, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x49, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x49, 0x64, 0x22, 0xff, 0x01, 0x0a, 0x19, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x69, 0x65, 0x73, 0x4f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x28, 0x0a, 0x0f, 0x64, 0x65, 0x73, 0x74,
	0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0f, 0x64, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x57, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x3f, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64,
	0x69, 0x6f, 0x2e, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x69, 0x65, 0x73,
	0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x1a, 0x39, 0x0a, 0x0b, 0x4c,
	0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x82, 0x01, 0x0a, 0x1a, 0x4c, 0x69, 0x73, 0x74, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x69, 0x65, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4e, 0x0a, 0x0e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x45, 0x6e, 0x74, 0x72, 0x69, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e,
	0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x67, 0x61,
	0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x45, 0x6e,
	0x74, 0x72, 0x69, 0x65, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x22, 0xf5, 0x01, 0x0a, 0x0f,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x12,
	0x1c, 0x0a, 0x09, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x09, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x65, 0x73, 0x12, 0x1a, 0x0a,
	0x08, 0x68, 0x6f, 0x73, 0x74, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x68, 0x6f, 0x73, 0x74, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6e, 0x6f, 0x64,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x6f, 0x64,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x50, 0x0a, 0x09, 0x63, 0x6f, 0x6e,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x61,
	0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x67, 0x61, 0x74,
	0x65, 0x77, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x45,
	0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x09, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x26, 0x0a, 0x0e, 0x74,
	0x72, 0x61, 0x66, 0x66, 0x69, 0x63, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x61, 0x6c, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0e, 0x74, 0x72, 0x61, 0x66, 0x66, 0x69, 0x63, 0x52, 0x65, 0x6d, 0x6f,
	0x76, 0x61, 0x6c, 0x22, 0x6c, 0x0a, 0x18, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x45, 0x6e,
	0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x14, 0x0a, 0x05, 0x72, 0x65, 0x61, 0x64, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05,
	0x72, 0x65, 0x61, 0x64, 0x79, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x12,
	0x20, 0x0a, 0x0b, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6e,
	0x67, 0x22, 0x79, 0x0a, 0x20, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x55, 0x0a, 0x10, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x29, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e,
	0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x52, 0x10, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x22, 0x6f, 0x0a, 0x1f,
	0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x26, 0x0a, 0x0e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x49,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x90, 0x01,
	0x0a, 0x24, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x54, 0x72, 0x61, 0x66, 0x66, 0x69, 0x63, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x61, 0x6c, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x49, 0x64, 0x12, 0x24,
	0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x65, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x22, 0x91, 0x01, 0x0a, 0x25, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x45, 0x6e, 0x64, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x66, 0x66, 0x69, 0x63, 0x52, 0x65, 0x63, 0x6f, 0x76,
	0x65, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x49, 0x64, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x65, 0x6e, 0x64, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x22, 0xce, 0x02, 0x0a, 0x27, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f,
	0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61,
	0x79, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x69, 0x73,
	0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a,
	0x0a, 0x08, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x08, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65,
	0x6d, 0x62, 0x65, 0x72, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x6d,
	0x62, 0x65, 0x72, 0x73, 0x12, 0x27, 0x0a, 0x05, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x18, 0x07, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x50, 0x6f, 0x72, 0x74, 0x52, 0x05, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x12, 0x28, 0x0a,
	0x0f, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x69, 0x73, 0x44, 0x65, 0x66,
	0x61, 0x75, 0x6c, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69, 0x73, 0x44, 0x65,
	0x66, 0x61, 0x75, 0x6c, 0x74, 0x22, 0xfb, 0x01, 0x0a, 0x1d, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x65,
	0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x77,
	0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x73, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x75, 0x73, 0x65, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x28, 0x0a,
	0x0f, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x69, 0x73, 0x44, 0x65, 0x66,
	0x61, 0x75, 0x6c, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x69, 0x73, 0x44, 0x65,
	0x66, 0x61, 0x75, 0x6c, 0x74, 0x12, 0x2c, 0x0a, 0x11, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x41,
	0x70, 0x70, 0x4e, 0x6f, 0x74, 0x45, 0x78, 0x69, 0x73, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x11, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x41, 0x70, 0x70, 0x4e, 0x6f, 0x74, 0x45, 0x78,
	0x69, 0x73, 0x74, 0x22, 0x80, 0x01, 0x0a, 0x1e, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x65, 0x73, 0x74,
	0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x48, 0x0a, 0x08, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e,
	0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79,
	0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x08, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x22, 0x59, 0x0a, 0x17, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x67, 0x61, 0x74, 0x65, 0x77,
	0x61, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x22, 0x6a, 0x0a, 0x18, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4e, 0x0a,
	0x0e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x69, 0x65, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73,
	0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x76, 0x31,
	0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x69, 0x65, 0x73, 0x22, 0x5b, 0x0a,
	0x1f, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x44, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72,
	0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xbf, 0x02, 0x0a, 0x15, 0x47,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18,
	0x0a, 0x07, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x12, 0x71, 0x0a, 0x19, 0x74, 0x6f, 0x70, 0x6f,
	0x6c, 0x6f, 0x67, 0x79, 0x53, 0x70, 0x72, 0x65, 0x61, 0x64, 0x43, 0x6f, 0x6e, 0x73, 0x74, 0x72,
	0x61, 0x69, 0x6e, 0x74, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x61, 0x70,
	0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x6f, 0x70, 0x6f, 0x6c, 0x6f, 0x67, 0x79, 0x53,
	0x70, 0x72, 0x65, 0x61, 0x64, 0x43, 0x6f, 0x6e, 0x73, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x74, 0x73,
	0x52, 0x19, 0x74, 0x6f, 0x70, 0x6f, 0x6c, 0x6f, 0x67, 0x79, 0x53, 0x70, 0x72, 0x65, 0x61, 0x64,
	0x43, 0x6f, 0x6e, 0x73, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x74, 0x73, 0x12, 0x5f, 0x0a, 0x13, 0x6c,
	0x6f, 0x61, 0x64, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65,
	0x67, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e,
	0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79,
	0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x61, 0x64, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x53,
	0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x52, 0x13, 0x6c, 0x6f, 0x61, 0x64, 0x42, 0x61, 0x6c,
	0x61, 0x6e, 0x63, 0x65, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x22, 0xb9, 0x04, 0x0a,
	0x0c, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x20, 0x0a,
	0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x20, 0x0a, 0x0b, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x5e, 0x0a, 0x13, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74,
	0x75, 0x64, 0x69, 0x6f, 0x2e, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x13, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x45, 0x6e, 0x74, 0x69, 0x74,
	0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x27, 0x0a, 0x05, 0x70, 0x6f, 0x72, 0x74, 0x73,
	0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x6f, 0x72, 0x74, 0x52, 0x05, 0x70, 0x6f, 0x72, 0x74, 0x73,
	0x12, 0x71, 0x0a, 0x19, 0x74, 0x6f, 0x70, 0x6f, 0x6c, 0x6f, 0x67, 0x79, 0x53, 0x70, 0x72, 0x65,
	0x61, 0x64, 0x43, 0x6f, 0x6e, 0x73, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x74, 0x73, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75,
	0x64, 0x69, 0x6f, 0x2e, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x54,
	0x6f, 0x70, 0x6f, 0x6c, 0x6f, 0x67, 0x79, 0x53, 0x70, 0x72, 0x65, 0x61, 0x64, 0x43, 0x6f, 0x6e,
	0x73, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x74, 0x73, 0x52, 0x19, 0x74, 0x6f, 0x70, 0x6f, 0x6c, 0x6f,
	0x67, 0x79, 0x53, 0x70, 0x72, 0x65, 0x61, 0x64, 0x43, 0x6f, 0x6e, 0x73, 0x74, 0x72, 0x61, 0x69,
	0x6e, 0x74, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x5f, 0x0a,
	0x13, 0x6c, 0x6f, 0x61, 0x64, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x53, 0x74, 0x72, 0x61,
	0x74, 0x65, 0x67, 0x79, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x61, 0x70, 0x69,
	0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x67, 0x61, 0x74, 0x65, 0x77,
	0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x61, 0x64, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63,
	0x65, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x52, 0x13, 0x6c, 0x6f, 0x61, 0x64, 0x42,
	0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x12, 0x38,
	0x0a, 0x06, 0x77, 0x61, 0x72, 0x6d, 0x75, 0x70, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20,
	0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x67,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x61, 0x72, 0x6d, 0x75, 0x70,
	0x52, 0x06, 0x77, 0x61, 0x72, 0x6d, 0x75, 0x70, 0x22, 0xd2, 0x02, 0x0a, 0x12, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12,
	0x1e, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x1c, 0x0a, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x44, 0x0a,
	0x0a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x66, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69,
	0x6f, 0x2e, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x66, 0x52, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x52, 0x65, 0x66, 0x12, 0x5e, 0x0a, 0x13, 0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x66, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x2c, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f,
	0x2e, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x70, 0x6c,
	0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x66, 0x52, 0x13,
	0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52,
	0x65, 0x66, 0x73, 0x12, 0x28, 0x0a, 0x0f, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x66, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0f, 0x61, 0x70,
	0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x66, 0x73, 0x22, 0xce, 0x01,
	0x0a, 0x12, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x52, 0x65, 0x66, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x30,
	0x0a, 0x13, 0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x64, 0x65, 0x70,
	0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x3e, 0x0a, 0x1a, 0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x1a, 0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x2c, 0x0a, 0x11, 0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x64, 0x65, 0x70,
	0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x22, 0x5a,
	0x0a, 0x0a, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x66, 0x12, 0x20, 0x0a, 0x0b,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2a,
	0x0a, 0x10, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x22, 0x93, 0x03, 0x0a, 0x12, 0x44,
	0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f,
	0x72, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x72, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x72, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x18, 0x06,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x12, 0x27, 0x0a,
	0x05, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x61,
	0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x6f, 0x72, 0x74, 0x52,
	0x05, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x12, 0x28, 0x0a, 0x0f, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0f, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x20, 0x0a, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x69, 0x73, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69, 0x73, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74,
	0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x1e, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65,
	0x22, 0xf2, 0x01, 0x0a, 0x19, 0x54, 0x6f, 0x70, 0x6f, 0x6c, 0x6f, 0x67, 0x79, 0x53, 0x70, 0x72,
	0x65, 0x61, 0x64, 0x43, 0x6f, 0x6e, 0x73, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x74, 0x73, 0x12, 0x60,
	0x0a, 0x05, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x4a, 0x2e,
	0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x67, 0x61,
	0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x6f, 0x70, 0x6f, 0x6c, 0x6f, 0x67,
	0x79, 0x53, 0x70, 0x72, 0x65, 0x61, 0x64, 0x43, 0x6f, 0x6e, 0x73, 0x74, 0x72, 0x61, 0x69, 0x6e,
	0x74, 0x73, 0x2e, 0x4c, 0x6f, 0x63, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x65, 0x66, 0x65, 0x72,
	0x65, 0x6e, 0x63, 0x65, 0x53, 0x63, 0x6f, 0x70, 0x65, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x70, 0x65,
	0x12, 0x20, 0x0a, 0x0b, 0x6d, 0x69, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x73, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x6d, 0x69, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x69, 0x63,
	0x61, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x22, 0x37, 0x0a, 0x16,
	0x4c, 0x6f, 0x63, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63,
	0x65, 0x53, 0x63, 0x6f, 0x70, 0x65, 0x12, 0x0a, 0x0a, 0x06, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e,
	0x10, 0x00, 0x12, 0x08, 0x0a, 0x04, 0x5a, 0x6f, 0x6e, 0x65, 0x10, 0x01, 0x12, 0x07, 0x0a, 0x03,
	0x49, 0x44, 0x43, 0x10, 0x02, 0x22, 0xc8, 0x01, 0x0a, 0x13, 0x4c, 0x6f, 0x61, 0x64, 0x42, 0x61,
	0x6c, 0x61, 0x6e, 0x63, 0x65, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x12, 0x73, 0x0a,
	0x13, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x69, 0x6e, 0x67, 0x41, 0x6c, 0x67, 0x6f, 0x72,
	0x69, 0x74, 0x68, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x41, 0x2e, 0x61, 0x70, 0x69,
	0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x67, 0x61, 0x74, 0x65, 0x77,
	0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x61, 0x64, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63,
	0x65, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75,
	0x6c, 0x69, 0x6e, 0x67, 0x41, 0x6c, 0x67, 0x6f, 0x72, 0x69, 0x74, 0x68, 0x6d, 0x52, 0x13, 0x73,
	0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x69, 0x6e, 0x67, 0x41, 0x6c, 0x67, 0x6f, 0x72, 0x69, 0x74,
	0x68, 0x6d, 0x22, 0x3c, 0x0a, 0x13, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x69, 0x6e, 0x67,
	0x41, 0x6c, 0x67, 0x6f, 0x72, 0x69, 0x74, 0x68, 0x6d, 0x12, 0x0f, 0x0a, 0x0b, 0x52, 0x4f, 0x55,
	0x4e, 0x44, 0x5f, 0x52, 0x4f, 0x42, 0x49, 0x4e, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x4c, 0x45,
	0x41, 0x53, 0x54, 0x5f, 0x43, 0x4f, 0x4e, 0x4e, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x01,
	0x22, 0x3e, 0x0a, 0x06, 0x57, 0x61, 0x72, 0x6d, 0x75, 0x70, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x65, 0x6e, 0x61,
	0x62, 0x6c, 0x65, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x32, 0xcd, 0x12, 0x0a, 0x10, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x79, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0xb1, 0x01, 0x0a, 0x18, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x44, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x12, 0x41, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64,
	0x69, 0x6f, 0x2e, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x65, 0x73, 0x74,
	0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x3a, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x34, 0x3a, 0x01, 0x2a, 0x22, 0x2f, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f,
	0x76, 0x31, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2f, 0x7b, 0x77, 0x6f,
	0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x64, 0x65, 0x73,
	0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0xb8, 0x01, 0x0a, 0x18, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x44, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x41, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69,
	0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x44, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74,
	0x79, 0x22, 0x41, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x3b, 0x3a, 0x01, 0x2a, 0x1a, 0x36, 0x2f, 0x61,
	0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x2f, 0x7b, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d,
	0x2f, 0x64, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x6e,
	0x61, 0x6d, 0x65, 0x7d, 0x12, 0xbd, 0x01, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x44, 0x65, 0x73, 0x74,
	0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x36,
	0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x67,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x65, 0x73,
	0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2c, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69,
	0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x76,
	0x31, 0x2e, 0x44, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x22, 0x3e, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x38, 0x12, 0x36, 0x2f, 0x61,
	0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x2f, 0x7b, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d,
	0x2f, 0x64, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x6e,
	0x61, 0x6d, 0x65, 0x7d, 0x12, 0xad, 0x01, 0x0a, 0x18, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x44,
	0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x12, 0x39, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69,
	0x6f, 0x2e, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x44, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x22, 0x3e, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x38, 0x2a, 0x36, 0x2f, 0x61,
	0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x2f, 0x7b, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d,
	0x2f, 0x64, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x6e,
	0x61, 0x6d, 0x65, 0x7d, 0x12, 0xa4, 0x01, 0x0a, 0x12, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x33, 0x2e, 0x61, 0x70,
	0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x34, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f,
	0x2e, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x23, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1d, 0x3a, 0x01,
	0x2a, 0x22, 0x18, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2d, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x65, 0x73, 0x12, 0x97, 0x01, 0x0a, 0x12,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x33, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64,
	0x69, 0x6f, 0x2e, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22,
	0x34, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2e, 0x3a, 0x01, 0x2a, 0x1a, 0x29, 0x2f, 0x61, 0x70, 0x69,
	0x73, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2d, 0x65, 0x6e, 0x74,
	0x72, 0x69, 0x65, 0x73, 0x2f, 0x7b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x49, 0x64, 0x7d, 0x12, 0x94, 0x01, 0x0a, 0x12, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x33, 0x2e, 0x61,
	0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x67, 0x61, 0x74,
	0x65, 0x77, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x31, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x2b, 0x2a, 0x29, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2d, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x65, 0x73, 0x2f, 0x7b, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x49, 0x64, 0x7d, 0x12, 0xa1, 0x01, 0x0a,
	0x12, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x45, 0x6e, 0x74, 0x72,
	0x69, 0x65, 0x73, 0x12, 0x33, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75,
	0x64, 0x69, 0x6f, 0x2e, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x69, 0x65,
	0x73, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x1a, 0x34, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e,
	0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79,
	0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x45,
	0x6e, 0x74, 0x72, 0x69, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x20,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1a, 0x12, 0x18, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31,
	0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2d, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x65, 0x73,
	0x12, 0xaa, 0x01, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x36, 0x2e, 0x61, 0x70, 0x69,
	0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x67, 0x61, 0x74, 0x65, 0x77,
	0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64,
	0x69, 0x6f, 0x2e, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x22, 0x31, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x2b, 0x12, 0x29, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2d, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x65, 0x73, 0x2f, 0x7b, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x49, 0x64, 0x7d, 0x12, 0xc5, 0x01,
	0x0a, 0x17, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x12, 0x37, 0x2e, 0x61, 0x70, 0x69, 0x73,
	0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x38, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64,
	0x69, 0x6f, 0x2e, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x44, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x37, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x31, 0x12, 0x2f, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x77,
	0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2f, 0x7b, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x64, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0xc8, 0x01, 0x0a, 0x1d, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x66, 0x66, 0x69, 0x63,
	0x52, 0x65, 0x6d, 0x6f, 0x76, 0x61, 0x6c, 0x12, 0x3e, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61,
	0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x54, 0x72, 0x61, 0x66, 0x66, 0x69, 0x63, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x61, 0x6c,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22,
	0x4f, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x49, 0x3a, 0x01, 0x2a, 0x22, 0x44, 0x2f, 0x61, 0x70, 0x69,
	0x73, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2d, 0x65, 0x6e, 0x74,
	0x72, 0x69, 0x65, 0x73, 0x2f, 0x7b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x49, 0x64, 0x7d, 0x2f, 0x7b, 0x65, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x7d,
	0x2f, 0x74, 0x72, 0x61, 0x66, 0x66, 0x69, 0x63, 0x2f, 0x72, 0x65, 0x6d, 0x6f, 0x76, 0x61, 0x6c,
	0x12, 0xcb, 0x01, 0x0a, 0x1e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x45, 0x6e, 0x64, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x66, 0x66, 0x69, 0x63, 0x52, 0x65, 0x63, 0x6f, 0x76,
	0x65, 0x72, 0x79, 0x12, 0x3f, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75,
	0x64, 0x69, 0x6f, 0x2e, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x54, 0x72,
	0x61, 0x66, 0x66, 0x69, 0x63, 0x52, 0x65, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x79, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x50, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x4a, 0x3a, 0x01, 0x2a, 0x22, 0x45, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76,
	0x31, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2d, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x65,
	0x73, 0x2f, 0x7b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x49,
	0x64, 0x7d, 0x2f, 0x7b, 0x65, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x7d, 0x2f, 0x74, 0x72,
	0x61, 0x66, 0x66, 0x69, 0x63, 0x2f, 0x72, 0x65, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x79, 0x12, 0xcf,
	0x01, 0x0a, 0x19, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x12, 0x39, 0x2e, 0x61,
	0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x67, 0x61, 0x74,
	0x65, 0x77, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3a, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61,
	0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x3b, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x35, 0x12, 0x33, 0x2f, 0x61, 0x70,
	0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2d, 0x65, 0x6e,
	0x74, 0x72, 0x69, 0x65, 0x73, 0x2f, 0x7b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x49, 0x64, 0x7d, 0x2f, 0x65, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73,
	0x42, 0x4a, 0x5a, 0x48, 0x67, 0x69, 0x74, 0x2e, 0x6c, 0x69, 0x61, 0x6e, 0x6a, 0x69, 0x61, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x2f,
	0x6b, 0x69, 0x63, 0x2f, 0x6b, 0x69, 0x63, 0x2d, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d,
	0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2f, 0x67,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_aistudio_gateway_v1_destination_proto_rawDescOnce sync.Once
	file_aistudio_gateway_v1_destination_proto_rawDescData = file_aistudio_gateway_v1_destination_proto_rawDesc
)

func file_aistudio_gateway_v1_destination_proto_rawDescGZIP() []byte {
	file_aistudio_gateway_v1_destination_proto_rawDescOnce.Do(func() {
		file_aistudio_gateway_v1_destination_proto_rawDescData = protoimpl.X.CompressGZIP(file_aistudio_gateway_v1_destination_proto_rawDescData)
	})
	return file_aistudio_gateway_v1_destination_proto_rawDescData
}

var file_aistudio_gateway_v1_destination_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_aistudio_gateway_v1_destination_proto_msgTypes = make([]protoimpl.MessageInfo, 32)
var file_aistudio_gateway_v1_destination_proto_goTypes = []any{
	(TopologySpreadConstraints_LocalityReferenceScope)(0), // 0: apis.aistudio.gateway.v1.TopologySpreadConstraints.LocalityReferenceScope
	(LoadBalanceStrategy_SchedulingAlgorithm)(0),          // 1: apis.aistudio.gateway.v1.LoadBalanceStrategy.SchedulingAlgorithm
	(*CreateServiceEntryRequest)(nil),                     // 2: apis.aistudio.gateway.v1.CreateServiceEntryRequest
	(*GetDestinationServiceRequest)(nil),                  // 3: apis.aistudio.gateway.v1.GetDestinationServiceRequest
	(*CreateServiceEntryResponse)(nil),                    // 4: apis.aistudio.gateway.v1.CreateServiceEntryResponse
	(*UpdateServiceEntryRequest)(nil),                     // 5: apis.aistudio.gateway.v1.UpdateServiceEntryRequest
	(*GetServiceEntryDetailRequest)(nil),                  // 6: apis.aistudio.gateway.v1.GetServiceEntryDetailRequest
	(*DeleteServiceEntryRequest)(nil),                     // 7: apis.aistudio.gateway.v1.DeleteServiceEntryRequest
	(*ListServiceEntriesOptions)(nil),                     // 8: apis.aistudio.gateway.v1.ListServiceEntriesOptions
	(*ListServiceEntriesResponse)(nil),                    // 9: apis.aistudio.gateway.v1.ListServiceEntriesResponse
	(*ServiceEndpoint)(nil),                               // 10: apis.aistudio.gateway.v1.ServiceEndpoint
	(*ServiceEndpointCondition)(nil),                      // 11: apis.aistudio.gateway.v1.ServiceEndpointCondition
	(*ListServiceEntryEndpointResponse)(nil),              // 12: apis.aistudio.gateway.v1.ListServiceEntryEndpointResponse
	(*ListServiceEntryEndpointRequest)(nil),               // 13: apis.aistudio.gateway.v1.ListServiceEntryEndpointRequest
	(*ServiceEndpointTrafficRemovalRequest)(nil),          // 14: apis.aistudio.gateway.v1.ServiceEndpointTrafficRemovalRequest
	(*ServiceEndpointTrafficRecoveryRequest)(nil),         // 15: apis.aistudio.gateway.v1.ServiceEndpointTrafficRecoveryRequest
	(*CreateOrUpdateDestinationServiceRequest)(nil),       // 16: apis.aistudio.gateway.v1.CreateOrUpdateDestinationServiceRequest
	(*ListDestinationServiceRequest)(nil),                 // 17: apis.aistudio.gateway.v1.ListDestinationServiceRequest
	(*ListDestinationServiceResponse)(nil),                // 18: apis.aistudio.gateway.v1.ListDestinationServiceResponse
	(*ListServiceEntryRequest)(nil),                       // 19: apis.aistudio.gateway.v1.ListServiceEntryRequest
	(*ListServiceEntryResponse)(nil),                      // 20: apis.aistudio.gateway.v1.ListServiceEntryResponse
	(*DeleteDestinationServiceRequest)(nil),               // 21: apis.aistudio.gateway.v1.DeleteDestinationServiceRequest
	(*GatewayBindingRequest)(nil),                         // 22: apis.aistudio.gateway.v1.GatewayBindingRequest
	(*ServiceEntry)(nil),                                  // 23: apis.aistudio.gateway.v1.ServiceEntry
	(*ServiceEntryStatus)(nil),                            // 24: apis.aistudio.gateway.v1.ServiceEntryStatus
	(*DeploymentGroupRef)(nil),                            // 25: apis.aistudio.gateway.v1.DeploymentGroupRef
	(*ServiceRef)(nil),                                    // 26: apis.aistudio.gateway.v1.ServiceRef
	(*DestinationService)(nil),                            // 27: apis.aistudio.gateway.v1.DestinationService
	(*TopologySpreadConstraints)(nil),                     // 28: apis.aistudio.gateway.v1.TopologySpreadConstraints
	(*LoadBalanceStrategy)(nil),                           // 29: apis.aistudio.gateway.v1.LoadBalanceStrategy
	(*Warmup)(nil),                                        // 30: apis.aistudio.gateway.v1.Warmup
	nil,                                                   // 31: apis.aistudio.gateway.v1.CreateServiceEntryRequest.LabelsEntry
	nil,                                                   // 32: apis.aistudio.gateway.v1.UpdateServiceEntryRequest.LabelsEntry
	nil,                                                   // 33: apis.aistudio.gateway.v1.ListServiceEntriesOptions.LabelsEntry
	(*common.Port)(nil),                                   // 34: apis.common.Port
	(*emptypb.Empty)(nil),                                 // 35: google.protobuf.Empty
}
var file_aistudio_gateway_v1_destination_proto_depIdxs = []int32{
	28, // 0: apis.aistudio.gateway.v1.CreateServiceEntryRequest.topologySpreadConstraints:type_name -> apis.aistudio.gateway.v1.TopologySpreadConstraints
	29, // 1: apis.aistudio.gateway.v1.CreateServiceEntryRequest.loadBalanceStrategy:type_name -> apis.aistudio.gateway.v1.LoadBalanceStrategy
	30, // 2: apis.aistudio.gateway.v1.CreateServiceEntryRequest.warmup:type_name -> apis.aistudio.gateway.v1.Warmup
	31, // 3: apis.aistudio.gateway.v1.CreateServiceEntryRequest.labels:type_name -> apis.aistudio.gateway.v1.CreateServiceEntryRequest.LabelsEntry
	28, // 4: apis.aistudio.gateway.v1.UpdateServiceEntryRequest.topologySpreadConstraints:type_name -> apis.aistudio.gateway.v1.TopologySpreadConstraints
	29, // 5: apis.aistudio.gateway.v1.UpdateServiceEntryRequest.loadBalanceStrategy:type_name -> apis.aistudio.gateway.v1.LoadBalanceStrategy
	30, // 6: apis.aistudio.gateway.v1.UpdateServiceEntryRequest.warmup:type_name -> apis.aistudio.gateway.v1.Warmup
	32, // 7: apis.aistudio.gateway.v1.UpdateServiceEntryRequest.labels:type_name -> apis.aistudio.gateway.v1.UpdateServiceEntryRequest.LabelsEntry
	33, // 8: apis.aistudio.gateway.v1.ListServiceEntriesOptions.labels:type_name -> apis.aistudio.gateway.v1.ListServiceEntriesOptions.LabelsEntry
	23, // 9: apis.aistudio.gateway.v1.ListServiceEntriesResponse.serviceEntries:type_name -> apis.aistudio.gateway.v1.ServiceEntry
	11, // 10: apis.aistudio.gateway.v1.ServiceEndpoint.condition:type_name -> apis.aistudio.gateway.v1.ServiceEndpointCondition
	10, // 11: apis.aistudio.gateway.v1.ListServiceEntryEndpointResponse.serviceEndpoints:type_name -> apis.aistudio.gateway.v1.ServiceEndpoint
	34, // 12: apis.aistudio.gateway.v1.CreateOrUpdateDestinationServiceRequest.ports:type_name -> apis.common.Port
	27, // 13: apis.aistudio.gateway.v1.ListDestinationServiceResponse.services:type_name -> apis.aistudio.gateway.v1.DestinationService
	23, // 14: apis.aistudio.gateway.v1.ListServiceEntryResponse.serviceEntries:type_name -> apis.aistudio.gateway.v1.ServiceEntry
	28, // 15: apis.aistudio.gateway.v1.GatewayBindingRequest.topologySpreadConstraints:type_name -> apis.aistudio.gateway.v1.TopologySpreadConstraints
	29, // 16: apis.aistudio.gateway.v1.GatewayBindingRequest.loadBalanceStrategy:type_name -> apis.aistudio.gateway.v1.LoadBalanceStrategy
	24, // 17: apis.aistudio.gateway.v1.ServiceEntry.serviceEntityStatus:type_name -> apis.aistudio.gateway.v1.ServiceEntryStatus
	34, // 18: apis.aistudio.gateway.v1.ServiceEntry.ports:type_name -> apis.common.Port
	28, // 19: apis.aistudio.gateway.v1.ServiceEntry.topologySpreadConstraints:type_name -> apis.aistudio.gateway.v1.TopologySpreadConstraints
	29, // 20: apis.aistudio.gateway.v1.ServiceEntry.loadBalanceStrategy:type_name -> apis.aistudio.gateway.v1.LoadBalanceStrategy
	30, // 21: apis.aistudio.gateway.v1.ServiceEntry.warmup:type_name -> apis.aistudio.gateway.v1.Warmup
	26, // 22: apis.aistudio.gateway.v1.ServiceEntryStatus.serviceRef:type_name -> apis.aistudio.gateway.v1.ServiceRef
	25, // 23: apis.aistudio.gateway.v1.ServiceEntryStatus.deploymentGroupRefs:type_name -> apis.aistudio.gateway.v1.DeploymentGroupRef
	34, // 24: apis.aistudio.gateway.v1.DestinationService.ports:type_name -> apis.common.Port
	0,  // 25: apis.aistudio.gateway.v1.TopologySpreadConstraints.scope:type_name -> apis.aistudio.gateway.v1.TopologySpreadConstraints.LocalityReferenceScope
	1,  // 26: apis.aistudio.gateway.v1.LoadBalanceStrategy.schedulingAlgorithm:type_name -> apis.aistudio.gateway.v1.LoadBalanceStrategy.SchedulingAlgorithm
	16, // 27: apis.aistudio.gateway.v1.DiscoveryService.CreateDestinationService:input_type -> apis.aistudio.gateway.v1.CreateOrUpdateDestinationServiceRequest
	16, // 28: apis.aistudio.gateway.v1.DiscoveryService.UpdateDestinationService:input_type -> apis.aistudio.gateway.v1.CreateOrUpdateDestinationServiceRequest
	3,  // 29: apis.aistudio.gateway.v1.DiscoveryService.GetDestinationService:input_type -> apis.aistudio.gateway.v1.GetDestinationServiceRequest
	21, // 30: apis.aistudio.gateway.v1.DiscoveryService.DeleteDestinationService:input_type -> apis.aistudio.gateway.v1.DeleteDestinationServiceRequest
	2,  // 31: apis.aistudio.gateway.v1.DiscoveryService.CreateServiceEntry:input_type -> apis.aistudio.gateway.v1.CreateServiceEntryRequest
	5,  // 32: apis.aistudio.gateway.v1.DiscoveryService.UpdateServiceEntry:input_type -> apis.aistudio.gateway.v1.UpdateServiceEntryRequest
	7,  // 33: apis.aistudio.gateway.v1.DiscoveryService.DeleteServiceEntry:input_type -> apis.aistudio.gateway.v1.DeleteServiceEntryRequest
	8,  // 34: apis.aistudio.gateway.v1.DiscoveryService.ListServiceEntries:input_type -> apis.aistudio.gateway.v1.ListServiceEntriesOptions
	6,  // 35: apis.aistudio.gateway.v1.DiscoveryService.GetServiceEntryDetail:input_type -> apis.aistudio.gateway.v1.GetServiceEntryDetailRequest
	17, // 36: apis.aistudio.gateway.v1.DiscoveryService.ListDestinationServices:input_type -> apis.aistudio.gateway.v1.ListDestinationServiceRequest
	14, // 37: apis.aistudio.gateway.v1.DiscoveryService.ServiceEndpointTrafficRemoval:input_type -> apis.aistudio.gateway.v1.ServiceEndpointTrafficRemovalRequest
	15, // 38: apis.aistudio.gateway.v1.DiscoveryService.ServiceEndpointTrafficRecovery:input_type -> apis.aistudio.gateway.v1.ServiceEndpointTrafficRecoveryRequest
	13, // 39: apis.aistudio.gateway.v1.DiscoveryService.ListServiceEntryEndpoints:input_type -> apis.aistudio.gateway.v1.ListServiceEntryEndpointRequest
	35, // 40: apis.aistudio.gateway.v1.DiscoveryService.CreateDestinationService:output_type -> google.protobuf.Empty
	35, // 41: apis.aistudio.gateway.v1.DiscoveryService.UpdateDestinationService:output_type -> google.protobuf.Empty
	27, // 42: apis.aistudio.gateway.v1.DiscoveryService.GetDestinationService:output_type -> apis.aistudio.gateway.v1.DestinationService
	35, // 43: apis.aistudio.gateway.v1.DiscoveryService.DeleteDestinationService:output_type -> google.protobuf.Empty
	4,  // 44: apis.aistudio.gateway.v1.DiscoveryService.CreateServiceEntry:output_type -> apis.aistudio.gateway.v1.CreateServiceEntryResponse
	35, // 45: apis.aistudio.gateway.v1.DiscoveryService.UpdateServiceEntry:output_type -> google.protobuf.Empty
	35, // 46: apis.aistudio.gateway.v1.DiscoveryService.DeleteServiceEntry:output_type -> google.protobuf.Empty
	9,  // 47: apis.aistudio.gateway.v1.DiscoveryService.ListServiceEntries:output_type -> apis.aistudio.gateway.v1.ListServiceEntriesResponse
	23, // 48: apis.aistudio.gateway.v1.DiscoveryService.GetServiceEntryDetail:output_type -> apis.aistudio.gateway.v1.ServiceEntry
	18, // 49: apis.aistudio.gateway.v1.DiscoveryService.ListDestinationServices:output_type -> apis.aistudio.gateway.v1.ListDestinationServiceResponse
	35, // 50: apis.aistudio.gateway.v1.DiscoveryService.ServiceEndpointTrafficRemoval:output_type -> google.protobuf.Empty
	35, // 51: apis.aistudio.gateway.v1.DiscoveryService.ServiceEndpointTrafficRecovery:output_type -> google.protobuf.Empty
	12, // 52: apis.aistudio.gateway.v1.DiscoveryService.ListServiceEntryEndpoints:output_type -> apis.aistudio.gateway.v1.ListServiceEntryEndpointResponse
	40, // [40:53] is the sub-list for method output_type
	27, // [27:40] is the sub-list for method input_type
	27, // [27:27] is the sub-list for extension type_name
	27, // [27:27] is the sub-list for extension extendee
	0,  // [0:27] is the sub-list for field type_name
}

func init() { file_aistudio_gateway_v1_destination_proto_init() }
func file_aistudio_gateway_v1_destination_proto_init() {
	if File_aistudio_gateway_v1_destination_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_aistudio_gateway_v1_destination_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*CreateServiceEntryRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_gateway_v1_destination_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*GetDestinationServiceRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_gateway_v1_destination_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*CreateServiceEntryResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_gateway_v1_destination_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*UpdateServiceEntryRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_gateway_v1_destination_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*GetServiceEntryDetailRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_gateway_v1_destination_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*DeleteServiceEntryRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_gateway_v1_destination_proto_msgTypes[6].Exporter = func(v any, i int) any {
			switch v := v.(*ListServiceEntriesOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_gateway_v1_destination_proto_msgTypes[7].Exporter = func(v any, i int) any {
			switch v := v.(*ListServiceEntriesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_gateway_v1_destination_proto_msgTypes[8].Exporter = func(v any, i int) any {
			switch v := v.(*ServiceEndpoint); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_gateway_v1_destination_proto_msgTypes[9].Exporter = func(v any, i int) any {
			switch v := v.(*ServiceEndpointCondition); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_gateway_v1_destination_proto_msgTypes[10].Exporter = func(v any, i int) any {
			switch v := v.(*ListServiceEntryEndpointResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_gateway_v1_destination_proto_msgTypes[11].Exporter = func(v any, i int) any {
			switch v := v.(*ListServiceEntryEndpointRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_gateway_v1_destination_proto_msgTypes[12].Exporter = func(v any, i int) any {
			switch v := v.(*ServiceEndpointTrafficRemovalRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_gateway_v1_destination_proto_msgTypes[13].Exporter = func(v any, i int) any {
			switch v := v.(*ServiceEndpointTrafficRecoveryRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_gateway_v1_destination_proto_msgTypes[14].Exporter = func(v any, i int) any {
			switch v := v.(*CreateOrUpdateDestinationServiceRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_gateway_v1_destination_proto_msgTypes[15].Exporter = func(v any, i int) any {
			switch v := v.(*ListDestinationServiceRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_gateway_v1_destination_proto_msgTypes[16].Exporter = func(v any, i int) any {
			switch v := v.(*ListDestinationServiceResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_gateway_v1_destination_proto_msgTypes[17].Exporter = func(v any, i int) any {
			switch v := v.(*ListServiceEntryRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_gateway_v1_destination_proto_msgTypes[18].Exporter = func(v any, i int) any {
			switch v := v.(*ListServiceEntryResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_gateway_v1_destination_proto_msgTypes[19].Exporter = func(v any, i int) any {
			switch v := v.(*DeleteDestinationServiceRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_gateway_v1_destination_proto_msgTypes[20].Exporter = func(v any, i int) any {
			switch v := v.(*GatewayBindingRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_gateway_v1_destination_proto_msgTypes[21].Exporter = func(v any, i int) any {
			switch v := v.(*ServiceEntry); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_gateway_v1_destination_proto_msgTypes[22].Exporter = func(v any, i int) any {
			switch v := v.(*ServiceEntryStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_gateway_v1_destination_proto_msgTypes[23].Exporter = func(v any, i int) any {
			switch v := v.(*DeploymentGroupRef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_gateway_v1_destination_proto_msgTypes[24].Exporter = func(v any, i int) any {
			switch v := v.(*ServiceRef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_gateway_v1_destination_proto_msgTypes[25].Exporter = func(v any, i int) any {
			switch v := v.(*DestinationService); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_gateway_v1_destination_proto_msgTypes[26].Exporter = func(v any, i int) any {
			switch v := v.(*TopologySpreadConstraints); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_gateway_v1_destination_proto_msgTypes[27].Exporter = func(v any, i int) any {
			switch v := v.(*LoadBalanceStrategy); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_gateway_v1_destination_proto_msgTypes[28].Exporter = func(v any, i int) any {
			switch v := v.(*Warmup); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_aistudio_gateway_v1_destination_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   32,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_aistudio_gateway_v1_destination_proto_goTypes,
		DependencyIndexes: file_aistudio_gateway_v1_destination_proto_depIdxs,
		EnumInfos:         file_aistudio_gateway_v1_destination_proto_enumTypes,
		MessageInfos:      file_aistudio_gateway_v1_destination_proto_msgTypes,
	}.Build()
	File_aistudio_gateway_v1_destination_proto = out.File
	file_aistudio_gateway_v1_destination_proto_rawDesc = nil
	file_aistudio_gateway_v1_destination_proto_goTypes = nil
	file_aistudio_gateway_v1_destination_proto_depIdxs = nil
}
