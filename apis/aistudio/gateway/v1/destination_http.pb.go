// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.3
// - protoc             v5.29.3
// source: aistudio/gateway/v1/destination.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationDiscoveryServiceCreateDestinationService = "/apis.aistudio.gateway.v1.DiscoveryService/CreateDestinationService"
const OperationDiscoveryServiceCreateServiceEntry = "/apis.aistudio.gateway.v1.DiscoveryService/CreateServiceEntry"
const OperationDiscoveryServiceDeleteDestinationService = "/apis.aistudio.gateway.v1.DiscoveryService/DeleteDestinationService"
const OperationDiscoveryServiceDeleteServiceEntry = "/apis.aistudio.gateway.v1.DiscoveryService/DeleteServiceEntry"
const OperationDiscoveryServiceGetDestinationService = "/apis.aistudio.gateway.v1.DiscoveryService/GetDestinationService"
const OperationDiscoveryServiceGetServiceEntryDetail = "/apis.aistudio.gateway.v1.DiscoveryService/GetServiceEntryDetail"
const OperationDiscoveryServiceListDestinationServices = "/apis.aistudio.gateway.v1.DiscoveryService/ListDestinationServices"
const OperationDiscoveryServiceListServiceEntries = "/apis.aistudio.gateway.v1.DiscoveryService/ListServiceEntries"
const OperationDiscoveryServiceListServiceEntryEndpoints = "/apis.aistudio.gateway.v1.DiscoveryService/ListServiceEntryEndpoints"
const OperationDiscoveryServiceServiceEndpointTrafficRecovery = "/apis.aistudio.gateway.v1.DiscoveryService/ServiceEndpointTrafficRecovery"
const OperationDiscoveryServiceServiceEndpointTrafficRemoval = "/apis.aistudio.gateway.v1.DiscoveryService/ServiceEndpointTrafficRemoval"
const OperationDiscoveryServiceUpdateDestinationService = "/apis.aistudio.gateway.v1.DiscoveryService/UpdateDestinationService"
const OperationDiscoveryServiceUpdateServiceEntry = "/apis.aistudio.gateway.v1.DiscoveryService/UpdateServiceEntry"

type DiscoveryServiceHTTPServer interface {
	CreateDestinationService(context.Context, *CreateOrUpdateDestinationServiceRequest) (*emptypb.Empty, error)
	CreateServiceEntry(context.Context, *CreateServiceEntryRequest) (*CreateServiceEntryResponse, error)
	DeleteDestinationService(context.Context, *DeleteDestinationServiceRequest) (*emptypb.Empty, error)
	DeleteServiceEntry(context.Context, *DeleteServiceEntryRequest) (*emptypb.Empty, error)
	GetDestinationService(context.Context, *GetDestinationServiceRequest) (*DestinationService, error)
	GetServiceEntryDetail(context.Context, *GetServiceEntryDetailRequest) (*ServiceEntry, error)
	ListDestinationServices(context.Context, *ListDestinationServiceRequest) (*ListDestinationServiceResponse, error)
	ListServiceEntries(context.Context, *ListServiceEntriesOptions) (*ListServiceEntriesResponse, error)
	ListServiceEntryEndpoints(context.Context, *ListServiceEntryEndpointRequest) (*ListServiceEntryEndpointResponse, error)
	ServiceEndpointTrafficRecovery(context.Context, *ServiceEndpointTrafficRecoveryRequest) (*emptypb.Empty, error)
	ServiceEndpointTrafficRemoval(context.Context, *ServiceEndpointTrafficRemovalRequest) (*emptypb.Empty, error)
	UpdateDestinationService(context.Context, *CreateOrUpdateDestinationServiceRequest) (*emptypb.Empty, error)
	UpdateServiceEntry(context.Context, *UpdateServiceEntryRequest) (*emptypb.Empty, error)
}

func RegisterDiscoveryServiceHTTPServer(s *http.Server, srv DiscoveryServiceHTTPServer) {
	r := s.Route("/")
	r.POST("/apis/v1/workspace/{workspaceName}/destinations", _DiscoveryService_CreateDestinationService0_HTTP_Handler(srv))
	r.PUT("/apis/v1/workspace/{workspaceName}/destinations/{name}", _DiscoveryService_UpdateDestinationService0_HTTP_Handler(srv))
	r.GET("/apis/v1/workspace/{workspaceName}/destinations/{name}", _DiscoveryService_GetDestinationService0_HTTP_Handler(srv))
	r.DELETE("/apis/v1/workspace/{workspaceName}/destinations/{name}", _DiscoveryService_DeleteDestinationService0_HTTP_Handler(srv))
	r.POST("/apis/v1/service-entries", _DiscoveryService_CreateServiceEntry0_HTTP_Handler(srv))
	r.PUT("/apis/v1/service-entries/{serviceEntryId}", _DiscoveryService_UpdateServiceEntry0_HTTP_Handler(srv))
	r.DELETE("/apis/v1/service-entries/{serviceEntryId}", _DiscoveryService_DeleteServiceEntry0_HTTP_Handler(srv))
	r.GET("/apis/v1/service-entries", _DiscoveryService_ListServiceEntries0_HTTP_Handler(srv))
	r.GET("/apis/v1/service-entries/{serviceEntryId}", _DiscoveryService_GetServiceEntryDetail0_HTTP_Handler(srv))
	r.GET("/apis/v1/workspace/{workspaceName}/destinations", _DiscoveryService_ListDestinationServices0_HTTP_Handler(srv))
	r.POST("/apis/v1/service-entries/{serviceEntryId}/{endpoint}/traffic/removal", _DiscoveryService_ServiceEndpointTrafficRemoval0_HTTP_Handler(srv))
	r.POST("/apis/v1/service-entries/{serviceEntryId}/{endpoint}/traffic/recovery", _DiscoveryService_ServiceEndpointTrafficRecovery0_HTTP_Handler(srv))
	r.GET("/apis/v1/service-entries/{serviceEntryId}/endpoints", _DiscoveryService_ListServiceEntryEndpoints0_HTTP_Handler(srv))
}

func _DiscoveryService_CreateDestinationService0_HTTP_Handler(srv DiscoveryServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateOrUpdateDestinationServiceRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationDiscoveryServiceCreateDestinationService)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateDestinationService(ctx, req.(*CreateOrUpdateDestinationServiceRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _DiscoveryService_UpdateDestinationService0_HTTP_Handler(srv DiscoveryServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateOrUpdateDestinationServiceRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationDiscoveryServiceUpdateDestinationService)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateDestinationService(ctx, req.(*CreateOrUpdateDestinationServiceRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _DiscoveryService_GetDestinationService0_HTTP_Handler(srv DiscoveryServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetDestinationServiceRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationDiscoveryServiceGetDestinationService)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetDestinationService(ctx, req.(*GetDestinationServiceRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DestinationService)
		return ctx.Result(200, reply)
	}
}

func _DiscoveryService_DeleteDestinationService0_HTTP_Handler(srv DiscoveryServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteDestinationServiceRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationDiscoveryServiceDeleteDestinationService)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteDestinationService(ctx, req.(*DeleteDestinationServiceRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _DiscoveryService_CreateServiceEntry0_HTTP_Handler(srv DiscoveryServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateServiceEntryRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationDiscoveryServiceCreateServiceEntry)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateServiceEntry(ctx, req.(*CreateServiceEntryRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CreateServiceEntryResponse)
		return ctx.Result(200, reply)
	}
}

func _DiscoveryService_UpdateServiceEntry0_HTTP_Handler(srv DiscoveryServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UpdateServiceEntryRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationDiscoveryServiceUpdateServiceEntry)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateServiceEntry(ctx, req.(*UpdateServiceEntryRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _DiscoveryService_DeleteServiceEntry0_HTTP_Handler(srv DiscoveryServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteServiceEntryRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationDiscoveryServiceDeleteServiceEntry)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteServiceEntry(ctx, req.(*DeleteServiceEntryRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _DiscoveryService_ListServiceEntries0_HTTP_Handler(srv DiscoveryServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListServiceEntriesOptions
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationDiscoveryServiceListServiceEntries)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListServiceEntries(ctx, req.(*ListServiceEntriesOptions))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListServiceEntriesResponse)
		return ctx.Result(200, reply)
	}
}

func _DiscoveryService_GetServiceEntryDetail0_HTTP_Handler(srv DiscoveryServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetServiceEntryDetailRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationDiscoveryServiceGetServiceEntryDetail)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetServiceEntryDetail(ctx, req.(*GetServiceEntryDetailRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ServiceEntry)
		return ctx.Result(200, reply)
	}
}

func _DiscoveryService_ListDestinationServices0_HTTP_Handler(srv DiscoveryServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListDestinationServiceRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationDiscoveryServiceListDestinationServices)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListDestinationServices(ctx, req.(*ListDestinationServiceRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListDestinationServiceResponse)
		return ctx.Result(200, reply)
	}
}

func _DiscoveryService_ServiceEndpointTrafficRemoval0_HTTP_Handler(srv DiscoveryServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ServiceEndpointTrafficRemovalRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationDiscoveryServiceServiceEndpointTrafficRemoval)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ServiceEndpointTrafficRemoval(ctx, req.(*ServiceEndpointTrafficRemovalRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _DiscoveryService_ServiceEndpointTrafficRecovery0_HTTP_Handler(srv DiscoveryServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ServiceEndpointTrafficRecoveryRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationDiscoveryServiceServiceEndpointTrafficRecovery)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ServiceEndpointTrafficRecovery(ctx, req.(*ServiceEndpointTrafficRecoveryRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _DiscoveryService_ListServiceEntryEndpoints0_HTTP_Handler(srv DiscoveryServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListServiceEntryEndpointRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationDiscoveryServiceListServiceEntryEndpoints)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListServiceEntryEndpoints(ctx, req.(*ListServiceEntryEndpointRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListServiceEntryEndpointResponse)
		return ctx.Result(200, reply)
	}
}

type DiscoveryServiceHTTPClient interface {
	CreateDestinationService(ctx context.Context, req *CreateOrUpdateDestinationServiceRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	CreateServiceEntry(ctx context.Context, req *CreateServiceEntryRequest, opts ...http.CallOption) (rsp *CreateServiceEntryResponse, err error)
	DeleteDestinationService(ctx context.Context, req *DeleteDestinationServiceRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	DeleteServiceEntry(ctx context.Context, req *DeleteServiceEntryRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	GetDestinationService(ctx context.Context, req *GetDestinationServiceRequest, opts ...http.CallOption) (rsp *DestinationService, err error)
	GetServiceEntryDetail(ctx context.Context, req *GetServiceEntryDetailRequest, opts ...http.CallOption) (rsp *ServiceEntry, err error)
	ListDestinationServices(ctx context.Context, req *ListDestinationServiceRequest, opts ...http.CallOption) (rsp *ListDestinationServiceResponse, err error)
	ListServiceEntries(ctx context.Context, req *ListServiceEntriesOptions, opts ...http.CallOption) (rsp *ListServiceEntriesResponse, err error)
	ListServiceEntryEndpoints(ctx context.Context, req *ListServiceEntryEndpointRequest, opts ...http.CallOption) (rsp *ListServiceEntryEndpointResponse, err error)
	ServiceEndpointTrafficRecovery(ctx context.Context, req *ServiceEndpointTrafficRecoveryRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	ServiceEndpointTrafficRemoval(ctx context.Context, req *ServiceEndpointTrafficRemovalRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	UpdateDestinationService(ctx context.Context, req *CreateOrUpdateDestinationServiceRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	UpdateServiceEntry(ctx context.Context, req *UpdateServiceEntryRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
}

type DiscoveryServiceHTTPClientImpl struct {
	cc *http.Client
}

func NewDiscoveryServiceHTTPClient(client *http.Client) DiscoveryServiceHTTPClient {
	return &DiscoveryServiceHTTPClientImpl{client}
}

func (c *DiscoveryServiceHTTPClientImpl) CreateDestinationService(ctx context.Context, in *CreateOrUpdateDestinationServiceRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/apis/v1/workspace/{workspaceName}/destinations"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationDiscoveryServiceCreateDestinationService))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *DiscoveryServiceHTTPClientImpl) CreateServiceEntry(ctx context.Context, in *CreateServiceEntryRequest, opts ...http.CallOption) (*CreateServiceEntryResponse, error) {
	var out CreateServiceEntryResponse
	pattern := "/apis/v1/service-entries"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationDiscoveryServiceCreateServiceEntry))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *DiscoveryServiceHTTPClientImpl) DeleteDestinationService(ctx context.Context, in *DeleteDestinationServiceRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/apis/v1/workspace/{workspaceName}/destinations/{name}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationDiscoveryServiceDeleteDestinationService))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *DiscoveryServiceHTTPClientImpl) DeleteServiceEntry(ctx context.Context, in *DeleteServiceEntryRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/apis/v1/service-entries/{serviceEntryId}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationDiscoveryServiceDeleteServiceEntry))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *DiscoveryServiceHTTPClientImpl) GetDestinationService(ctx context.Context, in *GetDestinationServiceRequest, opts ...http.CallOption) (*DestinationService, error) {
	var out DestinationService
	pattern := "/apis/v1/workspace/{workspaceName}/destinations/{name}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationDiscoveryServiceGetDestinationService))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *DiscoveryServiceHTTPClientImpl) GetServiceEntryDetail(ctx context.Context, in *GetServiceEntryDetailRequest, opts ...http.CallOption) (*ServiceEntry, error) {
	var out ServiceEntry
	pattern := "/apis/v1/service-entries/{serviceEntryId}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationDiscoveryServiceGetServiceEntryDetail))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *DiscoveryServiceHTTPClientImpl) ListDestinationServices(ctx context.Context, in *ListDestinationServiceRequest, opts ...http.CallOption) (*ListDestinationServiceResponse, error) {
	var out ListDestinationServiceResponse
	pattern := "/apis/v1/workspace/{workspaceName}/destinations"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationDiscoveryServiceListDestinationServices))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *DiscoveryServiceHTTPClientImpl) ListServiceEntries(ctx context.Context, in *ListServiceEntriesOptions, opts ...http.CallOption) (*ListServiceEntriesResponse, error) {
	var out ListServiceEntriesResponse
	pattern := "/apis/v1/service-entries"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationDiscoveryServiceListServiceEntries))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *DiscoveryServiceHTTPClientImpl) ListServiceEntryEndpoints(ctx context.Context, in *ListServiceEntryEndpointRequest, opts ...http.CallOption) (*ListServiceEntryEndpointResponse, error) {
	var out ListServiceEntryEndpointResponse
	pattern := "/apis/v1/service-entries/{serviceEntryId}/endpoints"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationDiscoveryServiceListServiceEntryEndpoints))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *DiscoveryServiceHTTPClientImpl) ServiceEndpointTrafficRecovery(ctx context.Context, in *ServiceEndpointTrafficRecoveryRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/apis/v1/service-entries/{serviceEntryId}/{endpoint}/traffic/recovery"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationDiscoveryServiceServiceEndpointTrafficRecovery))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *DiscoveryServiceHTTPClientImpl) ServiceEndpointTrafficRemoval(ctx context.Context, in *ServiceEndpointTrafficRemovalRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/apis/v1/service-entries/{serviceEntryId}/{endpoint}/traffic/removal"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationDiscoveryServiceServiceEndpointTrafficRemoval))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *DiscoveryServiceHTTPClientImpl) UpdateDestinationService(ctx context.Context, in *CreateOrUpdateDestinationServiceRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/apis/v1/workspace/{workspaceName}/destinations/{name}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationDiscoveryServiceUpdateDestinationService))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *DiscoveryServiceHTTPClientImpl) UpdateServiceEntry(ctx context.Context, in *UpdateServiceEntryRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/apis/v1/service-entries/{serviceEntryId}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationDiscoveryServiceUpdateServiceEntry))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
