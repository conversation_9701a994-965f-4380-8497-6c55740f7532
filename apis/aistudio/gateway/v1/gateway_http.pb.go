// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.3
// - protoc             v5.29.3
// source: aistudio/gateway/v1/gateway.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationGatewayServiceCreateGateway = "/apis.aistudio.gateway.v1.GatewayService/CreateGateway"
const OperationGatewayServiceCreateServiceBinding = "/apis.aistudio.gateway.v1.GatewayService/CreateServiceBinding"
const OperationGatewayServiceDeleteGateway = "/apis.aistudio.gateway.v1.GatewayService/DeleteGateway"
const OperationGatewayServiceDeleteServiceBinding = "/apis.aistudio.gateway.v1.GatewayService/DeleteServiceBinding"
const OperationGatewayServiceGetGateway = "/apis.aistudio.gateway.v1.GatewayService/GetGateway"
const OperationGatewayServiceListGateway = "/apis.aistudio.gateway.v1.GatewayService/ListGateway"
const OperationGatewayServiceListServiceBinding = "/apis.aistudio.gateway.v1.GatewayService/ListServiceBinding"
const OperationGatewayServiceUpdateGateway = "/apis.aistudio.gateway.v1.GatewayService/UpdateGateway"

type GatewayServiceHTTPServer interface {
	CreateGateway(context.Context, *CreateOrUpdateGatewayRequest) (*Gateway, error)
	CreateServiceBinding(context.Context, *CreateServiceBindingRequest) (*CreateServiceBindingResponse, error)
	DeleteGateway(context.Context, *DeleteGatewayRequest) (*DeleteGatewayResponse, error)
	DeleteServiceBinding(context.Context, *DeleteServiceBindingRequest) (*emptypb.Empty, error)
	GetGateway(context.Context, *GetGatewayRequest) (*Gateway, error)
	ListGateway(context.Context, *ListGatewayOptions) (*ListGatewayResponse, error)
	ListServiceBinding(context.Context, *ListServiceBindingOptions) (*ListServiceBindingResponse, error)
	UpdateGateway(context.Context, *CreateOrUpdateGatewayRequest) (*Gateway, error)
}

func RegisterGatewayServiceHTTPServer(s *http.Server, srv GatewayServiceHTTPServer) {
	r := s.Route("/")
	r.PUT("/apis/v1/workspace/{workspaceName}/gateway", _GatewayService_CreateGateway0_HTTP_Handler(srv))
	r.POST("/apis/v1/workspace/{workspaceName}/gateway/{name}", _GatewayService_UpdateGateway0_HTTP_Handler(srv))
	r.GET("/apis/v1/workspace/{workspaceName}/gateway/{name}", _GatewayService_GetGateway0_HTTP_Handler(srv))
	r.GET("/apis/v1/workspace/{workspaceName}/gateways", _GatewayService_ListGateway0_HTTP_Handler(srv))
	r.DELETE("/apis/v1/workspace/{workspaceName}/gateway/{name}", _GatewayService_DeleteGateway0_HTTP_Handler(srv))
	r.POST("/apis/v1/service-binding", _GatewayService_CreateServiceBinding0_HTTP_Handler(srv))
	r.DELETE("/apis/v1/service-binding/{serviceBindingId}", _GatewayService_DeleteServiceBinding0_HTTP_Handler(srv))
	r.GET("/apis/v1/service-bindings", _GatewayService_ListServiceBinding0_HTTP_Handler(srv))
}

func _GatewayService_CreateGateway0_HTTP_Handler(srv GatewayServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateOrUpdateGatewayRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGatewayServiceCreateGateway)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateGateway(ctx, req.(*CreateOrUpdateGatewayRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Gateway)
		return ctx.Result(200, reply)
	}
}

func _GatewayService_UpdateGateway0_HTTP_Handler(srv GatewayServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateOrUpdateGatewayRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGatewayServiceUpdateGateway)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateGateway(ctx, req.(*CreateOrUpdateGatewayRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Gateway)
		return ctx.Result(200, reply)
	}
}

func _GatewayService_GetGateway0_HTTP_Handler(srv GatewayServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetGatewayRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGatewayServiceGetGateway)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetGateway(ctx, req.(*GetGatewayRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Gateway)
		return ctx.Result(200, reply)
	}
}

func _GatewayService_ListGateway0_HTTP_Handler(srv GatewayServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListGatewayOptions
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGatewayServiceListGateway)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListGateway(ctx, req.(*ListGatewayOptions))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListGatewayResponse)
		return ctx.Result(200, reply)
	}
}

func _GatewayService_DeleteGateway0_HTTP_Handler(srv GatewayServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteGatewayRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGatewayServiceDeleteGateway)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteGateway(ctx, req.(*DeleteGatewayRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DeleteGatewayResponse)
		return ctx.Result(200, reply)
	}
}

func _GatewayService_CreateServiceBinding0_HTTP_Handler(srv GatewayServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateServiceBindingRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGatewayServiceCreateServiceBinding)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateServiceBinding(ctx, req.(*CreateServiceBindingRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CreateServiceBindingResponse)
		return ctx.Result(200, reply)
	}
}

func _GatewayService_DeleteServiceBinding0_HTTP_Handler(srv GatewayServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteServiceBindingRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGatewayServiceDeleteServiceBinding)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteServiceBinding(ctx, req.(*DeleteServiceBindingRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _GatewayService_ListServiceBinding0_HTTP_Handler(srv GatewayServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListServiceBindingOptions
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGatewayServiceListServiceBinding)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListServiceBinding(ctx, req.(*ListServiceBindingOptions))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListServiceBindingResponse)
		return ctx.Result(200, reply)
	}
}

type GatewayServiceHTTPClient interface {
	CreateGateway(ctx context.Context, req *CreateOrUpdateGatewayRequest, opts ...http.CallOption) (rsp *Gateway, err error)
	CreateServiceBinding(ctx context.Context, req *CreateServiceBindingRequest, opts ...http.CallOption) (rsp *CreateServiceBindingResponse, err error)
	DeleteGateway(ctx context.Context, req *DeleteGatewayRequest, opts ...http.CallOption) (rsp *DeleteGatewayResponse, err error)
	DeleteServiceBinding(ctx context.Context, req *DeleteServiceBindingRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	GetGateway(ctx context.Context, req *GetGatewayRequest, opts ...http.CallOption) (rsp *Gateway, err error)
	ListGateway(ctx context.Context, req *ListGatewayOptions, opts ...http.CallOption) (rsp *ListGatewayResponse, err error)
	ListServiceBinding(ctx context.Context, req *ListServiceBindingOptions, opts ...http.CallOption) (rsp *ListServiceBindingResponse, err error)
	UpdateGateway(ctx context.Context, req *CreateOrUpdateGatewayRequest, opts ...http.CallOption) (rsp *Gateway, err error)
}

type GatewayServiceHTTPClientImpl struct {
	cc *http.Client
}

func NewGatewayServiceHTTPClient(client *http.Client) GatewayServiceHTTPClient {
	return &GatewayServiceHTTPClientImpl{client}
}

func (c *GatewayServiceHTTPClientImpl) CreateGateway(ctx context.Context, in *CreateOrUpdateGatewayRequest, opts ...http.CallOption) (*Gateway, error) {
	var out Gateway
	pattern := "/apis/v1/workspace/{workspaceName}/gateway"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationGatewayServiceCreateGateway))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *GatewayServiceHTTPClientImpl) CreateServiceBinding(ctx context.Context, in *CreateServiceBindingRequest, opts ...http.CallOption) (*CreateServiceBindingResponse, error) {
	var out CreateServiceBindingResponse
	pattern := "/apis/v1/service-binding"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationGatewayServiceCreateServiceBinding))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *GatewayServiceHTTPClientImpl) DeleteGateway(ctx context.Context, in *DeleteGatewayRequest, opts ...http.CallOption) (*DeleteGatewayResponse, error) {
	var out DeleteGatewayResponse
	pattern := "/apis/v1/workspace/{workspaceName}/gateway/{name}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationGatewayServiceDeleteGateway))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *GatewayServiceHTTPClientImpl) DeleteServiceBinding(ctx context.Context, in *DeleteServiceBindingRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/apis/v1/service-binding/{serviceBindingId}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationGatewayServiceDeleteServiceBinding))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *GatewayServiceHTTPClientImpl) GetGateway(ctx context.Context, in *GetGatewayRequest, opts ...http.CallOption) (*Gateway, error) {
	var out Gateway
	pattern := "/apis/v1/workspace/{workspaceName}/gateway/{name}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationGatewayServiceGetGateway))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *GatewayServiceHTTPClientImpl) ListGateway(ctx context.Context, in *ListGatewayOptions, opts ...http.CallOption) (*ListGatewayResponse, error) {
	var out ListGatewayResponse
	pattern := "/apis/v1/workspace/{workspaceName}/gateways"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationGatewayServiceListGateway))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *GatewayServiceHTTPClientImpl) ListServiceBinding(ctx context.Context, in *ListServiceBindingOptions, opts ...http.CallOption) (*ListServiceBindingResponse, error) {
	var out ListServiceBindingResponse
	pattern := "/apis/v1/service-bindings"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationGatewayServiceListServiceBinding))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *GatewayServiceHTTPClientImpl) UpdateGateway(ctx context.Context, in *CreateOrUpdateGatewayRequest, opts ...http.CallOption) (*Gateway, error) {
	var out Gateway
	pattern := "/apis/v1/workspace/{workspaceName}/gateway/{name}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationGatewayServiceUpdateGateway))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
