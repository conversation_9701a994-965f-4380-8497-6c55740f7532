// Code generated by protoc-gen-deepcopy. DO NOT EDIT.
package v1

import (
	proto "google.golang.org/protobuf/proto"
)

// DeepCopyInto supports using CreateServiceEntryRequest within kubernetes types, where deepcopy-gen is used.
func (in *CreateServiceEntryRequest) DeepCopyInto(out *CreateServiceEntryRequest) {
	p := proto.Clone(in).(*CreateServiceEntryRequest)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new CreateServiceEntryRequest. Required by controller-gen.
func (in *CreateServiceEntryRequest) DeepCopy() *CreateServiceEntryRequest {
	if in == nil {
		return nil
	}
	out := new(CreateServiceEntryRequest)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new CreateServiceEntryRequest. Required by controller-gen.
func (in *CreateServiceEntryRequest) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using GetDestinationServiceRequest within kubernetes types, where deepcopy-gen is used.
func (in *GetDestinationServiceRequest) DeepCopyInto(out *GetDestinationServiceRequest) {
	p := proto.Clone(in).(*GetDestinationServiceRequest)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new GetDestinationServiceRequest. Required by controller-gen.
func (in *GetDestinationServiceRequest) DeepCopy() *GetDestinationServiceRequest {
	if in == nil {
		return nil
	}
	out := new(GetDestinationServiceRequest)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new GetDestinationServiceRequest. Required by controller-gen.
func (in *GetDestinationServiceRequest) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using CreateServiceEntryResponse within kubernetes types, where deepcopy-gen is used.
func (in *CreateServiceEntryResponse) DeepCopyInto(out *CreateServiceEntryResponse) {
	p := proto.Clone(in).(*CreateServiceEntryResponse)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new CreateServiceEntryResponse. Required by controller-gen.
func (in *CreateServiceEntryResponse) DeepCopy() *CreateServiceEntryResponse {
	if in == nil {
		return nil
	}
	out := new(CreateServiceEntryResponse)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new CreateServiceEntryResponse. Required by controller-gen.
func (in *CreateServiceEntryResponse) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using UpdateServiceEntryRequest within kubernetes types, where deepcopy-gen is used.
func (in *UpdateServiceEntryRequest) DeepCopyInto(out *UpdateServiceEntryRequest) {
	p := proto.Clone(in).(*UpdateServiceEntryRequest)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new UpdateServiceEntryRequest. Required by controller-gen.
func (in *UpdateServiceEntryRequest) DeepCopy() *UpdateServiceEntryRequest {
	if in == nil {
		return nil
	}
	out := new(UpdateServiceEntryRequest)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new UpdateServiceEntryRequest. Required by controller-gen.
func (in *UpdateServiceEntryRequest) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using GetServiceEntryDetailRequest within kubernetes types, where deepcopy-gen is used.
func (in *GetServiceEntryDetailRequest) DeepCopyInto(out *GetServiceEntryDetailRequest) {
	p := proto.Clone(in).(*GetServiceEntryDetailRequest)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new GetServiceEntryDetailRequest. Required by controller-gen.
func (in *GetServiceEntryDetailRequest) DeepCopy() *GetServiceEntryDetailRequest {
	if in == nil {
		return nil
	}
	out := new(GetServiceEntryDetailRequest)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new GetServiceEntryDetailRequest. Required by controller-gen.
func (in *GetServiceEntryDetailRequest) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using DeleteServiceEntryRequest within kubernetes types, where deepcopy-gen is used.
func (in *DeleteServiceEntryRequest) DeepCopyInto(out *DeleteServiceEntryRequest) {
	p := proto.Clone(in).(*DeleteServiceEntryRequest)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new DeleteServiceEntryRequest. Required by controller-gen.
func (in *DeleteServiceEntryRequest) DeepCopy() *DeleteServiceEntryRequest {
	if in == nil {
		return nil
	}
	out := new(DeleteServiceEntryRequest)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new DeleteServiceEntryRequest. Required by controller-gen.
func (in *DeleteServiceEntryRequest) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using ListServiceEntriesOptions within kubernetes types, where deepcopy-gen is used.
func (in *ListServiceEntriesOptions) DeepCopyInto(out *ListServiceEntriesOptions) {
	p := proto.Clone(in).(*ListServiceEntriesOptions)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ListServiceEntriesOptions. Required by controller-gen.
func (in *ListServiceEntriesOptions) DeepCopy() *ListServiceEntriesOptions {
	if in == nil {
		return nil
	}
	out := new(ListServiceEntriesOptions)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new ListServiceEntriesOptions. Required by controller-gen.
func (in *ListServiceEntriesOptions) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using ListServiceEntriesResponse within kubernetes types, where deepcopy-gen is used.
func (in *ListServiceEntriesResponse) DeepCopyInto(out *ListServiceEntriesResponse) {
	p := proto.Clone(in).(*ListServiceEntriesResponse)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ListServiceEntriesResponse. Required by controller-gen.
func (in *ListServiceEntriesResponse) DeepCopy() *ListServiceEntriesResponse {
	if in == nil {
		return nil
	}
	out := new(ListServiceEntriesResponse)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new ListServiceEntriesResponse. Required by controller-gen.
func (in *ListServiceEntriesResponse) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using ServiceEndpoint within kubernetes types, where deepcopy-gen is used.
func (in *ServiceEndpoint) DeepCopyInto(out *ServiceEndpoint) {
	p := proto.Clone(in).(*ServiceEndpoint)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ServiceEndpoint. Required by controller-gen.
func (in *ServiceEndpoint) DeepCopy() *ServiceEndpoint {
	if in == nil {
		return nil
	}
	out := new(ServiceEndpoint)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new ServiceEndpoint. Required by controller-gen.
func (in *ServiceEndpoint) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using ServiceEndpointCondition within kubernetes types, where deepcopy-gen is used.
func (in *ServiceEndpointCondition) DeepCopyInto(out *ServiceEndpointCondition) {
	p := proto.Clone(in).(*ServiceEndpointCondition)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ServiceEndpointCondition. Required by controller-gen.
func (in *ServiceEndpointCondition) DeepCopy() *ServiceEndpointCondition {
	if in == nil {
		return nil
	}
	out := new(ServiceEndpointCondition)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new ServiceEndpointCondition. Required by controller-gen.
func (in *ServiceEndpointCondition) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using ListServiceEntryEndpointResponse within kubernetes types, where deepcopy-gen is used.
func (in *ListServiceEntryEndpointResponse) DeepCopyInto(out *ListServiceEntryEndpointResponse) {
	p := proto.Clone(in).(*ListServiceEntryEndpointResponse)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ListServiceEntryEndpointResponse. Required by controller-gen.
func (in *ListServiceEntryEndpointResponse) DeepCopy() *ListServiceEntryEndpointResponse {
	if in == nil {
		return nil
	}
	out := new(ListServiceEntryEndpointResponse)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new ListServiceEntryEndpointResponse. Required by controller-gen.
func (in *ListServiceEntryEndpointResponse) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using ListServiceEntryEndpointRequest within kubernetes types, where deepcopy-gen is used.
func (in *ListServiceEntryEndpointRequest) DeepCopyInto(out *ListServiceEntryEndpointRequest) {
	p := proto.Clone(in).(*ListServiceEntryEndpointRequest)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ListServiceEntryEndpointRequest. Required by controller-gen.
func (in *ListServiceEntryEndpointRequest) DeepCopy() *ListServiceEntryEndpointRequest {
	if in == nil {
		return nil
	}
	out := new(ListServiceEntryEndpointRequest)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new ListServiceEntryEndpointRequest. Required by controller-gen.
func (in *ListServiceEntryEndpointRequest) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using ServiceEndpointTrafficRemovalRequest within kubernetes types, where deepcopy-gen is used.
func (in *ServiceEndpointTrafficRemovalRequest) DeepCopyInto(out *ServiceEndpointTrafficRemovalRequest) {
	p := proto.Clone(in).(*ServiceEndpointTrafficRemovalRequest)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ServiceEndpointTrafficRemovalRequest. Required by controller-gen.
func (in *ServiceEndpointTrafficRemovalRequest) DeepCopy() *ServiceEndpointTrafficRemovalRequest {
	if in == nil {
		return nil
	}
	out := new(ServiceEndpointTrafficRemovalRequest)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new ServiceEndpointTrafficRemovalRequest. Required by controller-gen.
func (in *ServiceEndpointTrafficRemovalRequest) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using ServiceEndpointTrafficRecoveryRequest within kubernetes types, where deepcopy-gen is used.
func (in *ServiceEndpointTrafficRecoveryRequest) DeepCopyInto(out *ServiceEndpointTrafficRecoveryRequest) {
	p := proto.Clone(in).(*ServiceEndpointTrafficRecoveryRequest)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ServiceEndpointTrafficRecoveryRequest. Required by controller-gen.
func (in *ServiceEndpointTrafficRecoveryRequest) DeepCopy() *ServiceEndpointTrafficRecoveryRequest {
	if in == nil {
		return nil
	}
	out := new(ServiceEndpointTrafficRecoveryRequest)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new ServiceEndpointTrafficRecoveryRequest. Required by controller-gen.
func (in *ServiceEndpointTrafficRecoveryRequest) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using CreateOrUpdateDestinationServiceRequest within kubernetes types, where deepcopy-gen is used.
func (in *CreateOrUpdateDestinationServiceRequest) DeepCopyInto(out *CreateOrUpdateDestinationServiceRequest) {
	p := proto.Clone(in).(*CreateOrUpdateDestinationServiceRequest)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new CreateOrUpdateDestinationServiceRequest. Required by controller-gen.
func (in *CreateOrUpdateDestinationServiceRequest) DeepCopy() *CreateOrUpdateDestinationServiceRequest {
	if in == nil {
		return nil
	}
	out := new(CreateOrUpdateDestinationServiceRequest)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new CreateOrUpdateDestinationServiceRequest. Required by controller-gen.
func (in *CreateOrUpdateDestinationServiceRequest) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using ListDestinationServiceRequest within kubernetes types, where deepcopy-gen is used.
func (in *ListDestinationServiceRequest) DeepCopyInto(out *ListDestinationServiceRequest) {
	p := proto.Clone(in).(*ListDestinationServiceRequest)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ListDestinationServiceRequest. Required by controller-gen.
func (in *ListDestinationServiceRequest) DeepCopy() *ListDestinationServiceRequest {
	if in == nil {
		return nil
	}
	out := new(ListDestinationServiceRequest)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new ListDestinationServiceRequest. Required by controller-gen.
func (in *ListDestinationServiceRequest) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using ListDestinationServiceResponse within kubernetes types, where deepcopy-gen is used.
func (in *ListDestinationServiceResponse) DeepCopyInto(out *ListDestinationServiceResponse) {
	p := proto.Clone(in).(*ListDestinationServiceResponse)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ListDestinationServiceResponse. Required by controller-gen.
func (in *ListDestinationServiceResponse) DeepCopy() *ListDestinationServiceResponse {
	if in == nil {
		return nil
	}
	out := new(ListDestinationServiceResponse)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new ListDestinationServiceResponse. Required by controller-gen.
func (in *ListDestinationServiceResponse) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using ListServiceEntryRequest within kubernetes types, where deepcopy-gen is used.
func (in *ListServiceEntryRequest) DeepCopyInto(out *ListServiceEntryRequest) {
	p := proto.Clone(in).(*ListServiceEntryRequest)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ListServiceEntryRequest. Required by controller-gen.
func (in *ListServiceEntryRequest) DeepCopy() *ListServiceEntryRequest {
	if in == nil {
		return nil
	}
	out := new(ListServiceEntryRequest)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new ListServiceEntryRequest. Required by controller-gen.
func (in *ListServiceEntryRequest) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using ListServiceEntryResponse within kubernetes types, where deepcopy-gen is used.
func (in *ListServiceEntryResponse) DeepCopyInto(out *ListServiceEntryResponse) {
	p := proto.Clone(in).(*ListServiceEntryResponse)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ListServiceEntryResponse. Required by controller-gen.
func (in *ListServiceEntryResponse) DeepCopy() *ListServiceEntryResponse {
	if in == nil {
		return nil
	}
	out := new(ListServiceEntryResponse)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new ListServiceEntryResponse. Required by controller-gen.
func (in *ListServiceEntryResponse) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using DeleteDestinationServiceRequest within kubernetes types, where deepcopy-gen is used.
func (in *DeleteDestinationServiceRequest) DeepCopyInto(out *DeleteDestinationServiceRequest) {
	p := proto.Clone(in).(*DeleteDestinationServiceRequest)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new DeleteDestinationServiceRequest. Required by controller-gen.
func (in *DeleteDestinationServiceRequest) DeepCopy() *DeleteDestinationServiceRequest {
	if in == nil {
		return nil
	}
	out := new(DeleteDestinationServiceRequest)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new DeleteDestinationServiceRequest. Required by controller-gen.
func (in *DeleteDestinationServiceRequest) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using GatewayBindingRequest within kubernetes types, where deepcopy-gen is used.
func (in *GatewayBindingRequest) DeepCopyInto(out *GatewayBindingRequest) {
	p := proto.Clone(in).(*GatewayBindingRequest)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new GatewayBindingRequest. Required by controller-gen.
func (in *GatewayBindingRequest) DeepCopy() *GatewayBindingRequest {
	if in == nil {
		return nil
	}
	out := new(GatewayBindingRequest)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new GatewayBindingRequest. Required by controller-gen.
func (in *GatewayBindingRequest) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using ServiceEntry within kubernetes types, where deepcopy-gen is used.
func (in *ServiceEntry) DeepCopyInto(out *ServiceEntry) {
	p := proto.Clone(in).(*ServiceEntry)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ServiceEntry. Required by controller-gen.
func (in *ServiceEntry) DeepCopy() *ServiceEntry {
	if in == nil {
		return nil
	}
	out := new(ServiceEntry)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new ServiceEntry. Required by controller-gen.
func (in *ServiceEntry) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using ServiceEntryStatus within kubernetes types, where deepcopy-gen is used.
func (in *ServiceEntryStatus) DeepCopyInto(out *ServiceEntryStatus) {
	p := proto.Clone(in).(*ServiceEntryStatus)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ServiceEntryStatus. Required by controller-gen.
func (in *ServiceEntryStatus) DeepCopy() *ServiceEntryStatus {
	if in == nil {
		return nil
	}
	out := new(ServiceEntryStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new ServiceEntryStatus. Required by controller-gen.
func (in *ServiceEntryStatus) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using DeploymentGroupRef within kubernetes types, where deepcopy-gen is used.
func (in *DeploymentGroupRef) DeepCopyInto(out *DeploymentGroupRef) {
	p := proto.Clone(in).(*DeploymentGroupRef)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new DeploymentGroupRef. Required by controller-gen.
func (in *DeploymentGroupRef) DeepCopy() *DeploymentGroupRef {
	if in == nil {
		return nil
	}
	out := new(DeploymentGroupRef)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new DeploymentGroupRef. Required by controller-gen.
func (in *DeploymentGroupRef) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using ServiceRef within kubernetes types, where deepcopy-gen is used.
func (in *ServiceRef) DeepCopyInto(out *ServiceRef) {
	p := proto.Clone(in).(*ServiceRef)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ServiceRef. Required by controller-gen.
func (in *ServiceRef) DeepCopy() *ServiceRef {
	if in == nil {
		return nil
	}
	out := new(ServiceRef)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new ServiceRef. Required by controller-gen.
func (in *ServiceRef) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using DestinationService within kubernetes types, where deepcopy-gen is used.
func (in *DestinationService) DeepCopyInto(out *DestinationService) {
	p := proto.Clone(in).(*DestinationService)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new DestinationService. Required by controller-gen.
func (in *DestinationService) DeepCopy() *DestinationService {
	if in == nil {
		return nil
	}
	out := new(DestinationService)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new DestinationService. Required by controller-gen.
func (in *DestinationService) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using TopologySpreadConstraints within kubernetes types, where deepcopy-gen is used.
func (in *TopologySpreadConstraints) DeepCopyInto(out *TopologySpreadConstraints) {
	p := proto.Clone(in).(*TopologySpreadConstraints)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new TopologySpreadConstraints. Required by controller-gen.
func (in *TopologySpreadConstraints) DeepCopy() *TopologySpreadConstraints {
	if in == nil {
		return nil
	}
	out := new(TopologySpreadConstraints)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new TopologySpreadConstraints. Required by controller-gen.
func (in *TopologySpreadConstraints) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using LoadBalanceStrategy within kubernetes types, where deepcopy-gen is used.
func (in *LoadBalanceStrategy) DeepCopyInto(out *LoadBalanceStrategy) {
	p := proto.Clone(in).(*LoadBalanceStrategy)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new LoadBalanceStrategy. Required by controller-gen.
func (in *LoadBalanceStrategy) DeepCopy() *LoadBalanceStrategy {
	if in == nil {
		return nil
	}
	out := new(LoadBalanceStrategy)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new LoadBalanceStrategy. Required by controller-gen.
func (in *LoadBalanceStrategy) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using Warmup within kubernetes types, where deepcopy-gen is used.
func (in *Warmup) DeepCopyInto(out *Warmup) {
	p := proto.Clone(in).(*Warmup)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Warmup. Required by controller-gen.
func (in *Warmup) DeepCopy() *Warmup {
	if in == nil {
		return nil
	}
	out := new(Warmup)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new Warmup. Required by controller-gen.
func (in *Warmup) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}
