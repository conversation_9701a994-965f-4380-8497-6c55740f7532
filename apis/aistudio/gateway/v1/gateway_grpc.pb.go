// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v5.29.3
// source: aistudio/gateway/v1/gateway.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	GatewayService_CreateGateway_FullMethodName        = "/apis.aistudio.gateway.v1.GatewayService/CreateGateway"
	GatewayService_UpdateGateway_FullMethodName        = "/apis.aistudio.gateway.v1.GatewayService/UpdateGateway"
	GatewayService_GetGateway_FullMethodName           = "/apis.aistudio.gateway.v1.GatewayService/GetGateway"
	GatewayService_ListGateway_FullMethodName          = "/apis.aistudio.gateway.v1.GatewayService/ListGateway"
	GatewayService_DeleteGateway_FullMethodName        = "/apis.aistudio.gateway.v1.GatewayService/DeleteGateway"
	GatewayService_CreateServiceBinding_FullMethodName = "/apis.aistudio.gateway.v1.GatewayService/CreateServiceBinding"
	GatewayService_DeleteServiceBinding_FullMethodName = "/apis.aistudio.gateway.v1.GatewayService/DeleteServiceBinding"
	GatewayService_ListServiceBinding_FullMethodName   = "/apis.aistudio.gateway.v1.GatewayService/ListServiceBinding"
)

// GatewayServiceClient is the client API for GatewayService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type GatewayServiceClient interface {
	CreateGateway(ctx context.Context, in *CreateOrUpdateGatewayRequest, opts ...grpc.CallOption) (*Gateway, error)
	UpdateGateway(ctx context.Context, in *CreateOrUpdateGatewayRequest, opts ...grpc.CallOption) (*Gateway, error)
	GetGateway(ctx context.Context, in *GetGatewayRequest, opts ...grpc.CallOption) (*Gateway, error)
	ListGateway(ctx context.Context, in *ListGatewayOptions, opts ...grpc.CallOption) (*ListGatewayResponse, error)
	DeleteGateway(ctx context.Context, in *DeleteGatewayRequest, opts ...grpc.CallOption) (*DeleteGatewayResponse, error)
	CreateServiceBinding(ctx context.Context, in *CreateServiceBindingRequest, opts ...grpc.CallOption) (*CreateServiceBindingResponse, error)
	DeleteServiceBinding(ctx context.Context, in *DeleteServiceBindingRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	ListServiceBinding(ctx context.Context, in *ListServiceBindingOptions, opts ...grpc.CallOption) (*ListServiceBindingResponse, error)
}

type gatewayServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewGatewayServiceClient(cc grpc.ClientConnInterface) GatewayServiceClient {
	return &gatewayServiceClient{cc}
}

func (c *gatewayServiceClient) CreateGateway(ctx context.Context, in *CreateOrUpdateGatewayRequest, opts ...grpc.CallOption) (*Gateway, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(Gateway)
	err := c.cc.Invoke(ctx, GatewayService_CreateGateway_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gatewayServiceClient) UpdateGateway(ctx context.Context, in *CreateOrUpdateGatewayRequest, opts ...grpc.CallOption) (*Gateway, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(Gateway)
	err := c.cc.Invoke(ctx, GatewayService_UpdateGateway_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gatewayServiceClient) GetGateway(ctx context.Context, in *GetGatewayRequest, opts ...grpc.CallOption) (*Gateway, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(Gateway)
	err := c.cc.Invoke(ctx, GatewayService_GetGateway_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gatewayServiceClient) ListGateway(ctx context.Context, in *ListGatewayOptions, opts ...grpc.CallOption) (*ListGatewayResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListGatewayResponse)
	err := c.cc.Invoke(ctx, GatewayService_ListGateway_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gatewayServiceClient) DeleteGateway(ctx context.Context, in *DeleteGatewayRequest, opts ...grpc.CallOption) (*DeleteGatewayResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteGatewayResponse)
	err := c.cc.Invoke(ctx, GatewayService_DeleteGateway_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gatewayServiceClient) CreateServiceBinding(ctx context.Context, in *CreateServiceBindingRequest, opts ...grpc.CallOption) (*CreateServiceBindingResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateServiceBindingResponse)
	err := c.cc.Invoke(ctx, GatewayService_CreateServiceBinding_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gatewayServiceClient) DeleteServiceBinding(ctx context.Context, in *DeleteServiceBindingRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, GatewayService_DeleteServiceBinding_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gatewayServiceClient) ListServiceBinding(ctx context.Context, in *ListServiceBindingOptions, opts ...grpc.CallOption) (*ListServiceBindingResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListServiceBindingResponse)
	err := c.cc.Invoke(ctx, GatewayService_ListServiceBinding_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// GatewayServiceServer is the server API for GatewayService service.
// All implementations must embed UnimplementedGatewayServiceServer
// for forward compatibility
type GatewayServiceServer interface {
	CreateGateway(context.Context, *CreateOrUpdateGatewayRequest) (*Gateway, error)
	UpdateGateway(context.Context, *CreateOrUpdateGatewayRequest) (*Gateway, error)
	GetGateway(context.Context, *GetGatewayRequest) (*Gateway, error)
	ListGateway(context.Context, *ListGatewayOptions) (*ListGatewayResponse, error)
	DeleteGateway(context.Context, *DeleteGatewayRequest) (*DeleteGatewayResponse, error)
	CreateServiceBinding(context.Context, *CreateServiceBindingRequest) (*CreateServiceBindingResponse, error)
	DeleteServiceBinding(context.Context, *DeleteServiceBindingRequest) (*emptypb.Empty, error)
	ListServiceBinding(context.Context, *ListServiceBindingOptions) (*ListServiceBindingResponse, error)
	mustEmbedUnimplementedGatewayServiceServer()
}

// UnimplementedGatewayServiceServer must be embedded to have forward compatible implementations.
type UnimplementedGatewayServiceServer struct {
}

func (UnimplementedGatewayServiceServer) CreateGateway(context.Context, *CreateOrUpdateGatewayRequest) (*Gateway, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateGateway not implemented")
}
func (UnimplementedGatewayServiceServer) UpdateGateway(context.Context, *CreateOrUpdateGatewayRequest) (*Gateway, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateGateway not implemented")
}
func (UnimplementedGatewayServiceServer) GetGateway(context.Context, *GetGatewayRequest) (*Gateway, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetGateway not implemented")
}
func (UnimplementedGatewayServiceServer) ListGateway(context.Context, *ListGatewayOptions) (*ListGatewayResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListGateway not implemented")
}
func (UnimplementedGatewayServiceServer) DeleteGateway(context.Context, *DeleteGatewayRequest) (*DeleteGatewayResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteGateway not implemented")
}
func (UnimplementedGatewayServiceServer) CreateServiceBinding(context.Context, *CreateServiceBindingRequest) (*CreateServiceBindingResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateServiceBinding not implemented")
}
func (UnimplementedGatewayServiceServer) DeleteServiceBinding(context.Context, *DeleteServiceBindingRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteServiceBinding not implemented")
}
func (UnimplementedGatewayServiceServer) ListServiceBinding(context.Context, *ListServiceBindingOptions) (*ListServiceBindingResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListServiceBinding not implemented")
}
func (UnimplementedGatewayServiceServer) mustEmbedUnimplementedGatewayServiceServer() {}

// UnsafeGatewayServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to GatewayServiceServer will
// result in compilation errors.
type UnsafeGatewayServiceServer interface {
	mustEmbedUnimplementedGatewayServiceServer()
}

func RegisterGatewayServiceServer(s grpc.ServiceRegistrar, srv GatewayServiceServer) {
	s.RegisterService(&GatewayService_ServiceDesc, srv)
}

func _GatewayService_CreateGateway_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateOrUpdateGatewayRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GatewayServiceServer).CreateGateway(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GatewayService_CreateGateway_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GatewayServiceServer).CreateGateway(ctx, req.(*CreateOrUpdateGatewayRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GatewayService_UpdateGateway_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateOrUpdateGatewayRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GatewayServiceServer).UpdateGateway(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GatewayService_UpdateGateway_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GatewayServiceServer).UpdateGateway(ctx, req.(*CreateOrUpdateGatewayRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GatewayService_GetGateway_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGatewayRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GatewayServiceServer).GetGateway(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GatewayService_GetGateway_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GatewayServiceServer).GetGateway(ctx, req.(*GetGatewayRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GatewayService_ListGateway_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListGatewayOptions)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GatewayServiceServer).ListGateway(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GatewayService_ListGateway_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GatewayServiceServer).ListGateway(ctx, req.(*ListGatewayOptions))
	}
	return interceptor(ctx, in, info, handler)
}

func _GatewayService_DeleteGateway_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteGatewayRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GatewayServiceServer).DeleteGateway(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GatewayService_DeleteGateway_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GatewayServiceServer).DeleteGateway(ctx, req.(*DeleteGatewayRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GatewayService_CreateServiceBinding_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateServiceBindingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GatewayServiceServer).CreateServiceBinding(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GatewayService_CreateServiceBinding_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GatewayServiceServer).CreateServiceBinding(ctx, req.(*CreateServiceBindingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GatewayService_DeleteServiceBinding_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteServiceBindingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GatewayServiceServer).DeleteServiceBinding(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GatewayService_DeleteServiceBinding_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GatewayServiceServer).DeleteServiceBinding(ctx, req.(*DeleteServiceBindingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GatewayService_ListServiceBinding_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListServiceBindingOptions)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GatewayServiceServer).ListServiceBinding(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GatewayService_ListServiceBinding_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GatewayServiceServer).ListServiceBinding(ctx, req.(*ListServiceBindingOptions))
	}
	return interceptor(ctx, in, info, handler)
}

// GatewayService_ServiceDesc is the grpc.ServiceDesc for GatewayService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var GatewayService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "apis.aistudio.gateway.v1.GatewayService",
	HandlerType: (*GatewayServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateGateway",
			Handler:    _GatewayService_CreateGateway_Handler,
		},
		{
			MethodName: "UpdateGateway",
			Handler:    _GatewayService_UpdateGateway_Handler,
		},
		{
			MethodName: "GetGateway",
			Handler:    _GatewayService_GetGateway_Handler,
		},
		{
			MethodName: "ListGateway",
			Handler:    _GatewayService_ListGateway_Handler,
		},
		{
			MethodName: "DeleteGateway",
			Handler:    _GatewayService_DeleteGateway_Handler,
		},
		{
			MethodName: "CreateServiceBinding",
			Handler:    _GatewayService_CreateServiceBinding_Handler,
		},
		{
			MethodName: "DeleteServiceBinding",
			Handler:    _GatewayService_DeleteServiceBinding_Handler,
		},
		{
			MethodName: "ListServiceBinding",
			Handler:    _GatewayService_ListServiceBinding_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "aistudio/gateway/v1/gateway.proto",
}
