// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v5.29.3
// source: aistudio/gateway/v1/destination.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	DiscoveryService_CreateDestinationService_FullMethodName       = "/apis.aistudio.gateway.v1.DiscoveryService/CreateDestinationService"
	DiscoveryService_UpdateDestinationService_FullMethodName       = "/apis.aistudio.gateway.v1.DiscoveryService/UpdateDestinationService"
	DiscoveryService_GetDestinationService_FullMethodName          = "/apis.aistudio.gateway.v1.DiscoveryService/GetDestinationService"
	DiscoveryService_DeleteDestinationService_FullMethodName       = "/apis.aistudio.gateway.v1.DiscoveryService/DeleteDestinationService"
	DiscoveryService_CreateServiceEntry_FullMethodName             = "/apis.aistudio.gateway.v1.DiscoveryService/CreateServiceEntry"
	DiscoveryService_UpdateServiceEntry_FullMethodName             = "/apis.aistudio.gateway.v1.DiscoveryService/UpdateServiceEntry"
	DiscoveryService_DeleteServiceEntry_FullMethodName             = "/apis.aistudio.gateway.v1.DiscoveryService/DeleteServiceEntry"
	DiscoveryService_ListServiceEntries_FullMethodName             = "/apis.aistudio.gateway.v1.DiscoveryService/ListServiceEntries"
	DiscoveryService_GetServiceEntryDetail_FullMethodName          = "/apis.aistudio.gateway.v1.DiscoveryService/GetServiceEntryDetail"
	DiscoveryService_ListDestinationServices_FullMethodName        = "/apis.aistudio.gateway.v1.DiscoveryService/ListDestinationServices"
	DiscoveryService_ServiceEndpointTrafficRemoval_FullMethodName  = "/apis.aistudio.gateway.v1.DiscoveryService/ServiceEndpointTrafficRemoval"
	DiscoveryService_ServiceEndpointTrafficRecovery_FullMethodName = "/apis.aistudio.gateway.v1.DiscoveryService/ServiceEndpointTrafficRecovery"
	DiscoveryService_ListServiceEntryEndpoints_FullMethodName      = "/apis.aistudio.gateway.v1.DiscoveryService/ListServiceEntryEndpoints"
)

// DiscoveryServiceClient is the client API for DiscoveryService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type DiscoveryServiceClient interface {
	CreateDestinationService(ctx context.Context, in *CreateOrUpdateDestinationServiceRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	UpdateDestinationService(ctx context.Context, in *CreateOrUpdateDestinationServiceRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	GetDestinationService(ctx context.Context, in *GetDestinationServiceRequest, opts ...grpc.CallOption) (*DestinationService, error)
	DeleteDestinationService(ctx context.Context, in *DeleteDestinationServiceRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	CreateServiceEntry(ctx context.Context, in *CreateServiceEntryRequest, opts ...grpc.CallOption) (*CreateServiceEntryResponse, error)
	UpdateServiceEntry(ctx context.Context, in *UpdateServiceEntryRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	DeleteServiceEntry(ctx context.Context, in *DeleteServiceEntryRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	ListServiceEntries(ctx context.Context, in *ListServiceEntriesOptions, opts ...grpc.CallOption) (*ListServiceEntriesResponse, error)
	GetServiceEntryDetail(ctx context.Context, in *GetServiceEntryDetailRequest, opts ...grpc.CallOption) (*ServiceEntry, error)
	ListDestinationServices(ctx context.Context, in *ListDestinationServiceRequest, opts ...grpc.CallOption) (*ListDestinationServiceResponse, error)
	ServiceEndpointTrafficRemoval(ctx context.Context, in *ServiceEndpointTrafficRemovalRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	ServiceEndpointTrafficRecovery(ctx context.Context, in *ServiceEndpointTrafficRecoveryRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	ListServiceEntryEndpoints(ctx context.Context, in *ListServiceEntryEndpointRequest, opts ...grpc.CallOption) (*ListServiceEntryEndpointResponse, error)
}

type discoveryServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewDiscoveryServiceClient(cc grpc.ClientConnInterface) DiscoveryServiceClient {
	return &discoveryServiceClient{cc}
}

func (c *discoveryServiceClient) CreateDestinationService(ctx context.Context, in *CreateOrUpdateDestinationServiceRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, DiscoveryService_CreateDestinationService_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *discoveryServiceClient) UpdateDestinationService(ctx context.Context, in *CreateOrUpdateDestinationServiceRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, DiscoveryService_UpdateDestinationService_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *discoveryServiceClient) GetDestinationService(ctx context.Context, in *GetDestinationServiceRequest, opts ...grpc.CallOption) (*DestinationService, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DestinationService)
	err := c.cc.Invoke(ctx, DiscoveryService_GetDestinationService_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *discoveryServiceClient) DeleteDestinationService(ctx context.Context, in *DeleteDestinationServiceRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, DiscoveryService_DeleteDestinationService_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *discoveryServiceClient) CreateServiceEntry(ctx context.Context, in *CreateServiceEntryRequest, opts ...grpc.CallOption) (*CreateServiceEntryResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateServiceEntryResponse)
	err := c.cc.Invoke(ctx, DiscoveryService_CreateServiceEntry_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *discoveryServiceClient) UpdateServiceEntry(ctx context.Context, in *UpdateServiceEntryRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, DiscoveryService_UpdateServiceEntry_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *discoveryServiceClient) DeleteServiceEntry(ctx context.Context, in *DeleteServiceEntryRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, DiscoveryService_DeleteServiceEntry_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *discoveryServiceClient) ListServiceEntries(ctx context.Context, in *ListServiceEntriesOptions, opts ...grpc.CallOption) (*ListServiceEntriesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListServiceEntriesResponse)
	err := c.cc.Invoke(ctx, DiscoveryService_ListServiceEntries_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *discoveryServiceClient) GetServiceEntryDetail(ctx context.Context, in *GetServiceEntryDetailRequest, opts ...grpc.CallOption) (*ServiceEntry, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ServiceEntry)
	err := c.cc.Invoke(ctx, DiscoveryService_GetServiceEntryDetail_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *discoveryServiceClient) ListDestinationServices(ctx context.Context, in *ListDestinationServiceRequest, opts ...grpc.CallOption) (*ListDestinationServiceResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListDestinationServiceResponse)
	err := c.cc.Invoke(ctx, DiscoveryService_ListDestinationServices_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *discoveryServiceClient) ServiceEndpointTrafficRemoval(ctx context.Context, in *ServiceEndpointTrafficRemovalRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, DiscoveryService_ServiceEndpointTrafficRemoval_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *discoveryServiceClient) ServiceEndpointTrafficRecovery(ctx context.Context, in *ServiceEndpointTrafficRecoveryRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, DiscoveryService_ServiceEndpointTrafficRecovery_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *discoveryServiceClient) ListServiceEntryEndpoints(ctx context.Context, in *ListServiceEntryEndpointRequest, opts ...grpc.CallOption) (*ListServiceEntryEndpointResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListServiceEntryEndpointResponse)
	err := c.cc.Invoke(ctx, DiscoveryService_ListServiceEntryEndpoints_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// DiscoveryServiceServer is the server API for DiscoveryService service.
// All implementations must embed UnimplementedDiscoveryServiceServer
// for forward compatibility
type DiscoveryServiceServer interface {
	CreateDestinationService(context.Context, *CreateOrUpdateDestinationServiceRequest) (*emptypb.Empty, error)
	UpdateDestinationService(context.Context, *CreateOrUpdateDestinationServiceRequest) (*emptypb.Empty, error)
	GetDestinationService(context.Context, *GetDestinationServiceRequest) (*DestinationService, error)
	DeleteDestinationService(context.Context, *DeleteDestinationServiceRequest) (*emptypb.Empty, error)
	CreateServiceEntry(context.Context, *CreateServiceEntryRequest) (*CreateServiceEntryResponse, error)
	UpdateServiceEntry(context.Context, *UpdateServiceEntryRequest) (*emptypb.Empty, error)
	DeleteServiceEntry(context.Context, *DeleteServiceEntryRequest) (*emptypb.Empty, error)
	ListServiceEntries(context.Context, *ListServiceEntriesOptions) (*ListServiceEntriesResponse, error)
	GetServiceEntryDetail(context.Context, *GetServiceEntryDetailRequest) (*ServiceEntry, error)
	ListDestinationServices(context.Context, *ListDestinationServiceRequest) (*ListDestinationServiceResponse, error)
	ServiceEndpointTrafficRemoval(context.Context, *ServiceEndpointTrafficRemovalRequest) (*emptypb.Empty, error)
	ServiceEndpointTrafficRecovery(context.Context, *ServiceEndpointTrafficRecoveryRequest) (*emptypb.Empty, error)
	ListServiceEntryEndpoints(context.Context, *ListServiceEntryEndpointRequest) (*ListServiceEntryEndpointResponse, error)
	mustEmbedUnimplementedDiscoveryServiceServer()
}

// UnimplementedDiscoveryServiceServer must be embedded to have forward compatible implementations.
type UnimplementedDiscoveryServiceServer struct {
}

func (UnimplementedDiscoveryServiceServer) CreateDestinationService(context.Context, *CreateOrUpdateDestinationServiceRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateDestinationService not implemented")
}
func (UnimplementedDiscoveryServiceServer) UpdateDestinationService(context.Context, *CreateOrUpdateDestinationServiceRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateDestinationService not implemented")
}
func (UnimplementedDiscoveryServiceServer) GetDestinationService(context.Context, *GetDestinationServiceRequest) (*DestinationService, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDestinationService not implemented")
}
func (UnimplementedDiscoveryServiceServer) DeleteDestinationService(context.Context, *DeleteDestinationServiceRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteDestinationService not implemented")
}
func (UnimplementedDiscoveryServiceServer) CreateServiceEntry(context.Context, *CreateServiceEntryRequest) (*CreateServiceEntryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateServiceEntry not implemented")
}
func (UnimplementedDiscoveryServiceServer) UpdateServiceEntry(context.Context, *UpdateServiceEntryRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateServiceEntry not implemented")
}
func (UnimplementedDiscoveryServiceServer) DeleteServiceEntry(context.Context, *DeleteServiceEntryRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteServiceEntry not implemented")
}
func (UnimplementedDiscoveryServiceServer) ListServiceEntries(context.Context, *ListServiceEntriesOptions) (*ListServiceEntriesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListServiceEntries not implemented")
}
func (UnimplementedDiscoveryServiceServer) GetServiceEntryDetail(context.Context, *GetServiceEntryDetailRequest) (*ServiceEntry, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetServiceEntryDetail not implemented")
}
func (UnimplementedDiscoveryServiceServer) ListDestinationServices(context.Context, *ListDestinationServiceRequest) (*ListDestinationServiceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListDestinationServices not implemented")
}
func (UnimplementedDiscoveryServiceServer) ServiceEndpointTrafficRemoval(context.Context, *ServiceEndpointTrafficRemovalRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ServiceEndpointTrafficRemoval not implemented")
}
func (UnimplementedDiscoveryServiceServer) ServiceEndpointTrafficRecovery(context.Context, *ServiceEndpointTrafficRecoveryRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ServiceEndpointTrafficRecovery not implemented")
}
func (UnimplementedDiscoveryServiceServer) ListServiceEntryEndpoints(context.Context, *ListServiceEntryEndpointRequest) (*ListServiceEntryEndpointResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListServiceEntryEndpoints not implemented")
}
func (UnimplementedDiscoveryServiceServer) mustEmbedUnimplementedDiscoveryServiceServer() {}

// UnsafeDiscoveryServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to DiscoveryServiceServer will
// result in compilation errors.
type UnsafeDiscoveryServiceServer interface {
	mustEmbedUnimplementedDiscoveryServiceServer()
}

func RegisterDiscoveryServiceServer(s grpc.ServiceRegistrar, srv DiscoveryServiceServer) {
	s.RegisterService(&DiscoveryService_ServiceDesc, srv)
}

func _DiscoveryService_CreateDestinationService_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateOrUpdateDestinationServiceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DiscoveryServiceServer).CreateDestinationService(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DiscoveryService_CreateDestinationService_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DiscoveryServiceServer).CreateDestinationService(ctx, req.(*CreateOrUpdateDestinationServiceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DiscoveryService_UpdateDestinationService_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateOrUpdateDestinationServiceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DiscoveryServiceServer).UpdateDestinationService(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DiscoveryService_UpdateDestinationService_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DiscoveryServiceServer).UpdateDestinationService(ctx, req.(*CreateOrUpdateDestinationServiceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DiscoveryService_GetDestinationService_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDestinationServiceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DiscoveryServiceServer).GetDestinationService(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DiscoveryService_GetDestinationService_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DiscoveryServiceServer).GetDestinationService(ctx, req.(*GetDestinationServiceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DiscoveryService_DeleteDestinationService_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteDestinationServiceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DiscoveryServiceServer).DeleteDestinationService(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DiscoveryService_DeleteDestinationService_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DiscoveryServiceServer).DeleteDestinationService(ctx, req.(*DeleteDestinationServiceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DiscoveryService_CreateServiceEntry_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateServiceEntryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DiscoveryServiceServer).CreateServiceEntry(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DiscoveryService_CreateServiceEntry_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DiscoveryServiceServer).CreateServiceEntry(ctx, req.(*CreateServiceEntryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DiscoveryService_UpdateServiceEntry_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateServiceEntryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DiscoveryServiceServer).UpdateServiceEntry(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DiscoveryService_UpdateServiceEntry_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DiscoveryServiceServer).UpdateServiceEntry(ctx, req.(*UpdateServiceEntryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DiscoveryService_DeleteServiceEntry_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteServiceEntryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DiscoveryServiceServer).DeleteServiceEntry(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DiscoveryService_DeleteServiceEntry_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DiscoveryServiceServer).DeleteServiceEntry(ctx, req.(*DeleteServiceEntryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DiscoveryService_ListServiceEntries_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListServiceEntriesOptions)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DiscoveryServiceServer).ListServiceEntries(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DiscoveryService_ListServiceEntries_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DiscoveryServiceServer).ListServiceEntries(ctx, req.(*ListServiceEntriesOptions))
	}
	return interceptor(ctx, in, info, handler)
}

func _DiscoveryService_GetServiceEntryDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetServiceEntryDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DiscoveryServiceServer).GetServiceEntryDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DiscoveryService_GetServiceEntryDetail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DiscoveryServiceServer).GetServiceEntryDetail(ctx, req.(*GetServiceEntryDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DiscoveryService_ListDestinationServices_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListDestinationServiceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DiscoveryServiceServer).ListDestinationServices(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DiscoveryService_ListDestinationServices_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DiscoveryServiceServer).ListDestinationServices(ctx, req.(*ListDestinationServiceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DiscoveryService_ServiceEndpointTrafficRemoval_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ServiceEndpointTrafficRemovalRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DiscoveryServiceServer).ServiceEndpointTrafficRemoval(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DiscoveryService_ServiceEndpointTrafficRemoval_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DiscoveryServiceServer).ServiceEndpointTrafficRemoval(ctx, req.(*ServiceEndpointTrafficRemovalRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DiscoveryService_ServiceEndpointTrafficRecovery_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ServiceEndpointTrafficRecoveryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DiscoveryServiceServer).ServiceEndpointTrafficRecovery(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DiscoveryService_ServiceEndpointTrafficRecovery_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DiscoveryServiceServer).ServiceEndpointTrafficRecovery(ctx, req.(*ServiceEndpointTrafficRecoveryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DiscoveryService_ListServiceEntryEndpoints_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListServiceEntryEndpointRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DiscoveryServiceServer).ListServiceEntryEndpoints(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DiscoveryService_ListServiceEntryEndpoints_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DiscoveryServiceServer).ListServiceEntryEndpoints(ctx, req.(*ListServiceEntryEndpointRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// DiscoveryService_ServiceDesc is the grpc.ServiceDesc for DiscoveryService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var DiscoveryService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "apis.aistudio.gateway.v1.DiscoveryService",
	HandlerType: (*DiscoveryServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateDestinationService",
			Handler:    _DiscoveryService_CreateDestinationService_Handler,
		},
		{
			MethodName: "UpdateDestinationService",
			Handler:    _DiscoveryService_UpdateDestinationService_Handler,
		},
		{
			MethodName: "GetDestinationService",
			Handler:    _DiscoveryService_GetDestinationService_Handler,
		},
		{
			MethodName: "DeleteDestinationService",
			Handler:    _DiscoveryService_DeleteDestinationService_Handler,
		},
		{
			MethodName: "CreateServiceEntry",
			Handler:    _DiscoveryService_CreateServiceEntry_Handler,
		},
		{
			MethodName: "UpdateServiceEntry",
			Handler:    _DiscoveryService_UpdateServiceEntry_Handler,
		},
		{
			MethodName: "DeleteServiceEntry",
			Handler:    _DiscoveryService_DeleteServiceEntry_Handler,
		},
		{
			MethodName: "ListServiceEntries",
			Handler:    _DiscoveryService_ListServiceEntries_Handler,
		},
		{
			MethodName: "GetServiceEntryDetail",
			Handler:    _DiscoveryService_GetServiceEntryDetail_Handler,
		},
		{
			MethodName: "ListDestinationServices",
			Handler:    _DiscoveryService_ListDestinationServices_Handler,
		},
		{
			MethodName: "ServiceEndpointTrafficRemoval",
			Handler:    _DiscoveryService_ServiceEndpointTrafficRemoval_Handler,
		},
		{
			MethodName: "ServiceEndpointTrafficRecovery",
			Handler:    _DiscoveryService_ServiceEndpointTrafficRecovery_Handler,
		},
		{
			MethodName: "ListServiceEntryEndpoints",
			Handler:    _DiscoveryService_ListServiceEntryEndpoints_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "aistudio/gateway/v1/destination.proto",
}
