// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v5.29.3
// source: aistudio/gateway/v1/gateway.proto

package v1

import (
	common "git.lianjia.com/cloudnative/kic/kic-platform/apis/common"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	_ "google.golang.org/protobuf/types/known/durationpb"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	_ "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ListServiceBindingResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ServiceBindings []*ServiceBinding `protobuf:"bytes,1,rep,name=serviceBindings,proto3" json:"serviceBindings,omitempty"`
	Total           int32             `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
}

func (x *ListServiceBindingResponse) Reset() {
	*x = ListServiceBindingResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_gateway_v1_gateway_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListServiceBindingResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListServiceBindingResponse) ProtoMessage() {}

func (x *ListServiceBindingResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_gateway_v1_gateway_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListServiceBindingResponse.ProtoReflect.Descriptor instead.
func (*ListServiceBindingResponse) Descriptor() ([]byte, []int) {
	return file_aistudio_gateway_v1_gateway_proto_rawDescGZIP(), []int{0}
}

func (x *ListServiceBindingResponse) GetServiceBindings() []*ServiceBinding {
	if x != nil {
		return x.ServiceBindings
	}
	return nil
}

func (x *ListServiceBindingResponse) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

type ServiceBinding struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName    string        `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Gateway          string        `protobuf:"bytes,2,opt,name=gateway,proto3" json:"gateway,omitempty"`
	GatewayId        string        `protobuf:"bytes,3,opt,name=gatewayId,proto3" json:"gatewayId,omitempty"`
	ServiceEntity    *ServiceEntry `protobuf:"bytes,4,opt,name=serviceEntity,proto3" json:"serviceEntity,omitempty"`
	Creator          string        `protobuf:"bytes,5,opt,name=creator,proto3" json:"creator,omitempty"`
	ServiceBindingId string        `protobuf:"bytes,6,opt,name=serviceBindingId,proto3" json:"serviceBindingId,omitempty"`
	CreateTime       string        `protobuf:"bytes,7,opt,name=createTime,proto3" json:"createTime,omitempty"`
	UpdateTime       string        `protobuf:"bytes,8,opt,name=updateTime,proto3" json:"updateTime,omitempty"`
}

func (x *ServiceBinding) Reset() {
	*x = ServiceBinding{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_gateway_v1_gateway_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceBinding) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceBinding) ProtoMessage() {}

func (x *ServiceBinding) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_gateway_v1_gateway_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceBinding.ProtoReflect.Descriptor instead.
func (*ServiceBinding) Descriptor() ([]byte, []int) {
	return file_aistudio_gateway_v1_gateway_proto_rawDescGZIP(), []int{1}
}

func (x *ServiceBinding) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *ServiceBinding) GetGateway() string {
	if x != nil {
		return x.Gateway
	}
	return ""
}

func (x *ServiceBinding) GetGatewayId() string {
	if x != nil {
		return x.GatewayId
	}
	return ""
}

func (x *ServiceBinding) GetServiceEntity() *ServiceEntry {
	if x != nil {
		return x.ServiceEntity
	}
	return nil
}

func (x *ServiceBinding) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *ServiceBinding) GetServiceBindingId() string {
	if x != nil {
		return x.ServiceBindingId
	}
	return ""
}

func (x *ServiceBinding) GetCreateTime() string {
	if x != nil {
		return x.CreateTime
	}
	return ""
}

func (x *ServiceBinding) GetUpdateTime() string {
	if x != nil {
		return x.UpdateTime
	}
	return ""
}

type CreateServiceBindingRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName             string                     `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Gateway                   string                     `protobuf:"bytes,2,opt,name=gateway,proto3" json:"gateway,omitempty"`
	DestinationName           string                     `protobuf:"bytes,3,opt,name=destinationName,proto3" json:"destinationName,omitempty"`
	Description               string                     `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	TopologySpreadConstraints *TopologySpreadConstraints `protobuf:"bytes,5,opt,name=topologySpreadConstraints,proto3" json:"topologySpreadConstraints,omitempty"` //拓扑约束
	LoadBalanceStrategy       *LoadBalanceStrategy       `protobuf:"bytes,6,opt,name=loadBalanceStrategy,proto3" json:"loadBalanceStrategy,omitempty"`             //负载均衡策略
	Warmup                    *Warmup                    `protobuf:"bytes,7,opt,name=warmup,proto3" json:"warmup,omitempty"`                                       //预热时间
}

func (x *CreateServiceBindingRequest) Reset() {
	*x = CreateServiceBindingRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_gateway_v1_gateway_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateServiceBindingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateServiceBindingRequest) ProtoMessage() {}

func (x *CreateServiceBindingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_gateway_v1_gateway_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateServiceBindingRequest.ProtoReflect.Descriptor instead.
func (*CreateServiceBindingRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_gateway_v1_gateway_proto_rawDescGZIP(), []int{2}
}

func (x *CreateServiceBindingRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *CreateServiceBindingRequest) GetGateway() string {
	if x != nil {
		return x.Gateway
	}
	return ""
}

func (x *CreateServiceBindingRequest) GetDestinationName() string {
	if x != nil {
		return x.DestinationName
	}
	return ""
}

func (x *CreateServiceBindingRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CreateServiceBindingRequest) GetTopologySpreadConstraints() *TopologySpreadConstraints {
	if x != nil {
		return x.TopologySpreadConstraints
	}
	return nil
}

func (x *CreateServiceBindingRequest) GetLoadBalanceStrategy() *LoadBalanceStrategy {
	if x != nil {
		return x.LoadBalanceStrategy
	}
	return nil
}

func (x *CreateServiceBindingRequest) GetWarmup() *Warmup {
	if x != nil {
		return x.Warmup
	}
	return nil
}

type CreateServiceBindingResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *CreateServiceBindingResponse) Reset() {
	*x = CreateServiceBindingResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_gateway_v1_gateway_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateServiceBindingResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateServiceBindingResponse) ProtoMessage() {}

func (x *CreateServiceBindingResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_gateway_v1_gateway_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateServiceBindingResponse.ProtoReflect.Descriptor instead.
func (*CreateServiceBindingResponse) Descriptor() ([]byte, []int) {
	return file_aistudio_gateway_v1_gateway_proto_rawDescGZIP(), []int{3}
}

func (x *CreateServiceBindingResponse) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type UpdateServiceBindingResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *UpdateServiceBindingResponse) Reset() {
	*x = UpdateServiceBindingResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_gateway_v1_gateway_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateServiceBindingResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateServiceBindingResponse) ProtoMessage() {}

func (x *UpdateServiceBindingResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_gateway_v1_gateway_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateServiceBindingResponse.ProtoReflect.Descriptor instead.
func (*UpdateServiceBindingResponse) Descriptor() ([]byte, []int) {
	return file_aistudio_gateway_v1_gateway_proto_rawDescGZIP(), []int{4}
}

func (x *UpdateServiceBindingResponse) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type UpdateServiceBindingRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ServiceBindingId          string                     `protobuf:"bytes,1,opt,name=serviceBindingId,proto3" json:"serviceBindingId,omitempty"`
	TopologySpreadConstraints *TopologySpreadConstraints `protobuf:"bytes,2,opt,name=topologySpreadConstraints,proto3" json:"topologySpreadConstraints,omitempty"` //拓扑约束
	LoadBalanceStrategy       *LoadBalanceStrategy       `protobuf:"bytes,3,opt,name=loadBalanceStrategy,proto3" json:"loadBalanceStrategy,omitempty"`             //负载均衡策略
	Warmup                    *Warmup                    `protobuf:"bytes,4,opt,name=warmup,proto3" json:"warmup,omitempty"`                                       //预热时间
}

func (x *UpdateServiceBindingRequest) Reset() {
	*x = UpdateServiceBindingRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_gateway_v1_gateway_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateServiceBindingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateServiceBindingRequest) ProtoMessage() {}

func (x *UpdateServiceBindingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_gateway_v1_gateway_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateServiceBindingRequest.ProtoReflect.Descriptor instead.
func (*UpdateServiceBindingRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_gateway_v1_gateway_proto_rawDescGZIP(), []int{5}
}

func (x *UpdateServiceBindingRequest) GetServiceBindingId() string {
	if x != nil {
		return x.ServiceBindingId
	}
	return ""
}

func (x *UpdateServiceBindingRequest) GetTopologySpreadConstraints() *TopologySpreadConstraints {
	if x != nil {
		return x.TopologySpreadConstraints
	}
	return nil
}

func (x *UpdateServiceBindingRequest) GetLoadBalanceStrategy() *LoadBalanceStrategy {
	if x != nil {
		return x.LoadBalanceStrategy
	}
	return nil
}

func (x *UpdateServiceBindingRequest) GetWarmup() *Warmup {
	if x != nil {
		return x.Warmup
	}
	return nil
}

type DeleteServiceBindingRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ServiceBindingId string `protobuf:"bytes,1,opt,name=serviceBindingId,proto3" json:"serviceBindingId,omitempty"`
}

func (x *DeleteServiceBindingRequest) Reset() {
	*x = DeleteServiceBindingRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_gateway_v1_gateway_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteServiceBindingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteServiceBindingRequest) ProtoMessage() {}

func (x *DeleteServiceBindingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_gateway_v1_gateway_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteServiceBindingRequest.ProtoReflect.Descriptor instead.
func (*DeleteServiceBindingRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_gateway_v1_gateway_proto_rawDescGZIP(), []int{6}
}

func (x *DeleteServiceBindingRequest) GetServiceBindingId() string {
	if x != nil {
		return x.ServiceBindingId
	}
	return ""
}

type GetServiceBindingRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ServiceBindingId string `protobuf:"bytes,1,opt,name=serviceBindingId,proto3" json:"serviceBindingId,omitempty"`
}

func (x *GetServiceBindingRequest) Reset() {
	*x = GetServiceBindingRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_gateway_v1_gateway_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetServiceBindingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetServiceBindingRequest) ProtoMessage() {}

func (x *GetServiceBindingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_gateway_v1_gateway_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetServiceBindingRequest.ProtoReflect.Descriptor instead.
func (*GetServiceBindingRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_gateway_v1_gateway_proto_rawDescGZIP(), []int{7}
}

func (x *GetServiceBindingRequest) GetServiceBindingId() string {
	if x != nil {
		return x.ServiceBindingId
	}
	return ""
}

type ListServiceBindingOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName     string `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	DestinationName   string `protobuf:"bytes,2,opt,name=destinationName,proto3" json:"destinationName,omitempty"`
	GatewayId         string `protobuf:"bytes,3,opt,name=gatewayId,proto3" json:"gatewayId,omitempty"`
	FilterAppNotExist bool   `protobuf:"varint,4,opt,name=filterAppNotExist,proto3" json:"filterAppNotExist,omitempty"`
}

func (x *ListServiceBindingOptions) Reset() {
	*x = ListServiceBindingOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_gateway_v1_gateway_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListServiceBindingOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListServiceBindingOptions) ProtoMessage() {}

func (x *ListServiceBindingOptions) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_gateway_v1_gateway_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListServiceBindingOptions.ProtoReflect.Descriptor instead.
func (*ListServiceBindingOptions) Descriptor() ([]byte, []int) {
	return file_aistudio_gateway_v1_gateway_proto_rawDescGZIP(), []int{8}
}

func (x *ListServiceBindingOptions) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *ListServiceBindingOptions) GetDestinationName() string {
	if x != nil {
		return x.DestinationName
	}
	return ""
}

func (x *ListServiceBindingOptions) GetGatewayId() string {
	if x != nil {
		return x.GatewayId
	}
	return ""
}

func (x *ListServiceBindingOptions) GetFilterAppNotExist() bool {
	if x != nil {
		return x.FilterAppNotExist
	}
	return false
}

type Gateway struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id            string         `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name          string         `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	DisplayName   string         `protobuf:"bytes,3,opt,name=displayName,proto3" json:"displayName,omitempty"`
	Description   string         `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	Region        string         `protobuf:"bytes,5,opt,name=region,proto3" json:"region,omitempty"`
	Zone          string         `protobuf:"bytes,6,opt,name=zone,proto3" json:"zone,omitempty"`
	Idc           string         `protobuf:"bytes,7,opt,name=idc,proto3" json:"idc,omitempty"`
	Vips          []string       `protobuf:"bytes,8,rep,name=vips,proto3" json:"vips,omitempty"`
	WorkspaceName string         `protobuf:"bytes,9,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"` //创建租户空间
	GatewayClass  string         `protobuf:"bytes,10,opt,name=gatewayClass,proto3" json:"gatewayClass,omitempty"`  //网关类型
	Protocol      string         `protobuf:"bytes,11,opt,name=protocol,proto3" json:"protocol,omitempty"`
	Creator       string         `protobuf:"bytes,12,opt,name=creator,proto3" json:"creator,omitempty"`
	Cluster       string         `protobuf:"bytes,13,opt,name=cluster,proto3" json:"cluster,omitempty"`
	GatewayStatus *GatewayStatus `protobuf:"bytes,14,opt,name=GatewayStatus,proto3" json:"GatewayStatus,omitempty"` //网关状态
	IsDefault     bool           `protobuf:"varint,15,opt,name=isDefault,proto3" json:"isDefault,omitempty"`
	Enabled       bool           `protobuf:"varint,16,opt,name=enabled,proto3" json:"enabled,omitempty"`
}

func (x *Gateway) Reset() {
	*x = Gateway{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_gateway_v1_gateway_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Gateway) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Gateway) ProtoMessage() {}

func (x *Gateway) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_gateway_v1_gateway_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Gateway.ProtoReflect.Descriptor instead.
func (*Gateway) Descriptor() ([]byte, []int) {
	return file_aistudio_gateway_v1_gateway_proto_rawDescGZIP(), []int{9}
}

func (x *Gateway) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Gateway) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Gateway) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *Gateway) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Gateway) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *Gateway) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *Gateway) GetIdc() string {
	if x != nil {
		return x.Idc
	}
	return ""
}

func (x *Gateway) GetVips() []string {
	if x != nil {
		return x.Vips
	}
	return nil
}

func (x *Gateway) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *Gateway) GetGatewayClass() string {
	if x != nil {
		return x.GatewayClass
	}
	return ""
}

func (x *Gateway) GetProtocol() string {
	if x != nil {
		return x.Protocol
	}
	return ""
}

func (x *Gateway) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *Gateway) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

func (x *Gateway) GetGatewayStatus() *GatewayStatus {
	if x != nil {
		return x.GatewayStatus
	}
	return nil
}

func (x *Gateway) GetIsDefault() bool {
	if x != nil {
		return x.IsDefault
	}
	return false
}

func (x *Gateway) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

type GatewayStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Conditions []*common.Condition `protobuf:"bytes,1,rep,name=conditions,proto3" json:"conditions,omitempty"`
}

func (x *GatewayStatus) Reset() {
	*x = GatewayStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_gateway_v1_gateway_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GatewayStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GatewayStatus) ProtoMessage() {}

func (x *GatewayStatus) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_gateway_v1_gateway_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GatewayStatus.ProtoReflect.Descriptor instead.
func (*GatewayStatus) Descriptor() ([]byte, []int) {
	return file_aistudio_gateway_v1_gateway_proto_rawDescGZIP(), []int{10}
}

func (x *GatewayStatus) GetConditions() []*common.Condition {
	if x != nil {
		return x.Conditions
	}
	return nil
}

type CreateOrUpdateGatewayRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name          string   `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	DisplayName   string   `protobuf:"bytes,2,opt,name=displayName,proto3" json:"displayName,omitempty"`
	Description   string   `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	Region        string   `protobuf:"bytes,4,opt,name=region,proto3" json:"region,omitempty"`
	Zone          string   `protobuf:"bytes,5,opt,name=zone,proto3" json:"zone,omitempty"`
	Idc           string   `protobuf:"bytes,6,opt,name=idc,proto3" json:"idc,omitempty"`
	WorkspaceName string   `protobuf:"bytes,8,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"` //创建租户空间
	GatewayClass  string   `protobuf:"bytes,9,opt,name=gatewayClass,proto3" json:"gatewayClass,omitempty"`   //网关类型
	Protocol      string   `protobuf:"bytes,10,opt,name=protocol,proto3" json:"protocol,omitempty"`
	Cluster       string   `protobuf:"bytes,11,opt,name=cluster,proto3" json:"cluster,omitempty"`
	Vips          []string `protobuf:"bytes,12,rep,name=vips,proto3" json:"vips,omitempty"`            //vip
	Enabled       bool     `protobuf:"varint,13,opt,name=enabled,proto3" json:"enabled,omitempty"`     //是否可用
	IsDefault     bool     `protobuf:"varint,14,opt,name=isDefault,proto3" json:"isDefault,omitempty"` //是否为默认网关
}

func (x *CreateOrUpdateGatewayRequest) Reset() {
	*x = CreateOrUpdateGatewayRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_gateway_v1_gateway_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateOrUpdateGatewayRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateOrUpdateGatewayRequest) ProtoMessage() {}

func (x *CreateOrUpdateGatewayRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_gateway_v1_gateway_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateOrUpdateGatewayRequest.ProtoReflect.Descriptor instead.
func (*CreateOrUpdateGatewayRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_gateway_v1_gateway_proto_rawDescGZIP(), []int{11}
}

func (x *CreateOrUpdateGatewayRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateOrUpdateGatewayRequest) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *CreateOrUpdateGatewayRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CreateOrUpdateGatewayRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *CreateOrUpdateGatewayRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *CreateOrUpdateGatewayRequest) GetIdc() string {
	if x != nil {
		return x.Idc
	}
	return ""
}

func (x *CreateOrUpdateGatewayRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *CreateOrUpdateGatewayRequest) GetGatewayClass() string {
	if x != nil {
		return x.GatewayClass
	}
	return ""
}

func (x *CreateOrUpdateGatewayRequest) GetProtocol() string {
	if x != nil {
		return x.Protocol
	}
	return ""
}

func (x *CreateOrUpdateGatewayRequest) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

func (x *CreateOrUpdateGatewayRequest) GetVips() []string {
	if x != nil {
		return x.Vips
	}
	return nil
}

func (x *CreateOrUpdateGatewayRequest) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

func (x *CreateOrUpdateGatewayRequest) GetIsDefault() bool {
	if x != nil {
		return x.IsDefault
	}
	return false
}

type GetGatewayRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName string `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Name          string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *GetGatewayRequest) Reset() {
	*x = GetGatewayRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_gateway_v1_gateway_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetGatewayRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGatewayRequest) ProtoMessage() {}

func (x *GetGatewayRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_gateway_v1_gateway_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGatewayRequest.ProtoReflect.Descriptor instead.
func (*GetGatewayRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_gateway_v1_gateway_proto_rawDescGZIP(), []int{12}
}

func (x *GetGatewayRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *GetGatewayRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type ListGatewayOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Region        string `protobuf:"bytes,1,opt,name=region,proto3" json:"region,omitempty"`
	Zone          string `protobuf:"bytes,2,opt,name=zone,proto3" json:"zone,omitempty"`
	Idc           string `protobuf:"bytes,3,opt,name=idc,proto3" json:"idc,omitempty"`
	WorkspaceName string `protobuf:"bytes,4,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Name          string `protobuf:"bytes,5,opt,name=name,proto3" json:"name,omitempty"`
	Creator       string `protobuf:"bytes,6,opt,name=creator,proto3" json:"creator,omitempty"`
	Enabled       string `protobuf:"bytes,7,opt,name=enabled,proto3" json:"enabled,omitempty"`     //是否可用
	IsDefault     string `protobuf:"bytes,8,opt,name=isDefault,proto3" json:"isDefault,omitempty"` //是否为默认网关
}

func (x *ListGatewayOptions) Reset() {
	*x = ListGatewayOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_gateway_v1_gateway_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListGatewayOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListGatewayOptions) ProtoMessage() {}

func (x *ListGatewayOptions) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_gateway_v1_gateway_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListGatewayOptions.ProtoReflect.Descriptor instead.
func (*ListGatewayOptions) Descriptor() ([]byte, []int) {
	return file_aistudio_gateway_v1_gateway_proto_rawDescGZIP(), []int{13}
}

func (x *ListGatewayOptions) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *ListGatewayOptions) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *ListGatewayOptions) GetIdc() string {
	if x != nil {
		return x.Idc
	}
	return ""
}

func (x *ListGatewayOptions) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *ListGatewayOptions) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ListGatewayOptions) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *ListGatewayOptions) GetEnabled() string {
	if x != nil {
		return x.Enabled
	}
	return ""
}

func (x *ListGatewayOptions) GetIsDefault() string {
	if x != nil {
		return x.IsDefault
	}
	return ""
}

type ListGatewayResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Gateways []*Gateway `protobuf:"bytes,1,rep,name=gateways,proto3" json:"gateways,omitempty"`
}

func (x *ListGatewayResponse) Reset() {
	*x = ListGatewayResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_gateway_v1_gateway_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListGatewayResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListGatewayResponse) ProtoMessage() {}

func (x *ListGatewayResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_gateway_v1_gateway_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListGatewayResponse.ProtoReflect.Descriptor instead.
func (*ListGatewayResponse) Descriptor() ([]byte, []int) {
	return file_aistudio_gateway_v1_gateway_proto_rawDescGZIP(), []int{14}
}

func (x *ListGatewayResponse) GetGateways() []*Gateway {
	if x != nil {
		return x.Gateways
	}
	return nil
}

type DeleteGatewayRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName string `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Name          string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *DeleteGatewayRequest) Reset() {
	*x = DeleteGatewayRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_gateway_v1_gateway_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteGatewayRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteGatewayRequest) ProtoMessage() {}

func (x *DeleteGatewayRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_gateway_v1_gateway_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteGatewayRequest.ProtoReflect.Descriptor instead.
func (*DeleteGatewayRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_gateway_v1_gateway_proto_rawDescGZIP(), []int{15}
}

func (x *DeleteGatewayRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *DeleteGatewayRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type DeleteGatewayResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success bool   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Reason  string `protobuf:"bytes,3,opt,name=reason,proto3" json:"reason,omitempty"`
}

func (x *DeleteGatewayResponse) Reset() {
	*x = DeleteGatewayResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_gateway_v1_gateway_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteGatewayResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteGatewayResponse) ProtoMessage() {}

func (x *DeleteGatewayResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_gateway_v1_gateway_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteGatewayResponse.ProtoReflect.Descriptor instead.
func (*DeleteGatewayResponse) Descriptor() ([]byte, []int) {
	return file_aistudio_gateway_v1_gateway_proto_rawDescGZIP(), []int{16}
}

func (x *DeleteGatewayResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *DeleteGatewayResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *DeleteGatewayResponse) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

var File_aistudio_gateway_v1_gateway_proto protoreflect.FileDescriptor

var file_aistudio_gateway_v1_gateway_proto_rawDesc = []byte{
	0x0a, 0x21, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2f, 0x67, 0x61, 0x74, 0x65, 0x77,
	0x61, 0x79, 0x2f, 0x76, 0x31, 0x2f, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x18, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64,
	0x69, 0x6f, 0x2e, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x64, 0x75, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x13, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74,
	0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x25, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69,
	0x6f, 0x2f, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x65, 0x73,
	0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x86,
	0x01, 0x0a, 0x1a, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x42, 0x69,
	0x6e, 0x64, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x52, 0x0a,
	0x0f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69,
	0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x52, 0x0f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x73, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x22, 0xc2, 0x02, 0x0a, 0x0e, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f,
	0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x18, 0x0a, 0x07, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x12, 0x1c, 0x0a, 0x09, 0x67, 0x61,
	0x74, 0x65, 0x77, 0x61, 0x79, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x67,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x49, 0x64, 0x12, 0x4c, 0x0a, 0x0d, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x26, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e,
	0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f,
	0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72,
	0x12, 0x2a, 0x0a, 0x10, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x42, 0x69, 0x6e, 0x64, 0x69,
	0x6e, 0x67, 0x49, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x22, 0xb7, 0x03, 0x0a,
	0x1b, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x42, 0x69,
	0x6e, 0x64, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x0d,
	0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x12, 0x28, 0x0a, 0x0f,
	0x64, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x64, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x71, 0x0a, 0x19, 0x74, 0x6f, 0x70, 0x6f,
	0x6c, 0x6f, 0x67, 0x79, 0x53, 0x70, 0x72, 0x65, 0x61, 0x64, 0x43, 0x6f, 0x6e, 0x73, 0x74, 0x72,
	0x61, 0x69, 0x6e, 0x74, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x61, 0x70,
	0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x6f, 0x70, 0x6f, 0x6c, 0x6f, 0x67, 0x79, 0x53,
	0x70, 0x72, 0x65, 0x61, 0x64, 0x43, 0x6f, 0x6e, 0x73, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x74, 0x73,
	0x52, 0x19, 0x74, 0x6f, 0x70, 0x6f, 0x6c, 0x6f, 0x67, 0x79, 0x53, 0x70, 0x72, 0x65, 0x61, 0x64,
	0x43, 0x6f, 0x6e, 0x73, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x74, 0x73, 0x12, 0x5f, 0x0a, 0x13, 0x6c,
	0x6f, 0x61, 0x64, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65,
	0x67, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e,
	0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79,
	0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x61, 0x64, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x53,
	0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x52, 0x13, 0x6c, 0x6f, 0x61, 0x64, 0x42, 0x61, 0x6c,
	0x61, 0x6e, 0x63, 0x65, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x12, 0x38, 0x0a, 0x06,
	0x77, 0x61, 0x72, 0x6d, 0x75, 0x70, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x61,
	0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x67, 0x61, 0x74,
	0x65, 0x77, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x61, 0x72, 0x6d, 0x75, 0x70, 0x52, 0x06,
	0x77, 0x61, 0x72, 0x6d, 0x75, 0x70, 0x22, 0x2e, 0x0a, 0x1c, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0x2e, 0x0a, 0x1c, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0xd7, 0x02, 0x0a, 0x1b, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2a, 0x0a, 0x10, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x10, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x49, 0x64, 0x12, 0x71, 0x0a, 0x19, 0x74, 0x6f, 0x70, 0x6f, 0x6c, 0x6f, 0x67, 0x79, 0x53, 0x70,
	0x72, 0x65, 0x61, 0x64, 0x43, 0x6f, 0x6e, 0x73, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x74, 0x73, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73,
	0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x76, 0x31,
	0x2e, 0x54, 0x6f, 0x70, 0x6f, 0x6c, 0x6f, 0x67, 0x79, 0x53, 0x70, 0x72, 0x65, 0x61, 0x64, 0x43,
	0x6f, 0x6e, 0x73, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x74, 0x73, 0x52, 0x19, 0x74, 0x6f, 0x70, 0x6f,
	0x6c, 0x6f, 0x67, 0x79, 0x53, 0x70, 0x72, 0x65, 0x61, 0x64, 0x43, 0x6f, 0x6e, 0x73, 0x74, 0x72,
	0x61, 0x69, 0x6e, 0x74, 0x73, 0x12, 0x5f, 0x0a, 0x13, 0x6c, 0x6f, 0x61, 0x64, 0x42, 0x61, 0x6c,
	0x61, 0x6e, 0x63, 0x65, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64,
	0x69, 0x6f, 0x2e, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f,
	0x61, 0x64, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67,
	0x79, 0x52, 0x13, 0x6c, 0x6f, 0x61, 0x64, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x53, 0x74,
	0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x12, 0x38, 0x0a, 0x06, 0x77, 0x61, 0x72, 0x6d, 0x75, 0x70,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69,
	0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x76,
	0x31, 0x2e, 0x57, 0x61, 0x72, 0x6d, 0x75, 0x70, 0x52, 0x06, 0x77, 0x61, 0x72, 0x6d, 0x75, 0x70,
	0x22, 0x49, 0x0a, 0x1b, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x2a, 0x0a, 0x10, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x49, 0x64, 0x22, 0x46, 0x0a, 0x18, 0x47,
	0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2a, 0x0a, 0x10, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x10, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x49, 0x64, 0x22, 0xb7, 0x01, 0x0a, 0x19, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x28, 0x0a, 0x0f, 0x64, 0x65, 0x73, 0x74, 0x69,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0f, 0x64, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x1c, 0x0a, 0x09, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x49, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x49, 0x64, 0x12,
	0x2c, 0x0a, 0x11, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x41, 0x70, 0x70, 0x4e, 0x6f, 0x74, 0x45,
	0x78, 0x69, 0x73, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x11, 0x66, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x41, 0x70, 0x70, 0x4e, 0x6f, 0x74, 0x45, 0x78, 0x69, 0x73, 0x74, 0x22, 0xe4, 0x03,
	0x0a, 0x07, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a,
	0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x6e,
	0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x10, 0x0a,
	0x03, 0x69, 0x64, 0x63, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x69, 0x64, 0x63, 0x12,
	0x12, 0x0a, 0x04, 0x76, 0x69, 0x70, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x76,
	0x69, 0x70, 0x73, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x67, 0x61, 0x74,
	0x65, 0x77, 0x61, 0x79, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x12, 0x1a, 0x0a,
	0x08, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x6f, 0x72, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x6f, 0x72, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x18, 0x0d,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x12, 0x4d, 0x0a,
	0x0d, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0e,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74,
	0x75, 0x64, 0x69, 0x6f, 0x2e, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0d, 0x47,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1c, 0x0a, 0x09,
	0x69, 0x73, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x09, 0x69, 0x73, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x10, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x65, 0x6e, 0x61,
	0x62, 0x6c, 0x65, 0x64, 0x22, 0x47, 0x0a, 0x0d, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x36, 0x0a, 0x0a, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x70, 0x69, 0x73,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x0a, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x80, 0x03,
	0x0a, 0x1c, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x12,
	0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f,
	0x6e, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x64, 0x63, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x69, 0x64, 0x63, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72,
	0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x67, 0x61,
	0x74, 0x65, 0x77, 0x61, 0x79, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x12, 0x1a,
	0x0a, 0x08, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6c,
	0x75, 0x73, 0x74, 0x65, 0x72, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6c, 0x75,
	0x73, 0x74, 0x65, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x76, 0x69, 0x70, 0x73, 0x18, 0x0c, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x04, 0x76, 0x69, 0x70, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x69, 0x73, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x18,
	0x0e, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69, 0x73, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74,
	0x22, 0x4d, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f,
	0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22,
	0xde, 0x01, 0x0a, 0x12, 0x4c, 0x69, 0x73, 0x74, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x4f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x12,
	0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f,
	0x6e, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x64, 0x63, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x69, 0x64, 0x63, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72,
	0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18,
	0x0a, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x69, 0x73, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x69, 0x73, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74,
	0x22, 0x54, 0x0a, 0x13, 0x4c, 0x69, 0x73, 0x74, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3d, 0x0a, 0x08, 0x67, 0x61, 0x74, 0x65, 0x77,
	0x61, 0x79, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x73,
	0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x52, 0x08, 0x67, 0x61,
	0x74, 0x65, 0x77, 0x61, 0x79, 0x73, 0x22, 0x50, 0x0a, 0x14, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24,
	0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x63, 0x0a, 0x15, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x32, 0xb8, 0x0a,
	0x0a, 0x0e, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x12, 0xa1, 0x01, 0x0a, 0x0d, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x47, 0x61, 0x74, 0x65, 0x77,
	0x61, 0x79, 0x12, 0x36, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64,
	0x69, 0x6f, 0x2e, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x47, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x21, 0x2e, 0x61, 0x70, 0x69,
	0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x67, 0x61, 0x74, 0x65, 0x77,
	0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x22, 0x35, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x2f, 0x3a, 0x01, 0x2a, 0x1a, 0x2a, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f,
	0x76, 0x31, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2f, 0x7b, 0x77, 0x6f,
	0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x67, 0x61, 0x74,
	0x65, 0x77, 0x61, 0x79, 0x12, 0xa8, 0x01, 0x0a, 0x0d, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x47,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x12, 0x36, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69,
	0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x21,
	0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x67,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x22, 0x3c, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x36, 0x3a, 0x01, 0x2a, 0x22, 0x31, 0x2f, 0x61,
	0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x2f, 0x7b, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d,
	0x2f, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x12,
	0x97, 0x01, 0x0a, 0x0a, 0x47, 0x65, 0x74, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x12, 0x2b,
	0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x67,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x47, 0x61, 0x74,
	0x65, 0x77, 0x61, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x21, 0x2e, 0x61, 0x70,
	0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x22, 0x39,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x33, 0x12, 0x31, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31,
	0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2f, 0x7b, 0x77, 0x6f, 0x72, 0x6b,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x67, 0x61, 0x74, 0x65, 0x77,
	0x61, 0x79, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x12, 0x9f, 0x01, 0x0a, 0x0b, 0x4c, 0x69,
	0x73, 0x74, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x12, 0x2c, 0x2e, 0x61, 0x70, 0x69, 0x73,
	0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79,
	0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x1a, 0x2d, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61,
	0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x33, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2d, 0x12, 0x2b,
	0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x2f, 0x7b, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x7d, 0x2f, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x73, 0x12, 0xab, 0x01, 0x0a, 0x0d,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x12, 0x2e, 0x2e,
	0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x67, 0x61,
	0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x47,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2f, 0x2e,
	0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x67, 0x61,
	0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x47,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x39,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x33, 0x2a, 0x31, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31,
	0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2f, 0x7b, 0x77, 0x6f, 0x72, 0x6b,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x67, 0x61, 0x74, 0x65, 0x77,
	0x61, 0x79, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x12, 0xaa, 0x01, 0x0a, 0x14, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x42, 0x69, 0x6e, 0x64, 0x69,
	0x6e, 0x67, 0x12, 0x35, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64,
	0x69, 0x6f, 0x2e, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x42, 0x69, 0x6e, 0x64, 0x69,
	0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x36, 0x2e, 0x61, 0x70, 0x69, 0x73,
	0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x23, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1d, 0x3a, 0x01, 0x2a, 0x22, 0x18, 0x2f, 0x61,
	0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2d, 0x62,
	0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x9a, 0x01, 0x0a, 0x14, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x12,
	0x35, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e,
	0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x33,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2d, 0x2a, 0x2b, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31,
	0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2d, 0x62, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x2f, 0x7b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x49, 0x64, 0x7d, 0x12, 0xa2, 0x01, 0x0a, 0x12, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x33, 0x2e, 0x61, 0x70, 0x69,
	0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x67, 0x61, 0x74, 0x65, 0x77,
	0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x1a,
	0x34, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e,
	0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x21, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1b, 0x12, 0x19, 0x2f,
	0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2d,
	0x62, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x42, 0x4a, 0x5a, 0x48, 0x67, 0x69, 0x74, 0x2e,
	0x6c, 0x69, 0x61, 0x6e, 0x6a, 0x69, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x63, 0x6c, 0x6f, 0x75,
	0x64, 0x6e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x2f, 0x6b, 0x69, 0x63, 0x2f, 0x6b, 0x69, 0x63, 0x2d,
	0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x61, 0x69,
	0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2f, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x76,
	0x31, 0x3b, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_aistudio_gateway_v1_gateway_proto_rawDescOnce sync.Once
	file_aistudio_gateway_v1_gateway_proto_rawDescData = file_aistudio_gateway_v1_gateway_proto_rawDesc
)

func file_aistudio_gateway_v1_gateway_proto_rawDescGZIP() []byte {
	file_aistudio_gateway_v1_gateway_proto_rawDescOnce.Do(func() {
		file_aistudio_gateway_v1_gateway_proto_rawDescData = protoimpl.X.CompressGZIP(file_aistudio_gateway_v1_gateway_proto_rawDescData)
	})
	return file_aistudio_gateway_v1_gateway_proto_rawDescData
}

var file_aistudio_gateway_v1_gateway_proto_msgTypes = make([]protoimpl.MessageInfo, 17)
var file_aistudio_gateway_v1_gateway_proto_goTypes = []any{
	(*ListServiceBindingResponse)(nil),   // 0: apis.aistudio.gateway.v1.ListServiceBindingResponse
	(*ServiceBinding)(nil),               // 1: apis.aistudio.gateway.v1.ServiceBinding
	(*CreateServiceBindingRequest)(nil),  // 2: apis.aistudio.gateway.v1.CreateServiceBindingRequest
	(*CreateServiceBindingResponse)(nil), // 3: apis.aistudio.gateway.v1.CreateServiceBindingResponse
	(*UpdateServiceBindingResponse)(nil), // 4: apis.aistudio.gateway.v1.UpdateServiceBindingResponse
	(*UpdateServiceBindingRequest)(nil),  // 5: apis.aistudio.gateway.v1.UpdateServiceBindingRequest
	(*DeleteServiceBindingRequest)(nil),  // 6: apis.aistudio.gateway.v1.DeleteServiceBindingRequest
	(*GetServiceBindingRequest)(nil),     // 7: apis.aistudio.gateway.v1.GetServiceBindingRequest
	(*ListServiceBindingOptions)(nil),    // 8: apis.aistudio.gateway.v1.ListServiceBindingOptions
	(*Gateway)(nil),                      // 9: apis.aistudio.gateway.v1.Gateway
	(*GatewayStatus)(nil),                // 10: apis.aistudio.gateway.v1.GatewayStatus
	(*CreateOrUpdateGatewayRequest)(nil), // 11: apis.aistudio.gateway.v1.CreateOrUpdateGatewayRequest
	(*GetGatewayRequest)(nil),            // 12: apis.aistudio.gateway.v1.GetGatewayRequest
	(*ListGatewayOptions)(nil),           // 13: apis.aistudio.gateway.v1.ListGatewayOptions
	(*ListGatewayResponse)(nil),          // 14: apis.aistudio.gateway.v1.ListGatewayResponse
	(*DeleteGatewayRequest)(nil),         // 15: apis.aistudio.gateway.v1.DeleteGatewayRequest
	(*DeleteGatewayResponse)(nil),        // 16: apis.aistudio.gateway.v1.DeleteGatewayResponse
	(*ServiceEntry)(nil),                 // 17: apis.aistudio.gateway.v1.ServiceEntry
	(*TopologySpreadConstraints)(nil),    // 18: apis.aistudio.gateway.v1.TopologySpreadConstraints
	(*LoadBalanceStrategy)(nil),          // 19: apis.aistudio.gateway.v1.LoadBalanceStrategy
	(*Warmup)(nil),                       // 20: apis.aistudio.gateway.v1.Warmup
	(*common.Condition)(nil),             // 21: apis.common.Condition
	(*emptypb.Empty)(nil),                // 22: google.protobuf.Empty
}
var file_aistudio_gateway_v1_gateway_proto_depIdxs = []int32{
	1,  // 0: apis.aistudio.gateway.v1.ListServiceBindingResponse.serviceBindings:type_name -> apis.aistudio.gateway.v1.ServiceBinding
	17, // 1: apis.aistudio.gateway.v1.ServiceBinding.serviceEntity:type_name -> apis.aistudio.gateway.v1.ServiceEntry
	18, // 2: apis.aistudio.gateway.v1.CreateServiceBindingRequest.topologySpreadConstraints:type_name -> apis.aistudio.gateway.v1.TopologySpreadConstraints
	19, // 3: apis.aistudio.gateway.v1.CreateServiceBindingRequest.loadBalanceStrategy:type_name -> apis.aistudio.gateway.v1.LoadBalanceStrategy
	20, // 4: apis.aistudio.gateway.v1.CreateServiceBindingRequest.warmup:type_name -> apis.aistudio.gateway.v1.Warmup
	18, // 5: apis.aistudio.gateway.v1.UpdateServiceBindingRequest.topologySpreadConstraints:type_name -> apis.aistudio.gateway.v1.TopologySpreadConstraints
	19, // 6: apis.aistudio.gateway.v1.UpdateServiceBindingRequest.loadBalanceStrategy:type_name -> apis.aistudio.gateway.v1.LoadBalanceStrategy
	20, // 7: apis.aistudio.gateway.v1.UpdateServiceBindingRequest.warmup:type_name -> apis.aistudio.gateway.v1.Warmup
	10, // 8: apis.aistudio.gateway.v1.Gateway.GatewayStatus:type_name -> apis.aistudio.gateway.v1.GatewayStatus
	21, // 9: apis.aistudio.gateway.v1.GatewayStatus.conditions:type_name -> apis.common.Condition
	9,  // 10: apis.aistudio.gateway.v1.ListGatewayResponse.gateways:type_name -> apis.aistudio.gateway.v1.Gateway
	11, // 11: apis.aistudio.gateway.v1.GatewayService.CreateGateway:input_type -> apis.aistudio.gateway.v1.CreateOrUpdateGatewayRequest
	11, // 12: apis.aistudio.gateway.v1.GatewayService.UpdateGateway:input_type -> apis.aistudio.gateway.v1.CreateOrUpdateGatewayRequest
	12, // 13: apis.aistudio.gateway.v1.GatewayService.GetGateway:input_type -> apis.aistudio.gateway.v1.GetGatewayRequest
	13, // 14: apis.aistudio.gateway.v1.GatewayService.ListGateway:input_type -> apis.aistudio.gateway.v1.ListGatewayOptions
	15, // 15: apis.aistudio.gateway.v1.GatewayService.DeleteGateway:input_type -> apis.aistudio.gateway.v1.DeleteGatewayRequest
	2,  // 16: apis.aistudio.gateway.v1.GatewayService.CreateServiceBinding:input_type -> apis.aistudio.gateway.v1.CreateServiceBindingRequest
	6,  // 17: apis.aistudio.gateway.v1.GatewayService.DeleteServiceBinding:input_type -> apis.aistudio.gateway.v1.DeleteServiceBindingRequest
	8,  // 18: apis.aistudio.gateway.v1.GatewayService.ListServiceBinding:input_type -> apis.aistudio.gateway.v1.ListServiceBindingOptions
	9,  // 19: apis.aistudio.gateway.v1.GatewayService.CreateGateway:output_type -> apis.aistudio.gateway.v1.Gateway
	9,  // 20: apis.aistudio.gateway.v1.GatewayService.UpdateGateway:output_type -> apis.aistudio.gateway.v1.Gateway
	9,  // 21: apis.aistudio.gateway.v1.GatewayService.GetGateway:output_type -> apis.aistudio.gateway.v1.Gateway
	14, // 22: apis.aistudio.gateway.v1.GatewayService.ListGateway:output_type -> apis.aistudio.gateway.v1.ListGatewayResponse
	16, // 23: apis.aistudio.gateway.v1.GatewayService.DeleteGateway:output_type -> apis.aistudio.gateway.v1.DeleteGatewayResponse
	3,  // 24: apis.aistudio.gateway.v1.GatewayService.CreateServiceBinding:output_type -> apis.aistudio.gateway.v1.CreateServiceBindingResponse
	22, // 25: apis.aistudio.gateway.v1.GatewayService.DeleteServiceBinding:output_type -> google.protobuf.Empty
	0,  // 26: apis.aistudio.gateway.v1.GatewayService.ListServiceBinding:output_type -> apis.aistudio.gateway.v1.ListServiceBindingResponse
	19, // [19:27] is the sub-list for method output_type
	11, // [11:19] is the sub-list for method input_type
	11, // [11:11] is the sub-list for extension type_name
	11, // [11:11] is the sub-list for extension extendee
	0,  // [0:11] is the sub-list for field type_name
}

func init() { file_aistudio_gateway_v1_gateway_proto_init() }
func file_aistudio_gateway_v1_gateway_proto_init() {
	if File_aistudio_gateway_v1_gateway_proto != nil {
		return
	}
	file_aistudio_gateway_v1_destination_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_aistudio_gateway_v1_gateway_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*ListServiceBindingResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_gateway_v1_gateway_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*ServiceBinding); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_gateway_v1_gateway_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*CreateServiceBindingRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_gateway_v1_gateway_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*CreateServiceBindingResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_gateway_v1_gateway_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*UpdateServiceBindingResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_gateway_v1_gateway_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*UpdateServiceBindingRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_gateway_v1_gateway_proto_msgTypes[6].Exporter = func(v any, i int) any {
			switch v := v.(*DeleteServiceBindingRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_gateway_v1_gateway_proto_msgTypes[7].Exporter = func(v any, i int) any {
			switch v := v.(*GetServiceBindingRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_gateway_v1_gateway_proto_msgTypes[8].Exporter = func(v any, i int) any {
			switch v := v.(*ListServiceBindingOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_gateway_v1_gateway_proto_msgTypes[9].Exporter = func(v any, i int) any {
			switch v := v.(*Gateway); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_gateway_v1_gateway_proto_msgTypes[10].Exporter = func(v any, i int) any {
			switch v := v.(*GatewayStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_gateway_v1_gateway_proto_msgTypes[11].Exporter = func(v any, i int) any {
			switch v := v.(*CreateOrUpdateGatewayRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_gateway_v1_gateway_proto_msgTypes[12].Exporter = func(v any, i int) any {
			switch v := v.(*GetGatewayRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_gateway_v1_gateway_proto_msgTypes[13].Exporter = func(v any, i int) any {
			switch v := v.(*ListGatewayOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_gateway_v1_gateway_proto_msgTypes[14].Exporter = func(v any, i int) any {
			switch v := v.(*ListGatewayResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_gateway_v1_gateway_proto_msgTypes[15].Exporter = func(v any, i int) any {
			switch v := v.(*DeleteGatewayRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_gateway_v1_gateway_proto_msgTypes[16].Exporter = func(v any, i int) any {
			switch v := v.(*DeleteGatewayResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_aistudio_gateway_v1_gateway_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   17,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_aistudio_gateway_v1_gateway_proto_goTypes,
		DependencyIndexes: file_aistudio_gateway_v1_gateway_proto_depIdxs,
		MessageInfos:      file_aistudio_gateway_v1_gateway_proto_msgTypes,
	}.Build()
	File_aistudio_gateway_v1_gateway_proto = out.File
	file_aistudio_gateway_v1_gateway_proto_rawDesc = nil
	file_aistudio_gateway_v1_gateway_proto_goTypes = nil
	file_aistudio_gateway_v1_gateway_proto_depIdxs = nil
}
