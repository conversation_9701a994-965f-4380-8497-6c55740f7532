// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.3
// - protoc             v5.29.3
// source: aistudio/queue/v1/queue.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationQueueServiceCheckQueueAuth = "/apis.aistudio.queue.v1.QueueService/CheckQueueAuth"
const OperationQueueServiceCreateQueue = "/apis.aistudio.queue.v1.QueueService/CreateQueue"
const OperationQueueServiceDeleteQueue = "/apis.aistudio.queue.v1.QueueService/DeleteQueue"
const OperationQueueServiceGetClustersByQueue = "/apis.aistudio.queue.v1.QueueService/GetClustersByQueue"
const OperationQueueServiceGetQueueDetail = "/apis.aistudio.queue.v1.QueueService/GetQueueDetail"
const OperationQueueServiceGetQueueGpuMemorySummary = "/apis.aistudio.queue.v1.QueueService/GetQueueGpuMemorySummary"
const OperationQueueServiceGetQueueMetrics = "/apis.aistudio.queue.v1.QueueService/GetQueueMetrics"
const OperationQueueServiceListQueue = "/apis.aistudio.queue.v1.QueueService/ListQueue"
const OperationQueueServiceUpdateMembers = "/apis.aistudio.queue.v1.QueueService/UpdateMembers"
const OperationQueueServiceUpdateQueue = "/apis.aistudio.queue.v1.QueueService/UpdateQueue"

type QueueServiceHTTPServer interface {
	// CheckQueueAuth 校验队列权限
	CheckQueueAuth(context.Context, *CheckQueueAuthRequest) (*CheckQueueAuthResponse, error)
	// CreateQueue创建或者更新队列,创建队列的时候会默认在关联的集群中创建队列的资源队列，根据服务器的资源情况进行队列的配额分配
	CreateQueue(context.Context, *CreateOrUpdateQueueRequest) (*Queue, error)
	// DeleteQueue删除队列
	//删除队列时，需要等待队列中的任务全部执行完毕后才能触发
	DeleteQueue(context.Context, *DeleteQueueRequest) (*emptypb.Empty, error)
	GetClustersByQueue(context.Context, *GetClustersByQueueRequest) (*GetClustersByQueueResponse, error)
	GetQueueDetail(context.Context, *GetQueueDetailRequest) (*Queue, error)
	// GetQueueGpuMemorySummary 获取队列各规格共享显存最大剩余量
	GetQueueGpuMemorySummary(context.Context, *GetQueueGpuMemorySummaryRequest) (*GetQueueGpuMemorySummaryResponse, error)
	GetQueueMetrics(context.Context, *GetQueueMetricsRequest) (*QueueMetric, error)
	// ListQueue查询队列列表
	ListQueue(context.Context, *ListQueueOptions) (*ListQueueResult, error)
	// UpdateMembers UpdateMembers 更新成员
	UpdateMembers(context.Context, *UpdateMembersRequest) (*emptypb.Empty, error)
	UpdateQueue(context.Context, *CreateOrUpdateQueueRequest) (*Queue, error)
}

func RegisterQueueServiceHTTPServer(s *http.Server, srv QueueServiceHTTPServer) {
	r := s.Route("/")
	r.POST("/apis/v1/workspace/{workspaceName}/queue", _QueueService_CreateQueue0_HTTP_Handler(srv))
	r.PUT("/apis/v1/workspace/{workspaceName}/queue/{name}", _QueueService_UpdateQueue0_HTTP_Handler(srv))
	r.DELETE("/apis/v1/workspace/{workspaceName}/queue/{name}", _QueueService_DeleteQueue0_HTTP_Handler(srv))
	r.GET("/apis/v1/workspace/{workspaceName}/queues", _QueueService_ListQueue0_HTTP_Handler(srv))
	r.GET("/apis/v1/workspace/{workspaceName}/queue/{name}", _QueueService_GetQueueDetail0_HTTP_Handler(srv))
	r.GET("/apis/v1/workspace/{workspaceName}/queue/{name}/metrics", _QueueService_GetQueueMetrics0_HTTP_Handler(srv))
	r.PUT("/apis/v1/workspace/{workspaceName}/queue/{name}/update_members", _QueueService_UpdateMembers1_HTTP_Handler(srv))
	r.GET("/apis/v1/workspace/{workspaceName}/queue/{name}/checkauth", _QueueService_CheckQueueAuth0_HTTP_Handler(srv))
	r.GET("/apis/v1/workspace/{workspaceName}/queue/{queueName}/clusters", _QueueService_GetClustersByQueue0_HTTP_Handler(srv))
	r.GET("/apis/v1/workspace/{workspaceName}/queue/{queueName}/gpumem", _QueueService_GetQueueGpuMemorySummary0_HTTP_Handler(srv))
}

func _QueueService_CreateQueue0_HTTP_Handler(srv QueueServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateOrUpdateQueueRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationQueueServiceCreateQueue)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateQueue(ctx, req.(*CreateOrUpdateQueueRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Queue)
		return ctx.Result(200, reply)
	}
}

func _QueueService_UpdateQueue0_HTTP_Handler(srv QueueServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateOrUpdateQueueRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationQueueServiceUpdateQueue)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateQueue(ctx, req.(*CreateOrUpdateQueueRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Queue)
		return ctx.Result(200, reply)
	}
}

func _QueueService_DeleteQueue0_HTTP_Handler(srv QueueServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteQueueRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationQueueServiceDeleteQueue)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteQueue(ctx, req.(*DeleteQueueRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _QueueService_ListQueue0_HTTP_Handler(srv QueueServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListQueueOptions
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationQueueServiceListQueue)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListQueue(ctx, req.(*ListQueueOptions))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListQueueResult)
		return ctx.Result(200, reply)
	}
}

func _QueueService_GetQueueDetail0_HTTP_Handler(srv QueueServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetQueueDetailRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationQueueServiceGetQueueDetail)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetQueueDetail(ctx, req.(*GetQueueDetailRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Queue)
		return ctx.Result(200, reply)
	}
}

func _QueueService_GetQueueMetrics0_HTTP_Handler(srv QueueServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetQueueMetricsRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationQueueServiceGetQueueMetrics)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetQueueMetrics(ctx, req.(*GetQueueMetricsRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*QueueMetric)
		return ctx.Result(200, reply)
	}
}

func _QueueService_UpdateMembers1_HTTP_Handler(srv QueueServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UpdateMembersRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationQueueServiceUpdateMembers)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateMembers(ctx, req.(*UpdateMembersRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _QueueService_CheckQueueAuth0_HTTP_Handler(srv QueueServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CheckQueueAuthRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationQueueServiceCheckQueueAuth)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CheckQueueAuth(ctx, req.(*CheckQueueAuthRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CheckQueueAuthResponse)
		return ctx.Result(200, reply)
	}
}

func _QueueService_GetClustersByQueue0_HTTP_Handler(srv QueueServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetClustersByQueueRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationQueueServiceGetClustersByQueue)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetClustersByQueue(ctx, req.(*GetClustersByQueueRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetClustersByQueueResponse)
		return ctx.Result(200, reply)
	}
}

func _QueueService_GetQueueGpuMemorySummary0_HTTP_Handler(srv QueueServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetQueueGpuMemorySummaryRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationQueueServiceGetQueueGpuMemorySummary)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetQueueGpuMemorySummary(ctx, req.(*GetQueueGpuMemorySummaryRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetQueueGpuMemorySummaryResponse)
		return ctx.Result(200, reply)
	}
}

type QueueServiceHTTPClient interface {
	CheckQueueAuth(ctx context.Context, req *CheckQueueAuthRequest, opts ...http.CallOption) (rsp *CheckQueueAuthResponse, err error)
	CreateQueue(ctx context.Context, req *CreateOrUpdateQueueRequest, opts ...http.CallOption) (rsp *Queue, err error)
	DeleteQueue(ctx context.Context, req *DeleteQueueRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	GetClustersByQueue(ctx context.Context, req *GetClustersByQueueRequest, opts ...http.CallOption) (rsp *GetClustersByQueueResponse, err error)
	GetQueueDetail(ctx context.Context, req *GetQueueDetailRequest, opts ...http.CallOption) (rsp *Queue, err error)
	GetQueueGpuMemorySummary(ctx context.Context, req *GetQueueGpuMemorySummaryRequest, opts ...http.CallOption) (rsp *GetQueueGpuMemorySummaryResponse, err error)
	GetQueueMetrics(ctx context.Context, req *GetQueueMetricsRequest, opts ...http.CallOption) (rsp *QueueMetric, err error)
	ListQueue(ctx context.Context, req *ListQueueOptions, opts ...http.CallOption) (rsp *ListQueueResult, err error)
	UpdateMembers(ctx context.Context, req *UpdateMembersRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	UpdateQueue(ctx context.Context, req *CreateOrUpdateQueueRequest, opts ...http.CallOption) (rsp *Queue, err error)
}

type QueueServiceHTTPClientImpl struct {
	cc *http.Client
}

func NewQueueServiceHTTPClient(client *http.Client) QueueServiceHTTPClient {
	return &QueueServiceHTTPClientImpl{client}
}

func (c *QueueServiceHTTPClientImpl) CheckQueueAuth(ctx context.Context, in *CheckQueueAuthRequest, opts ...http.CallOption) (*CheckQueueAuthResponse, error) {
	var out CheckQueueAuthResponse
	pattern := "/apis/v1/workspace/{workspaceName}/queue/{name}/checkauth"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationQueueServiceCheckQueueAuth))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *QueueServiceHTTPClientImpl) CreateQueue(ctx context.Context, in *CreateOrUpdateQueueRequest, opts ...http.CallOption) (*Queue, error) {
	var out Queue
	pattern := "/apis/v1/workspace/{workspaceName}/queue"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationQueueServiceCreateQueue))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *QueueServiceHTTPClientImpl) DeleteQueue(ctx context.Context, in *DeleteQueueRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/apis/v1/workspace/{workspaceName}/queue/{name}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationQueueServiceDeleteQueue))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *QueueServiceHTTPClientImpl) GetClustersByQueue(ctx context.Context, in *GetClustersByQueueRequest, opts ...http.CallOption) (*GetClustersByQueueResponse, error) {
	var out GetClustersByQueueResponse
	pattern := "/apis/v1/workspace/{workspaceName}/queue/{queueName}/clusters"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationQueueServiceGetClustersByQueue))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *QueueServiceHTTPClientImpl) GetQueueDetail(ctx context.Context, in *GetQueueDetailRequest, opts ...http.CallOption) (*Queue, error) {
	var out Queue
	pattern := "/apis/v1/workspace/{workspaceName}/queue/{name}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationQueueServiceGetQueueDetail))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *QueueServiceHTTPClientImpl) GetQueueGpuMemorySummary(ctx context.Context, in *GetQueueGpuMemorySummaryRequest, opts ...http.CallOption) (*GetQueueGpuMemorySummaryResponse, error) {
	var out GetQueueGpuMemorySummaryResponse
	pattern := "/apis/v1/workspace/{workspaceName}/queue/{queueName}/gpumem"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationQueueServiceGetQueueGpuMemorySummary))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *QueueServiceHTTPClientImpl) GetQueueMetrics(ctx context.Context, in *GetQueueMetricsRequest, opts ...http.CallOption) (*QueueMetric, error) {
	var out QueueMetric
	pattern := "/apis/v1/workspace/{workspaceName}/queue/{name}/metrics"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationQueueServiceGetQueueMetrics))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *QueueServiceHTTPClientImpl) ListQueue(ctx context.Context, in *ListQueueOptions, opts ...http.CallOption) (*ListQueueResult, error) {
	var out ListQueueResult
	pattern := "/apis/v1/workspace/{workspaceName}/queues"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationQueueServiceListQueue))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *QueueServiceHTTPClientImpl) UpdateMembers(ctx context.Context, in *UpdateMembersRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/apis/v1/workspace/{workspaceName}/queue/{name}/update_members"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationQueueServiceUpdateMembers))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *QueueServiceHTTPClientImpl) UpdateQueue(ctx context.Context, in *CreateOrUpdateQueueRequest, opts ...http.CallOption) (*Queue, error) {
	var out Queue
	pattern := "/apis/v1/workspace/{workspaceName}/queue/{name}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationQueueServiceUpdateQueue))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
