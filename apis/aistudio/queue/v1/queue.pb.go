// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v5.29.3
// source: aistudio/queue/v1/queue.proto

package v1

import (
	common "git.lianjia.com/cloudnative/kic/kic-platform/apis/common"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type WorkloadType int32

const (
	WorkloadType_Training   WorkloadType = 0
	WorkloadType_Inference  WorkloadType = 1
	WorkloadType_DevMachine WorkloadType = 2
)

// Enum value maps for WorkloadType.
var (
	WorkloadType_name = map[int32]string{
		0: "Training",
		1: "Inference",
		2: "DevMachine",
	}
	WorkloadType_value = map[string]int32{
		"Training":   0,
		"Inference":  1,
		"DevMachine": 2,
	}
)

func (x WorkloadType) Enum() *WorkloadType {
	p := new(WorkloadType)
	*p = x
	return p
}

func (x WorkloadType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (WorkloadType) Descriptor() protoreflect.EnumDescriptor {
	return file_aistudio_queue_v1_queue_proto_enumTypes[0].Descriptor()
}

func (WorkloadType) Type() protoreflect.EnumType {
	return &file_aistudio_queue_v1_queue_proto_enumTypes[0]
}

func (x WorkloadType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use WorkloadType.Descriptor instead.
func (WorkloadType) EnumDescriptor() ([]byte, []int) {
	return file_aistudio_queue_v1_queue_proto_rawDescGZIP(), []int{0}
}

type QueueState int32

const (
	QueueState_QueueStateCreating QueueState = 0
	QueueState_QueueStateRunning  QueueState = 1
	QueueState_QueueStateClosed   QueueState = 2 //队列状态被关闭
	QueueState_QueueStateUnknown  QueueState = 3
)

// Enum value maps for QueueState.
var (
	QueueState_name = map[int32]string{
		0: "QueueStateCreating",
		1: "QueueStateRunning",
		2: "QueueStateClosed",
		3: "QueueStateUnknown",
	}
	QueueState_value = map[string]int32{
		"QueueStateCreating": 0,
		"QueueStateRunning":  1,
		"QueueStateClosed":   2,
		"QueueStateUnknown":  3,
	}
)

func (x QueueState) Enum() *QueueState {
	p := new(QueueState)
	*p = x
	return p
}

func (x QueueState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (QueueState) Descriptor() protoreflect.EnumDescriptor {
	return file_aistudio_queue_v1_queue_proto_enumTypes[1].Descriptor()
}

func (QueueState) Type() protoreflect.EnumType {
	return &file_aistudio_queue_v1_queue_proto_enumTypes[1]
}

func (x QueueState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use QueueState.Descriptor instead.
func (QueueState) EnumDescriptor() ([]byte, []int) {
	return file_aistudio_queue_v1_queue_proto_rawDescGZIP(), []int{1}
}

// 空间队列，队列的设计不再精细化到CPU、内存等，而在直接绑定到资源池的概念上,直接关联Node
type Queue struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id            string       `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`     //全局唯一id
	Name          string       `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"` //队列名称
	Region        string       `protobuf:"bytes,3,opt,name=region,proto3" json:"region,omitempty"`
	NodeIPs       []string     `protobuf:"bytes,4,rep,name=nodeIPs,proto3" json:"nodeIPs,omitempty"` //队列绑定的节点
	Description   string       `protobuf:"bytes,5,opt,name=description,proto3" json:"description,omitempty"`
	DisplayName   string       `protobuf:"bytes,6,opt,name=displayName,proto3" json:"displayName,omitempty"`
	Weight        int32        `protobuf:"varint,7,opt,name=weight,proto3" json:"weight,omitempty"`           //队列权重, 默认为1
	Reclaimable   bool         `protobuf:"varint,8,opt,name=reclaimable,proto3" json:"reclaimable,omitempty"` //是否可以回收资源
	WorkspaceName string       `protobuf:"bytes,9,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	WorkloadType  WorkloadType `protobuf:"varint,10,opt,name=workloadType,proto3,enum=apis.aistudio.queue.v1.WorkloadType" json:"workloadType,omitempty"` //工作负载类型
	QueueStatus   *QueueStatus `protobuf:"bytes,11,opt,name=queueStatus,proto3" json:"queueStatus,omitempty"`                                             //队列状态
	Owner         string       `protobuf:"bytes,13,opt,name=owner,proto3" json:"owner,omitempty"`                                                         //队列负责人
	CreateTime    string       `protobuf:"bytes,14,opt,name=createTime,proto3" json:"createTime,omitempty"`                                               //创建时间
	UpdateTime    string       `protobuf:"bytes,15,opt,name=updateTime,proto3" json:"updateTime,omitempty"`                                               //更新时间
	Members       []string     `protobuf:"bytes,16,rep,name=members,proto3" json:"members,omitempty"`                                                     //队列成员
	Managers      []string     `protobuf:"bytes,17,rep,name=managers,proto3" json:"managers,omitempty"`                                                   //队列管理员
}

func (x *Queue) Reset() {
	*x = Queue{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_queue_v1_queue_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Queue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Queue) ProtoMessage() {}

func (x *Queue) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_queue_v1_queue_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Queue.ProtoReflect.Descriptor instead.
func (*Queue) Descriptor() ([]byte, []int) {
	return file_aistudio_queue_v1_queue_proto_rawDescGZIP(), []int{0}
}

func (x *Queue) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Queue) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Queue) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *Queue) GetNodeIPs() []string {
	if x != nil {
		return x.NodeIPs
	}
	return nil
}

func (x *Queue) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Queue) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *Queue) GetWeight() int32 {
	if x != nil {
		return x.Weight
	}
	return 0
}

func (x *Queue) GetReclaimable() bool {
	if x != nil {
		return x.Reclaimable
	}
	return false
}

func (x *Queue) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *Queue) GetWorkloadType() WorkloadType {
	if x != nil {
		return x.WorkloadType
	}
	return WorkloadType_Training
}

func (x *Queue) GetQueueStatus() *QueueStatus {
	if x != nil {
		return x.QueueStatus
	}
	return nil
}

func (x *Queue) GetOwner() string {
	if x != nil {
		return x.Owner
	}
	return ""
}

func (x *Queue) GetCreateTime() string {
	if x != nil {
		return x.CreateTime
	}
	return ""
}

func (x *Queue) GetUpdateTime() string {
	if x != nil {
		return x.UpdateTime
	}
	return ""
}

func (x *Queue) GetMembers() []string {
	if x != nil {
		return x.Members
	}
	return nil
}

func (x *Queue) GetManagers() []string {
	if x != nil {
		return x.Managers
	}
	return nil
}

// 集群队列状态
type ClusterQueueStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ClusterName      string                            `protobuf:"bytes,1,opt,name=clusterName,proto3" json:"clusterName,omitempty"`
	Nodes            []string                          `protobuf:"bytes,2,rep,name=nodes,proto3" json:"nodes,omitempty"` //节点的IP
	State            string                            `protobuf:"bytes,3,opt,name=state,proto3" json:"state,omitempty"`
	Cpu              *common.ResourceMetric            `protobuf:"bytes,4,opt,name=cpu,proto3" json:"cpu,omitempty"`
	Memory           *common.ResourceMetric            `protobuf:"bytes,5,opt,name=memory,proto3" json:"memory,omitempty"`
	Gpu              *common.ResourceMetric            `protobuf:"bytes,6,opt,name=gpu,proto3" json:"gpu,omitempty"`
	NodeGroupMetrics []*common.NodeGroupResourceMetric `protobuf:"bytes,7,rep,name=nodeGroupMetrics,proto3" json:"nodeGroupMetrics,omitempty"`
	InQueue          int32                             `protobuf:"varint,8,opt,name=inQueue,proto3" json:"inQueue,omitempty"`      //队列中的任务数
	Running          int32                             `protobuf:"varint,9,opt,name=running,proto3" json:"running,omitempty"`      //队列中运行的任务数
	Pending          int32                             `protobuf:"varint,10,opt,name=pending,proto3" json:"pending,omitempty"`     //队列中等待的任务数
	Completed        int32                             `protobuf:"varint,11,opt,name=completed,proto3" json:"completed,omitempty"` //队列中完成的任务数
	Unknown          int32                             `protobuf:"varint,12,opt,name=unknown,proto3" json:"unknown,omitempty"`     //队列中未知的任务数
	GpuMem           *common.ResourceMetric            `protobuf:"bytes,13,opt,name=gpuMem,proto3" json:"gpuMem,omitempty"`
}

func (x *ClusterQueueStatus) Reset() {
	*x = ClusterQueueStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_queue_v1_queue_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClusterQueueStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClusterQueueStatus) ProtoMessage() {}

func (x *ClusterQueueStatus) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_queue_v1_queue_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClusterQueueStatus.ProtoReflect.Descriptor instead.
func (*ClusterQueueStatus) Descriptor() ([]byte, []int) {
	return file_aistudio_queue_v1_queue_proto_rawDescGZIP(), []int{1}
}

func (x *ClusterQueueStatus) GetClusterName() string {
	if x != nil {
		return x.ClusterName
	}
	return ""
}

func (x *ClusterQueueStatus) GetNodes() []string {
	if x != nil {
		return x.Nodes
	}
	return nil
}

func (x *ClusterQueueStatus) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

func (x *ClusterQueueStatus) GetCpu() *common.ResourceMetric {
	if x != nil {
		return x.Cpu
	}
	return nil
}

func (x *ClusterQueueStatus) GetMemory() *common.ResourceMetric {
	if x != nil {
		return x.Memory
	}
	return nil
}

func (x *ClusterQueueStatus) GetGpu() *common.ResourceMetric {
	if x != nil {
		return x.Gpu
	}
	return nil
}

func (x *ClusterQueueStatus) GetNodeGroupMetrics() []*common.NodeGroupResourceMetric {
	if x != nil {
		return x.NodeGroupMetrics
	}
	return nil
}

func (x *ClusterQueueStatus) GetInQueue() int32 {
	if x != nil {
		return x.InQueue
	}
	return 0
}

func (x *ClusterQueueStatus) GetRunning() int32 {
	if x != nil {
		return x.Running
	}
	return 0
}

func (x *ClusterQueueStatus) GetPending() int32 {
	if x != nil {
		return x.Pending
	}
	return 0
}

func (x *ClusterQueueStatus) GetCompleted() int32 {
	if x != nil {
		return x.Completed
	}
	return 0
}

func (x *ClusterQueueStatus) GetUnknown() int32 {
	if x != nil {
		return x.Unknown
	}
	return 0
}

func (x *ClusterQueueStatus) GetGpuMem() *common.ResourceMetric {
	if x != nil {
		return x.GpuMem
	}
	return nil
}

type QueueStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	State            QueueState                        `protobuf:"varint,1,opt,name=state,proto3,enum=apis.aistudio.queue.v1.QueueState" json:"state,omitempty"`
	Cpu              *common.ResourceMetric            `protobuf:"bytes,2,opt,name=cpu,proto3" json:"cpu,omitempty"`
	Memory           *common.ResourceMetric            `protobuf:"bytes,3,opt,name=memory,proto3" json:"memory,omitempty"`
	Gpu              *common.ResourceMetric            `protobuf:"bytes,4,opt,name=gpu,proto3" json:"gpu,omitempty"`
	NodeGroupMetrics []*common.NodeGroupResourceMetric `protobuf:"bytes,5,rep,name=nodeGroupMetrics,proto3" json:"nodeGroupMetrics,omitempty"`
	InQueue          int32                             `protobuf:"varint,6,opt,name=inQueue,proto3" json:"inQueue,omitempty"`             //队列中的任务数
	Running          int32                             `protobuf:"varint,7,opt,name=running,proto3" json:"running,omitempty"`             //队列中运行的任务数
	Pending          int32                             `protobuf:"varint,8,opt,name=pending,proto3" json:"pending,omitempty"`             //队列中等待的任务数
	Completed        int32                             `protobuf:"varint,9,opt,name=completed,proto3" json:"completed,omitempty"`         //队列中完成的任务数
	Unknown          int32                             `protobuf:"varint,10,opt,name=unknown,proto3" json:"unknown,omitempty"`            //队列中未知的任务数
	ClusterStatus    []*ClusterQueueStatus             `protobuf:"bytes,11,rep,name=clusterStatus,proto3" json:"clusterStatus,omitempty"` //集群的队列状态
	Clusters         []string                          `protobuf:"bytes,12,rep,name=clusters,proto3" json:"clusters,omitempty"`           //集群
	GpuMem           *common.ResourceMetric            `protobuf:"bytes,13,opt,name=gpuMem,proto3" json:"gpuMem,omitempty"`
}

func (x *QueueStatus) Reset() {
	*x = QueueStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_queue_v1_queue_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueueStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueueStatus) ProtoMessage() {}

func (x *QueueStatus) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_queue_v1_queue_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueueStatus.ProtoReflect.Descriptor instead.
func (*QueueStatus) Descriptor() ([]byte, []int) {
	return file_aistudio_queue_v1_queue_proto_rawDescGZIP(), []int{2}
}

func (x *QueueStatus) GetState() QueueState {
	if x != nil {
		return x.State
	}
	return QueueState_QueueStateCreating
}

func (x *QueueStatus) GetCpu() *common.ResourceMetric {
	if x != nil {
		return x.Cpu
	}
	return nil
}

func (x *QueueStatus) GetMemory() *common.ResourceMetric {
	if x != nil {
		return x.Memory
	}
	return nil
}

func (x *QueueStatus) GetGpu() *common.ResourceMetric {
	if x != nil {
		return x.Gpu
	}
	return nil
}

func (x *QueueStatus) GetNodeGroupMetrics() []*common.NodeGroupResourceMetric {
	if x != nil {
		return x.NodeGroupMetrics
	}
	return nil
}

func (x *QueueStatus) GetInQueue() int32 {
	if x != nil {
		return x.InQueue
	}
	return 0
}

func (x *QueueStatus) GetRunning() int32 {
	if x != nil {
		return x.Running
	}
	return 0
}

func (x *QueueStatus) GetPending() int32 {
	if x != nil {
		return x.Pending
	}
	return 0
}

func (x *QueueStatus) GetCompleted() int32 {
	if x != nil {
		return x.Completed
	}
	return 0
}

func (x *QueueStatus) GetUnknown() int32 {
	if x != nil {
		return x.Unknown
	}
	return 0
}

func (x *QueueStatus) GetClusterStatus() []*ClusterQueueStatus {
	if x != nil {
		return x.ClusterStatus
	}
	return nil
}

func (x *QueueStatus) GetClusters() []string {
	if x != nil {
		return x.Clusters
	}
	return nil
}

func (x *QueueStatus) GetGpuMem() *common.ResourceMetric {
	if x != nil {
		return x.GpuMem
	}
	return nil
}

type UpdateMembersRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName string   `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Name          string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Members       []string `protobuf:"bytes,3,rep,name=members,proto3" json:"members,omitempty"`
	Managers      []string `protobuf:"bytes,4,rep,name=managers,proto3" json:"managers,omitempty"`
}

func (x *UpdateMembersRequest) Reset() {
	*x = UpdateMembersRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_queue_v1_queue_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateMembersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateMembersRequest) ProtoMessage() {}

func (x *UpdateMembersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_queue_v1_queue_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateMembersRequest.ProtoReflect.Descriptor instead.
func (*UpdateMembersRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_queue_v1_queue_proto_rawDescGZIP(), []int{3}
}

func (x *UpdateMembersRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *UpdateMembersRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UpdateMembersRequest) GetMembers() []string {
	if x != nil {
		return x.Members
	}
	return nil
}

func (x *UpdateMembersRequest) GetManagers() []string {
	if x != nil {
		return x.Managers
	}
	return nil
}

type UpdateManagersRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName string   `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Name          string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Managers      []string `protobuf:"bytes,3,rep,name=managers,proto3" json:"managers,omitempty"`
}

func (x *UpdateManagersRequest) Reset() {
	*x = UpdateManagersRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_queue_v1_queue_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateManagersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateManagersRequest) ProtoMessage() {}

func (x *UpdateManagersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_queue_v1_queue_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateManagersRequest.ProtoReflect.Descriptor instead.
func (*UpdateManagersRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_queue_v1_queue_proto_rawDescGZIP(), []int{4}
}

func (x *UpdateManagersRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *UpdateManagersRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UpdateManagersRequest) GetManagers() []string {
	if x != nil {
		return x.Managers
	}
	return nil
}

type CreateOrUpdateQueueRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName string       `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Name          string       `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"` //TODO 规则校验
	NodeIPs       []string     `protobuf:"bytes,3,rep,name=nodeIPs,proto3" json:"nodeIPs,omitempty"`
	Weight        int32        `protobuf:"varint,4,opt,name=weight,proto3" json:"weight,omitempty"` //队列权重
	Members       []string     `protobuf:"bytes,5,rep,name=members,proto3" json:"members,omitempty"`
	DisplayName   string       `protobuf:"bytes,7,opt,name=displayName,proto3" json:"displayName,omitempty"`
	Description   string       `protobuf:"bytes,8,opt,name=description,proto3" json:"description,omitempty"`
	WorkloadType  WorkloadType `protobuf:"varint,9,opt,name=workloadType,proto3,enum=apis.aistudio.queue.v1.WorkloadType" json:"workloadType,omitempty"`
	Region        string       `protobuf:"bytes,10,opt,name=region,proto3" json:"region,omitempty"`
	Reclaimable   bool         `protobuf:"varint,11,opt,name=reclaimable,proto3" json:"reclaimable,omitempty"`
	Owner         string       `protobuf:"bytes,12,opt,name=owner,proto3" json:"owner,omitempty"`
	Managers      []string     `protobuf:"bytes,13,rep,name=managers,proto3" json:"managers,omitempty"`
}

func (x *CreateOrUpdateQueueRequest) Reset() {
	*x = CreateOrUpdateQueueRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_queue_v1_queue_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateOrUpdateQueueRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateOrUpdateQueueRequest) ProtoMessage() {}

func (x *CreateOrUpdateQueueRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_queue_v1_queue_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateOrUpdateQueueRequest.ProtoReflect.Descriptor instead.
func (*CreateOrUpdateQueueRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_queue_v1_queue_proto_rawDescGZIP(), []int{5}
}

func (x *CreateOrUpdateQueueRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *CreateOrUpdateQueueRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateOrUpdateQueueRequest) GetNodeIPs() []string {
	if x != nil {
		return x.NodeIPs
	}
	return nil
}

func (x *CreateOrUpdateQueueRequest) GetWeight() int32 {
	if x != nil {
		return x.Weight
	}
	return 0
}

func (x *CreateOrUpdateQueueRequest) GetMembers() []string {
	if x != nil {
		return x.Members
	}
	return nil
}

func (x *CreateOrUpdateQueueRequest) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *CreateOrUpdateQueueRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CreateOrUpdateQueueRequest) GetWorkloadType() WorkloadType {
	if x != nil {
		return x.WorkloadType
	}
	return WorkloadType_Training
}

func (x *CreateOrUpdateQueueRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *CreateOrUpdateQueueRequest) GetReclaimable() bool {
	if x != nil {
		return x.Reclaimable
	}
	return false
}

func (x *CreateOrUpdateQueueRequest) GetOwner() string {
	if x != nil {
		return x.Owner
	}
	return ""
}

func (x *CreateOrUpdateQueueRequest) GetManagers() []string {
	if x != nil {
		return x.Managers
	}
	return nil
}

type ListQueueOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName string `protobuf:"bytes,2,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Region        string `protobuf:"bytes,3,opt,name=region,proto3" json:"region,omitempty"`
	Name          string `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	DisplayName   string `protobuf:"bytes,5,opt,name=displayName,proto3" json:"displayName,omitempty"`
	EnableStatus  bool   `protobuf:"varint,6,opt,name=enableStatus,proto3" json:"enableStatus,omitempty"`
	Member        string `protobuf:"bytes,7,opt,name=member,proto3" json:"member,omitempty"`
	WorkloadType  string `protobuf:"bytes,8,opt,name=workloadType,proto3" json:"workloadType,omitempty"`
	Manager       string `protobuf:"bytes,9,opt,name=manager,proto3" json:"manager,omitempty"`
}

func (x *ListQueueOptions) Reset() {
	*x = ListQueueOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_queue_v1_queue_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListQueueOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListQueueOptions) ProtoMessage() {}

func (x *ListQueueOptions) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_queue_v1_queue_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListQueueOptions.ProtoReflect.Descriptor instead.
func (*ListQueueOptions) Descriptor() ([]byte, []int) {
	return file_aistudio_queue_v1_queue_proto_rawDescGZIP(), []int{6}
}

func (x *ListQueueOptions) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *ListQueueOptions) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *ListQueueOptions) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ListQueueOptions) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *ListQueueOptions) GetEnableStatus() bool {
	if x != nil {
		return x.EnableStatus
	}
	return false
}

func (x *ListQueueOptions) GetMember() string {
	if x != nil {
		return x.Member
	}
	return ""
}

func (x *ListQueueOptions) GetWorkloadType() string {
	if x != nil {
		return x.WorkloadType
	}
	return ""
}

func (x *ListQueueOptions) GetManager() string {
	if x != nil {
		return x.Manager
	}
	return ""
}

type ListQueueResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Queues []*Queue `protobuf:"bytes,1,rep,name=queues,proto3" json:"queues,omitempty"`
}

func (x *ListQueueResult) Reset() {
	*x = ListQueueResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_queue_v1_queue_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListQueueResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListQueueResult) ProtoMessage() {}

func (x *ListQueueResult) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_queue_v1_queue_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListQueueResult.ProtoReflect.Descriptor instead.
func (*ListQueueResult) Descriptor() ([]byte, []int) {
	return file_aistudio_queue_v1_queue_proto_rawDescGZIP(), []int{7}
}

func (x *ListQueueResult) GetQueues() []*Queue {
	if x != nil {
		return x.Queues
	}
	return nil
}

type UpdateQueueStateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	State         QueueState `protobuf:"varint,1,opt,name=state,proto3,enum=apis.aistudio.queue.v1.QueueState" json:"state,omitempty"`
	WorkspaceName string     `protobuf:"bytes,2,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Name          string     `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *UpdateQueueStateRequest) Reset() {
	*x = UpdateQueueStateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_queue_v1_queue_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateQueueStateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateQueueStateRequest) ProtoMessage() {}

func (x *UpdateQueueStateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_queue_v1_queue_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateQueueStateRequest.ProtoReflect.Descriptor instead.
func (*UpdateQueueStateRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_queue_v1_queue_proto_rawDescGZIP(), []int{8}
}

func (x *UpdateQueueStateRequest) GetState() QueueState {
	if x != nil {
		return x.State
	}
	return QueueState_QueueStateCreating
}

func (x *UpdateQueueStateRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *UpdateQueueStateRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type GetQueueDetailRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name          string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	WorkspaceName string `protobuf:"bytes,2,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	EnableStatus  bool   `protobuf:"varint,3,opt,name=enableStatus,proto3" json:"enableStatus,omitempty"` // 为了优化前端查询
}

func (x *GetQueueDetailRequest) Reset() {
	*x = GetQueueDetailRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_queue_v1_queue_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetQueueDetailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetQueueDetailRequest) ProtoMessage() {}

func (x *GetQueueDetailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_queue_v1_queue_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetQueueDetailRequest.ProtoReflect.Descriptor instead.
func (*GetQueueDetailRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_queue_v1_queue_proto_rawDescGZIP(), []int{9}
}

func (x *GetQueueDetailRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GetQueueDetailRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *GetQueueDetailRequest) GetEnableStatus() bool {
	if x != nil {
		return x.EnableStatus
	}
	return false
}

type DeleteQueueRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name          string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	WorkspaceName string `protobuf:"bytes,2,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
}

func (x *DeleteQueueRequest) Reset() {
	*x = DeleteQueueRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_queue_v1_queue_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteQueueRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteQueueRequest) ProtoMessage() {}

func (x *DeleteQueueRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_queue_v1_queue_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteQueueRequest.ProtoReflect.Descriptor instead.
func (*DeleteQueueRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_queue_v1_queue_proto_rawDescGZIP(), []int{10}
}

func (x *DeleteQueueRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DeleteQueueRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

type GetQueueMetricsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name          string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	WorkspaceName string `protobuf:"bytes,2,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
}

func (x *GetQueueMetricsRequest) Reset() {
	*x = GetQueueMetricsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_queue_v1_queue_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetQueueMetricsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetQueueMetricsRequest) ProtoMessage() {}

func (x *GetQueueMetricsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_queue_v1_queue_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetQueueMetricsRequest.ProtoReflect.Descriptor instead.
func (*GetQueueMetricsRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_queue_v1_queue_proto_rawDescGZIP(), []int{11}
}

func (x *GetQueueMetricsRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GetQueueMetricsRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

type QueueMetric struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Cpu        *common.ResourceMetric            `protobuf:"bytes,1,opt,name=cpu,proto3" json:"cpu,omitempty"`
	Memory     *common.ResourceMetric            `protobuf:"bytes,2,opt,name=memory,proto3" json:"memory,omitempty"`
	Gpu        *common.ResourceMetric            `protobuf:"bytes,3,opt,name=gpu,proto3" json:"gpu,omitempty"`
	NodeGroups []*common.NodeGroupResourceMetric `protobuf:"bytes,4,rep,name=nodeGroups,proto3" json:"nodeGroups,omitempty"`
}

func (x *QueueMetric) Reset() {
	*x = QueueMetric{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_queue_v1_queue_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueueMetric) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueueMetric) ProtoMessage() {}

func (x *QueueMetric) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_queue_v1_queue_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueueMetric.ProtoReflect.Descriptor instead.
func (*QueueMetric) Descriptor() ([]byte, []int) {
	return file_aistudio_queue_v1_queue_proto_rawDescGZIP(), []int{12}
}

func (x *QueueMetric) GetCpu() *common.ResourceMetric {
	if x != nil {
		return x.Cpu
	}
	return nil
}

func (x *QueueMetric) GetMemory() *common.ResourceMetric {
	if x != nil {
		return x.Memory
	}
	return nil
}

func (x *QueueMetric) GetGpu() *common.ResourceMetric {
	if x != nil {
		return x.Gpu
	}
	return nil
}

func (x *QueueMetric) GetNodeGroups() []*common.NodeGroupResourceMetric {
	if x != nil {
		return x.NodeGroups
	}
	return nil
}

type CheckQueueAuthRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName string `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Name          string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *CheckQueueAuthRequest) Reset() {
	*x = CheckQueueAuthRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_queue_v1_queue_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckQueueAuthRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckQueueAuthRequest) ProtoMessage() {}

func (x *CheckQueueAuthRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_queue_v1_queue_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckQueueAuthRequest.ProtoReflect.Descriptor instead.
func (*CheckQueueAuthRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_queue_v1_queue_proto_rawDescGZIP(), []int{13}
}

func (x *CheckQueueAuthRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *CheckQueueAuthRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type CheckQueueAuthResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status int32  `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	Errors string `protobuf:"bytes,2,opt,name=errors,proto3" json:"errors,omitempty"`
}

func (x *CheckQueueAuthResponse) Reset() {
	*x = CheckQueueAuthResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_queue_v1_queue_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckQueueAuthResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckQueueAuthResponse) ProtoMessage() {}

func (x *CheckQueueAuthResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_queue_v1_queue_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckQueueAuthResponse.ProtoReflect.Descriptor instead.
func (*CheckQueueAuthResponse) Descriptor() ([]byte, []int) {
	return file_aistudio_queue_v1_queue_proto_rawDescGZIP(), []int{14}
}

func (x *CheckQueueAuthResponse) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *CheckQueueAuthResponse) GetErrors() string {
	if x != nil {
		return x.Errors
	}
	return ""
}

type GetClustersByQueueRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName          string   `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	QueueName              string   `protobuf:"bytes,2,opt,name=queueName,proto3" json:"queueName,omitempty"`
	NodeSpecificationNames []string `protobuf:"bytes,3,rep,name=nodeSpecificationNames,proto3" json:"nodeSpecificationNames,omitempty"`
}

func (x *GetClustersByQueueRequest) Reset() {
	*x = GetClustersByQueueRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_queue_v1_queue_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetClustersByQueueRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetClustersByQueueRequest) ProtoMessage() {}

func (x *GetClustersByQueueRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_queue_v1_queue_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetClustersByQueueRequest.ProtoReflect.Descriptor instead.
func (*GetClustersByQueueRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_queue_v1_queue_proto_rawDescGZIP(), []int{15}
}

func (x *GetClustersByQueueRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *GetClustersByQueueRequest) GetQueueName() string {
	if x != nil {
		return x.QueueName
	}
	return ""
}

func (x *GetClustersByQueueRequest) GetNodeSpecificationNames() []string {
	if x != nil {
		return x.NodeSpecificationNames
	}
	return nil
}

type GetClustersByQueueResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Cluster []string `protobuf:"bytes,1,rep,name=cluster,proto3" json:"cluster,omitempty"`
}

func (x *GetClustersByQueueResponse) Reset() {
	*x = GetClustersByQueueResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_queue_v1_queue_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetClustersByQueueResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetClustersByQueueResponse) ProtoMessage() {}

func (x *GetClustersByQueueResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_queue_v1_queue_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetClustersByQueueResponse.ProtoReflect.Descriptor instead.
func (*GetClustersByQueueResponse) Descriptor() ([]byte, []int) {
	return file_aistudio_queue_v1_queue_proto_rawDescGZIP(), []int{16}
}

func (x *GetClustersByQueueResponse) GetCluster() []string {
	if x != nil {
		return x.Cluster
	}
	return nil
}

type GetQueueGpuMemorySummaryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName         string `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	QueueName             string `protobuf:"bytes,2,opt,name=queueName,proto3" json:"queueName,omitempty"`
	NodeSpecificationName string `protobuf:"bytes,3,opt,name=nodeSpecificationName,proto3" json:"nodeSpecificationName,omitempty"`
}

func (x *GetQueueGpuMemorySummaryRequest) Reset() {
	*x = GetQueueGpuMemorySummaryRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_queue_v1_queue_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetQueueGpuMemorySummaryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetQueueGpuMemorySummaryRequest) ProtoMessage() {}

func (x *GetQueueGpuMemorySummaryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_queue_v1_queue_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetQueueGpuMemorySummaryRequest.ProtoReflect.Descriptor instead.
func (*GetQueueGpuMemorySummaryRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_queue_v1_queue_proto_rawDescGZIP(), []int{17}
}

func (x *GetQueueGpuMemorySummaryRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *GetQueueGpuMemorySummaryRequest) GetQueueName() string {
	if x != nil {
		return x.QueueName
	}
	return ""
}

func (x *GetQueueGpuMemorySummaryRequest) GetNodeSpecificationName() string {
	if x != nil {
		return x.NodeSpecificationName
	}
	return ""
}

type GetQueueGpuMemorySummaryResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SpecGroups []*GpuSpecMemoryGroup `protobuf:"bytes,1,rep,name=specGroups,proto3" json:"specGroups,omitempty"`
}

func (x *GetQueueGpuMemorySummaryResponse) Reset() {
	*x = GetQueueGpuMemorySummaryResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_queue_v1_queue_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetQueueGpuMemorySummaryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetQueueGpuMemorySummaryResponse) ProtoMessage() {}

func (x *GetQueueGpuMemorySummaryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_queue_v1_queue_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetQueueGpuMemorySummaryResponse.ProtoReflect.Descriptor instead.
func (*GetQueueGpuMemorySummaryResponse) Descriptor() ([]byte, []int) {
	return file_aistudio_queue_v1_queue_proto_rawDescGZIP(), []int{18}
}

func (x *GetQueueGpuMemorySummaryResponse) GetSpecGroups() []*GpuSpecMemoryGroup {
	if x != nil {
		return x.SpecGroups
	}
	return nil
}

type GpuSpecMemoryGroup struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Spec    string                 `protobuf:"bytes,1,opt,name=spec,proto3" json:"spec,omitempty"`       // 规格
	Records []*NodeGpuMemoryRecord `protobuf:"bytes,2,rep,name=records,proto3" json:"records,omitempty"` // 节点显存记录
}

func (x *GpuSpecMemoryGroup) Reset() {
	*x = GpuSpecMemoryGroup{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_queue_v1_queue_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GpuSpecMemoryGroup) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GpuSpecMemoryGroup) ProtoMessage() {}

func (x *GpuSpecMemoryGroup) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_queue_v1_queue_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GpuSpecMemoryGroup.ProtoReflect.Descriptor instead.
func (*GpuSpecMemoryGroup) Descriptor() ([]byte, []int) {
	return file_aistudio_queue_v1_queue_proto_rawDescGZIP(), []int{19}
}

func (x *GpuSpecMemoryGroup) GetSpec() string {
	if x != nil {
		return x.Spec
	}
	return ""
}

func (x *GpuSpecMemoryGroup) GetRecords() []*NodeGpuMemoryRecord {
	if x != nil {
		return x.Records
	}
	return nil
}

type NodeGpuMemoryRecord struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NodeIP              string `protobuf:"bytes,1,opt,name=nodeIP,proto3" json:"nodeIP,omitempty"`
	MaxCardAvailableMiB int64  `protobuf:"varint,2,opt,name=maxCardAvailableMiB,proto3" json:"maxCardAvailableMiB,omitempty"` // 单卡最大可用显存
}

func (x *NodeGpuMemoryRecord) Reset() {
	*x = NodeGpuMemoryRecord{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_queue_v1_queue_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NodeGpuMemoryRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NodeGpuMemoryRecord) ProtoMessage() {}

func (x *NodeGpuMemoryRecord) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_queue_v1_queue_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NodeGpuMemoryRecord.ProtoReflect.Descriptor instead.
func (*NodeGpuMemoryRecord) Descriptor() ([]byte, []int) {
	return file_aistudio_queue_v1_queue_proto_rawDescGZIP(), []int{20}
}

func (x *NodeGpuMemoryRecord) GetNodeIP() string {
	if x != nil {
		return x.NodeIP
	}
	return ""
}

func (x *NodeGpuMemoryRecord) GetMaxCardAvailableMiB() int64 {
	if x != nil {
		return x.MaxCardAvailableMiB
	}
	return 0
}

var File_aistudio_queue_v1_queue_proto protoreflect.FileDescriptor

var file_aistudio_queue_v1_queue_proto_rawDesc = []byte{
	0x0a, 0x1d, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2f, 0x71, 0x75, 0x65, 0x75, 0x65,
	0x2f, 0x76, 0x31, 0x2f, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x16, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x71,
	0x75, 0x65, 0x75, 0x65, 0x2e, 0x76, 0x31, 0x1a, 0x13, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74,
	0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0xd9, 0x04, 0x0a, 0x05, 0x51, 0x75, 0x65, 0x75, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x3b, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x27, 0xfa, 0x42, 0x24, 0x72, 0x22, 0x10,
	0x01, 0x18, 0x1f, 0x32, 0x1c, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x5d, 0x28, 0x5b, 0x2d, 0x61, 0x2d,
	0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x2a, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x29, 0x3f,
	0x24, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f,
	0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12,
	0x18, 0x0a, 0x07, 0x6e, 0x6f, 0x64, 0x65, 0x49, 0x50, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x07, 0x6e, 0x6f, 0x64, 0x65, 0x49, 0x50, 0x73, 0x12, 0x29, 0x0a, 0x0b, 0x64, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x64, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x29, 0x0a, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e,
	0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02,
	0x18, 0x1f, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x16, 0x0a, 0x06, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x06, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x72, 0x65, 0x63, 0x6c, 0x61,
	0x69, 0x6d, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x72, 0x65,
	0x63, 0x6c, 0x61, 0x69, 0x6d, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72,
	0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x48, 0x0a, 0x0c, 0x77, 0x6f, 0x72, 0x6b, 0x6c, 0x6f, 0x61, 0x64, 0x54, 0x79, 0x70, 0x65, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73,
	0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x57,
	0x6f, 0x72, 0x6b, 0x6c, 0x6f, 0x61, 0x64, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x77, 0x6f, 0x72,
	0x6b, 0x6c, 0x6f, 0x61, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x45, 0x0a, 0x0b, 0x71, 0x75, 0x65,
	0x75, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23,
	0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x71,
	0x75, 0x65, 0x75, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75, 0x65, 0x75, 0x65, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x0b, 0x71, 0x75, 0x65, 0x75, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x14, 0x0a, 0x05, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x54, 0x69, 0x6d, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x54, 0x69, 0x6d, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72,
	0x73, 0x18, 0x10, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73,
	0x12, 0x1a, 0x0a, 0x08, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x73, 0x18, 0x11, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x08, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x73, 0x22, 0x82, 0x04, 0x0a,
	0x12, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x51, 0x75, 0x65, 0x75, 0x65, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65,
	0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6e, 0x6f, 0x64, 0x65, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x6e, 0x6f, 0x64, 0x65, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x73,
	0x74, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74,
	0x65, 0x12, 0x2d, 0x0a, 0x03, 0x63, 0x70, 0x75, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b,
	0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x52, 0x03, 0x63, 0x70, 0x75,
	0x12, 0x33, 0x0a, 0x06, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x52, 0x06, 0x6d,
	0x65, 0x6d, 0x6f, 0x72, 0x79, 0x12, 0x2d, 0x0a, 0x03, 0x67, 0x70, 0x75, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x52,
	0x03, 0x67, 0x70, 0x75, 0x12, 0x50, 0x0a, 0x10, 0x6e, 0x6f, 0x64, 0x65, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24,
	0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4e, 0x6f, 0x64,
	0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65,
	0x74, 0x72, 0x69, 0x63, 0x52, 0x10, 0x6e, 0x6f, 0x64, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4d,
	0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x69, 0x6e, 0x51, 0x75, 0x65, 0x75,
	0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x69, 0x6e, 0x51, 0x75, 0x65, 0x75, 0x65,
	0x12, 0x18, 0x0a, 0x07, 0x72, 0x75, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x07, 0x72, 0x75, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x65,
	0x6e, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x70, 0x65, 0x6e,
	0x64, 0x69, 0x6e, 0x67, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65,
	0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74,
	0x65, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x75, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x07, 0x75, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x12, 0x33, 0x0a, 0x06,
	0x67, 0x70, 0x75, 0x4d, 0x65, 0x6d, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61,
	0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x52, 0x06, 0x67, 0x70, 0x75, 0x4d, 0x65,
	0x6d, 0x22, 0xd5, 0x04, 0x0a, 0x0b, 0x51, 0x75, 0x65, 0x75, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x38, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f,
	0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75, 0x65, 0x75, 0x65, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x2d, 0x0a, 0x03, 0x63,
	0x70, 0x75, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d,
	0x65, 0x74, 0x72, 0x69, 0x63, 0x52, 0x03, 0x63, 0x70, 0x75, 0x12, 0x33, 0x0a, 0x06, 0x6d, 0x65,
	0x6d, 0x6f, 0x72, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69,
	0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x52, 0x06, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x12,
	0x2d, 0x0a, 0x03, 0x67, 0x70, 0x75, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61,
	0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x52, 0x03, 0x67, 0x70, 0x75, 0x12, 0x50,
	0x0a, 0x10, 0x6e, 0x6f, 0x64, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4d, 0x65, 0x74, 0x72, 0x69,
	0x63, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x52, 0x10,
	0x6e, 0x6f, 0x64, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73,
	0x12, 0x18, 0x0a, 0x07, 0x69, 0x6e, 0x51, 0x75, 0x65, 0x75, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x07, 0x69, 0x6e, 0x51, 0x75, 0x65, 0x75, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x75,
	0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x72, 0x75, 0x6e,
	0x6e, 0x69, 0x6e, 0x67, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x70, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x1c,
	0x0a, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x12, 0x18, 0x0a, 0x07,
	0x75, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x75,
	0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x12, 0x50, 0x0a, 0x0d, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65,
	0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e,
	0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x71, 0x75,
	0x65, 0x75, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x51, 0x75,
	0x65, 0x75, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0d, 0x63, 0x6c, 0x75, 0x73, 0x74,
	0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x6c, 0x75, 0x73,
	0x74, 0x65, 0x72, 0x73, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6c, 0x75, 0x73,
	0x74, 0x65, 0x72, 0x73, 0x12, 0x33, 0x0a, 0x06, 0x67, 0x70, 0x75, 0x4d, 0x65, 0x6d, 0x18, 0x0d,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x72, 0x69,
	0x63, 0x52, 0x06, 0x67, 0x70, 0x75, 0x4d, 0x65, 0x6d, 0x22, 0x86, 0x01, 0x0a, 0x14, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07,
	0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x6d,
	0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x72, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x72, 0x73, 0x22, 0x6d, 0x0a, 0x15, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x61, 0x6e, 0x61,
	0x67, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x77,
	0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72,
	0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72,
	0x73, 0x22, 0xe4, 0x03, 0x0a, 0x1a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x51, 0x75, 0x65, 0x75, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x3b, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x27, 0xfa, 0x42, 0x24, 0x72, 0x22, 0x10, 0x01, 0x18, 0x1f, 0x32,
	0x1c, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x5d, 0x28, 0x5b, 0x2d, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39,
	0x5d, 0x2a, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x29, 0x3f, 0x24, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x25, 0x0a, 0x07, 0x6e, 0x6f, 0x64, 0x65, 0x49, 0x50, 0x73, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x09, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x92, 0x01, 0x05, 0x08, 0x00, 0x10, 0xe8,
	0x07, 0x52, 0x07, 0x6e, 0x6f, 0x64, 0x65, 0x49, 0x50, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x77, 0x65,
	0x69, 0x67, 0x68, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x77, 0x65, 0x69, 0x67,
	0x68, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x18, 0x05, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x12, 0x29, 0x0a, 0x0b,
	0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x1f, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x70,
	0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x29, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x72, 0x02, 0x18, 0x64, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x48, 0x0a, 0x0c, 0x77, 0x6f, 0x72, 0x6b, 0x6c, 0x6f, 0x61, 0x64, 0x54, 0x79,
	0x70, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e,
	0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x6c, 0x6f, 0x61, 0x64, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c,
	0x77, 0x6f, 0x72, 0x6b, 0x6c, 0x6f, 0x61, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06,
	0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65,
	0x67, 0x69, 0x6f, 0x6e, 0x12, 0x20, 0x0a, 0x0b, 0x72, 0x65, 0x63, 0x6c, 0x61, 0x69, 0x6d, 0x61,
	0x62, 0x6c, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x72, 0x65, 0x63, 0x6c, 0x61,
	0x69, 0x6d, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x12, 0x1a, 0x0a, 0x08,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x73, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x73, 0x22, 0x80, 0x02, 0x0a, 0x10, 0x4c, 0x69, 0x73,
	0x74, 0x51, 0x75, 0x65, 0x75, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x24, 0x0a,
	0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x20, 0x0a, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x22, 0x0a, 0x0c, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x22, 0x0a,
	0x0c, 0x77, 0x6f, 0x72, 0x6b, 0x6c, 0x6f, 0x61, 0x64, 0x54, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x77, 0x6f, 0x72, 0x6b, 0x6c, 0x6f, 0x61, 0x64, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x22, 0x48, 0x0a, 0x0f, 0x4c,
	0x69, 0x73, 0x74, 0x51, 0x75, 0x65, 0x75, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x35,
	0x0a, 0x06, 0x71, 0x75, 0x65, 0x75, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d,
	0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x71,
	0x75, 0x65, 0x75, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75, 0x65, 0x75, 0x65, 0x52, 0x06, 0x71,
	0x75, 0x65, 0x75, 0x65, 0x73, 0x22, 0x8d, 0x01, 0x0a, 0x17, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x51, 0x75, 0x65, 0x75, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x38, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f,
	0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75, 0x65, 0x75, 0x65, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x77,
	0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x75, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x51, 0x75, 0x65, 0x75,
	0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x65, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c,
	0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x4e, 0x0a, 0x12,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x51, 0x75, 0x65, 0x75, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77,
	0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x52, 0x0a, 0x16,
	0x47, 0x65, 0x74, 0x51, 0x75, 0x65, 0x75, 0x65, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f,
	0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x22, 0xe6, 0x01, 0x0a, 0x0b, 0x51, 0x75, 0x65, 0x75, 0x65, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63,
	0x12, 0x2d, 0x0a, 0x03, 0x63, 0x70, 0x75, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e,
	0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x52, 0x03, 0x63, 0x70, 0x75, 0x12,
	0x33, 0x0a, 0x06, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1b, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x52, 0x06, 0x6d, 0x65,
	0x6d, 0x6f, 0x72, 0x79, 0x12, 0x2d, 0x0a, 0x03, 0x67, 0x70, 0x75, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x52, 0x03,
	0x67, 0x70, 0x75, 0x12, 0x44, 0x0a, 0x0a, 0x6e, 0x6f, 0x64, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x52, 0x0a, 0x6e,
	0x6f, 0x64, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x22, 0x51, 0x0a, 0x15, 0x43, 0x68, 0x65,
	0x63, 0x6b, 0x51, 0x75, 0x65, 0x75, 0x65, 0x41, 0x75, 0x74, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x48, 0x0a, 0x16,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x51, 0x75, 0x65, 0x75, 0x65, 0x41, 0x75, 0x74, 0x68, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x16,
	0x0a, 0x06, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x22, 0x97, 0x01, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x43, 0x6c,
	0x75, 0x73, 0x74, 0x65, 0x72, 0x73, 0x42, 0x79, 0x51, 0x75, 0x65, 0x75, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72,
	0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x71, 0x75,
	0x65, 0x75, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x71,
	0x75, 0x65, 0x75, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x36, 0x0a, 0x16, 0x6e, 0x6f, 0x64, 0x65,
	0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d,
	0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x16, 0x6e, 0x6f, 0x64, 0x65, 0x53, 0x70,
	0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x73,
	0x22, 0x36, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x73, 0x42,
	0x79, 0x51, 0x75, 0x65, 0x75, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18,
	0x0a, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x22, 0x9b, 0x01, 0x0a, 0x1f, 0x47, 0x65, 0x74,
	0x51, 0x75, 0x65, 0x75, 0x65, 0x47, 0x70, 0x75, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x53, 0x75,
	0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x0d,
	0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x71, 0x75, 0x65, 0x75, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x71, 0x75, 0x65, 0x75, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x34, 0x0a, 0x15, 0x6e, 0x6f, 0x64, 0x65, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x15, 0x6e, 0x6f, 0x64, 0x65, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x6e, 0x0a, 0x20, 0x47, 0x65, 0x74, 0x51, 0x75, 0x65,
	0x75, 0x65, 0x47, 0x70, 0x75, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x53, 0x75, 0x6d, 0x6d, 0x61,
	0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4a, 0x0a, 0x0a, 0x73, 0x70,
	0x65, 0x63, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a,
	0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x71,
	0x75, 0x65, 0x75, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x70, 0x75, 0x53, 0x70, 0x65, 0x63, 0x4d,
	0x65, 0x6d, 0x6f, 0x72, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x0a, 0x73, 0x70, 0x65, 0x63,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x22, 0x6f, 0x0a, 0x12, 0x47, 0x70, 0x75, 0x53, 0x70, 0x65,
	0x63, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x12, 0x0a, 0x04,
	0x73, 0x70, 0x65, 0x63, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x73, 0x70, 0x65, 0x63,
	0x12, 0x45, 0x0a, 0x07, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x2b, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69,
	0x6f, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x47,
	0x70, 0x75, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x07,
	0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x22, 0x5f, 0x0a, 0x13, 0x4e, 0x6f, 0x64, 0x65, 0x47,
	0x70, 0x75, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x16,
	0x0a, 0x06, 0x6e, 0x6f, 0x64, 0x65, 0x49, 0x50, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x6e, 0x6f, 0x64, 0x65, 0x49, 0x50, 0x12, 0x30, 0x0a, 0x13, 0x6d, 0x61, 0x78, 0x43, 0x61, 0x72,
	0x64, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x4d, 0x69, 0x42, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x13, 0x6d, 0x61, 0x78, 0x43, 0x61, 0x72, 0x64, 0x41, 0x76, 0x61, 0x69,
	0x6c, 0x61, 0x62, 0x6c, 0x65, 0x4d, 0x69, 0x42, 0x2a, 0x3b, 0x0a, 0x0c, 0x57, 0x6f, 0x72, 0x6b,
	0x6c, 0x6f, 0x61, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0c, 0x0a, 0x08, 0x54, 0x72, 0x61, 0x69,
	0x6e, 0x69, 0x6e, 0x67, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x49, 0x6e, 0x66, 0x65, 0x72, 0x65,
	0x6e, 0x63, 0x65, 0x10, 0x01, 0x12, 0x0e, 0x0a, 0x0a, 0x44, 0x65, 0x76, 0x4d, 0x61, 0x63, 0x68,
	0x69, 0x6e, 0x65, 0x10, 0x02, 0x2a, 0x68, 0x0a, 0x0a, 0x51, 0x75, 0x65, 0x75, 0x65, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x12, 0x16, 0x0a, 0x12, 0x51, 0x75, 0x65, 0x75, 0x65, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x10, 0x00, 0x12, 0x15, 0x0a, 0x11, 0x51,
	0x75, 0x65, 0x75, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x75, 0x6e, 0x6e, 0x69, 0x6e, 0x67,
	0x10, 0x01, 0x12, 0x14, 0x0a, 0x10, 0x51, 0x75, 0x65, 0x75, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x43, 0x6c, 0x6f, 0x73, 0x65, 0x64, 0x10, 0x02, 0x12, 0x15, 0x0a, 0x11, 0x51, 0x75, 0x65, 0x75,
	0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x03, 0x32,
	0x9c, 0x0d, 0x0a, 0x0c, 0x51, 0x75, 0x65, 0x75, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x12, 0x95, 0x01, 0x0a, 0x0b, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x51, 0x75, 0x65, 0x75, 0x65,
	0x12, 0x32, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f,
	0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x4f, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x51, 0x75, 0x65, 0x75, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74,
	0x75, 0x64, 0x69, 0x6f, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75,
	0x65, 0x75, 0x65, 0x22, 0x33, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2d, 0x3a, 0x01, 0x2a, 0x22, 0x28,
	0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x2f, 0x7b, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x7d, 0x2f, 0x71, 0x75, 0x65, 0x75, 0x65, 0x12, 0x9c, 0x01, 0x0a, 0x0b, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x51, 0x75, 0x65, 0x75, 0x65, 0x12, 0x32, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e,
	0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x51, 0x75, 0x65, 0x75, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1d, 0x2e, 0x61,
	0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x71, 0x75, 0x65,
	0x75, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75, 0x65, 0x75, 0x65, 0x22, 0x3a, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x34, 0x3a, 0x01, 0x2a, 0x1a, 0x2f, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31,
	0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2f, 0x7b, 0x77, 0x6f, 0x72, 0x6b,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x71, 0x75, 0x65, 0x75, 0x65,
	0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x12, 0x8a, 0x01, 0x0a, 0x0b, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x51, 0x75, 0x65, 0x75, 0x65, 0x12, 0x2a, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61,
	0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x51, 0x75, 0x65, 0x75, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x37, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x31, 0x2a, 0x2f, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x6f,
	0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2f, 0x7b, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2f, 0x7b, 0x6e,
	0x61, 0x6d, 0x65, 0x7d, 0x12, 0x91, 0x01, 0x0a, 0x09, 0x4c, 0x69, 0x73, 0x74, 0x51, 0x75, 0x65,
	0x75, 0x65, 0x12, 0x28, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64,
	0x69, 0x6f, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74,
	0x51, 0x75, 0x65, 0x75, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x1a, 0x27, 0x2e, 0x61,
	0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x71, 0x75, 0x65,
	0x75, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x51, 0x75, 0x65, 0x75, 0x65, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x31, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2b, 0x12, 0x29, 0x2f,
	0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x2f, 0x7b, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x7d, 0x2f, 0x71, 0x75, 0x65, 0x75, 0x65, 0x73, 0x12, 0x97, 0x01, 0x0a, 0x0e, 0x47, 0x65, 0x74,
	0x51, 0x75, 0x65, 0x75, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x2d, 0x2e, 0x61, 0x70,
	0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x71, 0x75, 0x65, 0x75,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x51, 0x75, 0x65, 0x75, 0x65, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1d, 0x2e, 0x61, 0x70, 0x69,
	0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75, 0x65, 0x75, 0x65, 0x22, 0x37, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x31, 0x12, 0x2f, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x6f, 0x72, 0x6b,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x2f, 0x7b, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2f, 0x7b, 0x6e, 0x61, 0x6d,
	0x65, 0x7d, 0x12, 0xa7, 0x01, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x51, 0x75, 0x65, 0x75, 0x65, 0x4d,
	0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x12, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69,
	0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x51, 0x75, 0x65, 0x75, 0x65, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69,
	0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x76, 0x31, 0x2e,
	0x51, 0x75, 0x65, 0x75, 0x65, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x22, 0x3f, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x39, 0x12, 0x37, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x6f,
	0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2f, 0x7b, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2f, 0x7b, 0x6e,
	0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x12, 0xa0, 0x01, 0x0a,
	0x0d, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x12, 0x2c,
	0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x71,
	0x75, 0x65, 0x75, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x65,
	0x6d, 0x62, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x22, 0x49, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x43, 0x3a, 0x01, 0x2a, 0x1a,
	0x3e, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x2f, 0x7b, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x7d, 0x2f, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x7d,
	0x2f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x12,
	0xb2, 0x01, 0x0a, 0x0e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x51, 0x75, 0x65, 0x75, 0x65, 0x41, 0x75,
	0x74, 0x68, 0x12, 0x2d, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64,
	0x69, 0x6f, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x51, 0x75, 0x65, 0x75, 0x65, 0x41, 0x75, 0x74, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69,
	0x6f, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b,
	0x51, 0x75, 0x65, 0x75, 0x65, 0x41, 0x75, 0x74, 0x68, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x41, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x3b, 0x12, 0x39, 0x2f, 0x61, 0x70, 0x69, 0x73,
	0x2f, 0x76, 0x31, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2f, 0x7b, 0x77,
	0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x71, 0x75,
	0x65, 0x75, 0x65, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x63, 0x68, 0x65, 0x63, 0x6b,
	0x61, 0x75, 0x74, 0x68, 0x12, 0xc2, 0x01, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x43, 0x6c, 0x75, 0x73,
	0x74, 0x65, 0x72, 0x73, 0x42, 0x79, 0x51, 0x75, 0x65, 0x75, 0x65, 0x12, 0x31, 0x2e, 0x61, 0x70,
	0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x71, 0x75, 0x65, 0x75,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x73,
	0x42, 0x79, 0x51, 0x75, 0x65, 0x75, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x32,
	0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x71,
	0x75, 0x65, 0x75, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x6c, 0x75, 0x73, 0x74,
	0x65, 0x72, 0x73, 0x42, 0x79, 0x51, 0x75, 0x65, 0x75, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x45, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x3f, 0x12, 0x3d, 0x2f, 0x61, 0x70, 0x69,
	0x73, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2f, 0x7b,
	0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x71,
	0x75, 0x65, 0x75, 0x65, 0x2f, 0x7b, 0x71, 0x75, 0x65, 0x75, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d,
	0x2f, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x73, 0x12, 0xd2, 0x01, 0x0a, 0x18, 0x47, 0x65,
	0x74, 0x51, 0x75, 0x65, 0x75, 0x65, 0x47, 0x70, 0x75, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x53,
	0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x12, 0x37, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69,
	0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x51, 0x75, 0x65, 0x75, 0x65, 0x47, 0x70, 0x75, 0x4d, 0x65, 0x6d, 0x6f, 0x72,
	0x79, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x38, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e,
	0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x51, 0x75, 0x65, 0x75,
	0x65, 0x47, 0x70, 0x75, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72,
	0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x43, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x3d, 0x12, 0x3b, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x6f, 0x72, 0x6b,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x2f, 0x7b, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2f, 0x7b, 0x71, 0x75, 0x65,
	0x75, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x67, 0x70, 0x75, 0x6d, 0x65, 0x6d, 0x42, 0x48,
	0x5a, 0x46, 0x67, 0x69, 0x74, 0x2e, 0x6c, 0x69, 0x61, 0x6e, 0x6a, 0x69, 0x61, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x2f, 0x6b, 0x69,
	0x63, 0x2f, 0x6b, 0x69, 0x63, 0x2d, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x61,
	0x70, 0x69, 0x73, 0x2f, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2f, 0x71, 0x75, 0x65,
	0x75, 0x65, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_aistudio_queue_v1_queue_proto_rawDescOnce sync.Once
	file_aistudio_queue_v1_queue_proto_rawDescData = file_aistudio_queue_v1_queue_proto_rawDesc
)

func file_aistudio_queue_v1_queue_proto_rawDescGZIP() []byte {
	file_aistudio_queue_v1_queue_proto_rawDescOnce.Do(func() {
		file_aistudio_queue_v1_queue_proto_rawDescData = protoimpl.X.CompressGZIP(file_aistudio_queue_v1_queue_proto_rawDescData)
	})
	return file_aistudio_queue_v1_queue_proto_rawDescData
}

var file_aistudio_queue_v1_queue_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_aistudio_queue_v1_queue_proto_msgTypes = make([]protoimpl.MessageInfo, 21)
var file_aistudio_queue_v1_queue_proto_goTypes = []any{
	(WorkloadType)(0),                        // 0: apis.aistudio.queue.v1.WorkloadType
	(QueueState)(0),                          // 1: apis.aistudio.queue.v1.QueueState
	(*Queue)(nil),                            // 2: apis.aistudio.queue.v1.Queue
	(*ClusterQueueStatus)(nil),               // 3: apis.aistudio.queue.v1.ClusterQueueStatus
	(*QueueStatus)(nil),                      // 4: apis.aistudio.queue.v1.QueueStatus
	(*UpdateMembersRequest)(nil),             // 5: apis.aistudio.queue.v1.UpdateMembersRequest
	(*UpdateManagersRequest)(nil),            // 6: apis.aistudio.queue.v1.UpdateManagersRequest
	(*CreateOrUpdateQueueRequest)(nil),       // 7: apis.aistudio.queue.v1.CreateOrUpdateQueueRequest
	(*ListQueueOptions)(nil),                 // 8: apis.aistudio.queue.v1.ListQueueOptions
	(*ListQueueResult)(nil),                  // 9: apis.aistudio.queue.v1.ListQueueResult
	(*UpdateQueueStateRequest)(nil),          // 10: apis.aistudio.queue.v1.UpdateQueueStateRequest
	(*GetQueueDetailRequest)(nil),            // 11: apis.aistudio.queue.v1.GetQueueDetailRequest
	(*DeleteQueueRequest)(nil),               // 12: apis.aistudio.queue.v1.DeleteQueueRequest
	(*GetQueueMetricsRequest)(nil),           // 13: apis.aistudio.queue.v1.GetQueueMetricsRequest
	(*QueueMetric)(nil),                      // 14: apis.aistudio.queue.v1.QueueMetric
	(*CheckQueueAuthRequest)(nil),            // 15: apis.aistudio.queue.v1.CheckQueueAuthRequest
	(*CheckQueueAuthResponse)(nil),           // 16: apis.aistudio.queue.v1.CheckQueueAuthResponse
	(*GetClustersByQueueRequest)(nil),        // 17: apis.aistudio.queue.v1.GetClustersByQueueRequest
	(*GetClustersByQueueResponse)(nil),       // 18: apis.aistudio.queue.v1.GetClustersByQueueResponse
	(*GetQueueGpuMemorySummaryRequest)(nil),  // 19: apis.aistudio.queue.v1.GetQueueGpuMemorySummaryRequest
	(*GetQueueGpuMemorySummaryResponse)(nil), // 20: apis.aistudio.queue.v1.GetQueueGpuMemorySummaryResponse
	(*GpuSpecMemoryGroup)(nil),               // 21: apis.aistudio.queue.v1.GpuSpecMemoryGroup
	(*NodeGpuMemoryRecord)(nil),              // 22: apis.aistudio.queue.v1.NodeGpuMemoryRecord
	(*common.ResourceMetric)(nil),            // 23: apis.common.ResourceMetric
	(*common.NodeGroupResourceMetric)(nil),   // 24: apis.common.NodeGroupResourceMetric
	(*emptypb.Empty)(nil),                    // 25: google.protobuf.Empty
}
var file_aistudio_queue_v1_queue_proto_depIdxs = []int32{
	0,  // 0: apis.aistudio.queue.v1.Queue.workloadType:type_name -> apis.aistudio.queue.v1.WorkloadType
	4,  // 1: apis.aistudio.queue.v1.Queue.queueStatus:type_name -> apis.aistudio.queue.v1.QueueStatus
	23, // 2: apis.aistudio.queue.v1.ClusterQueueStatus.cpu:type_name -> apis.common.ResourceMetric
	23, // 3: apis.aistudio.queue.v1.ClusterQueueStatus.memory:type_name -> apis.common.ResourceMetric
	23, // 4: apis.aistudio.queue.v1.ClusterQueueStatus.gpu:type_name -> apis.common.ResourceMetric
	24, // 5: apis.aistudio.queue.v1.ClusterQueueStatus.nodeGroupMetrics:type_name -> apis.common.NodeGroupResourceMetric
	23, // 6: apis.aistudio.queue.v1.ClusterQueueStatus.gpuMem:type_name -> apis.common.ResourceMetric
	1,  // 7: apis.aistudio.queue.v1.QueueStatus.state:type_name -> apis.aistudio.queue.v1.QueueState
	23, // 8: apis.aistudio.queue.v1.QueueStatus.cpu:type_name -> apis.common.ResourceMetric
	23, // 9: apis.aistudio.queue.v1.QueueStatus.memory:type_name -> apis.common.ResourceMetric
	23, // 10: apis.aistudio.queue.v1.QueueStatus.gpu:type_name -> apis.common.ResourceMetric
	24, // 11: apis.aistudio.queue.v1.QueueStatus.nodeGroupMetrics:type_name -> apis.common.NodeGroupResourceMetric
	3,  // 12: apis.aistudio.queue.v1.QueueStatus.clusterStatus:type_name -> apis.aistudio.queue.v1.ClusterQueueStatus
	23, // 13: apis.aistudio.queue.v1.QueueStatus.gpuMem:type_name -> apis.common.ResourceMetric
	0,  // 14: apis.aistudio.queue.v1.CreateOrUpdateQueueRequest.workloadType:type_name -> apis.aistudio.queue.v1.WorkloadType
	2,  // 15: apis.aistudio.queue.v1.ListQueueResult.queues:type_name -> apis.aistudio.queue.v1.Queue
	1,  // 16: apis.aistudio.queue.v1.UpdateQueueStateRequest.state:type_name -> apis.aistudio.queue.v1.QueueState
	23, // 17: apis.aistudio.queue.v1.QueueMetric.cpu:type_name -> apis.common.ResourceMetric
	23, // 18: apis.aistudio.queue.v1.QueueMetric.memory:type_name -> apis.common.ResourceMetric
	23, // 19: apis.aistudio.queue.v1.QueueMetric.gpu:type_name -> apis.common.ResourceMetric
	24, // 20: apis.aistudio.queue.v1.QueueMetric.nodeGroups:type_name -> apis.common.NodeGroupResourceMetric
	21, // 21: apis.aistudio.queue.v1.GetQueueGpuMemorySummaryResponse.specGroups:type_name -> apis.aistudio.queue.v1.GpuSpecMemoryGroup
	22, // 22: apis.aistudio.queue.v1.GpuSpecMemoryGroup.records:type_name -> apis.aistudio.queue.v1.NodeGpuMemoryRecord
	7,  // 23: apis.aistudio.queue.v1.QueueService.CreateQueue:input_type -> apis.aistudio.queue.v1.CreateOrUpdateQueueRequest
	7,  // 24: apis.aistudio.queue.v1.QueueService.UpdateQueue:input_type -> apis.aistudio.queue.v1.CreateOrUpdateQueueRequest
	12, // 25: apis.aistudio.queue.v1.QueueService.DeleteQueue:input_type -> apis.aistudio.queue.v1.DeleteQueueRequest
	8,  // 26: apis.aistudio.queue.v1.QueueService.ListQueue:input_type -> apis.aistudio.queue.v1.ListQueueOptions
	11, // 27: apis.aistudio.queue.v1.QueueService.GetQueueDetail:input_type -> apis.aistudio.queue.v1.GetQueueDetailRequest
	13, // 28: apis.aistudio.queue.v1.QueueService.GetQueueMetrics:input_type -> apis.aistudio.queue.v1.GetQueueMetricsRequest
	5,  // 29: apis.aistudio.queue.v1.QueueService.UpdateMembers:input_type -> apis.aistudio.queue.v1.UpdateMembersRequest
	15, // 30: apis.aistudio.queue.v1.QueueService.CheckQueueAuth:input_type -> apis.aistudio.queue.v1.CheckQueueAuthRequest
	17, // 31: apis.aistudio.queue.v1.QueueService.GetClustersByQueue:input_type -> apis.aistudio.queue.v1.GetClustersByQueueRequest
	19, // 32: apis.aistudio.queue.v1.QueueService.GetQueueGpuMemorySummary:input_type -> apis.aistudio.queue.v1.GetQueueGpuMemorySummaryRequest
	2,  // 33: apis.aistudio.queue.v1.QueueService.CreateQueue:output_type -> apis.aistudio.queue.v1.Queue
	2,  // 34: apis.aistudio.queue.v1.QueueService.UpdateQueue:output_type -> apis.aistudio.queue.v1.Queue
	25, // 35: apis.aistudio.queue.v1.QueueService.DeleteQueue:output_type -> google.protobuf.Empty
	9,  // 36: apis.aistudio.queue.v1.QueueService.ListQueue:output_type -> apis.aistudio.queue.v1.ListQueueResult
	2,  // 37: apis.aistudio.queue.v1.QueueService.GetQueueDetail:output_type -> apis.aistudio.queue.v1.Queue
	14, // 38: apis.aistudio.queue.v1.QueueService.GetQueueMetrics:output_type -> apis.aistudio.queue.v1.QueueMetric
	25, // 39: apis.aistudio.queue.v1.QueueService.UpdateMembers:output_type -> google.protobuf.Empty
	16, // 40: apis.aistudio.queue.v1.QueueService.CheckQueueAuth:output_type -> apis.aistudio.queue.v1.CheckQueueAuthResponse
	18, // 41: apis.aistudio.queue.v1.QueueService.GetClustersByQueue:output_type -> apis.aistudio.queue.v1.GetClustersByQueueResponse
	20, // 42: apis.aistudio.queue.v1.QueueService.GetQueueGpuMemorySummary:output_type -> apis.aistudio.queue.v1.GetQueueGpuMemorySummaryResponse
	33, // [33:43] is the sub-list for method output_type
	23, // [23:33] is the sub-list for method input_type
	23, // [23:23] is the sub-list for extension type_name
	23, // [23:23] is the sub-list for extension extendee
	0,  // [0:23] is the sub-list for field type_name
}

func init() { file_aistudio_queue_v1_queue_proto_init() }
func file_aistudio_queue_v1_queue_proto_init() {
	if File_aistudio_queue_v1_queue_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_aistudio_queue_v1_queue_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*Queue); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_queue_v1_queue_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*ClusterQueueStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_queue_v1_queue_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*QueueStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_queue_v1_queue_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*UpdateMembersRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_queue_v1_queue_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*UpdateManagersRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_queue_v1_queue_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*CreateOrUpdateQueueRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_queue_v1_queue_proto_msgTypes[6].Exporter = func(v any, i int) any {
			switch v := v.(*ListQueueOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_queue_v1_queue_proto_msgTypes[7].Exporter = func(v any, i int) any {
			switch v := v.(*ListQueueResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_queue_v1_queue_proto_msgTypes[8].Exporter = func(v any, i int) any {
			switch v := v.(*UpdateQueueStateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_queue_v1_queue_proto_msgTypes[9].Exporter = func(v any, i int) any {
			switch v := v.(*GetQueueDetailRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_queue_v1_queue_proto_msgTypes[10].Exporter = func(v any, i int) any {
			switch v := v.(*DeleteQueueRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_queue_v1_queue_proto_msgTypes[11].Exporter = func(v any, i int) any {
			switch v := v.(*GetQueueMetricsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_queue_v1_queue_proto_msgTypes[12].Exporter = func(v any, i int) any {
			switch v := v.(*QueueMetric); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_queue_v1_queue_proto_msgTypes[13].Exporter = func(v any, i int) any {
			switch v := v.(*CheckQueueAuthRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_queue_v1_queue_proto_msgTypes[14].Exporter = func(v any, i int) any {
			switch v := v.(*CheckQueueAuthResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_queue_v1_queue_proto_msgTypes[15].Exporter = func(v any, i int) any {
			switch v := v.(*GetClustersByQueueRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_queue_v1_queue_proto_msgTypes[16].Exporter = func(v any, i int) any {
			switch v := v.(*GetClustersByQueueResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_queue_v1_queue_proto_msgTypes[17].Exporter = func(v any, i int) any {
			switch v := v.(*GetQueueGpuMemorySummaryRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_queue_v1_queue_proto_msgTypes[18].Exporter = func(v any, i int) any {
			switch v := v.(*GetQueueGpuMemorySummaryResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_queue_v1_queue_proto_msgTypes[19].Exporter = func(v any, i int) any {
			switch v := v.(*GpuSpecMemoryGroup); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_queue_v1_queue_proto_msgTypes[20].Exporter = func(v any, i int) any {
			switch v := v.(*NodeGpuMemoryRecord); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_aistudio_queue_v1_queue_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   21,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_aistudio_queue_v1_queue_proto_goTypes,
		DependencyIndexes: file_aistudio_queue_v1_queue_proto_depIdxs,
		EnumInfos:         file_aistudio_queue_v1_queue_proto_enumTypes,
		MessageInfos:      file_aistudio_queue_v1_queue_proto_msgTypes,
	}.Build()
	File_aistudio_queue_v1_queue_proto = out.File
	file_aistudio_queue_v1_queue_proto_rawDesc = nil
	file_aistudio_queue_v1_queue_proto_goTypes = nil
	file_aistudio_queue_v1_queue_proto_depIdxs = nil
}
