// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v5.29.3
// source: aistudio/queue/v1/queue.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	QueueService_CreateQueue_FullMethodName              = "/apis.aistudio.queue.v1.QueueService/CreateQueue"
	QueueService_UpdateQueue_FullMethodName              = "/apis.aistudio.queue.v1.QueueService/UpdateQueue"
	QueueService_DeleteQueue_FullMethodName              = "/apis.aistudio.queue.v1.QueueService/DeleteQueue"
	QueueService_ListQueue_FullMethodName                = "/apis.aistudio.queue.v1.QueueService/ListQueue"
	QueueService_GetQueueDetail_FullMethodName           = "/apis.aistudio.queue.v1.QueueService/GetQueueDetail"
	QueueService_GetQueueMetrics_FullMethodName          = "/apis.aistudio.queue.v1.QueueService/GetQueueMetrics"
	QueueService_UpdateMembers_FullMethodName            = "/apis.aistudio.queue.v1.QueueService/UpdateMembers"
	QueueService_CheckQueueAuth_FullMethodName           = "/apis.aistudio.queue.v1.QueueService/CheckQueueAuth"
	QueueService_GetClustersByQueue_FullMethodName       = "/apis.aistudio.queue.v1.QueueService/GetClustersByQueue"
	QueueService_GetQueueGpuMemorySummary_FullMethodName = "/apis.aistudio.queue.v1.QueueService/GetQueueGpuMemorySummary"
)

// QueueServiceClient is the client API for QueueService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type QueueServiceClient interface {
	// 创建或者更新队列,创建队列的时候会默认在关联的集群中创建队列的资源队列，根据服务器的资源情况进行队列的配额分配
	CreateQueue(ctx context.Context, in *CreateOrUpdateQueueRequest, opts ...grpc.CallOption) (*Queue, error)
	UpdateQueue(ctx context.Context, in *CreateOrUpdateQueueRequest, opts ...grpc.CallOption) (*Queue, error)
	// 删除队列
	// 删除队列时，需要等待队列中的任务全部执行完毕后才能触发
	DeleteQueue(ctx context.Context, in *DeleteQueueRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 查询队列列表
	ListQueue(ctx context.Context, in *ListQueueOptions, opts ...grpc.CallOption) (*ListQueueResult, error)
	GetQueueDetail(ctx context.Context, in *GetQueueDetailRequest, opts ...grpc.CallOption) (*Queue, error)
	GetQueueMetrics(ctx context.Context, in *GetQueueMetricsRequest, opts ...grpc.CallOption) (*QueueMetric, error)
	// UpdateMembers 更新成员
	UpdateMembers(ctx context.Context, in *UpdateMembersRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 校验队列权限
	CheckQueueAuth(ctx context.Context, in *CheckQueueAuthRequest, opts ...grpc.CallOption) (*CheckQueueAuthResponse, error)
	GetClustersByQueue(ctx context.Context, in *GetClustersByQueueRequest, opts ...grpc.CallOption) (*GetClustersByQueueResponse, error)
	// 获取队列各规格共享显存最大剩余量
	GetQueueGpuMemorySummary(ctx context.Context, in *GetQueueGpuMemorySummaryRequest, opts ...grpc.CallOption) (*GetQueueGpuMemorySummaryResponse, error)
}

type queueServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewQueueServiceClient(cc grpc.ClientConnInterface) QueueServiceClient {
	return &queueServiceClient{cc}
}

func (c *queueServiceClient) CreateQueue(ctx context.Context, in *CreateOrUpdateQueueRequest, opts ...grpc.CallOption) (*Queue, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(Queue)
	err := c.cc.Invoke(ctx, QueueService_CreateQueue_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *queueServiceClient) UpdateQueue(ctx context.Context, in *CreateOrUpdateQueueRequest, opts ...grpc.CallOption) (*Queue, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(Queue)
	err := c.cc.Invoke(ctx, QueueService_UpdateQueue_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *queueServiceClient) DeleteQueue(ctx context.Context, in *DeleteQueueRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, QueueService_DeleteQueue_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *queueServiceClient) ListQueue(ctx context.Context, in *ListQueueOptions, opts ...grpc.CallOption) (*ListQueueResult, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListQueueResult)
	err := c.cc.Invoke(ctx, QueueService_ListQueue_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *queueServiceClient) GetQueueDetail(ctx context.Context, in *GetQueueDetailRequest, opts ...grpc.CallOption) (*Queue, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(Queue)
	err := c.cc.Invoke(ctx, QueueService_GetQueueDetail_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *queueServiceClient) GetQueueMetrics(ctx context.Context, in *GetQueueMetricsRequest, opts ...grpc.CallOption) (*QueueMetric, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(QueueMetric)
	err := c.cc.Invoke(ctx, QueueService_GetQueueMetrics_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *queueServiceClient) UpdateMembers(ctx context.Context, in *UpdateMembersRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, QueueService_UpdateMembers_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *queueServiceClient) CheckQueueAuth(ctx context.Context, in *CheckQueueAuthRequest, opts ...grpc.CallOption) (*CheckQueueAuthResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CheckQueueAuthResponse)
	err := c.cc.Invoke(ctx, QueueService_CheckQueueAuth_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *queueServiceClient) GetClustersByQueue(ctx context.Context, in *GetClustersByQueueRequest, opts ...grpc.CallOption) (*GetClustersByQueueResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetClustersByQueueResponse)
	err := c.cc.Invoke(ctx, QueueService_GetClustersByQueue_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *queueServiceClient) GetQueueGpuMemorySummary(ctx context.Context, in *GetQueueGpuMemorySummaryRequest, opts ...grpc.CallOption) (*GetQueueGpuMemorySummaryResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetQueueGpuMemorySummaryResponse)
	err := c.cc.Invoke(ctx, QueueService_GetQueueGpuMemorySummary_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// QueueServiceServer is the server API for QueueService service.
// All implementations must embed UnimplementedQueueServiceServer
// for forward compatibility
type QueueServiceServer interface {
	// 创建或者更新队列,创建队列的时候会默认在关联的集群中创建队列的资源队列，根据服务器的资源情况进行队列的配额分配
	CreateQueue(context.Context, *CreateOrUpdateQueueRequest) (*Queue, error)
	UpdateQueue(context.Context, *CreateOrUpdateQueueRequest) (*Queue, error)
	// 删除队列
	// 删除队列时，需要等待队列中的任务全部执行完毕后才能触发
	DeleteQueue(context.Context, *DeleteQueueRequest) (*emptypb.Empty, error)
	// 查询队列列表
	ListQueue(context.Context, *ListQueueOptions) (*ListQueueResult, error)
	GetQueueDetail(context.Context, *GetQueueDetailRequest) (*Queue, error)
	GetQueueMetrics(context.Context, *GetQueueMetricsRequest) (*QueueMetric, error)
	// UpdateMembers 更新成员
	UpdateMembers(context.Context, *UpdateMembersRequest) (*emptypb.Empty, error)
	// 校验队列权限
	CheckQueueAuth(context.Context, *CheckQueueAuthRequest) (*CheckQueueAuthResponse, error)
	GetClustersByQueue(context.Context, *GetClustersByQueueRequest) (*GetClustersByQueueResponse, error)
	// 获取队列各规格共享显存最大剩余量
	GetQueueGpuMemorySummary(context.Context, *GetQueueGpuMemorySummaryRequest) (*GetQueueGpuMemorySummaryResponse, error)
	mustEmbedUnimplementedQueueServiceServer()
}

// UnimplementedQueueServiceServer must be embedded to have forward compatible implementations.
type UnimplementedQueueServiceServer struct {
}

func (UnimplementedQueueServiceServer) CreateQueue(context.Context, *CreateOrUpdateQueueRequest) (*Queue, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateQueue not implemented")
}
func (UnimplementedQueueServiceServer) UpdateQueue(context.Context, *CreateOrUpdateQueueRequest) (*Queue, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateQueue not implemented")
}
func (UnimplementedQueueServiceServer) DeleteQueue(context.Context, *DeleteQueueRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteQueue not implemented")
}
func (UnimplementedQueueServiceServer) ListQueue(context.Context, *ListQueueOptions) (*ListQueueResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListQueue not implemented")
}
func (UnimplementedQueueServiceServer) GetQueueDetail(context.Context, *GetQueueDetailRequest) (*Queue, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetQueueDetail not implemented")
}
func (UnimplementedQueueServiceServer) GetQueueMetrics(context.Context, *GetQueueMetricsRequest) (*QueueMetric, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetQueueMetrics not implemented")
}
func (UnimplementedQueueServiceServer) UpdateMembers(context.Context, *UpdateMembersRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateMembers not implemented")
}
func (UnimplementedQueueServiceServer) CheckQueueAuth(context.Context, *CheckQueueAuthRequest) (*CheckQueueAuthResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckQueueAuth not implemented")
}
func (UnimplementedQueueServiceServer) GetClustersByQueue(context.Context, *GetClustersByQueueRequest) (*GetClustersByQueueResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetClustersByQueue not implemented")
}
func (UnimplementedQueueServiceServer) GetQueueGpuMemorySummary(context.Context, *GetQueueGpuMemorySummaryRequest) (*GetQueueGpuMemorySummaryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetQueueGpuMemorySummary not implemented")
}
func (UnimplementedQueueServiceServer) mustEmbedUnimplementedQueueServiceServer() {}

// UnsafeQueueServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to QueueServiceServer will
// result in compilation errors.
type UnsafeQueueServiceServer interface {
	mustEmbedUnimplementedQueueServiceServer()
}

func RegisterQueueServiceServer(s grpc.ServiceRegistrar, srv QueueServiceServer) {
	s.RegisterService(&QueueService_ServiceDesc, srv)
}

func _QueueService_CreateQueue_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateOrUpdateQueueRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(QueueServiceServer).CreateQueue(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: QueueService_CreateQueue_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(QueueServiceServer).CreateQueue(ctx, req.(*CreateOrUpdateQueueRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _QueueService_UpdateQueue_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateOrUpdateQueueRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(QueueServiceServer).UpdateQueue(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: QueueService_UpdateQueue_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(QueueServiceServer).UpdateQueue(ctx, req.(*CreateOrUpdateQueueRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _QueueService_DeleteQueue_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteQueueRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(QueueServiceServer).DeleteQueue(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: QueueService_DeleteQueue_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(QueueServiceServer).DeleteQueue(ctx, req.(*DeleteQueueRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _QueueService_ListQueue_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListQueueOptions)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(QueueServiceServer).ListQueue(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: QueueService_ListQueue_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(QueueServiceServer).ListQueue(ctx, req.(*ListQueueOptions))
	}
	return interceptor(ctx, in, info, handler)
}

func _QueueService_GetQueueDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetQueueDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(QueueServiceServer).GetQueueDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: QueueService_GetQueueDetail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(QueueServiceServer).GetQueueDetail(ctx, req.(*GetQueueDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _QueueService_GetQueueMetrics_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetQueueMetricsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(QueueServiceServer).GetQueueMetrics(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: QueueService_GetQueueMetrics_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(QueueServiceServer).GetQueueMetrics(ctx, req.(*GetQueueMetricsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _QueueService_UpdateMembers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateMembersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(QueueServiceServer).UpdateMembers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: QueueService_UpdateMembers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(QueueServiceServer).UpdateMembers(ctx, req.(*UpdateMembersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _QueueService_CheckQueueAuth_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckQueueAuthRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(QueueServiceServer).CheckQueueAuth(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: QueueService_CheckQueueAuth_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(QueueServiceServer).CheckQueueAuth(ctx, req.(*CheckQueueAuthRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _QueueService_GetClustersByQueue_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetClustersByQueueRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(QueueServiceServer).GetClustersByQueue(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: QueueService_GetClustersByQueue_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(QueueServiceServer).GetClustersByQueue(ctx, req.(*GetClustersByQueueRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _QueueService_GetQueueGpuMemorySummary_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetQueueGpuMemorySummaryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(QueueServiceServer).GetQueueGpuMemorySummary(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: QueueService_GetQueueGpuMemorySummary_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(QueueServiceServer).GetQueueGpuMemorySummary(ctx, req.(*GetQueueGpuMemorySummaryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// QueueService_ServiceDesc is the grpc.ServiceDesc for QueueService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var QueueService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "apis.aistudio.queue.v1.QueueService",
	HandlerType: (*QueueServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateQueue",
			Handler:    _QueueService_CreateQueue_Handler,
		},
		{
			MethodName: "UpdateQueue",
			Handler:    _QueueService_UpdateQueue_Handler,
		},
		{
			MethodName: "DeleteQueue",
			Handler:    _QueueService_DeleteQueue_Handler,
		},
		{
			MethodName: "ListQueue",
			Handler:    _QueueService_ListQueue_Handler,
		},
		{
			MethodName: "GetQueueDetail",
			Handler:    _QueueService_GetQueueDetail_Handler,
		},
		{
			MethodName: "GetQueueMetrics",
			Handler:    _QueueService_GetQueueMetrics_Handler,
		},
		{
			MethodName: "UpdateMembers",
			Handler:    _QueueService_UpdateMembers_Handler,
		},
		{
			MethodName: "CheckQueueAuth",
			Handler:    _QueueService_CheckQueueAuth_Handler,
		},
		{
			MethodName: "GetClustersByQueue",
			Handler:    _QueueService_GetClustersByQueue_Handler,
		},
		{
			MethodName: "GetQueueGpuMemorySummary",
			Handler:    _QueueService_GetQueueGpuMemorySummary_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "aistudio/queue/v1/queue.proto",
}
