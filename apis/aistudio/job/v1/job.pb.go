// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v5.29.3
// source: aistudio/job/v1/job.proto

package v1

import (
	v1 "git.lianjia.com/cloudnative/kic/kic-platform/apis/aistudio/config/v1"
	common "git.lianjia.com/cloudnative/kic/kic-platform/apis/common"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	_ "google.golang.org/protobuf/types/known/durationpb"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type TriggerType int32

const (
	TriggerType_Timer   TriggerType = 0 //定时触发器
	TriggerType_Event   TriggerType = 1 //事件触发器
	TriggerType_Webhook TriggerType = 2 //webhook触发器
)

// Enum value maps for TriggerType.
var (
	TriggerType_name = map[int32]string{
		0: "Timer",
		1: "Event",
		2: "Webhook",
	}
	TriggerType_value = map[string]int32{
		"Timer":   0,
		"Event":   1,
		"Webhook": 2,
	}
)

func (x TriggerType) Enum() *TriggerType {
	p := new(TriggerType)
	*p = x
	return p
}

func (x TriggerType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TriggerType) Descriptor() protoreflect.EnumDescriptor {
	return file_aistudio_job_v1_job_proto_enumTypes[0].Descriptor()
}

func (TriggerType) Type() protoreflect.EnumType {
	return &file_aistudio_job_v1_job_proto_enumTypes[0]
}

func (x TriggerType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TriggerType.Descriptor instead.
func (TriggerType) EnumDescriptor() ([]byte, []int) {
	return file_aistudio_job_v1_job_proto_rawDescGZIP(), []int{0}
}

type ExecutionStrategy int32

const (
	ExecutionStrategy_ImmediateFirstTask ExecutionStrategy = 0 // 立即执行首次任务
	ExecutionStrategy_SaveTemplateOnly   ExecutionStrategy = 1 // 仅保存模板不执行任务
	ExecutionStrategy_ScheduledExecution ExecutionStrategy = 2 // 按照调度策略执行任务
)

// Enum value maps for ExecutionStrategy.
var (
	ExecutionStrategy_name = map[int32]string{
		0: "ImmediateFirstTask",
		1: "SaveTemplateOnly",
		2: "ScheduledExecution",
	}
	ExecutionStrategy_value = map[string]int32{
		"ImmediateFirstTask": 0,
		"SaveTemplateOnly":   1,
		"ScheduledExecution": 2,
	}
)

func (x ExecutionStrategy) Enum() *ExecutionStrategy {
	p := new(ExecutionStrategy)
	*p = x
	return p
}

func (x ExecutionStrategy) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ExecutionStrategy) Descriptor() protoreflect.EnumDescriptor {
	return file_aistudio_job_v1_job_proto_enumTypes[1].Descriptor()
}

func (ExecutionStrategy) Type() protoreflect.EnumType {
	return &file_aistudio_job_v1_job_proto_enumTypes[1]
}

func (x ExecutionStrategy) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ExecutionStrategy.Descriptor instead.
func (ExecutionStrategy) EnumDescriptor() ([]byte, []int) {
	return file_aistudio_job_v1_job_proto_rawDescGZIP(), []int{1}
}

type TriggerState int32

const (
	TriggerState_TriggerState_Inactive  TriggerState = 0 // 未生效
	TriggerState_TriggerState_Executing TriggerState = 1 // 执行中
	TriggerState_TriggerState_Paused    TriggerState = 2 // 暂停中
	TriggerState_TriggerState_Completed TriggerState = 3 // 已完成（执行次数等于最大副本数）
	TriggerState_TriggerState_Expired   TriggerState = 4 // 已过期（超过有效期）
)

// Enum value maps for TriggerState.
var (
	TriggerState_name = map[int32]string{
		0: "TriggerState_Inactive",
		1: "TriggerState_Executing",
		2: "TriggerState_Paused",
		3: "TriggerState_Completed",
		4: "TriggerState_Expired",
	}
	TriggerState_value = map[string]int32{
		"TriggerState_Inactive":  0,
		"TriggerState_Executing": 1,
		"TriggerState_Paused":    2,
		"TriggerState_Completed": 3,
		"TriggerState_Expired":   4,
	}
)

func (x TriggerState) Enum() *TriggerState {
	p := new(TriggerState)
	*p = x
	return p
}

func (x TriggerState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TriggerState) Descriptor() protoreflect.EnumDescriptor {
	return file_aistudio_job_v1_job_proto_enumTypes[2].Descriptor()
}

func (TriggerState) Type() protoreflect.EnumType {
	return &file_aistudio_job_v1_job_proto_enumTypes[2]
}

func (x TriggerState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TriggerState.Descriptor instead.
func (TriggerState) EnumDescriptor() ([]byte, []int) {
	return file_aistudio_job_v1_job_proto_rawDescGZIP(), []int{2}
}

type Priority int32

const (
	Priority_LOW    Priority = 0
	Priority_NORMAL Priority = 1
	Priority_HIGH   Priority = 2
)

// Enum value maps for Priority.
var (
	Priority_name = map[int32]string{
		0: "LOW",
		1: "NORMAL",
		2: "HIGH",
	}
	Priority_value = map[string]int32{
		"LOW":    0,
		"NORMAL": 1,
		"HIGH":   2,
	}
)

func (x Priority) Enum() *Priority {
	p := new(Priority)
	*p = x
	return p
}

func (x Priority) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Priority) Descriptor() protoreflect.EnumDescriptor {
	return file_aistudio_job_v1_job_proto_enumTypes[3].Descriptor()
}

func (Priority) Type() protoreflect.EnumType {
	return &file_aistudio_job_v1_job_proto_enumTypes[3]
}

func (x Priority) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Priority.Descriptor instead.
func (Priority) EnumDescriptor() ([]byte, []int) {
	return file_aistudio_job_v1_job_proto_rawDescGZIP(), []int{3}
}

type JobType int32

const (
	JobType_PyTorchDDP     JobType = 0
	JobType_DeepSpeed      JobType = 1
	JobType_SimpleTraining JobType = 2
)

// Enum value maps for JobType.
var (
	JobType_name = map[int32]string{
		0: "PyTorchDDP",
		1: "DeepSpeed",
		2: "SimpleTraining",
	}
	JobType_value = map[string]int32{
		"PyTorchDDP":     0,
		"DeepSpeed":      1,
		"SimpleTraining": 2,
	}
)

func (x JobType) Enum() *JobType {
	p := new(JobType)
	*p = x
	return p
}

func (x JobType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (JobType) Descriptor() protoreflect.EnumDescriptor {
	return file_aistudio_job_v1_job_proto_enumTypes[4].Descriptor()
}

func (JobType) Type() protoreflect.EnumType {
	return &file_aistudio_job_v1_job_proto_enumTypes[4]
}

func (x JobType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use JobType.Descriptor instead.
func (JobType) EnumDescriptor() ([]byte, []int) {
	return file_aistudio_job_v1_job_proto_rawDescGZIP(), []int{4}
}

type TaskPhase int32

const (
	TaskPhase_TaskPhase_Pending   TaskPhase = 0
	TaskPhase_TaskPhase_Running   TaskPhase = 1
	TaskPhase_TaskPhase_Succeeded TaskPhase = 2
	TaskPhase_TaskPhase_Failed    TaskPhase = 3
	TaskPhase_TaskPhase_Unknown   TaskPhase = 4
)

// Enum value maps for TaskPhase.
var (
	TaskPhase_name = map[int32]string{
		0: "TaskPhase_Pending",
		1: "TaskPhase_Running",
		2: "TaskPhase_Succeeded",
		3: "TaskPhase_Failed",
		4: "TaskPhase_Unknown",
	}
	TaskPhase_value = map[string]int32{
		"TaskPhase_Pending":   0,
		"TaskPhase_Running":   1,
		"TaskPhase_Succeeded": 2,
		"TaskPhase_Failed":    3,
		"TaskPhase_Unknown":   4,
	}
)

func (x TaskPhase) Enum() *TaskPhase {
	p := new(TaskPhase)
	*p = x
	return p
}

func (x TaskPhase) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TaskPhase) Descriptor() protoreflect.EnumDescriptor {
	return file_aistudio_job_v1_job_proto_enumTypes[5].Descriptor()
}

func (TaskPhase) Type() protoreflect.EnumType {
	return &file_aistudio_job_v1_job_proto_enumTypes[5]
}

func (x TaskPhase) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TaskPhase.Descriptor instead.
func (TaskPhase) EnumDescriptor() ([]byte, []int) {
	return file_aistudio_job_v1_job_proto_rawDescGZIP(), []int{5}
}

type TaskConditionType int32

const (
	TaskConditionType_ContainersReady TaskConditionType = 0 // ContainersReady indicates whether all containers in the pod are ready.
	TaskConditionType_PodInitialized  TaskConditionType = 1 // PodInitialized means that all init containers in the pod have started successfully.
	// PodReady means the pod is able to service requests and should be added to the
	// load balancing pools of all matching services.
	TaskConditionType_PodReady TaskConditionType = 2
	// PodScheduled represents status of the scheduling process for this pod.
	TaskConditionType_PodScheduled TaskConditionType = 3
	// DisruptionTarget indicates the pod is about to be terminated due to a
	// disruption (such as preemption, eviction API or garbage-collection).
	TaskConditionType_DisruptionTarget TaskConditionType = 4
)

// Enum value maps for TaskConditionType.
var (
	TaskConditionType_name = map[int32]string{
		0: "ContainersReady",
		1: "PodInitialized",
		2: "PodReady",
		3: "PodScheduled",
		4: "DisruptionTarget",
	}
	TaskConditionType_value = map[string]int32{
		"ContainersReady":  0,
		"PodInitialized":   1,
		"PodReady":         2,
		"PodScheduled":     3,
		"DisruptionTarget": 4,
	}
)

func (x TaskConditionType) Enum() *TaskConditionType {
	p := new(TaskConditionType)
	*p = x
	return p
}

func (x TaskConditionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TaskConditionType) Descriptor() protoreflect.EnumDescriptor {
	return file_aistudio_job_v1_job_proto_enumTypes[6].Descriptor()
}

func (TaskConditionType) Type() protoreflect.EnumType {
	return &file_aistudio_job_v1_job_proto_enumTypes[6]
}

func (x TaskConditionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TaskConditionType.Descriptor instead.
func (TaskConditionType) EnumDescriptor() ([]byte, []int) {
	return file_aistudio_job_v1_job_proto_rawDescGZIP(), []int{6}
}

type JobState int32

const (
	JobState_JobState_Pending     JobState = 0
	JobState_JobState_Running     JobState = 1
	JobState_JobState_Aborting    JobState = 3
	JobState_JobState_Aborted     JobState = 4
	JobState_JobState_Restarting  JobState = 5
	JobState_JobState_Completing  JobState = 6
	JobState_JobState_Completed   JobState = 7
	JobState_JobState_Terminating JobState = 8
	JobState_JobState_Terminated  JobState = 9
	JobState_JobState_Failed      JobState = 10
)

// Enum value maps for JobState.
var (
	JobState_name = map[int32]string{
		0:  "JobState_Pending",
		1:  "JobState_Running",
		3:  "JobState_Aborting",
		4:  "JobState_Aborted",
		5:  "JobState_Restarting",
		6:  "JobState_Completing",
		7:  "JobState_Completed",
		8:  "JobState_Terminating",
		9:  "JobState_Terminated",
		10: "JobState_Failed",
	}
	JobState_value = map[string]int32{
		"JobState_Pending":     0,
		"JobState_Running":     1,
		"JobState_Aborting":    3,
		"JobState_Aborted":     4,
		"JobState_Restarting":  5,
		"JobState_Completing":  6,
		"JobState_Completed":   7,
		"JobState_Terminating": 8,
		"JobState_Terminated":  9,
		"JobState_Failed":      10,
	}
)

func (x JobState) Enum() *JobState {
	p := new(JobState)
	*p = x
	return p
}

func (x JobState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (JobState) Descriptor() protoreflect.EnumDescriptor {
	return file_aistudio_job_v1_job_proto_enumTypes[7].Descriptor()
}

func (JobState) Type() protoreflect.EnumType {
	return &file_aistudio_job_v1_job_proto_enumTypes[7]
}

func (x JobState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use JobState.Descriptor instead.
func (JobState) EnumDescriptor() ([]byte, []int) {
	return file_aistudio_job_v1_job_proto_rawDescGZIP(), []int{7}
}

type JobPhase int32

const (
	JobPhase_InQueue             JobPhase = 0
	JobPhase_ResourcePreparation JobPhase = 1
	JobPhase_Running             JobPhase = 2
	JobPhase_Finished            JobPhase = 3
	JobPhase_Killed              JobPhase = 4
	JobPhase_Failed              JobPhase = 5
)

// Enum value maps for JobPhase.
var (
	JobPhase_name = map[int32]string{
		0: "InQueue",
		1: "ResourcePreparation",
		2: "Running",
		3: "Finished",
		4: "Killed",
		5: "Failed",
	}
	JobPhase_value = map[string]int32{
		"InQueue":             0,
		"ResourcePreparation": 1,
		"Running":             2,
		"Finished":            3,
		"Killed":              4,
		"Failed":              5,
	}
)

func (x JobPhase) Enum() *JobPhase {
	p := new(JobPhase)
	*p = x
	return p
}

func (x JobPhase) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (JobPhase) Descriptor() protoreflect.EnumDescriptor {
	return file_aistudio_job_v1_job_proto_enumTypes[8].Descriptor()
}

func (JobPhase) Type() protoreflect.EnumType {
	return &file_aistudio_job_v1_job_proto_enumTypes[8]
}

func (x JobPhase) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use JobPhase.Descriptor instead.
func (JobPhase) EnumDescriptor() ([]byte, []int) {
	return file_aistudio_job_v1_job_proto_rawDescGZIP(), []int{8}
}

type TimerTriggerOptions_TimerTriggerType int32

const (
	TimerTriggerOptions_Interval TimerTriggerOptions_TimerTriggerType = 0
	TimerTriggerOptions_CronExpr TimerTriggerOptions_TimerTriggerType = 1
	TimerTriggerOptions_Cycle    TimerTriggerOptions_TimerTriggerType = 2
)

// Enum value maps for TimerTriggerOptions_TimerTriggerType.
var (
	TimerTriggerOptions_TimerTriggerType_name = map[int32]string{
		0: "Interval",
		1: "CronExpr",
		2: "Cycle",
	}
	TimerTriggerOptions_TimerTriggerType_value = map[string]int32{
		"Interval": 0,
		"CronExpr": 1,
		"Cycle":    2,
	}
)

func (x TimerTriggerOptions_TimerTriggerType) Enum() *TimerTriggerOptions_TimerTriggerType {
	p := new(TimerTriggerOptions_TimerTriggerType)
	*p = x
	return p
}

func (x TimerTriggerOptions_TimerTriggerType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TimerTriggerOptions_TimerTriggerType) Descriptor() protoreflect.EnumDescriptor {
	return file_aistudio_job_v1_job_proto_enumTypes[9].Descriptor()
}

func (TimerTriggerOptions_TimerTriggerType) Type() protoreflect.EnumType {
	return &file_aistudio_job_v1_job_proto_enumTypes[9]
}

func (x TimerTriggerOptions_TimerTriggerType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TimerTriggerOptions_TimerTriggerType.Descriptor instead.
func (TimerTriggerOptions_TimerTriggerType) EnumDescriptor() ([]byte, []int) {
	return file_aistudio_job_v1_job_proto_rawDescGZIP(), []int{6, 0}
}

type JobCondition_ConditionStatus int32

const (
	JobCondition_True    JobCondition_ConditionStatus = 0
	JobCondition_False   JobCondition_ConditionStatus = 1
	JobCondition_Unknown JobCondition_ConditionStatus = 2
)

// Enum value maps for JobCondition_ConditionStatus.
var (
	JobCondition_ConditionStatus_name = map[int32]string{
		0: "True",
		1: "False",
		2: "Unknown",
	}
	JobCondition_ConditionStatus_value = map[string]int32{
		"True":    0,
		"False":   1,
		"Unknown": 2,
	}
)

func (x JobCondition_ConditionStatus) Enum() *JobCondition_ConditionStatus {
	p := new(JobCondition_ConditionStatus)
	*p = x
	return p
}

func (x JobCondition_ConditionStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (JobCondition_ConditionStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_aistudio_job_v1_job_proto_enumTypes[10].Descriptor()
}

func (JobCondition_ConditionStatus) Type() protoreflect.EnumType {
	return &file_aistudio_job_v1_job_proto_enumTypes[10]
}

func (x JobCondition_ConditionStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use JobCondition_ConditionStatus.Descriptor instead.
func (JobCondition_ConditionStatus) EnumDescriptor() ([]byte, []int) {
	return file_aistudio_job_v1_job_proto_rawDescGZIP(), []int{40, 0}
}

type CheckJobIsExistRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName string `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	JobName       string `protobuf:"bytes,2,opt,name=jobName,proto3" json:"jobName,omitempty"`
}

func (x *CheckJobIsExistRequest) Reset() {
	*x = CheckJobIsExistRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_job_v1_job_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckJobIsExistRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckJobIsExistRequest) ProtoMessage() {}

func (x *CheckJobIsExistRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_job_v1_job_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckJobIsExistRequest.ProtoReflect.Descriptor instead.
func (*CheckJobIsExistRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_job_v1_job_proto_rawDescGZIP(), []int{0}
}

func (x *CheckJobIsExistRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *CheckJobIsExistRequest) GetJobName() string {
	if x != nil {
		return x.JobName
	}
	return ""
}

type CheckJobIsExistResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status int32  `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"` // 适配 fcn，0 为校验成功， 422 为校验失败
	Errors string `protobuf:"bytes,2,opt,name=errors,proto3" json:"errors,omitempty"`
}

func (x *CheckJobIsExistResponse) Reset() {
	*x = CheckJobIsExistResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_job_v1_job_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckJobIsExistResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckJobIsExistResponse) ProtoMessage() {}

func (x *CheckJobIsExistResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_job_v1_job_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckJobIsExistResponse.ProtoReflect.Descriptor instead.
func (*CheckJobIsExistResponse) Descriptor() ([]byte, []int) {
	return file_aistudio_job_v1_job_proto_rawDescGZIP(), []int{1}
}

func (x *CheckJobIsExistResponse) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *CheckJobIsExistResponse) GetErrors() string {
	if x != nil {
		return x.Errors
	}
	return ""
}

type UpdateMembersRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName string   `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	JobName       string   `protobuf:"bytes,2,opt,name=jobName,proto3" json:"jobName,omitempty"`
	Members       []string `protobuf:"bytes,3,rep,name=members,proto3" json:"members,omitempty"`
}

func (x *UpdateMembersRequest) Reset() {
	*x = UpdateMembersRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_job_v1_job_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateMembersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateMembersRequest) ProtoMessage() {}

func (x *UpdateMembersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_job_v1_job_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateMembersRequest.ProtoReflect.Descriptor instead.
func (*UpdateMembersRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_job_v1_job_proto_rawDescGZIP(), []int{2}
}

func (x *UpdateMembersRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *UpdateMembersRequest) GetJobName() string {
	if x != nil {
		return x.JobName
	}
	return ""
}

func (x *UpdateMembersRequest) GetMembers() []string {
	if x != nil {
		return x.Members
	}
	return nil
}

type JobTemplate struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name              string            `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	DisplayName       string            `protobuf:"bytes,2,opt,name=displayName,proto3" json:"displayName,omitempty"`
	Description       string            `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	WorkspaceName     string            `protobuf:"bytes,4,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Region            string            `protobuf:"bytes,5,opt,name=region,proto3" json:"region,omitempty"`
	Labels            map[string]string `protobuf:"bytes,6,rep,name=labels,proto3" json:"labels,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Managers          []string          `protobuf:"bytes,7,rep,name=managers,proto3" json:"managers,omitempty"`
	Members           []string          `protobuf:"bytes,8,rep,name=members,proto3" json:"members,omitempty"`
	Triggers          []*Trigger        `protobuf:"bytes,9,rep,name=triggers,proto3" json:"triggers,omitempty"`
	ExecutionStrategy ExecutionStrategy `protobuf:"varint,10,opt,name=executionStrategy,proto3,enum=apis.aistudio.job.v1.ExecutionStrategy" json:"executionStrategy,omitempty"` // 执行策略
	Job               *Job              `protobuf:"bytes,11,opt,name=job,proto3" json:"job,omitempty"`
	Creator           string            `protobuf:"bytes,12,opt,name=creator,proto3" json:"creator,omitempty"`
	CreateTime        string            `protobuf:"bytes,14,opt,name=createTime,proto3" json:"createTime,omitempty"`
	UpdateTime        string            `protobuf:"bytes,15,opt,name=updateTime,proto3" json:"updateTime,omitempty"`
}

func (x *JobTemplate) Reset() {
	*x = JobTemplate{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_job_v1_job_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *JobTemplate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JobTemplate) ProtoMessage() {}

func (x *JobTemplate) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_job_v1_job_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JobTemplate.ProtoReflect.Descriptor instead.
func (*JobTemplate) Descriptor() ([]byte, []int) {
	return file_aistudio_job_v1_job_proto_rawDescGZIP(), []int{3}
}

func (x *JobTemplate) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *JobTemplate) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *JobTemplate) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *JobTemplate) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *JobTemplate) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *JobTemplate) GetLabels() map[string]string {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *JobTemplate) GetManagers() []string {
	if x != nil {
		return x.Managers
	}
	return nil
}

func (x *JobTemplate) GetMembers() []string {
	if x != nil {
		return x.Members
	}
	return nil
}

func (x *JobTemplate) GetTriggers() []*Trigger {
	if x != nil {
		return x.Triggers
	}
	return nil
}

func (x *JobTemplate) GetExecutionStrategy() ExecutionStrategy {
	if x != nil {
		return x.ExecutionStrategy
	}
	return ExecutionStrategy_ImmediateFirstTask
}

func (x *JobTemplate) GetJob() *Job {
	if x != nil {
		return x.Job
	}
	return nil
}

func (x *JobTemplate) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *JobTemplate) GetCreateTime() string {
	if x != nil {
		return x.CreateTime
	}
	return ""
}

func (x *JobTemplate) GetUpdateTime() string {
	if x != nil {
		return x.UpdateTime
	}
	return ""
}

type JobTemplateView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	JobTemplate       *JobTemplate `protobuf:"bytes,1,opt,name=jobTemplate,proto3" json:"jobTemplate,omitempty"`
	JobTemplateStatus string       `protobuf:"bytes,2,opt,name=jobTemplateStatus,proto3" json:"jobTemplateStatus,omitempty"` // 任务模板状态，获取任务模板所有触发器状态，封装模板状态展示
	NextNumActions    []string     `protobuf:"bytes,3,rep,name=nextNumActions,proto3" json:"nextNumActions,omitempty"`       // 下次执行时间，如果存在多个定时调度触发器情况下去最近的 3 个
	NumActions        int32        `protobuf:"varint,4,opt,name=numActions,proto3" json:"numActions,omitempty"`              // 已经执行的次数
}

func (x *JobTemplateView) Reset() {
	*x = JobTemplateView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_job_v1_job_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *JobTemplateView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JobTemplateView) ProtoMessage() {}

func (x *JobTemplateView) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_job_v1_job_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JobTemplateView.ProtoReflect.Descriptor instead.
func (*JobTemplateView) Descriptor() ([]byte, []int) {
	return file_aistudio_job_v1_job_proto_rawDescGZIP(), []int{4}
}

func (x *JobTemplateView) GetJobTemplate() *JobTemplate {
	if x != nil {
		return x.JobTemplate
	}
	return nil
}

func (x *JobTemplateView) GetJobTemplateStatus() string {
	if x != nil {
		return x.JobTemplateStatus
	}
	return ""
}

func (x *JobTemplateView) GetNextNumActions() []string {
	if x != nil {
		return x.NextNumActions
	}
	return nil
}

func (x *JobTemplateView) GetNumActions() int32 {
	if x != nil {
		return x.NumActions
	}
	return 0
}

type Trigger struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TriggerType        TriggerType          `protobuf:"varint,1,opt,name=triggerType,proto3,enum=apis.aistudio.job.v1.TriggerType" json:"triggerType,omitempty"` //触发器类型
	TriggerName        string               `protobuf:"bytes,2,opt,name=triggerName,proto3" json:"triggerName,omitempty"`
	TimeTriggerOptions *TimerTriggerOptions `protobuf:"bytes,3,opt,name=timeTriggerOptions,proto3" json:"timeTriggerOptions,omitempty"`
	NextActionTimes    []string             `protobuf:"bytes,4,rep,name=nextActionTimes,proto3" json:"nextActionTimes,omitempty"` // 下次执行时间
	NumActions         int32                `protobuf:"varint,5,opt,name=numActions,proto3" json:"numActions,omitempty"`          // 已经执行的次数
	State              TriggerState         `protobuf:"varint,6,opt,name=state,proto3,enum=apis.aistudio.job.v1.TriggerState" json:"state,omitempty"`
	Message            string               `protobuf:"bytes,7,opt,name=message,proto3" json:"message,omitempty"` // 信息
}

func (x *Trigger) Reset() {
	*x = Trigger{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_job_v1_job_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Trigger) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Trigger) ProtoMessage() {}

func (x *Trigger) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_job_v1_job_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Trigger.ProtoReflect.Descriptor instead.
func (*Trigger) Descriptor() ([]byte, []int) {
	return file_aistudio_job_v1_job_proto_rawDescGZIP(), []int{5}
}

func (x *Trigger) GetTriggerType() TriggerType {
	if x != nil {
		return x.TriggerType
	}
	return TriggerType_Timer
}

func (x *Trigger) GetTriggerName() string {
	if x != nil {
		return x.TriggerName
	}
	return ""
}

func (x *Trigger) GetTimeTriggerOptions() *TimerTriggerOptions {
	if x != nil {
		return x.TimeTriggerOptions
	}
	return nil
}

func (x *Trigger) GetNextActionTimes() []string {
	if x != nil {
		return x.NextActionTimes
	}
	return nil
}

func (x *Trigger) GetNumActions() int32 {
	if x != nil {
		return x.NumActions
	}
	return 0
}

func (x *Trigger) GetState() TriggerState {
	if x != nil {
		return x.State
	}
	return TriggerState_TriggerState_Inactive
}

func (x *Trigger) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type TimerTriggerOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type             TimerTriggerOptions_TimerTriggerType `protobuf:"varint,1,opt,name=type,proto3,enum=apis.aistudio.job.v1.TimerTriggerOptions_TimerTriggerType" json:"type,omitempty"`
	Interval         int32                                `protobuf:"varint,2,opt,name=interval,proto3" json:"interval,omitempty"`                //时间间隔
	CronExpr         string                               `protobuf:"bytes,3,opt,name=cronExpr,proto3" json:"cronExpr,omitempty"`                 //cron表达式
	Cycle            *Cycle                               `protobuf:"bytes,4,opt,name=cycle,proto3" json:"cycle,omitempty"`                       //周期调度
	PeriodOfValidity string                               `protobuf:"bytes,5,opt,name=periodOfValidity,proto3" json:"periodOfValidity,omitempty"` // 有效期
	MaxCount         int32                                `protobuf:"varint,6,opt,name=maxCount,proto3" json:"maxCount,omitempty"`                // 最大执行次数
	SkipTime         *Cycle                               `protobuf:"bytes,7,opt,name=skipTime,proto3" json:"skipTime,omitempty"`                 //跳过时间
}

func (x *TimerTriggerOptions) Reset() {
	*x = TimerTriggerOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_job_v1_job_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TimerTriggerOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TimerTriggerOptions) ProtoMessage() {}

func (x *TimerTriggerOptions) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_job_v1_job_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TimerTriggerOptions.ProtoReflect.Descriptor instead.
func (*TimerTriggerOptions) Descriptor() ([]byte, []int) {
	return file_aistudio_job_v1_job_proto_rawDescGZIP(), []int{6}
}

func (x *TimerTriggerOptions) GetType() TimerTriggerOptions_TimerTriggerType {
	if x != nil {
		return x.Type
	}
	return TimerTriggerOptions_Interval
}

func (x *TimerTriggerOptions) GetInterval() int32 {
	if x != nil {
		return x.Interval
	}
	return 0
}

func (x *TimerTriggerOptions) GetCronExpr() string {
	if x != nil {
		return x.CronExpr
	}
	return ""
}

func (x *TimerTriggerOptions) GetCycle() *Cycle {
	if x != nil {
		return x.Cycle
	}
	return nil
}

func (x *TimerTriggerOptions) GetPeriodOfValidity() string {
	if x != nil {
		return x.PeriodOfValidity
	}
	return ""
}

func (x *TimerTriggerOptions) GetMaxCount() int32 {
	if x != nil {
		return x.MaxCount
	}
	return 0
}

func (x *TimerTriggerOptions) GetSkipTime() *Cycle {
	if x != nil {
		return x.SkipTime
	}
	return nil
}

// 周期
type Cycle struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Month      []int32 `protobuf:"varint,2,rep,packed,name=month,proto3" json:"month,omitempty"`           //月
	DayOfMonth []int32 `protobuf:"varint,3,rep,packed,name=dayOfMonth,proto3" json:"dayOfMonth,omitempty"` //天
	DayOfWeek  []int32 `protobuf:"varint,4,rep,packed,name=dayOfWeek,proto3" json:"dayOfWeek,omitempty"`   //周
	Hour       []int32 `protobuf:"varint,5,rep,packed,name=hour,proto3" json:"hour,omitempty"`             //小时
	Minute     []int32 `protobuf:"varint,6,rep,packed,name=minute,proto3" json:"minute,omitempty"`         //分钟
}

func (x *Cycle) Reset() {
	*x = Cycle{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_job_v1_job_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Cycle) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Cycle) ProtoMessage() {}

func (x *Cycle) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_job_v1_job_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Cycle.ProtoReflect.Descriptor instead.
func (*Cycle) Descriptor() ([]byte, []int) {
	return file_aistudio_job_v1_job_proto_rawDescGZIP(), []int{7}
}

func (x *Cycle) GetMonth() []int32 {
	if x != nil {
		return x.Month
	}
	return nil
}

func (x *Cycle) GetDayOfMonth() []int32 {
	if x != nil {
		return x.DayOfMonth
	}
	return nil
}

func (x *Cycle) GetDayOfWeek() []int32 {
	if x != nil {
		return x.DayOfWeek
	}
	return nil
}

func (x *Cycle) GetHour() []int32 {
	if x != nil {
		return x.Hour
	}
	return nil
}

func (x *Cycle) GetMinute() []int32 {
	if x != nil {
		return x.Minute
	}
	return nil
}

type JobTriggerRecord struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	JobName         string `protobuf:"bytes,1,opt,name=jobName,proto3" json:"jobName,omitempty"`
	JobTemplateName string `protobuf:"bytes,3,opt,name=jobTemplateName,proto3" json:"jobTemplateName,omitempty"`
	TriggerName     string `protobuf:"bytes,4,opt,name=triggerName,proto3" json:"triggerName,omitempty"`
	TriggerTime     string `protobuf:"bytes,5,opt,name=triggerTime,proto3" json:"triggerTime,omitempty"`
	Creator         string `protobuf:"bytes,6,opt,name=creator,proto3" json:"creator,omitempty"`
}

func (x *JobTriggerRecord) Reset() {
	*x = JobTriggerRecord{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_job_v1_job_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *JobTriggerRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JobTriggerRecord) ProtoMessage() {}

func (x *JobTriggerRecord) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_job_v1_job_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JobTriggerRecord.ProtoReflect.Descriptor instead.
func (*JobTriggerRecord) Descriptor() ([]byte, []int) {
	return file_aistudio_job_v1_job_proto_rawDescGZIP(), []int{8}
}

func (x *JobTriggerRecord) GetJobName() string {
	if x != nil {
		return x.JobName
	}
	return ""
}

func (x *JobTriggerRecord) GetJobTemplateName() string {
	if x != nil {
		return x.JobTemplateName
	}
	return ""
}

func (x *JobTriggerRecord) GetTriggerName() string {
	if x != nil {
		return x.TriggerName
	}
	return ""
}

func (x *JobTriggerRecord) GetTriggerTime() string {
	if x != nil {
		return x.TriggerTime
	}
	return ""
}

func (x *JobTriggerRecord) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

type PauseJobTemplateTriggerRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName   string `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	JobTemplateName string `protobuf:"bytes,2,opt,name=jobTemplateName,proto3" json:"jobTemplateName,omitempty"`
	TriggerName     string `protobuf:"bytes,3,opt,name=triggerName,proto3" json:"triggerName,omitempty"`
	Message         string `protobuf:"bytes,4,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *PauseJobTemplateTriggerRequest) Reset() {
	*x = PauseJobTemplateTriggerRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_job_v1_job_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PauseJobTemplateTriggerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PauseJobTemplateTriggerRequest) ProtoMessage() {}

func (x *PauseJobTemplateTriggerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_job_v1_job_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PauseJobTemplateTriggerRequest.ProtoReflect.Descriptor instead.
func (*PauseJobTemplateTriggerRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_job_v1_job_proto_rawDescGZIP(), []int{9}
}

func (x *PauseJobTemplateTriggerRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *PauseJobTemplateTriggerRequest) GetJobTemplateName() string {
	if x != nil {
		return x.JobTemplateName
	}
	return ""
}

func (x *PauseJobTemplateTriggerRequest) GetTriggerName() string {
	if x != nil {
		return x.TriggerName
	}
	return ""
}

func (x *PauseJobTemplateTriggerRequest) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type RecoverJobTemplateTriggerRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName   string `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	JobTemplateName string `protobuf:"bytes,2,opt,name=jobTemplateName,proto3" json:"jobTemplateName,omitempty"`
	TriggerName     string `protobuf:"bytes,3,opt,name=triggerName,proto3" json:"triggerName,omitempty"`
	Message         string `protobuf:"bytes,4,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *RecoverJobTemplateTriggerRequest) Reset() {
	*x = RecoverJobTemplateTriggerRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_job_v1_job_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RecoverJobTemplateTriggerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecoverJobTemplateTriggerRequest) ProtoMessage() {}

func (x *RecoverJobTemplateTriggerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_job_v1_job_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecoverJobTemplateTriggerRequest.ProtoReflect.Descriptor instead.
func (*RecoverJobTemplateTriggerRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_job_v1_job_proto_rawDescGZIP(), []int{10}
}

func (x *RecoverJobTemplateTriggerRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *RecoverJobTemplateTriggerRequest) GetJobTemplateName() string {
	if x != nil {
		return x.JobTemplateName
	}
	return ""
}

func (x *RecoverJobTemplateTriggerRequest) GetTriggerName() string {
	if x != nil {
		return x.TriggerName
	}
	return ""
}

func (x *RecoverJobTemplateTriggerRequest) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type CreateOrUpdateJobTemplateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name              string            `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	DisplayName       string            `protobuf:"bytes,2,opt,name=displayName,proto3" json:"displayName,omitempty"`
	Description       string            `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	WorkspaceName     string            `protobuf:"bytes,4,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Region            string            `protobuf:"bytes,5,opt,name=region,proto3" json:"region,omitempty"`
	Labels            map[string]string `protobuf:"bytes,6,rep,name=labels,proto3" json:"labels,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Job               *Job              `protobuf:"bytes,7,opt,name=job,proto3" json:"job,omitempty"`
	Triggers          []*Trigger        `protobuf:"bytes,8,rep,name=triggers,proto3" json:"triggers,omitempty"`
	ExecutionStrategy ExecutionStrategy `protobuf:"varint,9,opt,name=executionStrategy,proto3,enum=apis.aistudio.job.v1.ExecutionStrategy" json:"executionStrategy,omitempty"` // 执行策略
	Managers          []string          `protobuf:"bytes,10,rep,name=managers,proto3" json:"managers,omitempty"`
	Members           []string          `protobuf:"bytes,11,rep,name=members,proto3" json:"members,omitempty"`
}

func (x *CreateOrUpdateJobTemplateRequest) Reset() {
	*x = CreateOrUpdateJobTemplateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_job_v1_job_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateOrUpdateJobTemplateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateOrUpdateJobTemplateRequest) ProtoMessage() {}

func (x *CreateOrUpdateJobTemplateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_job_v1_job_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateOrUpdateJobTemplateRequest.ProtoReflect.Descriptor instead.
func (*CreateOrUpdateJobTemplateRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_job_v1_job_proto_rawDescGZIP(), []int{11}
}

func (x *CreateOrUpdateJobTemplateRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateOrUpdateJobTemplateRequest) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *CreateOrUpdateJobTemplateRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CreateOrUpdateJobTemplateRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *CreateOrUpdateJobTemplateRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *CreateOrUpdateJobTemplateRequest) GetLabels() map[string]string {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *CreateOrUpdateJobTemplateRequest) GetJob() *Job {
	if x != nil {
		return x.Job
	}
	return nil
}

func (x *CreateOrUpdateJobTemplateRequest) GetTriggers() []*Trigger {
	if x != nil {
		return x.Triggers
	}
	return nil
}

func (x *CreateOrUpdateJobTemplateRequest) GetExecutionStrategy() ExecutionStrategy {
	if x != nil {
		return x.ExecutionStrategy
	}
	return ExecutionStrategy_ImmediateFirstTask
}

func (x *CreateOrUpdateJobTemplateRequest) GetManagers() []string {
	if x != nil {
		return x.Managers
	}
	return nil
}

func (x *CreateOrUpdateJobTemplateRequest) GetMembers() []string {
	if x != nil {
		return x.Members
	}
	return nil
}

type GetJobTemplateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName   string `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	JobTemplateName string `protobuf:"bytes,2,opt,name=jobTemplateName,proto3" json:"jobTemplateName,omitempty"`
}

func (x *GetJobTemplateRequest) Reset() {
	*x = GetJobTemplateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_job_v1_job_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetJobTemplateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetJobTemplateRequest) ProtoMessage() {}

func (x *GetJobTemplateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_job_v1_job_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetJobTemplateRequest.ProtoReflect.Descriptor instead.
func (*GetJobTemplateRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_job_v1_job_proto_rawDescGZIP(), []int{12}
}

func (x *GetJobTemplateRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *GetJobTemplateRequest) GetJobTemplateName() string {
	if x != nil {
		return x.JobTemplateName
	}
	return ""
}

type ListJobTemplateViewsOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName   string `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	JobTemplateName string `protobuf:"bytes,2,opt,name=jobTemplateName,proto3" json:"jobTemplateName,omitempty"`
	Region          string `protobuf:"bytes,4,opt,name=region,proto3" json:"region,omitempty"`
	Manager         string `protobuf:"bytes,3,opt,name=manager,proto3" json:"manager,omitempty"`
	Member          string `protobuf:"bytes,6,opt,name=member,proto3" json:"member,omitempty"`
	Page            int32  `protobuf:"varint,7,opt,name=page,proto3" json:"page,omitempty"`
	PageSize        int32  `protobuf:"varint,8,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
}

func (x *ListJobTemplateViewsOptions) Reset() {
	*x = ListJobTemplateViewsOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_job_v1_job_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListJobTemplateViewsOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListJobTemplateViewsOptions) ProtoMessage() {}

func (x *ListJobTemplateViewsOptions) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_job_v1_job_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListJobTemplateViewsOptions.ProtoReflect.Descriptor instead.
func (*ListJobTemplateViewsOptions) Descriptor() ([]byte, []int) {
	return file_aistudio_job_v1_job_proto_rawDescGZIP(), []int{13}
}

func (x *ListJobTemplateViewsOptions) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *ListJobTemplateViewsOptions) GetJobTemplateName() string {
	if x != nil {
		return x.JobTemplateName
	}
	return ""
}

func (x *ListJobTemplateViewsOptions) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *ListJobTemplateViewsOptions) GetManager() string {
	if x != nil {
		return x.Manager
	}
	return ""
}

func (x *ListJobTemplateViewsOptions) GetMember() string {
	if x != nil {
		return x.Member
	}
	return ""
}

func (x *ListJobTemplateViewsOptions) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListJobTemplateViewsOptions) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

type ListJobTemplateViewsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	JobTemplateViews []*JobTemplateView `protobuf:"bytes,1,rep,name=jobTemplateViews,proto3" json:"jobTemplateViews,omitempty"`
	Total            int64              `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
}

func (x *ListJobTemplateViewsResponse) Reset() {
	*x = ListJobTemplateViewsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_job_v1_job_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListJobTemplateViewsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListJobTemplateViewsResponse) ProtoMessage() {}

func (x *ListJobTemplateViewsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_job_v1_job_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListJobTemplateViewsResponse.ProtoReflect.Descriptor instead.
func (*ListJobTemplateViewsResponse) Descriptor() ([]byte, []int) {
	return file_aistudio_job_v1_job_proto_rawDescGZIP(), []int{14}
}

func (x *ListJobTemplateViewsResponse) GetJobTemplateViews() []*JobTemplateView {
	if x != nil {
		return x.JobTemplateViews
	}
	return nil
}

func (x *ListJobTemplateViewsResponse) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

type CreateOrUpdateJobTemplateTriggerRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName      string               `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	JobTemplateName    string               `protobuf:"bytes,2,opt,name=jobTemplateName,proto3" json:"jobTemplateName,omitempty"`
	TriggerType        TriggerType          `protobuf:"varint,3,opt,name=triggerType,proto3,enum=apis.aistudio.job.v1.TriggerType" json:"triggerType,omitempty"` //触发器类型
	TriggerName        string               `protobuf:"bytes,4,opt,name=triggerName,proto3" json:"triggerName,omitempty"`
	TimeTriggerOptions *TimerTriggerOptions `protobuf:"bytes,5,opt,name=timeTriggerOptions,proto3" json:"timeTriggerOptions,omitempty"`
}

func (x *CreateOrUpdateJobTemplateTriggerRequest) Reset() {
	*x = CreateOrUpdateJobTemplateTriggerRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_job_v1_job_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateOrUpdateJobTemplateTriggerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateOrUpdateJobTemplateTriggerRequest) ProtoMessage() {}

func (x *CreateOrUpdateJobTemplateTriggerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_job_v1_job_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateOrUpdateJobTemplateTriggerRequest.ProtoReflect.Descriptor instead.
func (*CreateOrUpdateJobTemplateTriggerRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_job_v1_job_proto_rawDescGZIP(), []int{15}
}

func (x *CreateOrUpdateJobTemplateTriggerRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *CreateOrUpdateJobTemplateTriggerRequest) GetJobTemplateName() string {
	if x != nil {
		return x.JobTemplateName
	}
	return ""
}

func (x *CreateOrUpdateJobTemplateTriggerRequest) GetTriggerType() TriggerType {
	if x != nil {
		return x.TriggerType
	}
	return TriggerType_Timer
}

func (x *CreateOrUpdateJobTemplateTriggerRequest) GetTriggerName() string {
	if x != nil {
		return x.TriggerName
	}
	return ""
}

func (x *CreateOrUpdateJobTemplateTriggerRequest) GetTimeTriggerOptions() *TimerTriggerOptions {
	if x != nil {
		return x.TimeTriggerOptions
	}
	return nil
}

type DeleteJobTemplateTriggerRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName   string `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	JobTemplateName string `protobuf:"bytes,2,opt,name=jobTemplateName,proto3" json:"jobTemplateName,omitempty"`
	TriggerName     string `protobuf:"bytes,3,opt,name=triggerName,proto3" json:"triggerName,omitempty"`
}

func (x *DeleteJobTemplateTriggerRequest) Reset() {
	*x = DeleteJobTemplateTriggerRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_job_v1_job_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteJobTemplateTriggerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteJobTemplateTriggerRequest) ProtoMessage() {}

func (x *DeleteJobTemplateTriggerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_job_v1_job_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteJobTemplateTriggerRequest.ProtoReflect.Descriptor instead.
func (*DeleteJobTemplateTriggerRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_job_v1_job_proto_rawDescGZIP(), []int{16}
}

func (x *DeleteJobTemplateTriggerRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *DeleteJobTemplateTriggerRequest) GetJobTemplateName() string {
	if x != nil {
		return x.JobTemplateName
	}
	return ""
}

func (x *DeleteJobTemplateTriggerRequest) GetTriggerName() string {
	if x != nil {
		return x.TriggerName
	}
	return ""
}

type GetJobTemplateTriggerRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName   string `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	JobTemplateName string `protobuf:"bytes,2,opt,name=jobTemplateName,proto3" json:"jobTemplateName,omitempty"`
	TriggerName     string `protobuf:"bytes,3,opt,name=triggerName,proto3" json:"triggerName,omitempty"`
}

func (x *GetJobTemplateTriggerRequest) Reset() {
	*x = GetJobTemplateTriggerRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_job_v1_job_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetJobTemplateTriggerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetJobTemplateTriggerRequest) ProtoMessage() {}

func (x *GetJobTemplateTriggerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_job_v1_job_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetJobTemplateTriggerRequest.ProtoReflect.Descriptor instead.
func (*GetJobTemplateTriggerRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_job_v1_job_proto_rawDescGZIP(), []int{17}
}

func (x *GetJobTemplateTriggerRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *GetJobTemplateTriggerRequest) GetJobTemplateName() string {
	if x != nil {
		return x.JobTemplateName
	}
	return ""
}

func (x *GetJobTemplateTriggerRequest) GetTriggerName() string {
	if x != nil {
		return x.TriggerName
	}
	return ""
}

type ListJobTemplateTriggersRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName   string `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	JobTemplateName string `protobuf:"bytes,2,opt,name=jobTemplateName,proto3" json:"jobTemplateName,omitempty"`
	TriggerName     string `protobuf:"bytes,3,opt,name=triggerName,proto3" json:"triggerName,omitempty"`
}

func (x *ListJobTemplateTriggersRequest) Reset() {
	*x = ListJobTemplateTriggersRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_job_v1_job_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListJobTemplateTriggersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListJobTemplateTriggersRequest) ProtoMessage() {}

func (x *ListJobTemplateTriggersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_job_v1_job_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListJobTemplateTriggersRequest.ProtoReflect.Descriptor instead.
func (*ListJobTemplateTriggersRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_job_v1_job_proto_rawDescGZIP(), []int{18}
}

func (x *ListJobTemplateTriggersRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *ListJobTemplateTriggersRequest) GetJobTemplateName() string {
	if x != nil {
		return x.JobTemplateName
	}
	return ""
}

func (x *ListJobTemplateTriggersRequest) GetTriggerName() string {
	if x != nil {
		return x.TriggerName
	}
	return ""
}

type ListJobTemplateTriggersResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Triggers []*Trigger `protobuf:"bytes,1,rep,name=triggers,proto3" json:"triggers,omitempty"`
	Total    int32      `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
}

func (x *ListJobTemplateTriggersResponse) Reset() {
	*x = ListJobTemplateTriggersResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_job_v1_job_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListJobTemplateTriggersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListJobTemplateTriggersResponse) ProtoMessage() {}

func (x *ListJobTemplateTriggersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_job_v1_job_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListJobTemplateTriggersResponse.ProtoReflect.Descriptor instead.
func (*ListJobTemplateTriggersResponse) Descriptor() ([]byte, []int) {
	return file_aistudio_job_v1_job_proto_rawDescGZIP(), []int{19}
}

func (x *ListJobTemplateTriggersResponse) GetTriggers() []*Trigger {
	if x != nil {
		return x.Triggers
	}
	return nil
}

func (x *ListJobTemplateTriggersResponse) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

type Job struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                        string                     `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"` //全局唯一的id
	Name                      string                     `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	DisplayName               string                     `protobuf:"bytes,3,opt,name=displayName,proto3" json:"displayName,omitempty"`
	Description               string                     `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	WorkspaceName             string                     `protobuf:"bytes,5,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Region                    string                     `protobuf:"bytes,6,opt,name=region,proto3" json:"region,omitempty"`
	Creator                   string                     `protobuf:"bytes,7,opt,name=creator,proto3" json:"creator,omitempty"`
	Revision                  string                     `protobuf:"bytes,8,opt,name=revision,proto3" json:"revision,omitempty"`
	WorkflowRevision          string                     `protobuf:"bytes,9,opt,name=workflowRevision,proto3" json:"workflowRevision,omitempty"`
	JobType                   JobType                    `protobuf:"varint,10,opt,name=jobType,proto3,enum=apis.aistudio.job.v1.JobType" json:"jobType,omitempty"`
	Members                   []string                   `protobuf:"bytes,11,rep,name=members,proto3" json:"members,omitempty"`
	JobStatus                 *JobStatus                 `protobuf:"bytes,12,opt,name=jobStatus,proto3" json:"jobStatus,omitempty"`
	QueueName                 string                     `protobuf:"bytes,13,opt,name=queueName,proto3" json:"queueName,omitempty"`
	PyTorchDDPJobTemplate     *PyTorchDDPJobTemplate     `protobuf:"bytes,14,opt,name=pyTorchDDPJobTemplate,proto3" json:"pyTorchDDPJobTemplate,omitempty"`
	DeepSpeedJobTemplate      *DeepSpeedJobTemplate      `protobuf:"bytes,15,opt,name=deepSpeedJobTemplate,proto3" json:"deepSpeedJobTemplate,omitempty"`
	SimpleTrainingJobTemplate *SimpleTrainingJobTemplate `protobuf:"bytes,16,opt,name=simpleTrainingJobTemplate,proto3" json:"simpleTrainingJobTemplate,omitempty"`
	CreateTime                string                     `protobuf:"bytes,17,opt,name=createTime,proto3" json:"createTime,omitempty"`
	UpdateTime                string                     `protobuf:"bytes,18,opt,name=updateTime,proto3" json:"updateTime,omitempty"`
	RestartPolicy             *RestartPolicy             `protobuf:"bytes,19,opt,name=restartPolicy,proto3" json:"restartPolicy,omitempty"`
	AlarmShielding            bool                       `protobuf:"varint,22,opt,name=alarmShielding,proto3" json:"alarmShielding,omitempty"` // 是否屏蔽告警
	Priority                  int32                      `protobuf:"varint,23,opt,name=priority,proto3" json:"priority,omitempty"`             // 优先级
	MaxWaitTime               int32                      `protobuf:"varint,24,opt,name=maxWaitTime,proto3" json:"maxWaitTime,omitempty"`       //最大等待时间
	Labels                    map[string]string          `protobuf:"bytes,25,rep,name=labels,proto3" json:"labels,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	JobTemplateName           string                     `protobuf:"bytes,27,opt,name=jobTemplateName,proto3" json:"jobTemplateName,omitempty"` // 模版名称, 为空表示没有模板。平台直接创建任务
}

func (x *Job) Reset() {
	*x = Job{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_job_v1_job_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Job) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Job) ProtoMessage() {}

func (x *Job) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_job_v1_job_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Job.ProtoReflect.Descriptor instead.
func (*Job) Descriptor() ([]byte, []int) {
	return file_aistudio_job_v1_job_proto_rawDescGZIP(), []int{20}
}

func (x *Job) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Job) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Job) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *Job) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Job) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *Job) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *Job) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *Job) GetRevision() string {
	if x != nil {
		return x.Revision
	}
	return ""
}

func (x *Job) GetWorkflowRevision() string {
	if x != nil {
		return x.WorkflowRevision
	}
	return ""
}

func (x *Job) GetJobType() JobType {
	if x != nil {
		return x.JobType
	}
	return JobType_PyTorchDDP
}

func (x *Job) GetMembers() []string {
	if x != nil {
		return x.Members
	}
	return nil
}

func (x *Job) GetJobStatus() *JobStatus {
	if x != nil {
		return x.JobStatus
	}
	return nil
}

func (x *Job) GetQueueName() string {
	if x != nil {
		return x.QueueName
	}
	return ""
}

func (x *Job) GetPyTorchDDPJobTemplate() *PyTorchDDPJobTemplate {
	if x != nil {
		return x.PyTorchDDPJobTemplate
	}
	return nil
}

func (x *Job) GetDeepSpeedJobTemplate() *DeepSpeedJobTemplate {
	if x != nil {
		return x.DeepSpeedJobTemplate
	}
	return nil
}

func (x *Job) GetSimpleTrainingJobTemplate() *SimpleTrainingJobTemplate {
	if x != nil {
		return x.SimpleTrainingJobTemplate
	}
	return nil
}

func (x *Job) GetCreateTime() string {
	if x != nil {
		return x.CreateTime
	}
	return ""
}

func (x *Job) GetUpdateTime() string {
	if x != nil {
		return x.UpdateTime
	}
	return ""
}

func (x *Job) GetRestartPolicy() *RestartPolicy {
	if x != nil {
		return x.RestartPolicy
	}
	return nil
}

func (x *Job) GetAlarmShielding() bool {
	if x != nil {
		return x.AlarmShielding
	}
	return false
}

func (x *Job) GetPriority() int32 {
	if x != nil {
		return x.Priority
	}
	return 0
}

func (x *Job) GetMaxWaitTime() int32 {
	if x != nil {
		return x.MaxWaitTime
	}
	return 0
}

func (x *Job) GetLabels() map[string]string {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *Job) GetJobTemplateName() string {
	if x != nil {
		return x.JobTemplateName
	}
	return ""
}

type RestartPolicy struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Enabled       bool  `protobuf:"varint,1,opt,name=enabled,proto3" json:"enabled,omitempty"`
	MaxRetryCount int32 `protobuf:"varint,2,opt,name=maxRetryCount,proto3" json:"maxRetryCount,omitempty"`
}

func (x *RestartPolicy) Reset() {
	*x = RestartPolicy{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_job_v1_job_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RestartPolicy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RestartPolicy) ProtoMessage() {}

func (x *RestartPolicy) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_job_v1_job_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RestartPolicy.ProtoReflect.Descriptor instead.
func (*RestartPolicy) Descriptor() ([]byte, []int) {
	return file_aistudio_job_v1_job_proto_rawDescGZIP(), []int{21}
}

func (x *RestartPolicy) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

func (x *RestartPolicy) GetMaxRetryCount() int32 {
	if x != nil {
		return x.MaxRetryCount
	}
	return 0
}

type DatasetVolume struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name       string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	MountPoint string `protobuf:"bytes,2,opt,name=mountPoint,proto3" json:"mountPoint,omitempty"`
}

func (x *DatasetVolume) Reset() {
	*x = DatasetVolume{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_job_v1_job_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DatasetVolume) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DatasetVolume) ProtoMessage() {}

func (x *DatasetVolume) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_job_v1_job_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DatasetVolume.ProtoReflect.Descriptor instead.
func (*DatasetVolume) Descriptor() ([]byte, []int) {
	return file_aistudio_job_v1_job_proto_rawDescGZIP(), []int{22}
}

func (x *DatasetVolume) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DatasetVolume) GetMountPoint() string {
	if x != nil {
		return x.MountPoint
	}
	return ""
}

type CreateJobRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name                      string                     `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	DisplayName               string                     `protobuf:"bytes,2,opt,name=displayName,proto3" json:"displayName,omitempty"`
	Description               string                     `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	WorkspaceName             string                     `protobuf:"bytes,4,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Region                    string                     `protobuf:"bytes,5,opt,name=region,proto3" json:"region,omitempty"`
	Labels                    map[string]string          `protobuf:"bytes,6,rep,name=labels,proto3" json:"labels,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	JobType                   JobType                    `protobuf:"varint,7,opt,name=jobType,proto3,enum=apis.aistudio.job.v1.JobType" json:"jobType,omitempty"`
	QueueName                 string                     `protobuf:"bytes,8,opt,name=queueName,proto3" json:"queueName,omitempty"`                                   // 运行队列
	Priority                  Priority                   `protobuf:"varint,9,opt,name=priority,proto3,enum=apis.aistudio.job.v1.Priority" json:"priority,omitempty"` // 优先级
	Members                   []string                   `protobuf:"bytes,10,rep,name=members,proto3" json:"members,omitempty"`
	PyTorchDDPJobTemplate     *PyTorchDDPJobTemplate     `protobuf:"bytes,11,opt,name=pyTorchDDPJobTemplate,proto3" json:"pyTorchDDPJobTemplate,omitempty"`
	DeepSpeedJobTemplate      *DeepSpeedJobTemplate      `protobuf:"bytes,12,opt,name=deepSpeedJobTemplate,proto3" json:"deepSpeedJobTemplate,omitempty"`
	SimpleTrainingJobTemplate *SimpleTrainingJobTemplate `protobuf:"bytes,13,opt,name=simpleTrainingJobTemplate,proto3" json:"simpleTrainingJobTemplate,omitempty"`
	MaxWaitTime               int32                      `protobuf:"varint,14,opt,name=maxWaitTime,proto3" json:"maxWaitTime,omitempty"` //最大等待时间
	// google.protobuf.Duration maxWaitTime = 14; //最大等待时间
	RestartPolicy   *RestartPolicy `protobuf:"bytes,15,opt,name=restartPolicy,proto3" json:"restartPolicy,omitempty"`
	AlarmShielding  bool           `protobuf:"varint,16,opt,name=alarmShielding,proto3" json:"alarmShielding,omitempty"`  // 是否屏蔽告警
	JobTemplateName string         `protobuf:"bytes,18,opt,name=jobTemplateName,proto3" json:"jobTemplateName,omitempty"` // 模版名称, 为空表示没有模板。平台直接创建任务
	HadoopEnabled   bool           `protobuf:"varint,19,opt,name=hadoopEnabled,proto3" json:"hadoopEnabled,omitempty"`    // 是否开启hadoop
	HadoopUsers     []string       `protobuf:"bytes,20,rep,name=hadoopUsers,proto3" json:"hadoopUsers,omitempty"`         // hadoop用户列表
}

func (x *CreateJobRequest) Reset() {
	*x = CreateJobRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_job_v1_job_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateJobRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateJobRequest) ProtoMessage() {}

func (x *CreateJobRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_job_v1_job_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateJobRequest.ProtoReflect.Descriptor instead.
func (*CreateJobRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_job_v1_job_proto_rawDescGZIP(), []int{23}
}

func (x *CreateJobRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateJobRequest) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *CreateJobRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CreateJobRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *CreateJobRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *CreateJobRequest) GetLabels() map[string]string {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *CreateJobRequest) GetJobType() JobType {
	if x != nil {
		return x.JobType
	}
	return JobType_PyTorchDDP
}

func (x *CreateJobRequest) GetQueueName() string {
	if x != nil {
		return x.QueueName
	}
	return ""
}

func (x *CreateJobRequest) GetPriority() Priority {
	if x != nil {
		return x.Priority
	}
	return Priority_LOW
}

func (x *CreateJobRequest) GetMembers() []string {
	if x != nil {
		return x.Members
	}
	return nil
}

func (x *CreateJobRequest) GetPyTorchDDPJobTemplate() *PyTorchDDPJobTemplate {
	if x != nil {
		return x.PyTorchDDPJobTemplate
	}
	return nil
}

func (x *CreateJobRequest) GetDeepSpeedJobTemplate() *DeepSpeedJobTemplate {
	if x != nil {
		return x.DeepSpeedJobTemplate
	}
	return nil
}

func (x *CreateJobRequest) GetSimpleTrainingJobTemplate() *SimpleTrainingJobTemplate {
	if x != nil {
		return x.SimpleTrainingJobTemplate
	}
	return nil
}

func (x *CreateJobRequest) GetMaxWaitTime() int32 {
	if x != nil {
		return x.MaxWaitTime
	}
	return 0
}

func (x *CreateJobRequest) GetRestartPolicy() *RestartPolicy {
	if x != nil {
		return x.RestartPolicy
	}
	return nil
}

func (x *CreateJobRequest) GetAlarmShielding() bool {
	if x != nil {
		return x.AlarmShielding
	}
	return false
}

func (x *CreateJobRequest) GetJobTemplateName() string {
	if x != nil {
		return x.JobTemplateName
	}
	return ""
}

func (x *CreateJobRequest) GetHadoopEnabled() bool {
	if x != nil {
		return x.HadoopEnabled
	}
	return false
}

func (x *CreateJobRequest) GetHadoopUsers() []string {
	if x != nil {
		return x.HadoopUsers
	}
	return nil
}

type CreateJobResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	JobName string `protobuf:"bytes,1,opt,name=jobName,proto3" json:"jobName,omitempty"`
}

func (x *CreateJobResponse) Reset() {
	*x = CreateJobResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_job_v1_job_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateJobResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateJobResponse) ProtoMessage() {}

func (x *CreateJobResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_job_v1_job_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateJobResponse.ProtoReflect.Descriptor instead.
func (*CreateJobResponse) Descriptor() ([]byte, []int) {
	return file_aistudio_job_v1_job_proto_rawDescGZIP(), []int{24}
}

func (x *CreateJobResponse) GetJobName() string {
	if x != nil {
		return x.JobName
	}
	return ""
}

type UpdateJobRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	JobName       string   `protobuf:"bytes,1,opt,name=jobName,proto3" json:"jobName,omitempty"`
	DisplayName   string   `protobuf:"bytes,2,opt,name=displayName,proto3" json:"displayName,omitempty"`
	Description   string   `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	WorkspaceName string   `protobuf:"bytes,4,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	Creator       string   `protobuf:"bytes,6,opt,name=creator,proto3" json:"creator,omitempty"`
	Members       []string `protobuf:"bytes,7,rep,name=members,proto3" json:"members,omitempty"`
	Priority      string   `protobuf:"bytes,8,opt,name=priority,proto3" json:"priority,omitempty"`
}

func (x *UpdateJobRequest) Reset() {
	*x = UpdateJobRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_job_v1_job_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateJobRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateJobRequest) ProtoMessage() {}

func (x *UpdateJobRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_job_v1_job_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateJobRequest.ProtoReflect.Descriptor instead.
func (*UpdateJobRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_job_v1_job_proto_rawDescGZIP(), []int{25}
}

func (x *UpdateJobRequest) GetJobName() string {
	if x != nil {
		return x.JobName
	}
	return ""
}

func (x *UpdateJobRequest) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *UpdateJobRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *UpdateJobRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *UpdateJobRequest) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *UpdateJobRequest) GetMembers() []string {
	if x != nil {
		return x.Members
	}
	return nil
}

func (x *UpdateJobRequest) GetPriority() string {
	if x != nil {
		return x.Priority
	}
	return ""
}

type CreateTensorboardRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName string `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	JobName       string `protobuf:"bytes,2,opt,name=jobName,proto3" json:"jobName,omitempty"`
}

func (x *CreateTensorboardRequest) Reset() {
	*x = CreateTensorboardRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_job_v1_job_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateTensorboardRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateTensorboardRequest) ProtoMessage() {}

func (x *CreateTensorboardRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_job_v1_job_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateTensorboardRequest.ProtoReflect.Descriptor instead.
func (*CreateTensorboardRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_job_v1_job_proto_rawDescGZIP(), []int{26}
}

func (x *CreateTensorboardRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *CreateTensorboardRequest) GetJobName() string {
	if x != nil {
		return x.JobName
	}
	return ""
}

type GetTensorboardRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName string `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	JobName       string `protobuf:"bytes,2,opt,name=jobName,proto3" json:"jobName,omitempty"`
}

func (x *GetTensorboardRequest) Reset() {
	*x = GetTensorboardRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_job_v1_job_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTensorboardRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTensorboardRequest) ProtoMessage() {}

func (x *GetTensorboardRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_job_v1_job_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTensorboardRequest.ProtoReflect.Descriptor instead.
func (*GetTensorboardRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_job_v1_job_proto_rawDescGZIP(), []int{27}
}

func (x *GetTensorboardRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *GetTensorboardRequest) GetJobName() string {
	if x != nil {
		return x.JobName
	}
	return ""
}

type GetTensorboardResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url   string `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	State string `protobuf:"bytes,2,opt,name=state,proto3" json:"state,omitempty"`
}

func (x *GetTensorboardResponse) Reset() {
	*x = GetTensorboardResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_job_v1_job_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTensorboardResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTensorboardResponse) ProtoMessage() {}

func (x *GetTensorboardResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_job_v1_job_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTensorboardResponse.ProtoReflect.Descriptor instead.
func (*GetTensorboardResponse) Descriptor() ([]byte, []int) {
	return file_aistudio_job_v1_job_proto_rawDescGZIP(), []int{28}
}

func (x *GetTensorboardResponse) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *GetTensorboardResponse) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

// PyTorch 分布式训练配置
type PyTorchDDPJobTemplate struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TensorboardEnabled bool                 `protobuf:"varint,1,opt,name=tensorboardEnabled,proto3" json:"tensorboardEnabled,omitempty"`
	VolumeSpecs        []*common.VolumeSpec `protobuf:"bytes,3,rep,name=volumeSpecs,proto3" json:"volumeSpecs,omitempty"` //卷, 可支持直接挂卷,可以写训练的模型
	SourceCode         *SourceCode          `protobuf:"bytes,4,opt,name=sourceCode,proto3" json:"sourceCode,omitempty"`
	EnvVars            []*common.EnvVar     `protobuf:"bytes,5,rep,name=envVars,proto3" json:"envVars,omitempty"`          //环境变量
	Command            string               `protobuf:"bytes,6,opt,name=command,proto3" json:"command,omitempty"`          //执行命令
	Image              string               `protobuf:"bytes,7,opt,name=image,proto3" json:"image,omitempty"`              //镜像
	Master             *TaskSpec            `protobuf:"bytes,8,opt,name=master,proto3" json:"master,omitempty"`            //master
	Worker             *TaskSpec            `protobuf:"bytes,9,opt,name=worker,proto3" json:"worker,omitempty"`            //worker
	ConfigSpecs        []*v1.ConfigSpec     `protobuf:"bytes,10,rep,name=configSpecs,proto3" json:"configSpecs,omitempty"` //文件配置
}

func (x *PyTorchDDPJobTemplate) Reset() {
	*x = PyTorchDDPJobTemplate{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_job_v1_job_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PyTorchDDPJobTemplate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PyTorchDDPJobTemplate) ProtoMessage() {}

func (x *PyTorchDDPJobTemplate) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_job_v1_job_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PyTorchDDPJobTemplate.ProtoReflect.Descriptor instead.
func (*PyTorchDDPJobTemplate) Descriptor() ([]byte, []int) {
	return file_aistudio_job_v1_job_proto_rawDescGZIP(), []int{29}
}

func (x *PyTorchDDPJobTemplate) GetTensorboardEnabled() bool {
	if x != nil {
		return x.TensorboardEnabled
	}
	return false
}

func (x *PyTorchDDPJobTemplate) GetVolumeSpecs() []*common.VolumeSpec {
	if x != nil {
		return x.VolumeSpecs
	}
	return nil
}

func (x *PyTorchDDPJobTemplate) GetSourceCode() *SourceCode {
	if x != nil {
		return x.SourceCode
	}
	return nil
}

func (x *PyTorchDDPJobTemplate) GetEnvVars() []*common.EnvVar {
	if x != nil {
		return x.EnvVars
	}
	return nil
}

func (x *PyTorchDDPJobTemplate) GetCommand() string {
	if x != nil {
		return x.Command
	}
	return ""
}

func (x *PyTorchDDPJobTemplate) GetImage() string {
	if x != nil {
		return x.Image
	}
	return ""
}

func (x *PyTorchDDPJobTemplate) GetMaster() *TaskSpec {
	if x != nil {
		return x.Master
	}
	return nil
}

func (x *PyTorchDDPJobTemplate) GetWorker() *TaskSpec {
	if x != nil {
		return x.Worker
	}
	return nil
}

func (x *PyTorchDDPJobTemplate) GetConfigSpecs() []*v1.ConfigSpec {
	if x != nil {
		return x.ConfigSpecs
	}
	return nil
}

// DeepSpeed 分布式训练配置
type DeepSpeedJobTemplate struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TensorboardEnabled bool                 `protobuf:"varint,1,opt,name=tensorboardEnabled,proto3" json:"tensorboardEnabled,omitempty"`
	VolumeSpecs        []*common.VolumeSpec `protobuf:"bytes,3,rep,name=volumeSpecs,proto3" json:"volumeSpecs,omitempty"` //卷, 可支持直接挂卷,可以写训练的模型
	SourceCode         *SourceCode          `protobuf:"bytes,4,opt,name=sourceCode,proto3" json:"sourceCode,omitempty"`
	EnvVars            []*common.EnvVar     `protobuf:"bytes,5,rep,name=envVars,proto3" json:"envVars,omitempty"` //环境变量
	Command            string               `protobuf:"bytes,6,opt,name=command,proto3" json:"command,omitempty"` //执行命令
	Image              string               `protobuf:"bytes,7,opt,name=image,proto3" json:"image,omitempty"`     //镜像
	Master             *TaskSpec            `protobuf:"bytes,8,opt,name=master,proto3" json:"master,omitempty"`
	Worker             *TaskSpec            `protobuf:"bytes,9,opt,name=worker,proto3" json:"worker,omitempty"`
	ConfigSpecs        []*v1.ConfigSpec     `protobuf:"bytes,10,rep,name=configSpecs,proto3" json:"configSpecs,omitempty"` //文件配置
}

func (x *DeepSpeedJobTemplate) Reset() {
	*x = DeepSpeedJobTemplate{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_job_v1_job_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeepSpeedJobTemplate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeepSpeedJobTemplate) ProtoMessage() {}

func (x *DeepSpeedJobTemplate) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_job_v1_job_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeepSpeedJobTemplate.ProtoReflect.Descriptor instead.
func (*DeepSpeedJobTemplate) Descriptor() ([]byte, []int) {
	return file_aistudio_job_v1_job_proto_rawDescGZIP(), []int{30}
}

func (x *DeepSpeedJobTemplate) GetTensorboardEnabled() bool {
	if x != nil {
		return x.TensorboardEnabled
	}
	return false
}

func (x *DeepSpeedJobTemplate) GetVolumeSpecs() []*common.VolumeSpec {
	if x != nil {
		return x.VolumeSpecs
	}
	return nil
}

func (x *DeepSpeedJobTemplate) GetSourceCode() *SourceCode {
	if x != nil {
		return x.SourceCode
	}
	return nil
}

func (x *DeepSpeedJobTemplate) GetEnvVars() []*common.EnvVar {
	if x != nil {
		return x.EnvVars
	}
	return nil
}

func (x *DeepSpeedJobTemplate) GetCommand() string {
	if x != nil {
		return x.Command
	}
	return ""
}

func (x *DeepSpeedJobTemplate) GetImage() string {
	if x != nil {
		return x.Image
	}
	return ""
}

func (x *DeepSpeedJobTemplate) GetMaster() *TaskSpec {
	if x != nil {
		return x.Master
	}
	return nil
}

func (x *DeepSpeedJobTemplate) GetWorker() *TaskSpec {
	if x != nil {
		return x.Worker
	}
	return nil
}

func (x *DeepSpeedJobTemplate) GetConfigSpecs() []*v1.ConfigSpec {
	if x != nil {
		return x.ConfigSpecs
	}
	return nil
}

type SimpleTrainingJobTemplate struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TensorboardEnabled    bool                  `protobuf:"varint,1,opt,name=tensorboardEnabled,proto3" json:"tensorboardEnabled,omitempty"`
	SourceCode            *SourceCode           `protobuf:"bytes,2,opt,name=sourceCode,proto3" json:"sourceCode,omitempty"`
	Command               string                `protobuf:"bytes,3,opt,name=command,proto3" json:"command,omitempty"`                             //执行命令
	Image                 string                `protobuf:"bytes,4,opt,name=image,proto3" json:"image,omitempty"`                                 //镜像
	EnvVars               []*common.EnvVar      `protobuf:"bytes,5,rep,name=envVars,proto3" json:"envVars,omitempty"`                             //环境变量
	VolumeSpecs           []*common.VolumeSpec  `protobuf:"bytes,6,rep,name=volumeSpecs,proto3" json:"volumeSpecs,omitempty"`                     //卷, 可支持直接挂卷,可以写训练的模型
	Specification         *common.Specification `protobuf:"bytes,7,opt,name=specification,proto3" json:"specification,omitempty"`                 //规格
	NodeSpecificationName string                `protobuf:"bytes,8,opt,name=nodeSpecificationName,proto3" json:"nodeSpecificationName,omitempty"` //节点规格
	ConfigSpecs           []*v1.ConfigSpec      `protobuf:"bytes,9,rep,name=configSpecs,proto3" json:"configSpecs,omitempty"`                     //文件配置
}

func (x *SimpleTrainingJobTemplate) Reset() {
	*x = SimpleTrainingJobTemplate{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_job_v1_job_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SimpleTrainingJobTemplate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SimpleTrainingJobTemplate) ProtoMessage() {}

func (x *SimpleTrainingJobTemplate) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_job_v1_job_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SimpleTrainingJobTemplate.ProtoReflect.Descriptor instead.
func (*SimpleTrainingJobTemplate) Descriptor() ([]byte, []int) {
	return file_aistudio_job_v1_job_proto_rawDescGZIP(), []int{31}
}

func (x *SimpleTrainingJobTemplate) GetTensorboardEnabled() bool {
	if x != nil {
		return x.TensorboardEnabled
	}
	return false
}

func (x *SimpleTrainingJobTemplate) GetSourceCode() *SourceCode {
	if x != nil {
		return x.SourceCode
	}
	return nil
}

func (x *SimpleTrainingJobTemplate) GetCommand() string {
	if x != nil {
		return x.Command
	}
	return ""
}

func (x *SimpleTrainingJobTemplate) GetImage() string {
	if x != nil {
		return x.Image
	}
	return ""
}

func (x *SimpleTrainingJobTemplate) GetEnvVars() []*common.EnvVar {
	if x != nil {
		return x.EnvVars
	}
	return nil
}

func (x *SimpleTrainingJobTemplate) GetVolumeSpecs() []*common.VolumeSpec {
	if x != nil {
		return x.VolumeSpecs
	}
	return nil
}

func (x *SimpleTrainingJobTemplate) GetSpecification() *common.Specification {
	if x != nil {
		return x.Specification
	}
	return nil
}

func (x *SimpleTrainingJobTemplate) GetNodeSpecificationName() string {
	if x != nil {
		return x.NodeSpecificationName
	}
	return ""
}

func (x *SimpleTrainingJobTemplate) GetConfigSpecs() []*v1.ConfigSpec {
	if x != nil {
		return x.ConfigSpecs
	}
	return nil
}

type TaskSpec struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name                  string                `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`                                   // master,worker
	Replicas              int32                 `protobuf:"varint,2,opt,name=replicas,proto3" json:"replicas,omitempty"`                          //副本数
	Command               string                `protobuf:"bytes,3,opt,name=command,proto3" json:"command,omitempty"`                             //执行命令
	Image                 string                `protobuf:"bytes,4,opt,name=image,proto3" json:"image,omitempty"`                                 //镜像
	EnvVars               []*common.EnvVar      `protobuf:"bytes,5,rep,name=envVars,proto3" json:"envVars,omitempty"`                             //环境变量
	VolumeSpecs           []*common.VolumeSpec  `protobuf:"bytes,6,rep,name=volumeSpecs,proto3" json:"volumeSpecs,omitempty"`                     //卷, 可支持直接挂卷,可以写训练的模型
	Specification         *common.Specification `protobuf:"bytes,7,opt,name=specification,proto3" json:"specification,omitempty"`                 //规格
	NodeSpecificationName string                `protobuf:"bytes,8,opt,name=nodeSpecificationName,proto3" json:"nodeSpecificationName,omitempty"` //节点规格, 可以设置多个规格
	Slots                 int32                 `protobuf:"varint,9,opt,name=slots,proto3" json:"slots,omitempty"`                                //占用的slot数
	ConfigSpecs           []*v1.ConfigSpec      `protobuf:"bytes,10,rep,name=configSpecs,proto3" json:"configSpecs,omitempty"`                    //文件配置
}

func (x *TaskSpec) Reset() {
	*x = TaskSpec{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_job_v1_job_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskSpec) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskSpec) ProtoMessage() {}

func (x *TaskSpec) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_job_v1_job_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskSpec.ProtoReflect.Descriptor instead.
func (*TaskSpec) Descriptor() ([]byte, []int) {
	return file_aistudio_job_v1_job_proto_rawDescGZIP(), []int{32}
}

func (x *TaskSpec) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *TaskSpec) GetReplicas() int32 {
	if x != nil {
		return x.Replicas
	}
	return 0
}

func (x *TaskSpec) GetCommand() string {
	if x != nil {
		return x.Command
	}
	return ""
}

func (x *TaskSpec) GetImage() string {
	if x != nil {
		return x.Image
	}
	return ""
}

func (x *TaskSpec) GetEnvVars() []*common.EnvVar {
	if x != nil {
		return x.EnvVars
	}
	return nil
}

func (x *TaskSpec) GetVolumeSpecs() []*common.VolumeSpec {
	if x != nil {
		return x.VolumeSpecs
	}
	return nil
}

func (x *TaskSpec) GetSpecification() *common.Specification {
	if x != nil {
		return x.Specification
	}
	return nil
}

func (x *TaskSpec) GetNodeSpecificationName() string {
	if x != nil {
		return x.NodeSpecificationName
	}
	return ""
}

func (x *TaskSpec) GetSlots() int32 {
	if x != nil {
		return x.Slots
	}
	return 0
}

func (x *TaskSpec) GetConfigSpecs() []*v1.ConfigSpec {
	if x != nil {
		return x.ConfigSpecs
	}
	return nil
}

type SourceCode struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Source:
	//
	//	*SourceCode_CloudFsSource_
	//	*SourceCode_GitSource_
	//	*SourceCode_CubeFsSource_
	Source     isSourceCode_Source `protobuf_oneof:"source"`
	MountPoint string              `protobuf:"bytes,4,opt,name=mountPoint,proto3" json:"mountPoint,omitempty"` //挂载点
}

func (x *SourceCode) Reset() {
	*x = SourceCode{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_job_v1_job_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SourceCode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SourceCode) ProtoMessage() {}

func (x *SourceCode) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_job_v1_job_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SourceCode.ProtoReflect.Descriptor instead.
func (*SourceCode) Descriptor() ([]byte, []int) {
	return file_aistudio_job_v1_job_proto_rawDescGZIP(), []int{33}
}

func (m *SourceCode) GetSource() isSourceCode_Source {
	if m != nil {
		return m.Source
	}
	return nil
}

func (x *SourceCode) GetCloudFsSource() *SourceCode_CloudFsSource {
	if x, ok := x.GetSource().(*SourceCode_CloudFsSource_); ok {
		return x.CloudFsSource
	}
	return nil
}

func (x *SourceCode) GetGitSource() *SourceCode_GitSource {
	if x, ok := x.GetSource().(*SourceCode_GitSource_); ok {
		return x.GitSource
	}
	return nil
}

func (x *SourceCode) GetCubeFsSource() *SourceCode_CubeFsSource {
	if x, ok := x.GetSource().(*SourceCode_CubeFsSource_); ok {
		return x.CubeFsSource
	}
	return nil
}

func (x *SourceCode) GetMountPoint() string {
	if x != nil {
		return x.MountPoint
	}
	return ""
}

type isSourceCode_Source interface {
	isSourceCode_Source()
}

type SourceCode_CloudFsSource_ struct {
	CloudFsSource *SourceCode_CloudFsSource `protobuf:"bytes,1,opt,name=cloudFsSource,proto3,oneof"`
}

type SourceCode_GitSource_ struct {
	GitSource *SourceCode_GitSource `protobuf:"bytes,2,opt,name=gitSource,proto3,oneof"`
}

type SourceCode_CubeFsSource_ struct {
	CubeFsSource *SourceCode_CubeFsSource `protobuf:"bytes,3,opt,name=cubeFsSource,proto3,oneof"`
}

func (*SourceCode_CloudFsSource_) isSourceCode_Source() {}

func (*SourceCode_GitSource_) isSourceCode_Source() {}

func (*SourceCode_CubeFsSource_) isSourceCode_Source() {}

// vcjob status + other status
type JobStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	State              string          `protobuf:"bytes,1,opt,name=state,proto3" json:"state,omitempty"`
	Pending            int32           `protobuf:"varint,2,opt,name=pending,proto3" json:"pending,omitempty"`                       //the number of pending pods
	Running            int32           `protobuf:"varint,3,opt,name=running,proto3" json:"running,omitempty"`                       //the number of running pods
	Succeeded          int32           `protobuf:"varint,4,opt,name=succeeded,proto3" json:"succeeded,omitempty"`                   //the number of succeeded pods
	Failed             int32           `protobuf:"varint,5,opt,name=failed,proto3" json:"failed,omitempty"`                         //the number of failed pods
	Terminating        int32           `protobuf:"varint,6,opt,name=terminating,proto3" json:"terminating,omitempty"`               //the number of terminating pods
	Unknown            int32           `protobuf:"varint,7,opt,name=unknown,proto3" json:"unknown,omitempty"`                       //the number of unknown pods
	Version            int32           `protobuf:"varint,8,opt,name=version,proto3" json:"version,omitempty"`                       //the version of the job
	RetryCount         int32           `protobuf:"varint,9,opt,name=retryCount,proto3" json:"retryCount,omitempty"`                 //the retry count of the job
	Reason             string          `protobuf:"bytes,10,opt,name=reason,proto3" json:"reason,omitempty"`                         //the reason of the job
	Message            string          `protobuf:"bytes,11,opt,name=message,proto3" json:"message,omitempty"`                       //the message of the job
	LastTransitionTime string          `protobuf:"bytes,12,opt,name=lastTransitionTime,proto3" json:"lastTransitionTime,omitempty"` //最后一次状态变更时间
	CreateTimestamp    string          `protobuf:"bytes,13,opt,name=createTimestamp,proto3" json:"createTimestamp,omitempty"`       //创建时间
	Conditions         []*JobCondition `protobuf:"bytes,14,rep,name=conditions,proto3" json:"conditions,omitempty"`                 //the conditions of the job
	RunningDuration    string          `protobuf:"bytes,15,opt,name=runningDuration,proto3" json:"runningDuration,omitempty"`       //运行时长
	ClusterName        string          `protobuf:"bytes,16,opt,name=clusterName,proto3" json:"clusterName,omitempty"`               //集群名称
}

func (x *JobStatus) Reset() {
	*x = JobStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_job_v1_job_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *JobStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JobStatus) ProtoMessage() {}

func (x *JobStatus) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_job_v1_job_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JobStatus.ProtoReflect.Descriptor instead.
func (*JobStatus) Descriptor() ([]byte, []int) {
	return file_aistudio_job_v1_job_proto_rawDescGZIP(), []int{34}
}

func (x *JobStatus) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

func (x *JobStatus) GetPending() int32 {
	if x != nil {
		return x.Pending
	}
	return 0
}

func (x *JobStatus) GetRunning() int32 {
	if x != nil {
		return x.Running
	}
	return 0
}

func (x *JobStatus) GetSucceeded() int32 {
	if x != nil {
		return x.Succeeded
	}
	return 0
}

func (x *JobStatus) GetFailed() int32 {
	if x != nil {
		return x.Failed
	}
	return 0
}

func (x *JobStatus) GetTerminating() int32 {
	if x != nil {
		return x.Terminating
	}
	return 0
}

func (x *JobStatus) GetUnknown() int32 {
	if x != nil {
		return x.Unknown
	}
	return 0
}

func (x *JobStatus) GetVersion() int32 {
	if x != nil {
		return x.Version
	}
	return 0
}

func (x *JobStatus) GetRetryCount() int32 {
	if x != nil {
		return x.RetryCount
	}
	return 0
}

func (x *JobStatus) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *JobStatus) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *JobStatus) GetLastTransitionTime() string {
	if x != nil {
		return x.LastTransitionTime
	}
	return ""
}

func (x *JobStatus) GetCreateTimestamp() string {
	if x != nil {
		return x.CreateTimestamp
	}
	return ""
}

func (x *JobStatus) GetConditions() []*JobCondition {
	if x != nil {
		return x.Conditions
	}
	return nil
}

func (x *JobStatus) GetRunningDuration() string {
	if x != nil {
		return x.RunningDuration
	}
	return ""
}

func (x *JobStatus) GetClusterName() string {
	if x != nil {
		return x.ClusterName
	}
	return ""
}

type ListJobTaskStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskStatus []*TaskStatus `protobuf:"bytes,1,rep,name=taskStatus,proto3" json:"taskStatus,omitempty"`
}

func (x *ListJobTaskStatus) Reset() {
	*x = ListJobTaskStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_job_v1_job_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListJobTaskStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListJobTaskStatus) ProtoMessage() {}

func (x *ListJobTaskStatus) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_job_v1_job_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListJobTaskStatus.ProtoReflect.Descriptor instead.
func (*ListJobTaskStatus) Descriptor() ([]byte, []int) {
	return file_aistudio_job_v1_job_proto_rawDescGZIP(), []int{35}
}

func (x *ListJobTaskStatus) GetTaskStatus() []*TaskStatus {
	if x != nil {
		return x.TaskStatus
	}
	return nil
}

type TaskStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Phase                       string           `protobuf:"bytes,1,opt,name=phase,proto3" json:"phase,omitempty"`
	PodName                     string           `protobuf:"bytes,2,opt,name=podName,proto3" json:"podName,omitempty"`
	PodNamespace                string           `protobuf:"bytes,3,opt,name=podNamespace,proto3" json:"podNamespace,omitempty"`
	PodIP                       string           `protobuf:"bytes,4,opt,name=podIP,proto3" json:"podIP,omitempty"`
	Cluster                     string           `protobuf:"bytes,5,opt,name=cluster,proto3" json:"cluster,omitempty"` //容器集群
	Region                      string           `protobuf:"bytes,6,opt,name=region,proto3" json:"region,omitempty"`   // 地区
	Zone                        string           `protobuf:"bytes,7,opt,name=zone,proto3" json:"zone,omitempty"`       //分区
	NodeIP                      string           `protobuf:"bytes,8,opt,name=nodeIP,proto3" json:"nodeIP,omitempty"`
	CreateTimestamp             string           `protobuf:"bytes,9,opt,name=createTimestamp,proto3" json:"createTimestamp,omitempty"`  //创建时间
	FinishTimestamp             string           `protobuf:"bytes,10,opt,name=finishTimestamp,proto3" json:"finishTimestamp,omitempty"` //开始时间
	Image                       string           `protobuf:"bytes,11,opt,name=image,proto3" json:"image,omitempty"`
	Conditions                  []*TaskCondition `protobuf:"bytes,12,rep,name=conditions,proto3" json:"conditions,omitempty"`      //任务状态
	RestartCount                int32            `protobuf:"varint,13,opt,name=restartCount,proto3" json:"restartCount,omitempty"` //重启次数
	Reason                      string           `protobuf:"bytes,14,opt,name=reason,proto3" json:"reason,omitempty"`
	Message                     string           `protobuf:"bytes,15,opt,name=message,proto3" json:"message,omitempty"`
	TaskName                    string           `protobuf:"bytes,16,opt,name=taskName,proto3" json:"taskName,omitempty"`
	TaskEnv                     string           `protobuf:"bytes,17,opt,name=taskEnv,proto3" json:"taskEnv,omitempty"`
	HostIP                      string           `protobuf:"bytes,18,opt,name=hostIP,proto3" json:"hostIP,omitempty"`
	TaskID                      string           `protobuf:"bytes,19,opt,name=taskID,proto3" json:"taskID,omitempty"`
	JobVersion                  string           `protobuf:"bytes,20,opt,name=jobVersion,proto3" json:"jobVersion,omitempty"`
	HistoricalLogStartTimestamp int64            `protobuf:"varint,21,opt,name=historicalLogStartTimestamp,proto3" json:"historicalLogStartTimestamp,omitempty"`
	HistoricalLogEndTimestamp   int64            `protobuf:"varint,22,opt,name=historicalLogEndTimestamp,proto3" json:"historicalLogEndTimestamp,omitempty"`
}

func (x *TaskStatus) Reset() {
	*x = TaskStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_job_v1_job_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskStatus) ProtoMessage() {}

func (x *TaskStatus) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_job_v1_job_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskStatus.ProtoReflect.Descriptor instead.
func (*TaskStatus) Descriptor() ([]byte, []int) {
	return file_aistudio_job_v1_job_proto_rawDescGZIP(), []int{36}
}

func (x *TaskStatus) GetPhase() string {
	if x != nil {
		return x.Phase
	}
	return ""
}

func (x *TaskStatus) GetPodName() string {
	if x != nil {
		return x.PodName
	}
	return ""
}

func (x *TaskStatus) GetPodNamespace() string {
	if x != nil {
		return x.PodNamespace
	}
	return ""
}

func (x *TaskStatus) GetPodIP() string {
	if x != nil {
		return x.PodIP
	}
	return ""
}

func (x *TaskStatus) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

func (x *TaskStatus) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *TaskStatus) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *TaskStatus) GetNodeIP() string {
	if x != nil {
		return x.NodeIP
	}
	return ""
}

func (x *TaskStatus) GetCreateTimestamp() string {
	if x != nil {
		return x.CreateTimestamp
	}
	return ""
}

func (x *TaskStatus) GetFinishTimestamp() string {
	if x != nil {
		return x.FinishTimestamp
	}
	return ""
}

func (x *TaskStatus) GetImage() string {
	if x != nil {
		return x.Image
	}
	return ""
}

func (x *TaskStatus) GetConditions() []*TaskCondition {
	if x != nil {
		return x.Conditions
	}
	return nil
}

func (x *TaskStatus) GetRestartCount() int32 {
	if x != nil {
		return x.RestartCount
	}
	return 0
}

func (x *TaskStatus) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *TaskStatus) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *TaskStatus) GetTaskName() string {
	if x != nil {
		return x.TaskName
	}
	return ""
}

func (x *TaskStatus) GetTaskEnv() string {
	if x != nil {
		return x.TaskEnv
	}
	return ""
}

func (x *TaskStatus) GetHostIP() string {
	if x != nil {
		return x.HostIP
	}
	return ""
}

func (x *TaskStatus) GetTaskID() string {
	if x != nil {
		return x.TaskID
	}
	return ""
}

func (x *TaskStatus) GetJobVersion() string {
	if x != nil {
		return x.JobVersion
	}
	return ""
}

func (x *TaskStatus) GetHistoricalLogStartTimestamp() int64 {
	if x != nil {
		return x.HistoricalLogStartTimestamp
	}
	return 0
}

func (x *TaskStatus) GetHistoricalLogEndTimestamp() int64 {
	if x != nil {
		return x.HistoricalLogEndTimestamp
	}
	return 0
}

type GetJobIDRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName string `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	JobName       string `protobuf:"bytes,2,opt,name=jobName,proto3" json:"jobName,omitempty"`
}

func (x *GetJobIDRequest) Reset() {
	*x = GetJobIDRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_job_v1_job_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetJobIDRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetJobIDRequest) ProtoMessage() {}

func (x *GetJobIDRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_job_v1_job_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetJobIDRequest.ProtoReflect.Descriptor instead.
func (*GetJobIDRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_job_v1_job_proto_rawDescGZIP(), []int{37}
}

func (x *GetJobIDRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *GetJobIDRequest) GetJobName() string {
	if x != nil {
		return x.JobName
	}
	return ""
}

type GetJobIDResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	JobID string `protobuf:"bytes,1,opt,name=jobID,proto3" json:"jobID,omitempty"`
}

func (x *GetJobIDResponse) Reset() {
	*x = GetJobIDResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_job_v1_job_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetJobIDResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetJobIDResponse) ProtoMessage() {}

func (x *GetJobIDResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_job_v1_job_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetJobIDResponse.ProtoReflect.Descriptor instead.
func (*GetJobIDResponse) Descriptor() ([]byte, []int) {
	return file_aistudio_job_v1_job_proto_rawDescGZIP(), []int{38}
}

func (x *GetJobIDResponse) GetJobID() string {
	if x != nil {
		return x.JobID
	}
	return ""
}

type TaskCondition struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Reason             string `protobuf:"bytes,1,opt,name=reason,proto3" json:"reason,omitempty"`
	Message            string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Type               string `protobuf:"bytes,3,opt,name=type,proto3" json:"type,omitempty"`
	Status             string `protobuf:"bytes,4,opt,name=status,proto3" json:"status,omitempty"`
	LastTransitionTime string `protobuf:"bytes,5,opt,name=lastTransitionTime,proto3" json:"lastTransitionTime,omitempty"` //最后一次状态变更时间
}

func (x *TaskCondition) Reset() {
	*x = TaskCondition{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_job_v1_job_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskCondition) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskCondition) ProtoMessage() {}

func (x *TaskCondition) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_job_v1_job_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskCondition.ProtoReflect.Descriptor instead.
func (*TaskCondition) Descriptor() ([]byte, []int) {
	return file_aistudio_job_v1_job_proto_rawDescGZIP(), []int{39}
}

func (x *TaskCondition) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *TaskCondition) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *TaskCondition) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *TaskCondition) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *TaskCondition) GetLastTransitionTime() string {
	if x != nil {
		return x.LastTransitionTime
	}
	return ""
}

type JobCondition struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Phase              string                       `protobuf:"bytes,1,opt,name=phase,proto3" json:"phase,omitempty"`
	LastTransitionTime string                       `protobuf:"bytes,2,opt,name=lastTransitionTime,proto3" json:"lastTransitionTime,omitempty"`                                                   //最后一次状态变更时间
	Reason             string                       `protobuf:"bytes,3,opt,name=reason,proto3" json:"reason,omitempty"`                                                                           //the reason of the job
	Message            string                       `protobuf:"bytes,4,opt,name=message,proto3" json:"message,omitempty"`                                                                         //the message of the job
	ConditionStatus    JobCondition_ConditionStatus `protobuf:"varint,6,opt,name=conditionStatus,proto3,enum=apis.aistudio.job.v1.JobCondition_ConditionStatus" json:"conditionStatus,omitempty"` //the status of the job
}

func (x *JobCondition) Reset() {
	*x = JobCondition{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_job_v1_job_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *JobCondition) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JobCondition) ProtoMessage() {}

func (x *JobCondition) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_job_v1_job_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JobCondition.ProtoReflect.Descriptor instead.
func (*JobCondition) Descriptor() ([]byte, []int) {
	return file_aistudio_job_v1_job_proto_rawDescGZIP(), []int{40}
}

func (x *JobCondition) GetPhase() string {
	if x != nil {
		return x.Phase
	}
	return ""
}

func (x *JobCondition) GetLastTransitionTime() string {
	if x != nil {
		return x.LastTransitionTime
	}
	return ""
}

func (x *JobCondition) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *JobCondition) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *JobCondition) GetConditionStatus() JobCondition_ConditionStatus {
	if x != nil {
		return x.ConditionStatus
	}
	return JobCondition_True
}

type StopJobRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName string `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	JobName       string `protobuf:"bytes,2,opt,name=jobName,proto3" json:"jobName,omitempty"`
}

func (x *StopJobRequest) Reset() {
	*x = StopJobRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_job_v1_job_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StopJobRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StopJobRequest) ProtoMessage() {}

func (x *StopJobRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_job_v1_job_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StopJobRequest.ProtoReflect.Descriptor instead.
func (*StopJobRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_job_v1_job_proto_rawDescGZIP(), []int{41}
}

func (x *StopJobRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *StopJobRequest) GetJobName() string {
	if x != nil {
		return x.JobName
	}
	return ""
}

type GetJobBaseRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName string `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	JobName       string `protobuf:"bytes,2,opt,name=jobName,proto3" json:"jobName,omitempty"`
	IsDeleted     string `protobuf:"bytes,3,opt,name=isDeleted,proto3" json:"isDeleted,omitempty"`
}

func (x *GetJobBaseRequest) Reset() {
	*x = GetJobBaseRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_job_v1_job_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetJobBaseRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetJobBaseRequest) ProtoMessage() {}

func (x *GetJobBaseRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_job_v1_job_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetJobBaseRequest.ProtoReflect.Descriptor instead.
func (*GetJobBaseRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_job_v1_job_proto_rawDescGZIP(), []int{42}
}

func (x *GetJobBaseRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *GetJobBaseRequest) GetJobName() string {
	if x != nil {
		return x.JobName
	}
	return ""
}

func (x *GetJobBaseRequest) GetIsDeleted() string {
	if x != nil {
		return x.IsDeleted
	}
	return ""
}

type GetJobRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName string `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	JobName       string `protobuf:"bytes,2,opt,name=jobName,proto3" json:"jobName,omitempty"`
	IsDeleted     string `protobuf:"bytes,3,opt,name=isDeleted,proto3" json:"isDeleted,omitempty"`
}

func (x *GetJobRequest) Reset() {
	*x = GetJobRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_job_v1_job_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetJobRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetJobRequest) ProtoMessage() {}

func (x *GetJobRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_job_v1_job_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetJobRequest.ProtoReflect.Descriptor instead.
func (*GetJobRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_job_v1_job_proto_rawDescGZIP(), []int{43}
}

func (x *GetJobRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *GetJobRequest) GetJobName() string {
	if x != nil {
		return x.JobName
	}
	return ""
}

func (x *GetJobRequest) GetIsDeleted() string {
	if x != nil {
		return x.IsDeleted
	}
	return ""
}

type DeleteJobRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName string `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	JobName       string `protobuf:"bytes,2,opt,name=jobName,proto3" json:"jobName,omitempty"`
	IsDeleted     string `protobuf:"bytes,3,opt,name=isDeleted,proto3" json:"isDeleted,omitempty"`
}

func (x *DeleteJobRequest) Reset() {
	*x = DeleteJobRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_job_v1_job_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteJobRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteJobRequest) ProtoMessage() {}

func (x *DeleteJobRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_job_v1_job_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteJobRequest.ProtoReflect.Descriptor instead.
func (*DeleteJobRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_job_v1_job_proto_rawDescGZIP(), []int{44}
}

func (x *DeleteJobRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *DeleteJobRequest) GetJobName() string {
	if x != nil {
		return x.JobName
	}
	return ""
}

func (x *DeleteJobRequest) GetIsDeleted() string {
	if x != nil {
		return x.IsDeleted
	}
	return ""
}

type DeleteJobTemplateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName   string `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	JobTemplateName string `protobuf:"bytes,2,opt,name=jobTemplateName,proto3" json:"jobTemplateName,omitempty"` // 模版名称
}

func (x *DeleteJobTemplateRequest) Reset() {
	*x = DeleteJobTemplateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_job_v1_job_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteJobTemplateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteJobTemplateRequest) ProtoMessage() {}

func (x *DeleteJobTemplateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_job_v1_job_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteJobTemplateRequest.ProtoReflect.Descriptor instead.
func (*DeleteJobTemplateRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_job_v1_job_proto_rawDescGZIP(), []int{45}
}

func (x *DeleteJobTemplateRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *DeleteJobTemplateRequest) GetJobTemplateName() string {
	if x != nil {
		return x.JobTemplateName
	}
	return ""
}

type GetJobTasksRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName        string `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	JobName              string `protobuf:"bytes,2,opt,name=jobName,proto3" json:"jobName,omitempty"`
	IsDeleted            string `protobuf:"bytes,3,opt,name=isDeleted,proto3" json:"isDeleted,omitempty"`
	ShowTheLatestVersion string `protobuf:"bytes,4,opt,name=showTheLatestVersion,proto3" json:"showTheLatestVersion,omitempty"` // 是否只展示最新版本 true 展示最新版本 false 展示旧版本 空值展示所有版本
}

func (x *GetJobTasksRequest) Reset() {
	*x = GetJobTasksRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_job_v1_job_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetJobTasksRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetJobTasksRequest) ProtoMessage() {}

func (x *GetJobTasksRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_job_v1_job_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetJobTasksRequest.ProtoReflect.Descriptor instead.
func (*GetJobTasksRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_job_v1_job_proto_rawDescGZIP(), []int{46}
}

func (x *GetJobTasksRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *GetJobTasksRequest) GetJobName() string {
	if x != nil {
		return x.JobName
	}
	return ""
}

func (x *GetJobTasksRequest) GetIsDeleted() string {
	if x != nil {
		return x.IsDeleted
	}
	return ""
}

func (x *GetJobTasksRequest) GetShowTheLatestVersion() string {
	if x != nil {
		return x.ShowTheLatestVersion
	}
	return ""
}

type GetJobVolumeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName string `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	JobName       string `protobuf:"bytes,2,opt,name=jobName,proto3" json:"jobName,omitempty"`
	IsDeleted     string `protobuf:"bytes,3,opt,name=isDeleted,proto3" json:"isDeleted,omitempty"`
}

func (x *GetJobVolumeRequest) Reset() {
	*x = GetJobVolumeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_job_v1_job_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetJobVolumeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetJobVolumeRequest) ProtoMessage() {}

func (x *GetJobVolumeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_job_v1_job_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetJobVolumeRequest.ProtoReflect.Descriptor instead.
func (*GetJobVolumeRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_job_v1_job_proto_rawDescGZIP(), []int{47}
}

func (x *GetJobVolumeRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *GetJobVolumeRequest) GetJobName() string {
	if x != nil {
		return x.JobName
	}
	return ""
}

func (x *GetJobVolumeRequest) GetIsDeleted() string {
	if x != nil {
		return x.IsDeleted
	}
	return ""
}

type ListJobsOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Creator         string `protobuf:"bytes,1,opt,name=creator,proto3" json:"creator,omitempty"`
	WorkspaceName   string `protobuf:"bytes,2,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	QueueName       string `protobuf:"bytes,3,opt,name=queueName,proto3" json:"queueName,omitempty"`
	JobType         string `protobuf:"bytes,4,opt,name=jobType,proto3" json:"jobType,omitempty"`
	Name            string `protobuf:"bytes,5,opt,name=name,proto3" json:"name,omitempty"`               //模糊匹配
	DisplayName     string `protobuf:"bytes,6,opt,name=displayName,proto3" json:"displayName,omitempty"` //模糊匹配
	Region          string `protobuf:"bytes,7,opt,name=region,proto3" json:"region,omitempty"`
	StatusEnabled   bool   `protobuf:"varint,8,opt,name=statusEnabled,proto3" json:"statusEnabled,omitempty"`
	Member          string `protobuf:"bytes,9,opt,name=member,proto3" json:"member,omitempty"`
	Page            int32  `protobuf:"varint,10,opt,name=page,proto3" json:"page,omitempty"`
	PageSize        int32  `protobuf:"varint,11,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
	State           string `protobuf:"bytes,12,opt,name=state,proto3" json:"state,omitempty"`
	JobTemplateName string `protobuf:"bytes,13,opt,name=jobTemplateName,proto3" json:"jobTemplateName,omitempty"` // 模板名称
}

func (x *ListJobsOptions) Reset() {
	*x = ListJobsOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_job_v1_job_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListJobsOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListJobsOptions) ProtoMessage() {}

func (x *ListJobsOptions) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_job_v1_job_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListJobsOptions.ProtoReflect.Descriptor instead.
func (*ListJobsOptions) Descriptor() ([]byte, []int) {
	return file_aistudio_job_v1_job_proto_rawDescGZIP(), []int{48}
}

func (x *ListJobsOptions) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *ListJobsOptions) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *ListJobsOptions) GetQueueName() string {
	if x != nil {
		return x.QueueName
	}
	return ""
}

func (x *ListJobsOptions) GetJobType() string {
	if x != nil {
		return x.JobType
	}
	return ""
}

func (x *ListJobsOptions) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ListJobsOptions) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *ListJobsOptions) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *ListJobsOptions) GetStatusEnabled() bool {
	if x != nil {
		return x.StatusEnabled
	}
	return false
}

func (x *ListJobsOptions) GetMember() string {
	if x != nil {
		return x.Member
	}
	return ""
}

func (x *ListJobsOptions) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListJobsOptions) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListJobsOptions) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

func (x *ListJobsOptions) GetJobTemplateName() string {
	if x != nil {
		return x.JobTemplateName
	}
	return ""
}

type ListJobsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Jobs  []*Job `protobuf:"bytes,1,rep,name=jobs,proto3" json:"jobs,omitempty"`
	Total int64  `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
}

func (x *ListJobsResponse) Reset() {
	*x = ListJobsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_job_v1_job_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListJobsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListJobsResponse) ProtoMessage() {}

func (x *ListJobsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_job_v1_job_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListJobsResponse.ProtoReflect.Descriptor instead.
func (*ListJobsResponse) Descriptor() ([]byte, []int) {
	return file_aistudio_job_v1_job_proto_rawDescGZIP(), []int{49}
}

func (x *ListJobsResponse) GetJobs() []*Job {
	if x != nil {
		return x.Jobs
	}
	return nil
}

func (x *ListJobsResponse) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

type JobTimeline struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name          string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"` // jobName
	WorkspaceName string `protobuf:"bytes,2,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	StartTime     string `protobuf:"bytes,3,opt,name=startTime,proto3" json:"startTime,omitempty"`
	Status        string `protobuf:"bytes,4,opt,name=status,proto3" json:"status,omitempty"`
	Message       string `protobuf:"bytes,5,opt,name=message,proto3" json:"message,omitempty"`
	Reason        string `protobuf:"bytes,6,opt,name=reason,proto3" json:"reason,omitempty"`
}

func (x *JobTimeline) Reset() {
	*x = JobTimeline{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_job_v1_job_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *JobTimeline) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JobTimeline) ProtoMessage() {}

func (x *JobTimeline) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_job_v1_job_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JobTimeline.ProtoReflect.Descriptor instead.
func (*JobTimeline) Descriptor() ([]byte, []int) {
	return file_aistudio_job_v1_job_proto_rawDescGZIP(), []int{50}
}

func (x *JobTimeline) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *JobTimeline) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *JobTimeline) GetStartTime() string {
	if x != nil {
		return x.StartTime
	}
	return ""
}

func (x *JobTimeline) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *JobTimeline) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *JobTimeline) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

type JobVolume struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name       string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	MountPoint string `protobuf:"bytes,2,opt,name=mountPoint,proto3" json:"mountPoint,omitempty"`
	VolumeType string `protobuf:"bytes,3,opt,name=volumeType,proto3" json:"volumeType,omitempty"`
	SubPath    string `protobuf:"bytes,4,opt,name=subPath,proto3" json:"subPath,omitempty"`
	Scope      string `protobuf:"bytes,5,opt,name=scope,proto3" json:"scope,omitempty"`
}

func (x *JobVolume) Reset() {
	*x = JobVolume{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_job_v1_job_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *JobVolume) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JobVolume) ProtoMessage() {}

func (x *JobVolume) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_job_v1_job_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JobVolume.ProtoReflect.Descriptor instead.
func (*JobVolume) Descriptor() ([]byte, []int) {
	return file_aistudio_job_v1_job_proto_rawDescGZIP(), []int{51}
}

func (x *JobVolume) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *JobVolume) GetMountPoint() string {
	if x != nil {
		return x.MountPoint
	}
	return ""
}

func (x *JobVolume) GetVolumeType() string {
	if x != nil {
		return x.VolumeType
	}
	return ""
}

func (x *JobVolume) GetSubPath() string {
	if x != nil {
		return x.SubPath
	}
	return ""
}

func (x *JobVolume) GetScope() string {
	if x != nil {
		return x.Scope
	}
	return ""
}

type ListJobVolumes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Volumes []*JobVolume `protobuf:"bytes,1,rep,name=volumes,proto3" json:"volumes,omitempty"`
}

func (x *ListJobVolumes) Reset() {
	*x = ListJobVolumes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_job_v1_job_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListJobVolumes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListJobVolumes) ProtoMessage() {}

func (x *ListJobVolumes) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_job_v1_job_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListJobVolumes.ProtoReflect.Descriptor instead.
func (*ListJobVolumes) Descriptor() ([]byte, []int) {
	return file_aistudio_job_v1_job_proto_rawDescGZIP(), []int{52}
}

func (x *ListJobVolumes) GetVolumes() []*JobVolume {
	if x != nil {
		return x.Volumes
	}
	return nil
}

type StopJobTaskRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName string `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	JobName       string `protobuf:"bytes,2,opt,name=jobName,proto3" json:"jobName,omitempty"`
	TaskID        string `protobuf:"bytes,3,opt,name=taskID,proto3" json:"taskID,omitempty"`
	PodName       string `protobuf:"bytes,4,opt,name=podName,proto3" json:"podName,omitempty"`
}

func (x *StopJobTaskRequest) Reset() {
	*x = StopJobTaskRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_job_v1_job_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StopJobTaskRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StopJobTaskRequest) ProtoMessage() {}

func (x *StopJobTaskRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_job_v1_job_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StopJobTaskRequest.ProtoReflect.Descriptor instead.
func (*StopJobTaskRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_job_v1_job_proto_rawDescGZIP(), []int{53}
}

func (x *StopJobTaskRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *StopJobTaskRequest) GetJobName() string {
	if x != nil {
		return x.JobName
	}
	return ""
}

func (x *StopJobTaskRequest) GetTaskID() string {
	if x != nil {
		return x.TaskID
	}
	return ""
}

func (x *StopJobTaskRequest) GetPodName() string {
	if x != nil {
		return x.PodName
	}
	return ""
}

type RedeployJobRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceName string `protobuf:"bytes,1,opt,name=workspaceName,proto3" json:"workspaceName,omitempty"`
	JobName       string `protobuf:"bytes,2,opt,name=jobName,proto3" json:"jobName,omitempty"`
}

func (x *RedeployJobRequest) Reset() {
	*x = RedeployJobRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_job_v1_job_proto_msgTypes[54]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RedeployJobRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RedeployJobRequest) ProtoMessage() {}

func (x *RedeployJobRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_job_v1_job_proto_msgTypes[54]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RedeployJobRequest.ProtoReflect.Descriptor instead.
func (*RedeployJobRequest) Descriptor() ([]byte, []int) {
	return file_aistudio_job_v1_job_proto_rawDescGZIP(), []int{54}
}

func (x *RedeployJobRequest) GetWorkspaceName() string {
	if x != nil {
		return x.WorkspaceName
	}
	return ""
}

func (x *RedeployJobRequest) GetJobName() string {
	if x != nil {
		return x.JobName
	}
	return ""
}

type SourceCode_CloudFsSource struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	VolumeName string `protobuf:"bytes,1,opt,name=volumeName,proto3" json:"volumeName,omitempty"`
	SubPath    string `protobuf:"bytes,2,opt,name=subPath,proto3" json:"subPath,omitempty"` //卷内部的路径,可以为空
}

func (x *SourceCode_CloudFsSource) Reset() {
	*x = SourceCode_CloudFsSource{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_job_v1_job_proto_msgTypes[59]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SourceCode_CloudFsSource) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SourceCode_CloudFsSource) ProtoMessage() {}

func (x *SourceCode_CloudFsSource) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_job_v1_job_proto_msgTypes[59]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SourceCode_CloudFsSource.ProtoReflect.Descriptor instead.
func (*SourceCode_CloudFsSource) Descriptor() ([]byte, []int) {
	return file_aistudio_job_v1_job_proto_rawDescGZIP(), []int{33, 0}
}

func (x *SourceCode_CloudFsSource) GetVolumeName() string {
	if x != nil {
		return x.VolumeName
	}
	return ""
}

func (x *SourceCode_CloudFsSource) GetSubPath() string {
	if x != nil {
		return x.SubPath
	}
	return ""
}

type SourceCode_GitSource struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url    string `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	Branch string `protobuf:"bytes,2,opt,name=branch,proto3" json:"branch,omitempty"`
}

func (x *SourceCode_GitSource) Reset() {
	*x = SourceCode_GitSource{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_job_v1_job_proto_msgTypes[60]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SourceCode_GitSource) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SourceCode_GitSource) ProtoMessage() {}

func (x *SourceCode_GitSource) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_job_v1_job_proto_msgTypes[60]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SourceCode_GitSource.ProtoReflect.Descriptor instead.
func (*SourceCode_GitSource) Descriptor() ([]byte, []int) {
	return file_aistudio_job_v1_job_proto_rawDescGZIP(), []int{33, 1}
}

func (x *SourceCode_GitSource) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *SourceCode_GitSource) GetBranch() string {
	if x != nil {
		return x.Branch
	}
	return ""
}

type SourceCode_CubeFsSource struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	VolumeName string `protobuf:"bytes,1,opt,name=volumeName,proto3" json:"volumeName,omitempty"`
}

func (x *SourceCode_CubeFsSource) Reset() {
	*x = SourceCode_CubeFsSource{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aistudio_job_v1_job_proto_msgTypes[61]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SourceCode_CubeFsSource) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SourceCode_CubeFsSource) ProtoMessage() {}

func (x *SourceCode_CubeFsSource) ProtoReflect() protoreflect.Message {
	mi := &file_aistudio_job_v1_job_proto_msgTypes[61]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SourceCode_CubeFsSource.ProtoReflect.Descriptor instead.
func (*SourceCode_CubeFsSource) Descriptor() ([]byte, []int) {
	return file_aistudio_job_v1_job_proto_rawDescGZIP(), []int{33, 2}
}

func (x *SourceCode_CubeFsSource) GetVolumeName() string {
	if x != nil {
		return x.VolumeName
	}
	return ""
}

var File_aistudio_job_v1_job_proto protoreflect.FileDescriptor

var file_aistudio_job_v1_job_proto_rawDesc = []byte{
	0x0a, 0x19, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2f, 0x6a, 0x6f, 0x62, 0x2f, 0x76,
	0x31, 0x2f, 0x6a, 0x6f, 0x62, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x14, 0x61, 0x70, 0x69,
	0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6a, 0x6f, 0x62, 0x2e, 0x76,
	0x31, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e,
	0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x13, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x61, 0x69, 0x73, 0x74,
	0x75, 0x64, 0x69, 0x6f, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x58, 0x0a, 0x16, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x4a, 0x6f, 0x62, 0x49, 0x73, 0x45, 0x78, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f,
	0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6a,
	0x6f, 0x62, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6a, 0x6f,
	0x62, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x49, 0x0a, 0x17, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4a, 0x6f,
	0x62, 0x49, 0x73, 0x45, 0x78, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73,
	0x22, 0x70, 0x0a, 0x14, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18,
	0x0a, 0x07, 0x6a, 0x6f, 0x62, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x6a, 0x6f, 0x62, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x6d, 0x62,
	0x65, 0x72, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x6d, 0x62, 0x65,
	0x72, 0x73, 0x22, 0xa6, 0x05, 0x0a, 0x0b, 0x4a, 0x6f, 0x62, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61,
	0x74, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61,
	0x79, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x69, 0x73,
	0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x29, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x72, 0x02, 0x18, 0x64, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x4d, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x27, 0xfa, 0x42, 0x24, 0x72,
	0x22, 0x10, 0x01, 0x18, 0x1f, 0x32, 0x1c, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x5d, 0x28, 0x5b, 0x2d,
	0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x2a, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d,
	0x29, 0x3f, 0x24, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x45, 0x0a, 0x06, 0x6c, 0x61,
	0x62, 0x65, 0x6c, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x61, 0x70, 0x69,
	0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6a, 0x6f, 0x62, 0x2e, 0x76,
	0x31, 0x2e, 0x4a, 0x6f, 0x62, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x2e, 0x4c, 0x61,
	0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c,
	0x73, 0x12, 0x1a, 0x0a, 0x08, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x73, 0x18, 0x07, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x08, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x73, 0x12, 0x18, 0x0a,
	0x07, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07,
	0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x12, 0x39, 0x0a, 0x08, 0x74, 0x72, 0x69, 0x67, 0x67,
	0x65, 0x72, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x73,
	0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6a, 0x6f, 0x62, 0x2e, 0x76, 0x31,
	0x2e, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x52, 0x08, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65,
	0x72, 0x73, 0x12, 0x55, 0x0a, 0x11, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x53,
	0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x27, 0x2e,
	0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6a, 0x6f,
	0x62, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74,
	0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x52, 0x11, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f,
	0x6e, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x12, 0x2b, 0x0a, 0x03, 0x6a, 0x6f, 0x62,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69,
	0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6a, 0x6f, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x4a, 0x6f,
	0x62, 0x52, 0x03, 0x6a, 0x6f, 0x62, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f,
	0x72, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72,
	0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x0e,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x1e, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x0f,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65,
	0x1a, 0x39, 0x0a, 0x0b, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xcc, 0x01, 0x0a, 0x0f,
	0x4a, 0x6f, 0x62, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x56, 0x69, 0x65, 0x77, 0x12,
	0x43, 0x0a, 0x0b, 0x6a, 0x6f, 0x62, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74,
	0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6a, 0x6f, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x4a, 0x6f, 0x62, 0x54,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x0b, 0x6a, 0x6f, 0x62, 0x54, 0x65, 0x6d, 0x70,
	0x6c, 0x61, 0x74, 0x65, 0x12, 0x2c, 0x0a, 0x11, 0x6a, 0x6f, 0x62, 0x54, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x11, 0x6a, 0x6f, 0x62, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x26, 0x0a, 0x0e, 0x6e, 0x65, 0x78, 0x74, 0x4e, 0x75, 0x6d, 0x41, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0e, 0x6e, 0x65, 0x78, 0x74,
	0x4e, 0x75, 0x6d, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x6e, 0x75,
	0x6d, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a,
	0x6e, 0x75, 0x6d, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0xe9, 0x02, 0x0a, 0x07, 0x54,
	0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x12, 0x43, 0x0a, 0x0b, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65,
	0x72, 0x54, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x61, 0x70,
	0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6a, 0x6f, 0x62, 0x2e,
	0x76, 0x31, 0x2e, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b,
	0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x74,
	0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x59, 0x0a,
	0x12, 0x74, 0x69, 0x6d, 0x65, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x4f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x61, 0x70, 0x69, 0x73,
	0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6a, 0x6f, 0x62, 0x2e, 0x76, 0x31,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x72, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x4f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x52, 0x12, 0x74, 0x69, 0x6d, 0x65, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65,
	0x72, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x28, 0x0a, 0x0f, 0x6e, 0x65, 0x78, 0x74,
	0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x0f, 0x6e, 0x65, 0x78, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x6e, 0x75, 0x6d, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x6e, 0x75, 0x6d, 0x41, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x12, 0x38, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69,
	0x6f, 0x2e, 0x6a, 0x6f, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x18, 0x0a, 0x07,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x8c, 0x03, 0x0a, 0x13, 0x54, 0x69, 0x6d, 0x65, 0x72,
	0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x4e,
	0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3a, 0x2e, 0x61,
	0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6a, 0x6f, 0x62,
	0x2e, 0x76, 0x31, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x72, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72,
	0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x72, 0x54, 0x72, 0x69,
	0x67, 0x67, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x1a,
	0x0a, 0x08, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x08, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x72,
	0x6f, 0x6e, 0x45, 0x78, 0x70, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x72,
	0x6f, 0x6e, 0x45, 0x78, 0x70, 0x72, 0x12, 0x31, 0x0a, 0x05, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73,
	0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6a, 0x6f, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x79, 0x63,
	0x6c, 0x65, 0x52, 0x05, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x12, 0x2a, 0x0a, 0x10, 0x70, 0x65, 0x72,
	0x69, 0x6f, 0x64, 0x4f, 0x66, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x69, 0x74, 0x79, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x10, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x4f, 0x66, 0x56, 0x61, 0x6c,
	0x69, 0x64, 0x69, 0x74, 0x79, 0x12, 0x1a, 0x0a, 0x08, 0x6d, 0x61, 0x78, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x6d, 0x61, 0x78, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x37, 0x0a, 0x08, 0x73, 0x6b, 0x69, 0x70, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75,
	0x64, 0x69, 0x6f, 0x2e, 0x6a, 0x6f, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x79, 0x63, 0x6c, 0x65,
	0x52, 0x08, 0x73, 0x6b, 0x69, 0x70, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x39, 0x0a, 0x10, 0x54, 0x69,
	0x6d, 0x65, 0x72, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0c,
	0x0a, 0x08, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08,
	0x43, 0x72, 0x6f, 0x6e, 0x45, 0x78, 0x70, 0x72, 0x10, 0x01, 0x12, 0x09, 0x0a, 0x05, 0x43, 0x79,
	0x63, 0x6c, 0x65, 0x10, 0x02, 0x22, 0x87, 0x01, 0x0a, 0x05, 0x43, 0x79, 0x63, 0x6c, 0x65, 0x12,
	0x14, 0x0a, 0x05, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x18, 0x02, 0x20, 0x03, 0x28, 0x05, 0x52, 0x05,
	0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x12, 0x1e, 0x0a, 0x0a, 0x64, 0x61, 0x79, 0x4f, 0x66, 0x4d, 0x6f,
	0x6e, 0x74, 0x68, 0x18, 0x03, 0x20, 0x03, 0x28, 0x05, 0x52, 0x0a, 0x64, 0x61, 0x79, 0x4f, 0x66,
	0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x12, 0x1c, 0x0a, 0x09, 0x64, 0x61, 0x79, 0x4f, 0x66, 0x57, 0x65,
	0x65, 0x6b, 0x18, 0x04, 0x20, 0x03, 0x28, 0x05, 0x52, 0x09, 0x64, 0x61, 0x79, 0x4f, 0x66, 0x57,
	0x65, 0x65, 0x6b, 0x12, 0x12, 0x0a, 0x04, 0x68, 0x6f, 0x75, 0x72, 0x18, 0x05, 0x20, 0x03, 0x28,
	0x05, 0x52, 0x04, 0x68, 0x6f, 0x75, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x69, 0x6e, 0x75, 0x74,
	0x65, 0x18, 0x06, 0x20, 0x03, 0x28, 0x05, 0x52, 0x06, 0x6d, 0x69, 0x6e, 0x75, 0x74, 0x65, 0x22,
	0xb4, 0x01, 0x0a, 0x10, 0x4a, 0x6f, 0x62, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x52, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x6a, 0x6f, 0x62, 0x4e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6a, 0x6f, 0x62, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x28,
	0x0a, 0x0f, 0x6a, 0x6f, 0x62, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6a, 0x6f, 0x62, 0x54, 0x65, 0x6d, 0x70,
	0x6c, 0x61, 0x74, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x74, 0x72, 0x69, 0x67,
	0x67, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x74,
	0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x74, 0x72,
	0x69, 0x67, 0x67, 0x65, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x22, 0xac, 0x01, 0x0a, 0x1e, 0x50, 0x61, 0x75, 0x73, 0x65,
	0x4a, 0x6f, 0x62, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x54, 0x72, 0x69, 0x67, 0x67,
	0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72,
	0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x28, 0x0a, 0x0f, 0x6a, 0x6f, 0x62, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6a, 0x6f, 0x62, 0x54, 0x65, 0x6d,
	0x70, 0x6c, 0x61, 0x74, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x74, 0x72, 0x69,
	0x67, 0x67, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0xae, 0x01, 0x0a, 0x20, 0x52, 0x65, 0x63, 0x6f, 0x76, 0x65,
	0x72, 0x4a, 0x6f, 0x62, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x54, 0x72, 0x69, 0x67,
	0x67, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f,
	0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x28, 0x0a, 0x0f, 0x6a, 0x6f, 0x62, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6a, 0x6f, 0x62, 0x54, 0x65,
	0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x74, 0x72,
	0x69, 0x67, 0x67, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0xf6, 0x04, 0x0a, 0x20, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x4f, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4a, 0x6f, 0x62, 0x54, 0x65, 0x6d, 0x70,
	0x6c, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x20, 0x0a, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x29, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x64, 0x52,
	0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x4d, 0x0a, 0x0d,
	0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x27, 0xfa, 0x42, 0x24, 0x72, 0x22, 0x10, 0x01, 0x18, 0x1f, 0x32, 0x1c,
	0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x5d, 0x28, 0x5b, 0x2d, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d,
	0x2a, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x29, 0x3f, 0x24, 0x52, 0x0d, 0x77, 0x6f,
	0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72,
	0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67,
	0x69, 0x6f, 0x6e, 0x12, 0x5a, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x06, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x42, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75,
	0x64, 0x69, 0x6f, 0x2e, 0x6a, 0x6f, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x4f, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4a, 0x6f, 0x62, 0x54, 0x65, 0x6d, 0x70,
	0x6c, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x4c, 0x61, 0x62, 0x65,
	0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x12,
	0x2b, 0x0a, 0x03, 0x6a, 0x6f, 0x62, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61,
	0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6a, 0x6f, 0x62,
	0x2e, 0x76, 0x31, 0x2e, 0x4a, 0x6f, 0x62, 0x52, 0x03, 0x6a, 0x6f, 0x62, 0x12, 0x39, 0x0a, 0x08,
	0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d,
	0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6a,
	0x6f, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x52, 0x08, 0x74,
	0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x73, 0x12, 0x55, 0x0a, 0x11, 0x65, 0x78, 0x65, 0x63, 0x75,
	0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64,
	0x69, 0x6f, 0x2e, 0x6a, 0x6f, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74,
	0x69, 0x6f, 0x6e, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x52, 0x11, 0x65, 0x78, 0x65,
	0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x12, 0x1a,
	0x0a, 0x08, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x08, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65,
	0x6d, 0x62, 0x65, 0x72, 0x73, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x6d,
	0x62, 0x65, 0x72, 0x73, 0x1a, 0x39, 0x0a, 0x0b, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22,
	0x67, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x4a, 0x6f, 0x62, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x28,
	0x0a, 0x0f, 0x6a, 0x6f, 0x62, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6a, 0x6f, 0x62, 0x54, 0x65, 0x6d, 0x70,
	0x6c, 0x61, 0x74, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xe7, 0x01, 0x0a, 0x1b, 0x4c, 0x69, 0x73,
	0x74, 0x4a, 0x6f, 0x62, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x56, 0x69, 0x65, 0x77,
	0x73, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x28,
	0x0a, 0x0f, 0x6a, 0x6f, 0x62, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6a, 0x6f, 0x62, 0x54, 0x65, 0x6d, 0x70,
	0x6c, 0x61, 0x74, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69,
	0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e,
	0x12, 0x18, 0x0a, 0x07, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x65,
	0x6d, 0x62, 0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x65, 0x6d, 0x62,
	0x65, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69,
	0x7a, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69,
	0x7a, 0x65, 0x22, 0x87, 0x01, 0x0a, 0x1c, 0x4c, 0x69, 0x73, 0x74, 0x4a, 0x6f, 0x62, 0x54, 0x65,
	0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x56, 0x69, 0x65, 0x77, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x51, 0x0a, 0x10, 0x6a, 0x6f, 0x62, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61,
	0x74, 0x65, 0x56, 0x69, 0x65, 0x77, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e,
	0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6a, 0x6f,
	0x62, 0x2e, 0x76, 0x31, 0x2e, 0x4a, 0x6f, 0x62, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65,
	0x56, 0x69, 0x65, 0x77, 0x52, 0x10, 0x6a, 0x6f, 0x62, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x56, 0x69, 0x65, 0x77, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x22, 0xbb, 0x02, 0x0a,
	0x27, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4a,
	0x6f, 0x62, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65,
	0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x28,
	0x0a, 0x0f, 0x6a, 0x6f, 0x62, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6a, 0x6f, 0x62, 0x54, 0x65, 0x6d, 0x70,
	0x6c, 0x61, 0x74, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x43, 0x0a, 0x0b, 0x74, 0x72, 0x69, 0x67,
	0x67, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e,
	0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6a, 0x6f,
	0x62, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x0b, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x20, 0x0a,
	0x0b, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x59, 0x0a, 0x12, 0x74, 0x69, 0x6d, 0x65, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x4f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x61, 0x70,
	0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6a, 0x6f, 0x62, 0x2e,
	0x76, 0x31, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x72, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x4f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x12, 0x74, 0x69, 0x6d, 0x65, 0x54, 0x72, 0x69, 0x67,
	0x67, 0x65, 0x72, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x93, 0x01, 0x0a, 0x1f, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x4a, 0x6f, 0x62, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65,
	0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24,
	0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x28, 0x0a, 0x0f, 0x6a, 0x6f, 0x62, 0x54, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6a,
	0x6f, 0x62, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x20,
	0x0a, 0x0b, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65,
	0x22, 0x90, 0x01, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x4a, 0x6f, 0x62, 0x54, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x28, 0x0a, 0x0f, 0x6a, 0x6f, 0x62, 0x54, 0x65,
	0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0f, 0x6a, 0x6f, 0x62, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x20, 0x0a, 0x0b, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x4e,
	0x61, 0x6d, 0x65, 0x22, 0x92, 0x01, 0x0a, 0x1e, 0x4c, 0x69, 0x73, 0x74, 0x4a, 0x6f, 0x62, 0x54,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77,
	0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x28, 0x0a, 0x0f,
	0x6a, 0x6f, 0x62, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6a, 0x6f, 0x62, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61,
	0x74, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65,
	0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x74, 0x72, 0x69,
	0x67, 0x67, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x72, 0x0a, 0x1f, 0x4c, 0x69, 0x73, 0x74,
	0x4a, 0x6f, 0x62, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x54, 0x72, 0x69, 0x67, 0x67,
	0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x39, 0x0a, 0x08, 0x74,
	0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e,
	0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6a, 0x6f,
	0x62, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x52, 0x08, 0x74, 0x72,
	0x69, 0x67, 0x67, 0x65, 0x72, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x22, 0x84, 0x09, 0x0a,
	0x03, 0x4a, 0x6f, 0x62, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x69, 0x73, 0x70,
	0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64,
	0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x24, 0x0a, 0x0d,
	0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x6f, 0x72, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e,
	0x12, 0x2a, 0x0a, 0x10, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x65, 0x76, 0x69,
	0x73, 0x69, 0x6f, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x77, 0x6f, 0x72, 0x6b,
	0x66, 0x6c, 0x6f, 0x77, 0x52, 0x65, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x37, 0x0a, 0x07,
	0x6a, 0x6f, 0x62, 0x54, 0x79, 0x70, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1d, 0x2e,
	0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6a, 0x6f,
	0x62, 0x2e, 0x76, 0x31, 0x2e, 0x4a, 0x6f, 0x62, 0x54, 0x79, 0x70, 0x65, 0x52, 0x07, 0x6a, 0x6f,
	0x62, 0x54, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73,
	0x18, 0x0b, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x12,
	0x3d, 0x0a, 0x09, 0x6a, 0x6f, 0x62, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64,
	0x69, 0x6f, 0x2e, 0x6a, 0x6f, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x4a, 0x6f, 0x62, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x09, 0x6a, 0x6f, 0x62, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1c,
	0x0a, 0x09, 0x71, 0x75, 0x65, 0x75, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x71, 0x75, 0x65, 0x75, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x61, 0x0a, 0x15,
	0x70, 0x79, 0x54, 0x6f, 0x72, 0x63, 0x68, 0x44, 0x44, 0x50, 0x4a, 0x6f, 0x62, 0x54, 0x65, 0x6d,
	0x70, 0x6c, 0x61, 0x74, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x61, 0x70,
	0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6a, 0x6f, 0x62, 0x2e,
	0x76, 0x31, 0x2e, 0x50, 0x79, 0x54, 0x6f, 0x72, 0x63, 0x68, 0x44, 0x44, 0x50, 0x4a, 0x6f, 0x62,
	0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x15, 0x70, 0x79, 0x54, 0x6f, 0x72, 0x63,
	0x68, 0x44, 0x44, 0x50, 0x4a, 0x6f, 0x62, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x12,
	0x5e, 0x0a, 0x14, 0x64, 0x65, 0x65, 0x70, 0x53, 0x70, 0x65, 0x65, 0x64, 0x4a, 0x6f, 0x62, 0x54,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e,
	0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6a, 0x6f,
	0x62, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x53, 0x70, 0x65, 0x65, 0x64, 0x4a, 0x6f,
	0x62, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x14, 0x64, 0x65, 0x65, 0x70, 0x53,
	0x70, 0x65, 0x65, 0x64, 0x4a, 0x6f, 0x62, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x12,
	0x6d, 0x0a, 0x19, 0x73, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x69, 0x6e,
	0x67, 0x4a, 0x6f, 0x62, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x18, 0x10, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64,
	0x69, 0x6f, 0x2e, 0x6a, 0x6f, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x69, 0x6d, 0x70, 0x6c, 0x65,
	0x54, 0x72, 0x61, 0x69, 0x6e, 0x69, 0x6e, 0x67, 0x4a, 0x6f, 0x62, 0x54, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x52, 0x19, 0x73, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x54, 0x72, 0x61, 0x69, 0x6e,
	0x69, 0x6e, 0x67, 0x4a, 0x6f, 0x62, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x12, 0x1e,
	0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x11, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1e,
	0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x12, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x49,
	0x0a, 0x0d, 0x72, 0x65, 0x73, 0x74, 0x61, 0x72, 0x74, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x18,
	0x13, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73,
	0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6a, 0x6f, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52, 0x0d, 0x72, 0x65, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12, 0x26, 0x0a, 0x0e, 0x61, 0x6c, 0x61,
	0x72, 0x6d, 0x53, 0x68, 0x69, 0x65, 0x6c, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x16, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x0e, 0x61, 0x6c, 0x61, 0x72, 0x6d, 0x53, 0x68, 0x69, 0x65, 0x6c, 0x64, 0x69, 0x6e,
	0x67, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x18, 0x17, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x12, 0x20, 0x0a,
	0x0b, 0x6d, 0x61, 0x78, 0x57, 0x61, 0x69, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x18, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0b, 0x6d, 0x61, 0x78, 0x57, 0x61, 0x69, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x3d, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x19, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x25, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e,
	0x6a, 0x6f, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x4a, 0x6f, 0x62, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c,
	0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x12, 0x28,
	0x0a, 0x0f, 0x6a, 0x6f, 0x62, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6a, 0x6f, 0x62, 0x54, 0x65, 0x6d, 0x70,
	0x6c, 0x61, 0x74, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x1a, 0x39, 0x0a, 0x0b, 0x4c, 0x61, 0x62, 0x65,
	0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x22, 0x4f, 0x0a, 0x0d, 0x52, 0x65, 0x73, 0x74, 0x61, 0x72, 0x74, 0x50, 0x6f,
	0x6c, 0x69, 0x63, 0x79, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x24,
	0x0a, 0x0d, 0x6d, 0x61, 0x78, 0x52, 0x65, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x6d, 0x61, 0x78, 0x52, 0x65, 0x74, 0x72, 0x79, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x22, 0x43, 0x0a, 0x0d, 0x44, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x56,
	0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x22, 0xd2, 0x08, 0x0a, 0x10, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x29, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02,
	0x18, 0x64, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x4d, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x27, 0xfa, 0x42, 0x24, 0x72, 0x22, 0x10, 0x01, 0x18,
	0x1f, 0x32, 0x1c, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x5d, 0x28, 0x5b, 0x2d, 0x61, 0x2d, 0x7a, 0x30,
	0x2d, 0x39, 0x5d, 0x2a, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x29, 0x3f, 0x24, 0x52,
	0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16,
	0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x4a, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73,
	0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69,
	0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6a, 0x6f, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x4c,
	0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65,
	0x6c, 0x73, 0x12, 0x37, 0x0a, 0x07, 0x6a, 0x6f, 0x62, 0x54, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75,
	0x64, 0x69, 0x6f, 0x2e, 0x6a, 0x6f, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x4a, 0x6f, 0x62, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x07, 0x6a, 0x6f, 0x62, 0x54, 0x79, 0x70, 0x65, 0x12, 0x27, 0x0a, 0x09, 0x71,
	0x75, 0x65, 0x75, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09,
	0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x1f, 0x52, 0x09, 0x71, 0x75, 0x65, 0x75, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x3a, 0x0a, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69,
	0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6a, 0x6f, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72,
	0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x52, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79,
	0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x07, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x12, 0x61, 0x0a, 0x15, 0x70, 0x79,
	0x54, 0x6f, 0x72, 0x63, 0x68, 0x44, 0x44, 0x50, 0x4a, 0x6f, 0x62, 0x54, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x61, 0x70, 0x69, 0x73,
	0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6a, 0x6f, 0x62, 0x2e, 0x76, 0x31,
	0x2e, 0x50, 0x79, 0x54, 0x6f, 0x72, 0x63, 0x68, 0x44, 0x44, 0x50, 0x4a, 0x6f, 0x62, 0x54, 0x65,
	0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x15, 0x70, 0x79, 0x54, 0x6f, 0x72, 0x63, 0x68, 0x44,
	0x44, 0x50, 0x4a, 0x6f, 0x62, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x12, 0x5e, 0x0a,
	0x14, 0x64, 0x65, 0x65, 0x70, 0x53, 0x70, 0x65, 0x65, 0x64, 0x4a, 0x6f, 0x62, 0x54, 0x65, 0x6d,
	0x70, 0x6c, 0x61, 0x74, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x61, 0x70,
	0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6a, 0x6f, 0x62, 0x2e,
	0x76, 0x31, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x53, 0x70, 0x65, 0x65, 0x64, 0x4a, 0x6f, 0x62, 0x54,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x14, 0x64, 0x65, 0x65, 0x70, 0x53, 0x70, 0x65,
	0x65, 0x64, 0x4a, 0x6f, 0x62, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x12, 0x6d, 0x0a,
	0x19, 0x73, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x69, 0x6e, 0x67, 0x4a,
	0x6f, 0x62, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x2f, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f,
	0x2e, 0x6a, 0x6f, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x54, 0x72,
	0x61, 0x69, 0x6e, 0x69, 0x6e, 0x67, 0x4a, 0x6f, 0x62, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x52, 0x19, 0x73, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x69, 0x6e,
	0x67, 0x4a, 0x6f, 0x62, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x12, 0x20, 0x0a, 0x0b,
	0x6d, 0x61, 0x78, 0x57, 0x61, 0x69, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0b, 0x6d, 0x61, 0x78, 0x57, 0x61, 0x69, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x49,
	0x0a, 0x0d, 0x72, 0x65, 0x73, 0x74, 0x61, 0x72, 0x74, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x18,
	0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73,
	0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6a, 0x6f, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52, 0x0d, 0x72, 0x65, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12, 0x26, 0x0a, 0x0e, 0x61, 0x6c, 0x61,
	0x72, 0x6d, 0x53, 0x68, 0x69, 0x65, 0x6c, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x10, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x0e, 0x61, 0x6c, 0x61, 0x72, 0x6d, 0x53, 0x68, 0x69, 0x65, 0x6c, 0x64, 0x69, 0x6e,
	0x67, 0x12, 0x28, 0x0a, 0x0f, 0x6a, 0x6f, 0x62, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6a, 0x6f, 0x62, 0x54,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x68,
	0x61, 0x64, 0x6f, 0x6f, 0x70, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x13, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x0d, 0x68, 0x61, 0x64, 0x6f, 0x6f, 0x70, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65,
	0x64, 0x12, 0x20, 0x0a, 0x0b, 0x68, 0x61, 0x64, 0x6f, 0x6f, 0x70, 0x55, 0x73, 0x65, 0x72, 0x73,
	0x18, 0x14, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x68, 0x61, 0x64, 0x6f, 0x6f, 0x70, 0x55, 0x73,
	0x65, 0x72, 0x73, 0x1a, 0x39, 0x0a, 0x0b, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x2d,
	0x0a, 0x11, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6a, 0x6f, 0x62, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6a, 0x6f, 0x62, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xef, 0x01,
	0x0a, 0x10, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x6a, 0x6f, 0x62, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x6a, 0x6f, 0x62, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b,
	0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x29,
	0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x64, 0x52, 0x0b, 0x64, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72,
	0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x18, 0x0a, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x6d,
	0x62, 0x65, 0x72, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x6d, 0x62,
	0x65, 0x72, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x22,
	0x5a, 0x0a, 0x18, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x62,
	0x6f, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x77,
	0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x18, 0x0a, 0x07, 0x6a, 0x6f, 0x62, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x6a, 0x6f, 0x62, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x57, 0x0a, 0x15, 0x47,
	0x65, 0x74, 0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72,
	0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6a, 0x6f,
	0x62, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6a, 0x6f, 0x62,
	0x4e, 0x61, 0x6d, 0x65, 0x22, 0x40, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x54, 0x65, 0x6e, 0x73, 0x6f,
	0x72, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x10,
	0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c,
	0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x22, 0xda, 0x03, 0x0a, 0x15, 0x50, 0x79, 0x54, 0x6f, 0x72,
	0x63, 0x68, 0x44, 0x44, 0x50, 0x4a, 0x6f, 0x62, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65,
	0x12, 0x2e, 0x0a, 0x12, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x45,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x12, 0x74, 0x65,
	0x6e, 0x73, 0x6f, 0x72, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64,
	0x12, 0x39, 0x0a, 0x0b, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x53, 0x70, 0x65, 0x63, 0x73, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x53, 0x70, 0x65, 0x63, 0x52, 0x0b,
	0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x53, 0x70, 0x65, 0x63, 0x73, 0x12, 0x40, 0x0a, 0x0a, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x20, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e,
	0x6a, 0x6f, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x43, 0x6f, 0x64,
	0x65, 0x52, 0x0a, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x2d, 0x0a,
	0x07, 0x65, 0x6e, 0x76, 0x56, 0x61, 0x72, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13,
	0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x6e, 0x76,
	0x56, 0x61, 0x72, 0x52, 0x07, 0x65, 0x6e, 0x76, 0x56, 0x61, 0x72, 0x73, 0x12, 0x18, 0x0a, 0x07,
	0x63, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63,
	0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x36, 0x0a, 0x06,
	0x6d, 0x61, 0x73, 0x74, 0x65, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x61,
	0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6a, 0x6f, 0x62,
	0x2e, 0x76, 0x31, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x70, 0x65, 0x63, 0x52, 0x06, 0x6d, 0x61,
	0x73, 0x74, 0x65, 0x72, 0x12, 0x36, 0x0a, 0x06, 0x77, 0x6f, 0x72, 0x6b, 0x65, 0x72, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74,
	0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6a, 0x6f, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x61, 0x73, 0x6b,
	0x53, 0x70, 0x65, 0x63, 0x52, 0x06, 0x77, 0x6f, 0x72, 0x6b, 0x65, 0x72, 0x12, 0x45, 0x0a, 0x0b,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x53, 0x70, 0x65, 0x63, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69,
	0x6f, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x53, 0x70, 0x65, 0x63, 0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x53, 0x70,
	0x65, 0x63, 0x73, 0x22, 0xd9, 0x03, 0x0a, 0x14, 0x44, 0x65, 0x65, 0x70, 0x53, 0x70, 0x65, 0x65,
	0x64, 0x4a, 0x6f, 0x62, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x12, 0x2e, 0x0a, 0x12,
	0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x45, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x12, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72,
	0x62, 0x6f, 0x61, 0x72, 0x64, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x39, 0x0a, 0x0b,
	0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x53, 0x70, 0x65, 0x63, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x17, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x53, 0x70, 0x65, 0x63, 0x52, 0x0b, 0x76, 0x6f, 0x6c, 0x75,
	0x6d, 0x65, 0x53, 0x70, 0x65, 0x63, 0x73, 0x12, 0x40, 0x0a, 0x0a, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x61, 0x70,
	0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6a, 0x6f, 0x62, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x0a, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x2d, 0x0a, 0x07, 0x65, 0x6e, 0x76,
	0x56, 0x61, 0x72, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x61, 0x70, 0x69,
	0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x6e, 0x76, 0x56, 0x61, 0x72, 0x52,
	0x07, 0x65, 0x6e, 0x76, 0x56, 0x61, 0x72, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6d, 0x6d,
	0x61, 0x6e, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x61,
	0x6e, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x36, 0x0a, 0x06, 0x6d, 0x61, 0x73, 0x74,
	0x65, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e,
	0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6a, 0x6f, 0x62, 0x2e, 0x76, 0x31, 0x2e,
	0x54, 0x61, 0x73, 0x6b, 0x53, 0x70, 0x65, 0x63, 0x52, 0x06, 0x6d, 0x61, 0x73, 0x74, 0x65, 0x72,
	0x12, 0x36, 0x0a, 0x06, 0x77, 0x6f, 0x72, 0x6b, 0x65, 0x72, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f,
	0x2e, 0x6a, 0x6f, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x70, 0x65, 0x63,
	0x52, 0x06, 0x77, 0x6f, 0x72, 0x6b, 0x65, 0x72, 0x12, 0x45, 0x0a, 0x0b, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x53, 0x70, 0x65, 0x63, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e,
	0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x53, 0x70,
	0x65, 0x63, 0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x53, 0x70, 0x65, 0x63, 0x73, 0x22,
	0xe6, 0x03, 0x0a, 0x19, 0x53, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x69,
	0x6e, 0x67, 0x4a, 0x6f, 0x62, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x12, 0x2e, 0x0a,
	0x12, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x45, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x12, 0x74, 0x65, 0x6e, 0x73, 0x6f,
	0x72, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x40, 0x0a,
	0x0a, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69,
	0x6f, 0x2e, 0x6a, 0x6f, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x43,
	0x6f, 0x64, 0x65, 0x52, 0x0a, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12,
	0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x6d, 0x61,
	0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x12,
	0x2d, 0x0a, 0x07, 0x65, 0x6e, 0x76, 0x56, 0x61, 0x72, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x13, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45,
	0x6e, 0x76, 0x56, 0x61, 0x72, 0x52, 0x07, 0x65, 0x6e, 0x76, 0x56, 0x61, 0x72, 0x73, 0x12, 0x39,
	0x0a, 0x0b, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x53, 0x70, 0x65, 0x63, 0x73, 0x18, 0x06, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x53, 0x70, 0x65, 0x63, 0x52, 0x0b, 0x76, 0x6f,
	0x6c, 0x75, 0x6d, 0x65, 0x53, 0x70, 0x65, 0x63, 0x73, 0x12, 0x40, 0x0a, 0x0d, 0x73, 0x70, 0x65,
	0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x53,
	0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0d, 0x73, 0x70,
	0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x34, 0x0a, 0x15, 0x6e,
	0x6f, 0x64, 0x65, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x6e, 0x6f, 0x64, 0x65,
	0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x45, 0x0a, 0x0b, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x53, 0x70, 0x65, 0x63, 0x73,
	0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69,
	0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x53, 0x70, 0x65, 0x63, 0x52, 0x0b, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x53, 0x70, 0x65, 0x63, 0x73, 0x22, 0xa9, 0x03, 0x0a, 0x08, 0x54, 0x61, 0x73,
	0x6b, 0x53, 0x70, 0x65, 0x63, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x70,
	0x6c, 0x69, 0x63, 0x61, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x72, 0x65, 0x70,
	0x6c, 0x69, 0x63, 0x61, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x12,
	0x14, 0x0a, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x69, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x2d, 0x0a, 0x07, 0x65, 0x6e, 0x76, 0x56, 0x61, 0x72, 0x73,
	0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x6e, 0x76, 0x56, 0x61, 0x72, 0x52, 0x07, 0x65, 0x6e, 0x76,
	0x56, 0x61, 0x72, 0x73, 0x12, 0x39, 0x0a, 0x0b, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x53, 0x70,
	0x65, 0x63, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x61, 0x70, 0x69, 0x73,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x53, 0x70,
	0x65, 0x63, 0x52, 0x0b, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x53, 0x70, 0x65, 0x63, 0x73, 0x12,
	0x40, 0x0a, 0x0d, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x0d, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x34, 0x0a, 0x15, 0x6e, 0x6f, 0x64, 0x65, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x15, 0x6e, 0x6f, 0x64, 0x65, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x6c, 0x6f, 0x74, 0x73,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x73, 0x6c, 0x6f, 0x74, 0x73, 0x12, 0x45, 0x0a,
	0x0b, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x53, 0x70, 0x65, 0x63, 0x73, 0x18, 0x0a, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64,
	0x69, 0x6f, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x53, 0x70, 0x65, 0x63, 0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x53,
	0x70, 0x65, 0x63, 0x73, 0x22, 0xe1, 0x03, 0x0a, 0x0a, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x43,
	0x6f, 0x64, 0x65, 0x12, 0x56, 0x0a, 0x0d, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x46, 0x73, 0x53, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69,
	0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6a, 0x6f, 0x62, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x2e, 0x43, 0x6c, 0x6f,
	0x75, 0x64, 0x46, 0x73, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x48, 0x00, 0x52, 0x0d, 0x63, 0x6c,
	0x6f, 0x75, 0x64, 0x46, 0x73, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x4a, 0x0a, 0x09, 0x67,
	0x69, 0x74, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a,
	0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6a,
	0x6f, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x43, 0x6f, 0x64, 0x65,
	0x2e, 0x47, 0x69, 0x74, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x48, 0x00, 0x52, 0x09, 0x67, 0x69,
	0x74, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x53, 0x0a, 0x0c, 0x63, 0x75, 0x62, 0x65, 0x46,
	0x73, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e,
	0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6a, 0x6f,
	0x62, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x2e,
	0x43, 0x75, 0x62, 0x65, 0x46, 0x73, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x48, 0x00, 0x52, 0x0c,
	0x63, 0x75, 0x62, 0x65, 0x46, 0x73, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x1e, 0x0a, 0x0a,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x1a, 0x49, 0x0a, 0x0d,
	0x43, 0x6c, 0x6f, 0x75, 0x64, 0x46, 0x73, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x1e, 0x0a,
	0x0a, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a,
	0x07, 0x73, 0x75, 0x62, 0x50, 0x61, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x73, 0x75, 0x62, 0x50, 0x61, 0x74, 0x68, 0x1a, 0x35, 0x0a, 0x09, 0x47, 0x69, 0x74, 0x53, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x62, 0x72, 0x61, 0x6e, 0x63, 0x68,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x62, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x1a, 0x2e,
	0x0a, 0x0c, 0x43, 0x75, 0x62, 0x65, 0x46, 0x73, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x1e,
	0x0a, 0x0a, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x42, 0x08,
	0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x22, 0x9d, 0x04, 0x0a, 0x09, 0x4a, 0x6f, 0x62,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x18, 0x0a, 0x07,
	0x70, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x70,
	0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x75, 0x6e, 0x6e, 0x69, 0x6e,
	0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x72, 0x75, 0x6e, 0x6e, 0x69, 0x6e, 0x67,
	0x12, 0x1c, 0x0a, 0x09, 0x73, 0x75, 0x63, 0x63, 0x65, 0x65, 0x64, 0x65, 0x64, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x09, 0x73, 0x75, 0x63, 0x63, 0x65, 0x65, 0x64, 0x65, 0x64, 0x12, 0x16,
	0x0a, 0x06, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06,
	0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e,
	0x61, 0x74, 0x69, 0x6e, 0x67, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x74, 0x65, 0x72,
	0x6d, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x18, 0x0a, 0x07, 0x75, 0x6e, 0x6b, 0x6e,
	0x6f, 0x77, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x75, 0x6e, 0x6b, 0x6e, 0x6f,
	0x77, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1e, 0x0a, 0x0a,
	0x72, 0x65, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0a, 0x72, 0x65, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x16, 0x0a, 0x06,
	0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65,
	0x61, 0x73, 0x6f, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x2e,
	0x0a, 0x12, 0x6c, 0x61, 0x73, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x54, 0x69, 0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x6c, 0x61, 0x73, 0x74,
	0x54, 0x72, 0x61, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x28,
	0x0a, 0x0f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x42, 0x0a, 0x0a, 0x63, 0x6f, 0x6e, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x0e, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x61,
	0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6a, 0x6f, 0x62,
	0x2e, 0x76, 0x31, 0x2e, 0x4a, 0x6f, 0x62, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x0a, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x28, 0x0a, 0x0f,
	0x72, 0x75, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x72, 0x75, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x44, 0x75,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65,
	0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6c, 0x75,
	0x73, 0x74, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x55, 0x0a, 0x11, 0x4c, 0x69, 0x73, 0x74,
	0x4a, 0x6f, 0x62, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x40, 0x0a,
	0x0a, 0x74, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69,
	0x6f, 0x2e, 0x6a, 0x6f, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x0a, 0x74, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22,
	0xdf, 0x05, 0x0a, 0x0a, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x14,
	0x0a, 0x05, 0x70, 0x68, 0x61, 0x73, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x70,
	0x68, 0x61, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x6f, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x6f, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x22,
	0x0a, 0x0c, 0x70, 0x6f, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x70, 0x6f, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x6f, 0x64, 0x49, 0x50, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x70, 0x6f, 0x64, 0x49, 0x50, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6c, 0x75, 0x73,
	0x74, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74,
	0x65, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f,
	0x6e, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x16,
	0x0a, 0x06, 0x6e, 0x6f, 0x64, 0x65, 0x49, 0x50, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x6e, 0x6f, 0x64, 0x65, 0x49, 0x50, 0x12, 0x28, 0x0a, 0x0f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x12, 0x28, 0x0a, 0x0f, 0x66, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x66, 0x69, 0x6e, 0x69, 0x73,
	0x68, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x6d,
	0x61, 0x67, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65,
	0x12, 0x43, 0x0a, 0x0a, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x0c,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74,
	0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6a, 0x6f, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x61, 0x73, 0x6b,
	0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0a, 0x63, 0x6f, 0x6e, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x22, 0x0a, 0x0c, 0x72, 0x65, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x72, 0x65, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x61,
	0x73, 0x6f, 0x6e, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f,
	0x6e, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x0f, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x74,
	0x61, 0x73, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74,
	0x61, 0x73, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x45,
	0x6e, 0x76, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x45, 0x6e,
	0x76, 0x12, 0x16, 0x0a, 0x06, 0x68, 0x6f, 0x73, 0x74, 0x49, 0x50, 0x18, 0x12, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x68, 0x6f, 0x73, 0x74, 0x49, 0x50, 0x12, 0x16, 0x0a, 0x06, 0x74, 0x61, 0x73,
	0x6b, 0x49, 0x44, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49,
	0x44, 0x12, 0x1e, 0x0a, 0x0a, 0x6a, 0x6f, 0x62, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18,
	0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6a, 0x6f, 0x62, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x12, 0x40, 0x0a, 0x1b, 0x68, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x69, 0x63, 0x61, 0x6c, 0x4c,
	0x6f, 0x67, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x18, 0x15, 0x20, 0x01, 0x28, 0x03, 0x52, 0x1b, 0x68, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x69, 0x63,
	0x61, 0x6c, 0x4c, 0x6f, 0x67, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x12, 0x3c, 0x0a, 0x19, 0x68, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x69, 0x63, 0x61,
	0x6c, 0x4c, 0x6f, 0x67, 0x45, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x18, 0x16, 0x20, 0x01, 0x28, 0x03, 0x52, 0x19, 0x68, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x69, 0x63,
	0x61, 0x6c, 0x4c, 0x6f, 0x67, 0x45, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x22, 0x51, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x4a, 0x6f, 0x62, 0x49, 0x44, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72,
	0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6a, 0x6f,
	0x62, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6a, 0x6f, 0x62,
	0x4e, 0x61, 0x6d, 0x65, 0x22, 0x28, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x4a, 0x6f, 0x62, 0x49, 0x44,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6a, 0x6f, 0x62, 0x49,
	0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6a, 0x6f, 0x62, 0x49, 0x44, 0x22, 0x9d,
	0x01, 0x0a, 0x0d, 0x54, 0x61, 0x73, 0x6b, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2e,
	0x0a, 0x12, 0x6c, 0x61, 0x73, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x54, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x6c, 0x61, 0x73, 0x74,
	0x54, 0x72, 0x61, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x99,
	0x02, 0x0a, 0x0c, 0x4a, 0x6f, 0x62, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x14, 0x0a, 0x05, 0x70, 0x68, 0x61, 0x73, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x70, 0x68, 0x61, 0x73, 0x65, 0x12, 0x2e, 0x0a, 0x12, 0x6c, 0x61, 0x73, 0x74, 0x54, 0x72, 0x61,
	0x6e, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x12, 0x6c, 0x61, 0x73, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x18, 0x0a,
	0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x5c, 0x0a, 0x0f, 0x63, 0x6f, 0x6e, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x32, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f,
	0x2e, 0x6a, 0x6f, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x4a, 0x6f, 0x62, 0x43, 0x6f, 0x6e, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x0f, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x33, 0x0a, 0x0f, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x08, 0x0a, 0x04, 0x54, 0x72, 0x75, 0x65,
	0x10, 0x00, 0x12, 0x09, 0x0a, 0x05, 0x46, 0x61, 0x6c, 0x73, 0x65, 0x10, 0x01, 0x12, 0x0b, 0x0a,
	0x07, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x02, 0x22, 0x50, 0x0a, 0x0e, 0x53, 0x74,
	0x6f, 0x70, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x0d,
	0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6a, 0x6f, 0x62, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x6a, 0x6f, 0x62, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x71, 0x0a, 0x11,
	0x47, 0x65, 0x74, 0x4a, 0x6f, 0x62, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6a, 0x6f, 0x62, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6a, 0x6f, 0x62, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x1c, 0x0a, 0x09, 0x69, 0x73, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x69, 0x73, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x22,
	0x6d, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6a, 0x6f, 0x62, 0x4e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6a, 0x6f, 0x62, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x1c, 0x0a, 0x09, 0x69, 0x73, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x69, 0x73, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x22, 0x70,
	0x0a, 0x10, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6a, 0x6f, 0x62, 0x4e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6a, 0x6f, 0x62, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x69, 0x73, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x69, 0x73, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64,
	0x22, 0x6a, 0x0a, 0x18, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4a, 0x6f, 0x62, 0x54, 0x65, 0x6d,
	0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x0d,
	0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x28, 0x0a, 0x0f, 0x6a, 0x6f, 0x62, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6a, 0x6f, 0x62,
	0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xa6, 0x01, 0x0a,
	0x12, 0x47, 0x65, 0x74, 0x4a, 0x6f, 0x62, 0x54, 0x61, 0x73, 0x6b, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6a, 0x6f, 0x62,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6a, 0x6f, 0x62, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x69, 0x73, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x69, 0x73, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x64, 0x12, 0x32, 0x0a, 0x14, 0x73, 0x68, 0x6f, 0x77, 0x54, 0x68, 0x65, 0x4c, 0x61, 0x74, 0x65,
	0x73, 0x74, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x14, 0x73, 0x68, 0x6f, 0x77, 0x54, 0x68, 0x65, 0x4c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x56, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x73, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x4a, 0x6f, 0x62, 0x56,
	0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x0d,
	0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6a, 0x6f, 0x62, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x6a, 0x6f, 0x62, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09,
	0x69, 0x73, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x69, 0x73, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x22, 0x85, 0x03, 0x0a, 0x0f, 0x4c,
	0x69, 0x73, 0x74, 0x4a, 0x6f, 0x62, 0x73, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x18,
	0x0a, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1c,
	0x0a, 0x09, 0x71, 0x75, 0x65, 0x75, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x71, 0x75, 0x65, 0x75, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07,
	0x6a, 0x6f, 0x62, 0x54, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6a,
	0x6f, 0x62, 0x54, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x69,
	0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06,
	0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65,
	0x67, 0x69, 0x6f, 0x6e, 0x12, 0x24, 0x0a, 0x0d, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x45, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x65,
	0x6d, 0x62, 0x65, 0x72, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x65, 0x6d, 0x62,
	0x65, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69,
	0x7a, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69,
	0x7a, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x28, 0x0a, 0x0f, 0x6a, 0x6f, 0x62, 0x54,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0f, 0x6a, 0x6f, 0x62, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x22, 0x57, 0x0a, 0x10, 0x4c, 0x69, 0x73, 0x74, 0x4a, 0x6f, 0x62, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2d, 0x0a, 0x04, 0x6a, 0x6f, 0x62, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74,
	0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6a, 0x6f, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x4a, 0x6f, 0x62, 0x52,
	0x04, 0x6a, 0x6f, 0x62, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x22, 0xaf, 0x01, 0x0a, 0x0b,
	0x4a, 0x6f, 0x62, 0x54, 0x69, 0x6d, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69,
	0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x22, 0x8f, 0x01,
	0x0a, 0x09, 0x4a, 0x6f, 0x62, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x1e, 0x0a, 0x0a, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x12,
	0x1e, 0x0a, 0x0a, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x18, 0x0a, 0x07, 0x73, 0x75, 0x62, 0x50, 0x61, 0x74, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x73, 0x75, 0x62, 0x50, 0x61, 0x74, 0x68, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x63, 0x6f,
	0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x22,
	0x4b, 0x0a, 0x0e, 0x4c, 0x69, 0x73, 0x74, 0x4a, 0x6f, 0x62, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65,
	0x73, 0x12, 0x39, 0x0a, 0x07, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64,
	0x69, 0x6f, 0x2e, 0x6a, 0x6f, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x4a, 0x6f, 0x62, 0x56, 0x6f, 0x6c,
	0x75, 0x6d, 0x65, 0x52, 0x07, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x73, 0x22, 0x86, 0x01, 0x0a,
	0x12, 0x53, 0x74, 0x6f, 0x70, 0x4a, 0x6f, 0x62, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6a, 0x6f, 0x62,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6a, 0x6f, 0x62, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x44, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x44, 0x12, 0x18, 0x0a, 0x07, 0x70,
	0x6f, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x6f,
	0x64, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x54, 0x0a, 0x12, 0x52, 0x65, 0x64, 0x65, 0x70, 0x6c, 0x6f,
	0x79, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x77,
	0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x18, 0x0a, 0x07, 0x6a, 0x6f, 0x62, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x6a, 0x6f, 0x62, 0x4e, 0x61, 0x6d, 0x65, 0x2a, 0x30, 0x0a, 0x0b, 0x54,
	0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x09, 0x0a, 0x05, 0x54, 0x69,
	0x6d, 0x65, 0x72, 0x10, 0x00, 0x12, 0x09, 0x0a, 0x05, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x10, 0x01,
	0x12, 0x0b, 0x0a, 0x07, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x10, 0x02, 0x2a, 0x59, 0x0a,
	0x11, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65,
	0x67, 0x79, 0x12, 0x16, 0x0a, 0x12, 0x49, 0x6d, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x74, 0x65, 0x46,
	0x69, 0x72, 0x73, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x53, 0x61,
	0x76, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x4f, 0x6e, 0x6c, 0x79, 0x10, 0x01,
	0x12, 0x16, 0x0a, 0x12, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x64, 0x45, 0x78, 0x65,
	0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x10, 0x02, 0x2a, 0x94, 0x01, 0x0a, 0x0c, 0x54, 0x72, 0x69,
	0x67, 0x67, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x19, 0x0a, 0x15, 0x54, 0x72, 0x69,
	0x67, 0x67, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x65, 0x5f, 0x49, 0x6e, 0x61, 0x63, 0x74, 0x69,
	0x76, 0x65, 0x10, 0x00, 0x12, 0x1a, 0x0a, 0x16, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x5f, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6e, 0x67, 0x10, 0x01,
	0x12, 0x17, 0x0a, 0x13, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x5f, 0x50, 0x61, 0x75, 0x73, 0x65, 0x64, 0x10, 0x02, 0x12, 0x1a, 0x0a, 0x16, 0x54, 0x72, 0x69,
	0x67, 0x67, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x65, 0x5f, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65,
	0x74, 0x65, 0x64, 0x10, 0x03, 0x12, 0x18, 0x0a, 0x14, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x5f, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x64, 0x10, 0x04, 0x2a,
	0x29, 0x0a, 0x08, 0x50, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x12, 0x07, 0x0a, 0x03, 0x4c,
	0x4f, 0x57, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x4e, 0x4f, 0x52, 0x4d, 0x41, 0x4c, 0x10, 0x01,
	0x12, 0x08, 0x0a, 0x04, 0x48, 0x49, 0x47, 0x48, 0x10, 0x02, 0x2a, 0x3c, 0x0a, 0x07, 0x4a, 0x6f,
	0x62, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0e, 0x0a, 0x0a, 0x50, 0x79, 0x54, 0x6f, 0x72, 0x63, 0x68,
	0x44, 0x44, 0x50, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x44, 0x65, 0x65, 0x70, 0x53, 0x70, 0x65,
	0x65, 0x64, 0x10, 0x01, 0x12, 0x12, 0x0a, 0x0e, 0x53, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x54, 0x72,
	0x61, 0x69, 0x6e, 0x69, 0x6e, 0x67, 0x10, 0x02, 0x2a, 0x7f, 0x0a, 0x09, 0x54, 0x61, 0x73, 0x6b,
	0x50, 0x68, 0x61, 0x73, 0x65, 0x12, 0x15, 0x0a, 0x11, 0x54, 0x61, 0x73, 0x6b, 0x50, 0x68, 0x61,
	0x73, 0x65, 0x5f, 0x50, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x10, 0x00, 0x12, 0x15, 0x0a, 0x11,
	0x54, 0x61, 0x73, 0x6b, 0x50, 0x68, 0x61, 0x73, 0x65, 0x5f, 0x52, 0x75, 0x6e, 0x6e, 0x69, 0x6e,
	0x67, 0x10, 0x01, 0x12, 0x17, 0x0a, 0x13, 0x54, 0x61, 0x73, 0x6b, 0x50, 0x68, 0x61, 0x73, 0x65,
	0x5f, 0x53, 0x75, 0x63, 0x63, 0x65, 0x65, 0x64, 0x65, 0x64, 0x10, 0x02, 0x12, 0x14, 0x0a, 0x10,
	0x54, 0x61, 0x73, 0x6b, 0x50, 0x68, 0x61, 0x73, 0x65, 0x5f, 0x46, 0x61, 0x69, 0x6c, 0x65, 0x64,
	0x10, 0x03, 0x12, 0x15, 0x0a, 0x11, 0x54, 0x61, 0x73, 0x6b, 0x50, 0x68, 0x61, 0x73, 0x65, 0x5f,
	0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x04, 0x2a, 0x72, 0x0a, 0x11, 0x54, 0x61, 0x73,
	0x6b, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x13,
	0x0a, 0x0f, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x73, 0x52, 0x65, 0x61, 0x64,
	0x79, 0x10, 0x00, 0x12, 0x12, 0x0a, 0x0e, 0x50, 0x6f, 0x64, 0x49, 0x6e, 0x69, 0x74, 0x69, 0x61,
	0x6c, 0x69, 0x7a, 0x65, 0x64, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x50, 0x6f, 0x64, 0x52, 0x65,
	0x61, 0x64, 0x79, 0x10, 0x02, 0x12, 0x10, 0x0a, 0x0c, 0x50, 0x6f, 0x64, 0x53, 0x63, 0x68, 0x65,
	0x64, 0x75, 0x6c, 0x65, 0x64, 0x10, 0x03, 0x12, 0x14, 0x0a, 0x10, 0x44, 0x69, 0x73, 0x72, 0x75,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x10, 0x04, 0x2a, 0xf5, 0x01,
	0x0a, 0x08, 0x4a, 0x6f, 0x62, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x14, 0x0a, 0x10, 0x4a, 0x6f,
	0x62, 0x53, 0x74, 0x61, 0x74, 0x65, 0x5f, 0x50, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x10, 0x00,
	0x12, 0x14, 0x0a, 0x10, 0x4a, 0x6f, 0x62, 0x53, 0x74, 0x61, 0x74, 0x65, 0x5f, 0x52, 0x75, 0x6e,
	0x6e, 0x69, 0x6e, 0x67, 0x10, 0x01, 0x12, 0x15, 0x0a, 0x11, 0x4a, 0x6f, 0x62, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x5f, 0x41, 0x62, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x10, 0x03, 0x12, 0x14, 0x0a,
	0x10, 0x4a, 0x6f, 0x62, 0x53, 0x74, 0x61, 0x74, 0x65, 0x5f, 0x41, 0x62, 0x6f, 0x72, 0x74, 0x65,
	0x64, 0x10, 0x04, 0x12, 0x17, 0x0a, 0x13, 0x4a, 0x6f, 0x62, 0x53, 0x74, 0x61, 0x74, 0x65, 0x5f,
	0x52, 0x65, 0x73, 0x74, 0x61, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x10, 0x05, 0x12, 0x17, 0x0a, 0x13,
	0x4a, 0x6f, 0x62, 0x53, 0x74, 0x61, 0x74, 0x65, 0x5f, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74,
	0x69, 0x6e, 0x67, 0x10, 0x06, 0x12, 0x16, 0x0a, 0x12, 0x4a, 0x6f, 0x62, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x5f, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x10, 0x07, 0x12, 0x18, 0x0a,
	0x14, 0x4a, 0x6f, 0x62, 0x53, 0x74, 0x61, 0x74, 0x65, 0x5f, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e,
	0x61, 0x74, 0x69, 0x6e, 0x67, 0x10, 0x08, 0x12, 0x17, 0x0a, 0x13, 0x4a, 0x6f, 0x62, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x5f, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x74, 0x65, 0x64, 0x10, 0x09,
	0x12, 0x13, 0x0a, 0x0f, 0x4a, 0x6f, 0x62, 0x53, 0x74, 0x61, 0x74, 0x65, 0x5f, 0x46, 0x61, 0x69,
	0x6c, 0x65, 0x64, 0x10, 0x0a, 0x2a, 0x63, 0x0a, 0x08, 0x4a, 0x6f, 0x62, 0x50, 0x68, 0x61, 0x73,
	0x65, 0x12, 0x0b, 0x0a, 0x07, 0x49, 0x6e, 0x51, 0x75, 0x65, 0x75, 0x65, 0x10, 0x00, 0x12, 0x17,
	0x0a, 0x13, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x50, 0x72, 0x65, 0x70, 0x61, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x52, 0x75, 0x6e, 0x6e, 0x69,
	0x6e, 0x67, 0x10, 0x02, 0x12, 0x0c, 0x0a, 0x08, 0x46, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x65, 0x64,
	0x10, 0x03, 0x12, 0x0a, 0x0a, 0x06, 0x4b, 0x69, 0x6c, 0x6c, 0x65, 0x64, 0x10, 0x04, 0x12, 0x0a,
	0x0a, 0x06, 0x46, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x10, 0x05, 0x32, 0xca, 0x24, 0x0a, 0x0a, 0x4a,
	0x6f, 0x62, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0xa0, 0x01, 0x0a, 0x11, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x4a, 0x6f, 0x62, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x12,
	0x36, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e,
	0x6a, 0x6f, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x4a, 0x6f, 0x62, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22,
	0x3b, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x35, 0x3a, 0x01, 0x2a, 0x22, 0x30, 0x2f, 0x61, 0x70, 0x69,
	0x73, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2f, 0x7b,
	0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x6a,
	0x6f, 0x62, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x12, 0xa6, 0x01, 0x0a,
	0x11, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4a, 0x6f, 0x62, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61,
	0x74, 0x65, 0x12, 0x36, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64,
	0x69, 0x6f, 0x2e, 0x6a, 0x6f, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x4f, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4a, 0x6f, 0x62, 0x54, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70,
	0x74, 0x79, 0x22, 0x41, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x3b, 0x3a, 0x01, 0x2a, 0x1a, 0x36, 0x2f,
	0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x2f, 0x7b, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x7d, 0x2f, 0x6a, 0x6f, 0x62, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x2f, 0x7b,
	0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x12, 0xab, 0x01, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x4a, 0x6f, 0x62,
	0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x12, 0x2b, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e,
	0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6a, 0x6f, 0x62, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x4a, 0x6f, 0x62, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73,
	0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6a, 0x6f, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x4a, 0x6f, 0x62,
	0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x22, 0x49, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x43,
	0x12, 0x41, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x2f, 0x7b, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x6a, 0x6f, 0x62, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x2f, 0x7b, 0x6a, 0x6f, 0x62, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x7d, 0x12, 0xb7, 0x01, 0x0a, 0x14, 0x4c, 0x69, 0x73, 0x74, 0x4a, 0x6f, 0x62, 0x54,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x56, 0x69, 0x65, 0x77, 0x73, 0x12, 0x31, 0x2e, 0x61,
	0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6a, 0x6f, 0x62,
	0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4a, 0x6f, 0x62, 0x54, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x56, 0x69, 0x65, 0x77, 0x73, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x1a,
	0x32, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e,
	0x6a, 0x6f, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4a, 0x6f, 0x62, 0x54, 0x65,
	0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x56, 0x69, 0x65, 0x77, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x38, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x32, 0x12, 0x30, 0x2f, 0x61, 0x70,
	0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2f,
	0x7b, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f,
	0x6a, 0x6f, 0x62, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x12, 0xa6, 0x01,
	0x0a, 0x11, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4a, 0x6f, 0x62, 0x54, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x12, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75,
	0x64, 0x69, 0x6f, 0x2e, 0x6a, 0x6f, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x4a, 0x6f, 0x62, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x49, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x43, 0x2a, 0x41, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x6f,
	0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2f, 0x7b, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x6a, 0x6f, 0x62, 0x5f, 0x74, 0x65, 0x6d, 0x70,
	0x6c, 0x61, 0x74, 0x65, 0x2f, 0x7b, 0x6a, 0x6f, 0x62, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x12, 0xc7, 0x01, 0x0a, 0x18, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x4a, 0x6f, 0x62, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x54, 0x72, 0x69, 0x67,
	0x67, 0x65, 0x72, 0x12, 0x3d, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75,
	0x64, 0x69, 0x6f, 0x2e, 0x6a, 0x6f, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x4f, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4a, 0x6f, 0x62, 0x54, 0x65, 0x6d, 0x70,
	0x6c, 0x61, 0x74, 0x65, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x54, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x4e, 0x3a, 0x01, 0x2a, 0x22, 0x49, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f,
	0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2f, 0x7b, 0x77, 0x6f, 0x72, 0x6b, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x6a, 0x6f, 0x62, 0x5f, 0x74, 0x65,
	0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x2f, 0x7b, 0x6a, 0x6f, 0x62, 0x54, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72,
	0x12, 0xd5, 0x01, 0x0a, 0x18, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4a, 0x6f, 0x62, 0x54, 0x65,
	0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x12, 0x3d, 0x2e,
	0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6a, 0x6f,
	0x62, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x4a, 0x6f, 0x62, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x54, 0x72,
	0x69, 0x67, 0x67, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x22, 0x62, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x5c, 0x3a, 0x01, 0x2a, 0x1a,
	0x57, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x2f, 0x7b, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x7d, 0x2f, 0x6a, 0x6f, 0x62, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65,
	0x2f, 0x7b, 0x6a, 0x6f, 0x62, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x7d, 0x2f, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x2f, 0x7b, 0x74, 0x72, 0x69, 0x67,
	0x67, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x12, 0xca, 0x01, 0x0a, 0x18, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x4a, 0x6f, 0x62, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x54, 0x72,
	0x69, 0x67, 0x67, 0x65, 0x72, 0x12, 0x35, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73,
	0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6a, 0x6f, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x4a, 0x6f, 0x62, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x54, 0x72,
	0x69, 0x67, 0x67, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x22, 0x5f, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x59, 0x2a, 0x57, 0x2f, 0x61,
	0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x2f, 0x7b, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d,
	0x2f, 0x6a, 0x6f, 0x62, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x2f, 0x7b, 0x6a,
	0x6f, 0x62, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f,
	0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x2f, 0x7b, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72,
	0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x12, 0xcb, 0x01, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x4a, 0x6f, 0x62,
	0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x12,
	0x32, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e,
	0x6a, 0x6f, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4a, 0x6f, 0x62, 0x54, 0x65, 0x6d,
	0x70, 0x6c, 0x61, 0x74, 0x65, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75,
	0x64, 0x69, 0x6f, 0x2e, 0x6a, 0x6f, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x72, 0x69, 0x67, 0x67,
	0x65, 0x72, 0x22, 0x5f, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x59, 0x12, 0x57, 0x2f, 0x61, 0x70, 0x69,
	0x73, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2f, 0x7b,
	0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x6a,
	0x6f, 0x62, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x2f, 0x7b, 0x6a, 0x6f, 0x62,
	0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x74, 0x72,
	0x69, 0x67, 0x67, 0x65, 0x72, 0x2f, 0x7b, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x4e, 0x61,
	0x6d, 0x65, 0x7d, 0x12, 0xda, 0x01, 0x0a, 0x17, 0x4c, 0x69, 0x73, 0x74, 0x4a, 0x6f, 0x62, 0x54,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x73, 0x12,
	0x34, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e,
	0x6a, 0x6f, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4a, 0x6f, 0x62, 0x54, 0x65,
	0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x35, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73,
	0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6a, 0x6f, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x4a, 0x6f, 0x62, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x54, 0x72, 0x69, 0x67,
	0x67, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x52, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x4c, 0x12, 0x4a, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x77,
	0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2f, 0x7b, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x6a, 0x6f, 0x62, 0x5f, 0x74, 0x65, 0x6d,
	0x70, 0x6c, 0x61, 0x74, 0x65, 0x2f, 0x7b, 0x6a, 0x6f, 0x62, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61,
	0x74, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x73,
	0x12, 0xd1, 0x01, 0x0a, 0x17, 0x50, 0x61, 0x75, 0x73, 0x65, 0x4a, 0x6f, 0x62, 0x54, 0x65, 0x6d,
	0x70, 0x6c, 0x61, 0x74, 0x65, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x12, 0x34, 0x2e, 0x61,
	0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6a, 0x6f, 0x62,
	0x2e, 0x76, 0x31, 0x2e, 0x50, 0x61, 0x75, 0x73, 0x65, 0x4a, 0x6f, 0x62, 0x54, 0x65, 0x6d, 0x70,
	0x6c, 0x61, 0x74, 0x65, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x68, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x62, 0x3a, 0x01, 0x2a, 0x22, 0x5d, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f,
	0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2f, 0x7b, 0x77, 0x6f, 0x72, 0x6b, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x6a, 0x6f, 0x62, 0x5f, 0x74, 0x65,
	0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x2f, 0x7b, 0x6a, 0x6f, 0x62, 0x54, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72,
	0x2f, 0x7b, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x70,
	0x61, 0x75, 0x73, 0x65, 0x12, 0xd7, 0x01, 0x0a, 0x19, 0x52, 0x65, 0x63, 0x6f, 0x76, 0x65, 0x72,
	0x4a, 0x6f, 0x62, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x54, 0x72, 0x69, 0x67, 0x67,
	0x65, 0x72, 0x12, 0x36, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64,
	0x69, 0x6f, 0x2e, 0x6a, 0x6f, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x63, 0x6f, 0x76, 0x65,
	0x72, 0x4a, 0x6f, 0x62, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x54, 0x72, 0x69, 0x67,
	0x67, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70,
	0x74, 0x79, 0x22, 0x6a, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x64, 0x3a, 0x01, 0x2a, 0x22, 0x5f, 0x2f,
	0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x2f, 0x7b, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x7d, 0x2f, 0x6a, 0x6f, 0x62, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x2f, 0x7b,
	0x6a, 0x6f, 0x62, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d,
	0x2f, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x2f, 0x7b, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65,
	0x72, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x12, 0x90,
	0x01, 0x0a, 0x09, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x4a, 0x6f, 0x62, 0x12, 0x26, 0x2e, 0x61,
	0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6a, 0x6f, 0x62,
	0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74,
	0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6a, 0x6f, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x32, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x2c, 0x3a, 0x01, 0x2a, 0x22, 0x27, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f,
	0x76, 0x31, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2f, 0x7b, 0x77, 0x6f,
	0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x6a, 0x6f, 0x62,
	0x73, 0x12, 0x89, 0x01, 0x0a, 0x07, 0x53, 0x74, 0x6f, 0x70, 0x4a, 0x6f, 0x62, 0x12, 0x24, 0x2e,
	0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6a, 0x6f,
	0x62, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x6f, 0x70, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x40, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x3a, 0x3a, 0x01, 0x2a, 0x22, 0x35, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31,
	0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2f, 0x7b, 0x77, 0x6f, 0x72, 0x6b,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x6a, 0x6f, 0x62, 0x2f, 0x7b,
	0x6a, 0x6f, 0x62, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x73, 0x74, 0x6f, 0x70, 0x12, 0x88, 0x01,
	0x0a, 0x09, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4a, 0x6f, 0x62, 0x12, 0x26, 0x2e, 0x61, 0x70,
	0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6a, 0x6f, 0x62, 0x2e,
	0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x3b, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x35, 0x3a, 0x01, 0x2a, 0x1a, 0x30, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31,
	0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2f, 0x7b, 0x77, 0x6f, 0x72, 0x6b,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x6a, 0x6f, 0x62, 0x2f, 0x7b,
	0x6a, 0x6f, 0x62, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x12, 0x90, 0x01, 0x0a, 0x0a, 0x47, 0x65, 0x74,
	0x4a, 0x6f, 0x62, 0x42, 0x61, 0x73, 0x65, 0x12, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61,
	0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6a, 0x6f, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x47,
	0x65, 0x74, 0x4a, 0x6f, 0x62, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f,
	0x2e, 0x6a, 0x6f, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x4a, 0x6f, 0x62, 0x22, 0x3e, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x38, 0x12, 0x36, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x6f,
	0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2f, 0x7b, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x6a, 0x6f, 0x62, 0x73, 0x2f, 0x7b, 0x6a, 0x6f,
	0x62, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x62, 0x61, 0x73, 0x65, 0x12, 0x90, 0x01, 0x0a, 0x0c,
	0x47, 0x65, 0x74, 0x4a, 0x6f, 0x62, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x23, 0x2e, 0x61,
	0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6a, 0x6f, 0x62,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69,
	0x6f, 0x2e, 0x6a, 0x6f, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x4a, 0x6f, 0x62, 0x22, 0x40, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x3a, 0x12, 0x38, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x77,
	0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2f, 0x7b, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x6a, 0x6f, 0x62, 0x73, 0x2f, 0x7b, 0x6a,
	0x6f, 0x62, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x85,
	0x01, 0x0a, 0x09, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4a, 0x6f, 0x62, 0x12, 0x26, 0x2e, 0x61,
	0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6a, 0x6f, 0x62,
	0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x38, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x32, 0x2a, 0x30, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x77,
	0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2f, 0x7b, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x6a, 0x6f, 0x62, 0x2f, 0x7b, 0x6a, 0x6f,
	0x62, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x12, 0x8a, 0x01, 0x0a, 0x08, 0x4c, 0x69, 0x73, 0x74, 0x4a,
	0x6f, 0x62, 0x73, 0x12, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75,
	0x64, 0x69, 0x6f, 0x2e, 0x6a, 0x6f, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4a,
	0x6f, 0x62, 0x73, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x1a, 0x26, 0x2e, 0x61, 0x70, 0x69,
	0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6a, 0x6f, 0x62, 0x2e, 0x76,
	0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4a, 0x6f, 0x62, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x2f, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x29, 0x12, 0x27, 0x2f, 0x61, 0x70, 0x69,
	0x73, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2f, 0x7b,
	0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x6a,
	0x6f, 0x62, 0x73, 0x12, 0xa4, 0x01, 0x0a, 0x11, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x65,
	0x6e, 0x73, 0x6f, 0x72, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x12, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x73,
	0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6a, 0x6f, 0x62, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x62, 0x6f, 0x61,
	0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74,
	0x79, 0x22, 0x47, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x41, 0x3a, 0x01, 0x2a, 0x22, 0x3c, 0x2f, 0x61,
	0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x2f, 0x7b, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d,
	0x2f, 0x6a, 0x6f, 0x62, 0x2f, 0x7b, 0x6a, 0x6f, 0x62, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x74,
	0x65, 0x6e, 0x73, 0x6f, 0x72, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x12, 0xb1, 0x01, 0x0a, 0x0e, 0x47,
	0x65, 0x74, 0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x12, 0x2b, 0x2e,
	0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6a, 0x6f,
	0x62, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x62, 0x6f,
	0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2c, 0x2e, 0x61, 0x70, 0x69,
	0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6a, 0x6f, 0x62, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x62, 0x6f, 0x61, 0x72, 0x64,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x44, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x3e,
	0x12, 0x3c, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x2f, 0x7b, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x6a, 0x6f, 0x62, 0x2f, 0x7b, 0x6a, 0x6f, 0x62, 0x4e, 0x61, 0x6d,
	0x65, 0x7d, 0x2f, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x12, 0xa0,
	0x01, 0x0a, 0x0b, 0x47, 0x65, 0x74, 0x4a, 0x6f, 0x62, 0x54, 0x61, 0x73, 0x6b, 0x73, 0x12, 0x28,
	0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6a,
	0x6f, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4a, 0x6f, 0x62, 0x54, 0x61, 0x73, 0x6b,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e,
	0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6a, 0x6f, 0x62, 0x2e, 0x76, 0x31, 0x2e,
	0x4c, 0x69, 0x73, 0x74, 0x4a, 0x6f, 0x62, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x22, 0x3e, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x38, 0x12, 0x36, 0x2f, 0x61, 0x70, 0x69, 0x73,
	0x2f, 0x76, 0x31, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2f, 0x7b, 0x77,
	0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x6a, 0x6f,
	0x62, 0x2f, 0x7b, 0x6a, 0x6f, 0x62, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x74, 0x61, 0x73, 0x6b,
	0x73, 0x12, 0xa2, 0x01, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x4a, 0x6f, 0x62, 0x56, 0x6f, 0x6c, 0x75,
	0x6d, 0x65, 0x73, 0x12, 0x29, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75,
	0x64, 0x69, 0x6f, 0x2e, 0x6a, 0x6f, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4a, 0x6f,
	0x62, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24,
	0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6a,
	0x6f, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4a, 0x6f, 0x62, 0x56, 0x6f, 0x6c,
	0x75, 0x6d, 0x65, 0x73, 0x22, 0x40, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x3a, 0x12, 0x38, 0x2f, 0x61,
	0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x2f, 0x7b, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d,
	0x2f, 0x6a, 0x6f, 0x62, 0x2f, 0x7b, 0x6a, 0x6f, 0x62, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x76,
	0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x73, 0x12, 0x9f, 0x01, 0x0a, 0x0b, 0x53, 0x74, 0x6f, 0x70, 0x4a,
	0x6f, 0x62, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x28, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69,
	0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6a, 0x6f, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74,
	0x6f, 0x70, 0x4a, 0x6f, 0x62, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x4e, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x48,
	0x3a, 0x01, 0x2a, 0x22, 0x43, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x6f,
	0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2f, 0x7b, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x6a, 0x6f, 0x62, 0x2f, 0x7b, 0x6a, 0x6f, 0x62,
	0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x74, 0x61, 0x73, 0x6b, 0x2f, 0x7b, 0x74, 0x61, 0x73, 0x6b,
	0x49, 0x44, 0x7d, 0x2f, 0x73, 0x74, 0x6f, 0x70, 0x12, 0x95, 0x01, 0x0a, 0x0b, 0x52, 0x65, 0x64,
	0x65, 0x70, 0x6c, 0x6f, 0x79, 0x4a, 0x6f, 0x62, 0x12, 0x28, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e,
	0x61, 0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6a, 0x6f, 0x62, 0x2e, 0x76, 0x31, 0x2e,
	0x52, 0x65, 0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x44, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x3e, 0x3a, 0x01, 0x2a, 0x22, 0x39, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f,
	0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2f, 0x7b, 0x77, 0x6f, 0x72, 0x6b, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x6a, 0x6f, 0x62, 0x2f, 0x7b, 0x6a,
	0x6f, 0x62, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79,
	0x12, 0xb2, 0x01, 0x0a, 0x0f, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4a, 0x6f, 0x62, 0x49, 0x73, 0x45,
	0x78, 0x69, 0x73, 0x74, 0x12, 0x2c, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74,
	0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6a, 0x6f, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x4a, 0x6f, 0x62, 0x49, 0x73, 0x45, 0x78, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x2d, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61, 0x69, 0x73, 0x74, 0x75, 0x64,
	0x69, 0x6f, 0x2e, 0x6a, 0x6f, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4a,
	0x6f, 0x62, 0x49, 0x73, 0x45, 0x78, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x42, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x3c, 0x12, 0x3a, 0x2f, 0x61, 0x70, 0x69, 0x73,
	0x2f, 0x76, 0x31, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2f, 0x7b, 0x77,
	0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x6a, 0x6f,
	0x62, 0x2f, 0x7b, 0x6a, 0x6f, 0x62, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x6a, 0x6f, 0x62, 0x5f,
	0x65, 0x78, 0x69, 0x73, 0x74, 0x12, 0x9f, 0x01, 0x0a, 0x0d, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x12, 0x2a, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x61,
	0x69, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x6a, 0x6f, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x4a, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x44, 0x3a, 0x01, 0x2a, 0x1a, 0x3f, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31,
	0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2f, 0x7b, 0x77, 0x6f, 0x72, 0x6b,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x6a, 0x6f, 0x62, 0x2f, 0x7b,
	0x6a, 0x6f, 0x62, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f,
	0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x42, 0x46, 0x5a, 0x44, 0x67, 0x69, 0x74, 0x2e, 0x6c,
	0x69, 0x61, 0x6e, 0x6a, 0x69, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64,
	0x6e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x2f, 0x6b, 0x69, 0x63, 0x2f, 0x6b, 0x69, 0x63, 0x2d, 0x70,
	0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x61, 0x69, 0x73,
	0x74, 0x75, 0x64, 0x69, 0x6f, 0x2f, 0x6a, 0x6f, 0x62, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_aistudio_job_v1_job_proto_rawDescOnce sync.Once
	file_aistudio_job_v1_job_proto_rawDescData = file_aistudio_job_v1_job_proto_rawDesc
)

func file_aistudio_job_v1_job_proto_rawDescGZIP() []byte {
	file_aistudio_job_v1_job_proto_rawDescOnce.Do(func() {
		file_aistudio_job_v1_job_proto_rawDescData = protoimpl.X.CompressGZIP(file_aistudio_job_v1_job_proto_rawDescData)
	})
	return file_aistudio_job_v1_job_proto_rawDescData
}

var file_aistudio_job_v1_job_proto_enumTypes = make([]protoimpl.EnumInfo, 11)
var file_aistudio_job_v1_job_proto_msgTypes = make([]protoimpl.MessageInfo, 62)
var file_aistudio_job_v1_job_proto_goTypes = []any{
	(TriggerType)(0),       // 0: apis.aistudio.job.v1.TriggerType
	(ExecutionStrategy)(0), // 1: apis.aistudio.job.v1.ExecutionStrategy
	(TriggerState)(0),      // 2: apis.aistudio.job.v1.TriggerState
	(Priority)(0),          // 3: apis.aistudio.job.v1.Priority
	(JobType)(0),           // 4: apis.aistudio.job.v1.JobType
	(TaskPhase)(0),         // 5: apis.aistudio.job.v1.TaskPhase
	(TaskConditionType)(0), // 6: apis.aistudio.job.v1.TaskConditionType
	(JobState)(0),          // 7: apis.aistudio.job.v1.JobState
	(JobPhase)(0),          // 8: apis.aistudio.job.v1.JobPhase
	(TimerTriggerOptions_TimerTriggerType)(0),       // 9: apis.aistudio.job.v1.TimerTriggerOptions.TimerTriggerType
	(JobCondition_ConditionStatus)(0),               // 10: apis.aistudio.job.v1.JobCondition.ConditionStatus
	(*CheckJobIsExistRequest)(nil),                  // 11: apis.aistudio.job.v1.CheckJobIsExistRequest
	(*CheckJobIsExistResponse)(nil),                 // 12: apis.aistudio.job.v1.CheckJobIsExistResponse
	(*UpdateMembersRequest)(nil),                    // 13: apis.aistudio.job.v1.UpdateMembersRequest
	(*JobTemplate)(nil),                             // 14: apis.aistudio.job.v1.JobTemplate
	(*JobTemplateView)(nil),                         // 15: apis.aistudio.job.v1.JobTemplateView
	(*Trigger)(nil),                                 // 16: apis.aistudio.job.v1.Trigger
	(*TimerTriggerOptions)(nil),                     // 17: apis.aistudio.job.v1.TimerTriggerOptions
	(*Cycle)(nil),                                   // 18: apis.aistudio.job.v1.Cycle
	(*JobTriggerRecord)(nil),                        // 19: apis.aistudio.job.v1.JobTriggerRecord
	(*PauseJobTemplateTriggerRequest)(nil),          // 20: apis.aistudio.job.v1.PauseJobTemplateTriggerRequest
	(*RecoverJobTemplateTriggerRequest)(nil),        // 21: apis.aistudio.job.v1.RecoverJobTemplateTriggerRequest
	(*CreateOrUpdateJobTemplateRequest)(nil),        // 22: apis.aistudio.job.v1.CreateOrUpdateJobTemplateRequest
	(*GetJobTemplateRequest)(nil),                   // 23: apis.aistudio.job.v1.GetJobTemplateRequest
	(*ListJobTemplateViewsOptions)(nil),             // 24: apis.aistudio.job.v1.ListJobTemplateViewsOptions
	(*ListJobTemplateViewsResponse)(nil),            // 25: apis.aistudio.job.v1.ListJobTemplateViewsResponse
	(*CreateOrUpdateJobTemplateTriggerRequest)(nil), // 26: apis.aistudio.job.v1.CreateOrUpdateJobTemplateTriggerRequest
	(*DeleteJobTemplateTriggerRequest)(nil),         // 27: apis.aistudio.job.v1.DeleteJobTemplateTriggerRequest
	(*GetJobTemplateTriggerRequest)(nil),            // 28: apis.aistudio.job.v1.GetJobTemplateTriggerRequest
	(*ListJobTemplateTriggersRequest)(nil),          // 29: apis.aistudio.job.v1.ListJobTemplateTriggersRequest
	(*ListJobTemplateTriggersResponse)(nil),         // 30: apis.aistudio.job.v1.ListJobTemplateTriggersResponse
	(*Job)(nil),                                     // 31: apis.aistudio.job.v1.Job
	(*RestartPolicy)(nil),                           // 32: apis.aistudio.job.v1.RestartPolicy
	(*DatasetVolume)(nil),                           // 33: apis.aistudio.job.v1.DatasetVolume
	(*CreateJobRequest)(nil),                        // 34: apis.aistudio.job.v1.CreateJobRequest
	(*CreateJobResponse)(nil),                       // 35: apis.aistudio.job.v1.CreateJobResponse
	(*UpdateJobRequest)(nil),                        // 36: apis.aistudio.job.v1.UpdateJobRequest
	(*CreateTensorboardRequest)(nil),                // 37: apis.aistudio.job.v1.CreateTensorboardRequest
	(*GetTensorboardRequest)(nil),                   // 38: apis.aistudio.job.v1.GetTensorboardRequest
	(*GetTensorboardResponse)(nil),                  // 39: apis.aistudio.job.v1.GetTensorboardResponse
	(*PyTorchDDPJobTemplate)(nil),                   // 40: apis.aistudio.job.v1.PyTorchDDPJobTemplate
	(*DeepSpeedJobTemplate)(nil),                    // 41: apis.aistudio.job.v1.DeepSpeedJobTemplate
	(*SimpleTrainingJobTemplate)(nil),               // 42: apis.aistudio.job.v1.SimpleTrainingJobTemplate
	(*TaskSpec)(nil),                                // 43: apis.aistudio.job.v1.TaskSpec
	(*SourceCode)(nil),                              // 44: apis.aistudio.job.v1.SourceCode
	(*JobStatus)(nil),                               // 45: apis.aistudio.job.v1.JobStatus
	(*ListJobTaskStatus)(nil),                       // 46: apis.aistudio.job.v1.ListJobTaskStatus
	(*TaskStatus)(nil),                              // 47: apis.aistudio.job.v1.TaskStatus
	(*GetJobIDRequest)(nil),                         // 48: apis.aistudio.job.v1.GetJobIDRequest
	(*GetJobIDResponse)(nil),                        // 49: apis.aistudio.job.v1.GetJobIDResponse
	(*TaskCondition)(nil),                           // 50: apis.aistudio.job.v1.TaskCondition
	(*JobCondition)(nil),                            // 51: apis.aistudio.job.v1.JobCondition
	(*StopJobRequest)(nil),                          // 52: apis.aistudio.job.v1.StopJobRequest
	(*GetJobBaseRequest)(nil),                       // 53: apis.aistudio.job.v1.GetJobBaseRequest
	(*GetJobRequest)(nil),                           // 54: apis.aistudio.job.v1.GetJobRequest
	(*DeleteJobRequest)(nil),                        // 55: apis.aistudio.job.v1.DeleteJobRequest
	(*DeleteJobTemplateRequest)(nil),                // 56: apis.aistudio.job.v1.DeleteJobTemplateRequest
	(*GetJobTasksRequest)(nil),                      // 57: apis.aistudio.job.v1.GetJobTasksRequest
	(*GetJobVolumeRequest)(nil),                     // 58: apis.aistudio.job.v1.GetJobVolumeRequest
	(*ListJobsOptions)(nil),                         // 59: apis.aistudio.job.v1.ListJobsOptions
	(*ListJobsResponse)(nil),                        // 60: apis.aistudio.job.v1.ListJobsResponse
	(*JobTimeline)(nil),                             // 61: apis.aistudio.job.v1.JobTimeline
	(*JobVolume)(nil),                               // 62: apis.aistudio.job.v1.JobVolume
	(*ListJobVolumes)(nil),                          // 63: apis.aistudio.job.v1.ListJobVolumes
	(*StopJobTaskRequest)(nil),                      // 64: apis.aistudio.job.v1.StopJobTaskRequest
	(*RedeployJobRequest)(nil),                      // 65: apis.aistudio.job.v1.RedeployJobRequest
	nil,                                             // 66: apis.aistudio.job.v1.JobTemplate.LabelsEntry
	nil,                                             // 67: apis.aistudio.job.v1.CreateOrUpdateJobTemplateRequest.LabelsEntry
	nil,                                             // 68: apis.aistudio.job.v1.Job.LabelsEntry
	nil,                                             // 69: apis.aistudio.job.v1.CreateJobRequest.LabelsEntry
	(*SourceCode_CloudFsSource)(nil),                // 70: apis.aistudio.job.v1.SourceCode.CloudFsSource
	(*SourceCode_GitSource)(nil),                    // 71: apis.aistudio.job.v1.SourceCode.GitSource
	(*SourceCode_CubeFsSource)(nil),                 // 72: apis.aistudio.job.v1.SourceCode.CubeFsSource
	(*common.VolumeSpec)(nil),                       // 73: apis.common.VolumeSpec
	(*common.EnvVar)(nil),                           // 74: apis.common.EnvVar
	(*v1.ConfigSpec)(nil),                           // 75: apis.aistudio.config.v1.ConfigSpec
	(*common.Specification)(nil),                    // 76: apis.common.Specification
	(*emptypb.Empty)(nil),                           // 77: google.protobuf.Empty
}
var file_aistudio_job_v1_job_proto_depIdxs = []int32{
	66, // 0: apis.aistudio.job.v1.JobTemplate.labels:type_name -> apis.aistudio.job.v1.JobTemplate.LabelsEntry
	16, // 1: apis.aistudio.job.v1.JobTemplate.triggers:type_name -> apis.aistudio.job.v1.Trigger
	1,  // 2: apis.aistudio.job.v1.JobTemplate.executionStrategy:type_name -> apis.aistudio.job.v1.ExecutionStrategy
	31, // 3: apis.aistudio.job.v1.JobTemplate.job:type_name -> apis.aistudio.job.v1.Job
	14, // 4: apis.aistudio.job.v1.JobTemplateView.jobTemplate:type_name -> apis.aistudio.job.v1.JobTemplate
	0,  // 5: apis.aistudio.job.v1.Trigger.triggerType:type_name -> apis.aistudio.job.v1.TriggerType
	17, // 6: apis.aistudio.job.v1.Trigger.timeTriggerOptions:type_name -> apis.aistudio.job.v1.TimerTriggerOptions
	2,  // 7: apis.aistudio.job.v1.Trigger.state:type_name -> apis.aistudio.job.v1.TriggerState
	9,  // 8: apis.aistudio.job.v1.TimerTriggerOptions.type:type_name -> apis.aistudio.job.v1.TimerTriggerOptions.TimerTriggerType
	18, // 9: apis.aistudio.job.v1.TimerTriggerOptions.cycle:type_name -> apis.aistudio.job.v1.Cycle
	18, // 10: apis.aistudio.job.v1.TimerTriggerOptions.skipTime:type_name -> apis.aistudio.job.v1.Cycle
	67, // 11: apis.aistudio.job.v1.CreateOrUpdateJobTemplateRequest.labels:type_name -> apis.aistudio.job.v1.CreateOrUpdateJobTemplateRequest.LabelsEntry
	31, // 12: apis.aistudio.job.v1.CreateOrUpdateJobTemplateRequest.job:type_name -> apis.aistudio.job.v1.Job
	16, // 13: apis.aistudio.job.v1.CreateOrUpdateJobTemplateRequest.triggers:type_name -> apis.aistudio.job.v1.Trigger
	1,  // 14: apis.aistudio.job.v1.CreateOrUpdateJobTemplateRequest.executionStrategy:type_name -> apis.aistudio.job.v1.ExecutionStrategy
	15, // 15: apis.aistudio.job.v1.ListJobTemplateViewsResponse.jobTemplateViews:type_name -> apis.aistudio.job.v1.JobTemplateView
	0,  // 16: apis.aistudio.job.v1.CreateOrUpdateJobTemplateTriggerRequest.triggerType:type_name -> apis.aistudio.job.v1.TriggerType
	17, // 17: apis.aistudio.job.v1.CreateOrUpdateJobTemplateTriggerRequest.timeTriggerOptions:type_name -> apis.aistudio.job.v1.TimerTriggerOptions
	16, // 18: apis.aistudio.job.v1.ListJobTemplateTriggersResponse.triggers:type_name -> apis.aistudio.job.v1.Trigger
	4,  // 19: apis.aistudio.job.v1.Job.jobType:type_name -> apis.aistudio.job.v1.JobType
	45, // 20: apis.aistudio.job.v1.Job.jobStatus:type_name -> apis.aistudio.job.v1.JobStatus
	40, // 21: apis.aistudio.job.v1.Job.pyTorchDDPJobTemplate:type_name -> apis.aistudio.job.v1.PyTorchDDPJobTemplate
	41, // 22: apis.aistudio.job.v1.Job.deepSpeedJobTemplate:type_name -> apis.aistudio.job.v1.DeepSpeedJobTemplate
	42, // 23: apis.aistudio.job.v1.Job.simpleTrainingJobTemplate:type_name -> apis.aistudio.job.v1.SimpleTrainingJobTemplate
	32, // 24: apis.aistudio.job.v1.Job.restartPolicy:type_name -> apis.aistudio.job.v1.RestartPolicy
	68, // 25: apis.aistudio.job.v1.Job.labels:type_name -> apis.aistudio.job.v1.Job.LabelsEntry
	69, // 26: apis.aistudio.job.v1.CreateJobRequest.labels:type_name -> apis.aistudio.job.v1.CreateJobRequest.LabelsEntry
	4,  // 27: apis.aistudio.job.v1.CreateJobRequest.jobType:type_name -> apis.aistudio.job.v1.JobType
	3,  // 28: apis.aistudio.job.v1.CreateJobRequest.priority:type_name -> apis.aistudio.job.v1.Priority
	40, // 29: apis.aistudio.job.v1.CreateJobRequest.pyTorchDDPJobTemplate:type_name -> apis.aistudio.job.v1.PyTorchDDPJobTemplate
	41, // 30: apis.aistudio.job.v1.CreateJobRequest.deepSpeedJobTemplate:type_name -> apis.aistudio.job.v1.DeepSpeedJobTemplate
	42, // 31: apis.aistudio.job.v1.CreateJobRequest.simpleTrainingJobTemplate:type_name -> apis.aistudio.job.v1.SimpleTrainingJobTemplate
	32, // 32: apis.aistudio.job.v1.CreateJobRequest.restartPolicy:type_name -> apis.aistudio.job.v1.RestartPolicy
	73, // 33: apis.aistudio.job.v1.PyTorchDDPJobTemplate.volumeSpecs:type_name -> apis.common.VolumeSpec
	44, // 34: apis.aistudio.job.v1.PyTorchDDPJobTemplate.sourceCode:type_name -> apis.aistudio.job.v1.SourceCode
	74, // 35: apis.aistudio.job.v1.PyTorchDDPJobTemplate.envVars:type_name -> apis.common.EnvVar
	43, // 36: apis.aistudio.job.v1.PyTorchDDPJobTemplate.master:type_name -> apis.aistudio.job.v1.TaskSpec
	43, // 37: apis.aistudio.job.v1.PyTorchDDPJobTemplate.worker:type_name -> apis.aistudio.job.v1.TaskSpec
	75, // 38: apis.aistudio.job.v1.PyTorchDDPJobTemplate.configSpecs:type_name -> apis.aistudio.config.v1.ConfigSpec
	73, // 39: apis.aistudio.job.v1.DeepSpeedJobTemplate.volumeSpecs:type_name -> apis.common.VolumeSpec
	44, // 40: apis.aistudio.job.v1.DeepSpeedJobTemplate.sourceCode:type_name -> apis.aistudio.job.v1.SourceCode
	74, // 41: apis.aistudio.job.v1.DeepSpeedJobTemplate.envVars:type_name -> apis.common.EnvVar
	43, // 42: apis.aistudio.job.v1.DeepSpeedJobTemplate.master:type_name -> apis.aistudio.job.v1.TaskSpec
	43, // 43: apis.aistudio.job.v1.DeepSpeedJobTemplate.worker:type_name -> apis.aistudio.job.v1.TaskSpec
	75, // 44: apis.aistudio.job.v1.DeepSpeedJobTemplate.configSpecs:type_name -> apis.aistudio.config.v1.ConfigSpec
	44, // 45: apis.aistudio.job.v1.SimpleTrainingJobTemplate.sourceCode:type_name -> apis.aistudio.job.v1.SourceCode
	74, // 46: apis.aistudio.job.v1.SimpleTrainingJobTemplate.envVars:type_name -> apis.common.EnvVar
	73, // 47: apis.aistudio.job.v1.SimpleTrainingJobTemplate.volumeSpecs:type_name -> apis.common.VolumeSpec
	76, // 48: apis.aistudio.job.v1.SimpleTrainingJobTemplate.specification:type_name -> apis.common.Specification
	75, // 49: apis.aistudio.job.v1.SimpleTrainingJobTemplate.configSpecs:type_name -> apis.aistudio.config.v1.ConfigSpec
	74, // 50: apis.aistudio.job.v1.TaskSpec.envVars:type_name -> apis.common.EnvVar
	73, // 51: apis.aistudio.job.v1.TaskSpec.volumeSpecs:type_name -> apis.common.VolumeSpec
	76, // 52: apis.aistudio.job.v1.TaskSpec.specification:type_name -> apis.common.Specification
	75, // 53: apis.aistudio.job.v1.TaskSpec.configSpecs:type_name -> apis.aistudio.config.v1.ConfigSpec
	70, // 54: apis.aistudio.job.v1.SourceCode.cloudFsSource:type_name -> apis.aistudio.job.v1.SourceCode.CloudFsSource
	71, // 55: apis.aistudio.job.v1.SourceCode.gitSource:type_name -> apis.aistudio.job.v1.SourceCode.GitSource
	72, // 56: apis.aistudio.job.v1.SourceCode.cubeFsSource:type_name -> apis.aistudio.job.v1.SourceCode.CubeFsSource
	51, // 57: apis.aistudio.job.v1.JobStatus.conditions:type_name -> apis.aistudio.job.v1.JobCondition
	47, // 58: apis.aistudio.job.v1.ListJobTaskStatus.taskStatus:type_name -> apis.aistudio.job.v1.TaskStatus
	50, // 59: apis.aistudio.job.v1.TaskStatus.conditions:type_name -> apis.aistudio.job.v1.TaskCondition
	10, // 60: apis.aistudio.job.v1.JobCondition.conditionStatus:type_name -> apis.aistudio.job.v1.JobCondition.ConditionStatus
	31, // 61: apis.aistudio.job.v1.ListJobsResponse.jobs:type_name -> apis.aistudio.job.v1.Job
	62, // 62: apis.aistudio.job.v1.ListJobVolumes.volumes:type_name -> apis.aistudio.job.v1.JobVolume
	22, // 63: apis.aistudio.job.v1.JobService.CreateJobTemplate:input_type -> apis.aistudio.job.v1.CreateOrUpdateJobTemplateRequest
	22, // 64: apis.aistudio.job.v1.JobService.UpdateJobTemplate:input_type -> apis.aistudio.job.v1.CreateOrUpdateJobTemplateRequest
	23, // 65: apis.aistudio.job.v1.JobService.GetJobTemplate:input_type -> apis.aistudio.job.v1.GetJobTemplateRequest
	24, // 66: apis.aistudio.job.v1.JobService.ListJobTemplateViews:input_type -> apis.aistudio.job.v1.ListJobTemplateViewsOptions
	56, // 67: apis.aistudio.job.v1.JobService.DeleteJobTemplate:input_type -> apis.aistudio.job.v1.DeleteJobTemplateRequest
	26, // 68: apis.aistudio.job.v1.JobService.CreateJobTemplateTrigger:input_type -> apis.aistudio.job.v1.CreateOrUpdateJobTemplateTriggerRequest
	26, // 69: apis.aistudio.job.v1.JobService.UpdateJobTemplateTrigger:input_type -> apis.aistudio.job.v1.CreateOrUpdateJobTemplateTriggerRequest
	27, // 70: apis.aistudio.job.v1.JobService.DeleteJobTemplateTrigger:input_type -> apis.aistudio.job.v1.DeleteJobTemplateTriggerRequest
	28, // 71: apis.aistudio.job.v1.JobService.GetJobTemplateTrigger:input_type -> apis.aistudio.job.v1.GetJobTemplateTriggerRequest
	29, // 72: apis.aistudio.job.v1.JobService.ListJobTemplateTriggers:input_type -> apis.aistudio.job.v1.ListJobTemplateTriggersRequest
	20, // 73: apis.aistudio.job.v1.JobService.PauseJobTemplateTrigger:input_type -> apis.aistudio.job.v1.PauseJobTemplateTriggerRequest
	21, // 74: apis.aistudio.job.v1.JobService.RecoverJobTemplateTrigger:input_type -> apis.aistudio.job.v1.RecoverJobTemplateTriggerRequest
	34, // 75: apis.aistudio.job.v1.JobService.SubmitJob:input_type -> apis.aistudio.job.v1.CreateJobRequest
	52, // 76: apis.aistudio.job.v1.JobService.StopJob:input_type -> apis.aistudio.job.v1.StopJobRequest
	36, // 77: apis.aistudio.job.v1.JobService.UpdateJob:input_type -> apis.aistudio.job.v1.UpdateJobRequest
	53, // 78: apis.aistudio.job.v1.JobService.GetJobBase:input_type -> apis.aistudio.job.v1.GetJobBaseRequest
	54, // 79: apis.aistudio.job.v1.JobService.GetJobDetail:input_type -> apis.aistudio.job.v1.GetJobRequest
	55, // 80: apis.aistudio.job.v1.JobService.DeleteJob:input_type -> apis.aistudio.job.v1.DeleteJobRequest
	59, // 81: apis.aistudio.job.v1.JobService.ListJobs:input_type -> apis.aistudio.job.v1.ListJobsOptions
	37, // 82: apis.aistudio.job.v1.JobService.CreateTensorboard:input_type -> apis.aistudio.job.v1.CreateTensorboardRequest
	38, // 83: apis.aistudio.job.v1.JobService.GetTensorboard:input_type -> apis.aistudio.job.v1.GetTensorboardRequest
	57, // 84: apis.aistudio.job.v1.JobService.GetJobTasks:input_type -> apis.aistudio.job.v1.GetJobTasksRequest
	58, // 85: apis.aistudio.job.v1.JobService.GetJobVolumes:input_type -> apis.aistudio.job.v1.GetJobVolumeRequest
	64, // 86: apis.aistudio.job.v1.JobService.StopJobTask:input_type -> apis.aistudio.job.v1.StopJobTaskRequest
	65, // 87: apis.aistudio.job.v1.JobService.RedeployJob:input_type -> apis.aistudio.job.v1.RedeployJobRequest
	11, // 88: apis.aistudio.job.v1.JobService.CheckJobIsExist:input_type -> apis.aistudio.job.v1.CheckJobIsExistRequest
	13, // 89: apis.aistudio.job.v1.JobService.UpdateMembers:input_type -> apis.aistudio.job.v1.UpdateMembersRequest
	77, // 90: apis.aistudio.job.v1.JobService.CreateJobTemplate:output_type -> google.protobuf.Empty
	77, // 91: apis.aistudio.job.v1.JobService.UpdateJobTemplate:output_type -> google.protobuf.Empty
	14, // 92: apis.aistudio.job.v1.JobService.GetJobTemplate:output_type -> apis.aistudio.job.v1.JobTemplate
	25, // 93: apis.aistudio.job.v1.JobService.ListJobTemplateViews:output_type -> apis.aistudio.job.v1.ListJobTemplateViewsResponse
	77, // 94: apis.aistudio.job.v1.JobService.DeleteJobTemplate:output_type -> google.protobuf.Empty
	77, // 95: apis.aistudio.job.v1.JobService.CreateJobTemplateTrigger:output_type -> google.protobuf.Empty
	77, // 96: apis.aistudio.job.v1.JobService.UpdateJobTemplateTrigger:output_type -> google.protobuf.Empty
	77, // 97: apis.aistudio.job.v1.JobService.DeleteJobTemplateTrigger:output_type -> google.protobuf.Empty
	16, // 98: apis.aistudio.job.v1.JobService.GetJobTemplateTrigger:output_type -> apis.aistudio.job.v1.Trigger
	30, // 99: apis.aistudio.job.v1.JobService.ListJobTemplateTriggers:output_type -> apis.aistudio.job.v1.ListJobTemplateTriggersResponse
	77, // 100: apis.aistudio.job.v1.JobService.PauseJobTemplateTrigger:output_type -> google.protobuf.Empty
	77, // 101: apis.aistudio.job.v1.JobService.RecoverJobTemplateTrigger:output_type -> google.protobuf.Empty
	35, // 102: apis.aistudio.job.v1.JobService.SubmitJob:output_type -> apis.aistudio.job.v1.CreateJobResponse
	77, // 103: apis.aistudio.job.v1.JobService.StopJob:output_type -> google.protobuf.Empty
	77, // 104: apis.aistudio.job.v1.JobService.UpdateJob:output_type -> google.protobuf.Empty
	31, // 105: apis.aistudio.job.v1.JobService.GetJobBase:output_type -> apis.aistudio.job.v1.Job
	31, // 106: apis.aistudio.job.v1.JobService.GetJobDetail:output_type -> apis.aistudio.job.v1.Job
	77, // 107: apis.aistudio.job.v1.JobService.DeleteJob:output_type -> google.protobuf.Empty
	60, // 108: apis.aistudio.job.v1.JobService.ListJobs:output_type -> apis.aistudio.job.v1.ListJobsResponse
	77, // 109: apis.aistudio.job.v1.JobService.CreateTensorboard:output_type -> google.protobuf.Empty
	39, // 110: apis.aistudio.job.v1.JobService.GetTensorboard:output_type -> apis.aistudio.job.v1.GetTensorboardResponse
	46, // 111: apis.aistudio.job.v1.JobService.GetJobTasks:output_type -> apis.aistudio.job.v1.ListJobTaskStatus
	63, // 112: apis.aistudio.job.v1.JobService.GetJobVolumes:output_type -> apis.aistudio.job.v1.ListJobVolumes
	77, // 113: apis.aistudio.job.v1.JobService.StopJobTask:output_type -> google.protobuf.Empty
	77, // 114: apis.aistudio.job.v1.JobService.RedeployJob:output_type -> google.protobuf.Empty
	12, // 115: apis.aistudio.job.v1.JobService.CheckJobIsExist:output_type -> apis.aistudio.job.v1.CheckJobIsExistResponse
	77, // 116: apis.aistudio.job.v1.JobService.UpdateMembers:output_type -> google.protobuf.Empty
	90, // [90:117] is the sub-list for method output_type
	63, // [63:90] is the sub-list for method input_type
	63, // [63:63] is the sub-list for extension type_name
	63, // [63:63] is the sub-list for extension extendee
	0,  // [0:63] is the sub-list for field type_name
}

func init() { file_aistudio_job_v1_job_proto_init() }
func file_aistudio_job_v1_job_proto_init() {
	if File_aistudio_job_v1_job_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_aistudio_job_v1_job_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*CheckJobIsExistRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_job_v1_job_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*CheckJobIsExistResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_job_v1_job_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*UpdateMembersRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_job_v1_job_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*JobTemplate); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_job_v1_job_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*JobTemplateView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_job_v1_job_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*Trigger); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_job_v1_job_proto_msgTypes[6].Exporter = func(v any, i int) any {
			switch v := v.(*TimerTriggerOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_job_v1_job_proto_msgTypes[7].Exporter = func(v any, i int) any {
			switch v := v.(*Cycle); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_job_v1_job_proto_msgTypes[8].Exporter = func(v any, i int) any {
			switch v := v.(*JobTriggerRecord); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_job_v1_job_proto_msgTypes[9].Exporter = func(v any, i int) any {
			switch v := v.(*PauseJobTemplateTriggerRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_job_v1_job_proto_msgTypes[10].Exporter = func(v any, i int) any {
			switch v := v.(*RecoverJobTemplateTriggerRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_job_v1_job_proto_msgTypes[11].Exporter = func(v any, i int) any {
			switch v := v.(*CreateOrUpdateJobTemplateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_job_v1_job_proto_msgTypes[12].Exporter = func(v any, i int) any {
			switch v := v.(*GetJobTemplateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_job_v1_job_proto_msgTypes[13].Exporter = func(v any, i int) any {
			switch v := v.(*ListJobTemplateViewsOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_job_v1_job_proto_msgTypes[14].Exporter = func(v any, i int) any {
			switch v := v.(*ListJobTemplateViewsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_job_v1_job_proto_msgTypes[15].Exporter = func(v any, i int) any {
			switch v := v.(*CreateOrUpdateJobTemplateTriggerRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_job_v1_job_proto_msgTypes[16].Exporter = func(v any, i int) any {
			switch v := v.(*DeleteJobTemplateTriggerRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_job_v1_job_proto_msgTypes[17].Exporter = func(v any, i int) any {
			switch v := v.(*GetJobTemplateTriggerRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_job_v1_job_proto_msgTypes[18].Exporter = func(v any, i int) any {
			switch v := v.(*ListJobTemplateTriggersRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_job_v1_job_proto_msgTypes[19].Exporter = func(v any, i int) any {
			switch v := v.(*ListJobTemplateTriggersResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_job_v1_job_proto_msgTypes[20].Exporter = func(v any, i int) any {
			switch v := v.(*Job); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_job_v1_job_proto_msgTypes[21].Exporter = func(v any, i int) any {
			switch v := v.(*RestartPolicy); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_job_v1_job_proto_msgTypes[22].Exporter = func(v any, i int) any {
			switch v := v.(*DatasetVolume); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_job_v1_job_proto_msgTypes[23].Exporter = func(v any, i int) any {
			switch v := v.(*CreateJobRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_job_v1_job_proto_msgTypes[24].Exporter = func(v any, i int) any {
			switch v := v.(*CreateJobResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_job_v1_job_proto_msgTypes[25].Exporter = func(v any, i int) any {
			switch v := v.(*UpdateJobRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_job_v1_job_proto_msgTypes[26].Exporter = func(v any, i int) any {
			switch v := v.(*CreateTensorboardRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_job_v1_job_proto_msgTypes[27].Exporter = func(v any, i int) any {
			switch v := v.(*GetTensorboardRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_job_v1_job_proto_msgTypes[28].Exporter = func(v any, i int) any {
			switch v := v.(*GetTensorboardResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_job_v1_job_proto_msgTypes[29].Exporter = func(v any, i int) any {
			switch v := v.(*PyTorchDDPJobTemplate); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_job_v1_job_proto_msgTypes[30].Exporter = func(v any, i int) any {
			switch v := v.(*DeepSpeedJobTemplate); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_job_v1_job_proto_msgTypes[31].Exporter = func(v any, i int) any {
			switch v := v.(*SimpleTrainingJobTemplate); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_job_v1_job_proto_msgTypes[32].Exporter = func(v any, i int) any {
			switch v := v.(*TaskSpec); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_job_v1_job_proto_msgTypes[33].Exporter = func(v any, i int) any {
			switch v := v.(*SourceCode); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_job_v1_job_proto_msgTypes[34].Exporter = func(v any, i int) any {
			switch v := v.(*JobStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_job_v1_job_proto_msgTypes[35].Exporter = func(v any, i int) any {
			switch v := v.(*ListJobTaskStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_job_v1_job_proto_msgTypes[36].Exporter = func(v any, i int) any {
			switch v := v.(*TaskStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_job_v1_job_proto_msgTypes[37].Exporter = func(v any, i int) any {
			switch v := v.(*GetJobIDRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_job_v1_job_proto_msgTypes[38].Exporter = func(v any, i int) any {
			switch v := v.(*GetJobIDResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_job_v1_job_proto_msgTypes[39].Exporter = func(v any, i int) any {
			switch v := v.(*TaskCondition); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_job_v1_job_proto_msgTypes[40].Exporter = func(v any, i int) any {
			switch v := v.(*JobCondition); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_job_v1_job_proto_msgTypes[41].Exporter = func(v any, i int) any {
			switch v := v.(*StopJobRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_job_v1_job_proto_msgTypes[42].Exporter = func(v any, i int) any {
			switch v := v.(*GetJobBaseRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_job_v1_job_proto_msgTypes[43].Exporter = func(v any, i int) any {
			switch v := v.(*GetJobRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_job_v1_job_proto_msgTypes[44].Exporter = func(v any, i int) any {
			switch v := v.(*DeleteJobRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_job_v1_job_proto_msgTypes[45].Exporter = func(v any, i int) any {
			switch v := v.(*DeleteJobTemplateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_job_v1_job_proto_msgTypes[46].Exporter = func(v any, i int) any {
			switch v := v.(*GetJobTasksRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_job_v1_job_proto_msgTypes[47].Exporter = func(v any, i int) any {
			switch v := v.(*GetJobVolumeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_job_v1_job_proto_msgTypes[48].Exporter = func(v any, i int) any {
			switch v := v.(*ListJobsOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_job_v1_job_proto_msgTypes[49].Exporter = func(v any, i int) any {
			switch v := v.(*ListJobsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_job_v1_job_proto_msgTypes[50].Exporter = func(v any, i int) any {
			switch v := v.(*JobTimeline); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_job_v1_job_proto_msgTypes[51].Exporter = func(v any, i int) any {
			switch v := v.(*JobVolume); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_job_v1_job_proto_msgTypes[52].Exporter = func(v any, i int) any {
			switch v := v.(*ListJobVolumes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_job_v1_job_proto_msgTypes[53].Exporter = func(v any, i int) any {
			switch v := v.(*StopJobTaskRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_job_v1_job_proto_msgTypes[54].Exporter = func(v any, i int) any {
			switch v := v.(*RedeployJobRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_job_v1_job_proto_msgTypes[59].Exporter = func(v any, i int) any {
			switch v := v.(*SourceCode_CloudFsSource); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_job_v1_job_proto_msgTypes[60].Exporter = func(v any, i int) any {
			switch v := v.(*SourceCode_GitSource); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aistudio_job_v1_job_proto_msgTypes[61].Exporter = func(v any, i int) any {
			switch v := v.(*SourceCode_CubeFsSource); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_aistudio_job_v1_job_proto_msgTypes[33].OneofWrappers = []any{
		(*SourceCode_CloudFsSource_)(nil),
		(*SourceCode_GitSource_)(nil),
		(*SourceCode_CubeFsSource_)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_aistudio_job_v1_job_proto_rawDesc,
			NumEnums:      11,
			NumMessages:   62,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_aistudio_job_v1_job_proto_goTypes,
		DependencyIndexes: file_aistudio_job_v1_job_proto_depIdxs,
		EnumInfos:         file_aistudio_job_v1_job_proto_enumTypes,
		MessageInfos:      file_aistudio_job_v1_job_proto_msgTypes,
	}.Build()
	File_aistudio_job_v1_job_proto = out.File
	file_aistudio_job_v1_job_proto_rawDesc = nil
	file_aistudio_job_v1_job_proto_goTypes = nil
	file_aistudio_job_v1_job_proto_depIdxs = nil
}
