// Code generated by protoc-gen-deepcopy. DO NOT EDIT.
package v1

import (
	proto "google.golang.org/protobuf/proto"
)

// DeepCopyInto supports using CheckJobIsExistRequest within kubernetes types, where deepcopy-gen is used.
func (in *CheckJobIsExistRequest) DeepCopyInto(out *CheckJobIsExistRequest) {
	p := proto.Clone(in).(*CheckJobIsExistRequest)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new CheckJobIsExistRequest. Required by controller-gen.
func (in *CheckJobIsExistRequest) DeepCopy() *CheckJobIsExistRequest {
	if in == nil {
		return nil
	}
	out := new(CheckJobIsExistRequest)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new CheckJobIsExistRequest. Required by controller-gen.
func (in *CheckJobIsExistRequest) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using CheckJobIsExistResponse within kubernetes types, where deepcopy-gen is used.
func (in *CheckJobIsExistResponse) DeepCopyInto(out *CheckJobIsExistResponse) {
	p := proto.Clone(in).(*CheckJobIsExistResponse)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new CheckJobIsExistResponse. Required by controller-gen.
func (in *CheckJobIsExistResponse) DeepCopy() *CheckJobIsExistResponse {
	if in == nil {
		return nil
	}
	out := new(CheckJobIsExistResponse)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new CheckJobIsExistResponse. Required by controller-gen.
func (in *CheckJobIsExistResponse) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using UpdateMembersRequest within kubernetes types, where deepcopy-gen is used.
func (in *UpdateMembersRequest) DeepCopyInto(out *UpdateMembersRequest) {
	p := proto.Clone(in).(*UpdateMembersRequest)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new UpdateMembersRequest. Required by controller-gen.
func (in *UpdateMembersRequest) DeepCopy() *UpdateMembersRequest {
	if in == nil {
		return nil
	}
	out := new(UpdateMembersRequest)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new UpdateMembersRequest. Required by controller-gen.
func (in *UpdateMembersRequest) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using JobTemplate within kubernetes types, where deepcopy-gen is used.
func (in *JobTemplate) DeepCopyInto(out *JobTemplate) {
	p := proto.Clone(in).(*JobTemplate)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new JobTemplate. Required by controller-gen.
func (in *JobTemplate) DeepCopy() *JobTemplate {
	if in == nil {
		return nil
	}
	out := new(JobTemplate)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new JobTemplate. Required by controller-gen.
func (in *JobTemplate) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using JobTemplateView within kubernetes types, where deepcopy-gen is used.
func (in *JobTemplateView) DeepCopyInto(out *JobTemplateView) {
	p := proto.Clone(in).(*JobTemplateView)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new JobTemplateView. Required by controller-gen.
func (in *JobTemplateView) DeepCopy() *JobTemplateView {
	if in == nil {
		return nil
	}
	out := new(JobTemplateView)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new JobTemplateView. Required by controller-gen.
func (in *JobTemplateView) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using Trigger within kubernetes types, where deepcopy-gen is used.
func (in *Trigger) DeepCopyInto(out *Trigger) {
	p := proto.Clone(in).(*Trigger)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Trigger. Required by controller-gen.
func (in *Trigger) DeepCopy() *Trigger {
	if in == nil {
		return nil
	}
	out := new(Trigger)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new Trigger. Required by controller-gen.
func (in *Trigger) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using TimerTriggerOptions within kubernetes types, where deepcopy-gen is used.
func (in *TimerTriggerOptions) DeepCopyInto(out *TimerTriggerOptions) {
	p := proto.Clone(in).(*TimerTriggerOptions)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new TimerTriggerOptions. Required by controller-gen.
func (in *TimerTriggerOptions) DeepCopy() *TimerTriggerOptions {
	if in == nil {
		return nil
	}
	out := new(TimerTriggerOptions)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new TimerTriggerOptions. Required by controller-gen.
func (in *TimerTriggerOptions) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using Cycle within kubernetes types, where deepcopy-gen is used.
func (in *Cycle) DeepCopyInto(out *Cycle) {
	p := proto.Clone(in).(*Cycle)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Cycle. Required by controller-gen.
func (in *Cycle) DeepCopy() *Cycle {
	if in == nil {
		return nil
	}
	out := new(Cycle)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new Cycle. Required by controller-gen.
func (in *Cycle) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using JobTriggerRecord within kubernetes types, where deepcopy-gen is used.
func (in *JobTriggerRecord) DeepCopyInto(out *JobTriggerRecord) {
	p := proto.Clone(in).(*JobTriggerRecord)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new JobTriggerRecord. Required by controller-gen.
func (in *JobTriggerRecord) DeepCopy() *JobTriggerRecord {
	if in == nil {
		return nil
	}
	out := new(JobTriggerRecord)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new JobTriggerRecord. Required by controller-gen.
func (in *JobTriggerRecord) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using PauseJobTemplateTriggerRequest within kubernetes types, where deepcopy-gen is used.
func (in *PauseJobTemplateTriggerRequest) DeepCopyInto(out *PauseJobTemplateTriggerRequest) {
	p := proto.Clone(in).(*PauseJobTemplateTriggerRequest)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new PauseJobTemplateTriggerRequest. Required by controller-gen.
func (in *PauseJobTemplateTriggerRequest) DeepCopy() *PauseJobTemplateTriggerRequest {
	if in == nil {
		return nil
	}
	out := new(PauseJobTemplateTriggerRequest)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new PauseJobTemplateTriggerRequest. Required by controller-gen.
func (in *PauseJobTemplateTriggerRequest) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using RecoverJobTemplateTriggerRequest within kubernetes types, where deepcopy-gen is used.
func (in *RecoverJobTemplateTriggerRequest) DeepCopyInto(out *RecoverJobTemplateTriggerRequest) {
	p := proto.Clone(in).(*RecoverJobTemplateTriggerRequest)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new RecoverJobTemplateTriggerRequest. Required by controller-gen.
func (in *RecoverJobTemplateTriggerRequest) DeepCopy() *RecoverJobTemplateTriggerRequest {
	if in == nil {
		return nil
	}
	out := new(RecoverJobTemplateTriggerRequest)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new RecoverJobTemplateTriggerRequest. Required by controller-gen.
func (in *RecoverJobTemplateTriggerRequest) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using CreateOrUpdateJobTemplateRequest within kubernetes types, where deepcopy-gen is used.
func (in *CreateOrUpdateJobTemplateRequest) DeepCopyInto(out *CreateOrUpdateJobTemplateRequest) {
	p := proto.Clone(in).(*CreateOrUpdateJobTemplateRequest)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new CreateOrUpdateJobTemplateRequest. Required by controller-gen.
func (in *CreateOrUpdateJobTemplateRequest) DeepCopy() *CreateOrUpdateJobTemplateRequest {
	if in == nil {
		return nil
	}
	out := new(CreateOrUpdateJobTemplateRequest)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new CreateOrUpdateJobTemplateRequest. Required by controller-gen.
func (in *CreateOrUpdateJobTemplateRequest) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using GetJobTemplateRequest within kubernetes types, where deepcopy-gen is used.
func (in *GetJobTemplateRequest) DeepCopyInto(out *GetJobTemplateRequest) {
	p := proto.Clone(in).(*GetJobTemplateRequest)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new GetJobTemplateRequest. Required by controller-gen.
func (in *GetJobTemplateRequest) DeepCopy() *GetJobTemplateRequest {
	if in == nil {
		return nil
	}
	out := new(GetJobTemplateRequest)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new GetJobTemplateRequest. Required by controller-gen.
func (in *GetJobTemplateRequest) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using ListJobTemplateViewsOptions within kubernetes types, where deepcopy-gen is used.
func (in *ListJobTemplateViewsOptions) DeepCopyInto(out *ListJobTemplateViewsOptions) {
	p := proto.Clone(in).(*ListJobTemplateViewsOptions)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ListJobTemplateViewsOptions. Required by controller-gen.
func (in *ListJobTemplateViewsOptions) DeepCopy() *ListJobTemplateViewsOptions {
	if in == nil {
		return nil
	}
	out := new(ListJobTemplateViewsOptions)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new ListJobTemplateViewsOptions. Required by controller-gen.
func (in *ListJobTemplateViewsOptions) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using ListJobTemplateViewsResponse within kubernetes types, where deepcopy-gen is used.
func (in *ListJobTemplateViewsResponse) DeepCopyInto(out *ListJobTemplateViewsResponse) {
	p := proto.Clone(in).(*ListJobTemplateViewsResponse)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ListJobTemplateViewsResponse. Required by controller-gen.
func (in *ListJobTemplateViewsResponse) DeepCopy() *ListJobTemplateViewsResponse {
	if in == nil {
		return nil
	}
	out := new(ListJobTemplateViewsResponse)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new ListJobTemplateViewsResponse. Required by controller-gen.
func (in *ListJobTemplateViewsResponse) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using CreateOrUpdateJobTemplateTriggerRequest within kubernetes types, where deepcopy-gen is used.
func (in *CreateOrUpdateJobTemplateTriggerRequest) DeepCopyInto(out *CreateOrUpdateJobTemplateTriggerRequest) {
	p := proto.Clone(in).(*CreateOrUpdateJobTemplateTriggerRequest)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new CreateOrUpdateJobTemplateTriggerRequest. Required by controller-gen.
func (in *CreateOrUpdateJobTemplateTriggerRequest) DeepCopy() *CreateOrUpdateJobTemplateTriggerRequest {
	if in == nil {
		return nil
	}
	out := new(CreateOrUpdateJobTemplateTriggerRequest)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new CreateOrUpdateJobTemplateTriggerRequest. Required by controller-gen.
func (in *CreateOrUpdateJobTemplateTriggerRequest) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using DeleteJobTemplateTriggerRequest within kubernetes types, where deepcopy-gen is used.
func (in *DeleteJobTemplateTriggerRequest) DeepCopyInto(out *DeleteJobTemplateTriggerRequest) {
	p := proto.Clone(in).(*DeleteJobTemplateTriggerRequest)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new DeleteJobTemplateTriggerRequest. Required by controller-gen.
func (in *DeleteJobTemplateTriggerRequest) DeepCopy() *DeleteJobTemplateTriggerRequest {
	if in == nil {
		return nil
	}
	out := new(DeleteJobTemplateTriggerRequest)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new DeleteJobTemplateTriggerRequest. Required by controller-gen.
func (in *DeleteJobTemplateTriggerRequest) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using GetJobTemplateTriggerRequest within kubernetes types, where deepcopy-gen is used.
func (in *GetJobTemplateTriggerRequest) DeepCopyInto(out *GetJobTemplateTriggerRequest) {
	p := proto.Clone(in).(*GetJobTemplateTriggerRequest)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new GetJobTemplateTriggerRequest. Required by controller-gen.
func (in *GetJobTemplateTriggerRequest) DeepCopy() *GetJobTemplateTriggerRequest {
	if in == nil {
		return nil
	}
	out := new(GetJobTemplateTriggerRequest)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new GetJobTemplateTriggerRequest. Required by controller-gen.
func (in *GetJobTemplateTriggerRequest) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using ListJobTemplateTriggersRequest within kubernetes types, where deepcopy-gen is used.
func (in *ListJobTemplateTriggersRequest) DeepCopyInto(out *ListJobTemplateTriggersRequest) {
	p := proto.Clone(in).(*ListJobTemplateTriggersRequest)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ListJobTemplateTriggersRequest. Required by controller-gen.
func (in *ListJobTemplateTriggersRequest) DeepCopy() *ListJobTemplateTriggersRequest {
	if in == nil {
		return nil
	}
	out := new(ListJobTemplateTriggersRequest)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new ListJobTemplateTriggersRequest. Required by controller-gen.
func (in *ListJobTemplateTriggersRequest) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using ListJobTemplateTriggersResponse within kubernetes types, where deepcopy-gen is used.
func (in *ListJobTemplateTriggersResponse) DeepCopyInto(out *ListJobTemplateTriggersResponse) {
	p := proto.Clone(in).(*ListJobTemplateTriggersResponse)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ListJobTemplateTriggersResponse. Required by controller-gen.
func (in *ListJobTemplateTriggersResponse) DeepCopy() *ListJobTemplateTriggersResponse {
	if in == nil {
		return nil
	}
	out := new(ListJobTemplateTriggersResponse)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new ListJobTemplateTriggersResponse. Required by controller-gen.
func (in *ListJobTemplateTriggersResponse) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using Job within kubernetes types, where deepcopy-gen is used.
func (in *Job) DeepCopyInto(out *Job) {
	p := proto.Clone(in).(*Job)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Job. Required by controller-gen.
func (in *Job) DeepCopy() *Job {
	if in == nil {
		return nil
	}
	out := new(Job)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new Job. Required by controller-gen.
func (in *Job) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using RestartPolicy within kubernetes types, where deepcopy-gen is used.
func (in *RestartPolicy) DeepCopyInto(out *RestartPolicy) {
	p := proto.Clone(in).(*RestartPolicy)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new RestartPolicy. Required by controller-gen.
func (in *RestartPolicy) DeepCopy() *RestartPolicy {
	if in == nil {
		return nil
	}
	out := new(RestartPolicy)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new RestartPolicy. Required by controller-gen.
func (in *RestartPolicy) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using DatasetVolume within kubernetes types, where deepcopy-gen is used.
func (in *DatasetVolume) DeepCopyInto(out *DatasetVolume) {
	p := proto.Clone(in).(*DatasetVolume)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new DatasetVolume. Required by controller-gen.
func (in *DatasetVolume) DeepCopy() *DatasetVolume {
	if in == nil {
		return nil
	}
	out := new(DatasetVolume)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new DatasetVolume. Required by controller-gen.
func (in *DatasetVolume) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using CreateJobRequest within kubernetes types, where deepcopy-gen is used.
func (in *CreateJobRequest) DeepCopyInto(out *CreateJobRequest) {
	p := proto.Clone(in).(*CreateJobRequest)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new CreateJobRequest. Required by controller-gen.
func (in *CreateJobRequest) DeepCopy() *CreateJobRequest {
	if in == nil {
		return nil
	}
	out := new(CreateJobRequest)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new CreateJobRequest. Required by controller-gen.
func (in *CreateJobRequest) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using CreateJobResponse within kubernetes types, where deepcopy-gen is used.
func (in *CreateJobResponse) DeepCopyInto(out *CreateJobResponse) {
	p := proto.Clone(in).(*CreateJobResponse)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new CreateJobResponse. Required by controller-gen.
func (in *CreateJobResponse) DeepCopy() *CreateJobResponse {
	if in == nil {
		return nil
	}
	out := new(CreateJobResponse)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new CreateJobResponse. Required by controller-gen.
func (in *CreateJobResponse) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using UpdateJobRequest within kubernetes types, where deepcopy-gen is used.
func (in *UpdateJobRequest) DeepCopyInto(out *UpdateJobRequest) {
	p := proto.Clone(in).(*UpdateJobRequest)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new UpdateJobRequest. Required by controller-gen.
func (in *UpdateJobRequest) DeepCopy() *UpdateJobRequest {
	if in == nil {
		return nil
	}
	out := new(UpdateJobRequest)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new UpdateJobRequest. Required by controller-gen.
func (in *UpdateJobRequest) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using CreateTensorboardRequest within kubernetes types, where deepcopy-gen is used.
func (in *CreateTensorboardRequest) DeepCopyInto(out *CreateTensorboardRequest) {
	p := proto.Clone(in).(*CreateTensorboardRequest)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new CreateTensorboardRequest. Required by controller-gen.
func (in *CreateTensorboardRequest) DeepCopy() *CreateTensorboardRequest {
	if in == nil {
		return nil
	}
	out := new(CreateTensorboardRequest)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new CreateTensorboardRequest. Required by controller-gen.
func (in *CreateTensorboardRequest) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using GetTensorboardRequest within kubernetes types, where deepcopy-gen is used.
func (in *GetTensorboardRequest) DeepCopyInto(out *GetTensorboardRequest) {
	p := proto.Clone(in).(*GetTensorboardRequest)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new GetTensorboardRequest. Required by controller-gen.
func (in *GetTensorboardRequest) DeepCopy() *GetTensorboardRequest {
	if in == nil {
		return nil
	}
	out := new(GetTensorboardRequest)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new GetTensorboardRequest. Required by controller-gen.
func (in *GetTensorboardRequest) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using GetTensorboardResponse within kubernetes types, where deepcopy-gen is used.
func (in *GetTensorboardResponse) DeepCopyInto(out *GetTensorboardResponse) {
	p := proto.Clone(in).(*GetTensorboardResponse)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new GetTensorboardResponse. Required by controller-gen.
func (in *GetTensorboardResponse) DeepCopy() *GetTensorboardResponse {
	if in == nil {
		return nil
	}
	out := new(GetTensorboardResponse)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new GetTensorboardResponse. Required by controller-gen.
func (in *GetTensorboardResponse) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using PyTorchDDPJobTemplate within kubernetes types, where deepcopy-gen is used.
func (in *PyTorchDDPJobTemplate) DeepCopyInto(out *PyTorchDDPJobTemplate) {
	p := proto.Clone(in).(*PyTorchDDPJobTemplate)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new PyTorchDDPJobTemplate. Required by controller-gen.
func (in *PyTorchDDPJobTemplate) DeepCopy() *PyTorchDDPJobTemplate {
	if in == nil {
		return nil
	}
	out := new(PyTorchDDPJobTemplate)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new PyTorchDDPJobTemplate. Required by controller-gen.
func (in *PyTorchDDPJobTemplate) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using DeepSpeedJobTemplate within kubernetes types, where deepcopy-gen is used.
func (in *DeepSpeedJobTemplate) DeepCopyInto(out *DeepSpeedJobTemplate) {
	p := proto.Clone(in).(*DeepSpeedJobTemplate)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new DeepSpeedJobTemplate. Required by controller-gen.
func (in *DeepSpeedJobTemplate) DeepCopy() *DeepSpeedJobTemplate {
	if in == nil {
		return nil
	}
	out := new(DeepSpeedJobTemplate)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new DeepSpeedJobTemplate. Required by controller-gen.
func (in *DeepSpeedJobTemplate) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using SimpleTrainingJobTemplate within kubernetes types, where deepcopy-gen is used.
func (in *SimpleTrainingJobTemplate) DeepCopyInto(out *SimpleTrainingJobTemplate) {
	p := proto.Clone(in).(*SimpleTrainingJobTemplate)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new SimpleTrainingJobTemplate. Required by controller-gen.
func (in *SimpleTrainingJobTemplate) DeepCopy() *SimpleTrainingJobTemplate {
	if in == nil {
		return nil
	}
	out := new(SimpleTrainingJobTemplate)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new SimpleTrainingJobTemplate. Required by controller-gen.
func (in *SimpleTrainingJobTemplate) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using TaskSpec within kubernetes types, where deepcopy-gen is used.
func (in *TaskSpec) DeepCopyInto(out *TaskSpec) {
	p := proto.Clone(in).(*TaskSpec)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new TaskSpec. Required by controller-gen.
func (in *TaskSpec) DeepCopy() *TaskSpec {
	if in == nil {
		return nil
	}
	out := new(TaskSpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new TaskSpec. Required by controller-gen.
func (in *TaskSpec) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using SourceCode within kubernetes types, where deepcopy-gen is used.
func (in *SourceCode) DeepCopyInto(out *SourceCode) {
	p := proto.Clone(in).(*SourceCode)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new SourceCode. Required by controller-gen.
func (in *SourceCode) DeepCopy() *SourceCode {
	if in == nil {
		return nil
	}
	out := new(SourceCode)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new SourceCode. Required by controller-gen.
func (in *SourceCode) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using SourceCode_CloudFsSource within kubernetes types, where deepcopy-gen is used.
func (in *SourceCode_CloudFsSource) DeepCopyInto(out *SourceCode_CloudFsSource) {
	p := proto.Clone(in).(*SourceCode_CloudFsSource)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new SourceCode_CloudFsSource. Required by controller-gen.
func (in *SourceCode_CloudFsSource) DeepCopy() *SourceCode_CloudFsSource {
	if in == nil {
		return nil
	}
	out := new(SourceCode_CloudFsSource)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new SourceCode_CloudFsSource. Required by controller-gen.
func (in *SourceCode_CloudFsSource) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using SourceCode_GitSource within kubernetes types, where deepcopy-gen is used.
func (in *SourceCode_GitSource) DeepCopyInto(out *SourceCode_GitSource) {
	p := proto.Clone(in).(*SourceCode_GitSource)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new SourceCode_GitSource. Required by controller-gen.
func (in *SourceCode_GitSource) DeepCopy() *SourceCode_GitSource {
	if in == nil {
		return nil
	}
	out := new(SourceCode_GitSource)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new SourceCode_GitSource. Required by controller-gen.
func (in *SourceCode_GitSource) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using SourceCode_CubeFsSource within kubernetes types, where deepcopy-gen is used.
func (in *SourceCode_CubeFsSource) DeepCopyInto(out *SourceCode_CubeFsSource) {
	p := proto.Clone(in).(*SourceCode_CubeFsSource)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new SourceCode_CubeFsSource. Required by controller-gen.
func (in *SourceCode_CubeFsSource) DeepCopy() *SourceCode_CubeFsSource {
	if in == nil {
		return nil
	}
	out := new(SourceCode_CubeFsSource)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new SourceCode_CubeFsSource. Required by controller-gen.
func (in *SourceCode_CubeFsSource) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using JobStatus within kubernetes types, where deepcopy-gen is used.
func (in *JobStatus) DeepCopyInto(out *JobStatus) {
	p := proto.Clone(in).(*JobStatus)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new JobStatus. Required by controller-gen.
func (in *JobStatus) DeepCopy() *JobStatus {
	if in == nil {
		return nil
	}
	out := new(JobStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new JobStatus. Required by controller-gen.
func (in *JobStatus) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using ListJobTaskStatus within kubernetes types, where deepcopy-gen is used.
func (in *ListJobTaskStatus) DeepCopyInto(out *ListJobTaskStatus) {
	p := proto.Clone(in).(*ListJobTaskStatus)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ListJobTaskStatus. Required by controller-gen.
func (in *ListJobTaskStatus) DeepCopy() *ListJobTaskStatus {
	if in == nil {
		return nil
	}
	out := new(ListJobTaskStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new ListJobTaskStatus. Required by controller-gen.
func (in *ListJobTaskStatus) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using TaskStatus within kubernetes types, where deepcopy-gen is used.
func (in *TaskStatus) DeepCopyInto(out *TaskStatus) {
	p := proto.Clone(in).(*TaskStatus)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new TaskStatus. Required by controller-gen.
func (in *TaskStatus) DeepCopy() *TaskStatus {
	if in == nil {
		return nil
	}
	out := new(TaskStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new TaskStatus. Required by controller-gen.
func (in *TaskStatus) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using GetJobIDRequest within kubernetes types, where deepcopy-gen is used.
func (in *GetJobIDRequest) DeepCopyInto(out *GetJobIDRequest) {
	p := proto.Clone(in).(*GetJobIDRequest)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new GetJobIDRequest. Required by controller-gen.
func (in *GetJobIDRequest) DeepCopy() *GetJobIDRequest {
	if in == nil {
		return nil
	}
	out := new(GetJobIDRequest)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new GetJobIDRequest. Required by controller-gen.
func (in *GetJobIDRequest) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using GetJobIDResponse within kubernetes types, where deepcopy-gen is used.
func (in *GetJobIDResponse) DeepCopyInto(out *GetJobIDResponse) {
	p := proto.Clone(in).(*GetJobIDResponse)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new GetJobIDResponse. Required by controller-gen.
func (in *GetJobIDResponse) DeepCopy() *GetJobIDResponse {
	if in == nil {
		return nil
	}
	out := new(GetJobIDResponse)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new GetJobIDResponse. Required by controller-gen.
func (in *GetJobIDResponse) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using TaskCondition within kubernetes types, where deepcopy-gen is used.
func (in *TaskCondition) DeepCopyInto(out *TaskCondition) {
	p := proto.Clone(in).(*TaskCondition)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new TaskCondition. Required by controller-gen.
func (in *TaskCondition) DeepCopy() *TaskCondition {
	if in == nil {
		return nil
	}
	out := new(TaskCondition)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new TaskCondition. Required by controller-gen.
func (in *TaskCondition) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using JobCondition within kubernetes types, where deepcopy-gen is used.
func (in *JobCondition) DeepCopyInto(out *JobCondition) {
	p := proto.Clone(in).(*JobCondition)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new JobCondition. Required by controller-gen.
func (in *JobCondition) DeepCopy() *JobCondition {
	if in == nil {
		return nil
	}
	out := new(JobCondition)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new JobCondition. Required by controller-gen.
func (in *JobCondition) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using StopJobRequest within kubernetes types, where deepcopy-gen is used.
func (in *StopJobRequest) DeepCopyInto(out *StopJobRequest) {
	p := proto.Clone(in).(*StopJobRequest)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new StopJobRequest. Required by controller-gen.
func (in *StopJobRequest) DeepCopy() *StopJobRequest {
	if in == nil {
		return nil
	}
	out := new(StopJobRequest)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new StopJobRequest. Required by controller-gen.
func (in *StopJobRequest) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using GetJobBaseRequest within kubernetes types, where deepcopy-gen is used.
func (in *GetJobBaseRequest) DeepCopyInto(out *GetJobBaseRequest) {
	p := proto.Clone(in).(*GetJobBaseRequest)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new GetJobBaseRequest. Required by controller-gen.
func (in *GetJobBaseRequest) DeepCopy() *GetJobBaseRequest {
	if in == nil {
		return nil
	}
	out := new(GetJobBaseRequest)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new GetJobBaseRequest. Required by controller-gen.
func (in *GetJobBaseRequest) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using GetJobRequest within kubernetes types, where deepcopy-gen is used.
func (in *GetJobRequest) DeepCopyInto(out *GetJobRequest) {
	p := proto.Clone(in).(*GetJobRequest)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new GetJobRequest. Required by controller-gen.
func (in *GetJobRequest) DeepCopy() *GetJobRequest {
	if in == nil {
		return nil
	}
	out := new(GetJobRequest)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new GetJobRequest. Required by controller-gen.
func (in *GetJobRequest) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using DeleteJobRequest within kubernetes types, where deepcopy-gen is used.
func (in *DeleteJobRequest) DeepCopyInto(out *DeleteJobRequest) {
	p := proto.Clone(in).(*DeleteJobRequest)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new DeleteJobRequest. Required by controller-gen.
func (in *DeleteJobRequest) DeepCopy() *DeleteJobRequest {
	if in == nil {
		return nil
	}
	out := new(DeleteJobRequest)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new DeleteJobRequest. Required by controller-gen.
func (in *DeleteJobRequest) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using DeleteJobTemplateRequest within kubernetes types, where deepcopy-gen is used.
func (in *DeleteJobTemplateRequest) DeepCopyInto(out *DeleteJobTemplateRequest) {
	p := proto.Clone(in).(*DeleteJobTemplateRequest)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new DeleteJobTemplateRequest. Required by controller-gen.
func (in *DeleteJobTemplateRequest) DeepCopy() *DeleteJobTemplateRequest {
	if in == nil {
		return nil
	}
	out := new(DeleteJobTemplateRequest)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new DeleteJobTemplateRequest. Required by controller-gen.
func (in *DeleteJobTemplateRequest) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using GetJobTasksRequest within kubernetes types, where deepcopy-gen is used.
func (in *GetJobTasksRequest) DeepCopyInto(out *GetJobTasksRequest) {
	p := proto.Clone(in).(*GetJobTasksRequest)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new GetJobTasksRequest. Required by controller-gen.
func (in *GetJobTasksRequest) DeepCopy() *GetJobTasksRequest {
	if in == nil {
		return nil
	}
	out := new(GetJobTasksRequest)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new GetJobTasksRequest. Required by controller-gen.
func (in *GetJobTasksRequest) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using GetJobVolumeRequest within kubernetes types, where deepcopy-gen is used.
func (in *GetJobVolumeRequest) DeepCopyInto(out *GetJobVolumeRequest) {
	p := proto.Clone(in).(*GetJobVolumeRequest)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new GetJobVolumeRequest. Required by controller-gen.
func (in *GetJobVolumeRequest) DeepCopy() *GetJobVolumeRequest {
	if in == nil {
		return nil
	}
	out := new(GetJobVolumeRequest)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new GetJobVolumeRequest. Required by controller-gen.
func (in *GetJobVolumeRequest) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using ListJobsOptions within kubernetes types, where deepcopy-gen is used.
func (in *ListJobsOptions) DeepCopyInto(out *ListJobsOptions) {
	p := proto.Clone(in).(*ListJobsOptions)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ListJobsOptions. Required by controller-gen.
func (in *ListJobsOptions) DeepCopy() *ListJobsOptions {
	if in == nil {
		return nil
	}
	out := new(ListJobsOptions)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new ListJobsOptions. Required by controller-gen.
func (in *ListJobsOptions) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using ListJobsResponse within kubernetes types, where deepcopy-gen is used.
func (in *ListJobsResponse) DeepCopyInto(out *ListJobsResponse) {
	p := proto.Clone(in).(*ListJobsResponse)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ListJobsResponse. Required by controller-gen.
func (in *ListJobsResponse) DeepCopy() *ListJobsResponse {
	if in == nil {
		return nil
	}
	out := new(ListJobsResponse)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new ListJobsResponse. Required by controller-gen.
func (in *ListJobsResponse) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using JobTimeline within kubernetes types, where deepcopy-gen is used.
func (in *JobTimeline) DeepCopyInto(out *JobTimeline) {
	p := proto.Clone(in).(*JobTimeline)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new JobTimeline. Required by controller-gen.
func (in *JobTimeline) DeepCopy() *JobTimeline {
	if in == nil {
		return nil
	}
	out := new(JobTimeline)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new JobTimeline. Required by controller-gen.
func (in *JobTimeline) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using JobVolume within kubernetes types, where deepcopy-gen is used.
func (in *JobVolume) DeepCopyInto(out *JobVolume) {
	p := proto.Clone(in).(*JobVolume)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new JobVolume. Required by controller-gen.
func (in *JobVolume) DeepCopy() *JobVolume {
	if in == nil {
		return nil
	}
	out := new(JobVolume)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new JobVolume. Required by controller-gen.
func (in *JobVolume) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using ListJobVolumes within kubernetes types, where deepcopy-gen is used.
func (in *ListJobVolumes) DeepCopyInto(out *ListJobVolumes) {
	p := proto.Clone(in).(*ListJobVolumes)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ListJobVolumes. Required by controller-gen.
func (in *ListJobVolumes) DeepCopy() *ListJobVolumes {
	if in == nil {
		return nil
	}
	out := new(ListJobVolumes)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new ListJobVolumes. Required by controller-gen.
func (in *ListJobVolumes) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using StopJobTaskRequest within kubernetes types, where deepcopy-gen is used.
func (in *StopJobTaskRequest) DeepCopyInto(out *StopJobTaskRequest) {
	p := proto.Clone(in).(*StopJobTaskRequest)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new StopJobTaskRequest. Required by controller-gen.
func (in *StopJobTaskRequest) DeepCopy() *StopJobTaskRequest {
	if in == nil {
		return nil
	}
	out := new(StopJobTaskRequest)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new StopJobTaskRequest. Required by controller-gen.
func (in *StopJobTaskRequest) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}

// DeepCopyInto supports using RedeployJobRequest within kubernetes types, where deepcopy-gen is used.
func (in *RedeployJobRequest) DeepCopyInto(out *RedeployJobRequest) {
	p := proto.Clone(in).(*RedeployJobRequest)
	*out = *p
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new RedeployJobRequest. Required by controller-gen.
func (in *RedeployJobRequest) DeepCopy() *RedeployJobRequest {
	if in == nil {
		return nil
	}
	out := new(RedeployJobRequest)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInterface is an autogenerated deepcopy function, copying the receiver, creating a new RedeployJobRequest. Required by controller-gen.
func (in *RedeployJobRequest) DeepCopyInterface() interface{} {
	return in.DeepCopy()
}
