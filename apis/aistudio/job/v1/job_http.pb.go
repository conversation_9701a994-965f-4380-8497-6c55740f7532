// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.3
// - protoc             v5.29.3
// source: aistudio/job/v1/job.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationJobServiceCheckJobIsExist = "/apis.aistudio.job.v1.JobService/CheckJobIsExist"
const OperationJobServiceCreateJobTemplate = "/apis.aistudio.job.v1.JobService/CreateJobTemplate"
const OperationJobServiceCreateJobTemplateTrigger = "/apis.aistudio.job.v1.JobService/CreateJobTemplateTrigger"
const OperationJobServiceCreateTensorboard = "/apis.aistudio.job.v1.JobService/CreateTensorboard"
const OperationJobServiceDeleteJob = "/apis.aistudio.job.v1.JobService/DeleteJob"
const OperationJobServiceDeleteJobTemplate = "/apis.aistudio.job.v1.JobService/DeleteJobTemplate"
const OperationJobServiceDeleteJobTemplateTrigger = "/apis.aistudio.job.v1.JobService/DeleteJobTemplateTrigger"
const OperationJobServiceGetJobBase = "/apis.aistudio.job.v1.JobService/GetJobBase"
const OperationJobServiceGetJobDetail = "/apis.aistudio.job.v1.JobService/GetJobDetail"
const OperationJobServiceGetJobTasks = "/apis.aistudio.job.v1.JobService/GetJobTasks"
const OperationJobServiceGetJobTemplate = "/apis.aistudio.job.v1.JobService/GetJobTemplate"
const OperationJobServiceGetJobTemplateTrigger = "/apis.aistudio.job.v1.JobService/GetJobTemplateTrigger"
const OperationJobServiceGetJobVolumes = "/apis.aistudio.job.v1.JobService/GetJobVolumes"
const OperationJobServiceGetTensorboard = "/apis.aistudio.job.v1.JobService/GetTensorboard"
const OperationJobServiceListJobTemplateTriggers = "/apis.aistudio.job.v1.JobService/ListJobTemplateTriggers"
const OperationJobServiceListJobTemplateViews = "/apis.aistudio.job.v1.JobService/ListJobTemplateViews"
const OperationJobServiceListJobs = "/apis.aistudio.job.v1.JobService/ListJobs"
const OperationJobServicePauseJobTemplateTrigger = "/apis.aistudio.job.v1.JobService/PauseJobTemplateTrigger"
const OperationJobServiceRecoverJobTemplateTrigger = "/apis.aistudio.job.v1.JobService/RecoverJobTemplateTrigger"
const OperationJobServiceRedeployJob = "/apis.aistudio.job.v1.JobService/RedeployJob"
const OperationJobServiceStopJob = "/apis.aistudio.job.v1.JobService/StopJob"
const OperationJobServiceStopJobTask = "/apis.aistudio.job.v1.JobService/StopJobTask"
const OperationJobServiceSubmitJob = "/apis.aistudio.job.v1.JobService/SubmitJob"
const OperationJobServiceUpdateJob = "/apis.aistudio.job.v1.JobService/UpdateJob"
const OperationJobServiceUpdateJobTemplate = "/apis.aistudio.job.v1.JobService/UpdateJobTemplate"
const OperationJobServiceUpdateJobTemplateTrigger = "/apis.aistudio.job.v1.JobService/UpdateJobTemplateTrigger"
const OperationJobServiceUpdateMembers = "/apis.aistudio.job.v1.JobService/UpdateMembers"

type JobServiceHTTPServer interface {
	// CheckJobIsExist CheckJobIsExist 检查任务是否存在
	CheckJobIsExist(context.Context, *CheckJobIsExistRequest) (*CheckJobIsExistResponse, error)
	// CreateJobTemplate CreateJobTemplate 创建任务模板
	CreateJobTemplate(context.Context, *CreateOrUpdateJobTemplateRequest) (*emptypb.Empty, error)
	// CreateJobTemplateTrigger CreateJobTemplateTrigger 创建任务模板触发器
	CreateJobTemplateTrigger(context.Context, *CreateOrUpdateJobTemplateTriggerRequest) (*emptypb.Empty, error)
	CreateTensorboard(context.Context, *CreateTensorboardRequest) (*emptypb.Empty, error)
	DeleteJob(context.Context, *DeleteJobRequest) (*emptypb.Empty, error)
	// DeleteJobTemplate DeleteJobTemplate 删除任务模板
	DeleteJobTemplate(context.Context, *DeleteJobTemplateRequest) (*emptypb.Empty, error)
	// DeleteJobTemplateTrigger DeleteJobTemplateTrigger 删除任务模板触发器
	DeleteJobTemplateTrigger(context.Context, *DeleteJobTemplateTriggerRequest) (*emptypb.Empty, error)
	// GetJobBase GetJobBase 获取训练任务基本信息，不带任务状态用于复制
	GetJobBase(context.Context, *GetJobBaseRequest) (*Job, error)
	// GetJobDetail GetJobDetail 获取训练任务详情，带有任务状态用于任务详情展示
	GetJobDetail(context.Context, *GetJobRequest) (*Job, error)
	// GetJobTasks 获取运行实例
	GetJobTasks(context.Context, *GetJobTasksRequest) (*ListJobTaskStatus, error)
	// GetJobTemplate GetJobTemplate 获取任务模板
	GetJobTemplate(context.Context, *GetJobTemplateRequest) (*JobTemplate, error)
	// GetJobTemplateTrigger GetJobTemplateTrigger 获取任务模板触发器
	GetJobTemplateTrigger(context.Context, *GetJobTemplateTriggerRequest) (*Trigger, error)
	GetJobVolumes(context.Context, *GetJobVolumeRequest) (*ListJobVolumes, error)
	GetTensorboard(context.Context, *GetTensorboardRequest) (*GetTensorboardResponse, error)
	// ListJobTemplateTriggers ListJobTemplateTriggers 获取任务模板触发器
	ListJobTemplateTriggers(context.Context, *ListJobTemplateTriggersRequest) (*ListJobTemplateTriggersResponse, error)
	// ListJobTemplateViews ListJobTemplates 列出任务模板
	ListJobTemplateViews(context.Context, *ListJobTemplateViewsOptions) (*ListJobTemplateViewsResponse, error)
	ListJobs(context.Context, *ListJobsOptions) (*ListJobsResponse, error)
	// PauseJobTemplateTrigger PauseJobTemplateTrigger 暂停任务模板触发器
	PauseJobTemplateTrigger(context.Context, *PauseJobTemplateTriggerRequest) (*emptypb.Empty, error)
	// RecoverJobTemplateTrigger RecoverJobTemplateTrigger 恢复任务模板触发器
	RecoverJobTemplateTrigger(context.Context, *RecoverJobTemplateTriggerRequest) (*emptypb.Empty, error)
	RedeployJob(context.Context, *RedeployJobRequest) (*emptypb.Empty, error)
	// StopJob 停止训练任务
	StopJob(context.Context, *StopJobRequest) (*emptypb.Empty, error)
	// StopJobTask 强制停止任务实例
	StopJobTask(context.Context, *StopJobTaskRequest) (*emptypb.Empty, error)
	// SubmitJob 提交训练任务
	SubmitJob(context.Context, *CreateJobRequest) (*CreateJobResponse, error)
	UpdateJob(context.Context, *UpdateJobRequest) (*emptypb.Empty, error)
	// UpdateJobTemplate UpdateJobTemplate 更新任务模板
	UpdateJobTemplate(context.Context, *CreateOrUpdateJobTemplateRequest) (*emptypb.Empty, error)
	// UpdateJobTemplateTrigger UpdateJobTemplateTrigger 更新任务模板触发器
	UpdateJobTemplateTrigger(context.Context, *CreateOrUpdateJobTemplateTriggerRequest) (*emptypb.Empty, error)
	// UpdateMembers UpdateMembers 更新成员
	UpdateMembers(context.Context, *UpdateMembersRequest) (*emptypb.Empty, error)
}

func RegisterJobServiceHTTPServer(s *http.Server, srv JobServiceHTTPServer) {
	r := s.Route("/")
	r.POST("/apis/v1/workspace/{workspaceName}/job_templates", _JobService_CreateJobTemplate0_HTTP_Handler(srv))
	r.PUT("/apis/v1/workspace/{workspaceName}/job_template/{name}", _JobService_UpdateJobTemplate0_HTTP_Handler(srv))
	r.GET("/apis/v1/workspace/{workspaceName}/job_template/{jobTemplateName}", _JobService_GetJobTemplate0_HTTP_Handler(srv))
	r.GET("/apis/v1/workspace/{workspaceName}/job_templates", _JobService_ListJobTemplateViews0_HTTP_Handler(srv))
	r.DELETE("/apis/v1/workspace/{workspaceName}/job_template/{jobTemplateName}", _JobService_DeleteJobTemplate0_HTTP_Handler(srv))
	r.POST("/apis/v1/workspace/{workspaceName}/job_template/{jobTemplateName}/trigger", _JobService_CreateJobTemplateTrigger0_HTTP_Handler(srv))
	r.PUT("/apis/v1/workspace/{workspaceName}/job_template/{jobTemplateName}/trigger/{triggerName}", _JobService_UpdateJobTemplateTrigger0_HTTP_Handler(srv))
	r.DELETE("/apis/v1/workspace/{workspaceName}/job_template/{jobTemplateName}/trigger/{triggerName}", _JobService_DeleteJobTemplateTrigger0_HTTP_Handler(srv))
	r.GET("/apis/v1/workspace/{workspaceName}/job_template/{jobTemplateName}/trigger/{triggerName}", _JobService_GetJobTemplateTrigger0_HTTP_Handler(srv))
	r.GET("/apis/v1/workspace/{workspaceName}/job_template/{jobTemplateName}/triggers", _JobService_ListJobTemplateTriggers0_HTTP_Handler(srv))
	r.POST("/apis/v1/workspace/{workspaceName}/job_template/{jobTemplateName}/trigger/{triggerName}/pause", _JobService_PauseJobTemplateTrigger0_HTTP_Handler(srv))
	r.POST("/apis/v1/workspace/{workspaceName}/job_template/{jobTemplateName}/trigger/{triggerName}/recover", _JobService_RecoverJobTemplateTrigger0_HTTP_Handler(srv))
	r.POST("/apis/v1/workspace/{workspaceName}/jobs", _JobService_SubmitJob0_HTTP_Handler(srv))
	r.POST("/apis/v1/workspace/{workspaceName}/job/{jobName}/stop", _JobService_StopJob0_HTTP_Handler(srv))
	r.PUT("/apis/v1/workspace/{workspaceName}/job/{jobName}", _JobService_UpdateJob0_HTTP_Handler(srv))
	r.GET("/apis/v1/workspace/{workspaceName}/jobs/{jobName}/base", _JobService_GetJobBase0_HTTP_Handler(srv))
	r.GET("/apis/v1/workspace/{workspaceName}/jobs/{jobName}/detail", _JobService_GetJobDetail0_HTTP_Handler(srv))
	r.DELETE("/apis/v1/workspace/{workspaceName}/job/{jobName}", _JobService_DeleteJob0_HTTP_Handler(srv))
	r.GET("/apis/v1/workspace/{workspaceName}/jobs", _JobService_ListJobs0_HTTP_Handler(srv))
	r.POST("/apis/v1/workspace/{workspaceName}/job/{jobName}/tensorboard", _JobService_CreateTensorboard0_HTTP_Handler(srv))
	r.GET("/apis/v1/workspace/{workspaceName}/job/{jobName}/tensorboard", _JobService_GetTensorboard0_HTTP_Handler(srv))
	r.GET("/apis/v1/workspace/{workspaceName}/job/{jobName}/tasks", _JobService_GetJobTasks0_HTTP_Handler(srv))
	r.GET("/apis/v1/workspace/{workspaceName}/job/{jobName}/volumes", _JobService_GetJobVolumes0_HTTP_Handler(srv))
	r.POST("/apis/v1/workspace/{workspaceName}/job/{jobName}/task/{taskID}/stop", _JobService_StopJobTask0_HTTP_Handler(srv))
	r.POST("/apis/v1/workspace/{workspaceName}/job/{jobName}/redeploy", _JobService_RedeployJob0_HTTP_Handler(srv))
	r.GET("/apis/v1/workspace/{workspaceName}/job/{jobName}/job_exist", _JobService_CheckJobIsExist0_HTTP_Handler(srv))
	r.PUT("/apis/v1/workspace/{workspaceName}/job/{jobName}/update_members", _JobService_UpdateMembers0_HTTP_Handler(srv))
}

func _JobService_CreateJobTemplate0_HTTP_Handler(srv JobServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateOrUpdateJobTemplateRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationJobServiceCreateJobTemplate)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateJobTemplate(ctx, req.(*CreateOrUpdateJobTemplateRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _JobService_UpdateJobTemplate0_HTTP_Handler(srv JobServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateOrUpdateJobTemplateRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationJobServiceUpdateJobTemplate)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateJobTemplate(ctx, req.(*CreateOrUpdateJobTemplateRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _JobService_GetJobTemplate0_HTTP_Handler(srv JobServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetJobTemplateRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationJobServiceGetJobTemplate)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetJobTemplate(ctx, req.(*GetJobTemplateRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*JobTemplate)
		return ctx.Result(200, reply)
	}
}

func _JobService_ListJobTemplateViews0_HTTP_Handler(srv JobServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListJobTemplateViewsOptions
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationJobServiceListJobTemplateViews)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListJobTemplateViews(ctx, req.(*ListJobTemplateViewsOptions))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListJobTemplateViewsResponse)
		return ctx.Result(200, reply)
	}
}

func _JobService_DeleteJobTemplate0_HTTP_Handler(srv JobServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteJobTemplateRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationJobServiceDeleteJobTemplate)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteJobTemplate(ctx, req.(*DeleteJobTemplateRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _JobService_CreateJobTemplateTrigger0_HTTP_Handler(srv JobServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateOrUpdateJobTemplateTriggerRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationJobServiceCreateJobTemplateTrigger)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateJobTemplateTrigger(ctx, req.(*CreateOrUpdateJobTemplateTriggerRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _JobService_UpdateJobTemplateTrigger0_HTTP_Handler(srv JobServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateOrUpdateJobTemplateTriggerRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationJobServiceUpdateJobTemplateTrigger)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateJobTemplateTrigger(ctx, req.(*CreateOrUpdateJobTemplateTriggerRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _JobService_DeleteJobTemplateTrigger0_HTTP_Handler(srv JobServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteJobTemplateTriggerRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationJobServiceDeleteJobTemplateTrigger)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteJobTemplateTrigger(ctx, req.(*DeleteJobTemplateTriggerRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _JobService_GetJobTemplateTrigger0_HTTP_Handler(srv JobServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetJobTemplateTriggerRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationJobServiceGetJobTemplateTrigger)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetJobTemplateTrigger(ctx, req.(*GetJobTemplateTriggerRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Trigger)
		return ctx.Result(200, reply)
	}
}

func _JobService_ListJobTemplateTriggers0_HTTP_Handler(srv JobServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListJobTemplateTriggersRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationJobServiceListJobTemplateTriggers)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListJobTemplateTriggers(ctx, req.(*ListJobTemplateTriggersRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListJobTemplateTriggersResponse)
		return ctx.Result(200, reply)
	}
}

func _JobService_PauseJobTemplateTrigger0_HTTP_Handler(srv JobServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in PauseJobTemplateTriggerRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationJobServicePauseJobTemplateTrigger)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.PauseJobTemplateTrigger(ctx, req.(*PauseJobTemplateTriggerRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _JobService_RecoverJobTemplateTrigger0_HTTP_Handler(srv JobServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in RecoverJobTemplateTriggerRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationJobServiceRecoverJobTemplateTrigger)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.RecoverJobTemplateTrigger(ctx, req.(*RecoverJobTemplateTriggerRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _JobService_SubmitJob0_HTTP_Handler(srv JobServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateJobRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationJobServiceSubmitJob)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SubmitJob(ctx, req.(*CreateJobRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CreateJobResponse)
		return ctx.Result(200, reply)
	}
}

func _JobService_StopJob0_HTTP_Handler(srv JobServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in StopJobRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationJobServiceStopJob)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.StopJob(ctx, req.(*StopJobRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _JobService_UpdateJob0_HTTP_Handler(srv JobServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UpdateJobRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationJobServiceUpdateJob)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateJob(ctx, req.(*UpdateJobRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _JobService_GetJobBase0_HTTP_Handler(srv JobServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetJobBaseRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationJobServiceGetJobBase)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetJobBase(ctx, req.(*GetJobBaseRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Job)
		return ctx.Result(200, reply)
	}
}

func _JobService_GetJobDetail0_HTTP_Handler(srv JobServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetJobRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationJobServiceGetJobDetail)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetJobDetail(ctx, req.(*GetJobRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Job)
		return ctx.Result(200, reply)
	}
}

func _JobService_DeleteJob0_HTTP_Handler(srv JobServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteJobRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationJobServiceDeleteJob)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteJob(ctx, req.(*DeleteJobRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _JobService_ListJobs0_HTTP_Handler(srv JobServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListJobsOptions
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationJobServiceListJobs)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListJobs(ctx, req.(*ListJobsOptions))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListJobsResponse)
		return ctx.Result(200, reply)
	}
}

func _JobService_CreateTensorboard0_HTTP_Handler(srv JobServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateTensorboardRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationJobServiceCreateTensorboard)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateTensorboard(ctx, req.(*CreateTensorboardRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _JobService_GetTensorboard0_HTTP_Handler(srv JobServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetTensorboardRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationJobServiceGetTensorboard)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetTensorboard(ctx, req.(*GetTensorboardRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetTensorboardResponse)
		return ctx.Result(200, reply)
	}
}

func _JobService_GetJobTasks0_HTTP_Handler(srv JobServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetJobTasksRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationJobServiceGetJobTasks)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetJobTasks(ctx, req.(*GetJobTasksRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListJobTaskStatus)
		return ctx.Result(200, reply)
	}
}

func _JobService_GetJobVolumes0_HTTP_Handler(srv JobServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetJobVolumeRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationJobServiceGetJobVolumes)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetJobVolumes(ctx, req.(*GetJobVolumeRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListJobVolumes)
		return ctx.Result(200, reply)
	}
}

func _JobService_StopJobTask0_HTTP_Handler(srv JobServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in StopJobTaskRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationJobServiceStopJobTask)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.StopJobTask(ctx, req.(*StopJobTaskRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _JobService_RedeployJob0_HTTP_Handler(srv JobServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in RedeployJobRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationJobServiceRedeployJob)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.RedeployJob(ctx, req.(*RedeployJobRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _JobService_CheckJobIsExist0_HTTP_Handler(srv JobServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CheckJobIsExistRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationJobServiceCheckJobIsExist)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CheckJobIsExist(ctx, req.(*CheckJobIsExistRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CheckJobIsExistResponse)
		return ctx.Result(200, reply)
	}
}

func _JobService_UpdateMembers0_HTTP_Handler(srv JobServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UpdateMembersRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationJobServiceUpdateMembers)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateMembers(ctx, req.(*UpdateMembersRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

type JobServiceHTTPClient interface {
	CheckJobIsExist(ctx context.Context, req *CheckJobIsExistRequest, opts ...http.CallOption) (rsp *CheckJobIsExistResponse, err error)
	CreateJobTemplate(ctx context.Context, req *CreateOrUpdateJobTemplateRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	CreateJobTemplateTrigger(ctx context.Context, req *CreateOrUpdateJobTemplateTriggerRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	CreateTensorboard(ctx context.Context, req *CreateTensorboardRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	DeleteJob(ctx context.Context, req *DeleteJobRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	DeleteJobTemplate(ctx context.Context, req *DeleteJobTemplateRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	DeleteJobTemplateTrigger(ctx context.Context, req *DeleteJobTemplateTriggerRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	GetJobBase(ctx context.Context, req *GetJobBaseRequest, opts ...http.CallOption) (rsp *Job, err error)
	GetJobDetail(ctx context.Context, req *GetJobRequest, opts ...http.CallOption) (rsp *Job, err error)
	GetJobTasks(ctx context.Context, req *GetJobTasksRequest, opts ...http.CallOption) (rsp *ListJobTaskStatus, err error)
	GetJobTemplate(ctx context.Context, req *GetJobTemplateRequest, opts ...http.CallOption) (rsp *JobTemplate, err error)
	GetJobTemplateTrigger(ctx context.Context, req *GetJobTemplateTriggerRequest, opts ...http.CallOption) (rsp *Trigger, err error)
	GetJobVolumes(ctx context.Context, req *GetJobVolumeRequest, opts ...http.CallOption) (rsp *ListJobVolumes, err error)
	GetTensorboard(ctx context.Context, req *GetTensorboardRequest, opts ...http.CallOption) (rsp *GetTensorboardResponse, err error)
	ListJobTemplateTriggers(ctx context.Context, req *ListJobTemplateTriggersRequest, opts ...http.CallOption) (rsp *ListJobTemplateTriggersResponse, err error)
	ListJobTemplateViews(ctx context.Context, req *ListJobTemplateViewsOptions, opts ...http.CallOption) (rsp *ListJobTemplateViewsResponse, err error)
	ListJobs(ctx context.Context, req *ListJobsOptions, opts ...http.CallOption) (rsp *ListJobsResponse, err error)
	PauseJobTemplateTrigger(ctx context.Context, req *PauseJobTemplateTriggerRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	RecoverJobTemplateTrigger(ctx context.Context, req *RecoverJobTemplateTriggerRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	RedeployJob(ctx context.Context, req *RedeployJobRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	StopJob(ctx context.Context, req *StopJobRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	StopJobTask(ctx context.Context, req *StopJobTaskRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	SubmitJob(ctx context.Context, req *CreateJobRequest, opts ...http.CallOption) (rsp *CreateJobResponse, err error)
	UpdateJob(ctx context.Context, req *UpdateJobRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	UpdateJobTemplate(ctx context.Context, req *CreateOrUpdateJobTemplateRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	UpdateJobTemplateTrigger(ctx context.Context, req *CreateOrUpdateJobTemplateTriggerRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	UpdateMembers(ctx context.Context, req *UpdateMembersRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
}

type JobServiceHTTPClientImpl struct {
	cc *http.Client
}

func NewJobServiceHTTPClient(client *http.Client) JobServiceHTTPClient {
	return &JobServiceHTTPClientImpl{client}
}

func (c *JobServiceHTTPClientImpl) CheckJobIsExist(ctx context.Context, in *CheckJobIsExistRequest, opts ...http.CallOption) (*CheckJobIsExistResponse, error) {
	var out CheckJobIsExistResponse
	pattern := "/apis/v1/workspace/{workspaceName}/job/{jobName}/job_exist"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationJobServiceCheckJobIsExist))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *JobServiceHTTPClientImpl) CreateJobTemplate(ctx context.Context, in *CreateOrUpdateJobTemplateRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/apis/v1/workspace/{workspaceName}/job_templates"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationJobServiceCreateJobTemplate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *JobServiceHTTPClientImpl) CreateJobTemplateTrigger(ctx context.Context, in *CreateOrUpdateJobTemplateTriggerRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/apis/v1/workspace/{workspaceName}/job_template/{jobTemplateName}/trigger"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationJobServiceCreateJobTemplateTrigger))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *JobServiceHTTPClientImpl) CreateTensorboard(ctx context.Context, in *CreateTensorboardRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/apis/v1/workspace/{workspaceName}/job/{jobName}/tensorboard"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationJobServiceCreateTensorboard))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *JobServiceHTTPClientImpl) DeleteJob(ctx context.Context, in *DeleteJobRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/apis/v1/workspace/{workspaceName}/job/{jobName}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationJobServiceDeleteJob))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *JobServiceHTTPClientImpl) DeleteJobTemplate(ctx context.Context, in *DeleteJobTemplateRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/apis/v1/workspace/{workspaceName}/job_template/{jobTemplateName}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationJobServiceDeleteJobTemplate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *JobServiceHTTPClientImpl) DeleteJobTemplateTrigger(ctx context.Context, in *DeleteJobTemplateTriggerRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/apis/v1/workspace/{workspaceName}/job_template/{jobTemplateName}/trigger/{triggerName}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationJobServiceDeleteJobTemplateTrigger))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *JobServiceHTTPClientImpl) GetJobBase(ctx context.Context, in *GetJobBaseRequest, opts ...http.CallOption) (*Job, error) {
	var out Job
	pattern := "/apis/v1/workspace/{workspaceName}/jobs/{jobName}/base"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationJobServiceGetJobBase))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *JobServiceHTTPClientImpl) GetJobDetail(ctx context.Context, in *GetJobRequest, opts ...http.CallOption) (*Job, error) {
	var out Job
	pattern := "/apis/v1/workspace/{workspaceName}/jobs/{jobName}/detail"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationJobServiceGetJobDetail))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *JobServiceHTTPClientImpl) GetJobTasks(ctx context.Context, in *GetJobTasksRequest, opts ...http.CallOption) (*ListJobTaskStatus, error) {
	var out ListJobTaskStatus
	pattern := "/apis/v1/workspace/{workspaceName}/job/{jobName}/tasks"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationJobServiceGetJobTasks))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *JobServiceHTTPClientImpl) GetJobTemplate(ctx context.Context, in *GetJobTemplateRequest, opts ...http.CallOption) (*JobTemplate, error) {
	var out JobTemplate
	pattern := "/apis/v1/workspace/{workspaceName}/job_template/{jobTemplateName}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationJobServiceGetJobTemplate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *JobServiceHTTPClientImpl) GetJobTemplateTrigger(ctx context.Context, in *GetJobTemplateTriggerRequest, opts ...http.CallOption) (*Trigger, error) {
	var out Trigger
	pattern := "/apis/v1/workspace/{workspaceName}/job_template/{jobTemplateName}/trigger/{triggerName}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationJobServiceGetJobTemplateTrigger))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *JobServiceHTTPClientImpl) GetJobVolumes(ctx context.Context, in *GetJobVolumeRequest, opts ...http.CallOption) (*ListJobVolumes, error) {
	var out ListJobVolumes
	pattern := "/apis/v1/workspace/{workspaceName}/job/{jobName}/volumes"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationJobServiceGetJobVolumes))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *JobServiceHTTPClientImpl) GetTensorboard(ctx context.Context, in *GetTensorboardRequest, opts ...http.CallOption) (*GetTensorboardResponse, error) {
	var out GetTensorboardResponse
	pattern := "/apis/v1/workspace/{workspaceName}/job/{jobName}/tensorboard"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationJobServiceGetTensorboard))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *JobServiceHTTPClientImpl) ListJobTemplateTriggers(ctx context.Context, in *ListJobTemplateTriggersRequest, opts ...http.CallOption) (*ListJobTemplateTriggersResponse, error) {
	var out ListJobTemplateTriggersResponse
	pattern := "/apis/v1/workspace/{workspaceName}/job_template/{jobTemplateName}/triggers"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationJobServiceListJobTemplateTriggers))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *JobServiceHTTPClientImpl) ListJobTemplateViews(ctx context.Context, in *ListJobTemplateViewsOptions, opts ...http.CallOption) (*ListJobTemplateViewsResponse, error) {
	var out ListJobTemplateViewsResponse
	pattern := "/apis/v1/workspace/{workspaceName}/job_templates"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationJobServiceListJobTemplateViews))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *JobServiceHTTPClientImpl) ListJobs(ctx context.Context, in *ListJobsOptions, opts ...http.CallOption) (*ListJobsResponse, error) {
	var out ListJobsResponse
	pattern := "/apis/v1/workspace/{workspaceName}/jobs"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationJobServiceListJobs))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *JobServiceHTTPClientImpl) PauseJobTemplateTrigger(ctx context.Context, in *PauseJobTemplateTriggerRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/apis/v1/workspace/{workspaceName}/job_template/{jobTemplateName}/trigger/{triggerName}/pause"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationJobServicePauseJobTemplateTrigger))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *JobServiceHTTPClientImpl) RecoverJobTemplateTrigger(ctx context.Context, in *RecoverJobTemplateTriggerRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/apis/v1/workspace/{workspaceName}/job_template/{jobTemplateName}/trigger/{triggerName}/recover"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationJobServiceRecoverJobTemplateTrigger))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *JobServiceHTTPClientImpl) RedeployJob(ctx context.Context, in *RedeployJobRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/apis/v1/workspace/{workspaceName}/job/{jobName}/redeploy"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationJobServiceRedeployJob))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *JobServiceHTTPClientImpl) StopJob(ctx context.Context, in *StopJobRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/apis/v1/workspace/{workspaceName}/job/{jobName}/stop"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationJobServiceStopJob))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *JobServiceHTTPClientImpl) StopJobTask(ctx context.Context, in *StopJobTaskRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/apis/v1/workspace/{workspaceName}/job/{jobName}/task/{taskID}/stop"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationJobServiceStopJobTask))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *JobServiceHTTPClientImpl) SubmitJob(ctx context.Context, in *CreateJobRequest, opts ...http.CallOption) (*CreateJobResponse, error) {
	var out CreateJobResponse
	pattern := "/apis/v1/workspace/{workspaceName}/jobs"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationJobServiceSubmitJob))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *JobServiceHTTPClientImpl) UpdateJob(ctx context.Context, in *UpdateJobRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/apis/v1/workspace/{workspaceName}/job/{jobName}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationJobServiceUpdateJob))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *JobServiceHTTPClientImpl) UpdateJobTemplate(ctx context.Context, in *CreateOrUpdateJobTemplateRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/apis/v1/workspace/{workspaceName}/job_template/{name}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationJobServiceUpdateJobTemplate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *JobServiceHTTPClientImpl) UpdateJobTemplateTrigger(ctx context.Context, in *CreateOrUpdateJobTemplateTriggerRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/apis/v1/workspace/{workspaceName}/job_template/{jobTemplateName}/trigger/{triggerName}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationJobServiceUpdateJobTemplateTrigger))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *JobServiceHTTPClientImpl) UpdateMembers(ctx context.Context, in *UpdateMembersRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/apis/v1/workspace/{workspaceName}/job/{jobName}/update_members"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationJobServiceUpdateMembers))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
