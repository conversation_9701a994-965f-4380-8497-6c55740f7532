// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v5.29.3
// source: aistudio/job/v1/job.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	JobService_CreateJobTemplate_FullMethodName         = "/apis.aistudio.job.v1.JobService/CreateJobTemplate"
	JobService_UpdateJobTemplate_FullMethodName         = "/apis.aistudio.job.v1.JobService/UpdateJobTemplate"
	JobService_GetJobTemplate_FullMethodName            = "/apis.aistudio.job.v1.JobService/GetJobTemplate"
	JobService_ListJobTemplateViews_FullMethodName      = "/apis.aistudio.job.v1.JobService/ListJobTemplateViews"
	JobService_DeleteJobTemplate_FullMethodName         = "/apis.aistudio.job.v1.JobService/DeleteJobTemplate"
	JobService_CreateJobTemplateTrigger_FullMethodName  = "/apis.aistudio.job.v1.JobService/CreateJobTemplateTrigger"
	JobService_UpdateJobTemplateTrigger_FullMethodName  = "/apis.aistudio.job.v1.JobService/UpdateJobTemplateTrigger"
	JobService_DeleteJobTemplateTrigger_FullMethodName  = "/apis.aistudio.job.v1.JobService/DeleteJobTemplateTrigger"
	JobService_GetJobTemplateTrigger_FullMethodName     = "/apis.aistudio.job.v1.JobService/GetJobTemplateTrigger"
	JobService_ListJobTemplateTriggers_FullMethodName   = "/apis.aistudio.job.v1.JobService/ListJobTemplateTriggers"
	JobService_PauseJobTemplateTrigger_FullMethodName   = "/apis.aistudio.job.v1.JobService/PauseJobTemplateTrigger"
	JobService_RecoverJobTemplateTrigger_FullMethodName = "/apis.aistudio.job.v1.JobService/RecoverJobTemplateTrigger"
	JobService_SubmitJob_FullMethodName                 = "/apis.aistudio.job.v1.JobService/SubmitJob"
	JobService_StopJob_FullMethodName                   = "/apis.aistudio.job.v1.JobService/StopJob"
	JobService_UpdateJob_FullMethodName                 = "/apis.aistudio.job.v1.JobService/UpdateJob"
	JobService_GetJobBase_FullMethodName                = "/apis.aistudio.job.v1.JobService/GetJobBase"
	JobService_GetJobDetail_FullMethodName              = "/apis.aistudio.job.v1.JobService/GetJobDetail"
	JobService_DeleteJob_FullMethodName                 = "/apis.aistudio.job.v1.JobService/DeleteJob"
	JobService_ListJobs_FullMethodName                  = "/apis.aistudio.job.v1.JobService/ListJobs"
	JobService_CreateTensorboard_FullMethodName         = "/apis.aistudio.job.v1.JobService/CreateTensorboard"
	JobService_GetTensorboard_FullMethodName            = "/apis.aistudio.job.v1.JobService/GetTensorboard"
	JobService_GetJobTasks_FullMethodName               = "/apis.aistudio.job.v1.JobService/GetJobTasks"
	JobService_GetJobVolumes_FullMethodName             = "/apis.aistudio.job.v1.JobService/GetJobVolumes"
	JobService_StopJobTask_FullMethodName               = "/apis.aistudio.job.v1.JobService/StopJobTask"
	JobService_RedeployJob_FullMethodName               = "/apis.aistudio.job.v1.JobService/RedeployJob"
	JobService_CheckJobIsExist_FullMethodName           = "/apis.aistudio.job.v1.JobService/CheckJobIsExist"
	JobService_UpdateMembers_FullMethodName             = "/apis.aistudio.job.v1.JobService/UpdateMembers"
)

// JobServiceClient is the client API for JobService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type JobServiceClient interface {
	// CreateJobTemplate 创建任务模板
	CreateJobTemplate(ctx context.Context, in *CreateOrUpdateJobTemplateRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// UpdateJobTemplate 更新任务模板
	UpdateJobTemplate(ctx context.Context, in *CreateOrUpdateJobTemplateRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// GetJobTemplate 获取任务模板
	GetJobTemplate(ctx context.Context, in *GetJobTemplateRequest, opts ...grpc.CallOption) (*JobTemplate, error)
	// ListJobTemplates 列出任务模板
	ListJobTemplateViews(ctx context.Context, in *ListJobTemplateViewsOptions, opts ...grpc.CallOption) (*ListJobTemplateViewsResponse, error)
	// DeleteJobTemplate 删除任务模板
	DeleteJobTemplate(ctx context.Context, in *DeleteJobTemplateRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// CreateJobTemplateTrigger 创建任务模板触发器
	CreateJobTemplateTrigger(ctx context.Context, in *CreateOrUpdateJobTemplateTriggerRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// UpdateJobTemplateTrigger 更新任务模板触发器
	UpdateJobTemplateTrigger(ctx context.Context, in *CreateOrUpdateJobTemplateTriggerRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// DeleteJobTemplateTrigger 删除任务模板触发器
	DeleteJobTemplateTrigger(ctx context.Context, in *DeleteJobTemplateTriggerRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// GetJobTemplateTrigger 获取任务模板触发器
	GetJobTemplateTrigger(ctx context.Context, in *GetJobTemplateTriggerRequest, opts ...grpc.CallOption) (*Trigger, error)
	// ListJobTemplateTriggers 获取任务模板触发器
	ListJobTemplateTriggers(ctx context.Context, in *ListJobTemplateTriggersRequest, opts ...grpc.CallOption) (*ListJobTemplateTriggersResponse, error)
	// PauseJobTemplateTrigger 暂停任务模板触发器
	PauseJobTemplateTrigger(ctx context.Context, in *PauseJobTemplateTriggerRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// RecoverJobTemplateTrigger 恢复任务模板触发器
	RecoverJobTemplateTrigger(ctx context.Context, in *RecoverJobTemplateTriggerRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 提交训练任务
	SubmitJob(ctx context.Context, in *CreateJobRequest, opts ...grpc.CallOption) (*CreateJobResponse, error)
	// 停止训练任务
	StopJob(ctx context.Context, in *StopJobRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	UpdateJob(ctx context.Context, in *UpdateJobRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// GetJobBase 获取训练任务基本信息，不带任务状态用于复制
	GetJobBase(ctx context.Context, in *GetJobBaseRequest, opts ...grpc.CallOption) (*Job, error)
	// GetJobDetail 获取训练任务详情，带有任务状态用于任务详情展示
	GetJobDetail(ctx context.Context, in *GetJobRequest, opts ...grpc.CallOption) (*Job, error)
	DeleteJob(ctx context.Context, in *DeleteJobRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	ListJobs(ctx context.Context, in *ListJobsOptions, opts ...grpc.CallOption) (*ListJobsResponse, error)
	CreateTensorboard(ctx context.Context, in *CreateTensorboardRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	GetTensorboard(ctx context.Context, in *GetTensorboardRequest, opts ...grpc.CallOption) (*GetTensorboardResponse, error)
	// 获取运行实例
	GetJobTasks(ctx context.Context, in *GetJobTasksRequest, opts ...grpc.CallOption) (*ListJobTaskStatus, error)
	GetJobVolumes(ctx context.Context, in *GetJobVolumeRequest, opts ...grpc.CallOption) (*ListJobVolumes, error)
	// 强制停止任务实例
	StopJobTask(ctx context.Context, in *StopJobTaskRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	RedeployJob(ctx context.Context, in *RedeployJobRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// CheckJobIsExist 检查任务是否存在
	CheckJobIsExist(ctx context.Context, in *CheckJobIsExistRequest, opts ...grpc.CallOption) (*CheckJobIsExistResponse, error)
	// UpdateMembers 更新成员
	UpdateMembers(ctx context.Context, in *UpdateMembersRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
}

type jobServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewJobServiceClient(cc grpc.ClientConnInterface) JobServiceClient {
	return &jobServiceClient{cc}
}

func (c *jobServiceClient) CreateJobTemplate(ctx context.Context, in *CreateOrUpdateJobTemplateRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, JobService_CreateJobTemplate_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jobServiceClient) UpdateJobTemplate(ctx context.Context, in *CreateOrUpdateJobTemplateRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, JobService_UpdateJobTemplate_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jobServiceClient) GetJobTemplate(ctx context.Context, in *GetJobTemplateRequest, opts ...grpc.CallOption) (*JobTemplate, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(JobTemplate)
	err := c.cc.Invoke(ctx, JobService_GetJobTemplate_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jobServiceClient) ListJobTemplateViews(ctx context.Context, in *ListJobTemplateViewsOptions, opts ...grpc.CallOption) (*ListJobTemplateViewsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListJobTemplateViewsResponse)
	err := c.cc.Invoke(ctx, JobService_ListJobTemplateViews_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jobServiceClient) DeleteJobTemplate(ctx context.Context, in *DeleteJobTemplateRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, JobService_DeleteJobTemplate_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jobServiceClient) CreateJobTemplateTrigger(ctx context.Context, in *CreateOrUpdateJobTemplateTriggerRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, JobService_CreateJobTemplateTrigger_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jobServiceClient) UpdateJobTemplateTrigger(ctx context.Context, in *CreateOrUpdateJobTemplateTriggerRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, JobService_UpdateJobTemplateTrigger_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jobServiceClient) DeleteJobTemplateTrigger(ctx context.Context, in *DeleteJobTemplateTriggerRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, JobService_DeleteJobTemplateTrigger_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jobServiceClient) GetJobTemplateTrigger(ctx context.Context, in *GetJobTemplateTriggerRequest, opts ...grpc.CallOption) (*Trigger, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(Trigger)
	err := c.cc.Invoke(ctx, JobService_GetJobTemplateTrigger_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jobServiceClient) ListJobTemplateTriggers(ctx context.Context, in *ListJobTemplateTriggersRequest, opts ...grpc.CallOption) (*ListJobTemplateTriggersResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListJobTemplateTriggersResponse)
	err := c.cc.Invoke(ctx, JobService_ListJobTemplateTriggers_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jobServiceClient) PauseJobTemplateTrigger(ctx context.Context, in *PauseJobTemplateTriggerRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, JobService_PauseJobTemplateTrigger_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jobServiceClient) RecoverJobTemplateTrigger(ctx context.Context, in *RecoverJobTemplateTriggerRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, JobService_RecoverJobTemplateTrigger_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jobServiceClient) SubmitJob(ctx context.Context, in *CreateJobRequest, opts ...grpc.CallOption) (*CreateJobResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateJobResponse)
	err := c.cc.Invoke(ctx, JobService_SubmitJob_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jobServiceClient) StopJob(ctx context.Context, in *StopJobRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, JobService_StopJob_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jobServiceClient) UpdateJob(ctx context.Context, in *UpdateJobRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, JobService_UpdateJob_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jobServiceClient) GetJobBase(ctx context.Context, in *GetJobBaseRequest, opts ...grpc.CallOption) (*Job, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(Job)
	err := c.cc.Invoke(ctx, JobService_GetJobBase_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jobServiceClient) GetJobDetail(ctx context.Context, in *GetJobRequest, opts ...grpc.CallOption) (*Job, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(Job)
	err := c.cc.Invoke(ctx, JobService_GetJobDetail_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jobServiceClient) DeleteJob(ctx context.Context, in *DeleteJobRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, JobService_DeleteJob_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jobServiceClient) ListJobs(ctx context.Context, in *ListJobsOptions, opts ...grpc.CallOption) (*ListJobsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListJobsResponse)
	err := c.cc.Invoke(ctx, JobService_ListJobs_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jobServiceClient) CreateTensorboard(ctx context.Context, in *CreateTensorboardRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, JobService_CreateTensorboard_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jobServiceClient) GetTensorboard(ctx context.Context, in *GetTensorboardRequest, opts ...grpc.CallOption) (*GetTensorboardResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetTensorboardResponse)
	err := c.cc.Invoke(ctx, JobService_GetTensorboard_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jobServiceClient) GetJobTasks(ctx context.Context, in *GetJobTasksRequest, opts ...grpc.CallOption) (*ListJobTaskStatus, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListJobTaskStatus)
	err := c.cc.Invoke(ctx, JobService_GetJobTasks_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jobServiceClient) GetJobVolumes(ctx context.Context, in *GetJobVolumeRequest, opts ...grpc.CallOption) (*ListJobVolumes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListJobVolumes)
	err := c.cc.Invoke(ctx, JobService_GetJobVolumes_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jobServiceClient) StopJobTask(ctx context.Context, in *StopJobTaskRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, JobService_StopJobTask_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jobServiceClient) RedeployJob(ctx context.Context, in *RedeployJobRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, JobService_RedeployJob_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jobServiceClient) CheckJobIsExist(ctx context.Context, in *CheckJobIsExistRequest, opts ...grpc.CallOption) (*CheckJobIsExistResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CheckJobIsExistResponse)
	err := c.cc.Invoke(ctx, JobService_CheckJobIsExist_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jobServiceClient) UpdateMembers(ctx context.Context, in *UpdateMembersRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, JobService_UpdateMembers_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// JobServiceServer is the server API for JobService service.
// All implementations must embed UnimplementedJobServiceServer
// for forward compatibility
type JobServiceServer interface {
	// CreateJobTemplate 创建任务模板
	CreateJobTemplate(context.Context, *CreateOrUpdateJobTemplateRequest) (*emptypb.Empty, error)
	// UpdateJobTemplate 更新任务模板
	UpdateJobTemplate(context.Context, *CreateOrUpdateJobTemplateRequest) (*emptypb.Empty, error)
	// GetJobTemplate 获取任务模板
	GetJobTemplate(context.Context, *GetJobTemplateRequest) (*JobTemplate, error)
	// ListJobTemplates 列出任务模板
	ListJobTemplateViews(context.Context, *ListJobTemplateViewsOptions) (*ListJobTemplateViewsResponse, error)
	// DeleteJobTemplate 删除任务模板
	DeleteJobTemplate(context.Context, *DeleteJobTemplateRequest) (*emptypb.Empty, error)
	// CreateJobTemplateTrigger 创建任务模板触发器
	CreateJobTemplateTrigger(context.Context, *CreateOrUpdateJobTemplateTriggerRequest) (*emptypb.Empty, error)
	// UpdateJobTemplateTrigger 更新任务模板触发器
	UpdateJobTemplateTrigger(context.Context, *CreateOrUpdateJobTemplateTriggerRequest) (*emptypb.Empty, error)
	// DeleteJobTemplateTrigger 删除任务模板触发器
	DeleteJobTemplateTrigger(context.Context, *DeleteJobTemplateTriggerRequest) (*emptypb.Empty, error)
	// GetJobTemplateTrigger 获取任务模板触发器
	GetJobTemplateTrigger(context.Context, *GetJobTemplateTriggerRequest) (*Trigger, error)
	// ListJobTemplateTriggers 获取任务模板触发器
	ListJobTemplateTriggers(context.Context, *ListJobTemplateTriggersRequest) (*ListJobTemplateTriggersResponse, error)
	// PauseJobTemplateTrigger 暂停任务模板触发器
	PauseJobTemplateTrigger(context.Context, *PauseJobTemplateTriggerRequest) (*emptypb.Empty, error)
	// RecoverJobTemplateTrigger 恢复任务模板触发器
	RecoverJobTemplateTrigger(context.Context, *RecoverJobTemplateTriggerRequest) (*emptypb.Empty, error)
	// 提交训练任务
	SubmitJob(context.Context, *CreateJobRequest) (*CreateJobResponse, error)
	// 停止训练任务
	StopJob(context.Context, *StopJobRequest) (*emptypb.Empty, error)
	UpdateJob(context.Context, *UpdateJobRequest) (*emptypb.Empty, error)
	// GetJobBase 获取训练任务基本信息，不带任务状态用于复制
	GetJobBase(context.Context, *GetJobBaseRequest) (*Job, error)
	// GetJobDetail 获取训练任务详情，带有任务状态用于任务详情展示
	GetJobDetail(context.Context, *GetJobRequest) (*Job, error)
	DeleteJob(context.Context, *DeleteJobRequest) (*emptypb.Empty, error)
	ListJobs(context.Context, *ListJobsOptions) (*ListJobsResponse, error)
	CreateTensorboard(context.Context, *CreateTensorboardRequest) (*emptypb.Empty, error)
	GetTensorboard(context.Context, *GetTensorboardRequest) (*GetTensorboardResponse, error)
	// 获取运行实例
	GetJobTasks(context.Context, *GetJobTasksRequest) (*ListJobTaskStatus, error)
	GetJobVolumes(context.Context, *GetJobVolumeRequest) (*ListJobVolumes, error)
	// 强制停止任务实例
	StopJobTask(context.Context, *StopJobTaskRequest) (*emptypb.Empty, error)
	RedeployJob(context.Context, *RedeployJobRequest) (*emptypb.Empty, error)
	// CheckJobIsExist 检查任务是否存在
	CheckJobIsExist(context.Context, *CheckJobIsExistRequest) (*CheckJobIsExistResponse, error)
	// UpdateMembers 更新成员
	UpdateMembers(context.Context, *UpdateMembersRequest) (*emptypb.Empty, error)
	mustEmbedUnimplementedJobServiceServer()
}

// UnimplementedJobServiceServer must be embedded to have forward compatible implementations.
type UnimplementedJobServiceServer struct {
}

func (UnimplementedJobServiceServer) CreateJobTemplate(context.Context, *CreateOrUpdateJobTemplateRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateJobTemplate not implemented")
}
func (UnimplementedJobServiceServer) UpdateJobTemplate(context.Context, *CreateOrUpdateJobTemplateRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateJobTemplate not implemented")
}
func (UnimplementedJobServiceServer) GetJobTemplate(context.Context, *GetJobTemplateRequest) (*JobTemplate, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetJobTemplate not implemented")
}
func (UnimplementedJobServiceServer) ListJobTemplateViews(context.Context, *ListJobTemplateViewsOptions) (*ListJobTemplateViewsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListJobTemplateViews not implemented")
}
func (UnimplementedJobServiceServer) DeleteJobTemplate(context.Context, *DeleteJobTemplateRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteJobTemplate not implemented")
}
func (UnimplementedJobServiceServer) CreateJobTemplateTrigger(context.Context, *CreateOrUpdateJobTemplateTriggerRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateJobTemplateTrigger not implemented")
}
func (UnimplementedJobServiceServer) UpdateJobTemplateTrigger(context.Context, *CreateOrUpdateJobTemplateTriggerRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateJobTemplateTrigger not implemented")
}
func (UnimplementedJobServiceServer) DeleteJobTemplateTrigger(context.Context, *DeleteJobTemplateTriggerRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteJobTemplateTrigger not implemented")
}
func (UnimplementedJobServiceServer) GetJobTemplateTrigger(context.Context, *GetJobTemplateTriggerRequest) (*Trigger, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetJobTemplateTrigger not implemented")
}
func (UnimplementedJobServiceServer) ListJobTemplateTriggers(context.Context, *ListJobTemplateTriggersRequest) (*ListJobTemplateTriggersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListJobTemplateTriggers not implemented")
}
func (UnimplementedJobServiceServer) PauseJobTemplateTrigger(context.Context, *PauseJobTemplateTriggerRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PauseJobTemplateTrigger not implemented")
}
func (UnimplementedJobServiceServer) RecoverJobTemplateTrigger(context.Context, *RecoverJobTemplateTriggerRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RecoverJobTemplateTrigger not implemented")
}
func (UnimplementedJobServiceServer) SubmitJob(context.Context, *CreateJobRequest) (*CreateJobResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SubmitJob not implemented")
}
func (UnimplementedJobServiceServer) StopJob(context.Context, *StopJobRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StopJob not implemented")
}
func (UnimplementedJobServiceServer) UpdateJob(context.Context, *UpdateJobRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateJob not implemented")
}
func (UnimplementedJobServiceServer) GetJobBase(context.Context, *GetJobBaseRequest) (*Job, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetJobBase not implemented")
}
func (UnimplementedJobServiceServer) GetJobDetail(context.Context, *GetJobRequest) (*Job, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetJobDetail not implemented")
}
func (UnimplementedJobServiceServer) DeleteJob(context.Context, *DeleteJobRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteJob not implemented")
}
func (UnimplementedJobServiceServer) ListJobs(context.Context, *ListJobsOptions) (*ListJobsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListJobs not implemented")
}
func (UnimplementedJobServiceServer) CreateTensorboard(context.Context, *CreateTensorboardRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateTensorboard not implemented")
}
func (UnimplementedJobServiceServer) GetTensorboard(context.Context, *GetTensorboardRequest) (*GetTensorboardResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTensorboard not implemented")
}
func (UnimplementedJobServiceServer) GetJobTasks(context.Context, *GetJobTasksRequest) (*ListJobTaskStatus, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetJobTasks not implemented")
}
func (UnimplementedJobServiceServer) GetJobVolumes(context.Context, *GetJobVolumeRequest) (*ListJobVolumes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetJobVolumes not implemented")
}
func (UnimplementedJobServiceServer) StopJobTask(context.Context, *StopJobTaskRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StopJobTask not implemented")
}
func (UnimplementedJobServiceServer) RedeployJob(context.Context, *RedeployJobRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RedeployJob not implemented")
}
func (UnimplementedJobServiceServer) CheckJobIsExist(context.Context, *CheckJobIsExistRequest) (*CheckJobIsExistResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckJobIsExist not implemented")
}
func (UnimplementedJobServiceServer) UpdateMembers(context.Context, *UpdateMembersRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateMembers not implemented")
}
func (UnimplementedJobServiceServer) mustEmbedUnimplementedJobServiceServer() {}

// UnsafeJobServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to JobServiceServer will
// result in compilation errors.
type UnsafeJobServiceServer interface {
	mustEmbedUnimplementedJobServiceServer()
}

func RegisterJobServiceServer(s grpc.ServiceRegistrar, srv JobServiceServer) {
	s.RegisterService(&JobService_ServiceDesc, srv)
}

func _JobService_CreateJobTemplate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateOrUpdateJobTemplateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobServiceServer).CreateJobTemplate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: JobService_CreateJobTemplate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobServiceServer).CreateJobTemplate(ctx, req.(*CreateOrUpdateJobTemplateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JobService_UpdateJobTemplate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateOrUpdateJobTemplateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobServiceServer).UpdateJobTemplate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: JobService_UpdateJobTemplate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobServiceServer).UpdateJobTemplate(ctx, req.(*CreateOrUpdateJobTemplateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JobService_GetJobTemplate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetJobTemplateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobServiceServer).GetJobTemplate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: JobService_GetJobTemplate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobServiceServer).GetJobTemplate(ctx, req.(*GetJobTemplateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JobService_ListJobTemplateViews_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListJobTemplateViewsOptions)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobServiceServer).ListJobTemplateViews(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: JobService_ListJobTemplateViews_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobServiceServer).ListJobTemplateViews(ctx, req.(*ListJobTemplateViewsOptions))
	}
	return interceptor(ctx, in, info, handler)
}

func _JobService_DeleteJobTemplate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteJobTemplateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobServiceServer).DeleteJobTemplate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: JobService_DeleteJobTemplate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobServiceServer).DeleteJobTemplate(ctx, req.(*DeleteJobTemplateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JobService_CreateJobTemplateTrigger_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateOrUpdateJobTemplateTriggerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobServiceServer).CreateJobTemplateTrigger(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: JobService_CreateJobTemplateTrigger_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobServiceServer).CreateJobTemplateTrigger(ctx, req.(*CreateOrUpdateJobTemplateTriggerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JobService_UpdateJobTemplateTrigger_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateOrUpdateJobTemplateTriggerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobServiceServer).UpdateJobTemplateTrigger(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: JobService_UpdateJobTemplateTrigger_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobServiceServer).UpdateJobTemplateTrigger(ctx, req.(*CreateOrUpdateJobTemplateTriggerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JobService_DeleteJobTemplateTrigger_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteJobTemplateTriggerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobServiceServer).DeleteJobTemplateTrigger(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: JobService_DeleteJobTemplateTrigger_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobServiceServer).DeleteJobTemplateTrigger(ctx, req.(*DeleteJobTemplateTriggerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JobService_GetJobTemplateTrigger_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetJobTemplateTriggerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobServiceServer).GetJobTemplateTrigger(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: JobService_GetJobTemplateTrigger_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobServiceServer).GetJobTemplateTrigger(ctx, req.(*GetJobTemplateTriggerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JobService_ListJobTemplateTriggers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListJobTemplateTriggersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobServiceServer).ListJobTemplateTriggers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: JobService_ListJobTemplateTriggers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobServiceServer).ListJobTemplateTriggers(ctx, req.(*ListJobTemplateTriggersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JobService_PauseJobTemplateTrigger_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PauseJobTemplateTriggerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobServiceServer).PauseJobTemplateTrigger(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: JobService_PauseJobTemplateTrigger_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobServiceServer).PauseJobTemplateTrigger(ctx, req.(*PauseJobTemplateTriggerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JobService_RecoverJobTemplateTrigger_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecoverJobTemplateTriggerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobServiceServer).RecoverJobTemplateTrigger(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: JobService_RecoverJobTemplateTrigger_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobServiceServer).RecoverJobTemplateTrigger(ctx, req.(*RecoverJobTemplateTriggerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JobService_SubmitJob_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateJobRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobServiceServer).SubmitJob(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: JobService_SubmitJob_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobServiceServer).SubmitJob(ctx, req.(*CreateJobRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JobService_StopJob_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StopJobRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobServiceServer).StopJob(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: JobService_StopJob_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobServiceServer).StopJob(ctx, req.(*StopJobRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JobService_UpdateJob_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateJobRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobServiceServer).UpdateJob(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: JobService_UpdateJob_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobServiceServer).UpdateJob(ctx, req.(*UpdateJobRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JobService_GetJobBase_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetJobBaseRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobServiceServer).GetJobBase(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: JobService_GetJobBase_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobServiceServer).GetJobBase(ctx, req.(*GetJobBaseRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JobService_GetJobDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetJobRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobServiceServer).GetJobDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: JobService_GetJobDetail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobServiceServer).GetJobDetail(ctx, req.(*GetJobRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JobService_DeleteJob_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteJobRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobServiceServer).DeleteJob(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: JobService_DeleteJob_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobServiceServer).DeleteJob(ctx, req.(*DeleteJobRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JobService_ListJobs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListJobsOptions)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobServiceServer).ListJobs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: JobService_ListJobs_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobServiceServer).ListJobs(ctx, req.(*ListJobsOptions))
	}
	return interceptor(ctx, in, info, handler)
}

func _JobService_CreateTensorboard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateTensorboardRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobServiceServer).CreateTensorboard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: JobService_CreateTensorboard_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobServiceServer).CreateTensorboard(ctx, req.(*CreateTensorboardRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JobService_GetTensorboard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTensorboardRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobServiceServer).GetTensorboard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: JobService_GetTensorboard_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobServiceServer).GetTensorboard(ctx, req.(*GetTensorboardRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JobService_GetJobTasks_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetJobTasksRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobServiceServer).GetJobTasks(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: JobService_GetJobTasks_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobServiceServer).GetJobTasks(ctx, req.(*GetJobTasksRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JobService_GetJobVolumes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetJobVolumeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobServiceServer).GetJobVolumes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: JobService_GetJobVolumes_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobServiceServer).GetJobVolumes(ctx, req.(*GetJobVolumeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JobService_StopJobTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StopJobTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobServiceServer).StopJobTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: JobService_StopJobTask_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobServiceServer).StopJobTask(ctx, req.(*StopJobTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JobService_RedeployJob_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RedeployJobRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobServiceServer).RedeployJob(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: JobService_RedeployJob_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobServiceServer).RedeployJob(ctx, req.(*RedeployJobRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JobService_CheckJobIsExist_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckJobIsExistRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobServiceServer).CheckJobIsExist(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: JobService_CheckJobIsExist_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobServiceServer).CheckJobIsExist(ctx, req.(*CheckJobIsExistRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JobService_UpdateMembers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateMembersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobServiceServer).UpdateMembers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: JobService_UpdateMembers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobServiceServer).UpdateMembers(ctx, req.(*UpdateMembersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// JobService_ServiceDesc is the grpc.ServiceDesc for JobService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var JobService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "apis.aistudio.job.v1.JobService",
	HandlerType: (*JobServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateJobTemplate",
			Handler:    _JobService_CreateJobTemplate_Handler,
		},
		{
			MethodName: "UpdateJobTemplate",
			Handler:    _JobService_UpdateJobTemplate_Handler,
		},
		{
			MethodName: "GetJobTemplate",
			Handler:    _JobService_GetJobTemplate_Handler,
		},
		{
			MethodName: "ListJobTemplateViews",
			Handler:    _JobService_ListJobTemplateViews_Handler,
		},
		{
			MethodName: "DeleteJobTemplate",
			Handler:    _JobService_DeleteJobTemplate_Handler,
		},
		{
			MethodName: "CreateJobTemplateTrigger",
			Handler:    _JobService_CreateJobTemplateTrigger_Handler,
		},
		{
			MethodName: "UpdateJobTemplateTrigger",
			Handler:    _JobService_UpdateJobTemplateTrigger_Handler,
		},
		{
			MethodName: "DeleteJobTemplateTrigger",
			Handler:    _JobService_DeleteJobTemplateTrigger_Handler,
		},
		{
			MethodName: "GetJobTemplateTrigger",
			Handler:    _JobService_GetJobTemplateTrigger_Handler,
		},
		{
			MethodName: "ListJobTemplateTriggers",
			Handler:    _JobService_ListJobTemplateTriggers_Handler,
		},
		{
			MethodName: "PauseJobTemplateTrigger",
			Handler:    _JobService_PauseJobTemplateTrigger_Handler,
		},
		{
			MethodName: "RecoverJobTemplateTrigger",
			Handler:    _JobService_RecoverJobTemplateTrigger_Handler,
		},
		{
			MethodName: "SubmitJob",
			Handler:    _JobService_SubmitJob_Handler,
		},
		{
			MethodName: "StopJob",
			Handler:    _JobService_StopJob_Handler,
		},
		{
			MethodName: "UpdateJob",
			Handler:    _JobService_UpdateJob_Handler,
		},
		{
			MethodName: "GetJobBase",
			Handler:    _JobService_GetJobBase_Handler,
		},
		{
			MethodName: "GetJobDetail",
			Handler:    _JobService_GetJobDetail_Handler,
		},
		{
			MethodName: "DeleteJob",
			Handler:    _JobService_DeleteJob_Handler,
		},
		{
			MethodName: "ListJobs",
			Handler:    _JobService_ListJobs_Handler,
		},
		{
			MethodName: "CreateTensorboard",
			Handler:    _JobService_CreateTensorboard_Handler,
		},
		{
			MethodName: "GetTensorboard",
			Handler:    _JobService_GetTensorboard_Handler,
		},
		{
			MethodName: "GetJobTasks",
			Handler:    _JobService_GetJobTasks_Handler,
		},
		{
			MethodName: "GetJobVolumes",
			Handler:    _JobService_GetJobVolumes_Handler,
		},
		{
			MethodName: "StopJobTask",
			Handler:    _JobService_StopJobTask_Handler,
		},
		{
			MethodName: "RedeployJob",
			Handler:    _JobService_RedeployJob_Handler,
		},
		{
			MethodName: "CheckJobIsExist",
			Handler:    _JobService_CheckJobIsExist_Handler,
		},
		{
			MethodName: "UpdateMembers",
			Handler:    _JobService_UpdateMembers_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "aistudio/job/v1/job.proto",
}
