// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: aistudio/job/v1/job.proto

package v1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on CheckJobIsExistRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CheckJobIsExistRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckJobIsExistRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CheckJobIsExistRequestMultiError, or nil if none found.
func (m *CheckJobIsExistRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckJobIsExistRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for WorkspaceName

	// no validation rules for JobName

	if len(errors) > 0 {
		return CheckJobIsExistRequestMultiError(errors)
	}

	return nil
}

// CheckJobIsExistRequestMultiError is an error wrapping multiple validation
// errors returned by CheckJobIsExistRequest.ValidateAll() if the designated
// constraints aren't met.
type CheckJobIsExistRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckJobIsExistRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckJobIsExistRequestMultiError) AllErrors() []error { return m }

// CheckJobIsExistRequestValidationError is the validation error returned by
// CheckJobIsExistRequest.Validate if the designated constraints aren't met.
type CheckJobIsExistRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckJobIsExistRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckJobIsExistRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckJobIsExistRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckJobIsExistRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckJobIsExistRequestValidationError) ErrorName() string {
	return "CheckJobIsExistRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CheckJobIsExistRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckJobIsExistRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckJobIsExistRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckJobIsExistRequestValidationError{}

// Validate checks the field values on CheckJobIsExistResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CheckJobIsExistResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckJobIsExistResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CheckJobIsExistResponseMultiError, or nil if none found.
func (m *CheckJobIsExistResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckJobIsExistResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Status

	// no validation rules for Errors

	if len(errors) > 0 {
		return CheckJobIsExistResponseMultiError(errors)
	}

	return nil
}

// CheckJobIsExistResponseMultiError is an error wrapping multiple validation
// errors returned by CheckJobIsExistResponse.ValidateAll() if the designated
// constraints aren't met.
type CheckJobIsExistResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckJobIsExistResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckJobIsExistResponseMultiError) AllErrors() []error { return m }

// CheckJobIsExistResponseValidationError is the validation error returned by
// CheckJobIsExistResponse.Validate if the designated constraints aren't met.
type CheckJobIsExistResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckJobIsExistResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckJobIsExistResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckJobIsExistResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckJobIsExistResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckJobIsExistResponseValidationError) ErrorName() string {
	return "CheckJobIsExistResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CheckJobIsExistResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckJobIsExistResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckJobIsExistResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckJobIsExistResponseValidationError{}

// Validate checks the field values on UpdateMembersRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateMembersRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateMembersRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateMembersRequestMultiError, or nil if none found.
func (m *UpdateMembersRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateMembersRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for WorkspaceName

	// no validation rules for JobName

	if len(errors) > 0 {
		return UpdateMembersRequestMultiError(errors)
	}

	return nil
}

// UpdateMembersRequestMultiError is an error wrapping multiple validation
// errors returned by UpdateMembersRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateMembersRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateMembersRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateMembersRequestMultiError) AllErrors() []error { return m }

// UpdateMembersRequestValidationError is the validation error returned by
// UpdateMembersRequest.Validate if the designated constraints aren't met.
type UpdateMembersRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateMembersRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateMembersRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateMembersRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateMembersRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateMembersRequestValidationError) ErrorName() string {
	return "UpdateMembersRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateMembersRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateMembersRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateMembersRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateMembersRequestValidationError{}

// Validate checks the field values on JobTemplate with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *JobTemplate) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on JobTemplate with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in JobTemplateMultiError, or
// nil if none found.
func (m *JobTemplate) ValidateAll() error {
	return m.validate(true)
}

func (m *JobTemplate) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for DisplayName

	if utf8.RuneCountInString(m.GetDescription()) > 100 {
		err := JobTemplateValidationError{
			field:  "Description",
			reason: "value length must be at most 100 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if l := utf8.RuneCountInString(m.GetWorkspaceName()); l < 1 || l > 31 {
		err := JobTemplateValidationError{
			field:  "WorkspaceName",
			reason: "value length must be between 1 and 31 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_JobTemplate_WorkspaceName_Pattern.MatchString(m.GetWorkspaceName()) {
		err := JobTemplateValidationError{
			field:  "WorkspaceName",
			reason: "value does not match regex pattern \"^[a-z]([-a-z0-9]*[a-z0-9])?$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Region

	// no validation rules for Labels

	for idx, item := range m.GetTriggers() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, JobTemplateValidationError{
						field:  fmt.Sprintf("Triggers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, JobTemplateValidationError{
						field:  fmt.Sprintf("Triggers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return JobTemplateValidationError{
					field:  fmt.Sprintf("Triggers[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for ExecutionStrategy

	if all {
		switch v := interface{}(m.GetJob()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, JobTemplateValidationError{
					field:  "Job",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, JobTemplateValidationError{
					field:  "Job",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetJob()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return JobTemplateValidationError{
				field:  "Job",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Creator

	// no validation rules for CreateTime

	// no validation rules for UpdateTime

	if len(errors) > 0 {
		return JobTemplateMultiError(errors)
	}

	return nil
}

// JobTemplateMultiError is an error wrapping multiple validation errors
// returned by JobTemplate.ValidateAll() if the designated constraints aren't met.
type JobTemplateMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m JobTemplateMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m JobTemplateMultiError) AllErrors() []error { return m }

// JobTemplateValidationError is the validation error returned by
// JobTemplate.Validate if the designated constraints aren't met.
type JobTemplateValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e JobTemplateValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e JobTemplateValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e JobTemplateValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e JobTemplateValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e JobTemplateValidationError) ErrorName() string { return "JobTemplateValidationError" }

// Error satisfies the builtin error interface
func (e JobTemplateValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sJobTemplate.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = JobTemplateValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = JobTemplateValidationError{}

var _JobTemplate_WorkspaceName_Pattern = regexp.MustCompile("^[a-z]([-a-z0-9]*[a-z0-9])?$")

// Validate checks the field values on JobTemplateView with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *JobTemplateView) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on JobTemplateView with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// JobTemplateViewMultiError, or nil if none found.
func (m *JobTemplateView) ValidateAll() error {
	return m.validate(true)
}

func (m *JobTemplateView) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetJobTemplate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, JobTemplateViewValidationError{
					field:  "JobTemplate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, JobTemplateViewValidationError{
					field:  "JobTemplate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetJobTemplate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return JobTemplateViewValidationError{
				field:  "JobTemplate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for JobTemplateStatus

	// no validation rules for NumActions

	if len(errors) > 0 {
		return JobTemplateViewMultiError(errors)
	}

	return nil
}

// JobTemplateViewMultiError is an error wrapping multiple validation errors
// returned by JobTemplateView.ValidateAll() if the designated constraints
// aren't met.
type JobTemplateViewMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m JobTemplateViewMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m JobTemplateViewMultiError) AllErrors() []error { return m }

// JobTemplateViewValidationError is the validation error returned by
// JobTemplateView.Validate if the designated constraints aren't met.
type JobTemplateViewValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e JobTemplateViewValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e JobTemplateViewValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e JobTemplateViewValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e JobTemplateViewValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e JobTemplateViewValidationError) ErrorName() string { return "JobTemplateViewValidationError" }

// Error satisfies the builtin error interface
func (e JobTemplateViewValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sJobTemplateView.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = JobTemplateViewValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = JobTemplateViewValidationError{}

// Validate checks the field values on Trigger with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Trigger) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Trigger with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in TriggerMultiError, or nil if none found.
func (m *Trigger) ValidateAll() error {
	return m.validate(true)
}

func (m *Trigger) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TriggerType

	// no validation rules for TriggerName

	if all {
		switch v := interface{}(m.GetTimeTriggerOptions()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TriggerValidationError{
					field:  "TimeTriggerOptions",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TriggerValidationError{
					field:  "TimeTriggerOptions",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTimeTriggerOptions()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TriggerValidationError{
				field:  "TimeTriggerOptions",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for NumActions

	// no validation rules for State

	// no validation rules for Message

	if len(errors) > 0 {
		return TriggerMultiError(errors)
	}

	return nil
}

// TriggerMultiError is an error wrapping multiple validation errors returned
// by Trigger.ValidateAll() if the designated constraints aren't met.
type TriggerMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TriggerMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TriggerMultiError) AllErrors() []error { return m }

// TriggerValidationError is the validation error returned by Trigger.Validate
// if the designated constraints aren't met.
type TriggerValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TriggerValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TriggerValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TriggerValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TriggerValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TriggerValidationError) ErrorName() string { return "TriggerValidationError" }

// Error satisfies the builtin error interface
func (e TriggerValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTrigger.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TriggerValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TriggerValidationError{}

// Validate checks the field values on TimerTriggerOptions with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *TimerTriggerOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TimerTriggerOptions with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// TimerTriggerOptionsMultiError, or nil if none found.
func (m *TimerTriggerOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *TimerTriggerOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Type

	// no validation rules for Interval

	// no validation rules for CronExpr

	if all {
		switch v := interface{}(m.GetCycle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TimerTriggerOptionsValidationError{
					field:  "Cycle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TimerTriggerOptionsValidationError{
					field:  "Cycle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCycle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TimerTriggerOptionsValidationError{
				field:  "Cycle",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PeriodOfValidity

	// no validation rules for MaxCount

	if all {
		switch v := interface{}(m.GetSkipTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TimerTriggerOptionsValidationError{
					field:  "SkipTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TimerTriggerOptionsValidationError{
					field:  "SkipTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSkipTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TimerTriggerOptionsValidationError{
				field:  "SkipTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return TimerTriggerOptionsMultiError(errors)
	}

	return nil
}

// TimerTriggerOptionsMultiError is an error wrapping multiple validation
// errors returned by TimerTriggerOptions.ValidateAll() if the designated
// constraints aren't met.
type TimerTriggerOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TimerTriggerOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TimerTriggerOptionsMultiError) AllErrors() []error { return m }

// TimerTriggerOptionsValidationError is the validation error returned by
// TimerTriggerOptions.Validate if the designated constraints aren't met.
type TimerTriggerOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TimerTriggerOptionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TimerTriggerOptionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TimerTriggerOptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TimerTriggerOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TimerTriggerOptionsValidationError) ErrorName() string {
	return "TimerTriggerOptionsValidationError"
}

// Error satisfies the builtin error interface
func (e TimerTriggerOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTimerTriggerOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TimerTriggerOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TimerTriggerOptionsValidationError{}

// Validate checks the field values on Cycle with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Cycle) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Cycle with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in CycleMultiError, or nil if none found.
func (m *Cycle) ValidateAll() error {
	return m.validate(true)
}

func (m *Cycle) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return CycleMultiError(errors)
	}

	return nil
}

// CycleMultiError is an error wrapping multiple validation errors returned by
// Cycle.ValidateAll() if the designated constraints aren't met.
type CycleMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CycleMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CycleMultiError) AllErrors() []error { return m }

// CycleValidationError is the validation error returned by Cycle.Validate if
// the designated constraints aren't met.
type CycleValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CycleValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CycleValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CycleValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CycleValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CycleValidationError) ErrorName() string { return "CycleValidationError" }

// Error satisfies the builtin error interface
func (e CycleValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCycle.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CycleValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CycleValidationError{}

// Validate checks the field values on JobTriggerRecord with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *JobTriggerRecord) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on JobTriggerRecord with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// JobTriggerRecordMultiError, or nil if none found.
func (m *JobTriggerRecord) ValidateAll() error {
	return m.validate(true)
}

func (m *JobTriggerRecord) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for JobName

	// no validation rules for JobTemplateName

	// no validation rules for TriggerName

	// no validation rules for TriggerTime

	// no validation rules for Creator

	if len(errors) > 0 {
		return JobTriggerRecordMultiError(errors)
	}

	return nil
}

// JobTriggerRecordMultiError is an error wrapping multiple validation errors
// returned by JobTriggerRecord.ValidateAll() if the designated constraints
// aren't met.
type JobTriggerRecordMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m JobTriggerRecordMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m JobTriggerRecordMultiError) AllErrors() []error { return m }

// JobTriggerRecordValidationError is the validation error returned by
// JobTriggerRecord.Validate if the designated constraints aren't met.
type JobTriggerRecordValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e JobTriggerRecordValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e JobTriggerRecordValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e JobTriggerRecordValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e JobTriggerRecordValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e JobTriggerRecordValidationError) ErrorName() string { return "JobTriggerRecordValidationError" }

// Error satisfies the builtin error interface
func (e JobTriggerRecordValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sJobTriggerRecord.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = JobTriggerRecordValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = JobTriggerRecordValidationError{}

// Validate checks the field values on PauseJobTemplateTriggerRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PauseJobTemplateTriggerRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PauseJobTemplateTriggerRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// PauseJobTemplateTriggerRequestMultiError, or nil if none found.
func (m *PauseJobTemplateTriggerRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *PauseJobTemplateTriggerRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for WorkspaceName

	// no validation rules for JobTemplateName

	// no validation rules for TriggerName

	// no validation rules for Message

	if len(errors) > 0 {
		return PauseJobTemplateTriggerRequestMultiError(errors)
	}

	return nil
}

// PauseJobTemplateTriggerRequestMultiError is an error wrapping multiple
// validation errors returned by PauseJobTemplateTriggerRequest.ValidateAll()
// if the designated constraints aren't met.
type PauseJobTemplateTriggerRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PauseJobTemplateTriggerRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PauseJobTemplateTriggerRequestMultiError) AllErrors() []error { return m }

// PauseJobTemplateTriggerRequestValidationError is the validation error
// returned by PauseJobTemplateTriggerRequest.Validate if the designated
// constraints aren't met.
type PauseJobTemplateTriggerRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PauseJobTemplateTriggerRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PauseJobTemplateTriggerRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PauseJobTemplateTriggerRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PauseJobTemplateTriggerRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PauseJobTemplateTriggerRequestValidationError) ErrorName() string {
	return "PauseJobTemplateTriggerRequestValidationError"
}

// Error satisfies the builtin error interface
func (e PauseJobTemplateTriggerRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPauseJobTemplateTriggerRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PauseJobTemplateTriggerRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PauseJobTemplateTriggerRequestValidationError{}

// Validate checks the field values on RecoverJobTemplateTriggerRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *RecoverJobTemplateTriggerRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RecoverJobTemplateTriggerRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// RecoverJobTemplateTriggerRequestMultiError, or nil if none found.
func (m *RecoverJobTemplateTriggerRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *RecoverJobTemplateTriggerRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for WorkspaceName

	// no validation rules for JobTemplateName

	// no validation rules for TriggerName

	// no validation rules for Message

	if len(errors) > 0 {
		return RecoverJobTemplateTriggerRequestMultiError(errors)
	}

	return nil
}

// RecoverJobTemplateTriggerRequestMultiError is an error wrapping multiple
// validation errors returned by
// RecoverJobTemplateTriggerRequest.ValidateAll() if the designated
// constraints aren't met.
type RecoverJobTemplateTriggerRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RecoverJobTemplateTriggerRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RecoverJobTemplateTriggerRequestMultiError) AllErrors() []error { return m }

// RecoverJobTemplateTriggerRequestValidationError is the validation error
// returned by RecoverJobTemplateTriggerRequest.Validate if the designated
// constraints aren't met.
type RecoverJobTemplateTriggerRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RecoverJobTemplateTriggerRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RecoverJobTemplateTriggerRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RecoverJobTemplateTriggerRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RecoverJobTemplateTriggerRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RecoverJobTemplateTriggerRequestValidationError) ErrorName() string {
	return "RecoverJobTemplateTriggerRequestValidationError"
}

// Error satisfies the builtin error interface
func (e RecoverJobTemplateTriggerRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRecoverJobTemplateTriggerRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RecoverJobTemplateTriggerRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RecoverJobTemplateTriggerRequestValidationError{}

// Validate checks the field values on CreateOrUpdateJobTemplateRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *CreateOrUpdateJobTemplateRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateOrUpdateJobTemplateRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CreateOrUpdateJobTemplateRequestMultiError, or nil if none found.
func (m *CreateOrUpdateJobTemplateRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateOrUpdateJobTemplateRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for DisplayName

	if utf8.RuneCountInString(m.GetDescription()) > 100 {
		err := CreateOrUpdateJobTemplateRequestValidationError{
			field:  "Description",
			reason: "value length must be at most 100 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if l := utf8.RuneCountInString(m.GetWorkspaceName()); l < 1 || l > 31 {
		err := CreateOrUpdateJobTemplateRequestValidationError{
			field:  "WorkspaceName",
			reason: "value length must be between 1 and 31 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_CreateOrUpdateJobTemplateRequest_WorkspaceName_Pattern.MatchString(m.GetWorkspaceName()) {
		err := CreateOrUpdateJobTemplateRequestValidationError{
			field:  "WorkspaceName",
			reason: "value does not match regex pattern \"^[a-z]([-a-z0-9]*[a-z0-9])?$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Region

	// no validation rules for Labels

	if all {
		switch v := interface{}(m.GetJob()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateOrUpdateJobTemplateRequestValidationError{
					field:  "Job",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateOrUpdateJobTemplateRequestValidationError{
					field:  "Job",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetJob()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateOrUpdateJobTemplateRequestValidationError{
				field:  "Job",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetTriggers() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CreateOrUpdateJobTemplateRequestValidationError{
						field:  fmt.Sprintf("Triggers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CreateOrUpdateJobTemplateRequestValidationError{
						field:  fmt.Sprintf("Triggers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CreateOrUpdateJobTemplateRequestValidationError{
					field:  fmt.Sprintf("Triggers[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for ExecutionStrategy

	if len(errors) > 0 {
		return CreateOrUpdateJobTemplateRequestMultiError(errors)
	}

	return nil
}

// CreateOrUpdateJobTemplateRequestMultiError is an error wrapping multiple
// validation errors returned by
// CreateOrUpdateJobTemplateRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateOrUpdateJobTemplateRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateOrUpdateJobTemplateRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateOrUpdateJobTemplateRequestMultiError) AllErrors() []error { return m }

// CreateOrUpdateJobTemplateRequestValidationError is the validation error
// returned by CreateOrUpdateJobTemplateRequest.Validate if the designated
// constraints aren't met.
type CreateOrUpdateJobTemplateRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateOrUpdateJobTemplateRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateOrUpdateJobTemplateRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateOrUpdateJobTemplateRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateOrUpdateJobTemplateRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateOrUpdateJobTemplateRequestValidationError) ErrorName() string {
	return "CreateOrUpdateJobTemplateRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateOrUpdateJobTemplateRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateOrUpdateJobTemplateRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateOrUpdateJobTemplateRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateOrUpdateJobTemplateRequestValidationError{}

var _CreateOrUpdateJobTemplateRequest_WorkspaceName_Pattern = regexp.MustCompile("^[a-z]([-a-z0-9]*[a-z0-9])?$")

// Validate checks the field values on GetJobTemplateRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetJobTemplateRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetJobTemplateRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetJobTemplateRequestMultiError, or nil if none found.
func (m *GetJobTemplateRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetJobTemplateRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for WorkspaceName

	// no validation rules for JobTemplateName

	if len(errors) > 0 {
		return GetJobTemplateRequestMultiError(errors)
	}

	return nil
}

// GetJobTemplateRequestMultiError is an error wrapping multiple validation
// errors returned by GetJobTemplateRequest.ValidateAll() if the designated
// constraints aren't met.
type GetJobTemplateRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetJobTemplateRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetJobTemplateRequestMultiError) AllErrors() []error { return m }

// GetJobTemplateRequestValidationError is the validation error returned by
// GetJobTemplateRequest.Validate if the designated constraints aren't met.
type GetJobTemplateRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetJobTemplateRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetJobTemplateRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetJobTemplateRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetJobTemplateRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetJobTemplateRequestValidationError) ErrorName() string {
	return "GetJobTemplateRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetJobTemplateRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetJobTemplateRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetJobTemplateRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetJobTemplateRequestValidationError{}

// Validate checks the field values on ListJobTemplateViewsOptions with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListJobTemplateViewsOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListJobTemplateViewsOptions with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListJobTemplateViewsOptionsMultiError, or nil if none found.
func (m *ListJobTemplateViewsOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *ListJobTemplateViewsOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for WorkspaceName

	// no validation rules for JobTemplateName

	// no validation rules for Region

	// no validation rules for Manager

	// no validation rules for Member

	// no validation rules for Page

	// no validation rules for PageSize

	if len(errors) > 0 {
		return ListJobTemplateViewsOptionsMultiError(errors)
	}

	return nil
}

// ListJobTemplateViewsOptionsMultiError is an error wrapping multiple
// validation errors returned by ListJobTemplateViewsOptions.ValidateAll() if
// the designated constraints aren't met.
type ListJobTemplateViewsOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListJobTemplateViewsOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListJobTemplateViewsOptionsMultiError) AllErrors() []error { return m }

// ListJobTemplateViewsOptionsValidationError is the validation error returned
// by ListJobTemplateViewsOptions.Validate if the designated constraints
// aren't met.
type ListJobTemplateViewsOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListJobTemplateViewsOptionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListJobTemplateViewsOptionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListJobTemplateViewsOptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListJobTemplateViewsOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListJobTemplateViewsOptionsValidationError) ErrorName() string {
	return "ListJobTemplateViewsOptionsValidationError"
}

// Error satisfies the builtin error interface
func (e ListJobTemplateViewsOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListJobTemplateViewsOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListJobTemplateViewsOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListJobTemplateViewsOptionsValidationError{}

// Validate checks the field values on ListJobTemplateViewsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListJobTemplateViewsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListJobTemplateViewsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListJobTemplateViewsResponseMultiError, or nil if none found.
func (m *ListJobTemplateViewsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListJobTemplateViewsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetJobTemplateViews() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListJobTemplateViewsResponseValidationError{
						field:  fmt.Sprintf("JobTemplateViews[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListJobTemplateViewsResponseValidationError{
						field:  fmt.Sprintf("JobTemplateViews[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListJobTemplateViewsResponseValidationError{
					field:  fmt.Sprintf("JobTemplateViews[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Total

	if len(errors) > 0 {
		return ListJobTemplateViewsResponseMultiError(errors)
	}

	return nil
}

// ListJobTemplateViewsResponseMultiError is an error wrapping multiple
// validation errors returned by ListJobTemplateViewsResponse.ValidateAll() if
// the designated constraints aren't met.
type ListJobTemplateViewsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListJobTemplateViewsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListJobTemplateViewsResponseMultiError) AllErrors() []error { return m }

// ListJobTemplateViewsResponseValidationError is the validation error returned
// by ListJobTemplateViewsResponse.Validate if the designated constraints
// aren't met.
type ListJobTemplateViewsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListJobTemplateViewsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListJobTemplateViewsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListJobTemplateViewsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListJobTemplateViewsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListJobTemplateViewsResponseValidationError) ErrorName() string {
	return "ListJobTemplateViewsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListJobTemplateViewsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListJobTemplateViewsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListJobTemplateViewsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListJobTemplateViewsResponseValidationError{}

// Validate checks the field values on CreateOrUpdateJobTemplateTriggerRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *CreateOrUpdateJobTemplateTriggerRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// CreateOrUpdateJobTemplateTriggerRequest with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// CreateOrUpdateJobTemplateTriggerRequestMultiError, or nil if none found.
func (m *CreateOrUpdateJobTemplateTriggerRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateOrUpdateJobTemplateTriggerRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for WorkspaceName

	// no validation rules for JobTemplateName

	// no validation rules for TriggerType

	// no validation rules for TriggerName

	if all {
		switch v := interface{}(m.GetTimeTriggerOptions()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateOrUpdateJobTemplateTriggerRequestValidationError{
					field:  "TimeTriggerOptions",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateOrUpdateJobTemplateTriggerRequestValidationError{
					field:  "TimeTriggerOptions",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTimeTriggerOptions()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateOrUpdateJobTemplateTriggerRequestValidationError{
				field:  "TimeTriggerOptions",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateOrUpdateJobTemplateTriggerRequestMultiError(errors)
	}

	return nil
}

// CreateOrUpdateJobTemplateTriggerRequestMultiError is an error wrapping
// multiple validation errors returned by
// CreateOrUpdateJobTemplateTriggerRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateOrUpdateJobTemplateTriggerRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateOrUpdateJobTemplateTriggerRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateOrUpdateJobTemplateTriggerRequestMultiError) AllErrors() []error { return m }

// CreateOrUpdateJobTemplateTriggerRequestValidationError is the validation
// error returned by CreateOrUpdateJobTemplateTriggerRequest.Validate if the
// designated constraints aren't met.
type CreateOrUpdateJobTemplateTriggerRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateOrUpdateJobTemplateTriggerRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateOrUpdateJobTemplateTriggerRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateOrUpdateJobTemplateTriggerRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateOrUpdateJobTemplateTriggerRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateOrUpdateJobTemplateTriggerRequestValidationError) ErrorName() string {
	return "CreateOrUpdateJobTemplateTriggerRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateOrUpdateJobTemplateTriggerRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateOrUpdateJobTemplateTriggerRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateOrUpdateJobTemplateTriggerRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateOrUpdateJobTemplateTriggerRequestValidationError{}

// Validate checks the field values on DeleteJobTemplateTriggerRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteJobTemplateTriggerRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteJobTemplateTriggerRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// DeleteJobTemplateTriggerRequestMultiError, or nil if none found.
func (m *DeleteJobTemplateTriggerRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteJobTemplateTriggerRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for WorkspaceName

	// no validation rules for JobTemplateName

	// no validation rules for TriggerName

	if len(errors) > 0 {
		return DeleteJobTemplateTriggerRequestMultiError(errors)
	}

	return nil
}

// DeleteJobTemplateTriggerRequestMultiError is an error wrapping multiple
// validation errors returned by DeleteJobTemplateTriggerRequest.ValidateAll()
// if the designated constraints aren't met.
type DeleteJobTemplateTriggerRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteJobTemplateTriggerRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteJobTemplateTriggerRequestMultiError) AllErrors() []error { return m }

// DeleteJobTemplateTriggerRequestValidationError is the validation error
// returned by DeleteJobTemplateTriggerRequest.Validate if the designated
// constraints aren't met.
type DeleteJobTemplateTriggerRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteJobTemplateTriggerRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteJobTemplateTriggerRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteJobTemplateTriggerRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteJobTemplateTriggerRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteJobTemplateTriggerRequestValidationError) ErrorName() string {
	return "DeleteJobTemplateTriggerRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteJobTemplateTriggerRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteJobTemplateTriggerRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteJobTemplateTriggerRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteJobTemplateTriggerRequestValidationError{}

// Validate checks the field values on GetJobTemplateTriggerRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetJobTemplateTriggerRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetJobTemplateTriggerRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetJobTemplateTriggerRequestMultiError, or nil if none found.
func (m *GetJobTemplateTriggerRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetJobTemplateTriggerRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for WorkspaceName

	// no validation rules for JobTemplateName

	// no validation rules for TriggerName

	if len(errors) > 0 {
		return GetJobTemplateTriggerRequestMultiError(errors)
	}

	return nil
}

// GetJobTemplateTriggerRequestMultiError is an error wrapping multiple
// validation errors returned by GetJobTemplateTriggerRequest.ValidateAll() if
// the designated constraints aren't met.
type GetJobTemplateTriggerRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetJobTemplateTriggerRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetJobTemplateTriggerRequestMultiError) AllErrors() []error { return m }

// GetJobTemplateTriggerRequestValidationError is the validation error returned
// by GetJobTemplateTriggerRequest.Validate if the designated constraints
// aren't met.
type GetJobTemplateTriggerRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetJobTemplateTriggerRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetJobTemplateTriggerRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetJobTemplateTriggerRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetJobTemplateTriggerRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetJobTemplateTriggerRequestValidationError) ErrorName() string {
	return "GetJobTemplateTriggerRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetJobTemplateTriggerRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetJobTemplateTriggerRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetJobTemplateTriggerRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetJobTemplateTriggerRequestValidationError{}

// Validate checks the field values on ListJobTemplateTriggersRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListJobTemplateTriggersRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListJobTemplateTriggersRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ListJobTemplateTriggersRequestMultiError, or nil if none found.
func (m *ListJobTemplateTriggersRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListJobTemplateTriggersRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for WorkspaceName

	// no validation rules for JobTemplateName

	// no validation rules for TriggerName

	if len(errors) > 0 {
		return ListJobTemplateTriggersRequestMultiError(errors)
	}

	return nil
}

// ListJobTemplateTriggersRequestMultiError is an error wrapping multiple
// validation errors returned by ListJobTemplateTriggersRequest.ValidateAll()
// if the designated constraints aren't met.
type ListJobTemplateTriggersRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListJobTemplateTriggersRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListJobTemplateTriggersRequestMultiError) AllErrors() []error { return m }

// ListJobTemplateTriggersRequestValidationError is the validation error
// returned by ListJobTemplateTriggersRequest.Validate if the designated
// constraints aren't met.
type ListJobTemplateTriggersRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListJobTemplateTriggersRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListJobTemplateTriggersRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListJobTemplateTriggersRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListJobTemplateTriggersRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListJobTemplateTriggersRequestValidationError) ErrorName() string {
	return "ListJobTemplateTriggersRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListJobTemplateTriggersRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListJobTemplateTriggersRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListJobTemplateTriggersRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListJobTemplateTriggersRequestValidationError{}

// Validate checks the field values on ListJobTemplateTriggersResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListJobTemplateTriggersResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListJobTemplateTriggersResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ListJobTemplateTriggersResponseMultiError, or nil if none found.
func (m *ListJobTemplateTriggersResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListJobTemplateTriggersResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetTriggers() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListJobTemplateTriggersResponseValidationError{
						field:  fmt.Sprintf("Triggers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListJobTemplateTriggersResponseValidationError{
						field:  fmt.Sprintf("Triggers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListJobTemplateTriggersResponseValidationError{
					field:  fmt.Sprintf("Triggers[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Total

	if len(errors) > 0 {
		return ListJobTemplateTriggersResponseMultiError(errors)
	}

	return nil
}

// ListJobTemplateTriggersResponseMultiError is an error wrapping multiple
// validation errors returned by ListJobTemplateTriggersResponse.ValidateAll()
// if the designated constraints aren't met.
type ListJobTemplateTriggersResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListJobTemplateTriggersResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListJobTemplateTriggersResponseMultiError) AllErrors() []error { return m }

// ListJobTemplateTriggersResponseValidationError is the validation error
// returned by ListJobTemplateTriggersResponse.Validate if the designated
// constraints aren't met.
type ListJobTemplateTriggersResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListJobTemplateTriggersResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListJobTemplateTriggersResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListJobTemplateTriggersResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListJobTemplateTriggersResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListJobTemplateTriggersResponseValidationError) ErrorName() string {
	return "ListJobTemplateTriggersResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListJobTemplateTriggersResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListJobTemplateTriggersResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListJobTemplateTriggersResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListJobTemplateTriggersResponseValidationError{}

// Validate checks the field values on Job with the rules defined in the proto
// definition for this message. If any rules are violated, the first error
// encountered is returned, or nil if there are no violations.
func (m *Job) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Job with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in JobMultiError, or nil if none found.
func (m *Job) ValidateAll() error {
	return m.validate(true)
}

func (m *Job) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Name

	// no validation rules for DisplayName

	// no validation rules for Description

	// no validation rules for WorkspaceName

	// no validation rules for Region

	// no validation rules for Creator

	// no validation rules for Revision

	// no validation rules for WorkflowRevision

	// no validation rules for JobType

	if all {
		switch v := interface{}(m.GetJobStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, JobValidationError{
					field:  "JobStatus",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, JobValidationError{
					field:  "JobStatus",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetJobStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return JobValidationError{
				field:  "JobStatus",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for QueueName

	if all {
		switch v := interface{}(m.GetPyTorchDDPJobTemplate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, JobValidationError{
					field:  "PyTorchDDPJobTemplate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, JobValidationError{
					field:  "PyTorchDDPJobTemplate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPyTorchDDPJobTemplate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return JobValidationError{
				field:  "PyTorchDDPJobTemplate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeepSpeedJobTemplate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, JobValidationError{
					field:  "DeepSpeedJobTemplate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, JobValidationError{
					field:  "DeepSpeedJobTemplate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeepSpeedJobTemplate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return JobValidationError{
				field:  "DeepSpeedJobTemplate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSimpleTrainingJobTemplate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, JobValidationError{
					field:  "SimpleTrainingJobTemplate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, JobValidationError{
					field:  "SimpleTrainingJobTemplate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSimpleTrainingJobTemplate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return JobValidationError{
				field:  "SimpleTrainingJobTemplate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CreateTime

	// no validation rules for UpdateTime

	if all {
		switch v := interface{}(m.GetRestartPolicy()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, JobValidationError{
					field:  "RestartPolicy",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, JobValidationError{
					field:  "RestartPolicy",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRestartPolicy()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return JobValidationError{
				field:  "RestartPolicy",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AlarmShielding

	// no validation rules for Priority

	// no validation rules for MaxWaitTime

	// no validation rules for Labels

	// no validation rules for JobTemplateName

	if len(errors) > 0 {
		return JobMultiError(errors)
	}

	return nil
}

// JobMultiError is an error wrapping multiple validation errors returned by
// Job.ValidateAll() if the designated constraints aren't met.
type JobMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m JobMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m JobMultiError) AllErrors() []error { return m }

// JobValidationError is the validation error returned by Job.Validate if the
// designated constraints aren't met.
type JobValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e JobValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e JobValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e JobValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e JobValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e JobValidationError) ErrorName() string { return "JobValidationError" }

// Error satisfies the builtin error interface
func (e JobValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sJob.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = JobValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = JobValidationError{}

// Validate checks the field values on RestartPolicy with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *RestartPolicy) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RestartPolicy with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in RestartPolicyMultiError, or
// nil if none found.
func (m *RestartPolicy) ValidateAll() error {
	return m.validate(true)
}

func (m *RestartPolicy) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Enabled

	// no validation rules for MaxRetryCount

	if len(errors) > 0 {
		return RestartPolicyMultiError(errors)
	}

	return nil
}

// RestartPolicyMultiError is an error wrapping multiple validation errors
// returned by RestartPolicy.ValidateAll() if the designated constraints
// aren't met.
type RestartPolicyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RestartPolicyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RestartPolicyMultiError) AllErrors() []error { return m }

// RestartPolicyValidationError is the validation error returned by
// RestartPolicy.Validate if the designated constraints aren't met.
type RestartPolicyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RestartPolicyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RestartPolicyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RestartPolicyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RestartPolicyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RestartPolicyValidationError) ErrorName() string { return "RestartPolicyValidationError" }

// Error satisfies the builtin error interface
func (e RestartPolicyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRestartPolicy.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RestartPolicyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RestartPolicyValidationError{}

// Validate checks the field values on DatasetVolume with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *DatasetVolume) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DatasetVolume with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in DatasetVolumeMultiError, or
// nil if none found.
func (m *DatasetVolume) ValidateAll() error {
	return m.validate(true)
}

func (m *DatasetVolume) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for MountPoint

	if len(errors) > 0 {
		return DatasetVolumeMultiError(errors)
	}

	return nil
}

// DatasetVolumeMultiError is an error wrapping multiple validation errors
// returned by DatasetVolume.ValidateAll() if the designated constraints
// aren't met.
type DatasetVolumeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DatasetVolumeMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DatasetVolumeMultiError) AllErrors() []error { return m }

// DatasetVolumeValidationError is the validation error returned by
// DatasetVolume.Validate if the designated constraints aren't met.
type DatasetVolumeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DatasetVolumeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DatasetVolumeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DatasetVolumeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DatasetVolumeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DatasetVolumeValidationError) ErrorName() string { return "DatasetVolumeValidationError" }

// Error satisfies the builtin error interface
func (e DatasetVolumeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDatasetVolume.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DatasetVolumeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DatasetVolumeValidationError{}

// Validate checks the field values on CreateJobRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CreateJobRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateJobRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateJobRequestMultiError, or nil if none found.
func (m *CreateJobRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateJobRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for DisplayName

	if utf8.RuneCountInString(m.GetDescription()) > 100 {
		err := CreateJobRequestValidationError{
			field:  "Description",
			reason: "value length must be at most 100 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if l := utf8.RuneCountInString(m.GetWorkspaceName()); l < 1 || l > 31 {
		err := CreateJobRequestValidationError{
			field:  "WorkspaceName",
			reason: "value length must be between 1 and 31 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_CreateJobRequest_WorkspaceName_Pattern.MatchString(m.GetWorkspaceName()) {
		err := CreateJobRequestValidationError{
			field:  "WorkspaceName",
			reason: "value does not match regex pattern \"^[a-z]([-a-z0-9]*[a-z0-9])?$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Region

	// no validation rules for Labels

	// no validation rules for JobType

	if l := utf8.RuneCountInString(m.GetQueueName()); l < 1 || l > 31 {
		err := CreateJobRequestValidationError{
			field:  "QueueName",
			reason: "value length must be between 1 and 31 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Priority

	if all {
		switch v := interface{}(m.GetPyTorchDDPJobTemplate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateJobRequestValidationError{
					field:  "PyTorchDDPJobTemplate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateJobRequestValidationError{
					field:  "PyTorchDDPJobTemplate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPyTorchDDPJobTemplate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateJobRequestValidationError{
				field:  "PyTorchDDPJobTemplate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeepSpeedJobTemplate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateJobRequestValidationError{
					field:  "DeepSpeedJobTemplate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateJobRequestValidationError{
					field:  "DeepSpeedJobTemplate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeepSpeedJobTemplate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateJobRequestValidationError{
				field:  "DeepSpeedJobTemplate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSimpleTrainingJobTemplate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateJobRequestValidationError{
					field:  "SimpleTrainingJobTemplate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateJobRequestValidationError{
					field:  "SimpleTrainingJobTemplate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSimpleTrainingJobTemplate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateJobRequestValidationError{
				field:  "SimpleTrainingJobTemplate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for MaxWaitTime

	if all {
		switch v := interface{}(m.GetRestartPolicy()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateJobRequestValidationError{
					field:  "RestartPolicy",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateJobRequestValidationError{
					field:  "RestartPolicy",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRestartPolicy()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateJobRequestValidationError{
				field:  "RestartPolicy",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AlarmShielding

	// no validation rules for JobTemplateName

	// no validation rules for HadoopEnabled

	if len(errors) > 0 {
		return CreateJobRequestMultiError(errors)
	}

	return nil
}

// CreateJobRequestMultiError is an error wrapping multiple validation errors
// returned by CreateJobRequest.ValidateAll() if the designated constraints
// aren't met.
type CreateJobRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateJobRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateJobRequestMultiError) AllErrors() []error { return m }

// CreateJobRequestValidationError is the validation error returned by
// CreateJobRequest.Validate if the designated constraints aren't met.
type CreateJobRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateJobRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateJobRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateJobRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateJobRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateJobRequestValidationError) ErrorName() string { return "CreateJobRequestValidationError" }

// Error satisfies the builtin error interface
func (e CreateJobRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateJobRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateJobRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateJobRequestValidationError{}

var _CreateJobRequest_WorkspaceName_Pattern = regexp.MustCompile("^[a-z]([-a-z0-9]*[a-z0-9])?$")

// Validate checks the field values on CreateJobResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CreateJobResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateJobResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateJobResponseMultiError, or nil if none found.
func (m *CreateJobResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateJobResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for JobName

	if len(errors) > 0 {
		return CreateJobResponseMultiError(errors)
	}

	return nil
}

// CreateJobResponseMultiError is an error wrapping multiple validation errors
// returned by CreateJobResponse.ValidateAll() if the designated constraints
// aren't met.
type CreateJobResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateJobResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateJobResponseMultiError) AllErrors() []error { return m }

// CreateJobResponseValidationError is the validation error returned by
// CreateJobResponse.Validate if the designated constraints aren't met.
type CreateJobResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateJobResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateJobResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateJobResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateJobResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateJobResponseValidationError) ErrorName() string {
	return "CreateJobResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateJobResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateJobResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateJobResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateJobResponseValidationError{}

// Validate checks the field values on UpdateJobRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *UpdateJobRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateJobRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateJobRequestMultiError, or nil if none found.
func (m *UpdateJobRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateJobRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for JobName

	// no validation rules for DisplayName

	if utf8.RuneCountInString(m.GetDescription()) > 100 {
		err := UpdateJobRequestValidationError{
			field:  "Description",
			reason: "value length must be at most 100 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for WorkspaceName

	// no validation rules for Creator

	// no validation rules for Priority

	if len(errors) > 0 {
		return UpdateJobRequestMultiError(errors)
	}

	return nil
}

// UpdateJobRequestMultiError is an error wrapping multiple validation errors
// returned by UpdateJobRequest.ValidateAll() if the designated constraints
// aren't met.
type UpdateJobRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateJobRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateJobRequestMultiError) AllErrors() []error { return m }

// UpdateJobRequestValidationError is the validation error returned by
// UpdateJobRequest.Validate if the designated constraints aren't met.
type UpdateJobRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateJobRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateJobRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateJobRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateJobRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateJobRequestValidationError) ErrorName() string { return "UpdateJobRequestValidationError" }

// Error satisfies the builtin error interface
func (e UpdateJobRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateJobRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateJobRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateJobRequestValidationError{}

// Validate checks the field values on CreateTensorboardRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateTensorboardRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateTensorboardRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateTensorboardRequestMultiError, or nil if none found.
func (m *CreateTensorboardRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateTensorboardRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for WorkspaceName

	// no validation rules for JobName

	if len(errors) > 0 {
		return CreateTensorboardRequestMultiError(errors)
	}

	return nil
}

// CreateTensorboardRequestMultiError is an error wrapping multiple validation
// errors returned by CreateTensorboardRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateTensorboardRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateTensorboardRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateTensorboardRequestMultiError) AllErrors() []error { return m }

// CreateTensorboardRequestValidationError is the validation error returned by
// CreateTensorboardRequest.Validate if the designated constraints aren't met.
type CreateTensorboardRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateTensorboardRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateTensorboardRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateTensorboardRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateTensorboardRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateTensorboardRequestValidationError) ErrorName() string {
	return "CreateTensorboardRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateTensorboardRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateTensorboardRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateTensorboardRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateTensorboardRequestValidationError{}

// Validate checks the field values on GetTensorboardRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetTensorboardRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetTensorboardRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetTensorboardRequestMultiError, or nil if none found.
func (m *GetTensorboardRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTensorboardRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for WorkspaceName

	// no validation rules for JobName

	if len(errors) > 0 {
		return GetTensorboardRequestMultiError(errors)
	}

	return nil
}

// GetTensorboardRequestMultiError is an error wrapping multiple validation
// errors returned by GetTensorboardRequest.ValidateAll() if the designated
// constraints aren't met.
type GetTensorboardRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTensorboardRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTensorboardRequestMultiError) AllErrors() []error { return m }

// GetTensorboardRequestValidationError is the validation error returned by
// GetTensorboardRequest.Validate if the designated constraints aren't met.
type GetTensorboardRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTensorboardRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTensorboardRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTensorboardRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTensorboardRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTensorboardRequestValidationError) ErrorName() string {
	return "GetTensorboardRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetTensorboardRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTensorboardRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTensorboardRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTensorboardRequestValidationError{}

// Validate checks the field values on GetTensorboardResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetTensorboardResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetTensorboardResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetTensorboardResponseMultiError, or nil if none found.
func (m *GetTensorboardResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTensorboardResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Url

	// no validation rules for State

	if len(errors) > 0 {
		return GetTensorboardResponseMultiError(errors)
	}

	return nil
}

// GetTensorboardResponseMultiError is an error wrapping multiple validation
// errors returned by GetTensorboardResponse.ValidateAll() if the designated
// constraints aren't met.
type GetTensorboardResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTensorboardResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTensorboardResponseMultiError) AllErrors() []error { return m }

// GetTensorboardResponseValidationError is the validation error returned by
// GetTensorboardResponse.Validate if the designated constraints aren't met.
type GetTensorboardResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTensorboardResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTensorboardResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTensorboardResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTensorboardResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTensorboardResponseValidationError) ErrorName() string {
	return "GetTensorboardResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetTensorboardResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTensorboardResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTensorboardResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTensorboardResponseValidationError{}

// Validate checks the field values on PyTorchDDPJobTemplate with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PyTorchDDPJobTemplate) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PyTorchDDPJobTemplate with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PyTorchDDPJobTemplateMultiError, or nil if none found.
func (m *PyTorchDDPJobTemplate) ValidateAll() error {
	return m.validate(true)
}

func (m *PyTorchDDPJobTemplate) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TensorboardEnabled

	for idx, item := range m.GetVolumeSpecs() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PyTorchDDPJobTemplateValidationError{
						field:  fmt.Sprintf("VolumeSpecs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PyTorchDDPJobTemplateValidationError{
						field:  fmt.Sprintf("VolumeSpecs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PyTorchDDPJobTemplateValidationError{
					field:  fmt.Sprintf("VolumeSpecs[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetSourceCode()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PyTorchDDPJobTemplateValidationError{
					field:  "SourceCode",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PyTorchDDPJobTemplateValidationError{
					field:  "SourceCode",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSourceCode()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PyTorchDDPJobTemplateValidationError{
				field:  "SourceCode",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetEnvVars() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PyTorchDDPJobTemplateValidationError{
						field:  fmt.Sprintf("EnvVars[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PyTorchDDPJobTemplateValidationError{
						field:  fmt.Sprintf("EnvVars[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PyTorchDDPJobTemplateValidationError{
					field:  fmt.Sprintf("EnvVars[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Command

	// no validation rules for Image

	if all {
		switch v := interface{}(m.GetMaster()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PyTorchDDPJobTemplateValidationError{
					field:  "Master",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PyTorchDDPJobTemplateValidationError{
					field:  "Master",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMaster()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PyTorchDDPJobTemplateValidationError{
				field:  "Master",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetWorker()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PyTorchDDPJobTemplateValidationError{
					field:  "Worker",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PyTorchDDPJobTemplateValidationError{
					field:  "Worker",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetWorker()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PyTorchDDPJobTemplateValidationError{
				field:  "Worker",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetConfigSpecs() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PyTorchDDPJobTemplateValidationError{
						field:  fmt.Sprintf("ConfigSpecs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PyTorchDDPJobTemplateValidationError{
						field:  fmt.Sprintf("ConfigSpecs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PyTorchDDPJobTemplateValidationError{
					field:  fmt.Sprintf("ConfigSpecs[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return PyTorchDDPJobTemplateMultiError(errors)
	}

	return nil
}

// PyTorchDDPJobTemplateMultiError is an error wrapping multiple validation
// errors returned by PyTorchDDPJobTemplate.ValidateAll() if the designated
// constraints aren't met.
type PyTorchDDPJobTemplateMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PyTorchDDPJobTemplateMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PyTorchDDPJobTemplateMultiError) AllErrors() []error { return m }

// PyTorchDDPJobTemplateValidationError is the validation error returned by
// PyTorchDDPJobTemplate.Validate if the designated constraints aren't met.
type PyTorchDDPJobTemplateValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PyTorchDDPJobTemplateValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PyTorchDDPJobTemplateValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PyTorchDDPJobTemplateValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PyTorchDDPJobTemplateValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PyTorchDDPJobTemplateValidationError) ErrorName() string {
	return "PyTorchDDPJobTemplateValidationError"
}

// Error satisfies the builtin error interface
func (e PyTorchDDPJobTemplateValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPyTorchDDPJobTemplate.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PyTorchDDPJobTemplateValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PyTorchDDPJobTemplateValidationError{}

// Validate checks the field values on DeepSpeedJobTemplate with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeepSpeedJobTemplate) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeepSpeedJobTemplate with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeepSpeedJobTemplateMultiError, or nil if none found.
func (m *DeepSpeedJobTemplate) ValidateAll() error {
	return m.validate(true)
}

func (m *DeepSpeedJobTemplate) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TensorboardEnabled

	for idx, item := range m.GetVolumeSpecs() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DeepSpeedJobTemplateValidationError{
						field:  fmt.Sprintf("VolumeSpecs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DeepSpeedJobTemplateValidationError{
						field:  fmt.Sprintf("VolumeSpecs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DeepSpeedJobTemplateValidationError{
					field:  fmt.Sprintf("VolumeSpecs[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetSourceCode()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DeepSpeedJobTemplateValidationError{
					field:  "SourceCode",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DeepSpeedJobTemplateValidationError{
					field:  "SourceCode",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSourceCode()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DeepSpeedJobTemplateValidationError{
				field:  "SourceCode",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetEnvVars() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DeepSpeedJobTemplateValidationError{
						field:  fmt.Sprintf("EnvVars[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DeepSpeedJobTemplateValidationError{
						field:  fmt.Sprintf("EnvVars[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DeepSpeedJobTemplateValidationError{
					field:  fmt.Sprintf("EnvVars[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Command

	// no validation rules for Image

	if all {
		switch v := interface{}(m.GetMaster()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DeepSpeedJobTemplateValidationError{
					field:  "Master",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DeepSpeedJobTemplateValidationError{
					field:  "Master",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMaster()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DeepSpeedJobTemplateValidationError{
				field:  "Master",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetWorker()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DeepSpeedJobTemplateValidationError{
					field:  "Worker",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DeepSpeedJobTemplateValidationError{
					field:  "Worker",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetWorker()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DeepSpeedJobTemplateValidationError{
				field:  "Worker",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetConfigSpecs() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DeepSpeedJobTemplateValidationError{
						field:  fmt.Sprintf("ConfigSpecs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DeepSpeedJobTemplateValidationError{
						field:  fmt.Sprintf("ConfigSpecs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DeepSpeedJobTemplateValidationError{
					field:  fmt.Sprintf("ConfigSpecs[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return DeepSpeedJobTemplateMultiError(errors)
	}

	return nil
}

// DeepSpeedJobTemplateMultiError is an error wrapping multiple validation
// errors returned by DeepSpeedJobTemplate.ValidateAll() if the designated
// constraints aren't met.
type DeepSpeedJobTemplateMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeepSpeedJobTemplateMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeepSpeedJobTemplateMultiError) AllErrors() []error { return m }

// DeepSpeedJobTemplateValidationError is the validation error returned by
// DeepSpeedJobTemplate.Validate if the designated constraints aren't met.
type DeepSpeedJobTemplateValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeepSpeedJobTemplateValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeepSpeedJobTemplateValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeepSpeedJobTemplateValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeepSpeedJobTemplateValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeepSpeedJobTemplateValidationError) ErrorName() string {
	return "DeepSpeedJobTemplateValidationError"
}

// Error satisfies the builtin error interface
func (e DeepSpeedJobTemplateValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeepSpeedJobTemplate.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeepSpeedJobTemplateValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeepSpeedJobTemplateValidationError{}

// Validate checks the field values on SimpleTrainingJobTemplate with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SimpleTrainingJobTemplate) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SimpleTrainingJobTemplate with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SimpleTrainingJobTemplateMultiError, or nil if none found.
func (m *SimpleTrainingJobTemplate) ValidateAll() error {
	return m.validate(true)
}

func (m *SimpleTrainingJobTemplate) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TensorboardEnabled

	if all {
		switch v := interface{}(m.GetSourceCode()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SimpleTrainingJobTemplateValidationError{
					field:  "SourceCode",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SimpleTrainingJobTemplateValidationError{
					field:  "SourceCode",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSourceCode()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SimpleTrainingJobTemplateValidationError{
				field:  "SourceCode",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Command

	// no validation rules for Image

	for idx, item := range m.GetEnvVars() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SimpleTrainingJobTemplateValidationError{
						field:  fmt.Sprintf("EnvVars[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SimpleTrainingJobTemplateValidationError{
						field:  fmt.Sprintf("EnvVars[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SimpleTrainingJobTemplateValidationError{
					field:  fmt.Sprintf("EnvVars[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetVolumeSpecs() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SimpleTrainingJobTemplateValidationError{
						field:  fmt.Sprintf("VolumeSpecs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SimpleTrainingJobTemplateValidationError{
						field:  fmt.Sprintf("VolumeSpecs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SimpleTrainingJobTemplateValidationError{
					field:  fmt.Sprintf("VolumeSpecs[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetSpecification()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SimpleTrainingJobTemplateValidationError{
					field:  "Specification",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SimpleTrainingJobTemplateValidationError{
					field:  "Specification",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSpecification()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SimpleTrainingJobTemplateValidationError{
				field:  "Specification",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for NodeSpecificationName

	for idx, item := range m.GetConfigSpecs() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SimpleTrainingJobTemplateValidationError{
						field:  fmt.Sprintf("ConfigSpecs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SimpleTrainingJobTemplateValidationError{
						field:  fmt.Sprintf("ConfigSpecs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SimpleTrainingJobTemplateValidationError{
					field:  fmt.Sprintf("ConfigSpecs[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return SimpleTrainingJobTemplateMultiError(errors)
	}

	return nil
}

// SimpleTrainingJobTemplateMultiError is an error wrapping multiple validation
// errors returned by SimpleTrainingJobTemplate.ValidateAll() if the
// designated constraints aren't met.
type SimpleTrainingJobTemplateMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SimpleTrainingJobTemplateMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SimpleTrainingJobTemplateMultiError) AllErrors() []error { return m }

// SimpleTrainingJobTemplateValidationError is the validation error returned by
// SimpleTrainingJobTemplate.Validate if the designated constraints aren't met.
type SimpleTrainingJobTemplateValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SimpleTrainingJobTemplateValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SimpleTrainingJobTemplateValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SimpleTrainingJobTemplateValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SimpleTrainingJobTemplateValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SimpleTrainingJobTemplateValidationError) ErrorName() string {
	return "SimpleTrainingJobTemplateValidationError"
}

// Error satisfies the builtin error interface
func (e SimpleTrainingJobTemplateValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSimpleTrainingJobTemplate.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SimpleTrainingJobTemplateValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SimpleTrainingJobTemplateValidationError{}

// Validate checks the field values on TaskSpec with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *TaskSpec) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TaskSpec with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in TaskSpecMultiError, or nil
// if none found.
func (m *TaskSpec) ValidateAll() error {
	return m.validate(true)
}

func (m *TaskSpec) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for Replicas

	// no validation rules for Command

	// no validation rules for Image

	for idx, item := range m.GetEnvVars() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, TaskSpecValidationError{
						field:  fmt.Sprintf("EnvVars[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, TaskSpecValidationError{
						field:  fmt.Sprintf("EnvVars[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return TaskSpecValidationError{
					field:  fmt.Sprintf("EnvVars[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetVolumeSpecs() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, TaskSpecValidationError{
						field:  fmt.Sprintf("VolumeSpecs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, TaskSpecValidationError{
						field:  fmt.Sprintf("VolumeSpecs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return TaskSpecValidationError{
					field:  fmt.Sprintf("VolumeSpecs[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetSpecification()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TaskSpecValidationError{
					field:  "Specification",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TaskSpecValidationError{
					field:  "Specification",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSpecification()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TaskSpecValidationError{
				field:  "Specification",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for NodeSpecificationName

	// no validation rules for Slots

	for idx, item := range m.GetConfigSpecs() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, TaskSpecValidationError{
						field:  fmt.Sprintf("ConfigSpecs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, TaskSpecValidationError{
						field:  fmt.Sprintf("ConfigSpecs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return TaskSpecValidationError{
					field:  fmt.Sprintf("ConfigSpecs[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return TaskSpecMultiError(errors)
	}

	return nil
}

// TaskSpecMultiError is an error wrapping multiple validation errors returned
// by TaskSpec.ValidateAll() if the designated constraints aren't met.
type TaskSpecMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TaskSpecMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TaskSpecMultiError) AllErrors() []error { return m }

// TaskSpecValidationError is the validation error returned by
// TaskSpec.Validate if the designated constraints aren't met.
type TaskSpecValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TaskSpecValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TaskSpecValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TaskSpecValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TaskSpecValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TaskSpecValidationError) ErrorName() string { return "TaskSpecValidationError" }

// Error satisfies the builtin error interface
func (e TaskSpecValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTaskSpec.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TaskSpecValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TaskSpecValidationError{}

// Validate checks the field values on SourceCode with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SourceCode) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SourceCode with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SourceCodeMultiError, or
// nil if none found.
func (m *SourceCode) ValidateAll() error {
	return m.validate(true)
}

func (m *SourceCode) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MountPoint

	switch v := m.Source.(type) {
	case *SourceCode_CloudFsSource_:
		if v == nil {
			err := SourceCodeValidationError{
				field:  "Source",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetCloudFsSource()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SourceCodeValidationError{
						field:  "CloudFsSource",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SourceCodeValidationError{
						field:  "CloudFsSource",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetCloudFsSource()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SourceCodeValidationError{
					field:  "CloudFsSource",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *SourceCode_GitSource_:
		if v == nil {
			err := SourceCodeValidationError{
				field:  "Source",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetGitSource()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SourceCodeValidationError{
						field:  "GitSource",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SourceCodeValidationError{
						field:  "GitSource",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetGitSource()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SourceCodeValidationError{
					field:  "GitSource",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *SourceCode_CubeFsSource_:
		if v == nil {
			err := SourceCodeValidationError{
				field:  "Source",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetCubeFsSource()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SourceCodeValidationError{
						field:  "CubeFsSource",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SourceCodeValidationError{
						field:  "CubeFsSource",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetCubeFsSource()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SourceCodeValidationError{
					field:  "CubeFsSource",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return SourceCodeMultiError(errors)
	}

	return nil
}

// SourceCodeMultiError is an error wrapping multiple validation errors
// returned by SourceCode.ValidateAll() if the designated constraints aren't met.
type SourceCodeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SourceCodeMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SourceCodeMultiError) AllErrors() []error { return m }

// SourceCodeValidationError is the validation error returned by
// SourceCode.Validate if the designated constraints aren't met.
type SourceCodeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SourceCodeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SourceCodeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SourceCodeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SourceCodeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SourceCodeValidationError) ErrorName() string { return "SourceCodeValidationError" }

// Error satisfies the builtin error interface
func (e SourceCodeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSourceCode.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SourceCodeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SourceCodeValidationError{}

// Validate checks the field values on JobStatus with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *JobStatus) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on JobStatus with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in JobStatusMultiError, or nil
// if none found.
func (m *JobStatus) ValidateAll() error {
	return m.validate(true)
}

func (m *JobStatus) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for State

	// no validation rules for Pending

	// no validation rules for Running

	// no validation rules for Succeeded

	// no validation rules for Failed

	// no validation rules for Terminating

	// no validation rules for Unknown

	// no validation rules for Version

	// no validation rules for RetryCount

	// no validation rules for Reason

	// no validation rules for Message

	// no validation rules for LastTransitionTime

	// no validation rules for CreateTimestamp

	for idx, item := range m.GetConditions() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, JobStatusValidationError{
						field:  fmt.Sprintf("Conditions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, JobStatusValidationError{
						field:  fmt.Sprintf("Conditions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return JobStatusValidationError{
					field:  fmt.Sprintf("Conditions[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for RunningDuration

	// no validation rules for ClusterName

	if len(errors) > 0 {
		return JobStatusMultiError(errors)
	}

	return nil
}

// JobStatusMultiError is an error wrapping multiple validation errors returned
// by JobStatus.ValidateAll() if the designated constraints aren't met.
type JobStatusMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m JobStatusMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m JobStatusMultiError) AllErrors() []error { return m }

// JobStatusValidationError is the validation error returned by
// JobStatus.Validate if the designated constraints aren't met.
type JobStatusValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e JobStatusValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e JobStatusValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e JobStatusValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e JobStatusValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e JobStatusValidationError) ErrorName() string { return "JobStatusValidationError" }

// Error satisfies the builtin error interface
func (e JobStatusValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sJobStatus.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = JobStatusValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = JobStatusValidationError{}

// Validate checks the field values on ListJobTaskStatus with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ListJobTaskStatus) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListJobTaskStatus with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListJobTaskStatusMultiError, or nil if none found.
func (m *ListJobTaskStatus) ValidateAll() error {
	return m.validate(true)
}

func (m *ListJobTaskStatus) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetTaskStatus() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListJobTaskStatusValidationError{
						field:  fmt.Sprintf("TaskStatus[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListJobTaskStatusValidationError{
						field:  fmt.Sprintf("TaskStatus[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListJobTaskStatusValidationError{
					field:  fmt.Sprintf("TaskStatus[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListJobTaskStatusMultiError(errors)
	}

	return nil
}

// ListJobTaskStatusMultiError is an error wrapping multiple validation errors
// returned by ListJobTaskStatus.ValidateAll() if the designated constraints
// aren't met.
type ListJobTaskStatusMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListJobTaskStatusMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListJobTaskStatusMultiError) AllErrors() []error { return m }

// ListJobTaskStatusValidationError is the validation error returned by
// ListJobTaskStatus.Validate if the designated constraints aren't met.
type ListJobTaskStatusValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListJobTaskStatusValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListJobTaskStatusValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListJobTaskStatusValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListJobTaskStatusValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListJobTaskStatusValidationError) ErrorName() string {
	return "ListJobTaskStatusValidationError"
}

// Error satisfies the builtin error interface
func (e ListJobTaskStatusValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListJobTaskStatus.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListJobTaskStatusValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListJobTaskStatusValidationError{}

// Validate checks the field values on TaskStatus with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *TaskStatus) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TaskStatus with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in TaskStatusMultiError, or
// nil if none found.
func (m *TaskStatus) ValidateAll() error {
	return m.validate(true)
}

func (m *TaskStatus) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Phase

	// no validation rules for PodName

	// no validation rules for PodNamespace

	// no validation rules for PodIP

	// no validation rules for Cluster

	// no validation rules for Region

	// no validation rules for Zone

	// no validation rules for NodeIP

	// no validation rules for CreateTimestamp

	// no validation rules for FinishTimestamp

	// no validation rules for Image

	for idx, item := range m.GetConditions() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, TaskStatusValidationError{
						field:  fmt.Sprintf("Conditions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, TaskStatusValidationError{
						field:  fmt.Sprintf("Conditions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return TaskStatusValidationError{
					field:  fmt.Sprintf("Conditions[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for RestartCount

	// no validation rules for Reason

	// no validation rules for Message

	// no validation rules for TaskName

	// no validation rules for TaskEnv

	// no validation rules for HostIP

	// no validation rules for TaskID

	// no validation rules for JobVersion

	// no validation rules for HistoricalLogStartTimestamp

	// no validation rules for HistoricalLogEndTimestamp

	if len(errors) > 0 {
		return TaskStatusMultiError(errors)
	}

	return nil
}

// TaskStatusMultiError is an error wrapping multiple validation errors
// returned by TaskStatus.ValidateAll() if the designated constraints aren't met.
type TaskStatusMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TaskStatusMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TaskStatusMultiError) AllErrors() []error { return m }

// TaskStatusValidationError is the validation error returned by
// TaskStatus.Validate if the designated constraints aren't met.
type TaskStatusValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TaskStatusValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TaskStatusValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TaskStatusValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TaskStatusValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TaskStatusValidationError) ErrorName() string { return "TaskStatusValidationError" }

// Error satisfies the builtin error interface
func (e TaskStatusValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTaskStatus.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TaskStatusValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TaskStatusValidationError{}

// Validate checks the field values on GetJobIDRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetJobIDRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetJobIDRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetJobIDRequestMultiError, or nil if none found.
func (m *GetJobIDRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetJobIDRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for WorkspaceName

	// no validation rules for JobName

	if len(errors) > 0 {
		return GetJobIDRequestMultiError(errors)
	}

	return nil
}

// GetJobIDRequestMultiError is an error wrapping multiple validation errors
// returned by GetJobIDRequest.ValidateAll() if the designated constraints
// aren't met.
type GetJobIDRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetJobIDRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetJobIDRequestMultiError) AllErrors() []error { return m }

// GetJobIDRequestValidationError is the validation error returned by
// GetJobIDRequest.Validate if the designated constraints aren't met.
type GetJobIDRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetJobIDRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetJobIDRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetJobIDRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetJobIDRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetJobIDRequestValidationError) ErrorName() string { return "GetJobIDRequestValidationError" }

// Error satisfies the builtin error interface
func (e GetJobIDRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetJobIDRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetJobIDRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetJobIDRequestValidationError{}

// Validate checks the field values on GetJobIDResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetJobIDResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetJobIDResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetJobIDResponseMultiError, or nil if none found.
func (m *GetJobIDResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetJobIDResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for JobID

	if len(errors) > 0 {
		return GetJobIDResponseMultiError(errors)
	}

	return nil
}

// GetJobIDResponseMultiError is an error wrapping multiple validation errors
// returned by GetJobIDResponse.ValidateAll() if the designated constraints
// aren't met.
type GetJobIDResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetJobIDResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetJobIDResponseMultiError) AllErrors() []error { return m }

// GetJobIDResponseValidationError is the validation error returned by
// GetJobIDResponse.Validate if the designated constraints aren't met.
type GetJobIDResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetJobIDResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetJobIDResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetJobIDResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetJobIDResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetJobIDResponseValidationError) ErrorName() string { return "GetJobIDResponseValidationError" }

// Error satisfies the builtin error interface
func (e GetJobIDResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetJobIDResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetJobIDResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetJobIDResponseValidationError{}

// Validate checks the field values on TaskCondition with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *TaskCondition) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TaskCondition with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in TaskConditionMultiError, or
// nil if none found.
func (m *TaskCondition) ValidateAll() error {
	return m.validate(true)
}

func (m *TaskCondition) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Reason

	// no validation rules for Message

	// no validation rules for Type

	// no validation rules for Status

	// no validation rules for LastTransitionTime

	if len(errors) > 0 {
		return TaskConditionMultiError(errors)
	}

	return nil
}

// TaskConditionMultiError is an error wrapping multiple validation errors
// returned by TaskCondition.ValidateAll() if the designated constraints
// aren't met.
type TaskConditionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TaskConditionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TaskConditionMultiError) AllErrors() []error { return m }

// TaskConditionValidationError is the validation error returned by
// TaskCondition.Validate if the designated constraints aren't met.
type TaskConditionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TaskConditionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TaskConditionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TaskConditionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TaskConditionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TaskConditionValidationError) ErrorName() string { return "TaskConditionValidationError" }

// Error satisfies the builtin error interface
func (e TaskConditionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTaskCondition.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TaskConditionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TaskConditionValidationError{}

// Validate checks the field values on JobCondition with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *JobCondition) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on JobCondition with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in JobConditionMultiError, or
// nil if none found.
func (m *JobCondition) ValidateAll() error {
	return m.validate(true)
}

func (m *JobCondition) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Phase

	// no validation rules for LastTransitionTime

	// no validation rules for Reason

	// no validation rules for Message

	// no validation rules for ConditionStatus

	if len(errors) > 0 {
		return JobConditionMultiError(errors)
	}

	return nil
}

// JobConditionMultiError is an error wrapping multiple validation errors
// returned by JobCondition.ValidateAll() if the designated constraints aren't met.
type JobConditionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m JobConditionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m JobConditionMultiError) AllErrors() []error { return m }

// JobConditionValidationError is the validation error returned by
// JobCondition.Validate if the designated constraints aren't met.
type JobConditionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e JobConditionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e JobConditionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e JobConditionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e JobConditionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e JobConditionValidationError) ErrorName() string { return "JobConditionValidationError" }

// Error satisfies the builtin error interface
func (e JobConditionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sJobCondition.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = JobConditionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = JobConditionValidationError{}

// Validate checks the field values on StopJobRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *StopJobRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StopJobRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in StopJobRequestMultiError,
// or nil if none found.
func (m *StopJobRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *StopJobRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for WorkspaceName

	// no validation rules for JobName

	if len(errors) > 0 {
		return StopJobRequestMultiError(errors)
	}

	return nil
}

// StopJobRequestMultiError is an error wrapping multiple validation errors
// returned by StopJobRequest.ValidateAll() if the designated constraints
// aren't met.
type StopJobRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StopJobRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StopJobRequestMultiError) AllErrors() []error { return m }

// StopJobRequestValidationError is the validation error returned by
// StopJobRequest.Validate if the designated constraints aren't met.
type StopJobRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StopJobRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StopJobRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StopJobRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StopJobRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StopJobRequestValidationError) ErrorName() string { return "StopJobRequestValidationError" }

// Error satisfies the builtin error interface
func (e StopJobRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStopJobRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StopJobRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StopJobRequestValidationError{}

// Validate checks the field values on GetJobBaseRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetJobBaseRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetJobBaseRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetJobBaseRequestMultiError, or nil if none found.
func (m *GetJobBaseRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetJobBaseRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for WorkspaceName

	// no validation rules for JobName

	// no validation rules for IsDeleted

	if len(errors) > 0 {
		return GetJobBaseRequestMultiError(errors)
	}

	return nil
}

// GetJobBaseRequestMultiError is an error wrapping multiple validation errors
// returned by GetJobBaseRequest.ValidateAll() if the designated constraints
// aren't met.
type GetJobBaseRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetJobBaseRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetJobBaseRequestMultiError) AllErrors() []error { return m }

// GetJobBaseRequestValidationError is the validation error returned by
// GetJobBaseRequest.Validate if the designated constraints aren't met.
type GetJobBaseRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetJobBaseRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetJobBaseRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetJobBaseRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetJobBaseRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetJobBaseRequestValidationError) ErrorName() string {
	return "GetJobBaseRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetJobBaseRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetJobBaseRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetJobBaseRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetJobBaseRequestValidationError{}

// Validate checks the field values on GetJobRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetJobRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetJobRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in GetJobRequestMultiError, or
// nil if none found.
func (m *GetJobRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetJobRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for WorkspaceName

	// no validation rules for JobName

	// no validation rules for IsDeleted

	if len(errors) > 0 {
		return GetJobRequestMultiError(errors)
	}

	return nil
}

// GetJobRequestMultiError is an error wrapping multiple validation errors
// returned by GetJobRequest.ValidateAll() if the designated constraints
// aren't met.
type GetJobRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetJobRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetJobRequestMultiError) AllErrors() []error { return m }

// GetJobRequestValidationError is the validation error returned by
// GetJobRequest.Validate if the designated constraints aren't met.
type GetJobRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetJobRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetJobRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetJobRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetJobRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetJobRequestValidationError) ErrorName() string { return "GetJobRequestValidationError" }

// Error satisfies the builtin error interface
func (e GetJobRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetJobRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetJobRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetJobRequestValidationError{}

// Validate checks the field values on DeleteJobRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DeleteJobRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteJobRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteJobRequestMultiError, or nil if none found.
func (m *DeleteJobRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteJobRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for WorkspaceName

	// no validation rules for JobName

	// no validation rules for IsDeleted

	if len(errors) > 0 {
		return DeleteJobRequestMultiError(errors)
	}

	return nil
}

// DeleteJobRequestMultiError is an error wrapping multiple validation errors
// returned by DeleteJobRequest.ValidateAll() if the designated constraints
// aren't met.
type DeleteJobRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteJobRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteJobRequestMultiError) AllErrors() []error { return m }

// DeleteJobRequestValidationError is the validation error returned by
// DeleteJobRequest.Validate if the designated constraints aren't met.
type DeleteJobRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteJobRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteJobRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteJobRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteJobRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteJobRequestValidationError) ErrorName() string { return "DeleteJobRequestValidationError" }

// Error satisfies the builtin error interface
func (e DeleteJobRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteJobRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteJobRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteJobRequestValidationError{}

// Validate checks the field values on DeleteJobTemplateRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteJobTemplateRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteJobTemplateRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteJobTemplateRequestMultiError, or nil if none found.
func (m *DeleteJobTemplateRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteJobTemplateRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for WorkspaceName

	// no validation rules for JobTemplateName

	if len(errors) > 0 {
		return DeleteJobTemplateRequestMultiError(errors)
	}

	return nil
}

// DeleteJobTemplateRequestMultiError is an error wrapping multiple validation
// errors returned by DeleteJobTemplateRequest.ValidateAll() if the designated
// constraints aren't met.
type DeleteJobTemplateRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteJobTemplateRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteJobTemplateRequestMultiError) AllErrors() []error { return m }

// DeleteJobTemplateRequestValidationError is the validation error returned by
// DeleteJobTemplateRequest.Validate if the designated constraints aren't met.
type DeleteJobTemplateRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteJobTemplateRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteJobTemplateRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteJobTemplateRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteJobTemplateRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteJobTemplateRequestValidationError) ErrorName() string {
	return "DeleteJobTemplateRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteJobTemplateRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteJobTemplateRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteJobTemplateRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteJobTemplateRequestValidationError{}

// Validate checks the field values on GetJobTasksRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetJobTasksRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetJobTasksRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetJobTasksRequestMultiError, or nil if none found.
func (m *GetJobTasksRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetJobTasksRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for WorkspaceName

	// no validation rules for JobName

	// no validation rules for IsDeleted

	// no validation rules for ShowTheLatestVersion

	if len(errors) > 0 {
		return GetJobTasksRequestMultiError(errors)
	}

	return nil
}

// GetJobTasksRequestMultiError is an error wrapping multiple validation errors
// returned by GetJobTasksRequest.ValidateAll() if the designated constraints
// aren't met.
type GetJobTasksRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetJobTasksRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetJobTasksRequestMultiError) AllErrors() []error { return m }

// GetJobTasksRequestValidationError is the validation error returned by
// GetJobTasksRequest.Validate if the designated constraints aren't met.
type GetJobTasksRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetJobTasksRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetJobTasksRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetJobTasksRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetJobTasksRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetJobTasksRequestValidationError) ErrorName() string {
	return "GetJobTasksRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetJobTasksRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetJobTasksRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetJobTasksRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetJobTasksRequestValidationError{}

// Validate checks the field values on GetJobVolumeRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetJobVolumeRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetJobVolumeRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetJobVolumeRequestMultiError, or nil if none found.
func (m *GetJobVolumeRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetJobVolumeRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for WorkspaceName

	// no validation rules for JobName

	// no validation rules for IsDeleted

	if len(errors) > 0 {
		return GetJobVolumeRequestMultiError(errors)
	}

	return nil
}

// GetJobVolumeRequestMultiError is an error wrapping multiple validation
// errors returned by GetJobVolumeRequest.ValidateAll() if the designated
// constraints aren't met.
type GetJobVolumeRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetJobVolumeRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetJobVolumeRequestMultiError) AllErrors() []error { return m }

// GetJobVolumeRequestValidationError is the validation error returned by
// GetJobVolumeRequest.Validate if the designated constraints aren't met.
type GetJobVolumeRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetJobVolumeRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetJobVolumeRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetJobVolumeRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetJobVolumeRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetJobVolumeRequestValidationError) ErrorName() string {
	return "GetJobVolumeRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetJobVolumeRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetJobVolumeRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetJobVolumeRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetJobVolumeRequestValidationError{}

// Validate checks the field values on ListJobsOptions with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ListJobsOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListJobsOptions with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListJobsOptionsMultiError, or nil if none found.
func (m *ListJobsOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *ListJobsOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Creator

	// no validation rules for WorkspaceName

	// no validation rules for QueueName

	// no validation rules for JobType

	// no validation rules for Name

	// no validation rules for DisplayName

	// no validation rules for Region

	// no validation rules for StatusEnabled

	// no validation rules for Member

	// no validation rules for Page

	// no validation rules for PageSize

	// no validation rules for State

	// no validation rules for JobTemplateName

	if len(errors) > 0 {
		return ListJobsOptionsMultiError(errors)
	}

	return nil
}

// ListJobsOptionsMultiError is an error wrapping multiple validation errors
// returned by ListJobsOptions.ValidateAll() if the designated constraints
// aren't met.
type ListJobsOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListJobsOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListJobsOptionsMultiError) AllErrors() []error { return m }

// ListJobsOptionsValidationError is the validation error returned by
// ListJobsOptions.Validate if the designated constraints aren't met.
type ListJobsOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListJobsOptionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListJobsOptionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListJobsOptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListJobsOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListJobsOptionsValidationError) ErrorName() string { return "ListJobsOptionsValidationError" }

// Error satisfies the builtin error interface
func (e ListJobsOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListJobsOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListJobsOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListJobsOptionsValidationError{}

// Validate checks the field values on ListJobsResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ListJobsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListJobsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListJobsResponseMultiError, or nil if none found.
func (m *ListJobsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListJobsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetJobs() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListJobsResponseValidationError{
						field:  fmt.Sprintf("Jobs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListJobsResponseValidationError{
						field:  fmt.Sprintf("Jobs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListJobsResponseValidationError{
					field:  fmt.Sprintf("Jobs[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Total

	if len(errors) > 0 {
		return ListJobsResponseMultiError(errors)
	}

	return nil
}

// ListJobsResponseMultiError is an error wrapping multiple validation errors
// returned by ListJobsResponse.ValidateAll() if the designated constraints
// aren't met.
type ListJobsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListJobsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListJobsResponseMultiError) AllErrors() []error { return m }

// ListJobsResponseValidationError is the validation error returned by
// ListJobsResponse.Validate if the designated constraints aren't met.
type ListJobsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListJobsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListJobsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListJobsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListJobsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListJobsResponseValidationError) ErrorName() string { return "ListJobsResponseValidationError" }

// Error satisfies the builtin error interface
func (e ListJobsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListJobsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListJobsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListJobsResponseValidationError{}

// Validate checks the field values on JobTimeline with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *JobTimeline) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on JobTimeline with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in JobTimelineMultiError, or
// nil if none found.
func (m *JobTimeline) ValidateAll() error {
	return m.validate(true)
}

func (m *JobTimeline) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for WorkspaceName

	// no validation rules for StartTime

	// no validation rules for Status

	// no validation rules for Message

	// no validation rules for Reason

	if len(errors) > 0 {
		return JobTimelineMultiError(errors)
	}

	return nil
}

// JobTimelineMultiError is an error wrapping multiple validation errors
// returned by JobTimeline.ValidateAll() if the designated constraints aren't met.
type JobTimelineMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m JobTimelineMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m JobTimelineMultiError) AllErrors() []error { return m }

// JobTimelineValidationError is the validation error returned by
// JobTimeline.Validate if the designated constraints aren't met.
type JobTimelineValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e JobTimelineValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e JobTimelineValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e JobTimelineValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e JobTimelineValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e JobTimelineValidationError) ErrorName() string { return "JobTimelineValidationError" }

// Error satisfies the builtin error interface
func (e JobTimelineValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sJobTimeline.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = JobTimelineValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = JobTimelineValidationError{}

// Validate checks the field values on JobVolume with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *JobVolume) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on JobVolume with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in JobVolumeMultiError, or nil
// if none found.
func (m *JobVolume) ValidateAll() error {
	return m.validate(true)
}

func (m *JobVolume) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for MountPoint

	// no validation rules for VolumeType

	// no validation rules for SubPath

	// no validation rules for Scope

	if len(errors) > 0 {
		return JobVolumeMultiError(errors)
	}

	return nil
}

// JobVolumeMultiError is an error wrapping multiple validation errors returned
// by JobVolume.ValidateAll() if the designated constraints aren't met.
type JobVolumeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m JobVolumeMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m JobVolumeMultiError) AllErrors() []error { return m }

// JobVolumeValidationError is the validation error returned by
// JobVolume.Validate if the designated constraints aren't met.
type JobVolumeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e JobVolumeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e JobVolumeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e JobVolumeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e JobVolumeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e JobVolumeValidationError) ErrorName() string { return "JobVolumeValidationError" }

// Error satisfies the builtin error interface
func (e JobVolumeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sJobVolume.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = JobVolumeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = JobVolumeValidationError{}

// Validate checks the field values on ListJobVolumes with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ListJobVolumes) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListJobVolumes with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ListJobVolumesMultiError,
// or nil if none found.
func (m *ListJobVolumes) ValidateAll() error {
	return m.validate(true)
}

func (m *ListJobVolumes) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetVolumes() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListJobVolumesValidationError{
						field:  fmt.Sprintf("Volumes[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListJobVolumesValidationError{
						field:  fmt.Sprintf("Volumes[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListJobVolumesValidationError{
					field:  fmt.Sprintf("Volumes[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListJobVolumesMultiError(errors)
	}

	return nil
}

// ListJobVolumesMultiError is an error wrapping multiple validation errors
// returned by ListJobVolumes.ValidateAll() if the designated constraints
// aren't met.
type ListJobVolumesMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListJobVolumesMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListJobVolumesMultiError) AllErrors() []error { return m }

// ListJobVolumesValidationError is the validation error returned by
// ListJobVolumes.Validate if the designated constraints aren't met.
type ListJobVolumesValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListJobVolumesValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListJobVolumesValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListJobVolumesValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListJobVolumesValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListJobVolumesValidationError) ErrorName() string { return "ListJobVolumesValidationError" }

// Error satisfies the builtin error interface
func (e ListJobVolumesValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListJobVolumes.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListJobVolumesValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListJobVolumesValidationError{}

// Validate checks the field values on StopJobTaskRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StopJobTaskRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StopJobTaskRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StopJobTaskRequestMultiError, or nil if none found.
func (m *StopJobTaskRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *StopJobTaskRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for WorkspaceName

	// no validation rules for JobName

	// no validation rules for TaskID

	// no validation rules for PodName

	if len(errors) > 0 {
		return StopJobTaskRequestMultiError(errors)
	}

	return nil
}

// StopJobTaskRequestMultiError is an error wrapping multiple validation errors
// returned by StopJobTaskRequest.ValidateAll() if the designated constraints
// aren't met.
type StopJobTaskRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StopJobTaskRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StopJobTaskRequestMultiError) AllErrors() []error { return m }

// StopJobTaskRequestValidationError is the validation error returned by
// StopJobTaskRequest.Validate if the designated constraints aren't met.
type StopJobTaskRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StopJobTaskRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StopJobTaskRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StopJobTaskRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StopJobTaskRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StopJobTaskRequestValidationError) ErrorName() string {
	return "StopJobTaskRequestValidationError"
}

// Error satisfies the builtin error interface
func (e StopJobTaskRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStopJobTaskRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StopJobTaskRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StopJobTaskRequestValidationError{}

// Validate checks the field values on RedeployJobRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RedeployJobRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RedeployJobRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RedeployJobRequestMultiError, or nil if none found.
func (m *RedeployJobRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *RedeployJobRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for WorkspaceName

	// no validation rules for JobName

	if len(errors) > 0 {
		return RedeployJobRequestMultiError(errors)
	}

	return nil
}

// RedeployJobRequestMultiError is an error wrapping multiple validation errors
// returned by RedeployJobRequest.ValidateAll() if the designated constraints
// aren't met.
type RedeployJobRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RedeployJobRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RedeployJobRequestMultiError) AllErrors() []error { return m }

// RedeployJobRequestValidationError is the validation error returned by
// RedeployJobRequest.Validate if the designated constraints aren't met.
type RedeployJobRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RedeployJobRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RedeployJobRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RedeployJobRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RedeployJobRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RedeployJobRequestValidationError) ErrorName() string {
	return "RedeployJobRequestValidationError"
}

// Error satisfies the builtin error interface
func (e RedeployJobRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRedeployJobRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RedeployJobRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RedeployJobRequestValidationError{}

// Validate checks the field values on SourceCode_CloudFsSource with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SourceCode_CloudFsSource) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SourceCode_CloudFsSource with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SourceCode_CloudFsSourceMultiError, or nil if none found.
func (m *SourceCode_CloudFsSource) ValidateAll() error {
	return m.validate(true)
}

func (m *SourceCode_CloudFsSource) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for VolumeName

	// no validation rules for SubPath

	if len(errors) > 0 {
		return SourceCode_CloudFsSourceMultiError(errors)
	}

	return nil
}

// SourceCode_CloudFsSourceMultiError is an error wrapping multiple validation
// errors returned by SourceCode_CloudFsSource.ValidateAll() if the designated
// constraints aren't met.
type SourceCode_CloudFsSourceMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SourceCode_CloudFsSourceMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SourceCode_CloudFsSourceMultiError) AllErrors() []error { return m }

// SourceCode_CloudFsSourceValidationError is the validation error returned by
// SourceCode_CloudFsSource.Validate if the designated constraints aren't met.
type SourceCode_CloudFsSourceValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SourceCode_CloudFsSourceValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SourceCode_CloudFsSourceValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SourceCode_CloudFsSourceValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SourceCode_CloudFsSourceValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SourceCode_CloudFsSourceValidationError) ErrorName() string {
	return "SourceCode_CloudFsSourceValidationError"
}

// Error satisfies the builtin error interface
func (e SourceCode_CloudFsSourceValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSourceCode_CloudFsSource.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SourceCode_CloudFsSourceValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SourceCode_CloudFsSourceValidationError{}

// Validate checks the field values on SourceCode_GitSource with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SourceCode_GitSource) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SourceCode_GitSource with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SourceCode_GitSourceMultiError, or nil if none found.
func (m *SourceCode_GitSource) ValidateAll() error {
	return m.validate(true)
}

func (m *SourceCode_GitSource) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Url

	// no validation rules for Branch

	if len(errors) > 0 {
		return SourceCode_GitSourceMultiError(errors)
	}

	return nil
}

// SourceCode_GitSourceMultiError is an error wrapping multiple validation
// errors returned by SourceCode_GitSource.ValidateAll() if the designated
// constraints aren't met.
type SourceCode_GitSourceMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SourceCode_GitSourceMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SourceCode_GitSourceMultiError) AllErrors() []error { return m }

// SourceCode_GitSourceValidationError is the validation error returned by
// SourceCode_GitSource.Validate if the designated constraints aren't met.
type SourceCode_GitSourceValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SourceCode_GitSourceValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SourceCode_GitSourceValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SourceCode_GitSourceValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SourceCode_GitSourceValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SourceCode_GitSourceValidationError) ErrorName() string {
	return "SourceCode_GitSourceValidationError"
}

// Error satisfies the builtin error interface
func (e SourceCode_GitSourceValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSourceCode_GitSource.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SourceCode_GitSourceValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SourceCode_GitSourceValidationError{}

// Validate checks the field values on SourceCode_CubeFsSource with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SourceCode_CubeFsSource) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SourceCode_CubeFsSource with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SourceCode_CubeFsSourceMultiError, or nil if none found.
func (m *SourceCode_CubeFsSource) ValidateAll() error {
	return m.validate(true)
}

func (m *SourceCode_CubeFsSource) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for VolumeName

	if len(errors) > 0 {
		return SourceCode_CubeFsSourceMultiError(errors)
	}

	return nil
}

// SourceCode_CubeFsSourceMultiError is an error wrapping multiple validation
// errors returned by SourceCode_CubeFsSource.ValidateAll() if the designated
// constraints aren't met.
type SourceCode_CubeFsSourceMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SourceCode_CubeFsSourceMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SourceCode_CubeFsSourceMultiError) AllErrors() []error { return m }

// SourceCode_CubeFsSourceValidationError is the validation error returned by
// SourceCode_CubeFsSource.Validate if the designated constraints aren't met.
type SourceCode_CubeFsSourceValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SourceCode_CubeFsSourceValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SourceCode_CubeFsSourceValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SourceCode_CubeFsSourceValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SourceCode_CubeFsSourceValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SourceCode_CubeFsSourceValidationError) ErrorName() string {
	return "SourceCode_CubeFsSourceValidationError"
}

// Error satisfies the builtin error interface
func (e SourceCode_CubeFsSourceValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSourceCode_CubeFsSource.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SourceCode_CubeFsSourceValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SourceCode_CubeFsSourceValidationError{}
