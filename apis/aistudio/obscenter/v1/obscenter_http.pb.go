// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.3
// - protoc             v5.29.3
// source: aistudio/obscenter/v1/obscenter.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationObsCenterServiceCreateOrUpdateInspectionRule = "/apis.aistudio.obscenter.v1.ObsCenterService/CreateOrUpdateInspectionRule"
const OperationObsCenterServiceDeleteInspectionRule = "/apis.aistudio.obscenter.v1.ObsCenterService/DeleteInspectionRule"
const OperationObsCenterServiceGetDeploymentGroupInsight = "/apis.aistudio.obscenter.v1.ObsCenterService/GetDeploymentGroupInsight"
const OperationObsCenterServiceGetNodeInsight = "/apis.aistudio.obscenter.v1.ObsCenterService/GetNodeInsight"
const OperationObsCenterServiceGetPodInsight = "/apis.aistudio.obscenter.v1.ObsCenterService/GetPodInsight"
const OperationObsCenterServiceGetPodRequestExceptionCount = "/apis.aistudio.obscenter.v1.ObsCenterService/GetPodRequestExceptionCount"
const OperationObsCenterServiceListApplicationsInsight = "/apis.aistudio.obscenter.v1.ObsCenterService/ListApplicationsInsight"
const OperationObsCenterServiceListInspectionRule = "/apis.aistudio.obscenter.v1.ObsCenterService/ListInspectionRule"
const OperationObsCenterServiceListNodesInsight = "/apis.aistudio.obscenter.v1.ObsCenterService/ListNodesInsight"

type ObsCenterServiceHTTPServer interface {
	CreateOrUpdateInspectionRule(context.Context, *CreateOrUpdateInspectionRuleRequest) (*emptypb.Empty, error)
	DeleteInspectionRule(context.Context, *DeleteInspectionRuleRequest) (*emptypb.Empty, error)
	// GetDeploymentGroupInsight 获取部署组详情
	GetDeploymentGroupInsight(context.Context, *GetDeploymentGroupInsightRequest) (*GetDeploymentGroupInsightResponse, error)
	// GetNodeInsight 获取节点详情
	GetNodeInsight(context.Context, *GetNodeInsightRequest) (*GetNodeInsightResponse, error)
	// GetPodInsight 获取实例详情
	GetPodInsight(context.Context, *GetPodInsightRequest) (*GetPodInsightResponse, error)
	GetPodRequestExceptionCount(context.Context, *GetPodRequestExceptionCountRequest) (*GetPodRequestExceptionCountResponse, error)
	// ListApplicationsInsight 获取应用概览
	ListApplicationsInsight(context.Context, *ListApplicationsInsightRequest) (*ListApplicationsInsightResponse, error)
	ListInspectionRule(context.Context, *ListInspectionRuleRequest) (*ListInspectionRuleResponse, error)
	// ListNodesInsight 获取节点概览
	ListNodesInsight(context.Context, *ListNodesInsightRequest) (*ListNodesInsightResponse, error)
}

func RegisterObsCenterServiceHTTPServer(s *http.Server, srv ObsCenterServiceHTTPServer) {
	r := s.Route("/")
	r.GET("/apis/v1/obs/application-insight", _ObsCenterService_ListApplicationsInsight0_HTTP_Handler(srv))
	r.GET("/apis/v1/obs/deployment-group-insight", _ObsCenterService_GetDeploymentGroupInsight0_HTTP_Handler(srv))
	r.POST("/apis/v1/obs/inspection-rule", _ObsCenterService_CreateOrUpdateInspectionRule0_HTTP_Handler(srv))
	r.GET("/apis/v1/obs/inspection-rules", _ObsCenterService_ListInspectionRule0_HTTP_Handler(srv))
	r.POST("/apis/v1/obs/inspection-rule/delete", _ObsCenterService_DeleteInspectionRule0_HTTP_Handler(srv))
	r.GET("/apis/v1/obs/nodes-insight", _ObsCenterService_ListNodesInsight0_HTTP_Handler(srv))
	r.GET("/apis/v1/obs/node-insight", _ObsCenterService_GetNodeInsight0_HTTP_Handler(srv))
	r.GET("/apis/v1/obs/pod-insight", _ObsCenterService_GetPodInsight0_HTTP_Handler(srv))
	r.GET("/apis/v1/obs/pod-exception", _ObsCenterService_GetPodRequestExceptionCount0_HTTP_Handler(srv))
}

func _ObsCenterService_ListApplicationsInsight0_HTTP_Handler(srv ObsCenterServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListApplicationsInsightRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationObsCenterServiceListApplicationsInsight)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListApplicationsInsight(ctx, req.(*ListApplicationsInsightRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListApplicationsInsightResponse)
		return ctx.Result(200, reply)
	}
}

func _ObsCenterService_GetDeploymentGroupInsight0_HTTP_Handler(srv ObsCenterServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetDeploymentGroupInsightRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationObsCenterServiceGetDeploymentGroupInsight)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetDeploymentGroupInsight(ctx, req.(*GetDeploymentGroupInsightRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetDeploymentGroupInsightResponse)
		return ctx.Result(200, reply)
	}
}

func _ObsCenterService_CreateOrUpdateInspectionRule0_HTTP_Handler(srv ObsCenterServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateOrUpdateInspectionRuleRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationObsCenterServiceCreateOrUpdateInspectionRule)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateOrUpdateInspectionRule(ctx, req.(*CreateOrUpdateInspectionRuleRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _ObsCenterService_ListInspectionRule0_HTTP_Handler(srv ObsCenterServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListInspectionRuleRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationObsCenterServiceListInspectionRule)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListInspectionRule(ctx, req.(*ListInspectionRuleRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListInspectionRuleResponse)
		return ctx.Result(200, reply)
	}
}

func _ObsCenterService_DeleteInspectionRule0_HTTP_Handler(srv ObsCenterServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteInspectionRuleRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationObsCenterServiceDeleteInspectionRule)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteInspectionRule(ctx, req.(*DeleteInspectionRuleRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _ObsCenterService_ListNodesInsight0_HTTP_Handler(srv ObsCenterServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListNodesInsightRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationObsCenterServiceListNodesInsight)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListNodesInsight(ctx, req.(*ListNodesInsightRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListNodesInsightResponse)
		return ctx.Result(200, reply)
	}
}

func _ObsCenterService_GetNodeInsight0_HTTP_Handler(srv ObsCenterServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetNodeInsightRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationObsCenterServiceGetNodeInsight)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetNodeInsight(ctx, req.(*GetNodeInsightRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetNodeInsightResponse)
		return ctx.Result(200, reply)
	}
}

func _ObsCenterService_GetPodInsight0_HTTP_Handler(srv ObsCenterServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetPodInsightRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationObsCenterServiceGetPodInsight)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetPodInsight(ctx, req.(*GetPodInsightRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetPodInsightResponse)
		return ctx.Result(200, reply)
	}
}

func _ObsCenterService_GetPodRequestExceptionCount0_HTTP_Handler(srv ObsCenterServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetPodRequestExceptionCountRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationObsCenterServiceGetPodRequestExceptionCount)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetPodRequestExceptionCount(ctx, req.(*GetPodRequestExceptionCountRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetPodRequestExceptionCountResponse)
		return ctx.Result(200, reply)
	}
}

type ObsCenterServiceHTTPClient interface {
	CreateOrUpdateInspectionRule(ctx context.Context, req *CreateOrUpdateInspectionRuleRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	DeleteInspectionRule(ctx context.Context, req *DeleteInspectionRuleRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	GetDeploymentGroupInsight(ctx context.Context, req *GetDeploymentGroupInsightRequest, opts ...http.CallOption) (rsp *GetDeploymentGroupInsightResponse, err error)
	GetNodeInsight(ctx context.Context, req *GetNodeInsightRequest, opts ...http.CallOption) (rsp *GetNodeInsightResponse, err error)
	GetPodInsight(ctx context.Context, req *GetPodInsightRequest, opts ...http.CallOption) (rsp *GetPodInsightResponse, err error)
	GetPodRequestExceptionCount(ctx context.Context, req *GetPodRequestExceptionCountRequest, opts ...http.CallOption) (rsp *GetPodRequestExceptionCountResponse, err error)
	ListApplicationsInsight(ctx context.Context, req *ListApplicationsInsightRequest, opts ...http.CallOption) (rsp *ListApplicationsInsightResponse, err error)
	ListInspectionRule(ctx context.Context, req *ListInspectionRuleRequest, opts ...http.CallOption) (rsp *ListInspectionRuleResponse, err error)
	ListNodesInsight(ctx context.Context, req *ListNodesInsightRequest, opts ...http.CallOption) (rsp *ListNodesInsightResponse, err error)
}

type ObsCenterServiceHTTPClientImpl struct {
	cc *http.Client
}

func NewObsCenterServiceHTTPClient(client *http.Client) ObsCenterServiceHTTPClient {
	return &ObsCenterServiceHTTPClientImpl{client}
}

func (c *ObsCenterServiceHTTPClientImpl) CreateOrUpdateInspectionRule(ctx context.Context, in *CreateOrUpdateInspectionRuleRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/apis/v1/obs/inspection-rule"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationObsCenterServiceCreateOrUpdateInspectionRule))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ObsCenterServiceHTTPClientImpl) DeleteInspectionRule(ctx context.Context, in *DeleteInspectionRuleRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/apis/v1/obs/inspection-rule/delete"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationObsCenterServiceDeleteInspectionRule))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ObsCenterServiceHTTPClientImpl) GetDeploymentGroupInsight(ctx context.Context, in *GetDeploymentGroupInsightRequest, opts ...http.CallOption) (*GetDeploymentGroupInsightResponse, error) {
	var out GetDeploymentGroupInsightResponse
	pattern := "/apis/v1/obs/deployment-group-insight"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationObsCenterServiceGetDeploymentGroupInsight))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ObsCenterServiceHTTPClientImpl) GetNodeInsight(ctx context.Context, in *GetNodeInsightRequest, opts ...http.CallOption) (*GetNodeInsightResponse, error) {
	var out GetNodeInsightResponse
	pattern := "/apis/v1/obs/node-insight"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationObsCenterServiceGetNodeInsight))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ObsCenterServiceHTTPClientImpl) GetPodInsight(ctx context.Context, in *GetPodInsightRequest, opts ...http.CallOption) (*GetPodInsightResponse, error) {
	var out GetPodInsightResponse
	pattern := "/apis/v1/obs/pod-insight"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationObsCenterServiceGetPodInsight))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ObsCenterServiceHTTPClientImpl) GetPodRequestExceptionCount(ctx context.Context, in *GetPodRequestExceptionCountRequest, opts ...http.CallOption) (*GetPodRequestExceptionCountResponse, error) {
	var out GetPodRequestExceptionCountResponse
	pattern := "/apis/v1/obs/pod-exception"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationObsCenterServiceGetPodRequestExceptionCount))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ObsCenterServiceHTTPClientImpl) ListApplicationsInsight(ctx context.Context, in *ListApplicationsInsightRequest, opts ...http.CallOption) (*ListApplicationsInsightResponse, error) {
	var out ListApplicationsInsightResponse
	pattern := "/apis/v1/obs/application-insight"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationObsCenterServiceListApplicationsInsight))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ObsCenterServiceHTTPClientImpl) ListInspectionRule(ctx context.Context, in *ListInspectionRuleRequest, opts ...http.CallOption) (*ListInspectionRuleResponse, error) {
	var out ListInspectionRuleResponse
	pattern := "/apis/v1/obs/inspection-rules"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationObsCenterServiceListInspectionRule))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ObsCenterServiceHTTPClientImpl) ListNodesInsight(ctx context.Context, in *ListNodesInsightRequest, opts ...http.CallOption) (*ListNodesInsightResponse, error) {
	var out ListNodesInsightResponse
	pattern := "/apis/v1/obs/nodes-insight"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationObsCenterServiceListNodesInsight))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
