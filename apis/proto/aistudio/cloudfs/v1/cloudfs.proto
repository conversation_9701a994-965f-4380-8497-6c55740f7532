syntax = "proto3";

package apis.aistudio.cloudFs.v1;
option go_package = "git.lianjia.com/cloudnative/kic/kic-platform/apis/aistudio/cloudfs/v1;v1";

import "google/api/annotations.proto";
import "google/protobuf/duration.proto";
import "common/common.proto";
import "google/protobuf/empty.proto";

// AIStudio层, 底层创建fluid的总dataset
service CloudFSService {
  rpc CreateVolume(CreateVolumeRequest) returns (Volume) {
    option (google.api.http) = {
      post: "/apis/v1/workspace/{workspaceName}/cloudfs/volume"
      body: "*"
    };
  }
  rpc UpdateVolume(UpdateVolumeRequest) returns (Volume) {
    option (google.api.http) = {
      put: "/apis/v1/workspace/{workspaceName}/cloudfs/volume/{name}"
      body: "*"
    };
  }
  rpc GetVolume(GetVolumeRequest) returns (Volume) {
    option (google.api.http) = {
      get: "/apis/v1/workspace/{workspaceName}/cloudfs/volume/{name}"
    };
  }
  rpc ListVolumes(ListVolumeOptions) returns (ListVolumeResult) {
    option (google.api.http) = {
      get: "/apis/v1/workspace/{workspaceName}/cloudfs/volume"
    };
  }

  rpc DeleteVolume(DeleteVolumeRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/apis/v1/workspace/{workspaceName}/cloudfs/volume/{name}"
    };
  }


  rpc GetVolumeMeta(GetVolumeMetaRequest) returns(GetVolumeMetaResponse) {
    option (google.api.http) = {
      get: "/apis/v1/workspace/{workspaceName}/cloudfs/volume/{volumeName}/meta"
    };
  }


}


message GetVolumeMetaRequest {
  string volumeName = 1;
  string workspaceName = 2;
}

message GetVolumeMetaResponse {
  string formatName = 1;
  string metaUrl = 2;
  string storage = 3;
  string bucket = 4;
  string accessKey = 5;
  string secretKey = 6;
}

message Volume {
  string workspaceName = 1;
  string name = 2;
  string description = 3;
  string region = 4; // 区域
  string zone = 5; //分区
  string displayName = 6; // 显示名称
  common.TimestampModel timestamp = 7; //时间
  string id = 8;
  string creator = 9;
  string state = 10;
  VolumeLimit volumeLimit = 11; //卷限制
}

message VolumeLimit {
  message VolumeQuotaLimit {
    uint64 capacity = 1; //容量,单位为GiB
    uint64 inodes = 2; //文件数量, 默认为1亿
  }
  uint64 uploadLimit = 1; //上传限制,单位为MiB
  uint64 downloadLimit = 2; //下载限制,单位为MiB
  uint32 trashDays = 3; // TrashDays 回收站保留天数
  VolumeQuotaLimit quotaLimit = 4; // 配额限制
}


message CreateVolumeRequest {
  string workspaceName = 1;
  string name = 2;
  string description = 3;
  string zone = 4; // 区域
  string region = 5; // 地域
  string displayName = 6; // 显示名称
  string creator = 7;
  bool readOnly = 8;
  VolumeLimit volumeLimit = 9; //卷限制
}

message UpdateVolumeRequest{
  string workspaceName = 1;
  string name = 2;
  string description = 3;
  string displayName = 4;
  bool readOnly = 5;
  string region = 6;
  string zone = 7;
  VolumeLimit volumeLimit = 8; //卷限制
}

message GetVolumeRequest {
  string workspaceName = 1;
  string name = 2;
}


message ListVolumeOptions {
  string region = 1;
  string zone = 2;
  string workspaceName = 3;
}

message ListVolumeResult {
  repeated Volume items = 1;
  int32 pageSize = 2;
  int32 pageNo = 3;
  int32 total = 4;
}

message DeleteVolumeRequest {
  string name = 1;
  string workspaceName = 2;
}
