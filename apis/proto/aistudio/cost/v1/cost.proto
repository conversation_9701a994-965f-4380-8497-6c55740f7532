syntax = "proto3";

package apis.aistudio.cost.v1;

import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "common/common.proto";
import "validate/validate.proto";


option go_package = "git.lianjia.com/cloudnative/kic/kic-platform/apis/aistudio/cost/v1;v1";


//CostService成本计费服务
service CostService {
  //CreateProductSpecification 创建产品规格
  rpc CreateProductSpecification(CreateOrUpdateProductSpecificationRequest) returns(google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/apis/v1/product_specification",
      body: "*"
    };
  }

  //UpdateProductSpecification 更新产品规格
  rpc UpdateProductSpecification(CreateOrUpdateProductSpecificationRequest) returns(google.protobuf.Empty) {
    option (google.api.http) = {
      put: "/apis/v1/product_specification",
      body: "*"
    };
  }

  //DeleteProductSpecification 删除产品规格
  rpc DeleteProductSpecification(DeleteProductSpecificationRequest) returns (google.protobuf.Empty){
    option (google.api.http) = {
      delete: "/apis/v1/product_specification"
    };
  }

  //GetProductSpecification 获取产品规格
  rpc GetProductSpecification(GetProductSpecificationRequest) returns (ProductSpecification){
    option (google.api.http) = {
      get: "/apis/v1/product_specification"
    };
  }

  //ListProductSpecifications 获取产品规格列表
  rpc ListProductSpecifications(ListProductSpecificationsOption) returns (ListProductSpecificationsResult){
    option (google.api.http) = {
      get: "/apis/v1/product_specifications"
    };
  }

  //ListProductSpecificationsCostDetail 获取产品规格成本明细
  rpc ListProductSpecificationsCostDetail(ListProductSpecificationsCostDetailOption) returns (ListProductSpecificationsCostDetailResult){
    option (google.api.http) = {
      get: "/apis/v1/product_specifications/cost_detail"
    };
  }

  //ListProductSpecificationsUsagesForKcsCost KcsCost服务专用获取产品规格用量数据
  rpc ListProductSpecificationsUsagesForKcsCost(ListProductSpecificationsUsagesForKcsCostOption) returns (ListProductSpecificationsUsagesForKcsCostResult){
    option (google.api.http) = {
      get: "/apis/v1/product-specifications/usages-for-kcs-cost"
    };
  }

  //GetProductSpecificationsTotalCost 获取产品规格总成本
  rpc GetProductSpecificationsTotalCost(ListProductSpecificationsCostDetailOption) returns (GetProductSpecificationsTotalCostResult){
    option (google.api.http) = {
      get: "/apis/v1/product_specifications/total_cost"
    };
  }

  //ListResourceStatisticsUsages 获取资源用量
  rpc ListResourceStatisticsUsages(ListResourceStatisticsUsagesOption) returns (ListResourceStatisticsUsagesResult){
    option (google.api.http) = {
      get: "/apis/v1/resource_statistics/usages"
    };
  }

}

message ListProductSpecificationsUsagesForKcsCostOption {
  string timePeriod = 1; // 时间周期，支持today, yesterday, lastmonth...
  string serviceSpecificationName = 2;
  int32 currentPage = 3 [json_name = "current_page"];
  int32 pageSize = 4 [json_name = "page_size"];
}

message ListProductSpecificationsUsagesForKcsCostResult {
  int32 code =1;
  string message = 2;
  KcsCostServiceUsageData data = 3;
}

message KcsCostServiceUsageData {
  int32 currentPage = 3 [json_name = "current_page"];
  int32 pageSize = 4 [json_name = "page_size"];
  int32 totalCount = 5 [json_name = "total_count"];
  repeated KcsCostServiceUsage list = 6;
}

message KcsCostServiceUsage {
  string serviceUsageId = 1 [json_name = "service_usage_id"];
  string serviceUsageIdDescription =2 [json_name = "service_usage_id_description"];
  string owner = 3;
  float usage = 4;
}


message GetProductSpecificationsTotalCostResult {
  double totalCost = 1;
}

message ListProductSpecificationsOption {
  int32 page = 1;
  int32 pageSize = 2;
}

message ListProductSpecificationsResult {
  int64 total = 1;
  int32 page = 2;
  int32 pageSize = 3;
  repeated ProductSpecification productSpecifications = 4;
}

message ProductSpecification {
  string id = 1 [(validate.rules).string.min_len = 1, (validate.rules).string.max_len = 31];
  string productName = 2 [(validate.rules).string.min_len = 1, (validate.rules).string.max_len = 63];
  string productNameCn = 3;
  string serviceSpecificationName = 4 [(validate.rules).string.min_len = 1, (validate.rules).string.max_len = 63];
  string serviceSpecificationNameCn = 5;
  float  price = 6;
  string priceUnit = 7;
  PriceType priceType =8;
  UsageType usageType = 9;
  BillingMode billingMode = 10;
  BillingCycle billingCycle = 11;
  string specificationExplanation = 12;
  bool isEnabled = 13;  // 默认为true
  string createTime = 14; //创建时间
  string updateTime = 15; //更新时间
  EffectiveResource effectiveResource = 16;
  string effectiveTimestamp = 17; //生效时间戳
}

message EffectiveResource {
  string resourceType = 1;
  repeated string resourceSpecification = 2;
  repeated string extensionField1Values = 3;
  repeated string extensionField2Values = 4;
  repeated string extensionField3Values = 5;
}

message CreateOrUpdateProductSpecificationRequest {
  string productName = 1 [(validate.rules).string.min_len = 1, (validate.rules).string.max_len = 63];
  string productNameCn = 2;
  string serviceSpecificationName =3 [(validate.rules).string.min_len = 1, (validate.rules).string.max_len = 63];
  string serviceSpecificationNameCn = 4;
  float  price = 5;
  string priceUnit = 6;
  PriceType priceType =7;
  UsageType usageType = 8;
  BillingMode billingMode = 9;
  BillingCycle billingCycle = 10;
  string specificationExplanation = 11;
  bool isEnabled = 12;  // 默认为true
  EffectiveResource effectiveResource = 13;
  string effectiveTimestamp = 14; //生效时间戳
}


enum PriceType {
  FixedUnitPrice = 0;
}

enum UsageType {
  Cumulative = 0;
  Count = 1;
}


enum BillingMode {
  UsageBilling = 0;
}

enum BillingCycle {
  Hourly = 0;
}


message DeleteProductSpecificationRequest {
  string productName = 1 [(validate.rules).string.min_len = 1, (validate.rules).string.max_len = 63];
  string serviceSpecificationName =2 [(validate.rules).string.min_len = 1, (validate.rules).string.max_len = 63];
}

message GetProductSpecificationRequest {
  string productName = 1 [(validate.rules).string.min_len = 1, (validate.rules).string.max_len = 63];
  string serviceSpecificationName =2 [(validate.rules).string.min_len = 1, (validate.rules).string.max_len = 63];
}


message ListProductSpecificationsCostDetailOption {
  string serviceSpecificationName = 1;
  WindowType windowType = 2;
  int32 page = 3;
  int32 pageSize = 4;
  string workspaceName = 5;
  string resourceId = 6;
  string admin = 7;
  string dayTimestampRange = 8;
  string monthTimestampRange= 9;
  string resourceType = 10;
  string resourceSpecification = 11;
}

enum WindowType {
  Day = 0;
  Month = 1;
}

message ListProductSpecificationsCostDetailResult {
  int64 total = 1 ;
  int32 page = 2;
  int32 pageSize = 3;
  repeated ProductSpecificationCostDetail productSpecificationCostDetails = 4;
}

message ProductSpecificationCostDetail {
  string productNameCn = 1;
  string serviceSpecificationNameCn = 2;
  string billingMode = 3;
  string region = 4;
  string workspace =5;
  float usage = 6;
  string usageUnit = 7;
  float  price = 8;
  string priceUnit = 9;
  string resourceIdExplanation = 10;
  double cost = 11;
  string resourceId = 12;
  string admin = 13;
  string resourceSpecification = 14; // 资源规格
  string resourceType= 15;
}


message ListResourceStatisticsUsagesOption {
  int32 page = 1;
  int32 pageSize = 2;
  string workspaceName =3;
  string resourceType = 4;
  string resourceSpecification = 5;
  string timestampRange = 6;
  string resourceId = 7;
  string admin = 8;
}

message ListResourceStatisticsUsagesResult {
  int64 total = 1 ;
  int32 page = 2;
  int32 pageSize = 3;
  repeated ResourceUsage resourceUsages = 4;
}

message ResourceUsage {
  string resourceId = 1;
  string resourceName =2;
  string region = 3;
  string workspace = 4;
  float usage = 5;
  string resourceSpecification = 6; // 资源规格
  string resourceType= 7;
  string admin = 8;
  string resourceIdExplanation = 9;
  string usageUnit = 10;
  string extensionField1 = 11;    //资源扩展说明字段1
  string extensionField2 = 12;    //资源扩展说明字段2
  string extensionField3 = 13;    //资源扩展说明字段3
}