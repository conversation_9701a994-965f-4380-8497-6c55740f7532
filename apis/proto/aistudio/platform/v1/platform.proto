syntax = "proto3";

package apis.aistudio.platform.v1;

import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "common/common.proto";
import "validate/validate.proto";


option go_package = "git.lianjia.com/cloudnative/kic/kic-platform/apis/aistudio/platform/v1;v1";

// 平台设置接口
service PlatformService {

  // 新增或者更新Kubernetes的集群信息
  rpc AddOrUpdateKubernetesCluster(CreateKubernetesClusterRequest) returns (KubernetesCluster) {
    option (google.api.http) = {
      put: "/apis/v1/platform/settings/k8s-cluster"
      body: "*"
      additional_bindings {
        post: "/apis/v1/k8s-cluster/{name}"
        body: "*"
      }
    };
  }

  rpc RemoveKubernetesCluster(RemoveKubernetesClusterRequest) returns (KubernetesCluster) {
    option (google.api.http) = {
      delete: "/apis/v1/platform/settings/k8s-cluster/{name}"
    };
  }


  rpc ListKubernetesClusters(ListKubernetesClustersOptions) returns (ListKubernetesClustersResponse) {
    option (google.api.http) = {
      get: "/apis/v1/platform/settings/k8s-cluster"
    };
  }


  rpc GetKubernetesCluster(GetKubernetesClusterRequest) returns(KubernetesCluster) {
    option (google.api.http) = {
      get: "/apis/v1/platform/settings/k8s-cluster/{name}"
    };
  }

  // 新增节点规格
  rpc AddOrUpdateNodeSpecification(NodeSpecification) returns (NodeSpecification) {
    option (google.api.http) = {
      put: "/apis/v1/platform/settings/node-specifications"
      body: "*"
      additional_bindings {
        put: "/apis/v1/platform/settings/node-specifications/{name}"
        body: "*"
      }
    };
  }

  // 删除节点规格
  rpc DeleteNodeSpecification(DeleteNodeSpecificationRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/apis/v1/platform/settings/node-specifications/{name}"
    };
  }

  // 查询所有的节点规格
  rpc ListNodeSpecifications(ListNodeSpecificationOptions) returns (ListNodeSpecificationsResult) {
    option (google.api.http) = {
      get: "/apis/v1/platform/settings/node-specifications"
    };
  }


  rpc AddOrUpdateCubeFSCluster(AddOrUpdateCubeFSClusterRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      put: "/apis/v1/platform/settings/cubefs-cluster"
      body: "*"
      additional_bindings {
        post: "/apis/v1/platform/settings/cubefs-cluster/{name}"
        body: "*"
      }
    };
  }

  rpc DeleteCubeFSCluster(DeleteCubeFSClusterRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/apis/v1/platform/settings/cubefs-cluster/{name}"
    };
  }

  rpc CubeFSClusterUpdateStatus(CubeFSClusterSetStatusRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/apis/v1/platform/settings/cubefs-cluster/{name}/status/{status}"
      body: "*"
    };
  }


  rpc SetCubeFSDashboard(SetCubeFSDashboardRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      put: "/apis/v1/platform/settings/cubefs-dashboard"
      body: "*"
      additional_bindings {
        post: "/apis/v1/platform/settings/cubefs-dashboard"
        body: "*"
      }
    };
  }

  // 展示所有节点
  rpc ListNodes(ListNodesOptions) returns (ListNodesResult) {
    option (google.api.http) = {
      get: "/apis/v1/platform/settings/nodes"
    };
  }

  // 节点信息统计
  rpc GetNodesInfoStatistics(GetNodesInfoStatisticsRequest) returns (NodesInfoStatisticsResponse) {
    option (google.api.http) = {
      get: "/apis/v1/platform/setting/nodes/statistics"
    };
  }

  // 节点分配和回收
  rpc AssignNode(AssignNodeRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/apis/v1/platform/settings/nodes/assign"
      body: "*"
    };
  }

  rpc RecycleNode(RecycleNodeRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/apis/v1/platform/settings/nodes/recycle"
      body: "*"
    };
  }

  //添加Dragonfly配置
  rpc AddDragonfly(AddDragonflyRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/apis/v1/platform/settings/dragonfly"
      body: "*"
    };
  }
  //更新Dragonfly配置
  rpc SetDragonfly(SetDragonflyRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      put: "/apis/v1/platform/settings/dragonfly"
      body: "*"
    };
  }
  //删除Dragonfly配置
  rpc DeleteDragonfly(DeleteDragonflyRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/apis/v1/platform/settings/dragonfly"
    };
  }
  //获取Dragonfly配置
  rpc ListDragonfly(ListDragonflyRequest) returns (ListDragonflyResponse) {
    option (google.api.http) = {
      get: "/apis/v1/platform/settings/dragonfly"
    };
  }
  // 获取分布式缓存注册表列表
  rpc ListDistributedCacheRegistrations(ListDistributedCacheRegistrationsRequest) returns(ListDistributedCacheRegistrationsResponse) {
    option (google.api.http) = {
      get: "/apis/v1/platform/settings/distributedcacheregistrations"
    };
  }
  // 创建分布式缓存注册表
  rpc CreateDistributedCacheRegistration(CreateDistributedCacheRegistrationRequest) returns(CreateDistributedCacheRegistrationResponse) {
    option (google.api.http) = {
      post: "/apis/v1/platform/settings/distributedcacheregistration"
      body: "*"
    };
  }
  // 删除分布式缓存注册表
  rpc DeleteDistributedCacheRegistration(DeleteDistributedCacheRegistrationRequest) returns(DeleteDistributedCacheRegistrationResponse) {
    option (google.api.http) = {
      delete: "/apis/v1/platform/settings/distributedcacheregistration/{id}"
    };
  }
  // 更新分布式缓存注册表
  rpc UpdateDistributedCacheRegistration(UpdateDistributedCacheRegistrationRequest) returns(UpdateDistributedCacheRegistrationResponse) {
    option (google.api.http) = {
      put: "/apis/v1/platform/settings/distributedcacheregistration/{id}"
      body: "*"
    };
  }
  // 获取存储框架地区
  rpc ListDistributedCacheRegistrationRegions(ListDistributedCacheRegistrationRegionsRequest) returns(ListDistributedCacheRegistrationRegionsResponse) {
    option (google.api.http) = {
      get: "/apis/v1/platform/settings/distributedcacheregistration/regions"
    };
  }
  // 获取分布式缓存注册可用区
  rpc ListDistributedCacheRegistrationZones(ListDistributedCacheRegistrationZonesRequest) returns(ListDistributedCacheRegistrationZonesResponse) {
    option (google.api.http) = {
      get: "/apis/v1/platform/settings/distributedcacheregistration/zones"
    };
  }
  // 获取分布式缓存注册集群
  rpc ListDistributedCacheRegistrationClusters(ListDistributedCacheRegistrationClustersRequest) returns(ListDistributedCacheRegistrationClustersResponse) {
    option (google.api.http) = {
      get: "/apis/v1/platform/settings/distributedcacheregistration/clusters"
    };
  }
}

message AddOrUpdateCubeFSClusterRequest {
  string name = 1 [(validate.rules).string.min_len = 1, (validate.rules).string.max_len = 31];
  string region = 2 [(validate.rules).string = {in: ["region-bj", "region-sh"]}];
  repeated string zones = 3 [(validate.rules).repeated.items.string = {in: ["wq"]}]; // 可用区
  string description = 4[(validate.rules).string.min_len = 1, (validate.rules).string.max_len = 255];
  string displayName = 5[(validate.rules).string.min_len = 1, (validate.rules).string.max_len = 31];
  string masterAddress = 6[(validate.rules).string.min_len = 1, (validate.rules).string.max_len = 255];
  bool enabled = 7;
  string specification = 8;
}

message DeleteCubeFSClusterRequest {
  string name = 1[(validate.rules).string.min_len = 1, (validate.rules).string.max_len = 31];
}

message CubeFSClusterSetStatusRequest {
  string name = 1 [(validate.rules).string.min_len = 1, (validate.rules).string.max_len = 31];
  string status = 2 [(validate.rules).string = {in: ["enabled", "disabled"]}];
}


message SetCubeFSDashboardRequest {
  string url = 1[(validate.rules).string.min_len = 1, (validate.rules).string.max_len = 255];
  string user = 2[(validate.rules).string.min_len = 1, (validate.rules).string.max_len = 31];
  string password = 3[(validate.rules).string.min_len = 1, (validate.rules).string.max_len = 64];
}


message ListNodeSpecificationOptions {
  bool gpuEnabled = 1;
  string gpuProduct = 2;
}

message ListNodeSpecificationsResult {
  repeated NodeSpecification nodeSpecifications = 1;
}

message DeleteNodeSpecificationRequest {
  string name = 1;
}


// 注册Kubernetes的集群信息
message KubernetesCluster {
  string name = 1;
  string region = 2;
  string zone = 3;
  string idc = 4;
  string displayName = 5;
  string description = 6; //集群的描述信息
  repeated ClusterUsage usages = 7; //集群用途
  map<string,string> nodeSelector = 8; //集群的nodeSelector, 不设置表示全量节点
}


// NodeSpecification is a specification for a node with gpu or cpu
message NodeSpecification {
  string name = 1;
  string displayName = 2;
  string description = 3;
  int32 cpuNum = 4;
  int32 memory = 5; // GiB
  int32 gpuNum = 6;
  int32 gpuMemory = 7; // GiB
  int32 diskCapacity = 8; // GiB 磁盘容量
  string gpuProduct = 9; // gpu产品名称
  common.TimestampModel timestamp = 10;
}


message CubeFSCluster {
  string name = 1;
  string region = 2;
  string zone = 3;
  string description = 4;
  string idc = 5;
  string displayName = 6;

}


enum ClusterUsage {
  Training = 0;
  Inference = 1;
}


message CreateKubernetesClusterRequest {
  string name = 1;
  string region = 2;
  string zone = 3;
  string idc = 4;
  string displayName = 5;
  string description = 6; //集群的描述信息
  repeated ClusterUsage usages = 7; //集群用途
  map<string,string> nodeSelector = 8; //集群的nodeSelector, 不设置表示全量节点
}



message RemoveKubernetesClusterRequest {
  string name = 1;
}


message ListKubernetesClustersResponse {
  repeated KubernetesCluster clusters = 1;
}


message GetKubernetesClusterRequest {
  string name = 1;
}

message ListKubernetesClustersOptions {
}

message ListNodesOptions {
  string ip = 1; // 用于模糊搜索
  int32 page = 2; // 页码
  int32 pageSize = 3; // 每页数量
  string workspaceName = 4; // 所属空间
  bool assigned = 5; // 是否已分配
  string cluster = 6; // 所属集群
  string status = 7; // 节点状态
  string queueName = 8; // 队列名称
}

message Node {
  string ip = 1;
  string cluster = 2;
  string status = 3;
  string gpuProduct = 4;
  string specification = 5;
  map<string,string> labels = 6;
  map<string, string> annotations = 7;
  string workspaceName = 8;
  string nodeName = 9;
  string queueName = 10;
}

message ListNodesResult {
  repeated Node nodes = 1;
  int32 total = 2;
}

message AssignNodeRequest {
  repeated string ips = 1;
  string workspaceName = 2;
}

message RecycleNodeRequest {
  repeated string ips = 1;
}

message GetNodesInfoStatisticsRequest{

}

message NodesInfoStatisticsResponse{
  int32 nodeTotal = 1;  // 节点总数
  int32 gpuNodeTotal = 2;  // gpu节点总数
  map<string, int32> gpuMap = 3;  // 统计不同的 gpu规格 节点数量
  int32 readyNodeNum = 4;  // 正常节点
  int32 notReadyNodeNum = 5;  // 异常节点
  int32 assignedNodeNum = 6;  // 已分配节点
  int32 notAssignedNodeNum = 7;  // 未分配节点
}

message AddDragonflyRequest {
  //集群名称
  string cluster = 1;
  //地区
  string region = 2;
  //分区
  string zone = 3;
  //描述
  string description = 4;
  //地址
  string address = 5;
}
message SetDragonflyRequest {
  //id
  string id = 1;
  //集群名称
  string cluster = 2;
  //地区
  string region = 3;
  //分区
  string zone = 4;
  //描述
  string description = 5;
  //地址
  string address = 6;
}
message DeleteDragonflyRequest {
  //id
  string id = 1;
}
message ListDragonflyRequest {
  //集群名称
  string cluster = 1;
  //创建人
  string creator = 2;
  //page
  int32 page = 3;
  //pageSize
  int32 pageSize = 4;
}
message ListDragonflyResponse {
  repeated Dragonfly dragonflies = 1;
  int32 total = 2;
}
message Dragonfly {
  //id
  string id = 1;
  //集群名称
  string cluster = 2;
  //地区
  string region = 3;
  //分区
  string zone = 4;
  //描述
  string description = 5;
  //地址
  string address = 6;
  //创建人
  string creator = 7;
  //创建时间
  string createTime = 8;
  //更新时间
  string updateTime = 9;
}

message ListDistributedCacheRegistrationsRequest {
  string creator = 1;
  int32 page = 2;
  int32 pageSize = 3;
}

message ListDistributedCacheRegistrationsResponse {
  repeated DistributedCacheRegistration distributedCacheRegistrations = 3;
  int32 total = 4;
}

message DistributedCacheRegistration {
  string id = 1;
  string cacheRegion = 2;
  string cacheRegionName = 3;
  string cacheZone = 4;
  string cacheZoneName = 5;
  string creator = 6;
  string cacheFramework = 7;
  string cluster = 8;
  string clusterName = 9;
  DistributedCacheRegistrationStatus status = 10;
  bool enabled = 11;
}

message DistributedCacheRegistrationStatus {
  int64 capacity = 1;
  int64 used = 2;
}

message CreateDistributedCacheRegistrationRequest {
  string cacheRegion = 1;
  string cacheRegionName = 2;
  string cacheZone = 3;
  string cacheZoneName = 4;
  string cacheFramework = 5;
  string cluster = 6;
  string clusterName = 7;
  bool enabled = 8;
}

message CreateDistributedCacheRegistrationResponse {
}

message DeleteDistributedCacheRegistrationRequest {
  string id = 1;  
}

message DeleteDistributedCacheRegistrationResponse {
}

message UpdateDistributedCacheRegistrationRequest {
  string id = 1;
  string cacheRegion = 2;
  string cacheRegionName = 3;
  string cacheZone = 4;
  string cacheZoneName = 5;
  string cacheFramework = 6;
  string cluster = 7;
  string clusterName = 8;
  bool enabled = 9;
}

message UpdateDistributedCacheRegistrationResponse {
}

message ListDistributedCacheRegistrationRegionsRequest {
}

message ListDistributedCacheRegistrationRegionsResponse {
  repeated DistributedCacheRegistrationRegion distributedCacheRegistrationRegions = 1;
}
message DistributedCacheRegistrationRegion {
  string cacheRegion = 1;
  string cacheRegionName = 2;
}

message ListDistributedCacheRegistrationZonesRequest {
  string cacheRegion = 1;
}

message ListDistributedCacheRegistrationZonesResponse {
  repeated DistributedCacheRegistrationZone distributedCacheRegistrationZones = 1;
}

message DistributedCacheRegistrationZone {
  string cacheZone = 1;
  string cacheZoneName = 2;
}

message ListDistributedCacheRegistrationClustersRequest {
  string cacheRegion = 1;
  string cacheZone = 2;
}

message ListDistributedCacheRegistrationClustersResponse {
  repeated DistributedCacheRegistrationCluster distributedCacheRegistrationClusters = 1;
}

message DistributedCacheRegistrationCluster {
  string cluster = 1;
  string clusterName = 2;
}