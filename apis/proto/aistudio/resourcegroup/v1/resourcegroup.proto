syntax = "proto3";

package apis.aistudio.resourcegroup.v1;

import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "common/common.proto";

option go_package = "git.lianjia.com/cloudnative/kic/kic-platform/apis/resourcegroup/v1;v1";

service ResourceGroupService {
  //返回节点列表
  rpc ListNodes(ListNodeDetailOptions) returns (ListNodeDetailResult) {
    option (google.api.http) = {
      get: "/apis/v1/workspace/{workspaceName}/nodes"
    };
  }

  // 获取节点统计信息
  rpc GetNodeStatistics(GetNodeStatisticsRequest) returns (NodeStatistics) {
    option (google.api.http) = {
      get: "/apis/v1/workspace/{workspaceName}/nodes/statistics"
    };
  }

  rpc GetResourceGroupMetrics(GetResourceGroupMetricsRequest) returns(ResourceGroupMetric) {
    option (google.api.http) = {
      get: "/apis/v1/workspace/{workspaceName}/resources/metrics"
    };
  }
}

service NodeService {
  rpc GetNodeDetail(GetNodeDetailRequest) returns (NodeDetail) {
    option (google.api.http) = {
      get: "/apis/v1/workspace/{workspaceName}/node/{ip}/detail"
    };
  }

  // 为了前端的一个数据结构
  rpc ListNodeDetailForFrontend(ListNodeDetailForFrontendOptions) returns (CascadeNodeDetails) {
    option (google.api.http) = {
      get: "/apis/v1/workspace/{workspaceName}/nodes/cascade"
    };
  }

  rpc GetNodeMetricsView(GetNodeViewRequest) returns (NodeMetricView) {
    option (google.api.http) = {
      get: "/apis/v1/workspace/{workspaceName}/node/metrics/view"
    };
  }

  rpc GetNodeMetric(GetNodeMetricRequest) returns (NodeMetric) {
    option (google.api.http) = {
      get: "/apis/v1/workspace/{workspaceName}/node/{ip}/metrics"
    };
  }

  rpc DisableNode(DisableNodeRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/apis/v1/workspace/{workspaceName}/node/{ip}/disable"
      body: "*"
    };
  }

  // 获取节点上工作负载信息
  rpc GetNodeWorkloads(GetNodeWorkloadsRequest) returns (GetNodeWorkloadsResponse) {
    option (google.api.http) = {
      get: "/apis/v1/workspace/{workspaceName}/node/{ip}/workloads"
    };
  }

  // 驱逐节点上的工作负载
  rpc EvictNodeWorkloads(EvictNodeWorkloadsRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/apis/v1/workspace/{workspaceName}/node/{ip}/evict"
      body: "*"
    };
  }

  // 改变节点GPU分配模式
  rpc ChangeNodeGpuAllocationMode(ChangeNodeGpuAllocationModeRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/apis/v1/workspace/{workspaceName}/node/{ip}/gpu-mode"
      body: "*"
    };
  }
}

message ListClustersRequest{
  string workspaceName = 1;
}

message ApplyResourceResult{
  string workspaceName = 1;
  string orderId = 2; //工单号
  string region = 3; // 区域
}

// 资源申请服务
message ApplyResourceRequest {
  string workspaceName = 1;
  string region = 2; // 区域
  string  description = 3; // 描述
  repeated ApplyResource resourcesList = 4;
}

// 可申请多个不同规格
message ApplyResource {
  string nodeSpecificationId = 1;
  int32 applyNum = 2;
}


message RecyclingNodeRequest {
  repeated string ips = 1;
  string workspaceName = 2;
}

// 基础信息 展示
message NodeDetail {
  string ip = 1;
  string nodeName = 29;
  string region = 2;
  string zone = 3;
  string room = 4;
  string rack = 5; //机柜
  string idcAlias = 6; //机房简称
  string cpuNum = 7;
  string memory = 8; // GiB
  string gpuNum = 9; // gpu数量
  string gpuMemory = 10; // GiB
  string diskInfo = 11; // 磁盘信息. 例如: nvme,6400,1,0,0, 代表nvme磁盘,容量6400G,数量1,iops性能, 带宽性能
  string gpuModel = 12; // gpu型号
  string gpuProductName = 13; // gpu产品名称
  string cudaDriverVersion = 14; // cuda Driver驱动版本
  string cudaRuntimeVersion = 15; // cuda Runtime版本
  MachineType machineType = 16; // 机器类型
  string gpuMachine = 17; // gpu机器型号
  string cluster = 18; // 集群名称
  string status = 19; // 节点状态
  string displaySpec = 20; // 显示的资源规格
  string workspaceName = 21; // 工作空间名称
  string queueName = 22; // 队列名称
  string machineModel = 23; // 机器型号,IDC才有
  string nodeSpecificationName = 24; // 节点规格
  // 机器的metrics
  common.ResourceMetric cpuMetric = 25;
  common.ResourceMetric memoryMetric = 26;
  common.ResourceMetric gpuMetric = 27;
  common.ResourceMetric gpuMemMetric = 28;
  GpuAllocationMode gpuAllocationMode = 31;
}

message GetResourceGroupMetricsRequest {
  string workspaceName = 1;
  string region = 2;
  string cluster = 3;
}

message ResourceGroupMetric {
  common.ResourceMetric cpu = 1;
  common.ResourceMetric memory = 2;
  common.ResourceMetric gpu = 3;
  common.ResourceMetric gpuMem = 4;
}

// 监控信息 展示
message NodeMetric {
  string ip = 1;
  common.ResourceMetric cpu = 2;
  common.ResourceMetric memory = 3;
  common.ResourceMetric gpu = 4;
  repeated PodMetric pods = 5;
  PodCount podCount = 6;
  common.ResourceMetric gpuMem = 7;
}

message GetNodeViewRequest {
  string workspaceName = 1;
  string region = 2;
}

message Cluster{
  string clusterName = 1;
  string displayName = 2;
  string region = 3;
  string zone = 4;
  string idc = 5;
}

message ListClusterResponse {
  repeated Cluster clusterList = 1;
}

message NodeMetricView {
  repeated NodeMetric nodeMetricView = 1;
}

message GetNodeStatisticsRequest {
  string workspaceName = 1;
  string region = 2;
  string cluster = 3;
}

message NodeStatistics {
  int32 total = 1;
  int32 gpuNodeNum = 2;
  int32 readyNodeNum = 3;
  int32 notReadyNodeNum = 4;
  int32 schedulingNodeNum = 5;
  int32 notSchedulingNodeNum = 6;
}

message OwnReference {
  Kind kind = 1;
  string name = 2;
}

enum Kind {
  Training = 0;
  Inference = 1;
  Job = 2;
  WebService = 3;
}

// 不展示
message PodMetric {
  string name = 1;
  string namespace = 2;
  OwnReference ownerReference = 3;
  common.ResourceMetric cpu = 4;
  common.ResourceMetric memory = 5;
  common.ResourceMetric gpu = 6;
  repeated ContainerMetric containers = 7;
  common.ResourceMetric gpuMem = 8;
}

message ContainerMetric {
  string name = 1;
  common.ResourceMetric cpu = 2;
  common.ResourceMetric memory = 3;
  common.ResourceMetric gpu = 4;
  common.ResourceMetric gpuMem = 5;
}


message PodCount {
  int32 current = 1;
  int32 allocatable = 2;
}

enum MachineType {
  BareMetal = 0;
  CloudHost = 1;
}

message GetNodeDetailRequest {
  string ip = 1;
  bool statisticsEnabled = 2; //是否返回统计信息
  string workspaceName = 3;
}


message ListNodeDetailResult {
  repeated NodeDetail nodes = 1;
  int32 total = 2;
}


message ListNodeDetailOptions {
  repeated string ips = 1;
  string region = 2;
  string zone = 3;
  int64 cpuNum = 4;
  int64 memory = 5;
  int64 gpuNum = 6;
  int64 gpuMemory = 7;
  string gpuModel = 8;
  string cudaVersion = 9;
  string workspaceName = 10;
  string cluster = 11; // 集群名称
  string queueName = 12; //队列名称
  bool enableStatus = 13;
  int32 page = 14;
  int32 pageSize = 15;
  repeated string nodeStatus = 16;
  string ip = 17;
  string gpuAllocationMode = 18;
}

message ListNodeDetailForFrontendOptions {
  string ip = 1; // 节点ip, 支持模糊搜索
  string workspaceName = 2; // 工作空间名称
  string queueName = 3; // 队列名称 队列名称不为空的时候，展示队列名称包括该队列的节点，用于编辑
}

message GetNodeMetricRequest {
  string ip = 1;
  string workspaceName = 2;
}

message GetNodeSeriesRequest {
  string ip = 1;
  string metricsName = 2;
  string startTime = 3;
  string endTime = 4;
  string step = 6;
}

message GetNodeSeriesResponse {
  string ip = 1;
}

message CascadeNodeDetail {
  string label = 1; // 显示NodeSpecificationName
  repeated NodeDetailChildren children = 2;
}

message NodeDetailChildren {
  string label = 1;
  string value = 2;
}

message CascadeNodeDetails{
  repeated CascadeNodeDetail nodes = 1;
}


message DisableNodeRequest {
  string ip = 1;
  string workspaceName = 2;
  bool disable = 3;
}

message NodeWorkload {
  string workloadName = 1;
  string workloadType = 2;
  string workloadDisplayName = 3;
  string creator = 4;
  string queueName = 5;
  string region = 6;
  string workloadStatus = 7;
  string appName = 8;
  AppType appType = 9;  // 0 WebService 1 Inference
  string deploymentGroupName = 10;
  string deploymentGroupType = 11;
  common.Specification specification = 12;
  string instanceName = 13;

  enum AppType {
    WebService = 0;
    Inference = 1;
    Unknown = 2;
  }
}

message GetNodeWorkloadsResponse {
  repeated NodeWorkload nodeWorkloads = 1;
  int32 total = 2;
}

message GetNodeWorkloadsRequest {
  string ip = 1;
  string workspaceName = 2;
}

message EvictNodeWorkloadsRequest {
  string ip = 1;
  string workspaceName = 2;
}

message NodeWorkloadsResponse {
  repeated NodeWorkload nodeWorkload = 1;
}

message ChangeNodeGpuAllocationModeRequest {
  string ip = 1;
  string workspaceName = 2;
  GpuAllocationMode gpuAllocationMode = 3;  // GPU分配模式 共享模式 独占模式
}

enum GpuAllocationMode {
  None = 0;  // CPU节点
  Exclusive = 1;  // GPU节点独占
  Shared = 2;  // GPU节点共享显存
}