syntax = "proto3";

package apis.aistudio.bcode;
import "errors/errors.proto";

option go_package = "git.lianjia.com/cloudnative/kic/kic-platform/apis/aistudio/bcode";


enum ErrorReason  {
  option(errors.default_code) = 500;
  UserNotFound = 0 [(errors.code) = 404];
  UserInconsistentPassword = 1 [(errors.code) = 403];
  UserInvalidPassword = 2 [(errors.code) = 400];
  ServerInternalError = 3[(errors.code) = 500];
  PermissionNotAllowed = 4[(errors.code) = 403];
  PermissionNotFound = 5 [(errors.code) = 400];
  RoleNotFound = 6 [(errors.code) = 400];
  RoleBindingNotFound = 7 [(errors.code) = 400];
  Unauthorized = 8[(errors.code) = 401];
  InvalidArgument = 9[(errors.code) = 400];
  KeycloakError = 10[(errors.code) = 500];
  WorkspaceAlreadyExist = 11[(errors.code) = 400];
  NodeNotFound = 12[(errors.code) = 404];
  KubernetesClusterNotFound = 13[(errors.code) = 404];
  NodeAlreadyAssigned = 14[(errors.code) = 409];
  QueueAlreadyExists = 15[(errors.code) = 409];
  QueueNotFound = 16[(errors.code) = 400];
  WorkflowNotFound = 17[(errors.code) = 400];
  K8sClusterInitError = 18[(errors.code) = 500];
  VelaProjectAlreadyExist = 19[(errors.code) = 400];
  VelaEnvironmentAlreadyExist = 20[(errors.code) = 400];
  VelaInternalError = 21[(errors.code) = 500];
  CubeFSVolumeAlReadyExists = 22[(errors.code) = 409];
  CubeFSVolumeNotFound = 23[(errors.code) = 404];
  CubeFSUserNotFount = 41[(errors.code) = 404];
  CubeFSUnauthorized = 42[(errors.code) = 401];
  JobNotFound = 24[(errors.code) = 404];
  CloudFSVolumeNotFound = 25[(errors.code) = 404];
  CloudFSVolumeAlreadyExists = 26[(errors.code) = 409];
  JobStatusError = 27[(errors.code) = 500];
  QueueHasRunningJobs = 28[(errors.code) = 400];
  ResourceInsufficient = 29[(errors.code) = 400];
  NotFoundGPUProduct = 30[(errors.code) = 400];
  JobAlReadyExists = 31[(errors.code) = 400];
  QueueNotRunning = 32[(errors.code) = 400];
  WorkspaceNotFound = 33[(errors.code) = 404];
  OperationNotAllowed = 34[(errors.code) = 400];
  NotFoundCubeFSWorkedCluster = 35[(errors.code) = 400];
  TensorboardNotFound = 36[(errors.code) = 404];
  TensorboardFeatureNotEnabled = 37[(errors.code) = 400];
  TcrSystemError = 38[(errors.code) = 500];
  DevMachineAlReadyExists = 39[(errors.code) = 400];
  DevMachineNotFound = 40[(errors.code) = 404];
  ModelNotFound = 43[(errors.code) = 404];
  RelatedResourcesNotCleared = 44[(errors.code) = 403];

  // application error
  ApplicationExist = 45 [(errors.code) = 400];
  ApplicationNotFound = 46 [(errors.code) = 404];
  WorkspaceNotReady = 47[(errors.code) = 400];
  DeploymentGroupExistsAlready = 48[(errors.code) = 400];
  DeploymentGroupNotFound = 49[(errors.code) = 404];
  HTTPRouteNotFound = 50[(errors.code) = 404];
  GatewayAlReadyExists = 51[(errors.code) = 400];
  GatewayNotFound = 52[(errors.code) = 404];
  DomainAlReadyExists = 53[(errors.code) = 400];
  DomainNotFound = 54[(errors.code) = 404];
  DestinationNotFound = 55[(errors.code) = 404];
  DestinationAlReadyExists = 56[(errors.code) = 400];
  ServiceEntryAlReadyExists = 57[(errors.code) = 400];
  ServiceEntryNotFound = 58[(errors.code) = 404];
  GatewayServiceBindingNotFound = 59[(errors.code) = 404];
  GatewayServiceBindingAlreadyExists = 60[(errors.code) = 419];

  ProductSpecificationAlreadyExists = 61[(errors.code) = 400];
  ProductSpecificationNotFound = 62[(errors.code) = 404];

  HadoopSecretNotFound = 63[(errors.code) = 404];

  NotEnoughResource = 64[(errors.code) = 400];
  ForbiddenOperation = 65[(errors.code) = 403];

  JobTemplateAlreadyExists = 66[(errors.code) = 400];
  TemporalScheduleNotExists = 67[(errors.code) = 404];
}