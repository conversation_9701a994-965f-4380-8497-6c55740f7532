syntax = "proto3";

package apis.aistudio.job.v1;

option go_package = "git.lianjia.com/cloudnative/kic/kic-platform/apis/aistudio/job/v1;v1";

import "google/api/annotations.proto";
import "google/protobuf/duration.proto";
import "validate/validate.proto";
import "google/protobuf/empty.proto";
import "common/common.proto";
import "aistudio/config/v1/config.proto";

service JobService {
  // CreateJobTemplate 创建任务模板
  rpc CreateJobTemplate(CreateOrUpdateJobTemplateRequest) returns(google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/apis/v1/workspace/{workspaceName}/job_templates"
      body: "*"
    };
  }

  // UpdateJobTemplate 更新任务模板
  rpc UpdateJobTemplate(CreateOrUpdateJobTemplateRequest) returns(google.protobuf.Empty) {
    option (google.api.http) = {
      put: "/apis/v1/workspace/{workspaceName}/job_template/{name}"
      body: "*"
    };
  }

  // GetJobTemplate 获取任务模板
  rpc GetJobTemplate(GetJobTemplateRequest) returns(JobTemplate) {
    option (google.api.http) = {
      get: "/apis/v1/workspace/{workspaceName}/job_template/{jobTemplateName}"
    };
  }

  // ListJobTemplates 列出任务模板
  rpc ListJobTemplateViews(ListJobTemplateViewsOptions) returns(ListJobTemplateViewsResponse) {
    option (google.api.http) = {
      get: "/apis/v1/workspace/{workspaceName}/job_templates"
    };
  }

  // DeleteJobTemplate 删除任务模板
  rpc DeleteJobTemplate(DeleteJobTemplateRequest) returns(google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/apis/v1/workspace/{workspaceName}/job_template/{jobTemplateName}"
    };
  }

  // CreateJobTemplateTrigger 创建任务模板触发器
  rpc CreateJobTemplateTrigger(CreateOrUpdateJobTemplateTriggerRequest) returns(google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/apis/v1/workspace/{workspaceName}/job_template/{jobTemplateName}/trigger"
      body: "*"
    };
  }

  // UpdateJobTemplateTrigger 更新任务模板触发器
  rpc UpdateJobTemplateTrigger(CreateOrUpdateJobTemplateTriggerRequest) returns(google.protobuf.Empty) {
    option (google.api.http) = {
      put: "/apis/v1/workspace/{workspaceName}/job_template/{jobTemplateName}/trigger/{triggerName}"
      body: "*"
    };
  }

  // DeleteJobTemplateTrigger 删除任务模板触发器
  rpc DeleteJobTemplateTrigger(DeleteJobTemplateTriggerRequest) returns(google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/apis/v1/workspace/{workspaceName}/job_template/{jobTemplateName}/trigger/{triggerName}"
    };
  }

  // GetJobTemplateTrigger 获取任务模板触发器
  rpc GetJobTemplateTrigger(GetJobTemplateTriggerRequest) returns(Trigger) {
    option (google.api.http) = {
      get: "/apis/v1/workspace/{workspaceName}/job_template/{jobTemplateName}/trigger/{triggerName}"
    };
  }

  // ListJobTemplateTriggers 获取任务模板触发器
  rpc ListJobTemplateTriggers(ListJobTemplateTriggersRequest) returns(ListJobTemplateTriggersResponse) {
    option (google.api.http) = {
      get: "/apis/v1/workspace/{workspaceName}/job_template/{jobTemplateName}/triggers"
    };
  }

  // PauseJobTemplateTrigger 暂停任务模板触发器
  rpc PauseJobTemplateTrigger(PauseJobTemplateTriggerRequest) returns(google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/apis/v1/workspace/{workspaceName}/job_template/{jobTemplateName}/trigger/{triggerName}/pause"
      body: "*"
    };
  }

  // RecoverJobTemplateTrigger 恢复任务模板触发器
  rpc RecoverJobTemplateTrigger(RecoverJobTemplateTriggerRequest) returns(google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/apis/v1/workspace/{workspaceName}/job_template/{jobTemplateName}/trigger/{triggerName}/recover"
      body: "*"
    };
  }

  // 提交训练任务
  rpc SubmitJob(CreateJobRequest) returns (CreateJobResponse) {
    option (google.api.http) = {
      post: "/apis/v1/workspace/{workspaceName}/jobs"
      body: "*"
    };
  }

  // 停止训练任务
  rpc StopJob(StopJobRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/apis/v1/workspace/{workspaceName}/job/{jobName}/stop"
      body: "*"
    };
  }

  rpc UpdateJob(UpdateJobRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      put: "/apis/v1/workspace/{workspaceName}/job/{jobName}"
      body: "*"
    };
  }

  // GetJobBase 获取训练任务基本信息，不带任务状态用于复制
  rpc GetJobBase(GetJobBaseRequest) returns (Job) {
    option (google.api.http) = {
      get: "/apis/v1/workspace/{workspaceName}/jobs/{jobName}/base"
    };
  }

  // GetJobDetail 获取训练任务详情，带有任务状态用于任务详情展示
  rpc GetJobDetail(GetJobRequest) returns (Job) {
    option (google.api.http) = {
      get: "/apis/v1/workspace/{workspaceName}/jobs/{jobName}/detail"
    };
  }

  rpc DeleteJob(DeleteJobRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/apis/v1/workspace/{workspaceName}/job/{jobName}"
    };
  }

  rpc ListJobs(ListJobsOptions) returns (ListJobsResponse) {
    option (google.api.http) = {
      get: "/apis/v1/workspace/{workspaceName}/jobs"
    };
  }

  rpc CreateTensorboard(CreateTensorboardRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/apis/v1/workspace/{workspaceName}/job/{jobName}/tensorboard"
      body: "*"
    };
  }

  rpc GetTensorboard(GetTensorboardRequest) returns (GetTensorboardResponse) {
    option (google.api.http) = {
      get: "/apis/v1/workspace/{workspaceName}/job/{jobName}/tensorboard"
    };
  }

  // 获取运行实例
  rpc GetJobTasks(GetJobTasksRequest) returns (ListJobTaskStatus) {
    option (google.api.http) = {
      get: "/apis/v1/workspace/{workspaceName}/job/{jobName}/tasks"
    };
  }

  rpc GetJobVolumes(GetJobVolumeRequest) returns (ListJobVolumes) {
    option (google.api.http) = {
      get: "/apis/v1/workspace/{workspaceName}/job/{jobName}/volumes"
    };
  }

  // 强制停止任务实例
  rpc StopJobTask(StopJobTaskRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/apis/v1/workspace/{workspaceName}/job/{jobName}/task/{taskID}/stop"
      body: "*"
    };
  }

  rpc RedeployJob(RedeployJobRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/apis/v1/workspace/{workspaceName}/job/{jobName}/redeploy"
      body: "*"
    };
  }

  // CheckJobIsExist 检查任务是否存在
  rpc CheckJobIsExist(CheckJobIsExistRequest) returns(CheckJobIsExistResponse) {
    option (google.api.http) = {
      get: "/apis/v1/workspace/{workspaceName}/job/{jobName}/job_exist"
    };
  }

  // UpdateMembers 更新成员
  rpc UpdateMembers(UpdateMembersRequest) returns(google.protobuf.Empty) {
    option (google.api.http) = {
      put: "/apis/v1/workspace/{workspaceName}/job/{jobName}/update_members"
      body: "*"
    };
  }
}

message CheckJobIsExistRequest {
  string workspaceName = 1;
  string jobName = 2;
}

message CheckJobIsExistResponse {
  int32 status = 1; // 适配 fcn，0 为校验成功， 422 为校验失败
  string errors = 2;
}

message UpdateMembersRequest {
  string workspaceName = 1;
  string jobName = 2;
  repeated string members = 3;
}

message JobTemplate {
  string name = 1;
  string displayName = 2;
  string description = 3 [(validate.rules).string.max_len = 100];
  string workspaceName = 4 [(validate.rules).string.min_len = 1, (validate.rules).string.max_len = 31, (validate.rules).string.pattern = "^[a-z]([-a-z0-9]*[a-z0-9])?$"];
  string region = 5;
  map<string, string> labels = 6;
  repeated string managers = 7;
  repeated string members = 8;
  repeated Trigger triggers = 9;
  ExecutionStrategy executionStrategy = 10; // 执行策略
  Job job = 11;
  string creator = 12;
  string createTime = 14;
  string updateTime = 15;
}

message JobTemplateView {
  JobTemplate jobTemplate = 1;
  string jobTemplateStatus = 2;  // 任务模板状态，获取任务模板所有触发器状态，封装模板状态展示
  repeated string nextNumActions = 3;  // 下次执行时间，如果存在多个定时调度触发器情况下去最近的 3 个
  int32 numActions = 4; // 已经执行的次数
}

message Trigger {
  TriggerType triggerType = 1; //触发器类型
  string triggerName = 2;
  TimerTriggerOptions timeTriggerOptions = 3;
  repeated string nextActionTimes = 4; // 下次执行时间
  int32 numActions = 5; // 已经执行的次数
  TriggerState state = 6;
  string message = 7; // 信息
}

message TimerTriggerOptions {
  TimerTriggerType type = 1;
  int32 interval = 2; //时间间隔
  string cronExpr = 3; //cron表达式
  Cycle cycle = 4; //周期调度
  string periodOfValidity = 5; // 有效期
  int32 maxCount = 6; // 最大执行次数
  Cycle skipTime = 7; //跳过时间
  enum TimerTriggerType  {
    Interval = 0;
    CronExpr = 1;
    Cycle = 2;
  }
}

enum TriggerType {
  Timer = 0; //定时触发器
  Event = 1; //事件触发器
  Webhook = 2; //webhook触发器
}

// 周期
message Cycle {
  repeated int32 month = 2; //月
  repeated int32 dayOfMonth = 3; //天
  repeated int32 dayOfWeek = 4; //周
  repeated int32 hour = 5; //小时
  repeated int32 minute = 6; //分钟
}

enum ExecutionStrategy {
  ImmediateFirstTask = 0; // 立即执行首次任务
  SaveTemplateOnly = 1;  // 仅保存模板不执行任务
  ScheduledExecution = 2;  // 按照调度策略执行任务
}

enum TriggerState {
  TriggerState_Inactive = 0;  // 未生效
  TriggerState_Executing = 1;  // 执行中
  TriggerState_Paused = 2;  // 暂停中
  TriggerState_Completed = 3;  // 已完成（执行次数等于最大副本数）
  TriggerState_Expired = 4;  // 已过期（超过有效期）
}

message JobTriggerRecord {
  string jobName = 1;
  string jobTemplateName = 3;
  string triggerName = 4;
  string triggerTime = 5;
  string creator = 6;
}

message PauseJobTemplateTriggerRequest {
  string workspaceName = 1;
  string jobTemplateName = 2;
  string triggerName = 3;
  string message = 4;
}

message RecoverJobTemplateTriggerRequest {
  string workspaceName = 1;
  string jobTemplateName = 2;
  string triggerName = 3;
  string message = 4;
}

message CreateOrUpdateJobTemplateRequest {
  string name = 1;
  string displayName = 2;
  string description = 3 [(validate.rules).string.max_len = 100];
  string workspaceName = 4 [(validate.rules).string.min_len = 1, (validate.rules).string.max_len = 31, (validate.rules).string.pattern = "^[a-z]([-a-z0-9]*[a-z0-9])?$"];
  string region = 5;
  map<string, string> labels = 6;
  Job job = 7;
  repeated Trigger triggers = 8;
  ExecutionStrategy executionStrategy = 9; // 执行策略
  repeated string managers = 10;
  repeated string members = 11;
}

message GetJobTemplateRequest {
  string workspaceName = 1;
  string jobTemplateName = 2;
}


message ListJobTemplateViewsOptions {
  string workspaceName = 1;
  string jobTemplateName = 2;
  string region = 4;
  string manager = 3;
  string member = 6;
  int32 page = 7;
  int32 pageSize = 8;
}

message ListJobTemplateViewsResponse {
  repeated JobTemplateView jobTemplateViews = 1;
  int64 total = 2;
}

message CreateOrUpdateJobTemplateTriggerRequest {
  string workspaceName = 1;
  string jobTemplateName = 2;
  TriggerType triggerType = 3; //触发器类型
  string triggerName = 4;
  TimerTriggerOptions timeTriggerOptions = 5;
}

message DeleteJobTemplateTriggerRequest {
  string workspaceName = 1;
  string jobTemplateName = 2;
  string triggerName = 3;
}

message GetJobTemplateTriggerRequest {
  string workspaceName = 1;
  string jobTemplateName = 2;
  string triggerName = 3;
}

message ListJobTemplateTriggersRequest {
  string workspaceName = 1;
  string jobTemplateName = 2;
  string triggerName = 3;
}

message ListJobTemplateTriggersResponse {
  repeated Trigger triggers = 1;
  int32 total = 2;
}

message Job {
  string id = 1; //全局唯一的id
  string name = 2;
  string displayName = 3;
  string description = 4;
  string workspaceName = 5;
  string region = 6;
  string creator = 7;
  string revision = 8;
  string workflowRevision = 9;
  JobType jobType = 10;
  repeated string members = 11;
  JobStatus jobStatus = 12;
  string queueName = 13;
  PyTorchDDPJobTemplate pyTorchDDPJobTemplate = 14;
  DeepSpeedJobTemplate deepSpeedJobTemplate = 15;
  SimpleTrainingJobTemplate simpleTrainingJobTemplate = 16;
  string createTime = 17;
  string updateTime = 18;
  RestartPolicy restartPolicy = 19;
  bool alarmShielding = 22; // 是否屏蔽告警
  int32 priority = 23; // 优先级
  int32 maxWaitTime = 24; //最大等待时间
  map<string, string> labels = 25;
  string jobTemplateName = 27; // 模版名称, 为空表示没有模板。平台直接创建任务
}

message RestartPolicy {
  bool enabled = 1;
  int32 maxRetryCount = 2;
}

message DatasetVolume {
  string name = 1;
  string mountPoint = 2;
}

message CreateJobRequest {
  string name = 1;
  string displayName = 2;
  string description = 3 [(validate.rules).string.max_len = 100];
  string workspaceName = 4 [(validate.rules).string.min_len = 1, (validate.rules).string.max_len = 31, (validate.rules).string.pattern = "^[a-z]([-a-z0-9]*[a-z0-9])?$"];
  string region = 5;
  map<string, string> labels = 6;
  JobType jobType = 7;
  string queueName = 8 [(validate.rules).string.min_len = 1, (validate.rules).string.max_len = 31]; // 运行队列
  Priority priority = 9; // 优先级
  repeated string members = 10;
  PyTorchDDPJobTemplate pyTorchDDPJobTemplate = 11;
  DeepSpeedJobTemplate deepSpeedJobTemplate = 12;
  SimpleTrainingJobTemplate simpleTrainingJobTemplate = 13;
  int32  maxWaitTime = 14; //最大等待时间
  //  google.protobuf.Duration maxWaitTime = 14; //最大等待时间
  RestartPolicy restartPolicy = 15;
  bool alarmShielding = 16; // 是否屏蔽告警
  string jobTemplateName = 18; // 模版名称, 为空表示没有模板。平台直接创建任务
  bool hadoopEnabled = 19; // 是否开启hadoop
  repeated string hadoopUsers = 20; // hadoop用户列表
}

message CreateJobResponse {
  string jobName = 1;
}

message UpdateJobRequest {
  string jobName = 1;
  string displayName = 2;
  string description = 3 [(validate.rules).string.max_len = 100];
  string workspaceName = 4;
  string creator = 6;
  repeated string members = 7;
  string priority = 8;
}

message CreateTensorboardRequest {
  string workspaceName = 1;
  string jobName = 2;
}

message GetTensorboardRequest {
  string workspaceName = 1;
  string jobName = 2;
}

message GetTensorboardResponse {
  string url = 1;
  string state = 2;
}

//PyTorch 分布式训练配置
message PyTorchDDPJobTemplate {
  bool tensorboardEnabled = 1;
  repeated common.VolumeSpec volumeSpecs = 3; //卷, 可支持直接挂卷,可以写训练的模型
  SourceCode sourceCode = 4;
  repeated common.EnvVar envVars = 5; //环境变量
  string command = 6; //执行命令
  string image = 7; //镜像
  TaskSpec master = 8; //master
  TaskSpec worker = 9; //worker
  repeated apis.aistudio.config.v1.ConfigSpec configSpecs = 10; //文件配置
}

//DeepSpeed 分布式训练配置
message DeepSpeedJobTemplate {
  bool tensorboardEnabled = 1;
  repeated common.VolumeSpec volumeSpecs = 3; //卷, 可支持直接挂卷,可以写训练的模型
  SourceCode sourceCode = 4;
  repeated common.EnvVar envVars = 5; //环境变量
  string command = 6; //执行命令
  string image = 7; //镜像
  TaskSpec master = 8;
  TaskSpec worker = 9;
  repeated apis.aistudio.config.v1.ConfigSpec configSpecs = 10; //文件配置
}

message SimpleTrainingJobTemplate {
  bool tensorboardEnabled = 1;
  SourceCode sourceCode = 2;
  string command = 3; //执行命令
  string image = 4; //镜像
  repeated common.EnvVar envVars = 5; //环境变量
  repeated common.VolumeSpec volumeSpecs = 6; //卷, 可支持直接挂卷,可以写训练的模型
  common.Specification specification = 7; //规格
  string nodeSpecificationName = 8; //节点规格
  repeated apis.aistudio.config.v1.ConfigSpec configSpecs = 9; //文件配置
}

enum Priority {
  LOW = 0;
  NORMAL = 1;
  HIGH = 2;
}

message TaskSpec {
  string name = 1; // master,worker
  int32 replicas = 2; //副本数
  string command = 3; //执行命令
  string image = 4; //镜像
  repeated common.EnvVar envVars = 5; //环境变量
  repeated common.VolumeSpec volumeSpecs = 6; //卷, 可支持直接挂卷,可以写训练的模型
  common.Specification  specification = 7; //规格
  string nodeSpecificationName = 8; //节点规格, 可以设置多个规格
  int32 slots = 9; //占用的slot数
  repeated apis.aistudio.config.v1.ConfigSpec configSpecs = 10; //文件配置
}

enum JobType {
  PyTorchDDP = 0;
  DeepSpeed = 1;
  SimpleTraining = 2;
}

message SourceCode {
  oneof source {
    CloudFsSource cloudFsSource = 1;
    GitSource gitSource = 2;
    CubeFsSource cubeFsSource = 3;
  }
  string mountPoint = 4; //挂载点

  message CloudFsSource {
    string volumeName = 1;
    string subPath = 2; //卷内部的路径,可以为空
  }
  message GitSource {
    string url = 1;
    string branch = 2;
  }
  message CubeFsSource {
    string volumeName = 1;
  }
}

// vcjob status + other status
message JobStatus {
  string state = 1;
  int32 pending = 2; //the number of pending pods
  int32 running = 3; //the number of running pods
  int32 succeeded = 4; //the number of succeeded pods
  int32 failed = 5; //the number of failed pods
  int32 terminating = 6;//the number of terminating pods
  int32 unknown = 7; //the number of unknown pods
  int32 version = 8; //the version of the job
  int32 retryCount = 9; //the retry count of the job
  string reason = 10; //the reason of the job
  string message = 11; //the message of the job
  string lastTransitionTime = 12; //最后一次状态变更时间
  string createTimestamp = 13; //创建时间
  repeated JobCondition conditions = 14; //the conditions of the job
  string runningDuration = 15; //运行时长
  string clusterName = 16; //集群名称
}

message ListJobTaskStatus{
  repeated TaskStatus taskStatus = 1;
}

message TaskStatus {
  string phase = 1;
  string podName = 2;
  string podNamespace = 3;
  string podIP = 4;
  string cluster = 5; //容器集群
  string region = 6;// 地区
  string zone = 7; //分区
  string nodeIP = 8;
  string createTimestamp = 9; //创建时间
  string finishTimestamp = 10; //开始时间
  string image = 11;
  repeated TaskCondition conditions = 12; //任务状态
  int32 restartCount = 13; //重启次数
  string reason = 14;
  string message = 15;
  string taskName = 16;
  string taskEnv = 17;
  string hostIP = 18;
  string taskID = 19;
  string jobVersion = 20;
  int64 historicalLogStartTimestamp = 21;
  int64 historicalLogEndTimestamp = 22;
}

enum TaskPhase {
  TaskPhase_Pending = 0;
  TaskPhase_Running = 1;
  TaskPhase_Succeeded = 2;
  TaskPhase_Failed = 3;
  TaskPhase_Unknown = 4;
}

message GetJobIDRequest {
  string workspaceName = 1;
  string jobName = 2;
}

message GetJobIDResponse {
  string jobID = 1;
}

message TaskCondition {
  string reason = 1;
  string message = 2;
  string type = 3;
  string status = 4;
  string lastTransitionTime = 5; //最后一次状态变更时间
}

enum TaskConditionType {
  ContainersReady = 0;  // ContainersReady indicates whether all containers in the pod are ready.
  PodInitialized = 1;  // PodInitialized means that all init containers in the pod have started successfully.
  // PodReady means the pod is able to service requests and should be added to the
  // load balancing pools of all matching services.
  PodReady = 2;
  // PodScheduled represents status of the scheduling process for this pod.
  PodScheduled = 3;
  // DisruptionTarget indicates the pod is about to be terminated due to a
  // disruption (such as preemption, eviction API or garbage-collection).
  DisruptionTarget = 4;
}

enum JobState {
  JobState_Pending = 0;
  JobState_Running = 1;
  JobState_Aborting = 3;
  JobState_Aborted = 4;
  JobState_Restarting = 5;
  JobState_Completing = 6;
  JobState_Completed = 7;
  JobState_Terminating = 8;
  JobState_Terminated = 9;
  JobState_Failed = 10;
}

enum JobPhase {
  InQueue = 0;
  ResourcePreparation = 1;
  Running = 2;
  Finished = 3;
  Killed = 4;
  Failed = 5;
}

message JobCondition {
  string phase = 1;
  string lastTransitionTime = 2; //最后一次状态变更时间
  string reason = 3; //the reason of the job
  string message = 4; //the message of the job
  ConditionStatus conditionStatus = 6; //the status of the job
  enum ConditionStatus {
    True = 0;
    False = 1;
    Unknown = 2;
  }
}

message StopJobRequest {
  string workspaceName = 1;
  string jobName = 2;
}

message GetJobBaseRequest {
  string workspaceName = 1;
  string jobName = 2;
  string isDeleted = 3;
}

message GetJobRequest {
  string workspaceName = 1;
  string jobName = 2;
  string isDeleted = 3;
}

message DeleteJobRequest {
  string workspaceName = 1;
  string jobName = 2;
  string isDeleted = 3;
}

message DeleteJobTemplateRequest{
  string workspaceName = 1;
  string jobTemplateName = 2; // 模版名称
}

message GetJobTasksRequest {
  string workspaceName = 1;
  string jobName = 2;
  string isDeleted = 3;
  string showTheLatestVersion = 4;  // 是否只展示最新版本 true 展示最新版本 false 展示旧版本 空值展示所有版本
}

message GetJobVolumeRequest {
  string workspaceName = 1;
  string jobName = 2;
  string isDeleted = 3;
}

message ListJobsOptions {
  string creator = 1;
  string workspaceName = 2;
  string queueName = 3;
  string jobType = 4;
  string name = 5; //模糊匹配
  string displayName = 6; //模糊匹配
  string region = 7;
  bool statusEnabled = 8;
  string member = 9;
  int32 page = 10;
  int32 pageSize = 11;
  string state = 12;
  string jobTemplateName = 13; // 模板名称
}


message ListJobsResponse {
  repeated Job jobs = 1;
  int64 total = 2;
}

message JobTimeline{
  string name = 1; // jobName
  string workspaceName = 2;
  string startTime = 3;
  string status = 4;
  string message = 5;
  string reason = 6;
}

message JobVolume{
  string name = 1;
  string mountPoint = 2;
  string volumeType = 3;
  string subPath = 4;
  string scope = 5;
}

message ListJobVolumes{
  repeated JobVolume volumes = 1;
}

message StopJobTaskRequest{
  string workspaceName = 1;
  string jobName = 2;
  string taskID = 3;
  string podName = 4;
}

message RedeployJobRequest {
  string workspaceName = 1;
  string jobName = 2;
}