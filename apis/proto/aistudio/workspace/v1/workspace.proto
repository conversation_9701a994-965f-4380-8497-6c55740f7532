syntax = "proto3";

package apis.workspace.v1;

import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "common/common.proto";
import "usercenter/v1/usercenter.proto";
import "validate/validate.proto";


option go_package = "git.lianjia.com/cloudnative/kic/kic-platform/apis/aistudio/workspace/v1;v1";

message WorkspaceBase  {
  string name = 1 [(validate.rules).string.min_len = 1, (validate.rules).string.max_len = 31, (validate.rules).string.pattern = "^[a-z]([-a-z0-9]*[a-z0-9])?$" ];
  string displayName = 2 [(validate.rules).string.max_len = 31];  // 空间别名
  string description = 3 [(validate.rules).string.max_len = 100];;  // 空间描述
  repeated string managers = 4; //管理员
  repeated string members = 5; //成员
  common.TimestampModel timestamp = 6;
  bool enabled = 7; //是否可用 删除空间通过设置为不可用，不删除数据库记录
  string resourceID = 8; //资源ID
}

service WorkspaceService {
  // CheckWorkspaceIsExist 检查工作空间是否存在
  rpc CheckWorkspaceIsExist(CheckWorkspaceIsExistRequest) returns(CheckWorkspaceIsExistResponse) {
    option (google.api.http) = {
      get: "/apis/v1/workspace_exist"
    };
  }

  // CreateWorkspace 创建工作空间
  rpc CreateWorkspace(CreateWorkspaceRequest) returns(WorkspaceDetail) {
    option (google.api.http) = {
      post: "/apis/v1/workspace",
      body: "*"
    };
  }

  // UpdateWorkspace 更新工作空间
  rpc UpdateWorkspace(UpdateWorkspaceRequest) returns(WorkspaceDetail) {
    option (google.api.http) = {
      put: "/apis/v1/workspace/{name}",
      body: "*"
    };
  }

  // DisableWorkspace 禁用工作空间
  rpc DisableWorkspace(DisableWorkspaceRequest) returns(WorkspaceDetail) {
    option (google.api.http) = {
      delete: "/apis/v1/workspace/{name}",
    };
  }

  // GetWorkspaceBase 获取工作空间基本信息
  rpc GetWorkspaceBase(GetWorkspaceBaseRequest) returns(WorkspaceBase) {
    option (google.api.http) = {
      get: "/apis/v1/workspace/{name}"
    };
  }

  // GetWorkspaceDetail 获取工作空间详细信息
  rpc GetWorkspaceDetail(GetWorkspaceDetailRequest) returns(WorkspaceDetail) {
    option (google.api.http) = {
      get: "/apis/v1/workspace/{name}/detail"
    };
  }

  //获取Workspace列表, 通过访问用户的权限来返回具有权限的Workspace
  rpc ListWorkspaces(ListOptions) returns(ListWorkspaceResult) {
    option (google.api.http) = {
      get: "/apis/v1/workspaces"
    };
  }

  //列出可加入空间
  rpc ListJoinableWorkspaces(ListOptions) returns(ListWorkspaceResult) {
    option (google.api.http) = {
      get: "/apis/v1/joinable-workspaces"
    };
  }

  //获取资源概括
  rpc GetWorkspaceResourceSummary(GetWorkspaceResourceSummaryRequest) returns (WorkspaceResourceSummary){
    option (google.api.http) = {
      get: "/apis/v1/workspace/{name}/resource-summary"
    };
  }

  // 创建空间机器人账号（先创建机器人用户，然后直接赋予机器人角色）
  rpc CreateOrUpdateWorkspaceRobot(CreateOrUpdateRobotAccountRequest) returns (usercenter.v1.UserDetail) {
    option (google.api.http) = {
      post: "/apis/v1/workspace/{workspaceName}/robot"
      body: "*"
      additional_bindings {
        put: "/apis/v1/workspace/{workspaceName}/robot/{account}",
        body: "*",
      }
    };
  }

  // 工作空间添加成员(多个)
  rpc AddWorkspaceUsers(AddWorkspaceUsersRequest) returns (google.protobuf.Empty){
    option (google.api.http) = {
      post: "/apis/v1/workspace/{workspaceName}/users",
      body: "*"
    };
  }

  // 工作空间删除成员(单个)
  rpc DeleteWorkspaceUser(DeleteWorkspaceUserRequest) returns (google.protobuf.Empty){
    option (google.api.http) = {
      delete: "/apis/v1/workspace/{workspaceName}/user/{account}/role/{role}"
    };
  }

  // 获取工作空间的所有成员
  rpc ListWorkspaceMembers(ListWorkspaceMembersRequest) returns (ListWorkspaceMembersResponse){
    option (google.api.http) = {
      get: "/apis/v1/workspace/{workspaceName}/members"
    };
  }

  // 更改空间用户角色
  rpc ChangeWorkspaceUserRole(ChangeWorkspaceUserRoleRequest) returns (google.protobuf.Empty){
    option (google.api.http) = {
      put: "/apis/v1/workspace/{workspaceName}/user/{account}/role/{role}"
      body: "*"
    };
  }

// 判断用户是不是工作空间的管理员
  rpc IsWorkspaceManager(IsWorkspaceManagerRequest) returns (IsWorkspaceManagerResponse){
      option (google.api.http) = {
      get: "/apis/v1/workspace/{workspaceName}/manager/{account}"
      };
  }


  rpc SetClusterBindings(CreateClusterBindingsRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/apis/v1/workspace/{workspaceName}/cluster-bindings"
      body: "*"
    };
  }

  rpc ListClusterBindings(ListClusterBindingRequest) returns (ListClusterBindingsResult) {
    option (google.api.http) = {
      get: "/apis/v1/workspace/{workspaceName}/cluster-bindings"
    };
  }

}



message CreateClusterBindingsRequest {
  string workspaceName = 1;
  repeated string clusters = 2;
}


message DeleteClusterBindingRequest {
  string id = 1;
}

message ListClusterBindingRequest {
  string workspaceName = 1;
}

message ListClusterBindingsResult {
  repeated ClusterBinding clusterBindings = 1;
}


message ClusterBinding {
  string workspaceName = 1;
  string cluster = 2;
  string clusterZone = 3;
  string clusterRegion = 4;
  string clusterIDC = 5;
  string namespace = 6;
  bool ready = 7;

}

message GetWorkspaceSettingsRequest {
  string name = 1;
}

message BindingClustersRequest {
  string name = 1;
  repeated string clusters = 2;
}

message CheckWorkspaceIsExistRequest {
  string name = 1;
}

message CheckWorkspaceIsExistResponse {
  int32 status = 1; // 适配 fcn，0 为校验成功， 422 为校验失败
  string errors = 2;
}

message WorkspaceDetail {
  WorkspaceBase base = 1;
 repeated ClusterBinding clusterBindings = 2;
}

message WorkspaceResourceSummary {
  common.CPUUsage cpuUsage = 1;
  common.MemoryUsage memoryUsage = 2;
  common.GPUUsage gpuUsage = 3;
  repeated common.NodeSummary nodeSummaries = 4;
  repeated common.QueueSummary queueSummaries = 5;
  int32 QueueNum = 6;
  int32 taskNum = 7;
  int32 taskInQueue = 8; //队列中的任务数
  int32 taskRunning = 9; //队列中运行的任务数
  int32 taskPending = 10; //队列中等待的任务数
  int32 taskCompleted = 11; //队列中完成的任务数
  int32 taskUnknown = 12; //队列中未知的任务数
}

message QueueSummary {
  int32 taskNum = 1;
  int32 taskInQueue = 2; //队列中的任务数
  int32 taskRunning = 3; //队列中运行的任务数
  int32 taskPending = 4; //队列中等待的任务数
  int32 taskCompleted = 5; //队列中完成的任务数
  int32 taskUnknown = 6; //队列中未知的任务数
  int32 cpuNumUsed = 7;
  int32 memoryNumUsed = 8;
  int32 gpuNumUsed = 9;
}

message WorkspaceUser {
  string account = 1;
  apis.usercenter.v1.UserDetail userDetail = 2;
  string role = 3;
}

message CreateWorkspaceRequest {
  string name = 1 [(validate.rules).string.min_len = 1, (validate.rules).string.max_len = 31, (validate.rules).string.pattern = "^[a-z]([-a-z0-9]*[a-z0-9])?$" ];
  string description = 2 [(validate.rules).string.max_len = 100];
  string displayName = 3 [(validate.rules).string.max_len = 31]; //展示的名称
  repeated string managers = 4; //管理员
  repeated string members = 5; //成员
  repeated string availabilityRegionNames = 6; //可用地域
}

message UpdateWorkspaceRequest {
  string name = 1;
  string description = 2 [(validate.rules).string.max_len = 100];
  string displayName = 3 [(validate.rules).string.max_len = 31]; //展示的名称
  repeated string managers = 4; //管理员
  repeated string members = 5; //成员
  repeated string availabilityRegionNames = 6; //可用地域
}

message DisableWorkspaceRequest {
  string name = 1;
}

message GetWorkspaceBaseRequest {
  string name = 1;
}

message GetWorkspaceResourceSummaryRequest {
  string name = 1;
}

message GetWorkspaceDetailRequest {
  string name = 1;
}

message ListOptions {
  string prefix = 1; //空间名模糊使用
  string account = 2;
  bool displayClosed = 3; //是否展示关闭的空间
}

message ListWorkspaceResult{
  repeated WorkspaceBase workspaces = 1;
}

message AvailabilityRegionSettings {
  string region = 1;
  bool enabled = 2;
  ImageRepositorySettings imageRepositorySettings = 3;
}

message ImageRepositorySettings {
  bool enabled = 1; //是否可用
  string projectName = 2;
  bool private = 3;
}


message AddWorkspaceUsersRequest {
  string workspaceName = 1;
  repeated string account = 2;
  string role = 3;
  string userType = 4;
}

message DeleteWorkspaceUserRequest {
  string workspaceName = 1;
  string account = 2;
  string role = 3;
  string userType = 4;
}

message ListWorkspaceMembersRequest {
  string workspaceName = 1;
  string prefix = 2; // 用户 account 模糊搜索
  string userType = 3;
}

message ListWorkspaceMembersResponse {
  repeated WorkspaceUser workspaceUsers = 1;
}

message ChangeWorkspaceUserRoleRequest {
  string workspaceName = 1;
  string account = 2;
  string role = 3;
  string userType = 4;
}

message IsWorkspaceManagerRequest {
  string workspaceName = 1;
  string account = 2;
}

message IsWorkspaceManagerResponse {
  bool isManager = 1;
}

message CreateOrUpdateRobotAccountRequest {
  string account = 1;
  string displayName = 2; // 显示名称
  string description = 3; // 描述信息
  string weChatRobotToken = 4; // 微信机器人token
  string workspaceName = 5; // 工作空间名称
//  int32 expires = 6; // 有效期
  string password = 7; // 密码
  string role = 8; // 角色
  bool  isDefault = 9; // 是否默认
}