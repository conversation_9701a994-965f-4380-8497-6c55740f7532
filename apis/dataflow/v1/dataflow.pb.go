// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v5.29.3
// source: dataflow/v1/dataflow.proto

package v1

import (
	_ "git.lianjia.com/cloudnative/kic/kic-platform/apis/common"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	_ "google.golang.org/protobuf/types/known/durationpb"
	_ "google.golang.org/protobuf/types/known/emptypb"
	_ "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PipelineTemplate struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Source     *Source          `protobuf:"bytes,1,opt,name=source,proto3" json:"source,omitempty"`
	Processors []*ProcessorDesc `protobuf:"bytes,2,rep,name=processors,proto3" json:"processors,omitempty"`
	Exporter   *Exporter        `protobuf:"bytes,3,opt,name=exporter,proto3" json:"exporter,omitempty"`
}

func (x *PipelineTemplate) Reset() {
	*x = PipelineTemplate{}
	if protoimpl.UnsafeEnabled {
		mi := &file_dataflow_v1_dataflow_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PipelineTemplate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PipelineTemplate) ProtoMessage() {}

func (x *PipelineTemplate) ProtoReflect() protoreflect.Message {
	mi := &file_dataflow_v1_dataflow_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PipelineTemplate.ProtoReflect.Descriptor instead.
func (*PipelineTemplate) Descriptor() ([]byte, []int) {
	return file_dataflow_v1_dataflow_proto_rawDescGZIP(), []int{0}
}

func (x *PipelineTemplate) GetSource() *Source {
	if x != nil {
		return x.Source
	}
	return nil
}

func (x *PipelineTemplate) GetProcessors() []*ProcessorDesc {
	if x != nil {
		return x.Processors
	}
	return nil
}

func (x *PipelineTemplate) GetExporter() *Exporter {
	if x != nil {
		return x.Exporter
	}
	return nil
}

type ProcessorDesc struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FuncName  string `protobuf:"bytes,1,opt,name=funcName,proto3" json:"funcName,omitempty"`
	Condition string `protobuf:"bytes,2,opt,name=condition,proto3" json:"condition,omitempty"`
}

func (x *ProcessorDesc) Reset() {
	*x = ProcessorDesc{}
	if protoimpl.UnsafeEnabled {
		mi := &file_dataflow_v1_dataflow_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessorDesc) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessorDesc) ProtoMessage() {}

func (x *ProcessorDesc) ProtoReflect() protoreflect.Message {
	mi := &file_dataflow_v1_dataflow_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessorDesc.ProtoReflect.Descriptor instead.
func (*ProcessorDesc) Descriptor() ([]byte, []int) {
	return file_dataflow_v1_dataflow_proto_rawDescGZIP(), []int{1}
}

func (x *ProcessorDesc) GetFuncName() string {
	if x != nil {
		return x.FuncName
	}
	return ""
}

func (x *ProcessorDesc) GetCondition() string {
	if x != nil {
		return x.Condition
	}
	return ""
}

type Source struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name            string           `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Connector       *Connector       `protobuf:"bytes,2,opt,name=connector,proto3" json:"connector,omitempty"`
	ScheduleOptions *ScheduleOptions `protobuf:"bytes,3,opt,name=scheduleOptions,proto3" json:"scheduleOptions,omitempty"`
}

func (x *Source) Reset() {
	*x = Source{}
	if protoimpl.UnsafeEnabled {
		mi := &file_dataflow_v1_dataflow_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Source) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Source) ProtoMessage() {}

func (x *Source) ProtoReflect() protoreflect.Message {
	mi := &file_dataflow_v1_dataflow_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Source.ProtoReflect.Descriptor instead.
func (*Source) Descriptor() ([]byte, []int) {
	return file_dataflow_v1_dataflow_proto_rawDescGZIP(), []int{2}
}

func (x *Source) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Source) GetConnector() *Connector {
	if x != nil {
		return x.Connector
	}
	return nil
}

func (x *Source) GetScheduleOptions() *ScheduleOptions {
	if x != nil {
		return x.ScheduleOptions
	}
	return nil
}

type ScheduleOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Schedule:
	//
	//	*ScheduleOptions_Cron_
	//	*ScheduleOptions_Duration_
	//	*ScheduleOptions_Once_
	Schedule isScheduleOptions_Schedule `protobuf_oneof:"Schedule"`
}

func (x *ScheduleOptions) Reset() {
	*x = ScheduleOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_dataflow_v1_dataflow_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ScheduleOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScheduleOptions) ProtoMessage() {}

func (x *ScheduleOptions) ProtoReflect() protoreflect.Message {
	mi := &file_dataflow_v1_dataflow_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScheduleOptions.ProtoReflect.Descriptor instead.
func (*ScheduleOptions) Descriptor() ([]byte, []int) {
	return file_dataflow_v1_dataflow_proto_rawDescGZIP(), []int{3}
}

func (m *ScheduleOptions) GetSchedule() isScheduleOptions_Schedule {
	if m != nil {
		return m.Schedule
	}
	return nil
}

func (x *ScheduleOptions) GetCron() *ScheduleOptions_Cron {
	if x, ok := x.GetSchedule().(*ScheduleOptions_Cron_); ok {
		return x.Cron
	}
	return nil
}

func (x *ScheduleOptions) GetDuration() *ScheduleOptions_Duration {
	if x, ok := x.GetSchedule().(*ScheduleOptions_Duration_); ok {
		return x.Duration
	}
	return nil
}

func (x *ScheduleOptions) GetOnce() *ScheduleOptions_Once {
	if x, ok := x.GetSchedule().(*ScheduleOptions_Once_); ok {
		return x.Once
	}
	return nil
}

type isScheduleOptions_Schedule interface {
	isScheduleOptions_Schedule()
}

type ScheduleOptions_Cron_ struct {
	Cron *ScheduleOptions_Cron `protobuf:"bytes,1,opt,name=cron,proto3,oneof"`
}

type ScheduleOptions_Duration_ struct {
	Duration *ScheduleOptions_Duration `protobuf:"bytes,2,opt,name=duration,proto3,oneof"`
}

type ScheduleOptions_Once_ struct {
	Once *ScheduleOptions_Once `protobuf:"bytes,3,opt,name=once,proto3,oneof"`
}

func (*ScheduleOptions_Cron_) isScheduleOptions_Schedule() {}

func (*ScheduleOptions_Duration_) isScheduleOptions_Schedule() {}

func (*ScheduleOptions_Once_) isScheduleOptions_Schedule() {}

type Connector struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Enabled    bool              `protobuf:"varint,1,opt,name=enabled,proto3" json:"enabled,omitempty"`
	Type       string            `protobuf:"bytes,2,opt,name=type,proto3" json:"type,omitempty"`
	Properties map[string]string `protobuf:"bytes,3,rep,name=properties,proto3" json:"properties,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *Connector) Reset() {
	*x = Connector{}
	if protoimpl.UnsafeEnabled {
		mi := &file_dataflow_v1_dataflow_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Connector) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Connector) ProtoMessage() {}

func (x *Connector) ProtoReflect() protoreflect.Message {
	mi := &file_dataflow_v1_dataflow_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Connector.ProtoReflect.Descriptor instead.
func (*Connector) Descriptor() ([]byte, []int) {
	return file_dataflow_v1_dataflow_proto_rawDescGZIP(), []int{4}
}

func (x *Connector) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

func (x *Connector) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *Connector) GetProperties() map[string]string {
	if x != nil {
		return x.Properties
	}
	return nil
}

type Exporter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FuncName string `protobuf:"bytes,1,opt,name=funcName,proto3" json:"funcName,omitempty"`
}

func (x *Exporter) Reset() {
	*x = Exporter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_dataflow_v1_dataflow_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Exporter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Exporter) ProtoMessage() {}

func (x *Exporter) ProtoReflect() protoreflect.Message {
	mi := &file_dataflow_v1_dataflow_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Exporter.ProtoReflect.Descriptor instead.
func (*Exporter) Descriptor() ([]byte, []int) {
	return file_dataflow_v1_dataflow_proto_rawDescGZIP(), []int{5}
}

func (x *Exporter) GetFuncName() string {
	if x != nil {
		return x.FuncName
	}
	return ""
}

type ScheduleOptions_Once struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ScheduleOptions_Once) Reset() {
	*x = ScheduleOptions_Once{}
	if protoimpl.UnsafeEnabled {
		mi := &file_dataflow_v1_dataflow_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ScheduleOptions_Once) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScheduleOptions_Once) ProtoMessage() {}

func (x *ScheduleOptions_Once) ProtoReflect() protoreflect.Message {
	mi := &file_dataflow_v1_dataflow_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScheduleOptions_Once.ProtoReflect.Descriptor instead.
func (*ScheduleOptions_Once) Descriptor() ([]byte, []int) {
	return file_dataflow_v1_dataflow_proto_rawDescGZIP(), []int{3, 0}
}

type ScheduleOptions_Cron struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Expression string `protobuf:"bytes,1,opt,name=expression,proto3" json:"expression,omitempty"`
}

func (x *ScheduleOptions_Cron) Reset() {
	*x = ScheduleOptions_Cron{}
	if protoimpl.UnsafeEnabled {
		mi := &file_dataflow_v1_dataflow_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ScheduleOptions_Cron) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScheduleOptions_Cron) ProtoMessage() {}

func (x *ScheduleOptions_Cron) ProtoReflect() protoreflect.Message {
	mi := &file_dataflow_v1_dataflow_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScheduleOptions_Cron.ProtoReflect.Descriptor instead.
func (*ScheduleOptions_Cron) Descriptor() ([]byte, []int) {
	return file_dataflow_v1_dataflow_proto_rawDescGZIP(), []int{3, 1}
}

func (x *ScheduleOptions_Cron) GetExpression() string {
	if x != nil {
		return x.Expression
	}
	return ""
}

type ScheduleOptions_Duration struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Duration string `protobuf:"bytes,1,opt,name=duration,proto3" json:"duration,omitempty"`
}

func (x *ScheduleOptions_Duration) Reset() {
	*x = ScheduleOptions_Duration{}
	if protoimpl.UnsafeEnabled {
		mi := &file_dataflow_v1_dataflow_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ScheduleOptions_Duration) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScheduleOptions_Duration) ProtoMessage() {}

func (x *ScheduleOptions_Duration) ProtoReflect() protoreflect.Message {
	mi := &file_dataflow_v1_dataflow_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScheduleOptions_Duration.ProtoReflect.Descriptor instead.
func (*ScheduleOptions_Duration) Descriptor() ([]byte, []int) {
	return file_dataflow_v1_dataflow_proto_rawDescGZIP(), []int{3, 2}
}

func (x *ScheduleOptions_Duration) GetDuration() string {
	if x != nil {
		return x.Duration
	}
	return ""
}

var File_dataflow_v1_dataflow_proto protoreflect.FileDescriptor

var file_dataflow_v1_dataflow_proto_rawDesc = []byte{
	0x0a, 0x1a, 0x64, 0x61, 0x74, 0x61, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x61,
	0x74, 0x61, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x10, 0x61, 0x70,
	0x69, 0x73, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x76, 0x31, 0x1a, 0x1c,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d,
	0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x13, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0xbd, 0x01, 0x0a, 0x10, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x54, 0x65, 0x6d, 0x70,
	0x6c, 0x61, 0x74, 0x65, 0x12, 0x30, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x64, 0x61, 0x74, 0x61,
	0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x06,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x3f, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x6f, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69,
	0x73, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x6f, 0x72, 0x44, 0x65, 0x73, 0x63, 0x52, 0x0a, 0x70, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x6f, 0x72, 0x73, 0x12, 0x36, 0x0a, 0x08, 0x65, 0x78, 0x70, 0x6f, 0x72,
	0x74, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x73,
	0x2e, 0x64, 0x61, 0x74, 0x61, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x70,
	0x6f, 0x72, 0x74, 0x65, 0x72, 0x52, 0x08, 0x65, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x72, 0x22,
	0x49, 0x0a, 0x0d, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x6f, 0x72, 0x44, 0x65, 0x73, 0x63,
	0x12, 0x1a, 0x0a, 0x08, 0x66, 0x75, 0x6e, 0x63, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x66, 0x75, 0x6e, 0x63, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09,
	0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xa4, 0x01, 0x0a, 0x06, 0x53,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x39, 0x0a, 0x09, 0x63, 0x6f, 0x6e,
	0x6e, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61,
	0x70, 0x69, 0x73, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x76, 0x31, 0x2e,
	0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x09, 0x63, 0x6f, 0x6e, 0x6e, 0x65,
	0x63, 0x74, 0x6f, 0x72, 0x12, 0x4b, 0x0a, 0x0f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65,
	0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e,
	0x61, 0x70, 0x69, 0x73, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x76, 0x31,
	0x2e, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x52, 0x0f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x22, 0xbb, 0x02, 0x0a, 0x0f, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x4f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x3c, 0x0a, 0x04, 0x63, 0x72, 0x6f, 0x6e, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x66,
	0x6c, 0x6f, 0x77, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x4f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x43, 0x72, 0x6f, 0x6e, 0x48, 0x00, 0x52, 0x04, 0x63,
	0x72, 0x6f, 0x6e, 0x12, 0x48, 0x0a, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x64, 0x61, 0x74,
	0x61, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c,
	0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x48, 0x00, 0x52, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3c, 0x0a,
	0x04, 0x6f, 0x6e, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x61, 0x70,
	0x69, 0x73, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x4f,
	0x6e, 0x63, 0x65, 0x48, 0x00, 0x52, 0x04, 0x6f, 0x6e, 0x63, 0x65, 0x1a, 0x06, 0x0a, 0x04, 0x4f,
	0x6e, 0x63, 0x65, 0x1a, 0x26, 0x0a, 0x04, 0x43, 0x72, 0x6f, 0x6e, 0x12, 0x1e, 0x0a, 0x0a, 0x65,
	0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x65, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x1a, 0x26, 0x0a, 0x08, 0x44,
	0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x42, 0x0a, 0x0a, 0x08, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x22,
	0xc5, 0x01, 0x0a, 0x09, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x12, 0x18, 0x0a,
	0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07,
	0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x4b, 0x0a, 0x0a, 0x70,
	0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x2b, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x66, 0x6c, 0x6f, 0x77, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x50, 0x72, 0x6f,
	0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0a, 0x70, 0x72,
	0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x1a, 0x3d, 0x0a, 0x0f, 0x50, 0x72, 0x6f, 0x70,
	0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x26, 0x0a, 0x08, 0x45, 0x78, 0x70, 0x6f, 0x72,
	0x74, 0x65, 0x72, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x75, 0x6e, 0x63, 0x4e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x75, 0x6e, 0x63, 0x4e, 0x61, 0x6d, 0x65, 0x42,
	0x42, 0x5a, 0x40, 0x67, 0x69, 0x74, 0x2e, 0x6c, 0x69, 0x61, 0x6e, 0x6a, 0x69, 0x61, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x2f, 0x6b,
	0x69, 0x63, 0x2f, 0x6b, 0x69, 0x63, 0x2d, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f,
	0x61, 0x70, 0x69, 0x73, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x76, 0x31,
	0x3b, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_dataflow_v1_dataflow_proto_rawDescOnce sync.Once
	file_dataflow_v1_dataflow_proto_rawDescData = file_dataflow_v1_dataflow_proto_rawDesc
)

func file_dataflow_v1_dataflow_proto_rawDescGZIP() []byte {
	file_dataflow_v1_dataflow_proto_rawDescOnce.Do(func() {
		file_dataflow_v1_dataflow_proto_rawDescData = protoimpl.X.CompressGZIP(file_dataflow_v1_dataflow_proto_rawDescData)
	})
	return file_dataflow_v1_dataflow_proto_rawDescData
}

var file_dataflow_v1_dataflow_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_dataflow_v1_dataflow_proto_goTypes = []any{
	(*PipelineTemplate)(nil),         // 0: apis.dataflow.v1.PipelineTemplate
	(*ProcessorDesc)(nil),            // 1: apis.dataflow.v1.ProcessorDesc
	(*Source)(nil),                   // 2: apis.dataflow.v1.Source
	(*ScheduleOptions)(nil),          // 3: apis.dataflow.v1.ScheduleOptions
	(*Connector)(nil),                // 4: apis.dataflow.v1.Connector
	(*Exporter)(nil),                 // 5: apis.dataflow.v1.Exporter
	(*ScheduleOptions_Once)(nil),     // 6: apis.dataflow.v1.ScheduleOptions.Once
	(*ScheduleOptions_Cron)(nil),     // 7: apis.dataflow.v1.ScheduleOptions.Cron
	(*ScheduleOptions_Duration)(nil), // 8: apis.dataflow.v1.ScheduleOptions.Duration
	nil,                              // 9: apis.dataflow.v1.Connector.PropertiesEntry
}
var file_dataflow_v1_dataflow_proto_depIdxs = []int32{
	2, // 0: apis.dataflow.v1.PipelineTemplate.source:type_name -> apis.dataflow.v1.Source
	1, // 1: apis.dataflow.v1.PipelineTemplate.processors:type_name -> apis.dataflow.v1.ProcessorDesc
	5, // 2: apis.dataflow.v1.PipelineTemplate.exporter:type_name -> apis.dataflow.v1.Exporter
	4, // 3: apis.dataflow.v1.Source.connector:type_name -> apis.dataflow.v1.Connector
	3, // 4: apis.dataflow.v1.Source.scheduleOptions:type_name -> apis.dataflow.v1.ScheduleOptions
	7, // 5: apis.dataflow.v1.ScheduleOptions.cron:type_name -> apis.dataflow.v1.ScheduleOptions.Cron
	8, // 6: apis.dataflow.v1.ScheduleOptions.duration:type_name -> apis.dataflow.v1.ScheduleOptions.Duration
	6, // 7: apis.dataflow.v1.ScheduleOptions.once:type_name -> apis.dataflow.v1.ScheduleOptions.Once
	9, // 8: apis.dataflow.v1.Connector.properties:type_name -> apis.dataflow.v1.Connector.PropertiesEntry
	9, // [9:9] is the sub-list for method output_type
	9, // [9:9] is the sub-list for method input_type
	9, // [9:9] is the sub-list for extension type_name
	9, // [9:9] is the sub-list for extension extendee
	0, // [0:9] is the sub-list for field type_name
}

func init() { file_dataflow_v1_dataflow_proto_init() }
func file_dataflow_v1_dataflow_proto_init() {
	if File_dataflow_v1_dataflow_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_dataflow_v1_dataflow_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*PipelineTemplate); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_dataflow_v1_dataflow_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*ProcessorDesc); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_dataflow_v1_dataflow_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*Source); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_dataflow_v1_dataflow_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*ScheduleOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_dataflow_v1_dataflow_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*Connector); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_dataflow_v1_dataflow_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*Exporter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_dataflow_v1_dataflow_proto_msgTypes[6].Exporter = func(v any, i int) any {
			switch v := v.(*ScheduleOptions_Once); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_dataflow_v1_dataflow_proto_msgTypes[7].Exporter = func(v any, i int) any {
			switch v := v.(*ScheduleOptions_Cron); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_dataflow_v1_dataflow_proto_msgTypes[8].Exporter = func(v any, i int) any {
			switch v := v.(*ScheduleOptions_Duration); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_dataflow_v1_dataflow_proto_msgTypes[3].OneofWrappers = []any{
		(*ScheduleOptions_Cron_)(nil),
		(*ScheduleOptions_Duration_)(nil),
		(*ScheduleOptions_Once_)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_dataflow_v1_dataflow_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_dataflow_v1_dataflow_proto_goTypes,
		DependencyIndexes: file_dataflow_v1_dataflow_proto_depIdxs,
		MessageInfos:      file_dataflow_v1_dataflow_proto_msgTypes,
	}.Build()
	File_dataflow_v1_dataflow_proto = out.File
	file_dataflow_v1_dataflow_proto_rawDesc = nil
	file_dataflow_v1_dataflow_proto_goTypes = nil
	file_dataflow_v1_dataflow_proto_depIdxs = nil
}
