// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v5.29.3
// source: common/common.proto

package common

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	_ "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Condition_ConditionStatus int32

const (
	Condition_TRUE    Condition_ConditionStatus = 0
	Condition_FALSE   Condition_ConditionStatus = 1
	Condition_UNKNOWN Condition_ConditionStatus = 2
)

// Enum value maps for Condition_ConditionStatus.
var (
	Condition_ConditionStatus_name = map[int32]string{
		0: "TRUE",
		1: "FALSE",
		2: "UNKNOWN",
	}
	Condition_ConditionStatus_value = map[string]int32{
		"TRUE":    0,
		"FALSE":   1,
		"UNKNOWN": 2,
	}
)

func (x Condition_ConditionStatus) Enum() *Condition_ConditionStatus {
	p := new(Condition_ConditionStatus)
	*p = x
	return p
}

func (x Condition_ConditionStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Condition_ConditionStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_common_common_proto_enumTypes[0].Descriptor()
}

func (Condition_ConditionStatus) Type() protoreflect.EnumType {
	return &file_common_common_proto_enumTypes[0]
}

func (x Condition_ConditionStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Condition_ConditionStatus.Descriptor instead.
func (Condition_ConditionStatus) EnumDescriptor() ([]byte, []int) {
	return file_common_common_proto_rawDescGZIP(), []int{12, 0}
}

type VolumeSpec_VolumeType int32

const (
	VolumeSpec_Dataset  VolumeSpec_VolumeType = 0
	VolumeSpec_CubeFs   VolumeSpec_VolumeType = 1
	VolumeSpec_CloudFs  VolumeSpec_VolumeType = 2
	VolumeSpec_HostPath VolumeSpec_VolumeType = 3
)

// Enum value maps for VolumeSpec_VolumeType.
var (
	VolumeSpec_VolumeType_name = map[int32]string{
		0: "Dataset",
		1: "CubeFs",
		2: "CloudFs",
		3: "HostPath",
	}
	VolumeSpec_VolumeType_value = map[string]int32{
		"Dataset":  0,
		"CubeFs":   1,
		"CloudFs":  2,
		"HostPath": 3,
	}
)

func (x VolumeSpec_VolumeType) Enum() *VolumeSpec_VolumeType {
	p := new(VolumeSpec_VolumeType)
	*p = x
	return p
}

func (x VolumeSpec_VolumeType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (VolumeSpec_VolumeType) Descriptor() protoreflect.EnumDescriptor {
	return file_common_common_proto_enumTypes[1].Descriptor()
}

func (VolumeSpec_VolumeType) Type() protoreflect.EnumType {
	return &file_common_common_proto_enumTypes[1]
}

func (x VolumeSpec_VolumeType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use VolumeSpec_VolumeType.Descriptor instead.
func (VolumeSpec_VolumeType) EnumDescriptor() ([]byte, []int) {
	return file_common_common_proto_rawDescGZIP(), []int{15, 0}
}

type TimestampModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// google.protobuf.Timestamp createTimestamp = 1;
	// google.protobuf.Timestamp updateTimestamp = 2;
	CreateTime string `protobuf:"bytes,3,opt,name=createTime,proto3" json:"createTime,omitempty"`
	UpdateTime string `protobuf:"bytes,4,opt,name=updateTime,proto3" json:"updateTime,omitempty"`
}

func (x *TimestampModel) Reset() {
	*x = TimestampModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_common_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TimestampModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TimestampModel) ProtoMessage() {}

func (x *TimestampModel) ProtoReflect() protoreflect.Message {
	mi := &file_common_common_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TimestampModel.ProtoReflect.Descriptor instead.
func (*TimestampModel) Descriptor() ([]byte, []int) {
	return file_common_common_proto_rawDescGZIP(), []int{0}
}

func (x *TimestampModel) GetCreateTime() string {
	if x != nil {
		return x.CreateTime
	}
	return ""
}

func (x *TimestampModel) GetUpdateTime() string {
	if x != nil {
		return x.UpdateTime
	}
	return ""
}

type CPUUsage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TotalNum                  float64 `protobuf:"fixed64,1,opt,name=totalNum,proto3" json:"totalNum,omitempty"`
	RequestsAllocatedQuantity float64 `protobuf:"fixed64,2,opt,name=requestsAllocatedQuantity,proto3" json:"requestsAllocatedQuantity,omitempty"` //请求量
	RequestsAllocatedRate     float64 `protobuf:"fixed64,3,opt,name=requestsAllocatedRate,proto3" json:"requestsAllocatedRate,omitempty"`
	LimitsAllocatedQuantity   float64 `protobuf:"fixed64,4,opt,name=limitsAllocatedQuantity,proto3" json:"limitsAllocatedQuantity,omitempty"` //限制量
	LimitsAllocatedRate       float64 `protobuf:"fixed64,5,opt,name=limitsAllocatedRate,proto3" json:"limitsAllocatedRate,omitempty"`
}

func (x *CPUUsage) Reset() {
	*x = CPUUsage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_common_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CPUUsage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CPUUsage) ProtoMessage() {}

func (x *CPUUsage) ProtoReflect() protoreflect.Message {
	mi := &file_common_common_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CPUUsage.ProtoReflect.Descriptor instead.
func (*CPUUsage) Descriptor() ([]byte, []int) {
	return file_common_common_proto_rawDescGZIP(), []int{1}
}

func (x *CPUUsage) GetTotalNum() float64 {
	if x != nil {
		return x.TotalNum
	}
	return 0
}

func (x *CPUUsage) GetRequestsAllocatedQuantity() float64 {
	if x != nil {
		return x.RequestsAllocatedQuantity
	}
	return 0
}

func (x *CPUUsage) GetRequestsAllocatedRate() float64 {
	if x != nil {
		return x.RequestsAllocatedRate
	}
	return 0
}

func (x *CPUUsage) GetLimitsAllocatedQuantity() float64 {
	if x != nil {
		return x.LimitsAllocatedQuantity
	}
	return 0
}

func (x *CPUUsage) GetLimitsAllocatedRate() float64 {
	if x != nil {
		return x.LimitsAllocatedRate
	}
	return 0
}

type MemoryUsage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total                     float64 `protobuf:"fixed64,1,opt,name=total,proto3" json:"total,omitempty"`                                         //总量, GiB
	RequestsAllocatedQuantity float64 `protobuf:"fixed64,2,opt,name=requestsAllocatedQuantity,proto3" json:"requestsAllocatedQuantity,omitempty"` //请求量
	RequestsAllocatedRate     float64 `protobuf:"fixed64,3,opt,name=requestsAllocatedRate,proto3" json:"requestsAllocatedRate,omitempty"`
	LimitsAllocatedQuantity   float64 `protobuf:"fixed64,4,opt,name=limitsAllocatedQuantity,proto3" json:"limitsAllocatedQuantity,omitempty"` //限制量
	LimitsAllocatedRate       float64 `protobuf:"fixed64,5,opt,name=limitsAllocatedRate,proto3" json:"limitsAllocatedRate,omitempty"`
}

func (x *MemoryUsage) Reset() {
	*x = MemoryUsage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_common_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MemoryUsage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MemoryUsage) ProtoMessage() {}

func (x *MemoryUsage) ProtoReflect() protoreflect.Message {
	mi := &file_common_common_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MemoryUsage.ProtoReflect.Descriptor instead.
func (*MemoryUsage) Descriptor() ([]byte, []int) {
	return file_common_common_proto_rawDescGZIP(), []int{2}
}

func (x *MemoryUsage) GetTotal() float64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *MemoryUsage) GetRequestsAllocatedQuantity() float64 {
	if x != nil {
		return x.RequestsAllocatedQuantity
	}
	return 0
}

func (x *MemoryUsage) GetRequestsAllocatedRate() float64 {
	if x != nil {
		return x.RequestsAllocatedRate
	}
	return 0
}

func (x *MemoryUsage) GetLimitsAllocatedQuantity() float64 {
	if x != nil {
		return x.LimitsAllocatedQuantity
	}
	return 0
}

func (x *MemoryUsage) GetLimitsAllocatedRate() float64 {
	if x != nil {
		return x.LimitsAllocatedRate
	}
	return 0
}

type GPUUsage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TotalNum                  float64 `protobuf:"fixed64,1,opt,name=totalNum,proto3" json:"totalNum,omitempty"`
	RequestsAllocatedQuantity float64 `protobuf:"fixed64,2,opt,name=requestsAllocatedQuantity,proto3" json:"requestsAllocatedQuantity,omitempty"` //请求量
	RequestsAllocatedRate     float64 `protobuf:"fixed64,3,opt,name=requestsAllocatedRate,proto3" json:"requestsAllocatedRate,omitempty"`
	LimitsAllocatedQuantity   float64 `protobuf:"fixed64,4,opt,name=limitsAllocatedQuantity,proto3" json:"limitsAllocatedQuantity,omitempty"` //限制量
	LimitsAllocatedRate       float64 `protobuf:"fixed64,5,opt,name=limitsAllocatedRate,proto3" json:"limitsAllocatedRate,omitempty"`
}

func (x *GPUUsage) Reset() {
	*x = GPUUsage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_common_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GPUUsage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GPUUsage) ProtoMessage() {}

func (x *GPUUsage) ProtoReflect() protoreflect.Message {
	mi := &file_common_common_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GPUUsage.ProtoReflect.Descriptor instead.
func (*GPUUsage) Descriptor() ([]byte, []int) {
	return file_common_common_proto_rawDescGZIP(), []int{3}
}

func (x *GPUUsage) GetTotalNum() float64 {
	if x != nil {
		return x.TotalNum
	}
	return 0
}

func (x *GPUUsage) GetRequestsAllocatedQuantity() float64 {
	if x != nil {
		return x.RequestsAllocatedQuantity
	}
	return 0
}

func (x *GPUUsage) GetRequestsAllocatedRate() float64 {
	if x != nil {
		return x.RequestsAllocatedRate
	}
	return 0
}

func (x *GPUUsage) GetLimitsAllocatedQuantity() float64 {
	if x != nil {
		return x.LimitsAllocatedQuantity
	}
	return 0
}

func (x *GPUUsage) GetLimitsAllocatedRate() float64 {
	if x != nil {
		return x.LimitsAllocatedRate
	}
	return 0
}

type NetIOUsage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ReceivedMiBPerSecond float64 `protobuf:"fixed64,1,opt,name=receivedMiBPerSecond,proto3" json:"receivedMiBPerSecond,omitempty"`
	TransmitMiBPerSecond float64 `protobuf:"fixed64,2,opt,name=transmitMiBPerSecond,proto3" json:"transmitMiBPerSecond,omitempty"`
}

func (x *NetIOUsage) Reset() {
	*x = NetIOUsage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_common_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NetIOUsage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NetIOUsage) ProtoMessage() {}

func (x *NetIOUsage) ProtoReflect() protoreflect.Message {
	mi := &file_common_common_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NetIOUsage.ProtoReflect.Descriptor instead.
func (*NetIOUsage) Descriptor() ([]byte, []int) {
	return file_common_common_proto_rawDescGZIP(), []int{4}
}

func (x *NetIOUsage) GetReceivedMiBPerSecond() float64 {
	if x != nil {
		return x.ReceivedMiBPerSecond
	}
	return 0
}

func (x *NetIOUsage) GetTransmitMiBPerSecond() float64 {
	if x != nil {
		return x.TransmitMiBPerSecond
	}
	return 0
}

type DiskIOUsage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ReceivedMiBPerSecond float64 `protobuf:"fixed64,1,opt,name=receivedMiBPerSecond,proto3" json:"receivedMiBPerSecond,omitempty"`
	TransmitMiBPerSecond float64 `protobuf:"fixed64,2,opt,name=transmitMiBPerSecond,proto3" json:"transmitMiBPerSecond,omitempty"`
}

func (x *DiskIOUsage) Reset() {
	*x = DiskIOUsage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_common_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DiskIOUsage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DiskIOUsage) ProtoMessage() {}

func (x *DiskIOUsage) ProtoReflect() protoreflect.Message {
	mi := &file_common_common_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DiskIOUsage.ProtoReflect.Descriptor instead.
func (*DiskIOUsage) Descriptor() ([]byte, []int) {
	return file_common_common_proto_rawDescGZIP(), []int{5}
}

func (x *DiskIOUsage) GetReceivedMiBPerSecond() float64 {
	if x != nil {
		return x.ReceivedMiBPerSecond
	}
	return 0
}

func (x *DiskIOUsage) GetTransmitMiBPerSecond() float64 {
	if x != nil {
		return x.TransmitMiBPerSecond
	}
	return 0
}

type ResourceCapacity struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CPUNum string `protobuf:"bytes,1,opt,name=CPUNum,proto3" json:"CPUNum,omitempty"` //CPU数量
	Memory string `protobuf:"bytes,2,opt,name=Memory,proto3" json:"Memory,omitempty"` //内存大小
	GPUNum string `protobuf:"bytes,4,opt,name=GPUNum,proto3" json:"GPUNum,omitempty"` //GPU数量
	GpuMem string `protobuf:"bytes,5,opt,name=GpuMem,proto3" json:"GpuMem,omitempty"` //单卡GPU共享显存
}

func (x *ResourceCapacity) Reset() {
	*x = ResourceCapacity{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_common_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResourceCapacity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResourceCapacity) ProtoMessage() {}

func (x *ResourceCapacity) ProtoReflect() protoreflect.Message {
	mi := &file_common_common_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResourceCapacity.ProtoReflect.Descriptor instead.
func (*ResourceCapacity) Descriptor() ([]byte, []int) {
	return file_common_common_proto_rawDescGZIP(), []int{6}
}

func (x *ResourceCapacity) GetCPUNum() string {
	if x != nil {
		return x.CPUNum
	}
	return ""
}

func (x *ResourceCapacity) GetMemory() string {
	if x != nil {
		return x.Memory
	}
	return ""
}

func (x *ResourceCapacity) GetGPUNum() string {
	if x != nil {
		return x.GPUNum
	}
	return ""
}

func (x *ResourceCapacity) GetGpuMem() string {
	if x != nil {
		return x.GpuMem
	}
	return ""
}

type ResourceAllocatable struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CPUNum string `protobuf:"bytes,1,opt,name=CPUNum,proto3" json:"CPUNum,omitempty"` //CPU数量
	Memory string `protobuf:"bytes,2,opt,name=Memory,proto3" json:"Memory,omitempty"` //内存大小
	GPUNum string `protobuf:"bytes,4,opt,name=GPUNum,proto3" json:"GPUNum,omitempty"` //GPU数量
	GpuMem string `protobuf:"bytes,5,opt,name=GpuMem,proto3" json:"GpuMem,omitempty"` //单卡最大GPU共享显存剩余量
}

func (x *ResourceAllocatable) Reset() {
	*x = ResourceAllocatable{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_common_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResourceAllocatable) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResourceAllocatable) ProtoMessage() {}

func (x *ResourceAllocatable) ProtoReflect() protoreflect.Message {
	mi := &file_common_common_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResourceAllocatable.ProtoReflect.Descriptor instead.
func (*ResourceAllocatable) Descriptor() ([]byte, []int) {
	return file_common_common_proto_rawDescGZIP(), []int{7}
}

func (x *ResourceAllocatable) GetCPUNum() string {
	if x != nil {
		return x.CPUNum
	}
	return ""
}

func (x *ResourceAllocatable) GetMemory() string {
	if x != nil {
		return x.Memory
	}
	return ""
}

func (x *ResourceAllocatable) GetGPUNum() string {
	if x != nil {
		return x.GPUNum
	}
	return ""
}

func (x *ResourceAllocatable) GetGpuMem() string {
	if x != nil {
		return x.GpuMem
	}
	return ""
}

type NodeSummary struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ip          string       `protobuf:"bytes,1,opt,name=ip,proto3" json:"ip,omitempty"`
	CPUUsage    *CPUUsage    `protobuf:"bytes,2,opt,name=CPUUsage,proto3" json:"CPUUsage,omitempty"`
	GPUUsage    *GPUUsage    `protobuf:"bytes,3,opt,name=GPUUsage,proto3" json:"GPUUsage,omitempty"`
	MemoryUsage *MemoryUsage `protobuf:"bytes,4,opt,name=memoryUsage,proto3" json:"memoryUsage,omitempty"`
	NetIOUsage  *NetIOUsage  `protobuf:"bytes,5,opt,name=netIOUsage,proto3" json:"netIOUsage,omitempty"`
	DiskIOUsage *DiskIOUsage `protobuf:"bytes,6,opt,name=diskIOUsage,proto3" json:"diskIOUsage,omitempty"`
}

func (x *NodeSummary) Reset() {
	*x = NodeSummary{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_common_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NodeSummary) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NodeSummary) ProtoMessage() {}

func (x *NodeSummary) ProtoReflect() protoreflect.Message {
	mi := &file_common_common_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NodeSummary.ProtoReflect.Descriptor instead.
func (*NodeSummary) Descriptor() ([]byte, []int) {
	return file_common_common_proto_rawDescGZIP(), []int{8}
}

func (x *NodeSummary) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *NodeSummary) GetCPUUsage() *CPUUsage {
	if x != nil {
		return x.CPUUsage
	}
	return nil
}

func (x *NodeSummary) GetGPUUsage() *GPUUsage {
	if x != nil {
		return x.GPUUsage
	}
	return nil
}

func (x *NodeSummary) GetMemoryUsage() *MemoryUsage {
	if x != nil {
		return x.MemoryUsage
	}
	return nil
}

func (x *NodeSummary) GetNetIOUsage() *NetIOUsage {
	if x != nil {
		return x.NetIOUsage
	}
	return nil
}

func (x *NodeSummary) GetDiskIOUsage() *DiskIOUsage {
	if x != nil {
		return x.DiskIOUsage
	}
	return nil
}

type QueueSummary struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Capacity    *ResourceCapacity    `protobuf:"bytes,1,opt,name=capacity,proto3" json:"capacity,omitempty"`       //队列的资源容量
	Allocatable *ResourceAllocatable `protobuf:"bytes,2,opt,name=allocatable,proto3" json:"allocatable,omitempty"` //队列的资源可分配
	InQueue     int32                `protobuf:"varint,3,opt,name=inQueue,proto3" json:"inQueue,omitempty"`        //队列中的任务数
	Running     int32                `protobuf:"varint,4,opt,name=running,proto3" json:"running,omitempty"`        //队列中运行的任务数
	Pending     int32                `protobuf:"varint,5,opt,name=pending,proto3" json:"pending,omitempty"`        //队列中等待的任务数
	Completed   int32                `protobuf:"varint,6,opt,name=completed,proto3" json:"completed,omitempty"`    //队列中完成的任务数
	Unknown     int32                `protobuf:"varint,7,opt,name=unknown,proto3" json:"unknown,omitempty"`        //队列中未知的任务数
}

func (x *QueueSummary) Reset() {
	*x = QueueSummary{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_common_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueueSummary) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueueSummary) ProtoMessage() {}

func (x *QueueSummary) ProtoReflect() protoreflect.Message {
	mi := &file_common_common_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueueSummary.ProtoReflect.Descriptor instead.
func (*QueueSummary) Descriptor() ([]byte, []int) {
	return file_common_common_proto_rawDescGZIP(), []int{9}
}

func (x *QueueSummary) GetCapacity() *ResourceCapacity {
	if x != nil {
		return x.Capacity
	}
	return nil
}

func (x *QueueSummary) GetAllocatable() *ResourceAllocatable {
	if x != nil {
		return x.Allocatable
	}
	return nil
}

func (x *QueueSummary) GetInQueue() int32 {
	if x != nil {
		return x.InQueue
	}
	return 0
}

func (x *QueueSummary) GetRunning() int32 {
	if x != nil {
		return x.Running
	}
	return 0
}

func (x *QueueSummary) GetPending() int32 {
	if x != nil {
		return x.Pending
	}
	return 0
}

func (x *QueueSummary) GetCompleted() int32 {
	if x != nil {
		return x.Completed
	}
	return 0
}

func (x *QueueSummary) GetUnknown() int32 {
	if x != nil {
		return x.Unknown
	}
	return 0
}

type ResourceMetric struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResourceType string `protobuf:"bytes,1,opt,name=resourceType,proto3" json:"resourceType,omitempty"` // 类型这里加两个类型 nvidia.com/gpumem 和 nvidia.com/gpucores
	Allocatable  string `protobuf:"bytes,2,opt,name=allocatable,proto3" json:"allocatable,omitempty"`
	Request      string `protobuf:"bytes,3,opt,name=request,proto3" json:"request,omitempty"`
	Limit        string `protobuf:"bytes,4,opt,name=limit,proto3" json:"limit,omitempty"`
	Capability   string `protobuf:"bytes,5,opt,name=capability,proto3" json:"capability,omitempty"`
	Remaining    string `protobuf:"bytes,6,opt,name=remaining,proto3" json:"remaining,omitempty"`
	Unavailable  string `protobuf:"bytes,7,opt,name=unavailable,proto3" json:"unavailable,omitempty"`
}

func (x *ResourceMetric) Reset() {
	*x = ResourceMetric{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_common_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResourceMetric) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResourceMetric) ProtoMessage() {}

func (x *ResourceMetric) ProtoReflect() protoreflect.Message {
	mi := &file_common_common_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResourceMetric.ProtoReflect.Descriptor instead.
func (*ResourceMetric) Descriptor() ([]byte, []int) {
	return file_common_common_proto_rawDescGZIP(), []int{10}
}

func (x *ResourceMetric) GetResourceType() string {
	if x != nil {
		return x.ResourceType
	}
	return ""
}

func (x *ResourceMetric) GetAllocatable() string {
	if x != nil {
		return x.Allocatable
	}
	return ""
}

func (x *ResourceMetric) GetRequest() string {
	if x != nil {
		return x.Request
	}
	return ""
}

func (x *ResourceMetric) GetLimit() string {
	if x != nil {
		return x.Limit
	}
	return ""
}

func (x *ResourceMetric) GetCapability() string {
	if x != nil {
		return x.Capability
	}
	return ""
}

func (x *ResourceMetric) GetRemaining() string {
	if x != nil {
		return x.Remaining
	}
	return ""
}

func (x *ResourceMetric) GetUnavailable() string {
	if x != nil {
		return x.Unavailable
	}
	return ""
}

type NodeGroupResourceMetric struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name         string          `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	CpuMetric    *ResourceMetric `protobuf:"bytes,2,opt,name=cpuMetric,proto3" json:"cpuMetric,omitempty"`
	MemoryMetric *ResourceMetric `protobuf:"bytes,3,opt,name=memoryMetric,proto3" json:"memoryMetric,omitempty"`
	GpuMetric    *ResourceMetric `protobuf:"bytes,4,opt,name=gpuMetric,proto3" json:"gpuMetric,omitempty"`
	GpuMemMetric *ResourceMetric `protobuf:"bytes,5,opt,name=gpuMemMetric,proto3" json:"gpuMemMetric,omitempty"`
}

func (x *NodeGroupResourceMetric) Reset() {
	*x = NodeGroupResourceMetric{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_common_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NodeGroupResourceMetric) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NodeGroupResourceMetric) ProtoMessage() {}

func (x *NodeGroupResourceMetric) ProtoReflect() protoreflect.Message {
	mi := &file_common_common_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NodeGroupResourceMetric.ProtoReflect.Descriptor instead.
func (*NodeGroupResourceMetric) Descriptor() ([]byte, []int) {
	return file_common_common_proto_rawDescGZIP(), []int{11}
}

func (x *NodeGroupResourceMetric) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *NodeGroupResourceMetric) GetCpuMetric() *ResourceMetric {
	if x != nil {
		return x.CpuMetric
	}
	return nil
}

func (x *NodeGroupResourceMetric) GetMemoryMetric() *ResourceMetric {
	if x != nil {
		return x.MemoryMetric
	}
	return nil
}

func (x *NodeGroupResourceMetric) GetGpuMetric() *ResourceMetric {
	if x != nil {
		return x.GpuMetric
	}
	return nil
}

func (x *NodeGroupResourceMetric) GetGpuMemMetric() *ResourceMetric {
	if x != nil {
		return x.GpuMemMetric
	}
	return nil
}

type Condition struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type               string                    `protobuf:"bytes,1,opt,name=type,proto3" json:"type,omitempty"`
	Status             Condition_ConditionStatus `protobuf:"varint,2,opt,name=status,proto3,enum=apis.common.Condition_ConditionStatus" json:"status,omitempty"`
	LastTransitionTime string                    `protobuf:"bytes,3,opt,name=lastTransitionTime,proto3" json:"lastTransitionTime,omitempty"`
	Reason             string                    `protobuf:"bytes,4,opt,name=reason,proto3" json:"reason,omitempty"`
	Message            string                    `protobuf:"bytes,5,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *Condition) Reset() {
	*x = Condition{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_common_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Condition) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Condition) ProtoMessage() {}

func (x *Condition) ProtoReflect() protoreflect.Message {
	mi := &file_common_common_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Condition.ProtoReflect.Descriptor instead.
func (*Condition) Descriptor() ([]byte, []int) {
	return file_common_common_proto_rawDescGZIP(), []int{12}
}

func (x *Condition) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *Condition) GetStatus() Condition_ConditionStatus {
	if x != nil {
		return x.Status
	}
	return Condition_TRUE
}

func (x *Condition) GetLastTransitionTime() string {
	if x != nil {
		return x.LastTransitionTime
	}
	return ""
}

func (x *Condition) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *Condition) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type EnvVar struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name  string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Value string `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *EnvVar) Reset() {
	*x = EnvVar{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_common_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EnvVar) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnvVar) ProtoMessage() {}

func (x *EnvVar) ProtoReflect() protoreflect.Message {
	mi := &file_common_common_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnvVar.ProtoReflect.Descriptor instead.
func (*EnvVar) Descriptor() ([]byte, []int) {
	return file_common_common_proto_rawDescGZIP(), []int{13}
}

func (x *EnvVar) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *EnvVar) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

// 资源规格
type Specification struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CpuNum    int32 `protobuf:"varint,1,opt,name=cpuNum,proto3" json:"cpuNum,omitempty"`
	MemoryGiB int32 `protobuf:"varint,2,opt,name=memoryGiB,proto3" json:"memoryGiB,omitempty"`
	GpuNum    int32 `protobuf:"varint,3,opt,name=gpuNum,proto3" json:"gpuNum,omitempty"`
	GpuMem    int32 `protobuf:"varint,4,opt,name=gpuMem,proto3" json:"gpuMem,omitempty"` // GPU 共享显存，单位GB
}

func (x *Specification) Reset() {
	*x = Specification{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_common_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Specification) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Specification) ProtoMessage() {}

func (x *Specification) ProtoReflect() protoreflect.Message {
	mi := &file_common_common_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Specification.ProtoReflect.Descriptor instead.
func (*Specification) Descriptor() ([]byte, []int) {
	return file_common_common_proto_rawDescGZIP(), []int{14}
}

func (x *Specification) GetCpuNum() int32 {
	if x != nil {
		return x.CpuNum
	}
	return 0
}

func (x *Specification) GetMemoryGiB() int32 {
	if x != nil {
		return x.MemoryGiB
	}
	return 0
}

func (x *Specification) GetGpuNum() int32 {
	if x != nil {
		return x.GpuNum
	}
	return 0
}

func (x *Specification) GetGpuMem() int32 {
	if x != nil {
		return x.GpuMem
	}
	return 0
}

// 卷
type VolumeSpec struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type       VolumeSpec_VolumeType `protobuf:"varint,1,opt,name=type,proto3,enum=apis.common.VolumeSpec_VolumeType" json:"type,omitempty"`
	MountPoint string                `protobuf:"bytes,2,opt,name=mountPoint,proto3" json:"mountPoint,omitempty"`
	// Types that are assignable to VolumeProperties:
	//
	//	*VolumeSpec_DatasetVolumeProperties
	//	*VolumeSpec_CubeFSVolumeProperties
	//	*VolumeSpec_CloudFSVolumeProperties
	//	*VolumeSpec_HostPathVolumeProperties
	VolumeProperties isVolumeSpec_VolumeProperties `protobuf_oneof:"volumeProperties"`
}

func (x *VolumeSpec) Reset() {
	*x = VolumeSpec{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_common_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VolumeSpec) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VolumeSpec) ProtoMessage() {}

func (x *VolumeSpec) ProtoReflect() protoreflect.Message {
	mi := &file_common_common_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VolumeSpec.ProtoReflect.Descriptor instead.
func (*VolumeSpec) Descriptor() ([]byte, []int) {
	return file_common_common_proto_rawDescGZIP(), []int{15}
}

func (x *VolumeSpec) GetType() VolumeSpec_VolumeType {
	if x != nil {
		return x.Type
	}
	return VolumeSpec_Dataset
}

func (x *VolumeSpec) GetMountPoint() string {
	if x != nil {
		return x.MountPoint
	}
	return ""
}

func (m *VolumeSpec) GetVolumeProperties() isVolumeSpec_VolumeProperties {
	if m != nil {
		return m.VolumeProperties
	}
	return nil
}

func (x *VolumeSpec) GetDatasetVolumeProperties() *DatasetVolumeProperties {
	if x, ok := x.GetVolumeProperties().(*VolumeSpec_DatasetVolumeProperties); ok {
		return x.DatasetVolumeProperties
	}
	return nil
}

func (x *VolumeSpec) GetCubeFSVolumeProperties() *CubeFSVolumeProperties {
	if x, ok := x.GetVolumeProperties().(*VolumeSpec_CubeFSVolumeProperties); ok {
		return x.CubeFSVolumeProperties
	}
	return nil
}

func (x *VolumeSpec) GetCloudFSVolumeProperties() *CloudFSVolumeProperties {
	if x, ok := x.GetVolumeProperties().(*VolumeSpec_CloudFSVolumeProperties); ok {
		return x.CloudFSVolumeProperties
	}
	return nil
}

func (x *VolumeSpec) GetHostPathVolumeProperties() *HostPathVolumeProperties {
	if x, ok := x.GetVolumeProperties().(*VolumeSpec_HostPathVolumeProperties); ok {
		return x.HostPathVolumeProperties
	}
	return nil
}

type isVolumeSpec_VolumeProperties interface {
	isVolumeSpec_VolumeProperties()
}

type VolumeSpec_DatasetVolumeProperties struct {
	DatasetVolumeProperties *DatasetVolumeProperties `protobuf:"bytes,3,opt,name=datasetVolumeProperties,proto3,oneof"`
}

type VolumeSpec_CubeFSVolumeProperties struct {
	CubeFSVolumeProperties *CubeFSVolumeProperties `protobuf:"bytes,4,opt,name=cubeFSVolumeProperties,proto3,oneof"`
}

type VolumeSpec_CloudFSVolumeProperties struct {
	CloudFSVolumeProperties *CloudFSVolumeProperties `protobuf:"bytes,5,opt,name=cloudFSVolumeProperties,proto3,oneof"`
}

type VolumeSpec_HostPathVolumeProperties struct {
	HostPathVolumeProperties *HostPathVolumeProperties `protobuf:"bytes,6,opt,name=hostPathVolumeProperties,proto3,oneof"`
}

func (*VolumeSpec_DatasetVolumeProperties) isVolumeSpec_VolumeProperties() {}

func (*VolumeSpec_CubeFSVolumeProperties) isVolumeSpec_VolumeProperties() {}

func (*VolumeSpec_CloudFSVolumeProperties) isVolumeSpec_VolumeProperties() {}

func (*VolumeSpec_HostPathVolumeProperties) isVolumeSpec_VolumeProperties() {}

type CubeFSVolumeProperties struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	VolumeName string `protobuf:"bytes,1,opt,name=volumeName,proto3" json:"volumeName,omitempty"`
	SubPath    string `protobuf:"bytes,2,opt,name=subPath,proto3" json:"subPath,omitempty"`
}

func (x *CubeFSVolumeProperties) Reset() {
	*x = CubeFSVolumeProperties{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_common_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CubeFSVolumeProperties) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CubeFSVolumeProperties) ProtoMessage() {}

func (x *CubeFSVolumeProperties) ProtoReflect() protoreflect.Message {
	mi := &file_common_common_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CubeFSVolumeProperties.ProtoReflect.Descriptor instead.
func (*CubeFSVolumeProperties) Descriptor() ([]byte, []int) {
	return file_common_common_proto_rawDescGZIP(), []int{16}
}

func (x *CubeFSVolumeProperties) GetVolumeName() string {
	if x != nil {
		return x.VolumeName
	}
	return ""
}

func (x *CubeFSVolumeProperties) GetSubPath() string {
	if x != nil {
		return x.SubPath
	}
	return ""
}

type DatasetVolumeProperties struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DatasetName string `protobuf:"bytes,1,opt,name=datasetName,proto3" json:"datasetName,omitempty"`
	SubPath     string `protobuf:"bytes,2,opt,name=subPath,proto3" json:"subPath,omitempty"`
}

func (x *DatasetVolumeProperties) Reset() {
	*x = DatasetVolumeProperties{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_common_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DatasetVolumeProperties) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DatasetVolumeProperties) ProtoMessage() {}

func (x *DatasetVolumeProperties) ProtoReflect() protoreflect.Message {
	mi := &file_common_common_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DatasetVolumeProperties.ProtoReflect.Descriptor instead.
func (*DatasetVolumeProperties) Descriptor() ([]byte, []int) {
	return file_common_common_proto_rawDescGZIP(), []int{17}
}

func (x *DatasetVolumeProperties) GetDatasetName() string {
	if x != nil {
		return x.DatasetName
	}
	return ""
}

func (x *DatasetVolumeProperties) GetSubPath() string {
	if x != nil {
		return x.SubPath
	}
	return ""
}

type CloudFSVolumeProperties struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	VolumeName string `protobuf:"bytes,1,opt,name=volumeName,proto3" json:"volumeName,omitempty"`
	SubPath    string `protobuf:"bytes,2,opt,name=subPath,proto3" json:"subPath,omitempty"`
}

func (x *CloudFSVolumeProperties) Reset() {
	*x = CloudFSVolumeProperties{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_common_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CloudFSVolumeProperties) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CloudFSVolumeProperties) ProtoMessage() {}

func (x *CloudFSVolumeProperties) ProtoReflect() protoreflect.Message {
	mi := &file_common_common_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CloudFSVolumeProperties.ProtoReflect.Descriptor instead.
func (*CloudFSVolumeProperties) Descriptor() ([]byte, []int) {
	return file_common_common_proto_rawDescGZIP(), []int{18}
}

func (x *CloudFSVolumeProperties) GetVolumeName() string {
	if x != nil {
		return x.VolumeName
	}
	return ""
}

func (x *CloudFSVolumeProperties) GetSubPath() string {
	if x != nil {
		return x.SubPath
	}
	return ""
}

type HostPathVolumeProperties struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HostPath string `protobuf:"bytes,1,opt,name=hostPath,proto3" json:"hostPath,omitempty"` //主机路径
	SubPath  string `protobuf:"bytes,2,opt,name=subPath,proto3" json:"subPath,omitempty"`   //子路径
}

func (x *HostPathVolumeProperties) Reset() {
	*x = HostPathVolumeProperties{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_common_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HostPathVolumeProperties) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HostPathVolumeProperties) ProtoMessage() {}

func (x *HostPathVolumeProperties) ProtoReflect() protoreflect.Message {
	mi := &file_common_common_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HostPathVolumeProperties.ProtoReflect.Descriptor instead.
func (*HostPathVolumeProperties) Descriptor() ([]byte, []int) {
	return file_common_common_proto_rawDescGZIP(), []int{19}
}

func (x *HostPathVolumeProperties) GetHostPath() string {
	if x != nil {
		return x.HostPath
	}
	return ""
}

func (x *HostPathVolumeProperties) GetSubPath() string {
	if x != nil {
		return x.SubPath
	}
	return ""
}

type Port struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PortName string `protobuf:"bytes,1,opt,name=portName,proto3" json:"portName,omitempty"`
	Port     int32  `protobuf:"varint,2,opt,name=port,proto3" json:"port,omitempty"`
	Protocol string `protobuf:"bytes,3,opt,name=protocol,proto3" json:"protocol,omitempty"`
}

func (x *Port) Reset() {
	*x = Port{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_common_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Port) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Port) ProtoMessage() {}

func (x *Port) ProtoReflect() protoreflect.Message {
	mi := &file_common_common_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Port.ProtoReflect.Descriptor instead.
func (*Port) Descriptor() ([]byte, []int) {
	return file_common_common_proto_rawDescGZIP(), []int{20}
}

func (x *Port) GetPortName() string {
	if x != nil {
		return x.PortName
	}
	return ""
}

func (x *Port) GetPort() int32 {
	if x != nil {
		return x.Port
	}
	return 0
}

func (x *Port) GetProtocol() string {
	if x != nil {
		return x.Protocol
	}
	return ""
}

var File_common_common_proto protoreflect.FileDescriptor

var file_common_common_proto_rawDesc = []byte{
	0x0a, 0x13, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0b, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0x50, 0x0a, 0x0e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x86, 0x02, 0x0a, 0x08, 0x43, 0x50, 0x55, 0x55, 0x73, 0x61,
	0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x4e, 0x75, 0x6d, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x01, 0x52, 0x08, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x4e, 0x75, 0x6d, 0x12, 0x3c,
	0x0a, 0x19, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x41, 0x6c, 0x6c, 0x6f, 0x63, 0x61,
	0x74, 0x65, 0x64, 0x51, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x01, 0x52, 0x19, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x41, 0x6c, 0x6c, 0x6f, 0x63,
	0x61, 0x74, 0x65, 0x64, 0x51, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x12, 0x34, 0x0a, 0x15,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x41, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x65,
	0x64, 0x52, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x52, 0x15, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x73, 0x41, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x65, 0x64, 0x52, 0x61,
	0x74, 0x65, 0x12, 0x38, 0x0a, 0x17, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x41, 0x6c, 0x6c, 0x6f,
	0x63, 0x61, 0x74, 0x65, 0x64, 0x51, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x01, 0x52, 0x17, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x41, 0x6c, 0x6c, 0x6f, 0x63,
	0x61, 0x74, 0x65, 0x64, 0x51, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x12, 0x30, 0x0a, 0x13,
	0x6c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x41, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x65, 0x64, 0x52,
	0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x01, 0x52, 0x13, 0x6c, 0x69, 0x6d, 0x69, 0x74,
	0x73, 0x41, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x65, 0x64, 0x52, 0x61, 0x74, 0x65, 0x22, 0x83,
	0x02, 0x0a, 0x0b, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x55, 0x73, 0x61, 0x67, 0x65, 0x12, 0x14,
	0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x52, 0x05, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x12, 0x3c, 0x0a, 0x19, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73,
	0x41, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x65, 0x64, 0x51, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74,
	0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x19, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x73, 0x41, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x65, 0x64, 0x51, 0x75, 0x61, 0x6e, 0x74, 0x69,
	0x74, 0x79, 0x12, 0x34, 0x0a, 0x15, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x41, 0x6c,
	0x6c, 0x6f, 0x63, 0x61, 0x74, 0x65, 0x64, 0x52, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x01, 0x52, 0x15, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x41, 0x6c, 0x6c, 0x6f, 0x63,
	0x61, 0x74, 0x65, 0x64, 0x52, 0x61, 0x74, 0x65, 0x12, 0x38, 0x0a, 0x17, 0x6c, 0x69, 0x6d, 0x69,
	0x74, 0x73, 0x41, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x65, 0x64, 0x51, 0x75, 0x61, 0x6e, 0x74,
	0x69, 0x74, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x01, 0x52, 0x17, 0x6c, 0x69, 0x6d, 0x69, 0x74,
	0x73, 0x41, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x65, 0x64, 0x51, 0x75, 0x61, 0x6e, 0x74, 0x69,
	0x74, 0x79, 0x12, 0x30, 0x0a, 0x13, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x41, 0x6c, 0x6c, 0x6f,
	0x63, 0x61, 0x74, 0x65, 0x64, 0x52, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x13, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x41, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x65, 0x64,
	0x52, 0x61, 0x74, 0x65, 0x22, 0x86, 0x02, 0x0a, 0x08, 0x47, 0x50, 0x55, 0x55, 0x73, 0x61, 0x67,
	0x65, 0x12, 0x1a, 0x0a, 0x08, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x4e, 0x75, 0x6d, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x01, 0x52, 0x08, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x4e, 0x75, 0x6d, 0x12, 0x3c, 0x0a,
	0x19, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x41, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74,
	0x65, 0x64, 0x51, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x19, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x41, 0x6c, 0x6c, 0x6f, 0x63, 0x61,
	0x74, 0x65, 0x64, 0x51, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x12, 0x34, 0x0a, 0x15, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x41, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x65, 0x64,
	0x52, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x52, 0x15, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x73, 0x41, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x65, 0x64, 0x52, 0x61, 0x74,
	0x65, 0x12, 0x38, 0x0a, 0x17, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x41, 0x6c, 0x6c, 0x6f, 0x63,
	0x61, 0x74, 0x65, 0x64, 0x51, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x01, 0x52, 0x17, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x41, 0x6c, 0x6c, 0x6f, 0x63, 0x61,
	0x74, 0x65, 0x64, 0x51, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x12, 0x30, 0x0a, 0x13, 0x6c,
	0x69, 0x6d, 0x69, 0x74, 0x73, 0x41, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x65, 0x64, 0x52, 0x61,
	0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x01, 0x52, 0x13, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x73,
	0x41, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x65, 0x64, 0x52, 0x61, 0x74, 0x65, 0x22, 0x74, 0x0a,
	0x0a, 0x4e, 0x65, 0x74, 0x49, 0x4f, 0x55, 0x73, 0x61, 0x67, 0x65, 0x12, 0x32, 0x0a, 0x14, 0x72,
	0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x64, 0x4d, 0x69, 0x42, 0x50, 0x65, 0x72, 0x53, 0x65, 0x63,
	0x6f, 0x6e, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x52, 0x14, 0x72, 0x65, 0x63, 0x65, 0x69,
	0x76, 0x65, 0x64, 0x4d, 0x69, 0x42, 0x50, 0x65, 0x72, 0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x12,
	0x32, 0x0a, 0x14, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x6d, 0x69, 0x74, 0x4d, 0x69, 0x42, 0x50, 0x65,
	0x72, 0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x14, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x6d, 0x69, 0x74, 0x4d, 0x69, 0x42, 0x50, 0x65, 0x72, 0x53, 0x65, 0x63,
	0x6f, 0x6e, 0x64, 0x22, 0x75, 0x0a, 0x0b, 0x44, 0x69, 0x73, 0x6b, 0x49, 0x4f, 0x55, 0x73, 0x61,
	0x67, 0x65, 0x12, 0x32, 0x0a, 0x14, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x64, 0x4d, 0x69,
	0x42, 0x50, 0x65, 0x72, 0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x14, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x64, 0x4d, 0x69, 0x42, 0x50, 0x65, 0x72,
	0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x12, 0x32, 0x0a, 0x14, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x6d,
	0x69, 0x74, 0x4d, 0x69, 0x42, 0x50, 0x65, 0x72, 0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x01, 0x52, 0x14, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x6d, 0x69, 0x74, 0x4d, 0x69,
	0x42, 0x50, 0x65, 0x72, 0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x22, 0x72, 0x0a, 0x10, 0x52, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x43, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x12, 0x16,
	0x0a, 0x06, 0x43, 0x50, 0x55, 0x4e, 0x75, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x43, 0x50, 0x55, 0x4e, 0x75, 0x6d, 0x12, 0x16, 0x0a, 0x06, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x12, 0x16,
	0x0a, 0x06, 0x47, 0x50, 0x55, 0x4e, 0x75, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x47, 0x50, 0x55, 0x4e, 0x75, 0x6d, 0x12, 0x16, 0x0a, 0x06, 0x47, 0x70, 0x75, 0x4d, 0x65, 0x6d,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x47, 0x70, 0x75, 0x4d, 0x65, 0x6d, 0x22, 0x75,
	0x0a, 0x13, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x41, 0x6c, 0x6c, 0x6f, 0x63, 0x61,
	0x74, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x43, 0x50, 0x55, 0x4e, 0x75, 0x6d, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x43, 0x50, 0x55, 0x4e, 0x75, 0x6d, 0x12, 0x16, 0x0a,
	0x06, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x4d,
	0x65, 0x6d, 0x6f, 0x72, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x47, 0x50, 0x55, 0x4e, 0x75, 0x6d, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x47, 0x50, 0x55, 0x4e, 0x75, 0x6d, 0x12, 0x16, 0x0a,
	0x06, 0x47, 0x70, 0x75, 0x4d, 0x65, 0x6d, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x47,
	0x70, 0x75, 0x4d, 0x65, 0x6d, 0x22, 0xb4, 0x02, 0x0a, 0x0b, 0x4e, 0x6f, 0x64, 0x65, 0x53, 0x75,
	0x6d, 0x6d, 0x61, 0x72, 0x79, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x02, 0x69, 0x70, 0x12, 0x31, 0x0a, 0x08, 0x43, 0x50, 0x55, 0x55, 0x73, 0x61, 0x67,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x43, 0x50, 0x55, 0x55, 0x73, 0x61, 0x67, 0x65, 0x52, 0x08,
	0x43, 0x50, 0x55, 0x55, 0x73, 0x61, 0x67, 0x65, 0x12, 0x31, 0x0a, 0x08, 0x47, 0x50, 0x55, 0x55,
	0x73, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x61, 0x70, 0x69,
	0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x47, 0x50, 0x55, 0x55, 0x73, 0x61, 0x67,
	0x65, 0x52, 0x08, 0x47, 0x50, 0x55, 0x55, 0x73, 0x61, 0x67, 0x65, 0x12, 0x3a, 0x0a, 0x0b, 0x6d,
	0x65, 0x6d, 0x6f, 0x72, 0x79, 0x55, 0x73, 0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4d,
	0x65, 0x6d, 0x6f, 0x72, 0x79, 0x55, 0x73, 0x61, 0x67, 0x65, 0x52, 0x0b, 0x6d, 0x65, 0x6d, 0x6f,
	0x72, 0x79, 0x55, 0x73, 0x61, 0x67, 0x65, 0x12, 0x37, 0x0a, 0x0a, 0x6e, 0x65, 0x74, 0x49, 0x4f,
	0x55, 0x73, 0x61, 0x67, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x61, 0x70,
	0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4e, 0x65, 0x74, 0x49, 0x4f, 0x55,
	0x73, 0x61, 0x67, 0x65, 0x52, 0x0a, 0x6e, 0x65, 0x74, 0x49, 0x4f, 0x55, 0x73, 0x61, 0x67, 0x65,
	0x12, 0x3a, 0x0a, 0x0b, 0x64, 0x69, 0x73, 0x6b, 0x49, 0x4f, 0x55, 0x73, 0x61, 0x67, 0x65, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x44, 0x69, 0x73, 0x6b, 0x49, 0x4f, 0x55, 0x73, 0x61, 0x67, 0x65, 0x52,
	0x0b, 0x64, 0x69, 0x73, 0x6b, 0x49, 0x4f, 0x55, 0x73, 0x61, 0x67, 0x65, 0x22, 0x93, 0x02, 0x0a,
	0x0c, 0x51, 0x75, 0x65, 0x75, 0x65, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x12, 0x39, 0x0a,
	0x08, 0x63, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1d, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x43, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x52, 0x08,
	0x63, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x12, 0x42, 0x0a, 0x0b, 0x61, 0x6c, 0x6c, 0x6f,
	0x63, 0x61, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e,
	0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x41, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x52,
	0x0b, 0x61, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x18, 0x0a, 0x07,
	0x69, 0x6e, 0x51, 0x75, 0x65, 0x75, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x69,
	0x6e, 0x51, 0x75, 0x65, 0x75, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x75, 0x6e, 0x6e, 0x69, 0x6e,
	0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x72, 0x75, 0x6e, 0x6e, 0x69, 0x6e, 0x67,
	0x12, 0x18, 0x0a, 0x07, 0x70, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x07, 0x70, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x6f,
	0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x63,
	0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x75, 0x6e, 0x6b, 0x6e,
	0x6f, 0x77, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x75, 0x6e, 0x6b, 0x6e, 0x6f,
	0x77, 0x6e, 0x22, 0xe6, 0x01, 0x0a, 0x0e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d,
	0x65, 0x74, 0x72, 0x69, 0x63, 0x12, 0x22, 0x0a, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x61, 0x6c, 0x6c,
	0x6f, 0x63, 0x61, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x61, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x63,
	0x61, 0x70, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x63, 0x61, 0x70, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x12, 0x1c, 0x0a, 0x09, 0x72,
	0x65, 0x6d, 0x61, 0x69, 0x6e, 0x69, 0x6e, 0x67, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x72, 0x65, 0x6d, 0x61, 0x69, 0x6e, 0x69, 0x6e, 0x67, 0x12, 0x20, 0x0a, 0x0b, 0x75, 0x6e, 0x61,
	0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x75, 0x6e, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x22, 0xa5, 0x02, 0x0a, 0x17,
	0x4e, 0x6f, 0x64, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x39, 0x0a, 0x09, 0x63,
	0x70, 0x75, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b,
	0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x52, 0x09, 0x63, 0x70, 0x75,
	0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x12, 0x3f, 0x0a, 0x0c, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79,
	0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61,
	0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x52, 0x0c, 0x6d, 0x65, 0x6d, 0x6f, 0x72,
	0x79, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x12, 0x39, 0x0a, 0x09, 0x67, 0x70, 0x75, 0x4d, 0x65,
	0x74, 0x72, 0x69, 0x63, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69,
	0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x52, 0x09, 0x67, 0x70, 0x75, 0x4d, 0x65, 0x74, 0x72,
	0x69, 0x63, 0x12, 0x3f, 0x0a, 0x0c, 0x67, 0x70, 0x75, 0x4d, 0x65, 0x6d, 0x4d, 0x65, 0x74, 0x72,
	0x69, 0x63, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d,
	0x65, 0x74, 0x72, 0x69, 0x63, 0x52, 0x0c, 0x67, 0x70, 0x75, 0x4d, 0x65, 0x6d, 0x4d, 0x65, 0x74,
	0x72, 0x69, 0x63, 0x22, 0xf6, 0x01, 0x0a, 0x09, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x3e, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x43, 0x6f,
	0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2e, 0x0a, 0x12, 0x6c, 0x61, 0x73, 0x74, 0x54, 0x72, 0x61,
	0x6e, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x12, 0x6c, 0x61, 0x73, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x18, 0x0a,
	0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x33, 0x0a, 0x0f, 0x43, 0x6f, 0x6e, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x08, 0x0a, 0x04, 0x54, 0x52,
	0x55, 0x45, 0x10, 0x00, 0x12, 0x09, 0x0a, 0x05, 0x46, 0x41, 0x4c, 0x53, 0x45, 0x10, 0x01, 0x12,
	0x0b, 0x0a, 0x07, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x02, 0x22, 0x32, 0x0a, 0x06,
	0x45, 0x6e, 0x76, 0x56, 0x61, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x22, 0x75, 0x0a, 0x0d, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x16, 0x0a, 0x06, 0x63, 0x70, 0x75, 0x4e, 0x75, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x06, 0x63, 0x70, 0x75, 0x4e, 0x75, 0x6d, 0x12, 0x1c, 0x0a, 0x09, 0x6d, 0x65, 0x6d,
	0x6f, 0x72, 0x79, 0x47, 0x69, 0x42, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x6d, 0x65,
	0x6d, 0x6f, 0x72, 0x79, 0x47, 0x69, 0x42, 0x12, 0x16, 0x0a, 0x06, 0x67, 0x70, 0x75, 0x4e, 0x75,
	0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x67, 0x70, 0x75, 0x4e, 0x75, 0x6d, 0x12,
	0x16, 0x0a, 0x06, 0x67, 0x70, 0x75, 0x4d, 0x65, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x06, 0x67, 0x70, 0x75, 0x4d, 0x65, 0x6d, 0x22, 0xc2, 0x04, 0x0a, 0x0a, 0x56, 0x6f, 0x6c, 0x75,
	0x6d, 0x65, 0x53, 0x70, 0x65, 0x63, 0x12, 0x36, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x53, 0x70, 0x65, 0x63, 0x2e, 0x56, 0x6f,
	0x6c, 0x75, 0x6d, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x1e,
	0x0a, 0x0a, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x12, 0x60,
	0x0a, 0x17, 0x64, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x50,
	0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x24, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x44, 0x61,
	0x74, 0x61, 0x73, 0x65, 0x74, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x50, 0x72, 0x6f, 0x70, 0x65,
	0x72, 0x74, 0x69, 0x65, 0x73, 0x48, 0x00, 0x52, 0x17, 0x64, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74,
	0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73,
	0x12, 0x5d, 0x0a, 0x16, 0x63, 0x75, 0x62, 0x65, 0x46, 0x53, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65,
	0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x43,
	0x75, 0x62, 0x65, 0x46, 0x53, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x50, 0x72, 0x6f, 0x70, 0x65,
	0x72, 0x74, 0x69, 0x65, 0x73, 0x48, 0x00, 0x52, 0x16, 0x63, 0x75, 0x62, 0x65, 0x46, 0x53, 0x56,
	0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x12,
	0x60, 0x0a, 0x17, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x46, 0x53, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65,
	0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x43,
	0x6c, 0x6f, 0x75, 0x64, 0x46, 0x53, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x50, 0x72, 0x6f, 0x70,
	0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x48, 0x00, 0x52, 0x17, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x46,
	0x53, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65,
	0x73, 0x12, 0x63, 0x0a, 0x18, 0x68, 0x6f, 0x73, 0x74, 0x50, 0x61, 0x74, 0x68, 0x56, 0x6f, 0x6c,
	0x75, 0x6d, 0x65, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x48, 0x6f, 0x73, 0x74, 0x50, 0x61, 0x74, 0x68, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65,
	0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x48, 0x00, 0x52, 0x18, 0x68, 0x6f,
	0x73, 0x74, 0x50, 0x61, 0x74, 0x68, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x50, 0x72, 0x6f, 0x70,
	0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x22, 0x40, 0x0a, 0x0a, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x0b, 0x0a, 0x07, 0x44, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x10,
	0x00, 0x12, 0x0a, 0x0a, 0x06, 0x43, 0x75, 0x62, 0x65, 0x46, 0x73, 0x10, 0x01, 0x12, 0x0b, 0x0a,
	0x07, 0x43, 0x6c, 0x6f, 0x75, 0x64, 0x46, 0x73, 0x10, 0x02, 0x12, 0x0c, 0x0a, 0x08, 0x48, 0x6f,
	0x73, 0x74, 0x50, 0x61, 0x74, 0x68, 0x10, 0x03, 0x42, 0x12, 0x0a, 0x10, 0x76, 0x6f, 0x6c, 0x75,
	0x6d, 0x65, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x22, 0x52, 0x0a, 0x16,
	0x43, 0x75, 0x62, 0x65, 0x46, 0x53, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x50, 0x72, 0x6f, 0x70,
	0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x76, 0x6f, 0x6c, 0x75,
	0x6d, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x62, 0x50, 0x61, 0x74,
	0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x75, 0x62, 0x50, 0x61, 0x74, 0x68,
	0x22, 0x55, 0x0a, 0x17, 0x44, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x56, 0x6f, 0x6c, 0x75, 0x6d,
	0x65, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x64,
	0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x64, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a,
	0x07, 0x73, 0x75, 0x62, 0x50, 0x61, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x73, 0x75, 0x62, 0x50, 0x61, 0x74, 0x68, 0x22, 0x53, 0x0a, 0x17, 0x43, 0x6c, 0x6f, 0x75, 0x64,
	0x46, 0x53, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69,
	0x65, 0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x62, 0x50, 0x61, 0x74, 0x68, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x75, 0x62, 0x50, 0x61, 0x74, 0x68, 0x22, 0x50, 0x0a, 0x18,
	0x48, 0x6f, 0x73, 0x74, 0x50, 0x61, 0x74, 0x68, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x50, 0x72,
	0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x68, 0x6f, 0x73, 0x74,
	0x50, 0x61, 0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x68, 0x6f, 0x73, 0x74,
	0x50, 0x61, 0x74, 0x68, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x62, 0x50, 0x61, 0x74, 0x68, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x75, 0x62, 0x50, 0x61, 0x74, 0x68, 0x22, 0x52,
	0x0a, 0x04, 0x50, 0x6f, 0x72, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6f, 0x72, 0x74, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x6f, 0x72, 0x74, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63,
	0x6f, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63,
	0x6f, 0x6c, 0x42, 0x3a, 0x5a, 0x38, 0x67, 0x69, 0x74, 0x2e, 0x6c, 0x69, 0x61, 0x6e, 0x6a, 0x69,
	0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6e, 0x61, 0x74, 0x69, 0x76,
	0x65, 0x2f, 0x6b, 0x69, 0x63, 0x2f, 0x6b, 0x69, 0x63, 0x2d, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f,
	0x72, 0x6d, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_common_common_proto_rawDescOnce sync.Once
	file_common_common_proto_rawDescData = file_common_common_proto_rawDesc
)

func file_common_common_proto_rawDescGZIP() []byte {
	file_common_common_proto_rawDescOnce.Do(func() {
		file_common_common_proto_rawDescData = protoimpl.X.CompressGZIP(file_common_common_proto_rawDescData)
	})
	return file_common_common_proto_rawDescData
}

var file_common_common_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_common_common_proto_msgTypes = make([]protoimpl.MessageInfo, 21)
var file_common_common_proto_goTypes = []any{
	(Condition_ConditionStatus)(0),   // 0: apis.common.Condition.ConditionStatus
	(VolumeSpec_VolumeType)(0),       // 1: apis.common.VolumeSpec.VolumeType
	(*TimestampModel)(nil),           // 2: apis.common.TimestampModel
	(*CPUUsage)(nil),                 // 3: apis.common.CPUUsage
	(*MemoryUsage)(nil),              // 4: apis.common.MemoryUsage
	(*GPUUsage)(nil),                 // 5: apis.common.GPUUsage
	(*NetIOUsage)(nil),               // 6: apis.common.NetIOUsage
	(*DiskIOUsage)(nil),              // 7: apis.common.DiskIOUsage
	(*ResourceCapacity)(nil),         // 8: apis.common.ResourceCapacity
	(*ResourceAllocatable)(nil),      // 9: apis.common.ResourceAllocatable
	(*NodeSummary)(nil),              // 10: apis.common.NodeSummary
	(*QueueSummary)(nil),             // 11: apis.common.QueueSummary
	(*ResourceMetric)(nil),           // 12: apis.common.ResourceMetric
	(*NodeGroupResourceMetric)(nil),  // 13: apis.common.NodeGroupResourceMetric
	(*Condition)(nil),                // 14: apis.common.Condition
	(*EnvVar)(nil),                   // 15: apis.common.EnvVar
	(*Specification)(nil),            // 16: apis.common.Specification
	(*VolumeSpec)(nil),               // 17: apis.common.VolumeSpec
	(*CubeFSVolumeProperties)(nil),   // 18: apis.common.CubeFSVolumeProperties
	(*DatasetVolumeProperties)(nil),  // 19: apis.common.DatasetVolumeProperties
	(*CloudFSVolumeProperties)(nil),  // 20: apis.common.CloudFSVolumeProperties
	(*HostPathVolumeProperties)(nil), // 21: apis.common.HostPathVolumeProperties
	(*Port)(nil),                     // 22: apis.common.Port
}
var file_common_common_proto_depIdxs = []int32{
	3,  // 0: apis.common.NodeSummary.CPUUsage:type_name -> apis.common.CPUUsage
	5,  // 1: apis.common.NodeSummary.GPUUsage:type_name -> apis.common.GPUUsage
	4,  // 2: apis.common.NodeSummary.memoryUsage:type_name -> apis.common.MemoryUsage
	6,  // 3: apis.common.NodeSummary.netIOUsage:type_name -> apis.common.NetIOUsage
	7,  // 4: apis.common.NodeSummary.diskIOUsage:type_name -> apis.common.DiskIOUsage
	8,  // 5: apis.common.QueueSummary.capacity:type_name -> apis.common.ResourceCapacity
	9,  // 6: apis.common.QueueSummary.allocatable:type_name -> apis.common.ResourceAllocatable
	12, // 7: apis.common.NodeGroupResourceMetric.cpuMetric:type_name -> apis.common.ResourceMetric
	12, // 8: apis.common.NodeGroupResourceMetric.memoryMetric:type_name -> apis.common.ResourceMetric
	12, // 9: apis.common.NodeGroupResourceMetric.gpuMetric:type_name -> apis.common.ResourceMetric
	12, // 10: apis.common.NodeGroupResourceMetric.gpuMemMetric:type_name -> apis.common.ResourceMetric
	0,  // 11: apis.common.Condition.status:type_name -> apis.common.Condition.ConditionStatus
	1,  // 12: apis.common.VolumeSpec.type:type_name -> apis.common.VolumeSpec.VolumeType
	19, // 13: apis.common.VolumeSpec.datasetVolumeProperties:type_name -> apis.common.DatasetVolumeProperties
	18, // 14: apis.common.VolumeSpec.cubeFSVolumeProperties:type_name -> apis.common.CubeFSVolumeProperties
	20, // 15: apis.common.VolumeSpec.cloudFSVolumeProperties:type_name -> apis.common.CloudFSVolumeProperties
	21, // 16: apis.common.VolumeSpec.hostPathVolumeProperties:type_name -> apis.common.HostPathVolumeProperties
	17, // [17:17] is the sub-list for method output_type
	17, // [17:17] is the sub-list for method input_type
	17, // [17:17] is the sub-list for extension type_name
	17, // [17:17] is the sub-list for extension extendee
	0,  // [0:17] is the sub-list for field type_name
}

func init() { file_common_common_proto_init() }
func file_common_common_proto_init() {
	if File_common_common_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_common_common_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*TimestampModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_common_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*CPUUsage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_common_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*MemoryUsage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_common_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*GPUUsage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_common_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*NetIOUsage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_common_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*DiskIOUsage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_common_proto_msgTypes[6].Exporter = func(v any, i int) any {
			switch v := v.(*ResourceCapacity); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_common_proto_msgTypes[7].Exporter = func(v any, i int) any {
			switch v := v.(*ResourceAllocatable); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_common_proto_msgTypes[8].Exporter = func(v any, i int) any {
			switch v := v.(*NodeSummary); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_common_proto_msgTypes[9].Exporter = func(v any, i int) any {
			switch v := v.(*QueueSummary); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_common_proto_msgTypes[10].Exporter = func(v any, i int) any {
			switch v := v.(*ResourceMetric); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_common_proto_msgTypes[11].Exporter = func(v any, i int) any {
			switch v := v.(*NodeGroupResourceMetric); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_common_proto_msgTypes[12].Exporter = func(v any, i int) any {
			switch v := v.(*Condition); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_common_proto_msgTypes[13].Exporter = func(v any, i int) any {
			switch v := v.(*EnvVar); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_common_proto_msgTypes[14].Exporter = func(v any, i int) any {
			switch v := v.(*Specification); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_common_proto_msgTypes[15].Exporter = func(v any, i int) any {
			switch v := v.(*VolumeSpec); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_common_proto_msgTypes[16].Exporter = func(v any, i int) any {
			switch v := v.(*CubeFSVolumeProperties); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_common_proto_msgTypes[17].Exporter = func(v any, i int) any {
			switch v := v.(*DatasetVolumeProperties); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_common_proto_msgTypes[18].Exporter = func(v any, i int) any {
			switch v := v.(*CloudFSVolumeProperties); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_common_proto_msgTypes[19].Exporter = func(v any, i int) any {
			switch v := v.(*HostPathVolumeProperties); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_common_proto_msgTypes[20].Exporter = func(v any, i int) any {
			switch v := v.(*Port); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_common_common_proto_msgTypes[15].OneofWrappers = []any{
		(*VolumeSpec_DatasetVolumeProperties)(nil),
		(*VolumeSpec_CubeFSVolumeProperties)(nil),
		(*VolumeSpec_CloudFSVolumeProperties)(nil),
		(*VolumeSpec_HostPathVolumeProperties)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_common_common_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   21,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_common_common_proto_goTypes,
		DependencyIndexes: file_common_common_proto_depIdxs,
		EnumInfos:         file_common_common_proto_enumTypes,
		MessageInfos:      file_common_common_proto_msgTypes,
	}.Build()
	File_common_common_proto = out.File
	file_common_common_proto_rawDesc = nil
	file_common_common_proto_goTypes = nil
	file_common_common_proto_depIdxs = nil
}
