// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v5.29.3
// source: eventhub/v1/eventhub.proto

package v1

import (
	_ "git.lianjia.com/cloudnative/kic/kic-platform/apis/common"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Event_EventType int32

const (
	Event_NORMAL  Event_EventType = 0
	Event_WARNING Event_EventType = 1
	Event_ERROR   Event_EventType = 2
)

// Enum value maps for Event_EventType.
var (
	Event_EventType_name = map[int32]string{
		0: "NORMAL",
		1: "WARNING",
		2: "ERROR",
	}
	Event_EventType_value = map[string]int32{
		"NORMAL":  0,
		"WARNING": 1,
		"ERROR":   2,
	}
)

func (x Event_EventType) Enum() *Event_EventType {
	p := new(Event_EventType)
	*p = x
	return p
}

func (x Event_EventType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Event_EventType) Descriptor() protoreflect.EnumDescriptor {
	return file_eventhub_v1_eventhub_proto_enumTypes[0].Descriptor()
}

func (Event_EventType) Type() protoreflect.EnumType {
	return &file_eventhub_v1_eventhub_proto_enumTypes[0]
}

func (x Event_EventType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Event_EventType.Descriptor instead.
func (Event_EventType) EnumDescriptor() ([]byte, []int) {
	return file_eventhub_v1_eventhub_proto_rawDescGZIP(), []int{0, 0}
}

type AggregateEvent_EventType int32

const (
	AggregateEvent_NORMAL  AggregateEvent_EventType = 0
	AggregateEvent_WARNING AggregateEvent_EventType = 1
	AggregateEvent_ERROR   AggregateEvent_EventType = 2
)

// Enum value maps for AggregateEvent_EventType.
var (
	AggregateEvent_EventType_name = map[int32]string{
		0: "NORMAL",
		1: "WARNING",
		2: "ERROR",
	}
	AggregateEvent_EventType_value = map[string]int32{
		"NORMAL":  0,
		"WARNING": 1,
		"ERROR":   2,
	}
)

func (x AggregateEvent_EventType) Enum() *AggregateEvent_EventType {
	p := new(AggregateEvent_EventType)
	*p = x
	return p
}

func (x AggregateEvent_EventType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AggregateEvent_EventType) Descriptor() protoreflect.EnumDescriptor {
	return file_eventhub_v1_eventhub_proto_enumTypes[1].Descriptor()
}

func (AggregateEvent_EventType) Type() protoreflect.EnumType {
	return &file_eventhub_v1_eventhub_proto_enumTypes[1]
}

func (x AggregateEvent_EventType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AggregateEvent_EventType.Descriptor instead.
func (AggregateEvent_EventType) EnumDescriptor() ([]byte, []int) {
	return file_eventhub_v1_eventhub_proto_rawDescGZIP(), []int{1, 0}
}

type Event struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name        string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Source      string                 `protobuf:"bytes,3,opt,name=source,proto3" json:"source,omitempty"`
	DisplayName string                 `protobuf:"bytes,4,opt,name=displayName,proto3" json:"displayName,omitempty"`
	Timestamp   *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	Message     string                 `protobuf:"bytes,6,opt,name=message,proto3" json:"message,omitempty"`
	Reason      string                 `protobuf:"bytes,7,opt,name=reason,proto3" json:"reason,omitempty"`
	Note        string                 `protobuf:"bytes,8,opt,name=note,proto3" json:"note,omitempty"`
	EventObject *EventObject           `protobuf:"bytes,9,opt,name=eventObject,proto3" json:"eventObject,omitempty"`
	Type        Event_EventType        `protobuf:"varint,10,opt,name=type,proto3,enum=apis.eventhub.v1.Event_EventType" json:"type,omitempty"` //事件类型
	Attributes  map[string]string      `protobuf:"bytes,11,rep,name=attributes,proto3" json:"attributes,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Operator    string                 `protobuf:"bytes,12,opt,name=operator,proto3" json:"operator,omitempty"`
}

func (x *Event) Reset() {
	*x = Event{}
	if protoimpl.UnsafeEnabled {
		mi := &file_eventhub_v1_eventhub_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Event) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Event) ProtoMessage() {}

func (x *Event) ProtoReflect() protoreflect.Message {
	mi := &file_eventhub_v1_eventhub_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Event.ProtoReflect.Descriptor instead.
func (*Event) Descriptor() ([]byte, []int) {
	return file_eventhub_v1_eventhub_proto_rawDescGZIP(), []int{0}
}

func (x *Event) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Event) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Event) GetSource() string {
	if x != nil {
		return x.Source
	}
	return ""
}

func (x *Event) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *Event) GetTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.Timestamp
	}
	return nil
}

func (x *Event) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *Event) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *Event) GetNote() string {
	if x != nil {
		return x.Note
	}
	return ""
}

func (x *Event) GetEventObject() *EventObject {
	if x != nil {
		return x.EventObject
	}
	return nil
}

func (x *Event) GetType() Event_EventType {
	if x != nil {
		return x.Type
	}
	return Event_NORMAL
}

func (x *Event) GetAttributes() map[string]string {
	if x != nil {
		return x.Attributes
	}
	return nil
}

func (x *Event) GetOperator() string {
	if x != nil {
		return x.Operator
	}
	return ""
}

type AggregateEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          string                   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name        string                   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Source      string                   `protobuf:"bytes,3,opt,name=source,proto3" json:"source,omitempty"`
	DisplayName string                   `protobuf:"bytes,4,opt,name=displayName,proto3" json:"displayName,omitempty"`
	StartAt     *timestamppb.Timestamp   `protobuf:"bytes,5,opt,name=startAt,proto3" json:"startAt,omitempty"`
	LatestAt    *timestamppb.Timestamp   `protobuf:"bytes,6,opt,name=latestAt,proto3" json:"latestAt,omitempty"`
	Message     string                   `protobuf:"bytes,7,opt,name=message,proto3" json:"message,omitempty"`
	Reason      string                   `protobuf:"bytes,8,opt,name=reason,proto3" json:"reason,omitempty"`
	Note        string                   `protobuf:"bytes,9,opt,name=note,proto3" json:"note,omitempty"`
	EventObject *EventObject             `protobuf:"bytes,10,opt,name=eventObject,proto3" json:"eventObject,omitempty"`
	Type        AggregateEvent_EventType `protobuf:"varint,11,opt,name=type,proto3,enum=apis.eventhub.v1.AggregateEvent_EventType" json:"type,omitempty"` //事件类型
	Attributes  map[string]string        `protobuf:"bytes,12,rep,name=attributes,proto3" json:"attributes,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Count       int64                    `protobuf:"varint,13,opt,name=count,proto3" json:"count,omitempty"` //事件发生的次数
}

func (x *AggregateEvent) Reset() {
	*x = AggregateEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_eventhub_v1_eventhub_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AggregateEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AggregateEvent) ProtoMessage() {}

func (x *AggregateEvent) ProtoReflect() protoreflect.Message {
	mi := &file_eventhub_v1_eventhub_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AggregateEvent.ProtoReflect.Descriptor instead.
func (*AggregateEvent) Descriptor() ([]byte, []int) {
	return file_eventhub_v1_eventhub_proto_rawDescGZIP(), []int{1}
}

func (x *AggregateEvent) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *AggregateEvent) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *AggregateEvent) GetSource() string {
	if x != nil {
		return x.Source
	}
	return ""
}

func (x *AggregateEvent) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *AggregateEvent) GetStartAt() *timestamppb.Timestamp {
	if x != nil {
		return x.StartAt
	}
	return nil
}

func (x *AggregateEvent) GetLatestAt() *timestamppb.Timestamp {
	if x != nil {
		return x.LatestAt
	}
	return nil
}

func (x *AggregateEvent) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *AggregateEvent) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *AggregateEvent) GetNote() string {
	if x != nil {
		return x.Note
	}
	return ""
}

func (x *AggregateEvent) GetEventObject() *EventObject {
	if x != nil {
		return x.EventObject
	}
	return nil
}

func (x *AggregateEvent) GetType() AggregateEvent_EventType {
	if x != nil {
		return x.Type
	}
	return AggregateEvent_NORMAL
}

func (x *AggregateEvent) GetAttributes() map[string]string {
	if x != nil {
		return x.Attributes
	}
	return nil
}

func (x *AggregateEvent) GetCount() int64 {
	if x != nil {
		return x.Count
	}
	return 0
}

type EventObject struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Kind string `protobuf:"bytes,1,opt,name=kind,proto3" json:"kind,omitempty"`
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *EventObject) Reset() {
	*x = EventObject{}
	if protoimpl.UnsafeEnabled {
		mi := &file_eventhub_v1_eventhub_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EventObject) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EventObject) ProtoMessage() {}

func (x *EventObject) ProtoReflect() protoreflect.Message {
	mi := &file_eventhub_v1_eventhub_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EventObject.ProtoReflect.Descriptor instead.
func (*EventObject) Descriptor() ([]byte, []int) {
	return file_eventhub_v1_eventhub_proto_rawDescGZIP(), []int{2}
}

func (x *EventObject) GetKind() string {
	if x != nil {
		return x.Kind
	}
	return ""
}

func (x *EventObject) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type QueryEventsResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Events []*AggregateEvent `protobuf:"bytes,1,rep,name=events,proto3" json:"events,omitempty"`
}

func (x *QueryEventsResult) Reset() {
	*x = QueryEventsResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_eventhub_v1_eventhub_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryEventsResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryEventsResult) ProtoMessage() {}

func (x *QueryEventsResult) ProtoReflect() protoreflect.Message {
	mi := &file_eventhub_v1_eventhub_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryEventsResult.ProtoReflect.Descriptor instead.
func (*QueryEventsResult) Descriptor() ([]byte, []int) {
	return file_eventhub_v1_eventhub_proto_rawDescGZIP(), []int{3}
}

func (x *QueryEventsResult) GetEvents() []*AggregateEvent {
	if x != nil {
		return x.Events
	}
	return nil
}

type QueryEventsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Sql   string     `protobuf:"bytes,1,opt,name=sql,proto3" json:"sql,omitempty"`
	Range *TimeRange `protobuf:"bytes,2,opt,name=range,proto3" json:"range,omitempty"`
}

func (x *QueryEventsRequest) Reset() {
	*x = QueryEventsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_eventhub_v1_eventhub_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryEventsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryEventsRequest) ProtoMessage() {}

func (x *QueryEventsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_eventhub_v1_eventhub_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryEventsRequest.ProtoReflect.Descriptor instead.
func (*QueryEventsRequest) Descriptor() ([]byte, []int) {
	return file_eventhub_v1_eventhub_proto_rawDescGZIP(), []int{4}
}

func (x *QueryEventsRequest) GetSql() string {
	if x != nil {
		return x.Sql
	}
	return ""
}

func (x *QueryEventsRequest) GetRange() *TimeRange {
	if x != nil {
		return x.Range
	}
	return nil
}

type TimeRange struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Start *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=start,proto3" json:"start,omitempty"`
	End   *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=end,proto3" json:"end,omitempty"`
}

func (x *TimeRange) Reset() {
	*x = TimeRange{}
	if protoimpl.UnsafeEnabled {
		mi := &file_eventhub_v1_eventhub_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TimeRange) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TimeRange) ProtoMessage() {}

func (x *TimeRange) ProtoReflect() protoreflect.Message {
	mi := &file_eventhub_v1_eventhub_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TimeRange.ProtoReflect.Descriptor instead.
func (*TimeRange) Descriptor() ([]byte, []int) {
	return file_eventhub_v1_eventhub_proto_rawDescGZIP(), []int{5}
}

func (x *TimeRange) GetStart() *timestamppb.Timestamp {
	if x != nil {
		return x.Start
	}
	return nil
}

func (x *TimeRange) GetEnd() *timestamppb.Timestamp {
	if x != nil {
		return x.End
	}
	return nil
}

var File_eventhub_v1_eventhub_proto protoreflect.FileDescriptor

var file_eventhub_v1_eventhub_proto_rawDesc = []byte{
	0x0a, 0x1a, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x68, 0x75, 0x62, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x76,
	0x65, 0x6e, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x10, 0x61, 0x70,
	0x69, 0x73, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x1a, 0x1c,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d,
	0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x13, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0xb2, 0x04, 0x0a, 0x05, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a,
	0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x70,
	0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x38, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72,
	0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x61,
	0x73, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x6f, 0x74, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6e, 0x6f, 0x74, 0x65, 0x12, 0x3f, 0x0a, 0x0b, 0x65, 0x76, 0x65, 0x6e, 0x74,
	0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x61,
	0x70, 0x69, 0x73, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e,
	0x45, 0x76, 0x65, 0x6e, 0x74, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x52, 0x0b, 0x65, 0x76, 0x65,
	0x6e, 0x74, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x35, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x65, 0x76,
	0x65, 0x6e, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x2e,
	0x45, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12,
	0x47, 0x0a, 0x0a, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x18, 0x0b, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74,
	0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x2e, 0x41, 0x74, 0x74,
	0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0a, 0x61, 0x74,
	0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x6f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x6f, 0x72, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x6f, 0x72, 0x1a, 0x3d, 0x0a, 0x0f, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74,
	0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x22, 0x2f, 0x0a, 0x09, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x0a, 0x0a, 0x06, 0x4e, 0x4f, 0x52, 0x4d, 0x41, 0x4c, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07,
	0x57, 0x41, 0x52, 0x4e, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x09, 0x0a, 0x05, 0x45, 0x52, 0x52,
	0x4f, 0x52, 0x10, 0x02, 0x22, 0xfb, 0x04, 0x0a, 0x0e, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61,
	0x74, 0x65, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61,
	0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x34, 0x0a, 0x07, 0x73, 0x74, 0x61, 0x72, 0x74, 0x41, 0x74,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x07, 0x73, 0x74, 0x61, 0x72, 0x74, 0x41, 0x74, 0x12, 0x36, 0x0a, 0x08, 0x6c,
	0x61, 0x74, 0x65, 0x73, 0x74, 0x41, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x08, 0x6c, 0x61, 0x74, 0x65, 0x73,
	0x74, 0x41, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x16, 0x0a,
	0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72,
	0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x6f, 0x74, 0x65, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x6f, 0x74, 0x65, 0x12, 0x3f, 0x0a, 0x0b, 0x65, 0x76, 0x65,
	0x6e, 0x74, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d,
	0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x76,
	0x31, 0x2e, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x52, 0x0b, 0x65,
	0x76, 0x65, 0x6e, 0x74, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x3e, 0x0a, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2a, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e,
	0x65, 0x76, 0x65, 0x6e, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x67, 0x67, 0x72,
	0x65, 0x67, 0x61, 0x74, 0x65, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x2e, 0x45, 0x76, 0x65, 0x6e, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x50, 0x0a, 0x0a, 0x61, 0x74,
	0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x30,
	0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x76,
	0x31, 0x2e, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x45, 0x76, 0x65, 0x6e, 0x74,
	0x2e, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x52, 0x0a, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x12, 0x14, 0x0a, 0x05,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x1a, 0x3d, 0x0a, 0x0f, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38,
	0x01, 0x22, 0x2f, 0x0a, 0x09, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0a,
	0x0a, 0x06, 0x4e, 0x4f, 0x52, 0x4d, 0x41, 0x4c, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x57, 0x41,
	0x52, 0x4e, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x09, 0x0a, 0x05, 0x45, 0x52, 0x52, 0x4f, 0x52,
	0x10, 0x02, 0x22, 0x35, 0x0a, 0x0b, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x4f, 0x62, 0x6a, 0x65, 0x63,
	0x74, 0x12, 0x12, 0x0a, 0x04, 0x6b, 0x69, 0x6e, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6b, 0x69, 0x6e, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x4d, 0x0a, 0x11, 0x51, 0x75, 0x65,
	0x72, 0x79, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x38,
	0x0a, 0x06, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20,
	0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x76,
	0x31, 0x2e, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x45, 0x76, 0x65, 0x6e, 0x74,
	0x52, 0x06, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x22, 0x59, 0x0a, 0x12, 0x51, 0x75, 0x65, 0x72,
	0x79, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x10,
	0x0a, 0x03, 0x73, 0x71, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x73, 0x71, 0x6c,
	0x12, 0x31, 0x0a, 0x05, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1b, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x76, 0x31, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x05, 0x72, 0x61,
	0x6e, 0x67, 0x65, 0x22, 0x6b, 0x0a, 0x09, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65,
	0x12, 0x30, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x72, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x05, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x12, 0x2c, 0x0a, 0x03, 0x65, 0x6e, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x03, 0x65, 0x6e, 0x64,
	0x32, 0xe1, 0x01, 0x0a, 0x0f, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x48, 0x75, 0x62, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x12, 0x71, 0x0a, 0x0b, 0x51, 0x75, 0x65, 0x72, 0x79, 0x45, 0x76, 0x65,
	0x6e, 0x74, 0x73, 0x12, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74,
	0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x45, 0x76, 0x65, 0x6e,
	0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x73,
	0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75, 0x65,
	0x72, 0x79, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x17,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x11, 0x12, 0x0f, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31,
	0x2f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x5b, 0x0a, 0x0c, 0x50, 0x75, 0x62, 0x6c, 0x69,
	0x73, 0x68, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x17, 0x2e, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x65,
	0x76, 0x65, 0x6e, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x76, 0x65, 0x6e, 0x74,
	0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x1a, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x14,
	0x3a, 0x01, 0x2a, 0x22, 0x0f, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x76,
	0x65, 0x6e, 0x74, 0x73, 0x42, 0x42, 0x5a, 0x40, 0x67, 0x69, 0x74, 0x2e, 0x6c, 0x69, 0x61, 0x6e,
	0x6a, 0x69, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6e, 0x61, 0x74,
	0x69, 0x76, 0x65, 0x2f, 0x6b, 0x69, 0x63, 0x2f, 0x6b, 0x69, 0x63, 0x2d, 0x70, 0x6c, 0x61, 0x74,
	0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x68,
	0x75, 0x62, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_eventhub_v1_eventhub_proto_rawDescOnce sync.Once
	file_eventhub_v1_eventhub_proto_rawDescData = file_eventhub_v1_eventhub_proto_rawDesc
)

func file_eventhub_v1_eventhub_proto_rawDescGZIP() []byte {
	file_eventhub_v1_eventhub_proto_rawDescOnce.Do(func() {
		file_eventhub_v1_eventhub_proto_rawDescData = protoimpl.X.CompressGZIP(file_eventhub_v1_eventhub_proto_rawDescData)
	})
	return file_eventhub_v1_eventhub_proto_rawDescData
}

var file_eventhub_v1_eventhub_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_eventhub_v1_eventhub_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_eventhub_v1_eventhub_proto_goTypes = []any{
	(Event_EventType)(0),          // 0: apis.eventhub.v1.Event.EventType
	(AggregateEvent_EventType)(0), // 1: apis.eventhub.v1.AggregateEvent.EventType
	(*Event)(nil),                 // 2: apis.eventhub.v1.Event
	(*AggregateEvent)(nil),        // 3: apis.eventhub.v1.AggregateEvent
	(*EventObject)(nil),           // 4: apis.eventhub.v1.EventObject
	(*QueryEventsResult)(nil),     // 5: apis.eventhub.v1.QueryEventsResult
	(*QueryEventsRequest)(nil),    // 6: apis.eventhub.v1.QueryEventsRequest
	(*TimeRange)(nil),             // 7: apis.eventhub.v1.TimeRange
	nil,                           // 8: apis.eventhub.v1.Event.AttributesEntry
	nil,                           // 9: apis.eventhub.v1.AggregateEvent.AttributesEntry
	(*timestamppb.Timestamp)(nil), // 10: google.protobuf.Timestamp
	(*emptypb.Empty)(nil),         // 11: google.protobuf.Empty
}
var file_eventhub_v1_eventhub_proto_depIdxs = []int32{
	10, // 0: apis.eventhub.v1.Event.timestamp:type_name -> google.protobuf.Timestamp
	4,  // 1: apis.eventhub.v1.Event.eventObject:type_name -> apis.eventhub.v1.EventObject
	0,  // 2: apis.eventhub.v1.Event.type:type_name -> apis.eventhub.v1.Event.EventType
	8,  // 3: apis.eventhub.v1.Event.attributes:type_name -> apis.eventhub.v1.Event.AttributesEntry
	10, // 4: apis.eventhub.v1.AggregateEvent.startAt:type_name -> google.protobuf.Timestamp
	10, // 5: apis.eventhub.v1.AggregateEvent.latestAt:type_name -> google.protobuf.Timestamp
	4,  // 6: apis.eventhub.v1.AggregateEvent.eventObject:type_name -> apis.eventhub.v1.EventObject
	1,  // 7: apis.eventhub.v1.AggregateEvent.type:type_name -> apis.eventhub.v1.AggregateEvent.EventType
	9,  // 8: apis.eventhub.v1.AggregateEvent.attributes:type_name -> apis.eventhub.v1.AggregateEvent.AttributesEntry
	3,  // 9: apis.eventhub.v1.QueryEventsResult.events:type_name -> apis.eventhub.v1.AggregateEvent
	7,  // 10: apis.eventhub.v1.QueryEventsRequest.range:type_name -> apis.eventhub.v1.TimeRange
	10, // 11: apis.eventhub.v1.TimeRange.start:type_name -> google.protobuf.Timestamp
	10, // 12: apis.eventhub.v1.TimeRange.end:type_name -> google.protobuf.Timestamp
	6,  // 13: apis.eventhub.v1.EventHubService.QueryEvents:input_type -> apis.eventhub.v1.QueryEventsRequest
	2,  // 14: apis.eventhub.v1.EventHubService.PublishEvent:input_type -> apis.eventhub.v1.Event
	5,  // 15: apis.eventhub.v1.EventHubService.QueryEvents:output_type -> apis.eventhub.v1.QueryEventsResult
	11, // 16: apis.eventhub.v1.EventHubService.PublishEvent:output_type -> google.protobuf.Empty
	15, // [15:17] is the sub-list for method output_type
	13, // [13:15] is the sub-list for method input_type
	13, // [13:13] is the sub-list for extension type_name
	13, // [13:13] is the sub-list for extension extendee
	0,  // [0:13] is the sub-list for field type_name
}

func init() { file_eventhub_v1_eventhub_proto_init() }
func file_eventhub_v1_eventhub_proto_init() {
	if File_eventhub_v1_eventhub_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_eventhub_v1_eventhub_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*Event); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_eventhub_v1_eventhub_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*AggregateEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_eventhub_v1_eventhub_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*EventObject); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_eventhub_v1_eventhub_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*QueryEventsResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_eventhub_v1_eventhub_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*QueryEventsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_eventhub_v1_eventhub_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*TimeRange); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_eventhub_v1_eventhub_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_eventhub_v1_eventhub_proto_goTypes,
		DependencyIndexes: file_eventhub_v1_eventhub_proto_depIdxs,
		EnumInfos:         file_eventhub_v1_eventhub_proto_enumTypes,
		MessageInfos:      file_eventhub_v1_eventhub_proto_msgTypes,
	}.Build()
	File_eventhub_v1_eventhub_proto = out.File
	file_eventhub_v1_eventhub_proto_rawDesc = nil
	file_eventhub_v1_eventhub_proto_goTypes = nil
	file_eventhub_v1_eventhub_proto_depIdxs = nil
}
