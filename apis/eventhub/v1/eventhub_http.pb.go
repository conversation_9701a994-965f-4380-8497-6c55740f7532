// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.3
// - protoc             v5.29.3
// source: eventhub/v1/eventhub.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationEventHubServicePublishEvent = "/apis.eventhub.v1.EventHubService/PublishEvent"
const OperationEventHubServiceQueryEvents = "/apis.eventhub.v1.EventHubService/QueryEvents"

type EventHubServiceHTTPServer interface {
	PublishEvent(context.Context, *Event) (*emptypb.Empty, error)
	QueryEvents(context.Context, *QueryEventsRequest) (*QueryEventsResult, error)
}

func RegisterEventHubServiceHTTPServer(s *http.Server, srv EventHubServiceHTTPServer) {
	r := s.Route("/")
	r.GET("/apis/v1/events", _EventHubService_QueryEvents0_HTTP_Handler(srv))
	r.POST("/apis/v1/events", _EventHubService_PublishEvent0_HTTP_Handler(srv))
}

func _EventHubService_QueryEvents0_HTTP_Handler(srv EventHubServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in QueryEventsRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationEventHubServiceQueryEvents)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.QueryEvents(ctx, req.(*QueryEventsRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*QueryEventsResult)
		return ctx.Result(200, reply)
	}
}

func _EventHubService_PublishEvent0_HTTP_Handler(srv EventHubServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in Event
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationEventHubServicePublishEvent)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.PublishEvent(ctx, req.(*Event))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

type EventHubServiceHTTPClient interface {
	PublishEvent(ctx context.Context, req *Event, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	QueryEvents(ctx context.Context, req *QueryEventsRequest, opts ...http.CallOption) (rsp *QueryEventsResult, err error)
}

type EventHubServiceHTTPClientImpl struct {
	cc *http.Client
}

func NewEventHubServiceHTTPClient(client *http.Client) EventHubServiceHTTPClient {
	return &EventHubServiceHTTPClientImpl{client}
}

func (c *EventHubServiceHTTPClientImpl) PublishEvent(ctx context.Context, in *Event, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/apis/v1/events"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationEventHubServicePublishEvent))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *EventHubServiceHTTPClientImpl) QueryEvents(ctx context.Context, in *QueryEventsRequest, opts ...http.CallOption) (*QueryEventsResult, error) {
	var out QueryEventsResult
	pattern := "/apis/v1/events"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationEventHubServiceQueryEvents))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
