// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v5.29.3
// source: eventhub/v1/eventhub.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	EventHubService_QueryEvents_FullMethodName  = "/apis.eventhub.v1.EventHubService/QueryEvents"
	EventHubService_PublishEvent_FullMethodName = "/apis.eventhub.v1.EventHubService/PublishEvent"
)

// EventHubServiceClient is the client API for EventHubService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type EventHubServiceClient interface {
	QueryEvents(ctx context.Context, in *QueryEventsRequest, opts ...grpc.CallOption) (*QueryEventsResult, error)
	PublishEvent(ctx context.Context, in *Event, opts ...grpc.CallOption) (*emptypb.Empty, error)
}

type eventHubServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewEventHubServiceClient(cc grpc.ClientConnInterface) EventHubServiceClient {
	return &eventHubServiceClient{cc}
}

func (c *eventHubServiceClient) QueryEvents(ctx context.Context, in *QueryEventsRequest, opts ...grpc.CallOption) (*QueryEventsResult, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(QueryEventsResult)
	err := c.cc.Invoke(ctx, EventHubService_QueryEvents_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eventHubServiceClient) PublishEvent(ctx context.Context, in *Event, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, EventHubService_PublishEvent_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// EventHubServiceServer is the server API for EventHubService service.
// All implementations must embed UnimplementedEventHubServiceServer
// for forward compatibility
type EventHubServiceServer interface {
	QueryEvents(context.Context, *QueryEventsRequest) (*QueryEventsResult, error)
	PublishEvent(context.Context, *Event) (*emptypb.Empty, error)
	mustEmbedUnimplementedEventHubServiceServer()
}

// UnimplementedEventHubServiceServer must be embedded to have forward compatible implementations.
type UnimplementedEventHubServiceServer struct {
}

func (UnimplementedEventHubServiceServer) QueryEvents(context.Context, *QueryEventsRequest) (*QueryEventsResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryEvents not implemented")
}
func (UnimplementedEventHubServiceServer) PublishEvent(context.Context, *Event) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PublishEvent not implemented")
}
func (UnimplementedEventHubServiceServer) mustEmbedUnimplementedEventHubServiceServer() {}

// UnsafeEventHubServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to EventHubServiceServer will
// result in compilation errors.
type UnsafeEventHubServiceServer interface {
	mustEmbedUnimplementedEventHubServiceServer()
}

func RegisterEventHubServiceServer(s grpc.ServiceRegistrar, srv EventHubServiceServer) {
	s.RegisterService(&EventHubService_ServiceDesc, srv)
}

func _EventHubService_QueryEvents_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryEventsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EventHubServiceServer).QueryEvents(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EventHubService_QueryEvents_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EventHubServiceServer).QueryEvents(ctx, req.(*QueryEventsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EventHubService_PublishEvent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Event)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EventHubServiceServer).PublishEvent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EventHubService_PublishEvent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EventHubServiceServer).PublishEvent(ctx, req.(*Event))
	}
	return interceptor(ctx, in, info, handler)
}

// EventHubService_ServiceDesc is the grpc.ServiceDesc for EventHubService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var EventHubService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "apis.eventhub.v1.EventHubService",
	HandlerType: (*EventHubServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "QueryEvents",
			Handler:    _EventHubService_QueryEvents_Handler,
		},
		{
			MethodName: "PublishEvent",
			Handler:    _EventHubService_PublishEvent_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "eventhub/v1/eventhub.proto",
}
