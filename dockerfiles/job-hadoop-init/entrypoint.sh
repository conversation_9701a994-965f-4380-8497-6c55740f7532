#!/bin/bash

HADOOP_PASSWORD=${SSH_PASSWORD:-hadoop123456}

# 设置hadoop用户
if [ -n "$HADOOP_USERS" ]; then
    echo "Setting hadoop users $HADOOP_USERS..."
    IFS=',' read -r -a HADOOP_USERS_ARRAY <<< "$HADOOP_USERS"
    for hadoopUser in "${HADOOP_USERS_ARRAY[@]}"; do
      echo "Creating hadoop user $hadoopUser..."
      if ! id -u "$hadoopUser" >/dev/null 2>&1; then
        useradd -m -s /bin/bash "$hadoopUser"
        echo "$hadoopUser:$HADOOP_PASSWORD" | chpasswd
      fi
    USER_BASHRC=$(eval echo ~$hadoopUser)/.bashrc
    if [ ! -f "$USER_BASHRC" ]; then
        touch "$USER_BASHRC"
        chown $hadoopUser:$hadoopUser "$USER_BASHRC"
    fi
    
    echo "if [ -f /home/<USER>/conf/prod/client-helium/example/hadoop/switch_hadoop_env.sh ]; then" >> "$USER_BASHRC"
    printf "    kinit -kt /etc/keytab/%s/%s.keytab %s/<EMAIL>\n" "$hadoopUser" "$hadoopUser" "$hadoopUser" >> "$USER_BASHRC"
    printf "    source /home/<USER>/conf/prod/client-helium/example/hadoop/switch_hadoop_env.sh -u %s\n" "$hadoopUser" >> "$USER_BASHRC"
    echo "fi" >> "$USER_BASHRC"
    done
fi