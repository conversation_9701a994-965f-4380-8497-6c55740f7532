#!/bin/bash

user=''
cluster='prod'

while getopts "c:u:" arg
do
    case $arg in
        u)
            user=$OPTARG
            ;;
        c)
            cluster=$OPTARG
            ;;       
        ?)
            echo "unkonw argument"
            exit 1
        ;;
    esac
done

if [[ -z "$user" ]]; then
  echo "please set user".
  exit 1
fi

unset JAVA_HOME
unset HADOOP_HOME
unset HADOOP_MAPRED_HOME
unset HIVE_HOME
unset TEZ_HOME
unset SPARK_HOME

unset HADOOP_CONF_DIR
unset HIVE_CONF_DIR
unset TEZ_CONF_DIR
unset SPARK_CONF_DIR

export JAVA_HOME="/home/<USER>/bin/jdk"
export HADOOP_HOME="/home/<USER>/bin/hadoop"
export HADOOP_MAPRED_HOME=${HADOOP_MAPRED_HOME}
export TEZ_HOME="/home/<USER>/bin/tez"
export HIVE_HOME="/home/<USER>/bin/hive"
export SPARK_HOME="/home/<USER>/bin/spark3.5"

export HADOOP_CONF_DIR=/home/<USER>/conf/$cluster/client-helium/example/hadoop
export TEZ_CONF_DIR=/home/<USER>/conf/$cluster/client-helium/example/tez
export HIVE_CONF_DIR=/home/<USER>/conf/$cluster/client-helium/example/hive
export SPARK_CONF_DIR=/home/<USER>/conf/$cluster/client-helium/example/spark3.5

export HADOOP_CLASSPATH="${TEZ_CONF_DIR}:${TEZ_HOME}/*:${TEZ_HOME}/lib/*:$HADOOP_CLASSPATH"
export PATH=$PATH:${JAVA_HOME}/bin:/usr/local/bin:/bin:/usr/bin:/usr/local/sbin:/usr/sbin:/home/<USER>/.local/bin:/home/<USER>/bin:/home/<USER>/bin
export PATH=$HADOOP_HOME/bin:$HIVE_HOME/bin:$SPARK_HOME/bin:$PATH 