FROM harbor.intra.ke.com/kcs/aicp-common/aicp_system/notebook-init-container:v2.0.6-hadoop

RUN rm -rf /notebook-init-container-files

COPY create_hadoop.sh /home/<USER>/job-hadoop-init/create_hadoop.sh
COPY switch_hadoop_env.sh /home/<USER>/conf/prod/client-helium/example/hadoop/switch_hadoop_env.sh


RUN chmod -R 755 /home/<USER>/conf/prod/client-helium/example/hadoop/switch_hadoop_env.sh \
    && chmod -R 755 /home/<USER>/job-hadoop-init/create_hadoop.sh

# docker build --platform linux/amd64 -f Dockerfile -t harbor.intra.ke.com/kcs/job/job-init-container:v1.1.6-hadoop . --push
