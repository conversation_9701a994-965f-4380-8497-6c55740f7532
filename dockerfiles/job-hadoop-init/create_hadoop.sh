#!/bin/bash
#添加软连接
set -x
if [ -n "$HADOOP_USERS" ]; then
    cd /home/<USER>/bin/ && ln -s /home/<USER>/local/apache-hive-3.1.2-bin /home/<USER>/bin/hive3
    cd /home/<USER>/bin/ && ln -s /home/<USER>/local/apache-hive-1.2.1-bin /home/<USER>/bin/hive
    cd /home/<USER>/bin/ && ln -s /home/<USER>/local/hadoop-3.2.1 /home/<USER>/bin/hadoop
    cd /home/<USER>/bin/ && ln -s /home/<USER>/local/jdk1.8.0_261 /home/<USER>/bin/jdk
    cd /home/<USER>/bin/ && ln -s /home/<USER>/local/spark-3.5.1-bin-3.2.1 /home/<USER>/bin/spark3.5
    cd /home/<USER>/bin/ && ln -s /home/<USER>/local/tez-0.9.2 /home/<USER>/bin/tez

    #安装krb5-user
    /bin/cp /home/<USER>/krb5.conf /etc/krb5.conf
    chmod 777 /etc/krb5.conf
    /bin/cp -r /etc/keytab-volume/ /etc/keytab/
    chmod -R 777 /etc/keytab/
    
    # 检测操作系统类型
    if [ -f /etc/os-release ]; then
        . /etc/os-release
        OS=$ID
    elif type lsb_release >/dev/null 2>&1; then
        OS=$(lsb_release -si | tr '[:upper:]' '[:lower:]')
    elif [ -f /etc/centos-release ]; then
        OS=centos
    else
        echo "Unsupported OS"
        exit 1
    fi

    # 根据操作系统类型执行相应的命令
    case $OS in
        ubuntu|debian)
            echo "Detected Ubuntu/Debian"
            apt-get update -y
            DEBIAN_FRONTEND=noninteractive apt-get install krb5-user -y
            ;;
        centos|rhel|fedora)
            echo "Detected CentOS/RHEL/Fedora"
            yum update -y
            yum install krb5-workstation -y
            ;;
        *)
            echo "Unsupported OS: $OS"
            exit 1
            ;;
    esac

    HADOOP_PASSWORD=${HADOOP_PASSWORD:-hadoop123456}
    echo "Setting hadoop users $HADOOP_USERS..."
    IFS=',' read -r -a HADOOP_USERS_ARRAY <<< "$HADOOP_USERS"
    for hadoopUser in "${HADOOP_USERS_ARRAY[@]}"; do
      echo "Creating hadoop user $hadoopUser..."
      if ! id -u "$hadoopUser" >/dev/null 2>&1; then
        useradd -m -s /bin/bash "$hadoopUser"
        echo "$hadoopUser:$HADOOP_PASSWORD" | chpasswd
      fi
    USER_BASHRC=$(eval echo ~$hadoopUser)/.bashrc
    if [ ! -f "$USER_BASHRC" ]; then
        touch "$USER_BASHRC"
        chown $hadoopUser:$hadoopUser "$USER_BASHRC"
    fi
    
    echo "if [ -f /home/<USER>/conf/prod/client-helium/example/hadoop/switch_hadoop_env.sh ]; then" >> "$USER_BASHRC"
    printf "    kinit -kt /etc/keytab/%s/%s.keytab %s/<EMAIL>\n" "$hadoopUser" "$hadoopUser" "$hadoopUser" >> "$USER_BASHRC"
    printf "    source /home/<USER>/conf/prod/client-helium/example/hadoop/switch_hadoop_env.sh -u %s\n" "$hadoopUser" >> "$USER_BASHRC"
    echo "fi" >> "$USER_BASHRC"
    done

fi
set +x
