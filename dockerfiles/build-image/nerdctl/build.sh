#!/bin/sh

# 检查环境变量是否已设置
if [ -z "$REGISTRY_USER" ] || [ -z "$REGISTRY_PASS" ] || [ -z "$CONTAINER_NAME" ] || [ -z "$WORKSPACE_NAME" ] || [ -z "$IMAGE_NAME" ] || [ -z "$IMAGE_TAG" ]; then
    echo "Error: One or more environment variables are not set."
    exit 1
fi

# 定义Docker命令
DOCKER_COMMAND="nerdctl"

# 登录Docker注册表
echo "$REGISTRY_PASS" | ${DOCKER_COMMAND} login bj-harbor01.ke.com -u "${REGISTRY_USER}" --password-stdin
if [ $? -ne 0 ]; then
    echo "Docker login failed."
    exit 1
fi

# 提交容器为新镜像
${DOCKER_COMMAND} commit --namespace k8s.io "${CONTAINER_NAME}" bj-harbor01.ke.com/aistudio-"${WORKSPACE_NAME}"/"${IMAGE_NAME}":"${IMAGE_TAG}"
if [ $? -ne 0 ]; then
    echo "Docker commit failed."
    exit 1
fi

# 推送镜像到注册表，失败时重试3次
MAX_RETRIES=3
RETRY_COUNT=0
while [ $RETRY_COUNT -lt $MAX_RETRIES ]; do
    ${DOCKER_COMMAND} push --namespace k8s.io bj-harbor01.ke.com/aistudio-"${WORKSPACE_NAME}"/"${IMAGE_NAME}":"${IMAGE_TAG}"
    if [ $? -eq 0 ]; then
        break
    fi
    RETRY_COUNT=$((RETRY_COUNT+1))
    echo "Docker push failed. Retrying ($RETRY_COUNT/$MAX_RETRIES)..."
    sleep 5
done

if [ $RETRY_COUNT -eq $MAX_RETRIES ]; then
    echo "Docker push failed after $MAX_RETRIES attempts."
    exit 1
fi

# 删除本地镜像
${DOCKER_COMMAND} rmi --namespace k8s.io bj-harbor01.ke.com/aistudio-"${WORKSPACE_NAME}"/"${IMAGE_NAME}":"${IMAGE_TAG}"
if [ $? -ne 0 ]; then
    echo "Docker rmi failed."
    exit 1
fi

echo "Script executed successfully."