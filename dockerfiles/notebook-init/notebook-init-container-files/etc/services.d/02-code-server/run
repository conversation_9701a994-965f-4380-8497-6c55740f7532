#!/command/with-contenv bash

# 函数：输出错误信息
function error_message {
  echo "[code-server]错误: $1" >&2
  sleep 120
}

code_server_version="4.92.2"

ldd_version=$(ldd --version | head -n 1 | awk '{print $NF}')
if [[ $(echo -e "$ldd_version\n2.28" | sort -V | head -n 1) == "$ldd_version" && "$ldd_version" != "2.28" ]]; then
  echo "ldd 版本小于 2.28，下载 code-server 4.17.0 版本..."
  code_server_version="4.17.0"
fi

# 检查并安装 code-server
if ! command -v code-server &> /dev/null; then
  if command -v dpkg &> /dev/null; then
    package_file="code-server_${code_server_version}_amd64.deb"
    download_url="http://nexus.kcs.ke.com/repository/aistudio/code-server/$package_file"
    download_command="curl -o \"$INIT_PATH/install/$package_file\" \"$download_url\""
    echo "执行命令: $download_command"
    eval $download_command
    if ! eval $download_command; then
      error_message "download $package_file failed."
    fi

    if ! dpkg -i "$INIT_PATH/install/$package_file"; then
      error_message "安装 $package_file 失败。"
    fi
  elif command -v rpm &> /dev/null; then
    package_file="code-server-$code_server_version-amd64.rpm"
    download_url="http://nexus.kcs.ke.com/repository/aistudio/code-server/$package_file"
    download_command="curl -o \"$INIT_PATH/install/$package_file\" \"$download_url\""
    echo "执行命令: $download_command"
    eval $download_command
    if ! eval $download_command; then
      error_message "download $package_file failed."
    fi
    if ! rpm -i "$INIT_PATH/install/$package_file"; then
      error_message "安装 $package_file 失败。"
    fi
  else
    error_message "无法确定包管理器。code-server 未安装。"
  fi
fi

# 检查并安装 jq
if ! command -v jq &> /dev/null; then
  if command -v apt &> /dev/null; then
    apt update && apt install -y jq
  elif command -v yum &> /dev/null; then
    yum install -y jq
  elif command -v dnf &> /dev/null; then
    dnf install -y jq
  else
    error_message "无法确定包管理器。jq 未安装。"
  fi
fi

# TODO 多租户
# 如果 code-server 安装成功，执行后续逻辑
if command -v code-server &> /dev/null; then
  # 创建 Code Server 配置文件目录
  CONFIG_DIR="${HOME}/.config/code-server"
  [ ! -d "$CONFIG_DIR" ] && mkdir -p "$CONFIG_DIR"

  # 如果 settings.json 不存在，则创建并设置默认主题色为黑色
  SETTINGS_JSON="${HOME}/.local/share/code-server/User/settings.json"
  if [ ! -f "$SETTINGS_JSON" ]; then
    mkdir -p "$(dirname "$SETTINGS_JSON")"
    cat <<EOF > "$SETTINGS_JSON"
{
  "workbench.colorTheme": "Default Dark+"
}
EOF
  fi

  # 修改 product.json
  PRODUCT_JSON="/usr/lib/code-server/lib/vscode/product.json"
  if [ -f "$PRODUCT_JSON" ]; then
    jq '. + {
      "extensionsGallery": {
        "serviceUrl": "https://openvsx-proxy.ke.com/vscode/gallery",
        "itemUrl": "https://openvsx-proxy.ke.com/vscode/item",
        "resourceUrlTemplate": "https://openvsx-proxy.ke.com/vscode/asset/{publisher}/{name}/{version}/Microsoft.VisualStudio.Code.WebResources/{path}"
      },
      "linkProtectionTrustedDomains": [
        "https://openvsx-proxy.ke.com"
      ]
    }' "$PRODUCT_JSON" > "${PRODUCT_JSON}.tmp" && mv "${PRODUCT_JSON}.tmp" "$PRODUCT_JSON"
  else
    error_message "找不到 product.json 文件，无法设置 extensionsGallery 和 linkProtectionTrustedDomains"
  fi

  # 用所有用户设置插件

  EXTENSIONS_DIR_ARG=""
  if [ -n "$VSCODE_EXTENSIONS_DIR" ] && [ -d "$VSCODE_EXTENSIONS_DIR" ]; then
    for USER in $(cut -d: -f1 /etc/passwd); do
        # 获取用户的主目录
        USER_HOME=$(eval echo ~$USER)
        # 检查用户主目录是否存在
        if [ -d "$USER_HOME" ]; then
            USER_EXTENSIONS_DIR="$USER_HOME/.vscode-server/extensions"
            if [ ! -L "$USER_EXTENSIONS_DIR" ]; then
                rm -rf "$USER_EXTENSIONS_DIR"
                ln -s "$VSCODE_EXTENSIONS_DIR" "$USER_EXTENSIONS_DIR" || echo "无法为用户 $USER 创建符号链接"
            fi
            setfacl -m u:"$USER":rwx "$VSCODE_EXTENSIONS_DIR"
        fi
    done
    EXTENSIONS_DIR_ARG="--extensions-dir $VSCODE_EXTENSIONS_DIR"
  fi

  # 设置工作目录
  WORK_DIR="${WORK_DIR:-$HOME}"

  # 启动 Code Server
  cd "$HOME" || error_message "无法切换到 HOME 目录"
  exec code-server \
    --bind-addr 0.0.0.0:8889 \
    --disable-telemetry \
    --disable-update-check \
    --disable-workspace-trust \
    --disable-getting-started-override \
    --auth none \
    $EXTENSIONS_DIR_ARG \
    "$WORK_DIR"
else
  error_message "code-server 未安装，请检查安装过程中的错误信息。"
fi
